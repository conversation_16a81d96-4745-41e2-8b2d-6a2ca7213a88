'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  HiOutlineArrowLeft,
  HiOutlineCheck,
  HiOutlineHeart,
  HiOutlineClipboard,
  HiOutlineEye,
  HiOutlineSparkles,
  HiOutlineArrowRight
} from 'react-icons/hi';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import './assign-plans.css';

interface Plan {
  _id: string;
  planName: string;
  planCode?: string;
  coverageType: string;
  coverageSubTypes: string[];
  planType: string;
  metalTier?: string;
  carrierId?: string;
  carrier?: {
    carrierName: string;
  };
  description?: string;
  highlights?: string[];
}

interface PlansByCategory {
  medical: Plan[];
  dental: Plan[];
  vision: Plan[];
  ancillary: Plan[];
}

const AssignPlansPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const dispatch = useDispatch();
  const companyId = params.companyId as string;

  const [selectedPlans, setSelectedPlans] = useState<string[]>([]);
  const [availablePlans, setAvailablePlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const managedCompanies = useSelector((state: RootState) => state.user.managedCompanies);
  const company = managedCompanies?.find(c => c._id === companyId);

  useEffect(() => {
    if (companyId) {
      fetchAvailablePlans();
    }
  }, [companyId]);

  const fetchAvailablePlans = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock available plans for assignment
      const mockPlans: Plan[] = [
        {
          _id: 'plan1',
          planName: 'Blue Cross Blue Shield PPO',
          planCode: 'BCBS-PPO-2024',
          coverageType: 'Your Health',
          coverageSubTypes: ['Medical'],
          planType: 'PPO',
          metalTier: 'Gold',
          carrierId: 'carrier1',
          carrier: {
            carrierName: 'Blue Cross Blue Shield'
          },
          description: 'Comprehensive PPO plan with nationwide coverage',
          highlights: ['Nationwide network', 'No referrals needed', 'Preventive care covered']
        },
        {
          _id: 'plan2',
          planName: 'Aetna Better Health HMO',
          planCode: 'AETNA-HMO-2024',
          coverageType: 'Your Health',
          coverageSubTypes: ['Medical'],
          planType: 'HMO',
          metalTier: 'Silver',
          carrierId: 'carrier2',
          carrier: {
            carrierName: 'Aetna'
          },
          description: 'Cost-effective HMO plan with coordinated care',
          highlights: ['Lower premiums', 'Coordinated care', 'Primary care focus']
        },
        {
          _id: 'plan3',
          planName: 'Delta Dental PPO',
          planCode: 'DELTA-PPO-2024',
          coverageType: 'Dental',
          coverageSubTypes: ['Dental'],
          planType: 'PPO',
          carrierId: 'carrier3',
          carrier: {
            carrierName: 'Delta Dental'
          },
          description: 'Comprehensive dental coverage with large network',
          highlights: ['Large provider network', 'Preventive care covered', 'Orthodontics included']
        },
        {
          _id: 'plan4',
          planName: 'Guardian Dental HMO',
          planCode: 'GUARD-HMO-2024',
          coverageType: 'Dental',
          coverageSubTypes: ['Dental'],
          planType: 'HMO',
          carrierId: 'carrier4',
          carrier: {
            carrierName: 'Guardian'
          },
          description: 'Affordable dental HMO with quality care',
          highlights: ['Lower cost option', 'Quality providers', 'Basic and major services']
        },
        {
          _id: 'plan5',
          planName: 'VSP Vision Care',
          planCode: 'VSP-2024',
          coverageType: 'Vision',
          coverageSubTypes: ['Vision'],
          planType: 'Vision',
          carrierId: 'carrier5',
          carrier: {
            carrierName: 'VSP'
          },
          description: 'Complete vision care with frame allowances',
          highlights: ['Annual eye exams', 'Frame allowance', 'Contact lens coverage']
        },
        {
          _id: 'plan6',
          planName: 'MetLife Life Insurance',
          planCode: 'MET-LIFE-2024',
          coverageType: 'Ancillary',
          coverageSubTypes: ['Term Life'],
          planType: 'Term Life',
          carrierId: 'carrier6',
          carrier: {
            carrierName: 'MetLife'
          },
          description: 'Term life insurance for financial protection',
          highlights: ['Competitive rates', 'Easy enrollment', 'Portable coverage']
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAvailablePlans(mockPlans);
    } catch (error) {
      console.error('Error fetching available plans:', error);
      setError('Failed to load available plans');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/ai-enroller/manage-groups/select-company');
  };

  const handlePlanToggle = (planId: string) => {
    setSelectedPlans(prev => 
      prev.includes(planId) 
        ? prev.filter(id => id !== planId)
        : [...prev, planId]
    );
  };

  const handleContinue = () => {
    if (selectedPlans.length === 0) {
      alert('Please select at least one plan to continue.');
      return;
    }
    
    // Store selected plans in localStorage for the next step
    localStorage.setItem('selectedPlansForAssignment', JSON.stringify(selectedPlans));
    
    // Navigate to tiers and pricing step
    router.push(`/ai-enroller/manage-groups/${companyId}/assign-plans/configure`);
  };

  const getPlanIcon = (coverageType: string) => {
    switch (coverageType.toLowerCase()) {
      case 'your health':
      case 'medical':
        return <HiOutlineHeart className="plan-icon medical" />;
      case 'dental':
        return <HiOutlineClipboard className="plan-icon dental" />;
      case 'vision':
        return <HiOutlineEye className="plan-icon vision" />;
      case 'ancillary':
      default:
        return <HiOutlineSparkles className="plan-icon ancillary" />;
    }
  };

  // Group plans by category
  const plansByCategory: PlansByCategory = {
    medical: [],
    dental: [],
    vision: [],
    ancillary: []
  };

  availablePlans.forEach(plan => {
    const coverageType = plan.coverageType?.toLowerCase() || '';
    if (coverageType.includes('health') || coverageType.includes('medical')) {
      plansByCategory.medical.push(plan);
    } else if (coverageType.includes('dental')) {
      plansByCategory.dental.push(plan);
    } else if (coverageType.includes('vision')) {
      plansByCategory.vision.push(plan);
    } else {
      plansByCategory.ancillary.push(plan);
    }
  });

  if (loading) {
    return (
      <div className="assign-plans-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading available plans...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="assign-plans-page">
        <div className="error-container">
          <p>{error}</p>
          <button onClick={handleBack} className="back-button">
            <HiOutlineArrowLeft size={20} />
            Back to Company Selection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="assign-plans-page">
      {/* Progress Steps */}
      <div className="progress-steps">
        <div className="step active">
          <div className="step-number">1</div>
          <div className="step-label">Select Plans</div>
        </div>
        <div className="step-connector"></div>
        <div className="step">
          <div className="step-number">2</div>
          <div className="step-label">Configure Tiers</div>
        </div>
        <div className="step-connector"></div>
        <div className="step">
          <div className="step-number">3</div>
          <div className="step-label">Set Dates</div>
        </div>
        <div className="step-connector"></div>
        <div className="step">
          <div className="step-number">4</div>
          <div className="step-label">Review & Assign</div>
        </div>
      </div>

      {/* Page Header */}
      <div className="page-header">
        <button onClick={handleBack} className="back-button">
          <HiOutlineArrowLeft size={20} />
          Back
        </button>
        <div className="header-content">
          <h1>Select Plans to Assign</h1>
          <p>Choose the benefit plans you want to assign to {company?.companyName || 'this company'}</p>
        </div>
      </div>

      {/* Selected Plans Summary */}
      {selectedPlans.length > 0 && (
        <div className="selected-summary">
          <div className="summary-content">
            <HiOutlineCheck size={20} />
            <span>{selectedPlans.length} plan{selectedPlans.length !== 1 ? 's' : ''} selected</span>
          </div>
        </div>
      )}

      {/* Plan Categories */}
      {Object.entries(plansByCategory).map(([category, plans]) => (
        plans.length > 0 && (
          <div key={category} className="plan-category">
            <div className="category-header">
              <h2>{category.charAt(0).toUpperCase() + category.slice(1)} Plans</h2>
              <span className="plan-count">{plans.length} available</span>
            </div>

            <div className="plans-grid">
              {plans.map((plan: Plan) => (
                <div
                  key={plan._id}
                  className={`plan-card ${selectedPlans.includes(plan._id) ? 'selected' : ''}`}
                  onClick={() => handlePlanToggle(plan._id)}
                >
                  <div className="plan-header">
                    <div className="plan-icon-wrapper">
                      {getPlanIcon(plan.coverageType)}
                    </div>
                    <div className="selection-indicator">
                      {selectedPlans.includes(plan._id) && <HiOutlineCheck size={16} />}
                    </div>
                  </div>

                  <div className="plan-content">
                    <h3>{plan.planName}</h3>
                    <div className="plan-carrier">{plan.carrier?.carrierName}</div>
                    <div className="plan-type">{plan.planType} {plan.metalTier && `• ${plan.metalTier}`}</div>

                    {plan.description && (
                      <p className="plan-description">{plan.description}</p>
                    )}

                    {plan.highlights && plan.highlights.length > 0 && (
                      <div className="plan-highlights">
                        {plan.highlights.slice(0, 3).map((highlight, index) => (
                          <div key={index} className="highlight-item">
                            <HiOutlineCheck size={12} />
                            <span>{highlight}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )
      ))}

      {/* Continue Button */}
      <div className="continue-section">
        <button
          className={`continue-btn ${selectedPlans.length === 0 ? 'disabled' : ''}`}
          onClick={handleContinue}
          disabled={selectedPlans.length === 0}
        >
          Continue to Configure Tiers
          <HiOutlineArrowRight size={20} />
        </button>
      </div>
    </div>
  );
};

export default AssignPlansPage;
