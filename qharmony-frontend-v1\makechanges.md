# 🛠️ Frontend Task List for `/create-plan` and `/plans` Pages

## ✅ `/create-plan` Page

### 🎨 UI/Styling
1. **Update All Black Buttons**
   - Replace all black buttons with:
     ```css
     --primary-gradient: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
     ```

2. **Custom Popups**
   - All popups (errors, warnings, success messages, confirmation prompts) should be custom-styled, not browser default.
   - Maintain consistent styling across all such components to match the app’s theme.

3. **Tooltip Visibility**
   - Fix top tooltips that are cut off or not visible due to component boundaries.
   - Ensure tooltips always appear *above* all elements (possibly using `z-index` or portal-based rendering).

4. **Preview Page Margin Fix**
   - Reduce unnecessary margins between child components and parent container.

5. **Purple Banner Consistency**
   - Match the last purple banner’s background color to the same one used in the navigation step (light gradient, not solid color).

---

## ✅ `/plans` Page

### 🎨 UI/Styling
1. **Update All Black Buttons**
   - Replace all black buttons with:
     ```css
     --primary-gradient: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
     ```

2. **Header Text Alignment**
   - Place the following text inline (horizontally) with the 3 buttons:
     ```
     Plan Management
     Manage and view all insurance plans
     ```

3. **Vertical Spacing**
   - Reduce vertical gaps between different components for tighter layout.

4. **Modal Text Consistency**
   - Ensure all text in “Create New Plan” and “Edit Plan” modals are consistent in:
     - Weight
     - Font size
     - Line height
     - Alignment

5. **Pagination**
   - Display 10 plans per page.
   - Add pagination controls to view more plans (next/previous).

6. **Status Filter**
   - Populate status filter with all current statuses available in the system.

---

## 🔧 Backend Functionality

### 🗑️ Delete Plan
- Implement delete using `DELETE` API from the plan controller.
- Before deleting, check `canDelete` flag (API or logic).
- Only allow delete if:
  - `canDelete: true`
  - `Plan Status: Archive`
  - Not referenced in any downstream entities.

### 🔁 Activate/Deactivate Plan
- Implement toggle:
  - **Activate → Draft**
  - Use provided API.

---

## 🧾 Editing Rules (Global)
- Allow submission without non-mandatory fields in:
  - Plan
  - Plan Assignment
  - Enrollment
  - Carrier
  - Company Benefit Settings
  - General User/Company data
- Reference DB schema for what’s considered non-mandatory.
- **Only system plans** should be allowed to clone/duplicate.

---

## 🎨 Universal Styling

- All popups (error/success/info) must:
  - Not use default browser styles.
  - Match app's modal/dialog style for consistency.

- **Icons**
  - Use relevant, meaningful icons (e.g., `react-icons`).
  - Replace generic/random icons.
  - Plan Management icon should be custom and use **gradient button style**.

---

✅ **Follow consistent code standards and styling across components.**
