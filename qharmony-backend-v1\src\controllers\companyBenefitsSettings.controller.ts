import express from 'express';
import CompanyBenefitsSettingsModelClass, {
  CompanyBenefitsSettingsDataInterface,
  UpdateableCompanyBenefitsSettingsDataInterface
} from '../nosql/preEnrollment/companyBenefitsSettings.model';
import UserModelClass, { UserDataInterface } from '../nosql/user.model';
import CompanyModelClass, { CompanyDataInterface } from '../nosql/company.model';
import { PAYROLL_FREQUENCIES, ENROLLMENT_PERIOD_TYPES } from '../constants';
import logger from '../utils/logger';

export class CompanyBenefitsSettingsController {
  public router = express.Router();

  constructor() {
    this.initializeRoutes();
  }

  // Test endpoint to verify API is working
  private testEndpoint = async (
    request: express.Request,
    response: express.Response
  ) => {
    const userId = request.headers['user-id'] as string;
    response.status(200).json({
      message: 'API is working!',
      timestamp: new Date().toISOString(),
      userId: userId,
      endpoint: 'company-benefits-settings/test'
    });
  };

  private initializeRoutes(): void {
    // Test endpoint
    this.router.get('/api/pre-enrollment/company-benefits-settings/test', this.testEndpoint);

    // Company Benefits Settings Management
    this.router.post('/api/pre-enrollment/company-benefits-settings', this.createCompanySettings);
    this.router.get('/api/pre-enrollment/company-benefits-settings/company/:companyId', this.getCompanySettings);
    this.router.put('/api/pre-enrollment/company-benefits-settings/company/:companyId', this.updateCompanySettings);
    this.router.get('/api/pre-enrollment/company-benefits-settings/broker-companies', this.getBrokerCompanies);
    this.router.post('/api/pre-enrollment/company-benefits-settings/company/:companyId/enrollment-periods', this.addEnrollmentPeriod);
    this.router.put('/api/pre-enrollment/company-benefits-settings/company/:companyId/enrollment-periods/:periodId', this.updateEnrollmentPeriod);
    this.router.get('/api/pre-enrollment/company-benefits-settings/company/:companyId/validate', this.validateSettingsCompleteness);
    this.router.delete('/api/pre-enrollment/company-benefits-settings/company/:companyId', this.deactivateCompanySettings);
  }

  /**
   * Create company benefits settings (Broker only)
   * POST /company-benefits-settings
   */
  private createCompanySettings = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;

      if (!userId) {
        response.status(401).json({ error: 'User ID is required' });
        return;
      }

      // Get user details to validate permissions
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(404).json({ error: 'User not found' });
        return;
      }

      // Only super admins and brokers can create company settings
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        response.status(403).json({ error: 'Only super admins and brokers can create company benefits settings' });
        return;
      }

      const {
        companyId,
        globalEligibility,
        enrollmentPeriods,
        companyPreferences
      } = request.body;

      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }

      // Validate company exists and user has access
      const company = await CompanyModelClass.getDataById(companyId);
      if (!company) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      // Check if user can manage this company
      const canManage = await this.canManageCompany(user, company);
      if (!canManage) {
        response.status(403).json({ error: 'Access denied to manage this company' });
        return;
      }

      // Check if settings already exist
      const existingSettings = await CompanyBenefitsSettingsModelClass.getDataByCompanyId(companyId);
      if (existingSettings) {
        response.status(409).json({ error: 'Company benefits settings already exist. Use PUT to update.' });
        return;
      }

      // Validate payroll frequency if provided
      if (globalEligibility?.payrollFrequency && !PAYROLL_FREQUENCIES.includes(globalEligibility.payrollFrequency)) {
        response.status(400).json({ error: `Invalid payroll frequency. Must be one of: ${PAYROLL_FREQUENCIES.join(', ')}` });
        return;
      }

      // Validate enrollment period types if provided
      if (enrollmentPeriods && Array.isArray(enrollmentPeriods)) {
        for (const period of enrollmentPeriods) {
          if (period.type && !ENROLLMENT_PERIOD_TYPES.includes(period.type)) {
            response.status(400).json({ error: `Invalid enrollment period type: ${period.type}. Must be one of: ${ENROLLMENT_PERIOD_TYPES.join(', ')}` });
            return;
          }
        }
      }

      // Create settings data
      const settingsData: CompanyBenefitsSettingsDataInterface = {
        companyId,
        globalEligibility: globalEligibility || { payrollFrequency: '' },
        enrollmentPeriods: enrollmentPeriods || [],
        companyPreferences: companyPreferences || {},
        isActive: true
      };

      // Create the settings
      const settings = await CompanyBenefitsSettingsModelClass.addData(settingsData);

      response.status(201).json({
        message: 'Company benefits settings created successfully',
        settings
      });

    } catch (error) {
      logger.error('Error creating company benefits settings:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * Get company benefits settings
   * GET /company-benefits-settings/company/:companyId
   */
  private getCompanySettings = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { companyId } = request.params;

      if (!userId) {
        response.status(401).json({ error: 'User ID is required' });
        return;
      }

      // Get user details to validate permissions
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(404).json({ error: 'User not found' });
        return;
      }

      // Validate company exists
      const company = await CompanyModelClass.getDataById(companyId);
      if (!company) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      // Check if user can access this company
      const canAccess = await this.canAccessCompany(user, company);
      if (!canAccess) {
        response.status(403).json({ error: 'Access denied to this company' });
        return;
      }

      // Get settings
      const settings = await CompanyBenefitsSettingsModelClass.getDataByCompanyId(companyId);
      if (!settings) {
        response.status(404).json({ error: 'Company benefits settings not found' });
        return;
      }

      response.status(200).json({ settings });

    } catch (error) {
      logger.error('Error fetching company benefits settings:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * Update company benefits settings (Broker or Employer)
   * PUT /company-benefits-settings/company/:companyId
   */
  private updateCompanySettings = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { companyId } = request.params;

      if (!userId) {
        response.status(401).json({ error: 'User ID is required' });
        return;
      }

      // Get user details to validate permissions
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(404).json({ error: 'User not found' });
        return;
      }

      // Validate company exists
      const company = await CompanyModelClass.getDataById(companyId);
      if (!company) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      // Check if user can manage this company (broker or employer)
      const canManage = await this.canManageCompany(user, company);
      if (!canManage) {
        response.status(403).json({ error: 'Access denied to manage this company' });
        return;
      }

      // Get existing settings
      const existingSettings = await CompanyBenefitsSettingsModelClass.getDataByCompanyId(companyId);
      if (!existingSettings) {
        response.status(404).json({ error: 'Company benefits settings not found' });
        return;
      }

      const {
        globalEligibility,
        enrollmentPeriods,
        companyPreferences
      } = request.body;

      // Validate payroll frequency if provided
      if (globalEligibility?.payrollFrequency && !PAYROLL_FREQUENCIES.includes(globalEligibility.payrollFrequency)) {
        response.status(400).json({ error: `Invalid payroll frequency. Must be one of: ${PAYROLL_FREQUENCIES.join(', ')}` });
        return;
      }

      // Validate enrollment period types if provided
      if (enrollmentPeriods && Array.isArray(enrollmentPeriods)) {
        for (const period of enrollmentPeriods) {
          if (period.type && !ENROLLMENT_PERIOD_TYPES.includes(period.type)) {
            response.status(400).json({ error: `Invalid enrollment period type: ${period.type}. Must be one of: ${ENROLLMENT_PERIOD_TYPES.join(', ')}` });
            return;
          }
        }
      }

      // ✅ FIXED: Use explicit updatable interface for type safety
      const updateData: UpdateableCompanyBenefitsSettingsDataInterface = {};

      if (globalEligibility !== undefined) {
        updateData.globalEligibility = { ...existingSettings.globalEligibility, ...globalEligibility };
      }

      if (enrollmentPeriods !== undefined) {
        updateData.enrollmentPeriods = enrollmentPeriods;
      }

      if (companyPreferences !== undefined) {
        updateData.companyPreferences = { ...existingSettings.companyPreferences, ...companyPreferences };
      }

      // Update settings
      await CompanyBenefitsSettingsModelClass.updateData({ id: existingSettings._id!.toString(), data: updateData });
      const updatedSettings = await CompanyBenefitsSettingsModelClass.getDataById(existingSettings._id!.toString());

      response.status(200).json({
        message: 'Company benefits settings updated successfully',
        settings: updatedSettings
      });

    } catch (error) {
      logger.error('Error updating company benefits settings:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * Get broker companies with settings status
   * GET /company-benefits-settings/broker-companies
   */
  private getBrokerCompanies = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;

      if (!userId) {
        response.status(401).json({ error: 'User ID is required' });
        return;
      }

      // Get user details to validate permissions
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(404).json({ error: 'User not found' });
        return;
      }

      // Only super admins and brokers can access this endpoint
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        response.status(403).json({ error: 'Only super admins and brokers can access broker companies' });
        return;
      }

      let companies: CompanyDataInterface[] = [];

      if (UserModelClass.isSuperAdmin(user)) {
        // Super admins can see all companies
        companies = await CompanyModelClass.getAllData();
      } else {
        // Brokers can see their client companies
        companies = await CompanyModelClass.getDataByBrokerId(userId);
      }

      // Get settings status for each company
      const companiesWithSettings = await Promise.all(
        companies.map(async (company) => {
          const settings = await CompanyBenefitsSettingsModelClass.getDataByCompanyId(company._id!.toString());
          return {
            companyId: company._id!.toString(),
            companyName: company.name,
            hasSettings: !!settings,
            settingsId: settings?._id?.toString() || null,
            lastUpdated: settings?.updatedAt || null
          };
        })
      );

      response.status(200).json({
        companies: companiesWithSettings,
        count: companiesWithSettings.length
      });

    } catch (error) {
      logger.error('Error fetching broker companies:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * Add enrollment period to company settings
   * POST /company-benefits-settings/company/:companyId/enrollment-periods
   */
  private addEnrollmentPeriod = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { companyId } = request.params;

      if (!userId) {
        response.status(401).json({ error: 'User ID is required' });
        return;
      }

      // Get user details to validate permissions
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(404).json({ error: 'User not found' });
        return;
      }

      // Validate company exists
      const company = await CompanyModelClass.getDataById(companyId);
      if (!company) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      // Check if user can manage this company
      const canManage = await this.canManageCompany(user, company);
      if (!canManage) {
        response.status(403).json({ error: 'Access denied to manage this company' });
        return;
      }

      // Get existing settings
      const existingSettings = await CompanyBenefitsSettingsModelClass.getDataByCompanyId(companyId);
      if (!existingSettings) {
        response.status(404).json({ error: 'Company benefits settings not found' });
        return;
      }

      const {
        type,
        startDate,
        endDate,
        coverageStartDate,
        coverageEndDate,
        description,
        isActive = true
      } = request.body;

      // Validate required fields
      if (!type || !startDate || !endDate || !coverageStartDate || !coverageEndDate) {
        response.status(400).json({
          error: 'Type, startDate, endDate, coverageStartDate, and coverageEndDate are required'
        });
        return;
      }

      // Validate enrollment period type
      if (!ENROLLMENT_PERIOD_TYPES.includes(type)) {
        response.status(400).json({
          error: `Invalid enrollment period type: ${type}. Must be one of: ${ENROLLMENT_PERIOD_TYPES.join(', ')}`
        });
        return;
      }

      // Create new enrollment period
      const newPeriod = {
        type,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        coverageStartDate: new Date(coverageStartDate),
        coverageEndDate: new Date(coverageEndDate),
        description: description || '',
        isActive
      };

      // Add to existing enrollment periods
      const updatedEnrollmentPeriods = [...existingSettings.enrollmentPeriods, newPeriod];

      // Update settings
      await CompanyBenefitsSettingsModelClass.updateData({
        id: existingSettings._id!.toString(),
        data: { enrollmentPeriods: updatedEnrollmentPeriods }
      });
      const updatedSettings = await CompanyBenefitsSettingsModelClass.getDataById(existingSettings._id!.toString());

      response.status(201).json({
        message: 'Enrollment period added successfully',
        settings: updatedSettings,
        addedPeriod: newPeriod
      });

    } catch (error) {
      logger.error('Error adding enrollment period:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * Update enrollment period
   * PUT /company-benefits-settings/company/:companyId/enrollment-periods/:periodId
   */
  private updateEnrollmentPeriod = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { companyId, periodId } = request.params;

      if (!userId) {
        response.status(401).json({ error: 'User ID is required' });
        return;
      }

      // Get user details to validate permissions
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(404).json({ error: 'User not found' });
        return;
      }

      // Validate company exists
      const company = await CompanyModelClass.getDataById(companyId);
      if (!company) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      // Check if user can manage this company
      const canManage = await this.canManageCompany(user, company);
      if (!canManage) {
        response.status(403).json({ error: 'Access denied to manage this company' });
        return;
      }

      // Get existing settings
      const existingSettings = await CompanyBenefitsSettingsModelClass.getDataByCompanyId(companyId);
      if (!existingSettings) {
        response.status(404).json({ error: 'Company benefits settings not found' });
        return;
      }

      // Find the enrollment period to update
      const periodIndex = existingSettings.enrollmentPeriods.findIndex(
        (period: any) => period._id?.toString() === periodId
      );

      if (periodIndex === -1) {
        response.status(404).json({ error: 'Enrollment period not found' });
        return;
      }

      const {
        type,
        startDate,
        endDate,
        coverageStartDate,
        coverageEndDate,
        description,
        isActive
      } = request.body;

      // Validate enrollment period type if provided
      if (type && !ENROLLMENT_PERIOD_TYPES.includes(type)) {
        response.status(400).json({
          error: `Invalid enrollment period type: ${type}. Must be one of: ${ENROLLMENT_PERIOD_TYPES.join(', ')}`
        });
        return;
      }

      // Update the enrollment period
      const updatedPeriod = {
        ...existingSettings.enrollmentPeriods[periodIndex],
        ...(type && { type }),
        ...(startDate && { startDate: new Date(startDate) }),
        ...(endDate && { endDate: new Date(endDate) }),
        ...(coverageStartDate && { coverageStartDate: new Date(coverageStartDate) }),
        ...(coverageEndDate && { coverageEndDate: new Date(coverageEndDate) }),
        ...(description !== undefined && { description }),
        ...(isActive !== undefined && { isActive })
      };

      // Update the enrollment periods array
      const updatedEnrollmentPeriods = [...existingSettings.enrollmentPeriods];
      updatedEnrollmentPeriods[periodIndex] = updatedPeriod;

      // Update settings
      await CompanyBenefitsSettingsModelClass.updateData({
        id: existingSettings._id!.toString(),
        data: { enrollmentPeriods: updatedEnrollmentPeriods }
      });
      const updatedSettings = await CompanyBenefitsSettingsModelClass.getDataById(existingSettings._id!.toString());

      response.status(200).json({
        message: 'Enrollment period updated successfully',
        settings: updatedSettings,
        updatedPeriod
      });

    } catch (error) {
      logger.error('Error updating enrollment period:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * Validate settings completeness
   * GET /company-benefits-settings/company/:companyId/validate
   */
  private validateSettingsCompleteness = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { companyId } = request.params;

      if (!userId) {
        response.status(401).json({ error: 'User ID is required' });
        return;
      }

      // Get user details to validate permissions
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(404).json({ error: 'User not found' });
        return;
      }

      // Validate company exists
      const company = await CompanyModelClass.getDataById(companyId);
      if (!company) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      // Check if user can access this company
      const canAccess = await this.canAccessCompany(user, company);
      if (!canAccess) {
        response.status(403).json({ error: 'Access denied to this company' });
        return;
      }

      // Get settings
      const settings = await CompanyBenefitsSettingsModelClass.getDataByCompanyId(companyId);
      if (!settings) {
        response.status(200).json({
          companyId,
          validation: {
            isComplete: false,
            missingFields: ['Company benefits settings not created'],
            warnings: []
          },
          readyForPlanAssignment: false
        });
        return;
      }

      // Validate completeness
      const validation = this.validateCompanySettingsCompleteness(settings);

      response.status(200).json({
        companyId,
        validation,
        readyForPlanAssignment: validation.isComplete
      });

    } catch (error) {
      logger.error('Error validating settings completeness:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * Deactivate company settings (soft delete)
   * DELETE /company-benefits-settings/company/:companyId
   */
  private deactivateCompanySettings = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { companyId } = request.params;

      if (!userId) {
        response.status(401).json({ error: 'User ID is required' });
        return;
      }

      // Get user details to validate permissions
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(404).json({ error: 'User not found' });
        return;
      }

      // Only super admins and brokers can deactivate settings
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        response.status(403).json({ error: 'Only super admins and brokers can deactivate company benefits settings' });
        return;
      }

      // Validate company exists
      const company = await CompanyModelClass.getDataById(companyId);
      if (!company) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      // Check if user can manage this company
      const canManage = await this.canManageCompany(user, company);
      if (!canManage) {
        response.status(403).json({ error: 'Access denied to manage this company' });
        return;
      }

      // Get existing settings
      const existingSettings = await CompanyBenefitsSettingsModelClass.getDataByCompanyId(companyId);
      if (!existingSettings) {
        response.status(404).json({ error: 'Company benefits settings not found' });
        return;
      }

      // Deactivate settings (soft delete)
      await CompanyBenefitsSettingsModelClass.updateData({
        id: existingSettings._id!.toString(),
        data: { isActive: false }
      });

      response.status(200).json({
        message: 'Company benefits settings deactivated successfully',
        companyId
      });

    } catch (error) {
      logger.error('Error deactivating company settings:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * Helper method to check if user can manage a company
   * Super admins can manage all companies
   * Brokers can manage their client companies
   * Employers can manage their own company
   */
  private async canManageCompany(user: UserDataInterface, company: CompanyDataInterface): Promise<boolean> {
    // Super admins can manage all companies
    if (UserModelClass.isSuperAdmin(user)) {
      return true;
    }

    // Brokers can manage their client companies
    if (user.isBroker && company.brokerId === user._id?.toString()) {
      return true;
    }

    // Employers (including brokers) can manage their own company
    if (user.isAdmin && user.companyId === company._id?.toString()) {
      return true;
    }

    return false;
  }

  /**
   * Helper method to check if user can access a company
   * All authenticated users can access companies they're associated with
   */
  private async canAccessCompany(user: UserDataInterface, company: CompanyDataInterface): Promise<boolean> {
    // Super admins can access all companies
    if (UserModelClass.isSuperAdmin(user)) {
      return true;
    }

    // Brokers can access their client companies
    if (user.isBroker && company.brokerId === user._id?.toString()) {
      return true;
    }

    // Company users can access their own company
    if (user.companyId === company._id?.toString()) {
      return true;
    }

    return false;
  }

  /**
   * Helper method to validate company settings completeness
   */
  private validateCompanySettingsCompleteness(settings: CompanyBenefitsSettingsDataInterface) {
    const missingFields: string[] = [];
    const warnings: string[] = [];

    // Check global eligibility
    if (!settings.globalEligibility?.payrollFrequency) {
      missingFields.push('Payroll frequency');
    }

    if (!settings.globalEligibility?.firstPayrollDate) {
      warnings.push('First payroll date not specified');
    }

    if (!settings.globalEligibility?.defaultWaitingPeriod) {
      warnings.push('Default waiting period not specified');
    }

    // Check enrollment periods
    if (!settings.enrollmentPeriods || settings.enrollmentPeriods.length === 0) {
      missingFields.push('Enrollment periods');
    } else {
      // Check if there's at least one active enrollment period
      const activeEnrollmentPeriods = settings.enrollmentPeriods.filter((period: any) => period.isActive);
      if (activeEnrollmentPeriods.length === 0) {
        warnings.push('No active enrollment periods');
      }
    }

    // Check company preferences
    if (settings.companyPreferences?.requireBeneficiaryDesignation === undefined) {
      warnings.push('Beneficiary designation requirement not specified');
    }

    if (settings.companyPreferences?.enableDependentVerification === undefined) {
      warnings.push('Dependent verification setting not specified');
    }

    return {
      isComplete: missingFields.length === 0,
      missingFields,
      warnings
    };
  }
}
