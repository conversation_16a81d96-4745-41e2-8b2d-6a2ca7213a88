const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function debugPlanAssignments() {
  try {
    console.log('🔍 DEBUGGING PLAN ASSIGNMENTS');
    
    // Connect to the same database as API
    const mongoUri = process.env.MONGO_URI;
    const dbName = process.env.MONGO_DB_NAME || 'testing';
    
    console.log(`📍 Connecting to database: ${dbName}`);
    console.log(`📍 MongoDB URI: ${mongoUri ? 'Found' : 'Missing'}`);
    
    // Connect to the database the API uses
    let connectionUri;
    if (dbName && dbName !== 'testing') {
      connectionUri = mongoUri.replace('/?retryWrites', `/${dbName}?retryWrites`);
    } else {
      connectionUri = mongoUri;
    }
    
    await mongoose.connect(connectionUri);
    console.log(`✅ Connected to MongoDB database: ${mongoose.connection.db.databaseName}`);
    
    // Create simple schema to query data
    const planAssignmentSchema = new mongoose.Schema({}, { strict: false });
    const PlanAssignment = mongoose.model('PlanAssignment', planAssignmentSchema);
    
    const COMPANY_ID = "67bf65bf50bad0a4b3d805ba";
    
    console.log('\n🔍 SEARCHING FOR PLAN ASSIGNMENTS...');
    console.log(`Company ID: ${COMPANY_ID}`);
    
    // 1. Check all plan assignments in database
    const allAssignments = await PlanAssignment.find({});
    console.log(`\n📊 Total plan assignments in database: ${allAssignments.length}`);
    
    if (allAssignments.length > 0) {
      console.log('\n📋 All Plan Assignments:');
      allAssignments.forEach((assignment, index) => {
        console.log(`  ${index + 1}. ID: ${assignment._id}`);
        console.log(`     Company ID: ${assignment.companyId} (Type: ${typeof assignment.companyId})`);
        console.log(`     Group Number: ${assignment.groupNumber}`);
        console.log(`     Is Active: ${assignment.isActive}`);
        console.log(`     Status: ${assignment.status}`);
        console.log('');
      });
    }
    
    // 2. Search by exact company ID (string)
    const assignmentsByStringId = await PlanAssignment.find({ companyId: COMPANY_ID });
    console.log(`\n🔍 Assignments with companyId as string "${COMPANY_ID}": ${assignmentsByStringId.length}`);
    
    // 3. Search by ObjectId
    const assignmentsByObjectId = await PlanAssignment.find({ 
      companyId: new mongoose.Types.ObjectId(COMPANY_ID) 
    });
    console.log(`🔍 Assignments with companyId as ObjectId: ${assignmentsByObjectId.length}`);
    
    // 4. Search with isActive filter (what API uses)
    const activeAssignments = await PlanAssignment.find({ 
      companyId: new mongoose.Types.ObjectId(COMPANY_ID),
      isActive: true 
    });
    console.log(`🔍 Active assignments (API query): ${activeAssignments.length}`);
    
    // 5. Check recent assignments (created in last hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentAssignments = await PlanAssignment.find({ 
      createdAt: { $gte: oneHourAgo }
    });
    console.log(`🔍 Recent assignments (last hour): ${recentAssignments.length}`);
    
    if (recentAssignments.length > 0) {
      console.log('\n📋 Recent Plan Assignments:');
      recentAssignments.forEach((assignment, index) => {
        console.log(`  ${index + 1}. ID: ${assignment._id}`);
        console.log(`     Company ID: ${assignment.companyId} (Type: ${typeof assignment.companyId})`);
        console.log(`     Group Number: ${assignment.groupNumber}`);
        console.log(`     Is Active: ${assignment.isActive}`);
        console.log(`     Status: ${assignment.status}`);
        console.log(`     Created: ${assignment.createdAt}`);
        console.log('');
      });
    }
    
    // 6. Test the exact API query
    console.log('\n🎯 TESTING EXACT API QUERY:');
    const apiQuery = {
      companyId: new mongoose.Types.ObjectId(COMPANY_ID),
      isActive: true
    };
    console.log('Query:', JSON.stringify(apiQuery, null, 2));
    
    const apiResults = await PlanAssignment.find(apiQuery);
    console.log(`API Query Results: ${apiResults.length} assignments found`);
    
    if (apiResults.length > 0) {
      console.log('✅ API should return these assignments:');
      apiResults.forEach((assignment, index) => {
        console.log(`  ${index + 1}. ${assignment.groupNumber} - ${assignment._id}`);
      });
    } else {
      console.log('❌ No assignments found with API query');
      
      // Suggest fixes
      console.log('\n🔧 SUGGESTED FIXES:');
      
      if (assignmentsByStringId.length > 0) {
        console.log('1. Convert companyId from string to ObjectId in database');
        console.log('2. Or modify API to accept string companyId');
      }
      
      const inactiveAssignments = await PlanAssignment.find({ 
        companyId: new mongoose.Types.ObjectId(COMPANY_ID),
        isActive: { $ne: true }
      });
      
      if (inactiveAssignments.length > 0) {
        console.log('3. Set isActive: true for plan assignments');
        console.log(`   Found ${inactiveAssignments.length} inactive assignments`);
      }
    }
    
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
}

debugPlanAssignments();
