{"name": "qharmony-backend", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "index.js", "scripts": {"build": "npx tsc", "dev": "nodemon", "fixlint": "eslint -c package.json 'src/**/*.ts' --fix", "start": "node dist/index.js", "test": "node tests/comprehensive_pre_enrollment_test.js", "test:demo": "node tests/demo_enrollment_test.js", "test:comprehensive": "node tests/comprehensive_pre_enrollment_test.js"}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"type-enum": [1, "always", ["build", "chore", "docs", "feat", "fix", "lint", "refactor", "revert", "type"]]}}, "prettier": {"semi": true, "singleQuote": true, "trailingComma": "es5"}, "eslintConfig": {"env": {"es6": true, "node": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "extends": ["plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "rules": {"sort-imports": ["error", {"ignoreCase": true, "ignoreDeclarationSort": true}], "@typescript-eslint/ban-ts-comment": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/explicit-module-boundary-types": 0, "@typescript-eslint/no-non-null-assertion": 0, "@typescript-eslint/no-unused-vars": [1, {"argsIgnorePattern": "^_"}]}}, "dependencies": {"@azure/storage-blob": "^12.26.0", "@mergeapi/merge-node-client": "^1.0.4", "@tryfinch/finch-api": "^5.18.0", "@types/multer": "^1.4.12", "axios": "^1.7.5", "body-parser": "^1.19.0", "cookie-parser": "^1.4.5", "crypto-js": "^4.2.0", "dotenv": "^16.0.1", "envalid": "^7.3.1", "express": "^5.0.0", "firebase": "^10.13.1", "firebase-admin": "^12.5.0", "helmet": "^4.2.0", "ioredis": "^5.3.2", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.1.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.15", "openai": "^4.56.0", "pg": "^8.11.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sequelize": "^6.32.0", "sequelize-typescript": "^2.1.5", "slack": "^11.0.2", "uuid": "^9.0.1", "winston": "^3.3.3"}, "devDependencies": {"@types/cookie-parser": "^1.4.2", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.13", "@types/helmet": "0.0.48", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^18.0.6", "@types/nodemailer": "^6.4.16", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.27.0", "concurrently": "^7.3.0", "cors": "^2.8.5", "eslint": "^7.29.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "nodemon": "^3.1.4", "prettier": "^2.3.1", "ts-node": "^10.0.0", "typescript": "^4.7.4", "winston-slack-webhook-transport": "^2.0.1"}}