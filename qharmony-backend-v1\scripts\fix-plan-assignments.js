const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function fixPlanAssignments() {
  try {
    console.log('🔧 FIXING PLAN ASSIGNMENTS DATA FORMAT');
    
    // Connect to production database
    const prodUri = process.env.MONGO_URI.replace('/?retryWrites', '/prod?retryWrites');
    await mongoose.connect(prodUri);
    console.log('✅ Connected to MongoDB PRODUCTION database');
    
    const dbName = mongoose.connection.db.databaseName;
    console.log(`📍 Current database: ${dbName}`);
    
    // Create schema
    const planAssignmentSchema = new mongoose.Schema({}, { strict: false });
    const PlanAssignment = mongoose.model('PlanAssignment', planAssignmentSchema);
    
    const COMPANY_ID = "67bf65bf50bad0a4b3d805ba";
    
    console.log('\n🔍 Finding plan assignments to fix...');
    
    // Find all assignments for this company (regardless of format)
    const allAssignments = await PlanAssignment.find({
      $or: [
        { companyId: COMPANY_ID },
        { companyId: new mongoose.Types.ObjectId(COMPANY_ID) }
      ]
    });
    
    console.log(`Found ${allAssignments.length} plan assignments for company ${COMPANY_ID}`);
    
    if (allAssignments.length === 0) {
      console.log('❌ No plan assignments found for this company');
      console.log('🔍 Let me check all assignments in the database...');
      
      const allInDb = await PlanAssignment.find({});
      console.log(`Total assignments in database: ${allInDb.length}`);
      
      if (allInDb.length > 0) {
        console.log('\n📋 All assignments in database:');
        allInDb.forEach((assignment, index) => {
          console.log(`  ${index + 1}. Company ID: ${assignment.companyId} (${typeof assignment.companyId})`);
          console.log(`     Group: ${assignment.groupNumber}`);
          console.log(`     Active: ${assignment.isActive}`);
          console.log('');
        });
      }
      
      await mongoose.disconnect();
      return;
    }
    
    console.log('\n🔧 Fixing data format...');
    let fixedCount = 0;
    
    for (const assignment of allAssignments) {
      const updates = {};
      let needsUpdate = false;
      
      // Fix 1: Ensure companyId is ObjectId
      if (typeof assignment.companyId === 'string') {
        updates.companyId = new mongoose.Types.ObjectId(assignment.companyId);
        needsUpdate = true;
        console.log(`  ✅ Converting companyId to ObjectId for ${assignment.groupNumber}`);
      }
      
      // Fix 2: Ensure planId is ObjectId
      if (typeof assignment.planId === 'string') {
        updates.planId = new mongoose.Types.ObjectId(assignment.planId);
        needsUpdate = true;
        console.log(`  ✅ Converting planId to ObjectId for ${assignment.groupNumber}`);
      }
      
      // Fix 3: Ensure isActive is true
      if (assignment.isActive !== true) {
        updates.isActive = true;
        needsUpdate = true;
        console.log(`  ✅ Setting isActive=true for ${assignment.groupNumber}`);
      }
      
      // Fix 4: Ensure status is Active
      if (assignment.status !== 'Active') {
        updates.status = 'Active';
        needsUpdate = true;
        console.log(`  ✅ Setting status=Active for ${assignment.groupNumber}`);
      }
      
      // Apply updates if needed
      if (needsUpdate) {
        await PlanAssignment.updateOne(
          { _id: assignment._id },
          { $set: updates }
        );
        fixedCount++;
        console.log(`  ✅ Fixed assignment: ${assignment.groupNumber}`);
      } else {
        console.log(`  ✅ Assignment OK: ${assignment.groupNumber}`);
      }
    }
    
    console.log(`\n🎯 SUMMARY: Fixed ${fixedCount} out of ${allAssignments.length} assignments`);
    
    // Test the API query after fixes
    console.log('\n🧪 Testing API query after fixes...');
    const apiQuery = {
      companyId: new mongoose.Types.ObjectId(COMPANY_ID),
      isActive: true
    };
    
    const apiResults = await PlanAssignment.find(apiQuery);
    console.log(`API Query Results: ${apiResults.length} assignments found`);
    
    if (apiResults.length > 0) {
      console.log('✅ API should now return these assignments:');
      apiResults.forEach((assignment, index) => {
        console.log(`  ${index + 1}. ${assignment.groupNumber} - ${assignment._id}`);
        console.log(`     Plan ID: ${assignment.planId}`);
        console.log(`     Company ID: ${assignment.companyId}`);
        console.log(`     Active: ${assignment.isActive}`);
        console.log(`     Status: ${assignment.status}`);
        console.log('');
      });
      
      console.log('🎉 Plan assignments should now work in the API!');
      console.log('\n🧪 Test with:');
      console.log(`curl -X GET "http://localhost:8080/api/pre-enrollment/plan-assignments/company/${COMPANY_ID}" -H "user-id: 67bf65bf50bad0a4b3d805bc"`);
      
    } else {
      console.log('❌ Still no results. There might be a deeper issue.');
    }
    
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
}

fixPlanAssignments();
