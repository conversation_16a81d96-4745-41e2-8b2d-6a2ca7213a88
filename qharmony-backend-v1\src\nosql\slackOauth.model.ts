import mongoose, { Document, Model } from 'mongoose';

const { Schema } = mongoose;

export interface SlackOauthDataInterface {
  slackBotOauthToken: string;
  slackTeamId: string;
  slackTeamName: string;
  slackInstallerUserId: string;
  slackInstalledAtTs: number;
}

interface SlackOauthDocument extends Document {
  data: SlackOauthDataInterface[];
}

class SlackOauthModelClass {
  private static slackOauthModel: Model<SlackOauthDocument>;

  public static initializeModel() {
    const schema = new Schema({
      slackBotOauthToken: String,
      slackTeamId: String,
      slackTeamName: String,
      slackInstallerUserId: String,
      slackInstalledAtTs: Number,
    });

    this.slackOauthModel = mongoose.model<SlackOauthDocument>(
      'SlackOauth',
      schema
    );
  }

  public static async addData(data: SlackOauthDataInterface): Promise<void> {
    try {
      // Extract the data. If slackTeamId exists, update the data. Otherwise, insert the data.
      const { slackTeamId } = data;
      const existingData = await this.slackOauthModel.findOne({ slackTeamId });
      if (existingData) {
        await this.slackOauthModel.updateOne({ slackTeamId }, data);
      } else {
        await this.slackOauthModel.create(data);
      }
    } catch (error) {
      console.error(error);
    }
  }

  public static async deleteDataBySlackTeamId(
    slackTeamId: string
  ): Promise<void> {
    try {
      await this.slackOauthModel.deleteOne({ slackTeamId });
    } catch (error) {
      console.error(error);
    }
  }

  public static async getAllData(): Promise<SlackOauthDataInterface[] | null> {
    try {
      const result =
        (await this.slackOauthModel.find()) as unknown as SlackOauthDataInterface[];
      return result;
    } catch (error) {
      console.error(error);
    }
    return null;
  }

  public static async getDataBySlackTeamId(
    slackTeamId: string
  ): Promise<SlackOauthDataInterface | null> {
    try {
      const result = (await this.slackOauthModel.findOne({
        slackTeamId,
      })) as SlackOauthDataInterface;
      return result;
    } catch (error) {
      console.error(error);
    }
    return null;
  }
}

SlackOauthModelClass.initializeModel();

export default SlackOauthModelClass;
