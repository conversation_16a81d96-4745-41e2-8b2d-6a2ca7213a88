/**
 * <PERSON><PERSON><PERSON> to update Southern Benefits Systems user details from CSV file
 * 
 * This script:
 * 1. Reads employee and dependent data from Census.csv
 * 2. Maps CSV data to existing users in the database
 * 3. Updates user details without overwriting existing data
 * 4. <PERSON>perly handles dependent relationships
 * 
 * Company: Southern Benefits Systems
 * CompanyId: 67bf65bf50bad0a4b3d805ba
 */

const mongoose = require('mongoose');
const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Load environment variables from parent directory
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// MongoDB connection using same config as main app
const MONGO_URI = process.env.MONGO_URI;
const MONGO_DB_NAME = process.env.MONGO_DB_NAME;

// Company details
const COMPANY_ID = '67bf65bf50bad0a4b3d805ba';
const ADMIN_EMAIL = '<EMAIL>';

// User schema
const userSchema = new mongoose.Schema({
  name: String,
  email: String,
  role: String,
  companyId: String,
  isAdmin: Boolean,
  isBroker: Boolean,
  isActivated: Boolean,
  isDisabled: Boolean,
  groupIds: [String],
  lastLoginAt: Date,
  details: {
    phoneNumber: String,
    department: String,
    title: String,
    role: String,
    dateOfBirth: Date,
    hireDate: Date,
    annualSalary: Number,
    employeeClassType: String,
    ssn: String,
    address: {
      street1: String,
      street2: String,
      city: String,
      state: String,
      zipCode: String,
      country: { type: String, default: 'US' }
    },
    mailingAddress: {
      street1: String,
      street2: String,
      city: String,
      state: String,
      zipCode: String,
      country: { type: String, default: 'US' }
    },
    dependents: [{
      _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
      name: { type: String, required: true },
      gender: {
        type: String,
        enum: ['Male', 'Female', 'Other', 'Prefer not to say'],
        required: true
      },
      dateOfBirth: { type: Date, required: true },
      relationship: {
        type: String,
        enum: ['Spouse', 'Child', 'Domestic Partner', 'Stepchild', 'Adopted Child', 'Other'],
        required: true
      },
      ssn: String,
      isStudent: { type: Boolean, default: false },
      isDisabled: { type: Boolean, default: false },
      address: {
        street1: String,
        street2: String,
        city: String,
        state: String,
        zipCode: String,
        country: { type: String, default: 'US' }
      },
      phoneNumber: String,
      email: String,
      isActive: { type: Boolean, default: true },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    }],
    emergencyContact: {
      name: String,
      relationship: String,
      phoneNumber: String,
      email: String
    }
  }
}, { timestamps: true });

const User = mongoose.model('User', userSchema);

/**
 * Parse CSV file and return structured data
 */
async function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    
    if (!fs.existsSync(filePath)) {
      reject(new Error(`CSV file not found: ${filePath}`));
      return;
    }

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => {
        console.log(`✅ Parsed ${results.length} rows from CSV file`);
        resolve(results);
      })
      .on('error', reject);
  });
}

/**
 * Map CSV gender to schema enum
 */
function mapGender(csvGender) {
  if (!csvGender) return 'Prefer not to say';
  
  const gender = csvGender.toLowerCase().trim();
  if (gender === 'm' || gender === 'male') return 'Male';
  if (gender === 'f' || gender === 'female') return 'Female';
  return 'Other';
}

/**
 * Map CSV relationship to schema enum
 */
function mapRelationship(csvRelationship) {
  if (!csvRelationship) return 'Other';
  
  const relationship = csvRelationship.toLowerCase().trim();
  if (relationship.includes('spouse') || relationship.includes('wife') || relationship.includes('husband')) {
    return 'Spouse';
  }
  if (relationship.includes('child') || relationship.includes('son') || relationship.includes('daughter')) {
    return 'Child';
  }
  if (relationship.includes('step')) return 'Stepchild';
  if (relationship.includes('adopt')) return 'Adopted Child';
  if (relationship.includes('partner')) return 'Domestic Partner';
  
  return 'Other';
}

/**
 * Parse date from various formats with validation
 */
function parseDate(dateString, personName = '') {
  if (!dateString) return null;

  try {
    // Handle common date formats: MM/DD/YYYY, MM-DD-YYYY, YYYY-MM-DD
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      console.warn(`⚠️ Invalid date format: ${dateString} for ${personName}`);
      return null;
    }

    // Check for future dates (likely data errors)
    const now = new Date();
    if (date > now) {
      console.warn(`⚠️ Future date detected: ${dateString} for ${personName} - skipping`);
      return null;
    }

    // Check for unreasonably old dates (over 120 years)
    const age = (now - date) / (1000 * 60 * 60 * 24 * 365.25);
    if (age > 120) {
      console.warn(`⚠️ Date too old: ${dateString} for ${personName} (${Math.floor(age)} years) - skipping`);
      return null;
    }

    return date;
  } catch (error) {
    console.warn(`⚠️ Error parsing date: ${dateString} for ${personName}`, error.message);
    return null;
  }
}

/**
 * Create full name from first and last name
 */
function createFullName(firstName, lastName) {
  const first = (firstName || '').trim();
  const last = (lastName || '').trim();
  return `${first} ${last}`.trim();
}

/**
 * Find user by name matching (exact matching for corrected CSV data)
 */
function findUserByName(users, fullName) {
  if (!fullName) return null;

  const searchName = fullName.trim();

  // Exact match (CSV names now match database names exactly)
  let user = users.find(u => u.name.toLowerCase().trim() === searchName.toLowerCase().trim());
  if (user) return user;

  // Partial match (first name + last name) as fallback
  const nameParts = searchName.split(' ');
  if (nameParts.length >= 2) {
    const firstName = nameParts[0].toLowerCase();
    const lastName = nameParts[nameParts.length - 1].toLowerCase();

    user = users.find(u => {
      const userName = u.name.toLowerCase().trim();
      return userName.includes(firstName) && userName.includes(lastName);
    });
  }

  return user;
}

/**
 * Group CSV data by employee (relationship = 'Employee' or empty)
 */
function groupCSVData(csvData) {
  const employees = [];
  const dependents = [];

  csvData.forEach(row => {
    const relationship = (row.Relationship || '').toLowerCase().trim();

    if (relationship === 'employee' || relationship === '' || relationship === 'self') {
      employees.push(row);
    } else {
      dependents.push(row);
    }
  });

  console.log(`📊 Found ${employees.length} employees and ${dependents.length} dependents in CSV`);
  return { employees, dependents };
}

/**
 * Calculate reasonable hire date based on date of birth
 */
function calculateHireDate(dateOfBirth) {
  if (!dateOfBirth) return null;

  const dob = new Date(dateOfBirth);
  const today = new Date();
  const age = today.getFullYear() - dob.getFullYear();

  // Assume hired between age 18-25, but not in the future
  let hireAge = 22; // Default hire age
  if (age < 25) hireAge = 18; // If they're young, assume hired at 18
  else if (age > 50) hireAge = 25; // If older, assume hired at 25

  const hireDate = new Date(dob);
  hireDate.setFullYear(dob.getFullYear() + hireAge);

  // Don't set hire date in the future
  if (hireDate > today) {
    hireDate.setFullYear(today.getFullYear() - 1);
  }

  return hireDate;
}

/**
 * Update user details from CSV data
 */
function updateUserDetails(user, csvRow) {
  const updates = {};

  // Only update if field is empty or doesn't exist
  if (!user.details) {
    updates.details = {};
  } else {
    updates.details = { ...user.details };
  }

  // Update date of birth
  if (csvRow.DOB && !user.details?.dateOfBirth) {
    const dob = parseDate(csvRow.DOB, createFullName(csvRow['First Name'], csvRow['Last Name']));
    if (dob) {
      updates.details.dateOfBirth = dob;
    }
  }

  // Update hire date (calculate from DOB if not present)
  if (!user.details?.hireDate) {
    const dob = user.details?.dateOfBirth || (csvRow.DOB ? parseDate(csvRow.DOB, createFullName(csvRow['First Name'], csvRow['Last Name'])) : null);
    if (dob) {
      const hireDate = calculateHireDate(dob);
      if (hireDate) {
        updates.details.hireDate = hireDate;
      }
    }
  }

  // Update employee class type (set to Full-Time for all SBS employees)
  if (!user.details?.employeeClassType) {
    updates.details.employeeClassType = 'Full-Time';
  }

  // Update address
  if (!user.details?.address || !user.details.address.street1) {
    updates.details.address = {
      street1: csvRow['Address 1'] || '',
      street2: csvRow['Address 2'] || '',
      city: csvRow.City || '',
      state: csvRow.State || '',
      zipCode: csvRow.Zip || '',
      country: 'US'
    };
  }

  // Update mailing address (same as address if not provided)
  if (!user.details?.mailingAddress) {
    updates.details.mailingAddress = updates.details.address || {
      street1: csvRow['Address 1'] || '',
      street2: csvRow['Address 2'] || '',
      city: csvRow.City || '',
      state: csvRow.State || '',
      zipCode: csvRow.Zip || '',
      country: 'US'
    };
  }

  return Object.keys(updates.details).length > 0 ? updates : null;
}

/**
 * Create dependent object from CSV data
 */
function createDependentFromCSV(csvRow, employeeName) {
  const fullName = createFullName(csvRow['First Name'], csvRow['Last Name']);

  if (!fullName) {
    console.warn(`⚠️ Skipping dependent with missing name for employee: ${employeeName}`);
    return null;
  }

  const dob = parseDate(csvRow.DOB, fullName);
  if (!dob) {
    console.warn(`⚠️ Skipping dependent ${fullName} - invalid date of birth: ${csvRow.DOB}`);
    return null;
  }

  return {
    name: fullName,
    gender: mapGender(csvRow.Sex),
    dateOfBirth: dob,
    relationship: mapRelationship(csvRow.Relationship),
    address: {
      street1: csvRow['Address 1'] || '',
      street2: csvRow['Address 2'] || '',
      city: csvRow.City || '',
      state: csvRow.State || '',
      zipCode: csvRow.Zip || '',
      country: 'US'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };
}

/**
 * Check if dependent already exists for user
 */
function dependentExists(user, dependentName, dateOfBirth) {
  if (!user.details?.dependents) return false;

  return user.details.dependents.some(dep => {
    const nameMatch = dep.name.toLowerCase().trim() === dependentName.toLowerCase().trim();
    const dobMatch = dep.dateOfBirth && dateOfBirth &&
                     dep.dateOfBirth.getTime() === dateOfBirth.getTime();
    return nameMatch || dobMatch;
  });
}

/**
 * Process and update users with CSV data
 */
async function processUsers(users, csvData) {
  const { employees, dependents } = groupCSVData(csvData);

  let updatedCount = 0;
  let dependentsAddedCount = 0;
  const errors = [];

  console.log('\n🔄 Processing employee updates...');

  // Process employees
  for (const employeeRow of employees) {
    try {
      const fullName = createFullName(employeeRow['First Name'], employeeRow['Last Name']);
      if (!fullName) continue;

      const user = findUserByName(users, fullName);
      if (!user) {
        console.log(`⚠️ Employee not found in database: ${fullName}`);
        continue;
      }

      console.log(`📝 Processing employee: ${user.name} (${user.email})`);

      // Update user details
      const updates = updateUserDetails(user, employeeRow);
      if (updates) {
        await User.updateOne({ _id: user._id }, { $set: updates });
        updatedCount++;

        // Log what fields were updated
        const updatedFields = [];
        if (updates.details.dateOfBirth) updatedFields.push('DOB');
        if (updates.details.hireDate) updatedFields.push('Hire Date');
        if (updates.details.employeeClassType) updatedFields.push('Employee Class');
        if (updates.details.address) updatedFields.push('Address');
        if (updates.details.mailingAddress) updatedFields.push('Mailing Address');

        console.log(`  ✅ Updated details for ${user.name}: ${updatedFields.join(', ')}`);
      } else {
        console.log(`  ℹ️ No updates needed for ${user.name} (data already exists)`);
      }

      // Find and add dependents for this employee
      const employeeDependents = dependents.filter(dep => {
        // Match by same address or explicit family grouping
        return dep['Address 1'] === employeeRow['Address 1'] &&
               dep.City === employeeRow.City &&
               dep.Zip === employeeRow.Zip;
      });

      console.log(`  👨‍👩‍👧‍👦 Found ${employeeDependents.length} potential dependents`);

      for (const depRow of employeeDependents) {
        const dependent = createDependentFromCSV(depRow, user.name);
        if (!dependent) continue;

        // Check if dependent already exists
        if (dependentExists(user, dependent.name, dependent.dateOfBirth)) {
          console.log(`    ℹ️ Dependent already exists: ${dependent.name}`);
          continue;
        }

        // Add dependent to user
        await User.updateOne(
          { _id: user._id },
          { $push: { 'details.dependents': dependent } }
        );

        dependentsAddedCount++;
        console.log(`    ✅ Added dependent: ${dependent.name} (${dependent.relationship})`);
      }

    } catch (error) {
      const errorMsg = `Error processing employee ${employeeRow['First Name']} ${employeeRow['Last Name']}: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      errors.push(errorMsg);
    }
  }

  return {
    updatedCount,
    dependentsAddedCount,
    errors
  };
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting Southern Benefits Systems user details update...\n');

    // Connect to MongoDB using same config as main app
    console.log('📡 Connecting to MongoDB...');

    if (!MONGO_URI) {
      throw new Error('MONGO_URI environment variable is not set. Please check your .env file.');
    }

    await mongoose.connect(MONGO_URI, {
      dbName: MONGO_DB_NAME,
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    });

    console.log(`✅ Connected to MongoDB database: ${MONGO_DB_NAME}\n`);

    // Get CSV file path (allow custom filename from command line)
    const filename = process.argv[2] || 'SBS-Census-Final.csv';
    const csvFilePath = path.join(__dirname, filename);
    console.log(`📄 Looking for CSV file: ${csvFilePath}`);

    // Parse CSV file
    const csvData = await parseCSVFile(csvFilePath);
    if (csvData.length === 0) {
      throw new Error('CSV file is empty or could not be parsed');
    }

    // Fetch all users for the company
    console.log(`\n👥 Fetching users for company: ${COMPANY_ID}`);
    const users = await User.find({ companyId: COMPANY_ID }).lean();
    console.log(`✅ Found ${users.length} users in database\n`);

    if (users.length === 0) {
      throw new Error('No users found for the specified company');
    }

    // Display current users
    console.log('📋 Current users in database:');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name} (${user.email}) - ${user.isActivated ? 'Activated' : 'Not Activated'}`);
    });
    console.log('');

    // Process users
    const result = await processUsers(users, csvData);

    // Display results
    console.log('\n📊 Update Summary:');
    console.log(`✅ Users updated: ${result.updatedCount}`);
    console.log(`👨‍👩‍👧‍👦 Dependents added: ${result.dependentsAddedCount}`);
    console.log(`📋 Fields updated: DOB, Hire Date, Employee Class (Full-Time), Address, Mailing Address`);

    if (result.errors.length > 0) {
      console.log(`\n❌ Errors encountered (${result.errors.length}):`);
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    console.log('\n🎉 Script completed successfully!');

  } catch (error) {
    console.error('\n💥 Script failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('📡 Disconnected from MongoDB');
    }
  }
}

/**
 * Validation function to check CSV format
 */
function validateCSVFormat(csvData) {
  const requiredFields = ['First Name', 'Last Name', 'Sex', 'DOB', 'Address 1', 'City', 'State', 'Zip', 'Relationship'];

  if (csvData.length === 0) {
    throw new Error('CSV file is empty');
  }

  const firstRow = csvData[0];
  const missingFields = requiredFields.filter(field => !(field in firstRow));

  if (missingFields.length > 0) {
    throw new Error(`CSV file is missing required columns: ${missingFields.join(', ')}`);
  }

  console.log('✅ CSV format validation passed');
  return true;
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  parseCSVFile,
  processUsers,
  validateCSVFormat,
  mapGender,
  mapRelationship,
  parseDate,
  createFullName,
  findUserByName
};
