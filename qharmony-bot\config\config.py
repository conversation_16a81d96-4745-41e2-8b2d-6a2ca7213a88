"""
 Configuration settings for the microservice.
"""

from envyaml import EnvYAML


class Config:
    """Configuration class to manage environment variables and settings."""

    def __init__(self):
        # load_dotenv()
        self.env = EnvYAML('config/config.yaml')

        # URI, CREDENTIALS, CONNECTIONS
        self.express_uri: str = self.env["EXPRESS_URI"]
        self.openai_key: str = self.env["OPENAI_API_KEY"]
        self.redis_url: str = self.env["REDIS_URL"]
        self.pinecone_api_key: str = self.env["PINECONE_API_KEY"]
        self.blob_connection_string: str = self.env["BLOB_CONNECTION_STRING"]

        # SETTINGS PARAMETERS
        self.session_timeout: int = int(self.env["SESSION_TIMEOUT"]) # for redis and memory
        self.task_cleanup_time: int = int(self.env["TASK_CLEANUP_TIME"]) # seconds
        self.max_memory_sessions: int = int(self.env["MAX_MEMORY_SESSIONS"])
        # REDIS TIMEOUT MULTIPLE
        self.redis_timeout_multiple: int = 5

        # DATABASE CONFIG
        self.mongodb_uri: str = self.env['MONGO_DB_URI']
        self.mongo_db: str = self.env["MONGO_DB"]

        # MODEL CONFIG
        self.openai_model_name: str = self.env['OPENAI_MODEL_NAME']
        self.openai_model_category: str = self.env["OPENAI_MODEL_CATEGORY"]
        self.chat_model_name: str = self.env['CHAT_MODEL_NAME']
        self.chat_model_category: str = self.env["CHAT_MODEL_CATEGORY"]
        self.model_temperature: float = float(self.env["MODEL_TEMPERATURE"])
        self.memory_order: int = int(self.env["MEMORY_ORDER"])
        # Fix for boolean parsing
        self.memory: bool = self._parse_bool(self.env['MEMORY'])

        # CACHE CONFIG
        self.lru_cache_ttl: int = int(self.env["LRU_CACHE_TTL"])
        self.lru_cache_maxsize: int = int(self.env["LRU_CACHE_MAXSIZE"])

        #VECTOR DATABSE
        #index and namespace
        self.index_name: str = self.env["INDEX_NAME"]
        self.namespace_prefix: str = self.env["NAMESPACE_PREFIX"]

        # CHUNK CONFIG FOR VECTOR DATABASE OPERATIONS
        self.chunk_size: int = int(self.env["CHUNK_SIZE"])
        self.chunk_overlap: int = int(self.env["CHUNK_OVERLAP"])
        #FILTERS
        self.k_filter: int = int(self.env['K_FILTER'])

        # LOGGING
        # Fix for boolean parsing
        self.ai_logging: bool = self._parse_bool(self.env["AI_LOGGING"])

        # INFERENCE VARIABLES LOADED AT INTERFENCE TIME
        self.inference_variables = ['documents']

        # OCR
        # Fix for boolean parsing
        self.ocr_available: bool = self._parse_bool(self.env['OCR_AVAILABLE'])

    def _parse_bool(self, value):
        """Parse string boolean values correctly."""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', 'yes', '1', 'y', 't')
        return bool(value)

config = Config()
