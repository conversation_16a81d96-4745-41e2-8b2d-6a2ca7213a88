/**
 * CSV Validation Script for Southern Benefits Systems
 * 
 * This script validates the Census.csv file format before running the main update script
 */

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Required CSV columns
const REQUIRED_COLUMNS = [
  'First Name',
  'Last Name', 
  'Sex',
  'DOB',
  'Address 1',
  'City',
  'State',
  'Zip',
  'Relationship'
];

// Optional columns
const OPTIONAL_COLUMNS = [
  'Address 2',
  'Marital Status'
];

// Valid relationship values
const VALID_RELATIONSHIPS = [
  'employee', 'self', '',
  'spouse', 'wife', 'husband',
  'child', 'son', 'daughter',
  'stepchild', 'adopted child',
  'domestic partner', 'partner'
];

// Valid gender values
const VALID_GENDERS = ['m', 'f', 'male', 'female', 'other'];

/**
 * Parse and validate CSV file
 */
async function validateCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    const errors = [];
    let rowNumber = 1; // Start at 1 for header
    
    if (!fs.existsSync(filePath)) {
      reject(new Error(`CSV file not found: ${filePath}`));
      return;
    }

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('headers', (headers) => {
        console.log('📋 CSV Headers found:', headers);
        
        // Check for required columns
        const missingColumns = REQUIRED_COLUMNS.filter(col => !headers.includes(col));
        if (missingColumns.length > 0) {
          errors.push(`Missing required columns: ${missingColumns.join(', ')}`);
        }
        
        // Check for unexpected columns
        const unexpectedColumns = headers.filter(col => 
          !REQUIRED_COLUMNS.includes(col) && !OPTIONAL_COLUMNS.includes(col)
        );
        if (unexpectedColumns.length > 0) {
          console.log('⚠️ Unexpected columns (will be ignored):', unexpectedColumns);
        }
      })
      .on('data', (data) => {
        rowNumber++;
        const rowErrors = validateRow(data, rowNumber);
        if (rowErrors.length > 0) {
          errors.push(...rowErrors);
        }
        results.push(data);
      })
      .on('end', () => {
        resolve({ data: results, errors, totalRows: results.length });
      })
      .on('error', reject);
  });
}

/**
 * Validate individual row
 */
function validateRow(row, rowNumber) {
  const errors = [];
  
  // Check required fields
  REQUIRED_COLUMNS.forEach(column => {
    if (!row[column] || row[column].trim() === '') {
      errors.push(`Row ${rowNumber}: Missing required field '${column}'`);
    }
  });
  
  // Validate specific fields
  if (row['First Name'] && row['Last Name']) {
    const fullName = `${row['First Name'].trim()} ${row['Last Name'].trim()}`;
    if (fullName.length < 2) {
      errors.push(`Row ${rowNumber}: Invalid name '${fullName}'`);
    }
  }
  
  // Validate gender
  if (row['Sex']) {
    const gender = row['Sex'].toLowerCase().trim();
    if (!VALID_GENDERS.includes(gender)) {
      errors.push(`Row ${rowNumber}: Invalid gender '${row['Sex']}'. Must be M, F, Male, or Female`);
    }
  }
  
  // Validate date of birth
  if (row['DOB']) {
    const date = new Date(row['DOB']);
    if (isNaN(date.getTime())) {
      errors.push(`Row ${rowNumber}: Invalid date format '${row['DOB']}'. Use MM/DD/YYYY`);
    } else {
      // Check if date is reasonable (not in future, not too old)
      const now = new Date();
      const age = (now - date) / (1000 * 60 * 60 * 24 * 365.25);
      if (age < 0) {
        errors.push(`Row ${rowNumber}: Date of birth cannot be in the future`);
      } else if (age > 120) {
        errors.push(`Row ${rowNumber}: Date of birth seems too old (${Math.floor(age)} years)`);
      }
    }
  }
  
  // Validate relationship
  if (row['Relationship']) {
    const relationship = row['Relationship'].toLowerCase().trim();
    if (!VALID_RELATIONSHIPS.includes(relationship)) {
      errors.push(`Row ${rowNumber}: Invalid relationship '${row['Relationship']}'. Valid values: Employee, Spouse, Child, etc.`);
    }
  }
  
  // Validate ZIP code
  if (row['Zip']) {
    const zip = row['Zip'].trim();
    if (!/^\d{5}(-\d{4})?$/.test(zip)) {
      errors.push(`Row ${rowNumber}: Invalid ZIP code '${zip}'. Use 12345 or 12345-6789 format`);
    }
  }
  
  // Validate state
  if (row['State']) {
    const state = row['State'].trim();
    if (state.length !== 2) {
      errors.push(`Row ${rowNumber}: Invalid state '${state}'. Use 2-letter abbreviation (e.g., SC)`);
    }
  }
  
  return errors;
}

/**
 * Analyze CSV data structure
 */
function analyzeData(data) {
  const employees = data.filter(row => {
    const rel = (row['Relationship'] || '').toLowerCase().trim();
    return rel === 'employee' || rel === 'self' || rel === '';
  });
  
  const dependents = data.filter(row => {
    const rel = (row['Relationship'] || '').toLowerCase().trim();
    return rel !== 'employee' && rel !== 'self' && rel !== '';
  });
  
  // Group dependents by address to find families
  const families = {};
  data.forEach(row => {
    const address = `${row['Address 1']}-${row['City']}-${row['Zip']}`;
    if (!families[address]) {
      families[address] = { employees: [], dependents: [] };
    }
    
    const rel = (row['Relationship'] || '').toLowerCase().trim();
    if (rel === 'employee' || rel === 'self' || rel === '') {
      families[address].employees.push(row);
    } else {
      families[address].dependents.push(row);
    }
  });
  
  return {
    totalRows: data.length,
    employees: employees.length,
    dependents: dependents.length,
    families: Object.keys(families).length,
    familyDetails: families
  };
}

/**
 * Main validation function
 */
async function main() {
  try {
    console.log('🔍 Validating CSV file for Southern Benefits Systems...\n');

    // Allow custom filename from command line argument
    const filename = process.argv[2] || 'SBS-Census-Final.csv';
    const csvFilePath = path.join(__dirname, filename);
    console.log(`📄 Checking file: ${csvFilePath}`);
    
    const result = await validateCSV(csvFilePath);
    
    console.log(`\n📊 Validation Results:`);
    console.log(`✅ Total rows processed: ${result.totalRows}`);
    
    if (result.errors.length === 0) {
      console.log('✅ No validation errors found!');
    } else {
      console.log(`❌ Found ${result.errors.length} validation errors:`);
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    // Analyze data structure
    const analysis = analyzeData(result.data);
    console.log(`\n📈 Data Analysis:`);
    console.log(`👥 Employees: ${analysis.employees}`);
    console.log(`👨‍👩‍👧‍👦 Dependents: ${analysis.dependents}`);
    console.log(`🏠 Families: ${analysis.families}`);
    
    // Show family groupings
    console.log(`\n🏠 Family Groupings:`);
    Object.entries(analysis.familyDetails).forEach(([address, family], index) => {
      if (family.employees.length > 0) {
        console.log(`  ${index + 1}. ${address}:`);
        console.log(`     👤 Employees: ${family.employees.map(e => `${e['First Name']} ${e['Last Name']}`).join(', ')}`);
        if (family.dependents.length > 0) {
          console.log(`     👨‍👩‍👧‍👦 Dependents: ${family.dependents.map(d => `${d['First Name']} ${d['Last Name']} (${d['Relationship']})`).join(', ')}`);
        }
      }
    });
    
    if (result.errors.length === 0) {
      console.log('\n🎉 CSV file is valid and ready for processing!');
      console.log('💡 Run the main script: node update-sbs-user-details.js');
    } else {
      console.log('\n⚠️ Please fix the validation errors before running the main script.');
    }
    
  } catch (error) {
    console.error('\n💥 Validation failed:', error.message);
    process.exit(1);
  }
}

// Run validation if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  validateCSV,
  validateRow,
  analyzeData,
  REQUIRED_COLUMNS,
  VALID_RELATIONSHIPS,
  VALID_GENDERS
};
