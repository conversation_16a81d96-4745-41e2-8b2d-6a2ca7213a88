
import pandas as pd
import numpy as np
from scipy.stats import norm
import shap
import pickle
import joblib
import xgboost as xgb
import os
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Union, Any, Tuple
from langchain_openai.chat_models import ChatOpenAI
import json
from scipy.stats import norm
from .dataModels import Question,QuestionsResponse,UserAnswer,LifeExpectancyPrediction
from .WellnessQuestions import get_life_expectancy_questions, get_life_expectancy_questions_json
import datetime, math
from .helpers import preprocess_dataframe

async def predict_heart_disease_single_function(user_response: UserAnswer, model, preprocessors, training_columns) -> Optional[float]:
    """
    Takes user responses in full JSON format, extracts relevant features,
    preprocesses, predicts, and returns the probability of heart disease.

    Parameters:
    - user_response (UserAnswer): Pydantic model containing user answers as a dictionary.
    - model: The pre-loaded trained heart disease prediction model.
    - preprocessors: The pre-loaded fitted preprocessors for the heart disease model.
    - training_columns (list): List of column names expected by the model AFTER preprocessing.

    Returns:
    - float: The predicted probability of having heart disease (class 1).
    - None: If artifacts are missing, or an error occurs during prediction.
    """
    
    # Explicitly check if each artifact is not None
    if model is None or preprocessors is None or training_columns is None:
        print("Error: Heart disease model, preprocessors, or training columns missing/not loaded.")
        return None

    # Check if training_columns is valid and non-empty
    if not hasattr(training_columns, '__len__') or len(training_columns) == 0:
        print("Error: training_columns artifact is invalid or empty.")
        return None

    # Access answers directly from the Pydantic model
    answers = user_response.answers
    
    # Define mappings and feature types (unchanged from original)
    question_to_feature_map = {
        "gender": "Sex", "bmi": "BMI", "age": "Age", "race": "Race",
        "general_health": "GenHealth", "diabetic": "Diabetic", "asthma": "Asthma",
        "stroke": "Stroke", "smoking": "Smoking", "alcohol": "AlcoholDrinking",
        "physical_activity": "PhysicalActivity", "sleep_hours": "SleepTime",
        "walking": "DiffWalking"
    }
    frequency_map_questions = {"smoking", "alcohol", "physical_activity", "walking"}

    # Feature types required by the heart disease model's preprocessor
    hd_column_types = {
        'yes_or_no': ['Asthma', 'Stroke', 'Smoking', 'AlcoholDrinking', 'PhysicalActivity', 'DiffWalking'],
        'ordinal': ['GenHealth'],
        'numeric': ['SleepTime', 'BMI'],
        'nominal': ['Sex', 'Race', 'Age', 'Diabetic']
    }
    hd_ordinal_categories = {
        'GenHealth': ['Poor', 'Fair', 'Good', 'Very good', 'Excellent']
    }
    required_features = set(hd_column_types['yes_or_no'] + hd_column_types['ordinal'] +
                           hd_column_types['numeric'] + hd_column_types['nominal'])

    # Extract, map, and filter features
    model_input_dict = {}
    for question_id, feature_name in question_to_feature_map.items():
        if feature_name in required_features:
            if question_id not in answers:
                print(f"Warning: Answer for '{question_id}' (feature '{feature_name}') not found in user response. Cannot predict.")
                return None
            user_answer = answers[question_id]

            if question_id in frequency_map_questions:
                if isinstance(user_answer, str):
                    ans_lower = user_answer.lower()
                    if ans_lower == "rarely":
                        model_input_dict[feature_name] = "No"
                    elif ans_lower in ["sometimes", "often"]:
                        model_input_dict[feature_name] = "Yes"
                    else:
                        print(f"Warning: Unexpected value '{user_answer}' for '{question_id}'. Treating as 'No'.")
                        model_input_dict[feature_name] = "No"
                else:
                    print(f"Warning: Non-string value '{user_answer}' for freq question '{question_id}'. Treating as 'No'.")
                    model_input_dict[feature_name] = "No"
            else:
                model_input_dict[feature_name] = user_answer

    # Check for missing required features
    missing_req_features = required_features - set(model_input_dict.keys())
    if missing_req_features:
        print(f"Error: Missing required features after mapping: {missing_req_features}. Cannot predict.")
        return None

    # Preprocessing and prediction
    try:
        input_df = pd.DataFrame([model_input_dict])
        input_preprocessed = preprocess_dataframe(
            df=input_df,
            column_types=hd_column_types,
            ordinal_categories=hd_ordinal_categories,
            fitted_preprocessors=preprocessors
        )
        input_reindexed = input_preprocessed.reindex(columns=training_columns, fill_value=0)
        prediction_proba = model.predict_proba(input_reindexed)
        heart_disease_probability = prediction_proba[0, 1]
        return heart_disease_probability*100

    except Exception as e:
        print(f"An error occurred during prediction: {e}")
        return None

