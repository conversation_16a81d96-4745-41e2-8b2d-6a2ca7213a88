import logger from '../../utils/logger';
import PlanModelClass, { PlanDataInterface, UpdateablePlanDataInterface } from '../../nosql/preEnrollment/plan.model';
import PlanAssignmentModelClass from '../../nosql/preEnrollment/planAssignment.model';
import CarrierModelClass from '../../nosql/preEnrollment/carrier.model';
import UserModelClass from '../../nosql/user.model';
import AzureBlobService from '../azure';
import AzureNamespaceService from '../azureNamespace.service';
import { v4 as uuidv4 } from 'uuid';
import {
  PLAN_TYPES,
  PRE_ENROLLMENT_COVERAGE_TYPES,
  PRE_ENROLLMENT_COVERAGE_SUBTYPES,
  METAL_TIERS,
  isValidPreEnrollmentCombination,
  PLAN_STATUSES,
  CARRIER_STATUSES
} from '../../constants';
import { isValidObjectId } from '../../utils/validation';

/**
 * Plan Service
 * Modular service layer for plan operations
 * Maintains all business logic while providing clean separation of concerns
 */
export class PlanService {
  
  /**
   * Validate user access and return user details
   */
  static async validateUserAccess(userId: string) {
    try {
      const user = await UserModelClass.getDataById(userId);
      return {
        hasAccess: !!user,
        user
      };
    } catch (error) {
      logger.error('Error validating user access:', error);
      return { hasAccess: false, user: null };
    }
  }

  /**
   * Get plan with access control validation
   */
  static async getPlanWithAccess(planId: string, userId: string, user: any) {
    try {
      if (!isValidObjectId(planId)) {
        return { hasAccess: false, reason: 'Invalid plan ID format', plan: null };
      }

      const plan = await PlanModelClass.getDataById(planId);
      if (!plan) {
        return { hasAccess: false, reason: 'Plan not found', plan: null };
      }

      // Check access permissions
      const hasAccess = Boolean(plan?.isTemplate) || // Templates are accessible to all
                       UserModelClass.isSuperAdmin(user) || // Super admins can access all
                       (Boolean(user?.isBroker) && plan.brokerId === userId) || // Broker's own plans
                       (Boolean(user?.isAdmin) && !Boolean(user?.isBroker)); // Employer admins can read templates

      // Additional check for employees - they can access plans through assignments
      if (!hasAccess && !user.isAdmin && !user.isBroker && !UserModelClass.isSuperAdmin(user)) {
        const planAssignment = await PlanAssignmentModelClass.getDataByPlanAndCompany(planId, user.companyId);
        if (planAssignment) {
          return { hasAccess: true, reason: null, plan };
        }
      }

      if (!hasAccess) {
        return { hasAccess: false, reason: 'Access denied to this plan', plan: null };
      }

      return { hasAccess: true, reason: null, plan };
    } catch (error) {
      logger.error('Error getting plan with access:', error);
      return { hasAccess: false, reason: 'Internal server error', plan: null };
    }
  }

  /**
   * Validate plan creation data
   */
  static async validatePlanCreation(planData: any, userId: string, user: any) {
    const errors: string[] = [];

    // Required fields validation
    if (!planData.planName || !planData.coverageType || !planData.coverageSubTypes || !planData.description) {
      errors.push('Plan name, coverage type, coverage subtypes, and description are required');
    }

    // Coverage type validation
    if (planData.coverageType && !PRE_ENROLLMENT_COVERAGE_TYPES.includes(planData.coverageType)) {
      errors.push('Invalid coverage type');
    }

    // Coverage subtypes validation
    if (planData.coverageSubTypes && Array.isArray(planData.coverageSubTypes)) {
      for (const subType of planData.coverageSubTypes) {
        if (!PRE_ENROLLMENT_COVERAGE_SUBTYPES.includes(subType)) {
          errors.push(`Invalid coverage subtype: ${subType}`);
        }
        if (planData.coverageType && !isValidPreEnrollmentCombination(planData.coverageType, subType)) {
          errors.push(`Invalid combination: ${subType} is not valid for ${planData.coverageType}`);
        }
      }
    }

    // Plan type validation
    if (planData.planType && !PLAN_TYPES.includes(planData.planType)) {
      errors.push('Invalid plan type');
    }

    // Metal tier validation
    if (planData.metalTier && !METAL_TIERS.includes(planData.metalTier)) {
      errors.push('Invalid metal tier');
    }

    // Template creation permissions
    if (planData.isTemplate && !UserModelClass.isSuperAdmin(user)) {
      errors.push('Only super admins can create templates');
    }

    // Carrier validation
    if (planData.carrierId) {
      if (!isValidObjectId(planData.carrierId)) {
        errors.push('Invalid carrier ID format');
      } else {
        const carrier = await CarrierModelClass.getDataById(planData.carrierId, user.isBroker ? userId : undefined);
        if (!carrier) {
          errors.push('Carrier not found or access denied');
        } else if (carrier.status !== CARRIER_STATUSES[0]) { // 'Active'
          errors.push(`Cannot assign carrier with status "${carrier.status}". Only Active carriers can be assigned to plans.`);
        } else {
          // Validate carrier compatibility
          const carrierValidation = await CarrierModelClass.validatePlanCarrierCompatibility(
            planData.carrierId,
            planData.planType,
            planData.coverageType,
            planData.coverageSubTypes
          );
          if (!carrierValidation.isCompatible) {
            errors.push('Carrier compatibility validation failed: ' + carrierValidation.errors.join(', '));
          }
        }
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Create a new plan
   */
  static async createPlan(planData: any, userId: string, user: any) {
    try {
      // Validate plan data
      const validation = await this.validatePlanCreation(planData, userId, user);
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(', '), plan: null };
      }

      // Determine ownership based on user role and isTemplate flag
      let brokerId: string | undefined;
      let brokerageId: string | undefined;
      let shouldBeTemplate = planData.isTemplate;

      // Super admins create system templates by default, others create regular plans
      if (UserModelClass.isSuperAdmin(user)) {
        shouldBeTemplate = true;
        brokerId = userId;
        brokerageId = user.companyId;
      } else if (user.isBroker) {
        shouldBeTemplate = false; // Brokers cannot create templates
        brokerId = userId;
        brokerageId = user.companyId;
      } else {
        return { success: false, error: 'Only super admins and brokers can create plans', plan: null };
      }

      // Broker-specific uniqueness validation (skip for templates)
      if (brokerId && !shouldBeTemplate) {
        const nameValidation = await PlanModelClass.validateBrokerUniquePlanName(brokerId, planData.planName);
        if (!nameValidation.isUnique) {
          return { success: false, error: nameValidation.message, plan: null };
        }

        if (planData.planCode) {
          const codeValidation = await PlanModelClass.validateBrokerUniquePlanCode(brokerId, planData.planCode);
          if (!codeValidation.isUnique) {
            return { success: false, error: codeValidation.message, plan: null };
          }
        }
      }

      const newPlanData: PlanDataInterface = {
        planName: planData.planName,
        planCode: planData.planCode,
        brokerId,
        brokerageId,
        isTemplate: shouldBeTemplate,
        coverageType: planData.coverageType,
        coverageSubTypes: planData.coverageSubTypes,
        planType: planData.planType,
        metalTier: planData.metalTier,
        description: planData.description,
        highlights: planData.highlights || [],
        informativeLinks: planData.informativeLinks || [],
        benefitDetails: planData.benefitDetails,
        documentIds: [],
        carrierId: planData.carrierId,
        carrierPlanId: planData.carrierPlanId,
        status: shouldBeTemplate ? PLAN_STATUSES[3] : PLAN_STATUSES[1], // 'Template' : 'Active'
        isActivated: true
      };

      const createdPlan = await PlanModelClass.addData(newPlanData);
      if (!createdPlan) {
        return { success: false, error: 'Failed to create plan', plan: null };
      }

      return { success: true, error: null, plan: createdPlan };
    } catch (error) {
      logger.error('Error in createPlan service:', error);
      return { success: false, error: 'Internal server error during plan creation', plan: null };
    }
  }

  /**
   * Get plans for user based on access control
   */
  static async getPlansForUser(userId: string, user: any, isTemplate?: boolean, includeCarrierData: boolean = true) {
    try {
      let plans: PlanDataInterface[] = [];

      if (isTemplate === true) {
        // Get system templates (accessible to all users)
        plans = await PlanModelClass.getTemplates();
      } else if (UserModelClass.isSuperAdmin(user)) {
        // Super admins can see all plans
        plans = await PlanModelClass.getAllData();
      } else if (user.isBroker) {
        // Brokers can see system templates + their own plans (getDataByBrokerId already includes templates)
        plans = await PlanModelClass.getDataByBrokerId(userId);
      } else if (user.isAdmin) {
        // Employer admins can see system templates (READ ONLY)
        plans = await PlanModelClass.getTemplates();
      } else {
        // Employees can see system templates (READ ONLY)
        plans = await PlanModelClass.getTemplates();
      }

      // 🎯 ENHANCED: Populate carrier data for consistency
      if (includeCarrierData && plans.length > 0) {
        return await this.populateCarrierData(plans);
      }

      return plans;
    } catch (error) {
      logger.error('Error getting plans for user:', error);
      return [];
    }
  }

  /**
   * 🎯 OPTIMIZED: Get plans with database-level filtering and pagination
   */
  static async getPlansOptimized(userId: string, user: any, filters: any = {}, pagination?: { page: number; limit: number }): Promise<{
    plans: PlanDataInterface[];
    totalCount: number;
    totalPages?: number;
    appliedFilters: string[];
  }> {
    try {
      // Build database query based on user role and filters
      const { query, appliedFilters } = this.buildPlanQuery(userId, user, filters);

      // Determine if carrier data should be included (default: true for consistency)
      const includeCarrierData = filters.includeCarrierData !== 'false';

      // Use optimized model method for database-level filtering and pagination
      const result = await PlanModelClass.getPlansOptimized(query, pagination);

      // 🎯 ENHANCED: Populate carrier data for consistency with plan assignments
      if (includeCarrierData && result.plans.length > 0) {
        const plansWithCarriers = await this.populateCarrierData(result.plans);
        return {
          ...result,
          plans: plansWithCarriers,
          appliedFilters: [...appliedFilters, 'includeCarrierData']
        };
      }

      return {
        ...result,
        appliedFilters
      };
    } catch (error) {
      logger.error('Error getting optimized plans:', error);
      return { plans: [], totalCount: 0, appliedFilters: [] };
    }
  }

  /**
   * 🎯 NEW: Populate carrier data for plans (consistent with plan assignments)
   */
  static async populateCarrierData(plans: PlanDataInterface[]): Promise<Array<PlanDataInterface & { carrierData?: any }>> {
    try {
      const CarrierModelClass = require('../../nosql/preEnrollment/carrier.model').default;

      const plansWithCarriers = await Promise.all(
        plans.map(async (plan) => {
          if (plan.carrierId) {
            try {
              const carrierData = await CarrierModelClass.getDataById(plan.carrierId);
              return { ...plan, carrierData };
            } catch (error) {
              console.warn(`Failed to fetch carrier data for plan ${plan._id}:`, error);
              return plan;
            }
          }
          return plan;
        })
      );

      return plansWithCarriers;
    } catch (error) {
      console.error('Error populating carrier data:', error);
      return plans; // Return original plans if population fails
    }
  }

  /**
   * Build MongoDB query based on user role and filters
   */
  static buildPlanQuery(userId: string, user: any, filters: any): { query: any; appliedFilters: string[] } {
    const appliedFilters: string[] = [];
    let baseQuery: any = {};

    // 1. Apply role-based access control
    if (filters.isTemplate === 'true') {
      baseQuery.isTemplate = true;
      appliedFilters.push('templates-only');
    } else {
      if (UserModelClass.isSuperAdmin(user)) {
        // SuperAdmins see all plans (no additional filter)
        appliedFilters.push('superadmin-all-access');
      } else if (user.isBroker) {
        // Brokers see templates + own plans
        baseQuery.$or = [
          { isTemplate: true },
          { brokerId: userId }
        ];
        appliedFilters.push('broker-accessible');
      } else if (user.isAdmin) {
        // Employer admins can see system templates (READ ONLY)
        baseQuery.isTemplate = true;
        appliedFilters.push('employer-admin-templates');
      } else {
        // Employees can see system templates (READ ONLY)
        baseQuery.isTemplate = true;
        appliedFilters.push('employee-templates');
      }
    }

    // 2. Apply status filter
    if (filters.status) {
      baseQuery.status = filters.status;
      appliedFilters.push(`status:${filters.status}`);
    }

    // 3. Apply coverage type filter
    if (filters.coverageType) {
      baseQuery.coverageType = filters.coverageType;
      appliedFilters.push(`coverageType:${filters.coverageType}`);
    }

    // 4. Apply plan type filter
    if (filters.planType) {
      baseQuery.planType = filters.planType;
      appliedFilters.push(`planType:${filters.planType}`);
    }

    // 5. Apply coverage subtype filter (array includes check)
    if (filters.coverageSubtype) {
      baseQuery.coverageSubTypes = { $in: [filters.coverageSubtype] };
      appliedFilters.push(`coverageSubtype:${filters.coverageSubtype}`);
    }

    // 6. Apply plan name filter (matching original logic exactly)
    if (filters.planName) {
      const searchTerm = filters.planName.toLowerCase().trim(); // Match original trim logic
      if (filters.strict === 'true') {
        // Strict mode: Case-insensitive exact match
        baseQuery.planName = new RegExp(`^${this.escapeRegex(searchTerm)}$`, 'i');
        appliedFilters.push(`planName-exact:${filters.planName}`);
      } else {
        // Normal mode: Case-insensitive partial match (like includes)
        baseQuery.planName = new RegExp(this.escapeRegex(searchTerm), 'i');
        appliedFilters.push(`planName-partial:${filters.planName}`);
      }
    }

    // 7. Apply plan code filter (matching original logic exactly)
    if (filters.planCode) {
      const searchTerm = filters.planCode.toLowerCase().trim(); // Match original trim logic
      if (filters.strict === 'true') {
        // Strict mode: Case-insensitive exact match
        baseQuery.planCode = new RegExp(`^${this.escapeRegex(searchTerm)}$`, 'i');
        appliedFilters.push(`planCode-exact:${filters.planCode}`);
      } else {
        // Normal mode: Case-insensitive partial match (like includes)
        baseQuery.planCode = new RegExp(this.escapeRegex(searchTerm), 'i');
        appliedFilters.push(`planCode-partial:${filters.planCode}`);
      }
    }

    return { query: baseQuery, appliedFilters };
  }

  /**
   * Escape special regex characters to match original string behavior
   */
  static escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Get assignable plans for user
   */
  static async getAssignablePlansForUser(userId: string, user: any, includeCarrierData: boolean = true) {
    try {
      let plans: PlanDataInterface[] = [];

      if (UserModelClass.isSuperAdmin(user)) {
        // Super admins can see all assignable plans
        plans = await PlanModelClass.getAssignablePlans();
      } else if (user.isBroker) {
        // Brokers can see their own assignable plans only
        plans = await PlanModelClass.getAssignablePlans(userId);
      } else {
        // Company admins and employees cannot assign plans
        return [];
      }

      // 🎯 ENHANCED: Populate carrier data for consistency
      if (includeCarrierData && plans.length > 0) {
        return await this.populateCarrierData(plans);
      }

      return plans;
    } catch (error) {
      logger.error('Error getting assignable plans for user:', error);
      return [];
    }
  }

  /**
   * Filter plans based on query parameters
   */
  static filterPlans(plans: PlanDataInterface[], query: any) {
    let filteredPlans = [...plans];

    // Apply filters
    if (query.status) {
      filteredPlans = filteredPlans.filter(plan => plan.status === query.status);
    }
    if (query.coverageType) {
      filteredPlans = filteredPlans.filter(plan => plan.coverageType === query.coverageType);
    }
    if (query.planType) {
      filteredPlans = filteredPlans.filter(plan => plan.planType === query.planType);
    }

    // Enhanced filtering with conditional strict mode
    const isStrictMode = query.strict === 'true';

    if (query.planName) {
      const searchTerm = query.planName.toLowerCase().trim();
      filteredPlans = filteredPlans.filter(plan => {
        if (!plan.planName) return false;

        const planNameLower = plan.planName.toLowerCase();

        if (isStrictMode) {
          // Strict mode: Case-insensitive exact match
          return planNameLower === searchTerm;
        } else {
          // Normal mode: Case-insensitive partial match
          return planNameLower.includes(searchTerm);
        }
      });
    }

    if (query.planCode) {
      const searchTerm = query.planCode.toLowerCase().trim();
      filteredPlans = filteredPlans.filter(plan => {
        if (!plan.planCode) return false;

        const planCodeLower = plan.planCode.toLowerCase();

        if (isStrictMode) {
          // Strict mode: Case-insensitive exact match
          return planCodeLower === searchTerm;
        } else {
          // Normal mode: Case-insensitive partial match
          return planCodeLower.includes(searchTerm);
        }
      });
    }

    if (query.coverageSubtype) {
      // Coverage subtype filtering (always exact match within array)
      filteredPlans = filteredPlans.filter(plan =>
        plan.coverageSubTypes &&
        Array.isArray(plan.coverageSubTypes) &&
        plan.coverageSubTypes.includes(query.coverageSubtype)
      );
    }

    return filteredPlans;
  }

  /**
   * Create pagination object
   */
  static createPagination(page: number, limit: number, totalCount: number) {
    return {
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit),
      totalPlans: totalCount,
      hasNext: (page * limit) < totalCount,
      hasPrev: page > 1
    };
  }

  /**
   * Check if user can update plan
   */
  static canUserUpdatePlan(plan: PlanDataInterface, userId: string, user: any) {
    return (Boolean(plan?.isTemplate) && UserModelClass.isSuperAdmin(user)) || // Only super admins can update templates
           UserModelClass.isSuperAdmin(user) || // Super admins can update all plans
           (Boolean(user?.isBroker) && plan.brokerId === userId); // Brokers can update their own plans
  }

  /**
   * Check if user can perform status operations on plan
   */
  static canUserManagePlanStatus(plan: PlanDataInterface, userId: string, user: any) {
    return (Boolean(plan?.isTemplate) && UserModelClass.isSuperAdmin(user)) || // Only super admins can manage template status
           UserModelClass.isSuperAdmin(user) || // Super admins can manage all plan status
           (Boolean(user?.isBroker) && plan.brokerId === userId); // Brokers can manage their own plan status
  }

  /**
   * Check if user can duplicate plan
   */
  static canUserDuplicatePlan(plan: PlanDataInterface, userId: string, user: any) {
    return (Boolean(plan?.isTemplate) && (UserModelClass.isSuperAdmin(user) || Boolean(user?.isBroker))) || // Templates accessible to super admins and brokers
           UserModelClass.isSuperAdmin(user) || // Super admins can access all
           (Boolean(user?.isBroker) && plan.brokerId === userId); // Broker's own plans
  }

  /**
   * Check if user can upload documents to plan
   */
  static canUserUploadDocuments(plan: PlanDataInterface, userId: string, user: any) {
    return (plan.isTemplate && UserModelClass.isSuperAdmin(user)) || // Only super admins can upload to templates
           UserModelClass.isSuperAdmin(user) || // Super admins can upload to all plans
           (user.isBroker && plan.brokerId === userId); // Brokers can upload to their own plans
  }

  /**
   * Update plan with validation
   */
  static async updatePlan(planId: string, updateData: any, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanWithAccess(planId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, plan: null };
      }

      const existingPlan = accessCheck.plan!;

      // Check update permissions
      if (!this.canUserUpdatePlan(existingPlan, userId, user)) {
        return { success: false, error: 'Access denied to update this plan', plan: null };
      }

      // Check if plan can be edited (≤1 assignment references it)
      const editCheck = await PlanModelClass.canEditPlan(planId);
      if (!editCheck.canEdit) {
        return {
          success: false,
          error: `Cannot edit plan with multiple assignment references. Reference count: ${editCheck.referenceCount}`,
          plan: null
        };
      }

      // Validate update data
      const errors: string[] = [];

      // Broker-specific uniqueness validation for updates (skip for templates)
      if (existingPlan.brokerId && !existingPlan.isTemplate) {
        if (updateData.planName) {
          const nameValidation = await PlanModelClass.validateBrokerUniquePlanName(
            existingPlan.brokerId,
            updateData.planName,
            planId
          );
          if (!nameValidation.isUnique) {
            errors.push(nameValidation.message);
          }
        }

        if (updateData.planCode) {
          const codeValidation = await PlanModelClass.validateBrokerUniquePlanCode(
            existingPlan.brokerId,
            updateData.planCode,
            planId
          );
          if (!codeValidation.isUnique) {
            errors.push(codeValidation.message);
          }
        }
      }

      // Validate coverage subtypes if provided
      if (updateData.coverageSubTypes) {
        for (const subType of updateData.coverageSubTypes) {
          if (!PRE_ENROLLMENT_COVERAGE_SUBTYPES.includes(subType)) {
            errors.push(`Invalid coverage subtype: ${subType}`);
          }
          if (!isValidPreEnrollmentCombination(existingPlan.coverageType, subType)) {
            errors.push(`Invalid combination: ${subType} is not valid for ${existingPlan.coverageType}`);
          }
        }
      }

      // Validate plan type if provided
      if (updateData.planType && !PLAN_TYPES.includes(updateData.planType)) {
        errors.push('Invalid plan type');
      }

      // Validate metal tier if provided
      if (updateData.metalTier && !METAL_TIERS.includes(updateData.metalTier)) {
        errors.push('Invalid metal tier');
      }

      // Validate carrier if provided and changed
      if (updateData.carrierId && updateData.carrierId !== existingPlan.carrierId) {
        if (!isValidObjectId(updateData.carrierId)) {
          errors.push('Invalid carrier ID format');
        } else {
          const carrier = await CarrierModelClass.getDataById(updateData.carrierId, Boolean(user?.isBroker) ? userId : undefined);
          if (!carrier) {
            errors.push('Carrier not found or access denied');
          } else if (carrier.status !== CARRIER_STATUSES[0]) { // 'Active'
            errors.push(`Cannot assign carrier with status "${carrier.status}". Only Active carriers can be assigned to plans.`);
          } else {
            // Validate carrier compatibility
            const carrierValidation = await CarrierModelClass.validatePlanCarrierCompatibility(
              updateData.carrierId,
              updateData.planType || existingPlan.planType,
              existingPlan.coverageType,
              updateData.coverageSubTypes || existingPlan.coverageSubTypes
            );
            if (!carrierValidation.isCompatible) {
              errors.push('Carrier compatibility validation failed: ' + carrierValidation.errors.join(', '));
            }
          }
        }
      }

      if (errors.length > 0) {
        return { success: false, error: errors.join(', '), plan: null };
      }

      // Build update object
      const updateObject: UpdateablePlanDataInterface = {};
      if (updateData.planName !== undefined) updateObject.planName = updateData.planName;
      if (updateData.planCode !== undefined) updateObject.planCode = updateData.planCode;
      if (updateData.description !== undefined) updateObject.description = updateData.description;
      if (updateData.highlights !== undefined) updateObject.highlights = updateData.highlights;
      if (updateData.informativeLinks !== undefined) updateObject.informativeLinks = updateData.informativeLinks;
      if (updateData.benefitDetails !== undefined) updateObject.benefitDetails = updateData.benefitDetails;
      if (updateData.planType !== undefined) updateObject.planType = updateData.planType;
      if (updateData.metalTier !== undefined) updateObject.metalTier = updateData.metalTier;
      if (updateData.carrierId !== undefined) updateObject.carrierId = updateData.carrierId;
      if (updateData.carrierPlanId !== undefined) updateObject.carrierPlanId = updateData.carrierPlanId;
      if (updateData.coverageSubTypes !== undefined) updateObject.coverageSubTypes = updateData.coverageSubTypes;

      const updateResult = await PlanModelClass.updateData({ id: planId, data: updateObject });
      if (updateResult.modifiedCount === 0) {
        return { success: false, error: 'Failed to update plan', plan: null };
      }

      // Get updated plan
      const updatedPlan = await PlanModelClass.getDataById(planId);
      return { success: true, error: null, plan: updatedPlan };

    } catch (error) {
      logger.error('Error in updatePlan service:', error);
      return { success: false, error: 'Internal server error during plan update', plan: null };
    }
  }

  /**
   * Modify plan status (activate, convert to draft, deactivate, archive)
   */
  static async modifyPlanStatus(planId: string, action: 'activate' | 'convert-to-draft' | 'deactivate' | 'archive', userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanWithAccess(planId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, plan: null };
      }

      const existingPlan = accessCheck.plan!;

      // Check status management permissions
      if (!this.canUserManagePlanStatus(existingPlan, userId, user)) {
        return { success: false, error: `Access denied to ${action} this plan`, plan: null };
      }

      let result;
      switch (action) {
        case 'activate':
          result = await PlanModelClass.activatePlan(planId, Boolean(user?.isBroker) ? userId : undefined);
          break;
        case 'convert-to-draft':
          result = await PlanModelClass.convertToDraft(planId, Boolean(user?.isBroker) ? userId : undefined);
          break;
        case 'deactivate':
          result = await PlanModelClass.deactivatePlan(planId, Boolean(user?.isBroker) ? userId : undefined);
          break;
        case 'archive':
          result = await PlanModelClass.archivePlan(planId, Boolean(user?.isBroker) ? userId : undefined);
          break;
        default:
          return { success: false, error: 'Invalid action', plan: null };
      }

      if (!result.success) {
        return { success: false, error: result.message, plan: null };
      }

      return { success: true, error: null, plan: result.plan };

    } catch (error) {
      logger.error(`Error in modifyPlanStatus service (${action}):`, error);
      return { success: false, error: `Internal server error during plan ${action}`, plan: null };
    }
  }

  /**
   * Duplicate plan
   */
  static async duplicatePlan(planId: string, duplicateData: any, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanWithAccess(planId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, plan: null };
      }

      const sourcePlan = accessCheck.plan!;

      // Check access to source plan
      if (!this.canUserDuplicatePlan(sourcePlan, userId, user)) {
        return { success: false, error: 'Access denied to duplicate this plan', plan: null };
      }

      // Only super admins and brokers can duplicate plans
      if (!UserModelClass.isSuperAdmin(user) && !Boolean(user?.isBroker)) {
        return { success: false, error: 'Only super admins and brokers can duplicate plans', plan: null };
      }

      if (!duplicateData.planName) {
        return { success: false, error: 'Plan name is required for duplication', plan: null };
      }

      // Create new plan data for duplication
      let newBrokerId: string | undefined;
      let newBrokerageId: string | undefined;
      let newIsTemplate = false;

      if (UserModelClass.isSuperAdmin(user)) {
        // Super admins can choose to create templates or regular plans
        newIsTemplate = true; // Super admin duplications become templates
        newBrokerId = userId; // Super admins are also brokers
        newBrokerageId = user.companyId;
      } else if (Boolean(user?.isBroker)) {
        // Brokers always create broker-owned plans when duplicating
        newIsTemplate = false;
        newBrokerId = userId;
        newBrokerageId = user.companyId;
      }

      // Broker-specific uniqueness validation for duplicated plans (skip for templates)
      if (newBrokerId && !newIsTemplate) {
        const nameValidation = await PlanModelClass.validateBrokerUniquePlanName(newBrokerId, duplicateData.planName);
        if (!nameValidation.isUnique) {
          return { success: false, error: nameValidation.message, plan: null };
        }

        if (duplicateData.planCode) {
          const codeValidation = await PlanModelClass.validateBrokerUniquePlanCode(newBrokerId, duplicateData.planCode);
          if (!codeValidation.isUnique) {
            return { success: false, error: codeValidation.message, plan: null };
          }
        }
      }

      const newPlanData: Partial<PlanDataInterface> = {
        planName: duplicateData.planName,
        planCode: duplicateData.planCode,
        brokerId: newBrokerId,
        brokerageId: newBrokerageId,
        isTemplate: newIsTemplate,
        status: PLAN_STATUSES[1], // 'Active'
        isActivated: false,
        documentIds: [] // Start with empty documents
      };

      const duplicatedPlan = await PlanModelClass.clonePlan({
        sourceId: planId,
        newData: newPlanData
      });

      if (!duplicatedPlan) {
        return { success: false, error: 'Failed to duplicate plan', plan: null };
      }

      return { success: true, error: null, plan: duplicatedPlan };

    } catch (error) {
      logger.error('Error in duplicatePlan service:', error);
      return { success: false, error: 'Internal server error during plan duplication', plan: null };
    }
  }

  /**
   * Upload documents to plan
   */
  static async uploadDocuments(planId: string, files: any[], userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanWithAccess(planId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, documents: null };
      }

      const existingPlan = accessCheck.plan!;

      // Check upload permissions
      if (!this.canUserUploadDocuments(existingPlan, userId, user)) {
        return { success: false, error: 'Access denied to upload documents to this plan', documents: null };
      }

      // Create Azure container for plan documents
      const containerName = AzureNamespaceService.getPlanContainer(planId);
      const containerExists = await AzureBlobService.containerExists(containerName);

      if (!containerExists) {
        await AzureBlobService.createContainer(containerName);
      }

      const uploadedDocuments = [];
      const documentIds = [];

      for (const file of files) {
        const fileExtension = file.originalname.split('.').pop() || '';
        const fileNameWithoutExtension = file.originalname.replace(/\.[^/.]+$/, '');
        const documentId = `${planId}-${Date.now()}-${uuidv4()}_____${fileNameWithoutExtension}.${fileExtension}`;

        const blobUrl = await AzureBlobService.upload(
          containerName,
          documentId,
          file.buffer,
          file.mimetype
        );

        uploadedDocuments.push({
          documentId,
          fileName: file.originalname,
          blobUrl,
          fileSize: file.size,
          mimeType: file.mimetype
        });
        documentIds.push(documentId);
      }

      // Update plan with new document IDs
      const currentDocumentIds = existingPlan.documentIds || [];
      const updatedDocumentIds = [...currentDocumentIds, ...documentIds];

      const updateResult = await PlanModelClass.updateDocuments({
        planId,
        documentIds: updatedDocumentIds
      });

      if (updateResult.modifiedCount === 0) {
        return { success: false, error: 'Failed to update plan with document references', documents: null };
      }

      return {
        success: true,
        error: null,
        documents: {
          uploadedDocuments,
          totalDocuments: updatedDocumentIds.length
        }
      };

    } catch (error) {
      logger.error('Error in uploadDocuments service:', error);
      return { success: false, error: 'Internal server error during document upload', documents: null };
    }
  }

  /**
   * Check if plan can be edited
   */
  static async canEditPlan(planId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanWithAccess(planId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, result: null };
      }

      const plan = accessCheck.plan!;

      // Check permissions
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        return { success: false, error: 'Only super admins and brokers can check edit permissions', result: null };
      }

      // Check if plan can be edited
      const editCheck = await PlanModelClass.canEditPlan(planId);

      const result = {
        planId,
        planName: plan.planName,
        canEdit: editCheck.canEdit,
        referenceCount: editCheck.referenceCount,
        referencedBy: editCheck.referencedBy,
        message: editCheck.canEdit
          ? 'Plan can be edited'
          : `Plan cannot be edited. It is referenced by ${editCheck.referenceCount} assignment(s).`
      };

      return { success: true, error: null, result };

    } catch (error) {
      logger.error('Error in canEditPlan service:', error);
      return { success: false, error: 'Internal server error during edit check', result: null };
    }
  }

  /**
   * Check if plan can be deleted
   */
  static async canDeletePlan(planId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanWithAccess(planId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, result: null };
      }

      const plan = accessCheck.plan!;

      // Check permissions
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        return { success: false, error: 'Only super admins and brokers can check delete permissions', result: null };
      }

      // Check if plan can be deleted
      const deleteCheck = await PlanModelClass.canDeletePlan(planId);

      const result = {
        planId,
        planName: plan.planName,
        canDelete: deleteCheck.canDelete,
        referenceCount: deleteCheck.referenceCount,
        referencedBy: deleteCheck.referencedBy,
        message: deleteCheck.canDelete
          ? 'Plan can be deleted'
          : `Plan cannot be deleted. It is referenced by ${deleteCheck.referenceCount} assignment(s).`
      };

      return { success: true, error: null, result };

    } catch (error) {
      logger.error('Error in canDeletePlan service:', error);
      return { success: false, error: 'Internal server error during delete check', result: null };
    }
  }

  /**
   * Get dependent assignments for plan
   */
  static async getDependentAssignments(planId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanWithAccess(planId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, result: null };
      }

      const plan = accessCheck.plan!;

      // Check permissions
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        return { success: false, error: 'Only super admins and brokers can view dependent assignments', result: null };
      }

      // Get assignments that reference this plan
      const dependentAssignments = await PlanModelClass.getAssignmentsReferencingPlan(planId);

      const result = {
        planId,
        planName: plan.planName,
        dependentAssignments,
        count: dependentAssignments.length
      };

      return { success: true, error: null, result };

    } catch (error) {
      logger.error('Error in getDependentAssignments service:', error);
      return { success: false, error: 'Internal server error during dependent assignments check', result: null };
    }
  }

  /**
   * Delete plan with validation
   */
  static async deletePlan(planId: string, userId: string, user: any) {
    try {
      // Check delete permissions
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        return { success: false, error: 'Only super admins and brokers can delete plans' };
      }

      // Verify plan exists and user has access
      let existingPlan: PlanDataInterface | null = null;

      if (UserModelClass.isSuperAdmin(user)) {
        // Super admins can delete any plan
        existingPlan = await PlanModelClass.getDataById(planId);
      } else if (user.isBroker) {
        // Brokers can only delete their own plans
        existingPlan = await PlanModelClass.getDataById(planId);
        if (existingPlan && existingPlan.brokerId !== userId) {
          return { success: false, error: 'Access denied to delete this plan' };
        }
      }

      if (!existingPlan) {
        return { success: false, error: 'Plan not found or access denied' };
      }

      // Check if plan can be deleted (no assignments reference it)
      const deleteCheck = await PlanModelClass.canDeletePlan(planId);
      if (!deleteCheck.canDelete) {
        return {
          success: false,
          error: `Cannot delete plan with assignment references. Reference count: ${deleteCheck.referenceCount}. Use archive API instead.`,
          suggestion: 'Use POST /api/pre-enrollment/plans/:planId/archive to safely archive this plan'
        };
      }

      // Plan has no references - perform hard delete (includes Azure cleanup)
      await PlanModelClass.hardDeleteData(planId);
      logger.info(`Plan hard deleted successfully: ${planId} (no assignment references)`);

      return {
        success: true,
        error: null,
        deletionType: 'hard',
        message: 'Plan permanently deleted (no assignment references found)'
      };

    } catch (error) {
      logger.error('Error in deletePlan service:', error);
      return { success: false, error: 'Internal server error during plan deletion' };
    }
  }
}
