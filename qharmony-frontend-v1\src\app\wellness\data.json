{"wellness_questions": [{"id": "gender", "text": "What is your gender?", "type": "categorical", "options": ["Male", "Female"], "feature": "Sex", "weights": null}, {"id": "bmi", "text": "What is your BMI?", "type": "numeric", "options": null, "feature": "BMI", "weights": null}, {"id": "height", "text": "What is your height in (cm)?", "type": "numeric", "options": null, "feature": null, "weights": null}, {"id": "weight", "text": "What is your weight in (kg)?", "type": "numeric", "options": null, "feature": null, "weights": null}, {"id": "age", "text": "What is your current age?", "type": "numeric", "options": null, "feature": "Age", "weights": null}, {"id": "race", "text": "What is your race?", "type": "categorical", "options": ["Black", "White", "Other", "American Indian/Alaskan Native", "Asian", "Hispanic", "White only, Non-Hispanic", "Black only, Non-Hispanic", "Multiracial, Non-Hispanic", "Other race only, Non-Hispanic"], "feature": "Race", "weights": null}, {"id": "general_health", "text": "What is your general health?", "type": "ordinal", "options": ["Poor", "Fair", "Good", "Very good", "Excellent"], "feature": "GenHealth", "weights": null}, {"id": "diabetic", "text": "Are you diabetic?", "type": "categorical", "options": ["No", "Yes", "No, borderline diabetes", "Yes (during pregnancy)", "Yes, but only during pregnancy (female)", "No, pre-diabetes or borderline diabetes"], "feature": "Diabetic", "weights": -2.5}, {"id": "asthma", "text": "Do you have asthma?", "type": "boolean", "options": null, "feature": "Asthma", "weights": -1.5}, {"id": "kidney_problems", "text": "Do you have any kidney problems?", "type": "boolean", "options": null, "feature": null, "weights": -2.0}, {"id": "smoking", "text": "How often have you smoked in the past two years?", "type": "categorical", "options": ["Rarely", "Sometimes", "Often"], "feature": "Smoking", "weights": {"Rarely": 0.0, "Sometimes": -2.0, "Often": -5.0}}, {"id": "alcohol", "text": "How often have you consumed alcohol in the past two years?", "type": "categorical", "options": ["Rarely", "Sometimes", "Often"], "feature": "AlcoholDrinking", "weights": {"Rarely": 0.0, "Sometimes": -1.0, "Often": -3.0}}, {"id": "physical_activity", "text": "How often have you been engaging in physical activities in the past year?", "type": "categorical", "options": ["Rarely", "Sometimes", "Often"], "feature": "PhysicalActivity", "weights": {"Rarely": -3.0, "Sometimes": 1.0, "Often": 5.0}}, {"id": "sleep_hours", "text": "How many hours do you sleep?", "type": "numeric", "options": null, "feature": "SleepTime", "weights": {"<5": -3.0, "5-7": 0.0, "7-9": 2.0, ">9": -1.0}}, {"id": "walking", "text": "How often do you walk?", "type": "categorical", "options": ["Rarely", "Sometimes", "Often"], "feature": "<PERSON><PERSON><PERSON>alk<PERSON>", "weights": {"Rarely": -2.0, "Sometimes": 1.0, "Often": 3.0}}, {"id": "stress", "text": "Do you often take stress?", "type": "boolean", "options": null, "feature": null, "weights": {"Yes": -2.0, "No": 1.0}}, {"id": "social_life", "text": "Do you like engaging in social gatherings?", "type": "boolean", "options": null, "feature": null, "weights": {"Yes": -1.0, "No": 0.0}}, {"id": "healthy_food", "text": "How often do you include fruits and vegetables in your meal without using processed food?", "type": "categorical", "options": ["Rarely", "Sometimes", "Often"], "feature": null, "weights": {"Rarely": -3.0, "Sometimes": 1.0, "Often": 4.0}}, {"id": "cardio", "text": "How often do you have cardio?", "type": "categorical", "options": ["Rarely", "Sometimes", "Often"], "feature": null, "weights": {"Rarely": -3.0, "Sometimes": 1.0, "Often": 3.0}}, {"id": "life_span_grandparents", "text": "Life span of grand parents? (Average would work)", "type": "numeric", "options": null, "feature": null, "weights": null}, {"id": "ever_married", "text": "Are you married or were you ever married?", "type": "boolean", "options": null, "feature": null, "weights": {"Yes": -1.0, "No": 0.0}}, {"id": "work_type", "text": "What is your work type?", "type": "categorical", "options": ["Self-employed", "Govt_job", "Private"], "feature": null, "weights": null}, {"id": "residence_type", "text": "What is your residence type?", "type": "categorical", "options": ["Rural", "Urban"], "feature": null, "weights": null}, {"id": "avg_glucose_level", "text": "What is your average glucose level?", "type": "numeric", "options": null, "feature": null, "weights": null}]}