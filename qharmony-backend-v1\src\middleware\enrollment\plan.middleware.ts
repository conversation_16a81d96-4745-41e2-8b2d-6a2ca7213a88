import express from 'express';
import logger from '../../utils/logger';
import { isValidObjectId } from '../../utils/validation';

/**
 * Plan Middleware
 * Modular middleware for plan operations
 * Provides consistent validation, authentication, and response handling
 */
export class PlanMiddleware {

  /**
   * Basic middleware stack for plan operations
   */
  static forBasicOperation() {
    return [
      this.validateUserId,
      this.validateObjectIds
    ];
  }

  /**
   * Middleware stack for plan creation
   */
  static forPlanCreation() {
    return [
      this.validateUserId,
      this.validatePlanCreationData
    ];
  }

  /**
   * Middleware stack for plan listing
   */
  static forPlanListing() {
    return [
      this.validateUserId,
      this.validatePaginationParams
    ];
  }

  /**
   * Middleware stack for plan retrieval
   */
  static forPlanRetrieval() {
    return [
      this.validateUserId,
      this.validatePlanId
    ];
  }

  /**
   * Middleware stack for plan update
   */
  static forPlanUpdate() {
    return [
      this.validateUserId,
      this.validatePlanId,
      this.validatePlanUpdateData
    ];
  }

  /**
   * Middleware stack for document upload
   */
  static forDocumentUpload() {
    return [
      this.validateUserId,
      this.validatePlanId,
      this.validateFileUpload
    ];
  }

  // ===== VALIDATION MIDDLEWARE =====

  /**
   * Validate user ID in headers
   */
  private static validateUserId = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const userId = request.headers['user-id'] as string;

    if (!userId) {
      return response.status(401).json({ error: 'User ID is required' });
    }

    // Attach userId to request for easy access
    (request as any).userId = userId;
    next();
  };

  /**
   * Validate plan ID parameter
   */
  private static validatePlanId = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { planId } = request.params;

    if (!planId) {
      return response.status(400).json({ error: 'Plan ID is required' });
    }

    if (!isValidObjectId(planId)) {
      return response.status(400).json({ error: 'Invalid plan ID format' });
    }

    next();
  };

  /**
   * Validate ObjectIds in request
   */
  private static validateObjectIds = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { planId } = request.params;
    const { carrierId } = request.body;

    if (planId && !isValidObjectId(planId)) {
      return response.status(400).json({ error: 'Invalid plan ID format' });
    }

    if (carrierId && !isValidObjectId(carrierId)) {
      return response.status(400).json({ error: 'Invalid carrier ID format' });
    }

    next();
  };

  /**
   * Validate plan creation data
   */
  private static validatePlanCreationData = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { planName, coverageType, coverageSubTypes, description } = request.body;

    if (!planName || !coverageType || !coverageSubTypes || !description) {
      return response.status(400).json({
        error: 'Plan name, coverage type, coverage subtypes, and description are required'
      });
    }

    if (!Array.isArray(coverageSubTypes)) {
      return response.status(400).json({ error: 'Coverage subtypes must be an array' });
    }

    next();
  };

  /**
   * Validate plan update data
   */
  private static validatePlanUpdateData = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { coverageSubTypes } = request.body;

    if (coverageSubTypes && !Array.isArray(coverageSubTypes)) {
      return response.status(400).json({ error: 'Coverage subtypes must be an array' });
    }

    next();
  };

  /**
   * Validate pagination parameters
   */
  private static validatePaginationParams = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { page, limit } = request.query;

    if (page && (isNaN(Number(page)) || Number(page) < 1)) {
      return response.status(400).json({ error: 'Page must be a positive number' });
    }

    if (limit && (isNaN(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
      return response.status(400).json({ error: 'Limit must be a number between 1 and 100' });
    }

    next();
  };

  /**
   * Validate file upload
   */
  private static validateFileUpload = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    if (!request.files || !Array.isArray(request.files) || request.files.length === 0) {
      return response.status(400).json({ error: 'No files were uploaded' });
    }

    next();
  };

  // ===== RESPONSE HANDLERS =====

  /**
   * Handle successful plan creation
   */
  static planCreated(response: express.Response, plan: any) {
    return response.status(201).json({
      message: 'Plan created successfully',
      plan
    });
  }

  /**
   * Handle successful plan retrieval
   */
  static planRetrieved(response: express.Response, plan: any, carrier?: any) {
    const responseData: any = { plan };
    if (carrier) {
      responseData.carrier = carrier;
    }
    return response.status(200).json(responseData);
  }

  /**
   * Handle successful plan listing
   */
  static plansListed(response: express.Response, plans: any[], pagination?: any) {
    const responseData: any = { plans };
    if (pagination) {
      responseData.pagination = pagination;
    } else {
      responseData.count = plans.length;
    }
    return response.status(200).json(responseData);
  }

  /**
   * Handle successful plan update
   */
  static planUpdated(response: express.Response, plan: any) {
    return response.status(200).json({
      message: 'Plan updated successfully',
      plan
    });
  }

  /**
   * Handle successful plan status change
   */
  static planStatusChanged(response: express.Response, action: string, plan?: any) {
    const responseData: any = {
      message: `Plan ${action}d successfully`
    };
    if (plan) {
      responseData.plan = plan;
    }
    return response.status(200).json(responseData);
  }

  /**
   * Handle successful plan duplication
   */
  static planDuplicated(response: express.Response, plan: any) {
    return response.status(201).json({
      message: 'Plan duplicated successfully',
      plan
    });
  }

  /**
   * Handle successful document upload
   */
  static documentsUploaded(response: express.Response, documents: any) {
    return response.status(200).json({
      message: 'Documents uploaded successfully',
      ...documents
    });
  }

  /**
   * Handle successful plan deletion
   */
  static planDeleted(response: express.Response) {
    return response.status(200).json({
      message: 'Plan deleted successfully'
    });
  }

  /**
   * Handle operation results (for validation endpoints)
   */
  static operationResult(response: express.Response, result: any) {
    return response.status(200).json(result);
  }

  /**
   * Handle service errors
   */
  static handleServiceError(response: express.Response, error: string) {
    return response.status(400).json({ error });
  }

  /**
   * Handle internal server errors
   */
  static internalError(response: express.Response) {
    return response.status(500).json({ error: 'Internal server error' });
  }
}
