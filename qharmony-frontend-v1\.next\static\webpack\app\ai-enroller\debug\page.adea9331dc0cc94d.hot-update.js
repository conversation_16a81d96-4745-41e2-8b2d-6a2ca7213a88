"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/debug/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/debug/page.tsx":
/*!********************************************!*\
  !*** ./src/app/ai-enroller/debug/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DebugPage() {\n    _s();\n    const [localStorageData, setLocalStorageData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get all localStorage data\n        const data = {};\n        for(let i = 0; i < localStorage.length; i++){\n            const key = localStorage.key(i);\n            if (key) {\n                data[key] = localStorage.getItem(key) || \"\";\n            }\n        }\n        setLocalStorageData(data);\n    }, []);\n    const clearUserData = ()=>{\n        localStorage.removeItem(\"userid1\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"user\");\n        // Set the ACTUAL user ID that exists in the database\n        localStorage.setItem(\"userid1\", \"67bf65bf50bad0a4b3d805bc\");\n        localStorage.setItem(\"companyId1\", \"67bf65bf50bad0a4b3d805ba\");\n        // Refresh the data\n        const data = {};\n        for(let i = 0; i < localStorage.length; i++){\n            const key = localStorage.key(i);\n            if (key) {\n                data[key] = localStorage.getItem(key) || \"\";\n            }\n        }\n        setLocalStorageData(data);\n        alert(\"User data cleared and reset to DATABASE user ID: 67bf65bf50bad0a4b3d805bc\");\n    };\n    const testDirectAPI = async ()=>{\n        const userId = localStorage.getItem(\"userid1\");\n        const companyId = localStorage.getItem(\"companyId1\");\n        if (!userId || !companyId) {\n            alert(\"Missing userId or companyId in localStorage\");\n            return;\n        }\n        try {\n            var _result_assignments;\n            // Test direct API call\n            const response = await fetch(\"http://localhost:8080/api/pre-enrollment/plan-assignments/company/\".concat(companyId, \"?includePlanData=true\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD0D Direct API Test Result:\", {\n                status: response.status,\n                ok: response.ok,\n                result\n            });\n            alert(\"API Test Result:\\nStatus: \".concat(response.status, \"\\nAssignments: \").concat(((_result_assignments = result.assignments) === null || _result_assignments === void 0 ? void 0 : _result_assignments.length) || 0, \"\\nTotal Count: \").concat(result.totalCount || 0, \"\\nApplied Filters: \").concat(JSON.stringify(result.appliedFilters)));\n        } catch (error) {\n            console.error(\"API Test Error:\", error);\n            alert(\"API Test Error: \".concat(error.message));\n        }\n    };\n    const testUserAPI = async ()=>{\n        const userId = localStorage.getItem(\"userid1\");\n        if (!userId) {\n            alert(\"Missing userId in localStorage\");\n            return;\n        }\n        try {\n            // Test user API to see what user data the backend has\n            const response = await fetch(\"http://localhost:8080/api/users/\".concat(userId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD0D User API Test Result:\", {\n                status: response.status,\n                ok: response.ok,\n                result\n            });\n            if (response.ok) {\n                alert(\"User API Result:\\nName: \".concat(result.name, \"\\nEmail: \").concat(result.email, \"\\nCompany ID: \").concat(result.companyId, \"\\nisBroker: \").concat(result.isBroker, \"\\nisAdmin: \").concat(result.isAdmin));\n            } else {\n                alert(\"User API Error:\\nStatus: \".concat(response.status, \"\\nError: \").concat(JSON.stringify(result)));\n            }\n        } catch (error) {\n            console.error(\"User API Test Error:\", error);\n            alert(\"User API Test Error: \".concat(error.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"monospace\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Debug - LocalStorage Data & API Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\",\n                    display: \"flex\",\n                    gap: \"10px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearUserData,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#007bff\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Clear & Reset User Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: testDirectAPI,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#28a745\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Test Direct API Call\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: testUserAPI,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#ffc107\",\n                            color: \"black\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Test User API\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Current LocalStorage Contents:\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                style: {\n                    backgroundColor: \"#f5f5f5\",\n                    padding: \"10px\",\n                    borderRadius: \"4px\"\n                },\n                children: JSON.stringify(localStorageData, null, 2)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Expected User ID:\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Should be: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"6838677aef6db0212bcfdacd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Current User ID from getUserId():\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"userid1: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"userid1\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 18\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 81\n                    }, this),\n                    \"userId: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"userId\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 79\n                    }, this),\n                    \"companyId1: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"companyId1\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugPage, \"aIby2hUaZfOHmhr0X+VFs6100zo=\");\n_c = DebugPage;\nvar _c;\n$RefreshReg$(_c, \"DebugPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/debug/page.tsx\n"));

/***/ })

});