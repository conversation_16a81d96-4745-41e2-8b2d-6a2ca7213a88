class EnrollmentService {
  private apiBaseUrl: string;

  constructor() {
    this.apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add user ID from localStorage for authentication using correct header name
    const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
    if (userId) {
      headers['user-id'] = userId; // Use 'user-id' header as expected by backend
    }

    return headers;
  }

  /**
   * Get plan details by planId
   */
  async getPlanById(planId: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}/api/pre-enrollment/plans/${planId}`,
        {
          method: 'GET',
          headers: this.getHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result.plan
      };
    } catch (error) {
      console.error('Error fetching plan details:', error);
      return {
        success: false,
        error: 'Failed to fetch plan details'
      };
    }
  }

  /**
   * Get plan assignments for a company grouped by coverage subtype
   */
  async getPlanAssignmentsByCompany(companyId: string): Promise<{
    success: boolean;
    data?: Record<string, any[]>;
    error?: string;
  }> {
    try {
      console.log('🔍 Fetching plan assignments for company:', companyId);
      
      // Add query parameters to filter for active assignments only
      const queryParams = new URLSearchParams();
      queryParams.append('includeExpired', 'false'); // Only get active assignments

      const response = await fetch(
        `${this.apiBaseUrl}/api/pre-enrollment/plan-assignments/company/${companyId}?${queryParams.toString()}`,
        {
          method: 'GET',
          headers: this.getHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📋 Plan assignments response:', result);
      
      const assignments = result.assignments || [];
      console.log('📊 Total assignments found:', assignments.length);

      // Filter assignments to only show those in enrollment period
      const now = new Date();
      const enrollmentPeriodAssignments = assignments.filter((assignment: any) => {
        const enrollmentStart = new Date(assignment.enrollmentStartDate);
        const enrollmentEnd = new Date(assignment.enrollmentEndDate);
        const isInEnrollmentPeriod = assignment.isActive &&
                                   enrollmentStart <= now &&
                                   enrollmentEnd >= now;

        if (!isInEnrollmentPeriod) {
          console.log(`⏰ Skipping assignment ${assignment._id} - not in enrollment period:`, {
            enrollmentStart: enrollmentStart.toISOString(),
            enrollmentEnd: enrollmentEnd.toISOString(),
            now: now.toISOString(),
            isActive: assignment.isActive
          });
        }

        return isInEnrollmentPeriod;
      });

      console.log(`📊 Assignments in enrollment period: ${enrollmentPeriodAssignments.length} of ${assignments.length}`);

      // Fetch plan details for each assignment and group by coverage subtype
      const groupedPlans: Record<string, any[]> = {};
      
      for (const assignment of enrollmentPeriodAssignments) {
        console.log('🔍 Processing assignment:', assignment._id, 'planId:', assignment.planId);
        
        // Fetch plan details
        const planResult = await this.getPlanById(assignment.planId);
        
        if (planResult.success && planResult.data) {
          const plan = planResult.data;
          console.log('✅ Plan details:', {
            planName: plan.planName,
            coverageSubTypes: plan.coverageSubTypes
          });
          
          // Create enriched plan object with assignment data
          const enrichedPlan = {
            ...plan,
            assignment: assignment,
            // Map to expected format for plan pages
            id: assignment._id,
            name: plan.planName,
            planCode: plan.planCode,
            carrierName: plan.carrierName,
            coverageType: plan.coverageType,
            coverageSubTypes: plan.coverageSubTypes,
            // Calculate cost from assignment coverage tiers
            cost: this.calculatePlanCost(assignment),
            features: this.extractPlanFeatures(plan, assignment)
          };
          
          // Group by coverage subtypes
          if (plan.coverageSubTypes && Array.isArray(plan.coverageSubTypes)) {
            plan.coverageSubTypes.forEach((subType: string) => {
              const key = subType.toLowerCase();
              if (!groupedPlans[key]) {
                groupedPlans[key] = [];
              }
              groupedPlans[key].push(enrichedPlan);
            });
          }
        } else {
          console.warn('⚠️ Failed to fetch plan details for:', assignment.planId);
        }
      }

      console.log('🎯 Grouped plans by subtype:', Object.keys(groupedPlans));
      
      return {
        success: true,
        data: groupedPlans
      };
    } catch (error) {
      console.error('❌ Error fetching plan assignments:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch plan assignments'
      };
    }
  }

  /**
   * Calculate plan cost from assignment coverage tiers based on selected tier
   */
  calculatePlanCost(assignment: any, selectedCoverageTier?: string): number {
    try {
      if (selectedCoverageTier && assignment.coverageTiers) {
        // Find the specific tier that matches the selection
        const matchingTier = assignment.coverageTiers.find(
          (tier: any) => tier.tierName === selectedCoverageTier
        );

        if (matchingTier) {
          return matchingTier.employeeCost || 0;
        }
      }

      // Fallback: Look for employee-only tier cost
      const employeeOnlyTier = assignment.coverageTiers?.find(
        (tier: any) => tier.tierName?.toLowerCase().includes('employee only') ||
                      tier.tierName?.toLowerCase() === 'employee'
      );

      if (employeeOnlyTier) {
        return employeeOnlyTier.employeeCost || 0;
      }

      // Final fallback to first tier if employee-only not found
      if (assignment.coverageTiers && assignment.coverageTiers.length > 0) {
        return assignment.coverageTiers[0].employeeCost || 0;
      }

      return 0;
    } catch (error) {
      console.warn('Error calculating plan cost:', error);
      return 0;
    }
  }

  /**
   * Extract plan features from plan and assignment data
   */
  extractPlanFeatures(plan: any, assignment: any): string[] {
    const features: string[] = [];

    // First priority: Use plan highlights if available
    if (plan.highlights && Array.isArray(plan.highlights) && plan.highlights.length > 0) {
      return plan.highlights;
    }

    // Second priority: Use assignment highlights if available
    if (assignment.highlights && Array.isArray(assignment.highlights) && assignment.highlights.length > 0) {
      return assignment.highlights;
    }

    // Fallback: Generate features from plan data
    if (plan.metalTier) {
      features.push(`${plan.metalTier} tier coverage`);
    }

    if (plan.carrierName) {
      features.push(`${plan.carrierName} network`);
    }

    // Add coverage tier info
    if (assignment.coverageTiers && assignment.coverageTiers.length > 0) {
      features.push(`${assignment.coverageTiers.length} coverage tier options`);
    }

    // Add plan type specific features
    if (plan.coverageType) {
      features.push(`${plan.coverageType} benefits`);
    }

    return features.length > 0 ? features : ['Comprehensive coverage', 'Network benefits', 'Quality care'];
  }
}

// Export singleton instance
const enrollmentService = new EnrollmentService();
export default enrollmentService;
