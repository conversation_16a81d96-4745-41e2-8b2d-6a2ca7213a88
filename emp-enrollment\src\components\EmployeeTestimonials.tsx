
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Star, ChevronLeft, ChevronRight } from 'lucide-react';

const testimonials = [
  {
    name: "<PERSON>",
    department: "Marketing",
    rating: 5,
    text: "The HSA plan saved me $3,000 last year alone! The tax benefits are incredible.",
    highlight: "Saved $3,000"
  },
  {
    name: "<PERSON>",
    department: "Engineering",
    rating: 5,
    text: "Having dental coverage meant my crown only cost $200 instead of $1,200. Worth every penny!",
    highlight: "Saved $1,000"
  },
  {
    name: "<PERSON>",
    department: "HR",
    rating: 5,
    text: "The vision plan paid for itself with just one pair of glasses. Eye exams caught my diabetes early!",
    highlight: "Early Detection"
  },
  {
    name: "<PERSON>",
    department: "Finance",
    rating: 5,
    text: "Pet insurance saved us $4,500 when our dog needed emergency surgery. Best decision ever!",
    highlight: "Saved $4,500"
  }
];

export const EmployeeTestimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial(prev => (prev + 1) % testimonials.length);
    }, 6000);
    return () => clearInterval(timer);
  }, []);

  const nextTestimonial = () => {
    setCurrentTestimonial(prev => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial(prev => prev === 0 ? testimonials.length - 1 : prev - 1);
  };

  const current = testimonials[currentTestimonial];

  return (
    <Card className="bg-gradient-to-r from-amber-50 to-yellow-100 dark:from-amber-950 dark:to-yellow-900 border-amber-200 dark:border-amber-800">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
            ⭐ Employee Success Story
          </Badge>
          <div className="flex gap-1">
            <button onClick={prevTestimonial} className="p-1 hover:bg-amber-100 dark:hover:bg-amber-900 rounded">
              <ChevronLeft className="w-4 h-4" />
            </button>
            <button onClick={nextTestimonial} className="p-1 hover:bg-amber-100 dark:hover:bg-amber-900 rounded">
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        <div className="flex items-start gap-3">
          <Avatar className="w-10 h-10">
            <AvatarFallback className="bg-amber-200 text-amber-800">
              {current.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium">{current.name}</span>
              <span className="text-sm text-muted-foreground">{current.department}</span>
              <div className="flex gap-0.5">
                {[...Array(current.rating)].map((_, i) => (
                  <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
            </div>
            
            <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
              "{current.text}"
            </p>
            
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">
              💰 {current.highlight}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
