'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function AuthTestPage() {
  const router = useRouter();
  const [authStatus, setAuthStatus] = useState<{
    hasUserId: boolean;
    userId?: string;
  }>({ hasUserId: false });

  useEffect(() => {
    // Check localStorage for user ID
    const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
    setAuthStatus({
      hasUserId: !!userId,
      userId: userId || undefined
    });
  }, []);

  const setTestUserId = () => {
    localStorage.setItem('userid1', '6838677aef6db0212bcfdacd');
    setAuthStatus({
      hasUserId: true,
      userId: '6838677aef6db0212bcfdacd'
    });
  };

  const clearUserId = () => {
    localStorage.removeItem('userid1');
    localStorage.removeItem('userId');
    setAuthStatus({
      hasUserId: false
    });
  };

  const testAIEnrollerAccess = () => {
    router.push('/ai-enroller');
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🔐 Authentication Test Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>Current Authentication Status</h2>
        <div style={{ 
          background: authStatus.hasUserId ? '#d4edda' : '#f8d7da', 
          padding: '15px', 
          borderRadius: '5px',
          border: `1px solid ${authStatus.hasUserId ? '#c3e6cb' : '#f5c6cb'}`
        }}>
          {authStatus.hasUserId ? (
            <>
              <div><strong>✅ Authenticated</strong></div>
              <div><strong>User ID:</strong> {authStatus.userId}</div>
              <div style={{ marginTop: '10px', fontSize: '14px', color: '#155724' }}>
                You should be able to access AI Enroller pages
              </div>
            </>
          ) : (
            <>
              <div><strong>❌ Not Authenticated</strong></div>
              <div style={{ marginTop: '10px', fontSize: '14px', color: '#721c24' }}>
                AI Enroller pages should redirect you to home
              </div>
            </>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Test Actions</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={setTestUserId}
            style={{ 
              padding: '10px 20px', 
              background: '#007bff', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Set Test User ID
          </button>
          
          <button 
            onClick={clearUserId}
            style={{ 
              padding: '10px 20px', 
              background: '#dc3545', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Clear User ID
          </button>

          <button 
            onClick={testAIEnrollerAccess}
            style={{ 
              padding: '10px 20px', 
              background: '#28a745', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Test AI Enroller Access
          </button>
        </div>
      </div>

      <div style={{ marginTop: '30px', padding: '20px', background: '#e9ecef', borderRadius: '5px' }}>
        <h3>💡 Expected Behavior</h3>
        <div style={{ marginBottom: '15px' }}>
          <h4>✅ With Authentication (User ID set):</h4>
          <ul>
            <li>Clicking "Test AI Enroller Access" should take you to AI Enroller</li>
            <li>You should see the AI Enroller dashboard</li>
            <li>All AI Enroller features should work normally</li>
          </ul>
        </div>
        
        <div>
          <h4>❌ Without Authentication (No User ID):</h4>
          <ul>
            <li>Clicking "Test AI Enroller Access" should redirect you to home page</li>
            <li>You should see a loading spinner briefly, then get redirected</li>
            <li>No access to AI Enroller features</li>
          </ul>
        </div>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', background: '#fff3cd', borderRadius: '5px', border: '1px solid #ffeaa7' }}>
        <h4>🧪 Test Steps:</h4>
        <ol>
          <li><strong>Clear User ID</strong> → Click "Test AI Enroller Access" → Should redirect to home</li>
          <li><strong>Set Test User ID</strong> → Click "Test AI Enroller Access" → Should access AI Enroller</li>
          <li><strong>Verify</strong> that AI Enroller now requires proper authentication</li>
        </ol>
      </div>
    </div>
  );
}
