'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { WellnessResult } from '@/services/wellness.service';
import './wellness2.css';

// Demo data to use when no results are available
const demoResults: WellnessResult = {
  predictions: {
    life_expectancy: {
      predicted_baseline_age: 85.7,
      additional_adjustment: 2.3,
      final_adjusted_age: 88.0,
      survival_probability_past_100: 0.15,
      message: "Congratulations, you are expected to live around this age."
    },
    heart_disease_probability: 12.5,
    stroke_prediction: {
      stroke_probability: 3.2,
      stroke: "No"
    }
  },
  recommendations: [
    "Maintain a balanced diet rich in fruits, vegetables, and whole grains.",
    "Aim for at least 150 minutes of moderate exercise per week.",
    "Practice stress reduction techniques like meditation or deep breathing.",
    "Ensure you get 7-8 hours of quality sleep each night.",
    "Schedule regular check-ups with your healthcare provider."
  ],
  sources: [
    "https://api.benosphere.com/benefits/document?objectKey=sample-dental-benefits.pdf&companyId=demo",
    "https://api.benosphere.com/benefits/document?objectKey=sample-health-insurance.pdf&companyId=demo"
  ]
};

export default function LongevityResults() {
  const router = useRouter();
  const [results, setResults] = useState<WellnessResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [isDemo, setIsDemo] = useState(false);

  useEffect(() => {
    // Load results from localStorage
    const savedResults = localStorage.getItem('wellness_results');
    if (savedResults) {
      setResults(JSON.parse(savedResults));
      setIsDemo(false);
    } else {
      // Use demo data if no results are available
      setResults(demoResults);
      setIsDemo(true);
    }
    setLoading(false);
  }, []);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href);
    alert('Link copied! Challenge your friends to check their longevity too!');
  };

  const handleReminder = () => {
    alert('We’ll remind you! (Coming soon)');
  };

  const handleDisclaimer = () => {
    alert(`This app provides an estimate of potential lifespan based on general statistical models and user-provided data.
    
It is not a medical diagnosis, prediction of death, or guarantee of future outcomes.

Consult a healthcare professional for accurate health assessments. We are not liable for decisions made based on this tool.`);
  };

  const handleRestart = () => {
    localStorage.removeItem('wellness_results');
    router.push('/wellness');
  };

  if (loading) {
    return <div className="loading">Loading results...</div>;
  }

  if (!results) {
    return (
      <div className="wellness-body">
        <div className="container error-container">
          <h2>No Results Found</h2>
          <p>We couldn&apos;t find your wellness assessment results.</p>
          <button className="next-btn" onClick={handleRestart}>
            Take Assessment
          </button>
        </div>
      </div>
    );
  }

  const { predictions, recommendations, sources } = results;
  const lifeExpectancy = predictions.life_expectancy.final_adjusted_age.toFixed(1);
  const heartRisk = predictions.heart_disease_probability.toFixed(1);
  const strokeRisk = predictions.stroke_prediction.stroke_probability.toFixed(1);
  const survivalPast100 = (predictions.life_expectancy.survival_probability_past_100 * 100).toFixed(2);

  return (
    <div className="wellness-body">
      {isDemo && (
        <div className="demo-banner">
          <p>Viewing demo results. <button onClick={handleRestart}>Take the assessment</button> to get personalized results.</p>
        </div>
      )}
      
      <nav className="wellness-nav">
        <a href="#longevity">Longevity</a>
        <a href="#health">Health Risks</a>
        <a href="#recommendations">Recommendations</a>
        <a href="#benefits">Benefits</a>
      </nav>

      <section id="longevity" className="section container">
        <h2>Your Longevity Report 🎉</h2>
        <p className="highlight">
          Expected Longevity: {lifeExpectancy} years
          <a href="#" onClick={e => { e.preventDefault(); handleDisclaimer(); }}>
            (disclaimer)
          </a>
        </p>
        <p>Based on the data provided, you&apos;re likely to live around this age.</p>
        <p><strong>Chance of Living Past 100:</strong> {survivalPast100}%</p>
        <div className="centered">
          <button className="share-button" onClick={handleCopyLink}>Challenge My Friends 💪</button>
          <button className="reminder-button" onClick={handleReminder}>Send Me Reminders ⏰</button>
        </div>
        <div className="centered faces">
          <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="User 1" />
          <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="User 2" />
          <img src="https://randomuser.me/api/portraits/men/35.jpg" alt="User 3" />
          <p>You&apos;re in the top 25% of healthiest users!</p>
        </div>
      </section>

      <section id="health" className="section container">
        <h2>Health Risk Overview 🩺</h2>
        <p><strong>Heart Disease Risk:</strong> {heartRisk}%</p>
        <p><strong>Stroke Risk:</strong> {parseFloat(strokeRisk) < 1 ? 'Very Low' : 'Moderate'} ({strokeRisk}%)</p>
        <p><strong>Stroke Prediction:</strong> {predictions.stroke_prediction.stroke}</p>
      </section>

      <section id="recommendations" className="section container">
        <h2>Personalized Health Recommendations 💡</h2>
        {recommendations.map((recommendation, index) => (
          <p key={index}>{recommendation}</p>
        ))}
        <div className="centered">
          <button className="reminder-button" onClick={handleReminder}>Send Me Reminders ⏰</button>
        </div>
      </section>

      <section id="benefits" className="section container">
        <h2>Benefits + Resources 📄</h2>
        <div className="benefits-container">
          {sources.length > 0 ? (
            sources.map((source, index) => (
              <a 
                key={index}
                href={source} 
                target="_blank" 
                rel="noopener noreferrer"
                className="benefit-link"
              >
                Benefit Document {index + 1}
              </a>
            ))
          ) : (
            <div className="no-benefits-message">
              No specific benefit documents found for your health profile.
            </div>
          )}
        </div>
      </section>
          <div className='centered'>
            <button className="share-button" onClick={handleRestart}>
            Retake Assessment
            </button>
          </div>
      

      <footer className="wellness-footer">
        <div className="disclaimer container">
        <p>⚠️ <strong>Disclaimer:</strong> This is not a medical diagnosis. It&apos;s a statistical estimate only.</p>
        <p>🩺 Consult a healthcare professional for accurate health assessments.</p>
        <p><em>This is an estimate, not a prediction—talk to your doctor!</em></p>
        </div>
        <p>&copy; 2025 BenOsphere. All rights reserved.</p>
      </footer>
    </div>
  );
}
