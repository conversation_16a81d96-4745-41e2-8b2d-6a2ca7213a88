'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { User, Heart, Stethoscope, Play, BarChart3, CheckCircle } from 'lucide-react';
import { FloatingHelp } from './FloatingHelp';
import { VideoPlayer } from './VideoPlayer';
import { CompareModal } from './CompareModal';

interface MedicalPlanPageProps {
  userProfile: any;
  onPlanSelect: (planData: any) => void;
  recommendation: any;
}

export const MedicalPlanPage: React.FC<MedicalPlanPageProps> = ({
  userProfile,
  onPlanSelect,
  recommendation
}) => {
  const [isSelected, setIsSelected] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [showCompare, setShowCompare] = useState(false);

  const handleSelectPlan = () => {
    setIsSelected(!isSelected);
    onPlanSelect(recommendation.plan);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0
          }}>
            🎯 Based on your answers, here&apos;s my smart medical plan recommendation!
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0'
          }}>
            I analyzed your healthcare needs, budget preferences, and family situation to find the perfect match.
          </p>
        </div>
      </div>

      {/* Medical Plan Recommendation */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '24px' }}>
          <div style={{
            width: '24px',
            height: '24px',
            backgroundColor: '#fee2e2',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Heart style={{ width: '16px', height: '16px', color: '#dc2626' }} />
          </div>
          <div style={{
            width: '24px',
            height: '24px',
            backgroundColor: '#fee2e2',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Stethoscope style={{ width: '16px', height: '16px', color: '#dc2626' }} />
          </div>
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            Smart Medical Plan Recommendation
          </h2>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {/* Why we recommend this */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontSize: '18px' }}>💡</span>
              <h3 style={{ fontWeight: '600', color: '#111827', margin: 0 }}>Why we recommend this:</h3>
            </div>
            <p style={{
              color: '#1d4ed8',
              lineHeight: '1.6',
              margin: 0
            }}>
              {recommendation.reason}
            </p>
          </div>

          {/* Plan Card */}
          <div style={{
            border: '2px solid #bfdbfe',
            borderRadius: '8px',
            padding: '24px',
            backgroundColor: '#eff6ff'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '16px' }}>
              <div>
                <h3 style={{
                  fontSize: '20px',
                  fontWeight: '700',
                  color: '#111827',
                  marginBottom: '8px',
                  margin: 0
                }}>
                  {recommendation.plan.name}
                </h3>
                <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '8px' }}>
                  <span style={{
                    fontSize: '24px',
                    fontWeight: '700',
                    color: '#111827'
                  }}>
                    ${recommendation.plan.cost}
                  </span>
                  <span style={{ color: '#6b7280' }}>/paycheck</span>
                </div>
              </div>
              <div style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                padding: '4px 12px',
                borderRadius: '9999px',
                fontSize: '14px',
                fontWeight: '500'
              }}>
                ⭐ Recommended
              </div>
            </div>

            {/* Plan Features */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', marginBottom: '24px' }}>
              {recommendation.plan.features.map((feature: string, index: number) => (
                <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <CheckCircle style={{ width: '20px', height: '20px', color: '#16a34a', flexShrink: 0 }} />
                  <span style={{ color: '#374151' }}>{feature}</span>
                </div>
              ))}
            </div>

            {/* Select Button */}
            <button
              onClick={handleSelectPlan}
              style={{
                width: '100%',
                backgroundColor: isSelected ? '#000000' : '#f3f4f6',
                color: isSelected ? 'white' : '#6b7280',
                padding: '12px 24px',
                borderRadius: '8px',
                fontWeight: '500',
                border: 'none',
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }}
            >
              {isSelected ? '✓ Selected' : 'Select This Plan'}
            </button>
          </div>

          {/* Action Buttons */}
          <div style={{ display: 'flex', gap: '12px', paddingTop: '16px' }}>
            <button
              onClick={() => setShowVideo(true)}
              style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <Play size={16} style={{ color: '#6b7280' }} />
              Watch Video
            </button>
            <button
              onClick={() => setShowCompare(true)}
              style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <BarChart3 size={16} style={{ color: '#6b7280' }} />
              Compare Plans
            </button>
            <button
              onClick={() => setShowHelp(true)}
              style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <span style={{ color: '#2563eb' }}>❓</span>
              Ask Questions
            </button>
          </div>
        </div>
      </div>

      {/* Modal Components */}
      {showHelp && (
        <FloatingHelp onClose={() => setShowHelp(false)} />
      )}

      {showVideo && (
        <VideoPlayer
          title="Medical Plan Overview"
          description="Learn about your medical plan options and benefits"
          planType="medical"
          onClose={() => setShowVideo(false)}
        />
      )}

      {showCompare && (
        <CompareModal
          plans={[]}
          onClose={() => setShowCompare(false)}
        />
      )}
    </div>
  );
};
