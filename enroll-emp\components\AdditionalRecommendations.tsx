import React, { useState } from 'react';
import { UserProfile } from './BenefitsEnrollmentBot';
import { Heart, Shield, CheckCircle, X, Plus } from 'lucide-react';

interface AdditionalRecommendationsProps {
  userProfile: UserProfile;
  onComplete: (additionalPlans: any) => void;
}

export const AdditionalRecommendations = ({ userProfile, onComplete }: AdditionalRecommendationsProps) => {
  const [selectedPetInsurance, setSelectedPetInsurance] = useState<any>(null);
  const [selectedHospitalIndemnity, setSelectedHospitalIndemnity] = useState<any>(null);

  const petInsurancePlans = [
    {
      name: "MetLife Pet Plan",
      cost: 18.50,
      features: [
        "Up to 90% reimbursement for vet bills",
        "No age limits for enrollment",
        "Coverage for accidents & illness",
        "Optional wellness add-on available"
      ]
    },
    {
      name: "ASPCA Pet Health Plan",
      cost: 22.00,
      features: [
        "Comprehensive accident & illness coverage",
        "Hereditary condition coverage",
        "24/7 pet helpline",
        "No breed restrictions"
      ]
    }
  ];

  const hospitalIndemnityPlans = [
    {
      name: "Hospital Cash Plan",
      cost: 8.75,
      features: [
        "Cash benefit for hospital stays",
        "No network restrictions",
        "Use funds however you need",
        "Covers unexpected medical expenses"
      ]
    },
    {
      name: "Critical Illness Plan",
      cost: 12.50,
      features: [
        "Lump sum for critical illness diagnosis",
        "Cancer, heart attack, stroke coverage",
        "Help with out-of-pocket expenses",
        "Peace of mind protection"
      ]
    }
  ];

  const hospitalIndemnityBenefits = [
    "Cash payments directly to you for hospital stays",
    "Helps cover deductibles and out-of-pocket costs",
    "No network restrictions - use any hospital"
  ];

  const handlePetPlanSelect = (plan: any) => {
    setSelectedPetInsurance(plan);
    // Auto-advance if both selections are made
    if (selectedHospitalIndemnity) {
      setTimeout(() => {
        onComplete({
          selectedPetInsurance: plan,
          selectedHospitalIndemnity: selectedHospitalIndemnity
        });
      }, 500);
    }
  };

  const handleHospitalPlanSelect = (plan: any) => {
    setSelectedHospitalIndemnity(plan);
    // Auto-advance if both selections are made
    if (selectedPetInsurance) {
      setTimeout(() => {
        onComplete({
          selectedPetInsurance: selectedPetInsurance,
          selectedHospitalIndemnity: plan
        });
      }, 500);
    }
  };

  const handleSkipPet = () => {
    const skipPlan = { name: 'No Pet Insurance', cost: 0 };
    setSelectedPetInsurance(skipPlan);
    // Auto-advance if hospital selection is also made
    if (selectedHospitalIndemnity) {
      setTimeout(() => {
        onComplete({
          selectedPetInsurance: skipPlan,
          selectedHospitalIndemnity: selectedHospitalIndemnity
        });
      }, 500);
    }
  };

  const handleSkipHospital = () => {
    const skipPlan = { name: 'No Hospital Indemnity', cost: 0 };
    setSelectedHospitalIndemnity(skipPlan);
    // Auto-advance if pet selection is also made
    if (selectedPetInsurance) {
      setTimeout(() => {
        onComplete({
          selectedPetInsurance: selectedPetInsurance,
          selectedHospitalIndemnity: skipPlan
        });
      }, 500);
    }
  };

  const handleContinue = () => {
    onComplete({
      selectedPetInsurance: selectedPetInsurance || { name: 'No Pet Insurance', cost: 0 },
      selectedHospitalIndemnity: selectedHospitalIndemnity || { name: 'No Hospital Indemnity', cost: 0 }
    });
  };

  const isComplete = selectedPetInsurance && selectedHospitalIndemnity;

  return (
    <div className="space-y-6">
      {/* Pet Insurance Recommendation */}
      <div className="border-2 border-orange-200 bg-orange-50 rounded-lg">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Heart className="w-5 h-5 text-orange-500" />
            🐕 Are you a pet owner?
            <span className="bg-orange-500 text-white px-2 py-1 rounded text-sm">Recommended</span>
          </h3>
          <p className="text-sm text-gray-600 mt-2">
            Pet insurance can help you manage unexpected veterinary costs and ensure your furry family members get the care they need.
          </p>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid gap-3">
            {petInsurancePlans.map((plan, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedPetInsurance?.name === plan.name
                    ? 'border-orange-500 bg-orange-100'
                    : 'border-gray-200 bg-white hover:border-orange-300'
                }`}
                onClick={() => handlePetPlanSelect(plan)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{plan.name}</h4>
                  <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">${plan.cost}/paycheck</span>
                </div>
                <ul className="space-y-1">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <button onClick={handleSkipPet} className="flex-1 flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <X className="w-4 h-4" />
              Skip Pet Insurance
            </button>
          </div>
        </div>
      </div>

      {/* Hospital Indemnity Recommendation */}
      <div className="border-2 border-blue-200 bg-blue-50 rounded-lg">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-500" />
            🏥 Want extra financial protection?
          </h3>
          <p className="text-sm text-gray-600 mt-2">
            Hospital indemnity insurance provides cash benefits to help with unexpected medical expenses.
          </p>
        </div>
        <div className="p-6 space-y-4">
          <div className="bg-blue-100 p-3 rounded-lg">
            <p className="font-medium text-sm mb-2">Top 3 Benefits:</p>
            <ul className="space-y-1">
              {hospitalIndemnityBenefits.map((benefit, index) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <Plus className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="grid gap-3">
            {hospitalIndemnityPlans.map((plan, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedHospitalIndemnity?.name === plan.name
                    ? 'border-blue-500 bg-blue-100'
                    : 'border-gray-200 bg-white hover:border-blue-300'
                }`}
                onClick={() => handleHospitalPlanSelect(plan)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{plan.name}</h4>
                  <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">${plan.cost}/paycheck</span>
                </div>
                <ul className="space-y-1">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <button onClick={handleSkipHospital} className="flex-1 flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <X className="w-4 h-4" />
              Skip Hospital Indemnity
            </button>
          </div>
        </div>
      </div>

      {/* Continue Button - Only show if both selections made but auto-advance didn't trigger */}
      {isComplete && (
        <>
          <div className="border-t border-gray-200 my-6"></div>
          <div className="flex justify-center">
            <button onClick={handleContinue} className="w-full max-w-md px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
              Continue to Summary
            </button>
          </div>
        </>
      )}
    </div>
  );
};
