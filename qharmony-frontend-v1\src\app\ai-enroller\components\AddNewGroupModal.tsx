'use client';

import React, { useState } from 'react';
import { HiOutlineX, HiOutlineOfficeBuilding } from 'react-icons/hi';
import { brokerAddsCompany } from '@/middleware/company_middleware';
import './AddNewGroupModal.css';

interface AddNewGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddNewGroupModal: React.FC<AddNewGroupModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState({
    companyName: '',
    adminName: '',
    adminEmail: '',
    industry: '',
    location: '',
    companySize: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const industries = [
    'Technology',
    'Healthcare',
    'Finance',
    'Manufacturing',
    'Retail',
    'Education',
    'Construction',
    'Real Estate',
    'Transportation',
    'Hospitality',
    'Other'
  ];

  const companySizes = [
    '1-10 employees',
    '11-50 employees',
    '51-200 employees',
    '201-500 employees',
    '501-1000 employees',
    '1000+ employees'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const brokerId = (typeof window !== 'undefined') ? localStorage.getItem('userid1') : null;
      if (!brokerId) {
        throw new Error('User not authenticated');
      }

      const response = await brokerAddsCompany(
        brokerId,
        formData.companyName,
        formData.adminEmail,
        formData.adminName
      );

      if (response.status === 200) {
        onSuccess();
        setFormData({
          companyName: '',
          adminName: '',
          adminEmail: '',
          industry: '',
          location: '',
          companySize: ''
        });
      } else {
        throw new Error(response.data?.message || 'Failed to add company');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    return formData.companyName.trim() && 
           formData.adminName.trim() && 
           formData.adminEmail.trim() &&
           formData.adminEmail.includes('@');
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        <div className="modal-header">
          <div className="modal-title">
            <HiOutlineOfficeBuilding size={24} />
            <h2>Add New Employer Group</h2>
          </div>
          <button className="close-button" onClick={onClose}>
            <HiOutlineX size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-form">
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="companyName">Company Name *</label>
              <input
                type="text"
                id="companyName"
                name="companyName"
                value={formData.companyName}
                onChange={handleInputChange}
                placeholder="Enter company name"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="adminName">Admin Name *</label>
              <input
                type="text"
                id="adminName"
                name="adminName"
                value={formData.adminName}
                onChange={handleInputChange}
                placeholder="Enter admin full name"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="adminEmail">Admin Email *</label>
              <input
                type="email"
                id="adminEmail"
                name="adminEmail"
                value={formData.adminEmail}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="industry">Industry</label>
              <select
                id="industry"
                name="industry"
                value={formData.industry}
                onChange={handleInputChange}
              >
                <option value="">Select industry</option>
                {industries.map(industry => (
                  <option key={industry} value={industry}>
                    {industry}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="location">Location</label>
              <input
                type="text"
                id="location"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                placeholder="City, State"
              />
            </div>

            <div className="form-group">
              <label htmlFor="companySize">Company Size</label>
              <select
                id="companySize"
                name="companySize"
                value={formData.companySize}
                onChange={handleInputChange}
              >
                <option value="">Select company size</option>
                {companySizes.map(size => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <div className="form-actions">
            <button
              type="button"
              className="cancel-button"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="submit-button"
              disabled={!isFormValid() || loading}
            >
              {loading ? 'Adding...' : 'Add Company'}
            </button>
          </div>
        </form>

        <div className="modal-note">
          <p>
            <strong>Note:</strong> The admin will receive an email invitation to set up their account 
            and complete the company onboarding process.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AddNewGroupModal;
