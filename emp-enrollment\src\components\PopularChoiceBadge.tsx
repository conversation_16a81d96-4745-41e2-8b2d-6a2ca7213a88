
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Users } from 'lucide-react';

interface PopularChoiceBadgeProps {
  percentage: number;
  planType: 'medical' | 'dental' | 'vision';
}

export const PopularChoiceBadge = ({ percentage, planType }: PopularChoiceBadgeProps) => {
  const getIcon = () => {
    if (percentage >= 70) return <TrendingUp className="w-3 h-3" />;
    return <Users className="w-3 h-3" />;
  };

  const getVariant = () => {
    if (percentage >= 70) return "default";
    return "secondary";
  };

  const getText = () => {
    if (percentage >= 70) return `🔥 ${percentage}% Choose This`;
    return `👥 ${percentage}% Popular`;
  };

  return (
    <Badge variant={getVariant()} className="flex items-center gap-1">
      {getIcon()}
      {getText()}
    </Badge>
  );
};
