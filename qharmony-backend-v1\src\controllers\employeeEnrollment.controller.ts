import express from 'express';
import Controller from '../interfaces/controller.interface';
import logger from '../utils/logger';
import EmployeeEnrollmentModelClass, {
  EmployeeEnrollmentDataInterface
} from '../nosql/preEnrollment/employeeEnrollment.model';
import UserModelClass from '../nosql/user.model';

import PlanAssignmentModelClass from '../nosql/preEnrollment/planAssignment.model';
import EmployeeEnrollmentService from '../services/employeeEnrollmentService';
import EmployeeEnrollmentMiddleware from '../middleware/employeeEnrollment.middleware';
import { ENROLLMENT_PERIOD_TYPES } from '../constants';

/**
 * EmployeeEnrollmentController
 * 
 * Handles employee enrollment operations for the pre-enrollment system.
 * This controller manages employee enrollments in plan assignments.
 * 
 * TODO: Implement full CRUD operations for employee enrollments
 * - Create enrollment
 * - Get enrollments by employee/company/plan
 * - Update enrollment details
 * - Cancel/terminate enrollments
 * - Enrollment validation and business rules
 */
class EmployeeEnrollmentController implements Controller {
  public router = express.Router();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // Apply middleware to all routes
    const middleware = EmployeeEnrollmentMiddleware.validateUserAuth;

    // ===== CORE ENROLLMENT OPERATIONS (APIs 1-6) =====
    this.router.post('/api/pre-enrollment/employee-enrollments/check-eligibility', middleware, this.checkEligibility);
    this.router.get('/api/pre-enrollment/employee-enrollments/enrollment-periods/:planAssignmentId', middleware, this.getEnrollmentPeriods);
    this.router.post('/api/pre-enrollment/employee-enrollments/calculate-cost', middleware, this.calculateCost);
    this.router.post('/api/pre-enrollment/employee-enrollments/estimate-plan-costs', middleware, this.estimatePlanCosts);
    this.router.post('/api/pre-enrollment/employee-enrollments', middleware, this.createEnrollment);
    this.router.get('/api/pre-enrollment/employee-enrollments/employee/:employeeId', middleware, this.getEmployeeEnrollments);
    this.router.put('/api/pre-enrollment/employee-enrollments/:enrollmentId', middleware, this.updateEnrollment);
    this.router.delete('/api/pre-enrollment/employee-enrollments/:enrollmentId', middleware, this.deleteEnrollment);

    // ===== STATUS MANAGEMENT OPERATIONS =====
    this.router.post('/api/pre-enrollment/employee-enrollments/:enrollmentId/terminate', middleware, this.terminateEnrollment);
    this.router.post('/api/pre-enrollment/employee-enrollments/:enrollmentId/waive', middleware, this.waiveEnrollment);
    this.router.post('/api/pre-enrollment/employee-enrollments/:enrollmentId/reinstate', middleware, this.reinstateEnrollment);
    this.router.post('/api/pre-enrollment/employee-enrollments/:enrollmentId/activate', middleware, this.activateEnrollment);

    // ===== BULK OPERATIONS =====
    this.router.post('/api/pre-enrollment/employee-enrollments/bulk-waive', middleware, this.bulkWaiveEnrollments);
    this.router.post('/api/pre-enrollment/employee-enrollments/bulk', middleware, this.bulkEnrollment);

    // ===== EXPIRY MANAGEMENT OPERATIONS =====
    this.router.get('/api/pre-enrollment/employee-enrollments/expired', middleware, this.getExpiredEnrollments);
    this.router.post('/api/pre-enrollment/employee-enrollments/check-expired', middleware, this.checkExpiredEnrollments);

    // ===== FUTURE ENHANCEMENTS (Optional) =====
    // this.router.get('/api/pre-enrollment/employee-enrollments/company/:companyId', this.getCompanyEnrollments);
    // this.router.get('/api/pre-enrollment/employee-enrollments/plan-assignment/:planAssignmentId', this.getPlanAssignmentEnrollments);
  }



  // ===== HELPER METHODS =====







  // ===== API IMPLEMENTATIONS =====

  /**
   * API 1: Check Eligibility
   * POST /api/pre-enrollment/employee-enrollments/check-eligibility
   */
  private checkEligibility = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const {
        employeeId,
        planAssignmentId,
        enrollmentType,
        qualifyingLifeEvent,
        newHireDate
      } = request.body;

      // Validate required fields
      if (!employeeId || !planAssignmentId) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'employeeId and planAssignmentId are required');
      }

      // Use service method for eligibility check (uses existing validateComprehensiveEligibility)
      const result = await EmployeeEnrollmentService.checkEligibility({
        employeeId,
        planAssignmentId,
        enrollmentType,
        qualifyingLifeEvent,
        newHireDate
      }, userId, user);

      // Handle existing enrollment case (check if result has isEligible property)
      if ('isEligible' in result && !result.isEligible && result.reasons?.includes('Employee already enrolled in this plan')) {
        const existingEnrollment = await EmployeeEnrollmentModelClass.getDataByEmployeeAndPlan(employeeId, planAssignmentId);
        return response.status(409).json({
          isEligible: false,
          reason: 'Employee already enrolled in this plan',
          existingEnrollment: {
            enrollmentId: existingEnrollment?._id,
            status: existingEnrollment?.status,
            effectiveDate: existingEnrollment?.effectiveDate,
            enrollmentDate: existingEnrollment?.enrollmentDate
          },
          timestamp: new Date().toISOString()
        });
      }

      return EmployeeEnrollmentMiddleware.eligibilityChecked(response, result);

    } catch (error) {
      logger.error('Error checking enrollment eligibility:', error);
      return EmployeeEnrollmentMiddleware.internalError(response, 'Internal server error');
    }
  };



  /**
   * 🎯 SIMPLIFIED: Determine Enrollment Type based on request data
   * This method analyzes the request and determines the appropriate enrollment type
   */
  private determineEnrollmentTypeFromRequest(
    requestedType?: string,
    qualifyingLifeEvent?: any,
    newHireDate?: string
  ): {
    enrollmentType: string;
    reason: string;
    isValid: boolean;
  } {
    // If explicitly requested and valid, use it
    if (requestedType && ENROLLMENT_PERIOD_TYPES.includes(requestedType as any)) {
      if (requestedType === 'Qualifying Life Event' && !qualifyingLifeEvent) {
        return {
          enrollmentType: requestedType,
          reason: 'QLE enrollment type requested but no qualifying life event data provided',
          isValid: false
        };
      }
      if (requestedType === 'New Hire' && !newHireDate) {
        return {
          enrollmentType: requestedType,
          reason: 'New Hire enrollment type requested but no hire date provided',
          isValid: false
        };
      }
      return {
        enrollmentType: requestedType,
        reason: `Using requested enrollment type: ${requestedType}`,
        isValid: true
      };
    }

    // Auto-detect based on provided data
    if (qualifyingLifeEvent?.eventType && qualifyingLifeEvent?.eventDate) {
      return {
        enrollmentType: 'Qualifying Life Event',
        reason: 'Auto-detected QLE enrollment based on provided qualifying life event data',
        isValid: true
      };
    }

    if (newHireDate) {
      return {
        enrollmentType: 'New Hire',
        reason: 'Auto-detected New Hire enrollment based on provided hire date',
        isValid: true
      };
    }

    // Default to Open Enrollment
    return {
      enrollmentType: 'Open Enrollment',
      reason: 'Defaulting to Open Enrollment (no specific enrollment type indicators found)',
      isValid: true
    };
  }

  /**
   * 🎯 NEW API: Get Enrollment Periods
   * GET /api/pre-enrollment/employee-enrollments/enrollment-periods/:planAssignmentId
   *
   * Purpose: Get all available enrollment periods for a plan assignment
   */
  private getEnrollmentPeriods = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { planAssignmentId } = request.params;

      if (!userId) {
        return response.status(401).json({ error: 'User ID required in headers' });
      }

      if (!planAssignmentId) {
        return response.status(400).json({ error: 'planAssignmentId is required' });
      }

      // Get user for service validation
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        return response.status(401).json({ error: 'Invalid user' });
      }

      // Use service method for getting enrollment periods
      const result = await EmployeeEnrollmentService.getEnrollmentPeriods(planAssignmentId, userId, user);

      if (!result.success) {
        if (result.error?.includes('not found')) {
          return response.status(404).json({ error: result.error });
        }
        if (result.error?.includes('Access denied')) {
          return response.status(403).json({ error: result.error });
        }
        return response.status(500).json({ error: result.error || 'Failed to get enrollment periods' });
      }

      // Use standardized middleware response
      return response.status(200).json({
        success: true,
        message: 'Enrollment periods retrieved successfully',
        enrollmentPeriods: result.enrollmentPeriods,
        planAssignmentId: result.planAssignmentId,
        currentDate: result.currentDate,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error getting enrollment periods:', error);
      return EmployeeEnrollmentMiddleware.internalError(response, 'Internal server error');
    }
  };

  /**
   * API 2: Calculate Cost
   * POST /api/pre-enrollment/employee-enrollments/calculate-cost
   */
  private calculateCost = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const { employeeId, planAssignmentId, coverageTier, dependentIds = [] } = request.body;

      // Validate required fields
      if (!employeeId || !planAssignmentId || !coverageTier) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'employeeId, planAssignmentId, and coverageTier are required');
      }

      // Use service method for cost calculation
      const result = await EmployeeEnrollmentService.calculateCost({
        employeeId,
        planAssignmentId,
        coverageTier,
        dependentIds
      }, userId, user);

      if (!result.success) {
        if (result.error === 'Plan assignment not found') {
          return EmployeeEnrollmentMiddleware.notFound(response, 'Plan assignment');
        }
        if (result.error?.includes('Access denied')) {
          return EmployeeEnrollmentMiddleware.accessDenied(response, result.error);
        }
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Cost calculation failed');
      }

      return EmployeeEnrollmentMiddleware.costCalculated(response, result);

    } catch (error) {
      logger.error('Error calculating enrollment cost:', error);
      return EmployeeEnrollmentMiddleware.internalError(response, 'Internal server error');
    }
  };

  /**
   * 🎯 NEW API: Estimate Plan Assignment Costs
   * POST /api/pre-enrollment/employee-enrollments/estimate-plan-costs
   *
   * Purpose: Estimate costs for a plan assignment across all tiers and scenarios
   * without requiring a specific employee
   */
  private estimatePlanCosts = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const {
        planAssignmentId,
        scenarios = [],
        payrollFrequency = 'Monthly'
      } = request.body;

      if (!userId) {
        return response.status(401).json({ error: 'User ID required in headers' });
      }

      if (!planAssignmentId) {
        return response.status(400).json({
          error: 'planAssignmentId is required'
        });
      }

      // Get user for service validation
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        return response.status(401).json({ error: 'Invalid user' });
      }

      // Use service method for cost estimation
      const result = await EmployeeEnrollmentService.estimatePlanCosts(
        { planAssignmentId, scenarios },
        userId,
        user
      );

      if (!result.success) {
        if (result.error?.includes('not found')) {
          return response.status(404).json({ error: result.error });
        }
        if (result.error?.includes('Access denied')) {
          return response.status(403).json({ error: result.error });
        }
        return response.status(500).json({ error: result.error || 'Failed to estimate plan costs' });
      }

      // Format response to match existing API structure
      response.status(200).json({
        success: true,
        planAssignmentId: result.planAssignmentId,
        costEstimations: result.costEstimations,
        metadata: {
          payrollFrequency,
          scenarioCount: result.scenarios?.length || 0,
          tierCount: result.costEstimations?.length || 0,
          calculatedAt: result.calculationDate
        }
      });

    } catch (error) {
      logger.error('Error estimating plan costs:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * API 3: Create Enrollment
   * POST /api/pre-enrollment/employee-enrollments
   */
  private createEnrollment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const {
        employeeId,
        planAssignmentId,
        coverageTier,
        dependentIds = [],
        enrollmentType = 'Open Enrollment',
        qualifyingLifeEvent,
        newHireDate
      } = request.body;

      // Use simplified enrollment type determination
      const enrollmentTypeResult = this.determineEnrollmentTypeFromRequest(
        enrollmentType,
        qualifyingLifeEvent,
        newHireDate
      );

      if (!enrollmentTypeResult.isValid) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Invalid enrollment type configuration');
      }

      // Get plan assignment for validation
      const planAssignment = await PlanAssignmentModelClass.getDataById(planAssignmentId);
      if (!planAssignment) {
        return EmployeeEnrollmentMiddleware.notFound(response, 'Plan assignment');
      }

      // Early validation for QLE requirements
      if (enrollmentTypeResult.enrollmentType === 'Qualifying Life Event') {
        if (!qualifyingLifeEvent || !qualifyingLifeEvent.eventType || !qualifyingLifeEvent.eventDate) {
          return EmployeeEnrollmentMiddleware.handleServiceError(response,
            'Qualifying Life Event data required: eventType and eventDate are required for QLE enrollments');
        }
      }

      // Final duplicate check to prevent race conditions
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataByEmployeeAndPlan(employeeId, planAssignmentId);
      if (existingEnrollment) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Employee already enrolled in this plan assignment', 409);
      }

      // Use service method for creating enrollment (uses existing createEnrollmentWithCostCalculation)
      const result = await EmployeeEnrollmentService.createEnrollment({
        employeeId,
        planAssignmentId,
        coverageTier,
        dependentIds,
        enrollmentType: enrollmentTypeResult.enrollmentType,
        qualifyingLifeEvent,
        newHireDate
      }, userId, user);

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, (result as any).error || 'Enrollment creation failed');
      }

      // Pass eligibility details to response
      const eligibilityDetails = {
        determinedType: enrollmentTypeResult.enrollmentType,
        requestedType: request.body.enrollmentType,
        wasEligible: true, // If we got here, enrollment was successful
        overriddenByAdmin: (result as any).overriddenByAdmin || false
      };

      return EmployeeEnrollmentMiddleware.enrollmentCreated(response, (result as any).enrollment, eligibilityDetails);

    } catch (error) {
      logger.error('Error creating enrollment:', error);
      return EmployeeEnrollmentMiddleware.internalError(response, 'Internal server error');
    }
  };

  /**
   * API 7: Get Employee Enrollments (Enhanced with Status Filtering)
   * GET /api/pre-enrollment/employee-enrollments/employee/:employeeId
   *
   * Query Parameters:
   * - includeWaived: boolean (default: true) - Include waived enrollments
   * - includeTerminated: boolean (default: true) - Include terminated enrollments
   * - includeExpired: boolean (default: false) - Include expired enrollments
   * - status: string (optional) - Filter by specific status (legacy support)
   * - planAssignmentId: string (optional) - Filter by plan assignment
   *
   * Default behavior: Returns currently enrolled + waived + terminated (excludes expired)
   * If all include parameters are false: Returns only currently enrolled (Pending + Enrolled)
   */
  private getEmployeeEnrollments = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const { employeeId } = request.params;
      const { status, planAssignmentId } = request.query;

      // Parse enhanced query parameters with defaults
      const includeWaived = request.query.includeWaived !== 'false'; // Default: true
      const includeTerminated = request.query.includeTerminated !== 'false'; // Default: true
      const includeExpired = request.query.includeExpired === 'true'; // Default: false

      // Validate required parameters
      if (!employeeId) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, 'employeeId is required');
      }

      // Use service method for getting employee enrollments
      const result = await EmployeeEnrollmentService.getEnrollmentsByEmployee(
        employeeId,
        {
          includeWaived,
          includeTerminated,
          includeExpired,
          planAssignmentId: planAssignmentId as string,
          status: status as string
        },
        userId,
        user
      );

      if (!result.success) {
        if (result.error?.includes('Access denied')) {
          return EmployeeEnrollmentMiddleware.accessDenied(response, result.error);
        }
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Failed to get enrollments');
      }

      return EmployeeEnrollmentMiddleware.enrollmentsListed(response, {
        enrollments: result.enrollments,
        filters: {
          includeWaived,
          includeTerminated,
          includeExpired,
          status,
          planAssignmentId
        }
      });

    } catch (error) {
      logger.error('Error getting employee enrollments:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  // 🎯 FUTURE ENHANCEMENTS: Company and Plan Assignment enrollment views
  // These methods can be implemented when needed for dashboard features

  /**
   * API 5: Update Enrollment
   * PUT /api/pre-enrollment/employee-enrollments/:enrollmentId
   */
  private updateEnrollment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const { enrollmentId } = request.params;
      const updateData = request.body;

      // Get existing enrollment for validation
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return EmployeeEnrollmentMiddleware.notFound(response, 'Enrollment');
      }

      // Check if enrollment can be edited (must be within enrollment period unless SuperAdmin)
      const periodValidation = await EmployeeEnrollmentService.validateEnrollmentPeriodForExistingEnrollment({
        planAssignmentId: existingEnrollment.planAssignmentId,
        isSuperAdmin: UserModelClass.isSuperAdmin(user),
        operation: 'edit'
      });

      if (!periodValidation.isAllowed) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, periodValidation.reason);
      }

      // Check if enrollment is expired
      if (existingEnrollment.status === 'Expired') {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Cannot edit expired enrollment. Expired enrollments are read-only.');
      }

      // Check if enrollment status allows editing
      if (['Terminated', 'Expired'].includes(existingEnrollment.status)) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          `Cannot edit enrollment with status: ${existingEnrollment.status}`);
      }

      // Use service method for update
      const result = await EmployeeEnrollmentService.updateEnrollment(
        enrollmentId,
        updateData,
        userId,
        user
      );

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Update failed');
      }

      return EmployeeEnrollmentMiddleware.enrollmentUpdated(response, result.enrollment);

    } catch (error) {
      logger.error('Error updating enrollment:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };



  /**
   * API 6: Delete Enrollment
   * DELETE /api/pre-enrollment/employee-enrollments/:enrollmentId
   */
  private deleteEnrollment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const { enrollmentId } = request.params;

      // Get existing enrollment for validation
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return EmployeeEnrollmentMiddleware.notFound(response, 'Enrollment');
      }

      // Business Rule: Can only delete during enrollment period (unless SuperAdmin)
      const periodValidation = await EmployeeEnrollmentService.validateEnrollmentPeriodForExistingEnrollment({
        planAssignmentId: existingEnrollment.planAssignmentId,
        isSuperAdmin: UserModelClass.isSuperAdmin(user),
        operation: 'delete'
      });

      if (!periodValidation.isAllowed) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, periodValidation.reason);
      }

      // Check if enrollment is expired
      if (existingEnrollment.status === 'Expired') {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Cannot delete expired enrollment');
      }

      // Business Rule: Can only delete Pending or Waived enrollments
      if (!['Pending', 'Waived'].includes(existingEnrollment.status)) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          `Cannot delete enrollment with status: ${existingEnrollment.status}`);
      }

      // Use service method for deletion
      const result = await EmployeeEnrollmentService.deleteEnrollment(
        enrollmentId,
        userId,
        user
      );

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Deletion failed');
      }

      return EmployeeEnrollmentMiddleware.enrollmentDeleted(response);

    } catch (error) {
      logger.error('Error deleting enrollment:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * API 7: Terminate Enrollment
   * POST /api/pre-enrollment/employee-enrollments/:enrollmentId/terminate
   */
  private terminateEnrollment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const { enrollmentId } = request.params;
      const { terminationDate, terminationReason } = request.body;

      // Validate required fields
      if (!terminationDate || !terminationReason) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'terminationDate and terminationReason are required');
      }

      // Get existing enrollment for validation
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return EmployeeEnrollmentMiddleware.notFound(response, 'Enrollment');
      }

      // Check if enrollment is expired
      if (existingEnrollment.status === 'Expired') {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Cannot terminate expired enrollment. Expired enrollments are read-only.');
      }

      // Check if enrollment can be terminated
      if (existingEnrollment.status === 'Terminated') {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Enrollment is already terminated');
      }

      if (!['Enrolled', 'Pending'].includes(existingEnrollment.status)) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          `Cannot terminate enrollment with status: ${existingEnrollment.status}`);
      }

      // Validate termination date
      const termDate = new Date(terminationDate);
      const effectiveDate = new Date(existingEnrollment.effectiveDate);

      if (termDate < effectiveDate) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Termination date cannot be before effective date');
      }

      // Use service method for termination
      const result = await EmployeeEnrollmentService.terminateEnrollment(
        enrollmentId,
        { terminationDate, terminationReason },
        userId,
        user
      );

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Termination failed');
      }

      return EmployeeEnrollmentMiddleware.statusOperationCompleted(response, result);

    } catch (error) {
      logger.error('Error terminating enrollment:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  // ========================================
  // 🎯 STATUS MANAGEMENT SECTION
  // ========================================

  /**
   * STATUS API 1: Waive Enrollment
   * POST /api/pre-enrollment/employee-enrollments/:enrollmentId/waive
   */
  private waiveEnrollment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const { enrollmentId } = request.params;
      const { waiveReason } = request.body;

      // Validate required fields
      if (!waiveReason?.trim()) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, 'waiveReason is required');
      }

      // Get existing enrollment for validation
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return EmployeeEnrollmentMiddleware.notFound(response, 'Enrollment');
      }

      // Check enrollment period for non-SuperAdmin users
      const periodValidation = await EmployeeEnrollmentService.validateEnrollmentPeriodForExistingEnrollment({
        planAssignmentId: existingEnrollment.planAssignmentId,
        isSuperAdmin: UserModelClass.isSuperAdmin(user),
        operation: 'waive'
      });

      if (!periodValidation.isAllowed) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, periodValidation.reason);
      }

      // Check if enrollment is expired
      if (existingEnrollment.status === 'Expired') {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Cannot waive expired enrollment. Expired enrollments are read-only.');
      }

      // Validate status transition
      const validTransitions = EmployeeEnrollmentModelClass.getValidStatusTransitions(existingEnrollment.status);
      if (!validTransitions.includes('Waived')) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          `Cannot waive enrollment with status: ${existingEnrollment.status}`);
      }

      // Use service method for waiving
      const result = await EmployeeEnrollmentService.waiveEnrollment(
        enrollmentId,
        { waiveReason: waiveReason.trim() },
        userId,
        user
      );

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Waiving failed');
      }

      return EmployeeEnrollmentMiddleware.statusOperationCompleted(response, result);

    } catch (error) {
      logger.error('Error waiving enrollment:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * STATUS API 2: Reinstate Enrollment
   * POST /api/pre-enrollment/employee-enrollments/:enrollmentId/reinstate
   */
  private reinstateEnrollment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const { enrollmentId } = request.params;
      const { newStatus, reinstateReason, newEffectiveDate } = request.body;

      // Validate required fields
      if (!newStatus || !['Pending', 'Enrolled'].includes(newStatus)) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'newStatus is required and must be either "Pending" or "Enrolled"');
      }

      if (!reinstateReason?.trim()) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, 'reinstateReason is required');
      }

      // Get existing enrollment for validation
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return EmployeeEnrollmentMiddleware.notFound(response, 'Enrollment');
      }

      // Check if enrollment is expired
      if (existingEnrollment.status === 'Expired') {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Cannot reinstate expired enrollment. Expired enrollments are read-only.');
      }

      // Validate status transition
      const validTransitions = EmployeeEnrollmentModelClass.getValidStatusTransitions(existingEnrollment.status);
      if (!validTransitions.includes(newStatus)) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          `Cannot reinstate enrollment from ${existingEnrollment.status} to ${newStatus}`);
      }

      // Comprehensive eligibility validation for reinstatement
      const eligibilityCheck = await EmployeeEnrollmentModelClass.validateComprehensiveEligibility({
        employeeId: existingEnrollment.employeeId,
        planAssignmentId: existingEnrollment.planAssignmentId,
        operation: 'reinstatement',
        userId
      });

      if (!eligibilityCheck.isEligible) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Employee not eligible for reinstatement');
      }

      // Use service method for reinstatement
      const result = await EmployeeEnrollmentService.reinstateEnrollment(
        enrollmentId,
        { newStatus, reinstateReason, newEffectiveDate },
        userId,
        user
      );

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Reinstatement failed');
      }

      return EmployeeEnrollmentMiddleware.statusOperationCompleted(response, result);

    } catch (error) {
      logger.error('Error reinstating enrollment:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * STATUS API 3: Activate Enrollment
   * POST /api/pre-enrollment/employee-enrollments/:enrollmentId/activate
   */
  private activateEnrollment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const { enrollmentId } = request.params;
      const { activationDate } = request.body;

      // Get existing enrollment for validation
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return EmployeeEnrollmentMiddleware.notFound(response, 'Enrollment');
      }

      // Validate status transition
      const validTransitions = EmployeeEnrollmentModelClass.getValidStatusTransitions(existingEnrollment.status);
      if (!validTransitions.includes('Enrolled')) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          `Cannot activate enrollment with status: ${existingEnrollment.status}`);
      }

      // Comprehensive eligibility validation for activation
      const eligibilityCheck = await EmployeeEnrollmentModelClass.validateComprehensiveEligibility({
        employeeId: existingEnrollment.employeeId,
        planAssignmentId: existingEnrollment.planAssignmentId,
        operation: 'activation',
        userId
      });

      if (!eligibilityCheck.isEligible) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Employee not eligible for enrollment activation');
      }

      // Validate activation date if provided
      let activationDateToUse = new Date();
      if (activationDate) {
        activationDateToUse = new Date(activationDate);

        // Validate activation date is not in the past (allow same day)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        activationDateToUse.setHours(0, 0, 0, 0);

        if (activationDateToUse < today && !UserModelClass.isSuperAdmin(user)) {
          return EmployeeEnrollmentMiddleware.handleServiceError(response,
            'Activation date cannot be in the past');
        }
      }

      // Use service method for activation
      const result = await EmployeeEnrollmentService.activateEnrollment(
        enrollmentId,
        { activationDate: activationDateToUse },
        userId,
        user
      );

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Activation failed');
      }

      return EmployeeEnrollmentMiddleware.statusOperationCompleted(response, result);

    } catch (error) {
      logger.error('Error activating enrollment:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * BULK API 1: Bulk Waive Plans
   * POST /api/pre-enrollment/employee-enrollments/bulk-waive
   */
  private bulkWaiveEnrollments = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const {
        employeeId,
        planAssignmentIds,
        waiveReason,
        waiveDate,
        enrollmentType = 'Open Enrollment',
        companyId
      } = request.body;

      // Validate enrollment type
      if (!ENROLLMENT_PERIOD_TYPES.includes(enrollmentType as any)) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Invalid enrollmentType');
      }

      // Use service method for bulk waive
      const result = await EmployeeEnrollmentService.bulkWaiveEnrollments({
        employeeId,
        planAssignmentIds,
        waiveReason,
        waiveDate,
        companyId
      }, userId, user);

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Bulk waive operation failed');
      }

      return EmployeeEnrollmentMiddleware.bulkOperationCompleted(response, result);

    } catch (error) {
      logger.error('Error in bulk waive enrollments:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * BULK API 2: Bulk Enrollment in Multiple Plans
   * POST /api/pre-enrollment/employee-enrollments/bulk
   */
  private bulkEnrollment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, user } = request as any; // From middleware
      const {
        employeeId,
        companyId,
        employeeClassType = 'Full-Time',
        planSelections,
        effectiveDate
      } = request.body;

      // Validation
      if (!employeeId || !companyId || !planSelections || !Array.isArray(planSelections)) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'Missing required fields: employeeId, companyId, planSelections');
      }

      if (planSelections.length === 0) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response,
          'At least one plan selection is required');
      }

      // Validate each plan selection
      for (let i = 0; i < planSelections.length; i++) {
        const selection = planSelections[i];
        if (!selection.planAssignmentId || !selection.coverageTier) {
          return EmployeeEnrollmentMiddleware.handleServiceError(response,
            `Invalid plan selection at index ${i}: planAssignmentId and coverageTier are required`);
        }

        // Validate QLE data if enrollment type is QLE
        if (selection.enrollmentType === 'Qualifying Life Event') {
          if (!selection.qualifyingLifeEvent || !selection.qualifyingLifeEvent.eventType || !selection.qualifyingLifeEvent.eventDate) {
            return EmployeeEnrollmentMiddleware.handleServiceError(response,
              `Invalid QLE data for plan selection at index ${i}: eventType and eventDate are required for QLE enrollments`);
          }
        }
      }

      // Use service method for bulk enrollment
      const result = await EmployeeEnrollmentService.bulkEnrollment({
        employeeId,
        companyId,
        employeeClassType,
        planSelections,
        effectiveDate
      }, userId, user);

      if (!result.success) {
        return EmployeeEnrollmentMiddleware.handleServiceError(response, result.error || 'Bulk enrollment failed');
      }

      return EmployeeEnrollmentMiddleware.bulkOperationCompleted(response, result);

    } catch (error) {
      logger.error('Error in bulk enrollment:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * API 8: Get Expired Enrollments (with automatic expiry check)
   * GET /api/pre-enrollment/employee-enrollments/expired
   *
   * Query Parameters:
   * - mode: 'user' (default) | 'planAssignments'
   * - targetUserId: User ID to get expired enrollments for (mode=user)
   * - planAssignmentIds: Comma-separated plan assignment IDs (mode=planAssignments)
   */
  private getExpiredEnrollments = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const mode = (request.query.mode as string) || 'user';
      const targetUserId = request.query.targetUserId as string;
      const planAssignmentIds = request.query.planAssignmentIds as string;

      if (!userId) {
        return response.status(401).json({ error: 'User ID required in headers' });
      }

      // Get user for service validation
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        return response.status(404).json({ error: 'User not found' });
      }

      // Validate mode
      if (!['user', 'planAssignments'].includes(mode)) {
        return response.status(400).json({
          error: 'Invalid mode. Must be "user" or "planAssignments"',
          validModes: ['user', 'planAssignments']
        });
      }

      // Use service method for getting expired enrollments
      const result = await EmployeeEnrollmentService.getExpiredEnrollments(
        { mode, targetUserId, planAssignmentIds },
        userId,
        user
      );

      if (!result.success) {
        if (result.error?.includes('not found')) {
          return response.status(404).json({ error: result.error });
        }
        if (result.error?.includes('Access denied')) {
          return response.status(403).json({ error: result.error });
        }
        if (result.error?.includes('required')) {
          return response.status(400).json({ error: result.error });
        }
        return response.status(500).json({ error: result.error || 'Failed to get expired enrollments' });
      }

      response.status(200).json(result);

    } catch (error) {
      logger.error('Error getting expired enrollments:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  /**
   * API 9: Manual Expiry Check (SuperAdmin only)
   * POST /api/pre-enrollment/employee-enrollments/check-expired
   */
  private checkExpiredEnrollments = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;

      if (!userId) {
        return response.status(401).json({ error: 'User ID required in headers' });
      }

      // Get user for service validation
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        return response.status(404).json({ error: 'User not found' });
      }

      // Use service method for checking expired enrollments
      const result = await EmployeeEnrollmentService.checkExpiredEnrollments(userId, user);

      if (!result.success) {
        if (result.error?.includes('Access denied')) {
          return response.status(403).json({ error: result.error });
        }
        return response.status(500).json({ error: result.error || 'Failed to check expired enrollments' });
      }

      response.status(200).json(result);

    } catch (error) {
      logger.error('Error performing manual expiry check:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  // ========================================
  // 🎯 STATUS MANAGEMENT SUMMARY
  // ========================================

  /**
   * ENROLLMENT STATUS LIFECYCLE & TRANSITIONS:
   *
   * 📋 VALID STATUS TRANSITIONS:
   * - Pending → Enrolled (activate API or update API)
   * - Pending → Waived (waive API)
   * - Pending → Terminated (terminate API)
   * - Enrolled → Waived (waive API)
   * - Enrolled → Terminated (terminate API)
   * - Waived → Pending (reinstate API)
   * - Waived → Enrolled (reinstate API)
   * - Terminated → Pending (reinstate API)
   * - Terminated → Enrolled (reinstate API)
   *
   * 🎯 DEDICATED STATUS APIS:
   * - POST /:enrollmentId/waive - Employee declines coverage
   * - POST /:enrollmentId/reinstate - Restore waived/terminated enrollment
   * - POST /:enrollmentId/activate - Move pending to enrolled
   * - POST /:enrollmentId/terminate - End coverage early
   * - PUT /:enrollmentId - General updates with status transition validation
   *
   * ✅ COMPREHENSIVE VALIDATION:
   * - Eligibility validation for all operations
   * - Enrollment period validation (SuperAdmin override)
   * - Employee class validation
   * - Waiting period validation
   * - Profile completeness validation
   * - Cost recalculation on tier/dependent changes
   * - Access control validation
   *
   * 🔧 BUSINESS RULES:
   * - Waived enrollments require waiveReason
   * - Terminated enrollments require terminationDate and reason
   * - Reinstatement requires reason and new status
   * - Activation validates effective date
   * - All operations respect enrollment periods (unless SuperAdmin)
   * - Status transitions follow defined business logic
   */
}

export default EmployeeEnrollmentController;
