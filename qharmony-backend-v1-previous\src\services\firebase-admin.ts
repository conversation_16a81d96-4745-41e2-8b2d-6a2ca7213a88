import * as admin from 'firebase-admin';

const serviceAccount = {
  type: 'service_account',
  project_id: 'qharmony-dev',
  private_key_id: '5253a574bcce3d79ba73bcafa608d0631086d4da',
  private_key:
    '-----B<PERSON>IN PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCMJz7P2+S4HsRI\ni2yNHyoh0oXeCmHfJAPsZ4AQg4++kdhQhDY+aN96E0lA2zyFsN+VyA9eEM5tvKbF\nbe+NotTNWBEtCf5rcPq1ldxiRcSeu7ClnMPIdxdW1dQ/wZgQ+9W3vizxGsSHMCZ/\nGP9bmXrBgF44Haq01/T6ezeZJ/JfhWh9qRZhU4xLp3lU73zjmL7384NICMfgA5U2\nHJqd6B1m1+RWkdpJJZkDLDLAHAgoA25UIH6jmAJojJe6Z6klPdYUp7/LVRwMhlcO\nPY+mcWcLBB/yRtvSKElqnZCCmNmkNey4ur0iKnxibHy7oTfHQ4SNreOZ3ZoLOBCQ\niF2+pRq9AgMBAAECggEACGGncrhIgQ9L9zepTfY1Z5DmswzlZfoKJwbYhIRNohyo\nNkZ11P0iEjOZj6CcybT2ZgdxDdPM+IiN46QrrYl1lvMjkdE1bxjyUU4tQA5CkCiS\ny5Cor7IZifafKxHphWh+Ey8zElcith/1yWeGC+UNj4lc+RejGA4LJzibrlPSfY5O\n9XZziFjI0Ei6O0JejlvgpzniRWx/GR2sVfRYMEFIeTRP4VQOJhB40VaOz+rMcFZ7\nVWcxPsnxWzhiQl7jSI8TeF91v4n3ObvKMO6pSoeV+n0GZn8nAo3k4+CL22oS+jiJ\nXjdv7Ty/1nJ+kcuVejVoebTzMzcrkHywD0nf0nKJUQKBgQC/j9uXnZRddoNIC5VN\nuHsg7azCEqUGBqDtfC66eYckL6wn72xlXTSk9eDcelDOitkIRtyQO3rfpAgBLkta\n52YBoZnrVkc5qUzE/XNY1ol9RXGXHjAT41hS+YMlt1ixna3jKlcTXnP6/fcRIXwN\njRYcg5ghq4BU6gXa7ml0efcwMQKBgQC7TGPTh1r5RE+csAKe0NEpPmHDnx9/cgiZ\nrKhiL1qr8cR6X3nAfMswxyqinYrMMkKzydrXGwJcraQGJNUMnb1wczBw3N08wlxG\nqDVF55NbkXsG7LXt6yDMT1Zt/0PamorfdUl6M36FwYQZfnkEKPkwP/HvtWo73Ejr\nxpsCZRpcTQKBgGTKGi3xuUhbvMNBZPMZp58xrn4GNVGwVyfc+PEgVByUAK6aaJwE\nqFhLDl6y9KVXpM2YCyDQStw0gAmvA/L2YVNmZYfPQ3ZxP3U+LVyGtJx210c9mOo9\n6hDuOzSy/TWm0wD9HAeS4M8kpMQcrBqtKv8htf+xLrp+z0nbuxyPt6FhAoGAESCe\nLFS/W/YENbbetnkv906OEB53+eaKpMrmJvxflUOpjOs4LgL7bb14x6ASdjPOZ7R4\nv5znrWPvkORDW0D9dtAByQBZdtnrKEbvgDH6BrCaCHsS8+vBdw12QN7L7KyfMGoA\nSx10X3TfyJkJJfk9OE1QqXP0f/7X6D+kIybtry0CgYBe7+Ut40xPmM+v2ktc5j0U\nI+gQZrbHJsNRo4Jfa7aRNL2XEIGhx9U38ggULfUeij/sq0JWKgWJU+ciLNW06dX2\njF033R5MDHTnOOWGFXhZbPJZy5LOoKtH/sZo5wN6TRXGsx2ASEf6Ram64cVI5T77\nIIFqwqHHmtBxMyIoV6Jc+A==\n-----END PRIVATE KEY-----\n',
  client_email: '<EMAIL>',
  client_id: '108829980583547471756',
  auth_uri: 'https://accounts.google.com/o/oauth2/auth',
  token_uri: 'https://oauth2.googleapis.com/token',
  auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
  client_x509_cert_url:
    'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-lseai%40qharmony-dev.iam.gserviceaccount.com',
  universe_domain: 'googleapis.com',
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});
export const auth = admin.auth();
