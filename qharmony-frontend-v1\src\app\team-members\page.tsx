"use client";

import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  Typography,
  Button,
  Avatar,
  Divider,
  IconButton,
  Dialog,
  DialogTitle,
  CircularProgress,
  DialogContent,
  DialogContentText,
  TextField,
  DialogActions,
} from "@mui/material";
import withSidebar from "@/components/withSidebar";
import theme from "@/theme";
import AddTeamMemberDialog from "./AddTeamMemberDialogue";
import {
  enableEmployee,
  getCompanyTeamMembers,
  offboardEmployee,
  sendLoginLinkToEmployee,
} from "@/middleware/company_middleware";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useAppSelector } from "@/redux/hooks";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";

// Colors for avatars
const colors = [
  "#FFB6C1",
  "#FF6347",
  "#FFD700",
  "#90EE90",
  "#00CED1",
  "#1E90FF",
  "#BA55D3",
];

const getInitials = (name: string) => {
  const [firstName, lastName] = name.split(" ");
  return `${firstName[0]}${lastName ? lastName[0] : ""}`;
};

const getAvatarColor = (index: number) => colors[index % colors.length];

const TeamMembers = () => {
  const dispatch = useDispatch();

  const currentUserId = useAppSelector((state) => state.user._id);

  const teamMembers = useSelector(
    (state: RootState) => state.company.companyTeamMembers,
  );

  const [openDialog, setOpenDialog] = useState(false);
  const [editMember, setEditMember] = useState(null); // New state for editing
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState(null);
  const [confirmText, setConfirmText] = useState("");
  const [isSendingLoginLink, setIsSendingLoginLink] = useState<string | null>(
    null,
  ); // New state for sending login link
  const [isDeleting, setIsDeleting] = useState(false); // New state for deletion
  const [isLoading, setIsLoading] = useState(true); // New state for loading
  const [isEnabling, setIsEnabling] = useState<string | null>(null); // New state for enabling

  const handleOpenDialog = () => setOpenDialog(true);
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditMember(null); // Reset edit member on close
  };

  const handleEdit = (member: any) => {
    console.log("Editing team member:", member);
    setEditMember(member); // Set the member to be edited
    handleOpenDialog();
  };

  const handleSendLoginLink = async (id: string, companyId: string) => {
    setIsSendingLoginLink(id); // Set loading state
    const response = await sendLoginLinkToEmployee(id, companyId);
    console.log("Response from sendLoginLinkToEmployee:", response);
    setIsSendingLoginLink(null); // Reset loading state
  };

  const handleAddMember = () => {
    handleOpenDialog();
  };
  const handleRemoveEmployee = (id: string, companyId: string) => {
    setMemberToDelete({ id: id, companyId: companyId } as any);
    setConfirmDialogOpen(true);
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
    setMemberToDelete(null);
    setConfirmText("");
  };

  const handleConfirmDelete = async () => {
    if (memberToDelete) {
      setIsDeleting(true); // Set loading state
      const offboarded = await offboardEmployee(
        (memberToDelete as any).id,
        (memberToDelete as any).companyId,
      );
      if (offboarded) {
        await getCompanyTeamMembers(dispatch);
        handleCloseConfirmDialog();
      } else {
        console.error("Failed to offboard employee");
      }
      setIsDeleting(false); // Reset loading state
    }
  };

  const handleEnable = async (id: string) => {
    setIsEnabling(id); // Set loading state
    const response = await enableEmployee(id, currentUserId);
    await getCompanyTeamMembers(dispatch);
    setIsEnabling(null); // Reset loading state
    handleCloseConfirmDialog();
  };

  useEffect(() => {
    const fetchTeamMembers = async () => {
      setIsLoading(true); // Set loading state
      await getCompanyTeamMembers(dispatch);
      setIsLoading(false); // Reset loading state
    };

    fetchTeamMembers();
  }, [dispatch]);

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6FA",
          p: 4,
          width: "100%",
          height: "95vh",
          overflow: "auto",
        }}
      >
        <Grid
          container
          alignItems="center"
          justifyContent="space-between"
          sx={{ mb: 2 }}
        >
          <Grid item>
            <Typography variant="h5">Team Members</Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddMember}
              sx={{
                backgroundColor: "#000000",
                textTransform: "none",
                borderRadius: "6px",
              }}
            >
              Add new member
            </Button>
          </Grid>
        </Grid>

        <Box
          sx={{
            bgcolor: "#ffffff",
            borderRadius: "12px",
            width: "100%",
            p: 2,
            boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
          }}
        >
          {isLoading ? (
            <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            teamMembers.map((member: any, index: any) => (
              <Box key={index}>
                <Grid container alignItems="center" spacing={2}>
                  {/* Avatar and Name */}
                  <Grid item xs={3} container alignItems="center">
                    <Avatar
                      sx={{
                        bgcolor: getAvatarColor(index),
                        color: "#ffffff",
                        width: 48,
                        height: 48,
                        fontSize: "1.2rem",
                        mr: 2, // Add space between Avatar and Name
                      }}
                    >
                      {getInitials(member.name)}
                    </Avatar>
                    <Box>
                      <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                        {member.name}
                      </Typography>
                      {!member.isActivated && (
                        <Typography variant="body2" color="error">
                          Pending Activation
                        </Typography>
                      )}
                    </Box>
                  </Grid>

                  {/* Role */}
                  <Grid item xs={3}>
                    <Typography variant="body2">{member.role}</Typography>
                  </Grid>

                  {/* Email */}
                  <Grid item xs={3}>
                    <Typography variant="body2">{member.email}</Typography>
                  </Grid>

                  {/* Actions */}
                  <Grid item xs={3} container justifyContent="flex-end">
                    {member.isDisabled ? (
                      <>
                        <Typography
                          variant="body2"
                          color="error"
                          sx={{ mr: 2 }}
                        >
                          Disabled
                        </Typography>
                        {isEnabling === member._id ? (
                          <CircularProgress size={24} />
                        ) : (
                          <Button
                            variant="contained"
                            onClick={() => handleEnable(member._id)}
                            sx={{
                              backgroundColor: "#f0f0f0",
                              borderRadius: "8px",
                              textTransform: "none",
                              color: "#000",
                              padding: "4px",
                              marginRight: 3,
                              boxShadow: "none",
                              border: "none",
                              "&:hover": {
                                backgroundColor: "#E0E0E0",
                                boxShadow: "none",
                              },
                            }}
                          >
                            Enable
                          </Button>
                        )}
                      </>
                    ) : (
                      <>
                        <Button
                          variant="contained"
                          onClick={() => handleEdit(member)}
                          sx={{
                            backgroundColor: "#f0f0f0",
                            borderRadius: "8px",
                            textTransform: "none",
                            color: "#000",
                            padding: "4px",
                            marginRight: 3,
                            boxShadow: "none",
                            border: "none",
                            "&:hover": {
                              backgroundColor: "#E0E0E0",
                              boxShadow: "none",
                            },
                          }}
                        >
                          Edit
                        </Button>
                        {isSendingLoginLink === member._id ? (
                          <CircularProgress size={24} />
                        ) : (
                          <Button
                            variant="contained"
                            onClick={() => handleSendLoginLink(member._id, member.companyId)}
                            sx={{
                              backgroundColor: "#f0f0f0",
                              borderRadius: "8px",
                              textTransform: "none",
                              color: "#000",
                              padding: "4px",
                              marginRight: 3,
                              boxShadow: "none",
                              border: "none",
                              "&:hover": {
                                backgroundColor: "#E0E0E0",
                                boxShadow: "none",
                              },
                            }}
                          >
                            Send Login Link
                          </Button>
                        )}
                        {isDeleting && (memberToDelete as any).id === member._id ? (
                          <CircularProgress size={24} />
                        ) : (
                          member._id !== currentUserId && (
                            <IconButton
                              onClick={() => handleRemoveEmployee(member._id, member.companyId)}
                              sx={{
                                backgroundColor: "#f0f0f0", // Light gray background
                                borderRadius: "8px", // Rounded corners
                                textTransform: "none", // No uppercase transformation
                                color: "#000", // Black text color
                                padding: "4px", // Similar padding to the chip
                                marginRight: 3,
                                boxShadow: "none", // Remove the default button shadow
                                border: "none", // No border
                                "&:hover": {
                                  backgroundColor: "#E0E0E0", // Slightly darker on hover
                                  boxShadow: "none", // Ensure no shadow on hover
                                },
                              }}
                            >
                              <HighlightOffIcon />
                            </IconButton>
                          )
                        )}
                      </>
                    )}
                  </Grid>
                </Grid>
                {index < teamMembers.length - 1 && <Divider sx={{ my: 2 }} />}
              </Box>
            ))
          )}
        </Box>
        <AddTeamMemberDialog
          open={openDialog}
          onClose={handleCloseDialog}
          member={editMember} // Pass the member to the dialog
        />

        <Dialog
          open={confirmDialogOpen}
          onClose={handleCloseConfirmDialog}
          PaperProps={{
            style: {
              borderRadius: "16px",
              boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
            },
          }}
        >
          <DialogTitle
            sx={{ fontWeight: "bold", fontSize: "1.5rem", color: "#000000" }}
          >
            Confirm Deletion
          </DialogTitle>
          <DialogContent>
            <DialogContentText
              sx={{ color: "#6c757d", fontSize: "1rem", mb: 2 }}
            >
              To confirm deletion, please type &quot;
              <strong style={{ color: "black" }}>remove employee</strong>&quot;
              in the box below.
            </DialogContentText>
            <TextField
              autoFocus
              fullWidth
              variant="outlined"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "12px",
                  backgroundColor: "#f9f9f9",
                },
              }}
            />
          </DialogContent>
          <DialogActions sx={{ padding: "16px" }}>
            <Button
              onClick={handleCloseConfirmDialog}
              sx={{
                color: "#6c757d",
                backgroundColor: "#ffffff",
                borderRadius: "8px",
                padding: "8px 16px",
                textTransform: "none",
                boxShadow: "none",
                "&:hover": {
                  backgroundColor: "#f0f0f0",
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmDelete}
              disabled={confirmText !== "remove employee" || isDeleting}
              sx={{
                color: "#ffffff",
                backgroundColor:
                  confirmText === "remove employee" ? "#1073ff" : "#9E9E9E",
                borderRadius: "8px",
                padding: "8px 16px",
                textTransform: "none",
                "&:hover": {
                  backgroundColor:
                    confirmText === "remove employee" ? "#0d62d3" : "#9E9E9E",
                },
              }}
            >
              {isDeleting ? (
                <CircularProgress size={24} sx={{ color: "#ffffff" }} />
              ) : (
                "Confirm"
              )}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ProtectedRoute>
  );
};

export default withSidebar(TeamMembers);
