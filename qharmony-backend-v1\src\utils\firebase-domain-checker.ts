import { URL } from 'url';
import logger from './logger';
import EnvService from '../services/env.service';

/**
 * Utility to help diagnose Firebase domain authorization issues
 */
export class FirebaseDomainChecker {
  
  /**
   * Extract domain from a URL
   */
  static extractDomain(url: string): string {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.hostname;
    } catch (error) {
      logger.error('Invalid URL provided:', url);
      return '';
    }
  }

  /**
   * Check if the current frontend URL domain might cause Firebase issues
   */
  static checkCurrentDomain(): void {
    const frontendUrl = EnvService.env().FRONTEND_BASE_URL;
    const domain = this.extractDomain(frontendUrl);
    
    logger.info(`🔍 Firebase Domain Check:`);
    logger.info(`   Frontend URL: ${frontendUrl}`);
    logger.info(`   Extracted Domain: ${domain}`);
    
    // Known working domains (update this list based on your Firebase config)
    const knownWorkingDomains = [
      'localhost',
      'app.benosphere.com',
      'test.benosphere.com',
      // Add other known working domains here
    ];
    
    if (knownWorkingDomains.includes(domain)) {
      logger.info(`✅ Domain '${domain}' is likely authorized in Firebase`);
    } else {
      logger.warn(`⚠️  Domain '${domain}' may not be authorized in Firebase`);
      logger.warn(`   If you get "Domain not whitelisted" errors, add this domain to:`);
      logger.warn(`   Firebase Console → Authentication → Settings → Authorized domains`);
      logger.warn(`   Recommended: Add '*.benosphere.com' for all subdomains`);
    }
  }

  /**
   * Log helpful information when Firebase domain errors occur
   */
  static logDomainError(error: any, attemptedUrl?: string): void {
    logger.error('🚨 Firebase Domain Authorization Error:');
    logger.error(`   Error: ${error.message || error}`);
    
    if (attemptedUrl) {
      const domain = this.extractDomain(attemptedUrl);
      logger.error(`   Attempted URL: ${attemptedUrl}`);
      logger.error(`   Domain: ${domain}`);
    }
    
    logger.error('📋 To fix this:');
    logger.error('   1. Go to Firebase Console: https://console.firebase.google.com/');
    logger.error('   2. Select project: qharmony-dev');
    logger.error('   3. Navigate to: Authentication → Settings → Authorized domains');
    logger.error('   4. Add domain or use wildcard: *.benosphere.com');
  }

  /**
   * Get recommendations for Firebase domain configuration
   */
  static getRecommendations(): string[] {
    return [
      'localhost (for development)',
      '*.benosphere.com (wildcard for all subdomains)',
      'Or add specific domains:',
      '  - app.benosphere.com',
      '  - test.benosphere.com', 
      '  - staging.benosphere.com',
      '  - admin.benosphere.com'
    ];
  }
}
