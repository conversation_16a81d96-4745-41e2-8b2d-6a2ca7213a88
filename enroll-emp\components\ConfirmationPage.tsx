import React from 'react';
import { CheckCircle, Download, Mail } from 'lucide-react';
import { BotQuestion } from './BotQuestion';

export const ConfirmationPage = () => {
  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <BotQuestion 
        question="🎉 Congratulations! Your enrollment is complete!"
        context="Your benefits will be active starting January 1, 2025. You should receive confirmation documents within 24 hours."
      />

      <div className="bg-white rounded-lg border shadow-sm p-6 text-center">
        <div className="mb-6">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-green-600 mb-2">Enrollment Complete!</h3>
          <p className="text-gray-600">Your 2025 benefits have been successfully enrolled.</p>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <h4 className="font-semibold text-green-800 mb-2">What happens next?</h4>
          <ul className="text-sm text-green-700 space-y-1">
            <li>✓ Confirmation email sent to your work email</li>
            <li>✓ Benefits cards will be mailed to your home address</li>
            <li>✓ Coverage begins January 1, 2025</li>
            <li>✓ First payroll deduction starts with your first 2025 paycheck</li>
          </ul>
        </div>

        <div className="flex gap-4 justify-center">
          <button className="flex items-center gap-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
            <Download className="w-4 h-4" />
            Download Summary
          </button>
          <button className="flex items-center gap-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            <Mail className="w-4 h-4" />
            Email Summary
          </button>
        </div>

        <div className="mt-6 pt-4 border-t text-sm text-gray-600">
          <p>Questions about your benefits? Contact <NAME_EMAIL> or call (555) 123-4567</p>
        </div>
      </div>
    </div>
  );
};
