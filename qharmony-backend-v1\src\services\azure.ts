import { BlobServiceClient, ContainerClient, StorageSharedKeyCredential } from '@azure/storage-blob';
import { BlobSASPermissions, generateBlobSASQueryParameters, SASProtocol } from '@azure/storage-blob';

import logger from '../utils/logger';

class AzureBlobService {
  private static blobServiceClient: BlobServiceClient;

  static init(): void {
    const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || '';

    if (!connectionString) {
      throw new Error('Azure Storage connection string is missing.');
    }

    this.blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);

    logger.info('Azure Blob Service initialized using the connection string.');
  }
  
  static async upload(containerName: string, blobName: string, data: Buffer | string, contentType: string): Promise<string> {
    const containerClient = this.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
  
    await blockBlobClient.upload(data, Buffer.byteLength(data), {
      blobHTTPHeaders: {
        blobContentType: contentType, // Set the Content-Type
      },
    });
    logger.info(`Uploaded blob: ${blobName} to container: ${containerName} with Content-Type: ${contentType}`);
    return blockBlobClient.url
  }
  

  static async getAllObjects(containerName: string): Promise<string[]> {
    const containerClient = this.getContainerClient(containerName);
    const blobs: string[] = [];
    for await (const blob of containerClient.listBlobsFlat()) {
      blobs.push(blob.name);
    }
    logger.info(`Retrieved ${blobs.length} blobs from container: ${containerName}`);
    return blobs;
  }

  static async getObject(containerName: string, blobName: string): Promise<Buffer> {
    const containerClient = this.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(blobName);
    const downloadBlockBlobResponse = await blobClient.download(0);

    const downloadedData = await this.streamToBuffer(downloadBlockBlobResponse.readableStreamBody!);
    logger.info(`Retrieved blob: ${blobName} from container: ${containerName}`);
    return downloadedData;
  }

  static async deleteBlob(containerName: string, blobName: string): Promise<void> {
    const containerClient = this.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(blobName);

    await blobClient.delete();
    logger.info(`Deleted blob: ${blobName} from container: ${containerName}`);
  }

  static async createContainer(containerName: string): Promise<void> {
    const containerClient = this.getContainerClient(containerName);
    try {
      await containerClient.create();
      logger.info(`Container "${containerName}" created successfully.`);
    } catch (error: any) {
      if (error.statusCode === 409) {
        logger.warn(`Container "${containerName}" already exists.`);
      } else {
        logger.error('Error creating container:', error.message);
        throw error;
      }
    }
  }

  static async containerExists(containerName: string): Promise<boolean> {
    const containerClient = this.getContainerClient(containerName);
    try {
      await containerClient.getProperties();
      return true;
    } catch (error: any) {
      if (error.statusCode === 404) {
        return false;
      }
      throw error;
    }
  }

  static async deleteContainer(containerName: string): Promise<void> {
    const containerClient = this.getContainerClient(containerName);
    try {
      await containerClient.delete();
      logger.info(`Container "${containerName}" deleted successfully.`);
    } catch (error: any) {
      if (error.statusCode === 404) {
        logger.warn(`Container "${containerName}" does not exist.`);
      } else {
        logger.error('Error deleting container:', error.message);
        throw error;
      }
    }
  }

    static async generatePresignedUrl(
    containerName: string,
    blobName: string,
    expiryMinutes: number
    ): Promise<string> {
    const containerClient = this.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(blobName);

    const startsOn = new Date();
    const expiresOn = new Date(new Date().valueOf() + expiryMinutes * 60 * 1000);

    // Define permissions using BlobSASPermissions
    const permissions = new BlobSASPermissions();
    permissions.write = true; // Grant read permission

    // Generate SAS token
    const sasToken = generateBlobSASQueryParameters({
        containerName: containerName,
        blobName: blobName,
        startsOn,
        expiresOn,
        permissions: permissions,
        // protocol: SASProtocol.Https, // Optional: Enforce HTTPS
    }, this.blobServiceClient.credential as StorageSharedKeyCredential).toString();

    // Construct the full URL with the SAS token
    const sasUrl = `${blobClient.url}?${sasToken}`;
    logger.info(`Generated presigned URL for blob: ${blobName} in container: ${containerName}`);
    return sasUrl;
    }

    static async getObjectAttributes(containerName: string, blobName: string) {
        const containerClient = this.blobServiceClient.getContainerClient(containerName);
        const blobClient = containerClient.getBlobClient(blobName);
        try {
          const properties = await blobClient.getProperties();
          return properties;
        } catch (error) {
          throw new Error(`Failed to retrieve blob properties: ${error}`);
        }
      }


  public static getContainerClient(containerName: string): ContainerClient {
    return this.blobServiceClient.getContainerClient(containerName);
  }

  public static async generateViewableImageURL(containerName: string, blobName: string, expiryMinutes: number): Promise<string> {
    const containerClient = this.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(blobName);
  
    const startsOn = new Date(new Date().valueOf() - 5 * 60 * 1000); // 5 minutes in the past
    const expiresOn = new Date(new Date().valueOf() + expiryMinutes * 60 * 1000);
  
    const permissions = new BlobSASPermissions();
    permissions.read = true; // Allow read access
  
    const sasToken = generateBlobSASQueryParameters(
      {
        containerName,
        blobName,
        startsOn,
        expiresOn,
        permissions,
        protocol: SASProtocol.Https,
      },
      this.blobServiceClient.credential as StorageSharedKeyCredential
    ).toString();
  
    return `${blobClient.url}?${sasToken}`; // Generate the full SAS URL
  }

  private static async streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks: Uint8Array[] = [];
      readableStream.on('data', (chunk) => chunks.push(chunk));
      readableStream.on('end', () => resolve(Buffer.concat(chunks)));
      readableStream.on('error', reject);
    });
  }
}

export default AzureBlobService;
