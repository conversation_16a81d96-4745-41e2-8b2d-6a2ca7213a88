"use client";

import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  IconButton,
  CircularProgress,
  Typography,
  Box,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Card,
  CardContent,
  CardActions,
  Chip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { RootState } from "@/redux/store";
import { getUserDetails, updateUser } from "@/middleware/company_middleware";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface DependentFormData {
  _id?: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  relationship: string;
  ssn?: string;
  isStudent?: boolean;
  isDisabled?: boolean;
  isActive?: boolean;
}

interface EnhancedEditProfileDialogProps {
  open: boolean;
  onClose: () => void;
}

const EnhancedEditProfileDialog: React.FC<EnhancedEditProfileDialogProps> = ({
  open,
  onClose,
}) => {
  const dispatch = useDispatch();
  const userId = useSelector((state: RootState) => state.user._id);
  const userDetails = useSelector((state: RootState) => state.user.userProfile);
  
  // Tab state
  const [tabValue, setTabValue] = useState(0);
  
  // Basic Information
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [department, setDepartment] = useState("");
  const [title, setTitle] = useState("");
  
  // Employee Demographics
  const [dateOfBirth, setDateOfBirth] = useState("");
  const [hireDate, setHireDate] = useState("");
  const [annualSalary, setAnnualSalary] = useState("");
  const [employeeClassType, setEmployeeClassType] = useState("");
  const [workSchedule, setWorkSchedule] = useState("");
  
  // Personal Identification
  const [ssn, setSsn] = useState("");
  const [employeeId, setEmployeeId] = useState("");
  
  // Address Information
  const [street1, setStreet1] = useState("");
  const [street2, setStreet2] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [country, setCountry] = useState("US");
  
  // Mailing Address (if different)
  const [mailingStreet1, setMailingStreet1] = useState("");
  const [mailingStreet2, setMailingStreet2] = useState("");
  const [mailingCity, setMailingCity] = useState("");
  const [mailingState, setMailingState] = useState("");
  const [mailingZipCode, setMailingZipCode] = useState("");
  const [mailingCountry, setMailingCountry] = useState("US");
  
  // Emergency Contact
  const [emergencyName, setEmergencyName] = useState("");
  const [emergencyRelationship, setEmergencyRelationship] = useState("");
  const [emergencyPhone, setEmergencyPhone] = useState("");
  const [emergencyEmail, setEmergencyEmail] = useState("");
  
  // Employment Details
  const [workLocation, setWorkLocation] = useState("");

  // Dependents Management
  const [dependents, setDependents] = useState<DependentFormData[]>([]);
  const [showDependentForm, setShowDependentForm] = useState(false);
  const [editingDependent, setEditingDependent] = useState<DependentFormData | null>(null);
  const [dependentForm, setDependentForm] = useState<DependentFormData>({
    firstName: "",
    middleName: "",
    lastName: "",
    gender: "",
    dateOfBirth: "",
    relationship: "",
    ssn: "",
    isStudent: false,
    isDisabled: false,
    isActive: true,
  });

  const [successMessage, setSuccessMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Dependent management functions
  const handleAddDependent = () => {
    setEditingDependent(null);
    setDependentForm({
      firstName: "",
      middleName: "",
      lastName: "",
      gender: "",
      dateOfBirth: "",
      relationship: "",
      ssn: "",
      isStudent: false,
      isDisabled: false,
      isActive: true,
    });
    setShowDependentForm(true);
  };

  const handleEditDependent = (dependent: DependentFormData) => {
    setEditingDependent(dependent);
    // If editing an existing dependent that might have old name format, parse it
    if (!dependent.firstName && (dependent as any).name) {
      const nameParts = (dependent as any).name.split(" ");
      const firstName = nameParts[0] || "";
      const lastName = nameParts.length > 1 ? nameParts[nameParts.length - 1] : "";
      const middleName = nameParts.length > 2 ? nameParts.slice(1, -1).join(" ") : "";
      setDependentForm({
        ...dependent,
        firstName,
        middleName,
        lastName
      });
    } else {
      setDependentForm({ ...dependent });
    }
    setShowDependentForm(true);
  };

  const handleSaveDependent = () => {
    if (!dependentForm.firstName || !dependentForm.lastName || !dependentForm.dateOfBirth || !dependentForm.relationship) {
      alert("Please fill in all required fields (First Name, Last Name, Date of Birth, Relationship)");
      return;
    }

    if (editingDependent) {
      // Update existing dependent
      setDependents(prev => prev.map(dep =>
        dep._id === editingDependent._id ? { ...dependentForm } : dep
      ));
    } else {
      // Add new dependent
      const newDependent = {
        ...dependentForm,
        _id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Temporary ID for frontend
      };
      setDependents(prev => [...prev, newDependent]);
    }

    setShowDependentForm(false);
    setEditingDependent(null);
  };

  const handleDeleteDependent = (dependentId: string) => {
    // Use HTML confirm dialog instead of window.confirm
    const confirmDelete = window.confirm("Are you sure you want to remove this dependent?");
    if (confirmDelete) {
      setDependents(prev => prev.filter(dep => dep._id !== dependentId));
    }
  };

  const handleCancelDependentForm = () => {
    setShowDependentForm(false);
    setEditingDependent(null);
  };

  useEffect(() => {
    if (userDetails) {
      // Basic Information
      const nameParts = userDetails.name.split(" ");
      setFirstName(nameParts[0] || "");
      setLastName(nameParts.slice(1).join(" ") || "");
      setEmail(userDetails.email || "");
      setPhoneNumber(userDetails.details?.phoneNumber || "");
      setDepartment(userDetails.details?.department || "");
      setTitle(userDetails.details?.title || "");
      
      // Employee Demographics
      setDateOfBirth(userDetails.details?.dateOfBirth ? new Date(userDetails.details.dateOfBirth).toISOString().split('T')[0] : "");
      setHireDate(userDetails.details?.hireDate ? new Date(userDetails.details.hireDate).toISOString().split('T')[0] : "");
      setAnnualSalary(userDetails.details?.annualSalary?.toString() || "");
      setEmployeeClassType(userDetails.details?.employeeClassType || "");
      setWorkSchedule(userDetails.details?.workSchedule || "");
      
      // Personal Identification
      setSsn(userDetails.details?.ssn || "");
      setEmployeeId(userDetails.details?.employeeId || "");
      
      // Address Information
      setStreet1(userDetails.details?.address?.street1 || "");
      setStreet2(userDetails.details?.address?.street2 || "");
      setCity(userDetails.details?.address?.city || "");
      setState(userDetails.details?.address?.state || "");
      setZipCode(userDetails.details?.address?.zipCode || "");
      setCountry(userDetails.details?.address?.country || "US");
      
      // Mailing Address
      setMailingStreet1(userDetails.details?.mailingAddress?.street1 || "");
      setMailingStreet2(userDetails.details?.mailingAddress?.street2 || "");
      setMailingCity(userDetails.details?.mailingAddress?.city || "");
      setMailingState(userDetails.details?.mailingAddress?.state || "");
      setMailingZipCode(userDetails.details?.mailingAddress?.zipCode || "");
      setMailingCountry(userDetails.details?.mailingAddress?.country || "US");
      
      // Emergency Contact
      setEmergencyName(userDetails.details?.emergencyContact?.name || "");
      setEmergencyRelationship(userDetails.details?.emergencyContact?.relationship || "");
      setEmergencyPhone(userDetails.details?.emergencyContact?.phoneNumber || "");
      setEmergencyEmail(userDetails.details?.emergencyContact?.email || "");
      
      // Employment Details
      setWorkLocation(userDetails.details?.workLocation || "");

      // Dependents
      if (userDetails.details?.dependents && Array.isArray(userDetails.details.dependents)) {
        const formattedDependents = userDetails.details.dependents.map((dep: any, index: number) => {
          // Parse existing name into first, middle, last
          const nameParts = (dep.name || "").split(" ");
          const firstName = nameParts[0] || "";
          const lastName = nameParts.length > 1 ? nameParts[nameParts.length - 1] : "";
          const middleName = nameParts.length > 2 ? nameParts.slice(1, -1).join(" ") : "";

          return {
            _id: dep._id || `existing_${index}_${Date.now()}`,
            firstName,
            middleName,
            lastName,
            gender: dep.gender || "",
            dateOfBirth: dep.dateOfBirth ? new Date(dep.dateOfBirth).toISOString().split('T')[0] : "",
            relationship: dep.relationship || "",
            ssn: dep.ssn || "",
            isStudent: dep.isStudent || false,
            isDisabled: dep.isDisabled || false,
            isActive: dep.isActive !== false, // Default to true if not specified
          };
        });
        console.log("Loading existing dependents:", formattedDependents);
        setDependents(formattedDependents);
      } else {
        console.log("No existing dependents found or dependents is not an array");
        setDependents([]);
      }
    }
  }, [userDetails]);

  const handleUpdateProfile = async () => {
    setLoading(true);

    try {
      // Start with basic required fields only
      const updatedUser: any = {
        name: `${firstName} ${lastName}`.trim(),
        email: email,
        phoneNumber: phoneNumber || "",
        department: department || "",
        title: title || "",
      };

      // Only add optional fields if they have values
      if (dateOfBirth) {
        updatedUser.dateOfBirth = new Date(dateOfBirth).toISOString();
      }

      if (hireDate) {
        updatedUser.hireDate = new Date(hireDate).toISOString();
      }

      if (annualSalary && !isNaN(parseFloat(annualSalary))) {
        updatedUser.annualSalary = parseFloat(annualSalary);
      }

      if (employeeClassType) {
        updatedUser.employeeClassType = employeeClassType;
      }

      if (workSchedule) {
        updatedUser.workSchedule = workSchedule;
      }

      if (ssn) {
        updatedUser.ssn = ssn;
      }

      if (employeeId) {
        updatedUser.employeeId = employeeId;
      }

      if (workLocation) {
        updatedUser.workLocation = workLocation;
      }

      // Only include address if we have the required fields
      if (street1 && city && state && zipCode) {
        updatedUser.address = {
          street1,
          city,
          state,
          zipCode,
          country: country || "US",
        };

        if (street2) {
          updatedUser.address.street2 = street2;
        }
      }

      // Only include mailing address if it's different and has required fields
      if (mailingStreet1 && mailingCity && mailingState && mailingZipCode) {
        updatedUser.mailingAddress = {
          street1: mailingStreet1,
          city: mailingCity,
          state: mailingState,
          zipCode: mailingZipCode,
          country: mailingCountry || "US",
        };

        if (mailingStreet2) {
          updatedUser.mailingAddress.street2 = mailingStreet2;
        }
      }

      // Only include emergency contact if we have a name
      if (emergencyName) {
        updatedUser.emergencyContact = {
          name: emergencyName,
        };

        if (emergencyRelationship) {
          updatedUser.emergencyContact.relationship = emergencyRelationship;
        }

        if (emergencyPhone) {
          updatedUser.emergencyContact.phoneNumber = emergencyPhone;
        }

        if (emergencyEmail) {
          updatedUser.emergencyContact.email = emergencyEmail;
        }
      }

      // Always include dependents array (even if empty)
      updatedUser.dependents = dependents.map(dep => ({
        _id: dep._id && !dep._id.toString().startsWith('temp_') ? dep._id : undefined, // Remove temp IDs
        name: `${dep.firstName} ${dep.middleName ? dep.middleName + ' ' : ''}${dep.lastName}`.trim(),
        gender: dep.gender,
        dateOfBirth: new Date(dep.dateOfBirth).toISOString(),
        relationship: dep.relationship,
        ssn: dep.ssn || undefined,
        isStudent: dep.isStudent || false,
        isDisabled: dep.isDisabled || false,
        isActive: dep.isActive !== false, // Default to true
      }));

      console.log("Dependents being sent:", updatedUser.dependents);
      console.log("Current dependents state:", dependents);

      console.log("Sending update request with data:", updatedUser);
      console.log("Dependents in state before sending:", dependents);
      console.log("Formatted dependents for backend:", updatedUser.dependents);

      const response = await updateUser(dispatch, userId, updatedUser);

      if (response && response.status === 200) {
        await getUserDetails(dispatch, userId);
        setSuccessMessage("Profile updated successfully!");

        setTimeout(() => {
          setSuccessMessage("");
          onClose();
        }, 1500);
      } else {
        throw new Error(response?.data?.error || "Update failed");
      }
    } catch (error: any) {
      console.error("Profile update error:", error);
      console.error("Error response:", error.response?.data);
      console.error("Error status:", error.response?.status);

      let errorMessage = "Error updating profile. Please try again.";
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 400) {
        errorMessage = "Invalid data format. Please check all required fields.";
      } else if (error.response?.status === 403) {
        errorMessage = "You don't have permission to edit this profile.";
      }

      setSuccessMessage(errorMessage);

      setTimeout(() => {
        setSuccessMessage("");
      }, 3000);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        style: {
          borderRadius: "16px",
          boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
          padding: "5px",
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          fontWeight: "bold",
          fontSize: "1.5rem",
          pb: 1,
        }}
      >
        Edit Profile
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="profile tabs">
            <Tab label="Basic Info" />
            <Tab label="Employment" />
            <Tab label="Address" />
            <Tab label="Emergency Contact" />
            <Tab label="Dependents" />
          </Tabs>
        </Box>

        {/* Basic Information Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Department"
                value={department}
                onChange={(e) => setDepartment(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Job Title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date of Birth *"
                type="date"
                value={dateOfBirth}
                onChange={(e) => setDateOfBirth(e.target.value)}
                variant="outlined"
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="SSN"
                value={ssn}
                onChange={(e) => setSsn(e.target.value)}
                variant="outlined"
                placeholder="XXX-XX-XXXX"
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Employment Information Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Employee ID"
                value={employeeId}
                onChange={(e) => setEmployeeId(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hire Date *"
                type="date"
                value={hireDate}
                onChange={(e) => setHireDate(e.target.value)}
                variant="outlined"
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Annual Salary"
                type="number"
                value={annualSalary}
                onChange={(e) => setAnnualSalary(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth sx={{ mb: 2 }} required>
                <InputLabel>Employee Class Type *</InputLabel>
                <Select
                  value={employeeClassType}
                  onChange={(e) => setEmployeeClassType(e.target.value)}
                  label="Employee Class Type *"
                  required
                >
                  <MenuItem value="Full-Time">Full-Time</MenuItem>
                  <MenuItem value="Part-Time">Part-Time</MenuItem>
                  <MenuItem value="Contractor">Contractor</MenuItem>
                  <MenuItem value="Temporary">Temporary</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Work Schedule</InputLabel>
                <Select
                  value={workSchedule}
                  onChange={(e) => setWorkSchedule(e.target.value)}
                  label="Work Schedule"
                >
                  <MenuItem value="Full-Time">Full-Time</MenuItem>
                  <MenuItem value="Part-Time">Part-Time</MenuItem>
                  <MenuItem value="Remote">Remote</MenuItem>
                  <MenuItem value="Hybrid">Hybrid</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Work Location"
                value={workLocation}
                onChange={(e) => setWorkLocation(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Address Information Tab */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            Primary Address
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Street Address"
                value={street1}
                onChange={(e) => setStreet1(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Apartment, Suite, Unit (Optional)"
                value={street2}
                onChange={(e) => setStreet2(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="City"
                value={city}
                onChange={(e) => setCity(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="State"
                value={state}
                onChange={(e) => setState(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="ZIP Code"
                value={zipCode}
                onChange={(e) => setZipCode(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            Mailing Address (if different)
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Mailing Street Address"
                value={mailingStreet1}
                onChange={(e) => setMailingStreet1(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Apartment, Suite, Unit (Optional)"
                value={mailingStreet2}
                onChange={(e) => setMailingStreet2(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="City"
                value={mailingCity}
                onChange={(e) => setMailingCity(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="State"
                value={mailingState}
                onChange={(e) => setMailingState(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="ZIP Code"
                value={mailingZipCode}
                onChange={(e) => setMailingZipCode(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Emergency Contact Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Name"
                value={emergencyName}
                onChange={(e) => setEmergencyName(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Relationship</InputLabel>
                <Select
                  value={emergencyRelationship}
                  onChange={(e) => setEmergencyRelationship(e.target.value)}
                  label="Relationship"
                >
                  <MenuItem value="Spouse">Spouse</MenuItem>
                  <MenuItem value="Parent">Parent</MenuItem>
                  <MenuItem value="Child">Child</MenuItem>
                  <MenuItem value="Sibling">Sibling</MenuItem>
                  <MenuItem value="Friend">Friend</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Phone"
                value={emergencyPhone}
                onChange={(e) => setEmergencyPhone(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Email"
                value={emergencyEmail}
                onChange={(e) => setEmergencyEmail(e.target.value)}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Dependents Tab */}
        <TabPanel value={tabValue} index={4}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              Dependents
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddDependent}
              sx={{
                backgroundColor: "#000000",
                color: "#ffffff",
                borderRadius: "8px",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "#333333",
                },
              }}
            >
              Add Dependent
            </Button>
          </Box>

          {/* Dependents List - Horizontal Cards */}
          <Box sx={{ mb: 3 }}>
            {dependents.map((dependent) => (
              <Card
                key={dependent._id}
                sx={{
                  border: '1px solid #e0e0e0',
                  borderRadius: '8px',
                  mb: 1.5,
                  '&:hover': {
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    transition: 'box-shadow 0.2s ease-in-out'
                  }
                }}
              >
                <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    {/* Left side - Main info */}
                    <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                      {/* Name and relationship */}
                      <Box sx={{ mr: 4 }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.25, fontSize: '1.1rem' }}>
                          {`${dependent.firstName} ${dependent.middleName ? dependent.middleName + ' ' : ''}${dependent.lastName}`.trim()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                          {dependent.relationship}
                        </Typography>
                      </Box>

                      {/* Details */}
                      <Box sx={{ display: 'flex', gap: 3, alignItems: 'center', flex: 1 }}>
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontSize: '0.75rem', lineHeight: 1.2 }}>
                            Date of Birth
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.875rem', mt: 0.25 }}>
                            {new Date(dependent.dateOfBirth).toLocaleDateString()}
                          </Typography>
                        </Box>

                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontSize: '0.75rem', lineHeight: 1.2 }}>
                            Gender
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.875rem', mt: 0.25 }}>
                            {dependent.gender}
                          </Typography>
                        </Box>

                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontSize: '0.75rem', lineHeight: 1.2 }}>
                            Age
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.875rem', mt: 0.25 }}>
                            {Math.floor((new Date().getTime() - new Date(dependent.dateOfBirth).getTime()) / (1000 * 60 * 60 * 24 * 365))} years
                          </Typography>
                        </Box>
                      </Box>
                    </Box>

                    {/* Right side - Status chips and actions */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                      {/* Status chips */}
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        {dependent.isStudent && (
                          <Chip
                            label="Student"
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ height: '24px', fontSize: '0.75rem' }}
                          />
                        )}
                        {dependent.isDisabled && (
                          <Chip
                            label="Disabled"
                            size="small"
                            color="secondary"
                            variant="outlined"
                            sx={{ height: '24px', fontSize: '0.75rem' }}
                          />
                        )}
                        <Chip
                          label={dependent.isActive ? "Active" : "Inactive"}
                          size="small"
                          color={dependent.isActive ? "success" : "default"}
                          variant={dependent.isActive ? "filled" : "outlined"}
                          sx={{ height: '24px', fontSize: '0.75rem' }}
                        />
                      </Box>

                      {/* Action buttons */}
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleEditDependent(dependent)}
                          sx={{
                            backgroundColor: '#f5f5f5',
                            width: '32px',
                            height: '32px',
                            '&:hover': { backgroundColor: '#e0e0e0' }
                          }}
                        >
                          <EditIcon sx={{ fontSize: '16px' }} />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteDependent(dependent._id!)}
                          sx={{
                            backgroundColor: '#ffebee',
                            color: '#d32f2f',
                            width: '32px',
                            height: '32px',
                            '&:hover': { backgroundColor: '#ffcdd2' }
                          }}
                        >
                          <DeleteIcon sx={{ fontSize: '16px' }} />
                        </IconButton>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            ))}

            {dependents.length === 0 && (
              <Box sx={{
                textAlign: 'center',
                py: 6,
                border: '2px dashed #e0e0e0',
                borderRadius: '12px',
                backgroundColor: '#fafafa'
              }}>
                <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                  No dependents added yet
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Click &quot;Add Dependent&quot; to get started adding family members to your profile.
                </Typography>
              </Box>
            )}
          </Box>

          {/* Dependent Form Modal */}
          {showDependentForm && (
            <Box
              sx={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 9999,
              }}
            >
              <Card sx={{ width: '90%', maxWidth: '600px', maxHeight: '90vh', overflow: 'auto' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                    {editingDependent ? 'Edit Dependent' : 'Add New Dependent'}
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="First Name *"
                        value={dependentForm.firstName}
                        onChange={(e) => setDependentForm(prev => ({ ...prev, firstName: e.target.value }))}
                        variant="outlined"
                        required
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Middle Name (Optional)"
                        value={dependentForm.middleName}
                        onChange={(e) => setDependentForm(prev => ({ ...prev, middleName: e.target.value }))}
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Last Name *"
                        value={dependentForm.lastName}
                        onChange={(e) => setDependentForm(prev => ({ ...prev, lastName: e.target.value }))}
                        variant="outlined"
                        required
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Gender *</InputLabel>
                        <Select
                          value={dependentForm.gender}
                          onChange={(e) => setDependentForm(prev => ({ ...prev, gender: e.target.value }))}
                          label="Gender *"
                        >
                          <MenuItem value="Male">Male</MenuItem>
                          <MenuItem value="Female">Female</MenuItem>
                          <MenuItem value="Other">Other</MenuItem>
                          <MenuItem value="Prefer not to say">Prefer not to say</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Date of Birth *"
                        type="date"
                        value={dependentForm.dateOfBirth}
                        onChange={(e) => setDependentForm(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                        variant="outlined"
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <InputLabel>Relationship *</InputLabel>
                        <Select
                          value={dependentForm.relationship}
                          onChange={(e) => setDependentForm(prev => ({ ...prev, relationship: e.target.value }))}
                          label="Relationship *"
                        >
                          <MenuItem value="Spouse">Spouse</MenuItem>
                          <MenuItem value="Child">Child</MenuItem>
                          <MenuItem value="Domestic Partner">Domestic Partner</MenuItem>
                          <MenuItem value="Stepchild">Stepchild</MenuItem>
                          <MenuItem value="Adopted Child">Adopted Child</MenuItem>
                          <MenuItem value="Other">Other</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="SSN (Optional)"
                        value={dependentForm.ssn}
                        onChange={(e) => setDependentForm(prev => ({ ...prev, ssn: e.target.value }))}
                        variant="outlined"
                        placeholder="XXX-XX-XXXX"
                      />
                    </Grid>
                  </Grid>
                </CardContent>

                <CardActions sx={{ justifyContent: 'flex-end', p: 2 }}>
                  <Button
                    onClick={handleCancelDependentForm}
                    sx={{ color: '#666', textTransform: 'none' }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveDependent}
                    variant="contained"
                    sx={{
                      backgroundColor: '#000000',
                      color: '#ffffff',
                      textTransform: 'none',
                      '&:hover': {
                        backgroundColor: '#333333',
                      },
                    }}
                  >
                    {editingDependent ? 'Update' : 'Add'} Dependent
                  </Button>
                </CardActions>
              </Card>
            </Box>
          )}
        </TabPanel>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress />
          </Box>
        )}

        {successMessage && (
          <Box
            sx={{
              color: "green",
              p: 2,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <CheckCircleIcon sx={{ mr: 1 }} />
            {successMessage}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ padding: "16px" }}>
        <Button
          onClick={onClose}
          sx={{
            color: "#666",
            borderRadius: "12px",
            padding: "8px 24px",
            textTransform: "none",
            fontWeight: "bold",
            mr: 1,
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleUpdateProfile}
          sx={{
            color: "#ffffff",
            backgroundColor: "#000000",
            borderRadius: "12px",
            padding: "8px 24px",
            textTransform: "none",
            fontWeight: "bold",
            "&:hover": {
              backgroundColor: "#333333",
            },
          }}
          disabled={loading}
        >
          {loading ? "Saving..." : "Save Changes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EnhancedEditProfileDialog;

