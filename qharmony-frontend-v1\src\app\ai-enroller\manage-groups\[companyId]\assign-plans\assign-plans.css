.assign-plans-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  background: white;
  min-height: 100vh;
}

/* Progress Steps */
.progress-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  background: #e5e7eb;
  color: #6b7280;
  transition: all 0.2s ease;
}

.step.active .step-number {
  background: #7c3aed;
  color: white;
}

.step-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-align: center;
}

.step.active .step-label {
  color: #7c3aed;
}

.step-connector {
  width: 60px;
  height: 2px;
  background: #e5e7eb;
  margin: 0 1rem;
}

/* Page Header */
.page-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background: #f8fafc;
  color: #475569;
  border-color: #cbd5e1;
}

.header-content h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.header-content p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* Selected Summary */
.selected-summary {
  background: #dcfce7;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
}

.summary-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #166534;
  font-weight: 500;
}

/* Plan Categories */
.plan-category {
  margin-bottom: 2rem;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.category-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.plan-count {
  font-size: 14px;
  color: #6b7280;
}

/* Plans Grid */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1rem;
}

.plan-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.plan-card:hover {
  border-color: #7c3aed;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.plan-card.selected {
  border-color: #7c3aed;
  background: #faf5ff;
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.15);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.plan-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plan-icon {
  width: 24px;
  height: 24px;
  color: white;
}

.plan-icon.medical {
  background: #ef4444;
  border-radius: 8px;
  padding: 12px;
}

.plan-icon.dental {
  background: #3b82f6;
  border-radius: 8px;
  padding: 12px;
}

.plan-icon.vision {
  background: #10b981;
  border-radius: 8px;
  padding: 12px;
}

.plan-icon.ancillary {
  background: #f59e0b;
  border-radius: 8px;
  padding: 12px;
}

.selection-indicator {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.2s ease;
}

.plan-card.selected .selection-indicator {
  background: #7c3aed;
  border-color: #7c3aed;
  color: white;
}

.plan-content h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.plan-carrier {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.plan-type {
  font-size: 12px;
  color: #7c3aed;
  font-weight: 500;
  margin-bottom: 1rem;
}

.plan-description {
  font-size: 14px;
  color: #4b5563;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.plan-highlights {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 12px;
  color: #6b7280;
}

.highlight-item svg {
  color: #10b981;
  flex-shrink: 0;
}

/* Continue Section */
.continue-section {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.continue-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(90deg, #7206E6, #B54BFF);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.continue-btn:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(114, 6, 230, 0.3);
}

.continue-btn.disabled {
  background: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #7c3aed;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .assign-plans-page {
    padding: 1rem;
  }

  .progress-steps {
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .step-connector {
    width: 30px;
    margin: 0 0.5rem;
  }

  .step-label {
    font-size: 12px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
  }

  .category-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
