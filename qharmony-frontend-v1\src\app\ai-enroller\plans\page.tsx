'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  HiOutlineArrowLeft,
  HiOutlineSearch,
  HiOutlineEye,
  HiOutlineDuplicate,
  HiOutlineTrash,
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineQuestionMarkCircle,
  HiOutlineViewGrid,
  HiOutlineX,
  HiOutlinePlay,
  HiOutlinePause,
  HiOutlineFilter,
  HiOutlineRefresh,
  HiOutlineChevronLeft,
  HiOutlineChevronRight
} from 'react-icons/hi';
import {
  RiHealthBookLine,
  RiCalendarLine,
  RiMoneyDollarCircleLine,
  RiShieldCheckLine,
  RiFileListLine,
  RiSettings3Line
} from 'react-icons/ri';
import ProtectedRoute from '@/components/ProtectedRoute';
import { getPlans, getCarriers, type ApiResponse, type Plan } from '../create-plan/services/planApi';
import CreatePlanForm from '../manage-groups/company/[companyId]/plans/components/CreatePlanForm';
import AIEnrollerHeader from '../components/AIEnrollerHeader';
import './plans.css';

import { getApiBaseUrl, getUserId } from '../../../utils/env';

// API configuration
const API_BASE_URL = getApiBaseUrl();

const PlansPage: React.FC = () => {
  const router = useRouter();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [carrierFilter, setCarrierFilter] = useState('all');
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [carriers, setCarriers] = useState<any[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPlanModal, setShowPlanModal] = useState(false);
  const [editingPlan, setEditingPlan] = useState<Plan | null>(null);
  const [planAssignmentCounts, setPlanAssignmentCounts] = useState<Record<string, number>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Custom modal states
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmModalData, setConfirmModalData] = useState<{
    title: string;
    message: string;
    onConfirm: () => void;
    onCancel?: () => void;
  } | null>(null);
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [alertModalData, setAlertModalData] = useState<{
    title: string;
    message: string;
    onClose?: () => void;
  } | null>(null);
  const [showInputModal, setShowInputModal] = useState(false);
  const [inputModalData, setInputModalData] = useState<{
    title: string;
    fields: Array<{
      name: string;
      label: string;
      placeholder: string;
      defaultValue: string;
      required?: boolean;
    }>;
    onSubmit: (values: Record<string, string>) => void;
    onCancel?: () => void;
  } | null>(null);

  useEffect(() => {
    loadPlans();
  }, []);

  // Function to fetch assignment counts for all plans
  const loadPlanAssignmentCounts = async (planIds: string[]) => {
    try {
      const counts: Record<string, number> = {};

      // Fetch assignment counts for each plan
      await Promise.all(
        planIds.map(async (planId) => {
          try {
            const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments?planId=${planId}`, {
              headers: { 'user-id': getUserId() }
            });

            if (response.ok) {
              const result = await response.json();
              counts[planId] = result.count || 0;
            } else {
              counts[planId] = 0;
            }
          } catch (error) {
            console.error(`Error fetching assignment count for plan ${planId}:`, error);
            counts[planId] = 0;
          }
        })
      );

      setPlanAssignmentCounts(counts);
    } catch (error) {
      console.error('Error loading plan assignment counts:', error);
    }
  };

  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load both plans and carriers
      const [plansResult, carriersResult] = await Promise.all([
        getPlans(),
        getCarriers()
      ]);

      if (plansResult.success && plansResult.data) {
        const plans = plansResult.data.plans;
        setPlans(plans);

        // Calculate statistics
        const totalPlans = plans.length;
        const activePlans = plans.filter(p => p.status === 'Active').length;
        const recentPlans = plans.filter(p => {
          if (!p.createdAt) return false;
          const createdDate = new Date(p.createdAt);
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          return createdDate > weekAgo;
        });

        const plansByStatus = plans.reduce((acc: any, plan) => {
          const status = plan.status || 'Unknown';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, {});

        setStats({
          totalPlans,
          plansByStatus,
          recentPlans
        });

        // Load assignment counts for all plans
        const planIds = plans.map(plan => plan._id);
        loadPlanAssignmentCounts(planIds);
      } else {
        setError(plansResult.error || 'Failed to load plans');
      }

      // Load carriers for display purposes
      if (carriersResult.success && carriersResult.data) {
        setCarriers(carriersResult.data);
      }

    } catch (err) {
      setError('Failed to load plans');
      console.error('Error loading plans:', err);
    } finally {
      setLoading(false);
    }
  };

  const filteredPlans = plans.filter(plan => {
    const matchesSearch = plan.planName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (plan.description || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         plan.planCode?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter = filterType === 'all' ||
                         plan.planType?.toLowerCase() === filterType.toLowerCase() ||
                         (plan.status || '').toLowerCase() === filterType.toLowerCase();

    const matchesCarrier = carrierFilter === 'all' ||
                          plan.carrierId === carrierFilter;

    return matchesSearch && matchesFilter && matchesCarrier;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredPlans.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedPlans = filteredPlans.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setFilterType('all');
    setCarrierFilter('all');
    setCurrentPage(1);
  };

  // Custom modal helpers
  const showCustomAlert = (title: string, message: string, onClose?: () => void) => {
    setAlertModalData({ title, message, onClose });
    setShowAlertModal(true);
  };

  const showCustomConfirm = (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => {
    setConfirmModalData({ title, message, onConfirm, onCancel });
    setShowConfirmModal(true);
  };

  const closeAlertModal = () => {
    setShowAlertModal(false);
    if (alertModalData?.onClose) {
      alertModalData.onClose();
    }
    setAlertModalData(null);
  };

  const closeConfirmModal = () => {
    setShowConfirmModal(false);
    if (confirmModalData?.onCancel) {
      confirmModalData.onCancel();
    }
    setConfirmModalData(null);
  };

  const confirmAction = () => {
    if (confirmModalData?.onConfirm) {
      confirmModalData.onConfirm();
    }
    closeConfirmModal();
  };

  const showCustomInput = (
    title: string,
    fields: Array<{
      name: string;
      label: string;
      placeholder: string;
      defaultValue: string;
      required?: boolean;
    }>,
    onSubmit: (values: Record<string, string>) => void,
    onCancel?: () => void
  ) => {
    setInputModalData({ title, fields, onSubmit, onCancel });
    setShowInputModal(true);
  };

  const closeInputModal = () => {
    setShowInputModal(false);
    if (inputModalData?.onCancel) {
      inputModalData.onCancel();
    }
    setInputModalData(null);
  };

  const handleEditPlan = async (planId: string) => {
    try {
      // Check if plan can be edited
      const canEditResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/can-edit`, {
        headers: { 'user-id': getUserId() }
      });

      if (canEditResponse.ok) {
        const canEditResult = await canEditResponse.json();
        if (canEditResult.canEdit) {
          // Find the plan and open edit modal
          const plan = plans.find(p => p._id === planId);
          if (plan) {
            setEditingPlan(plan);
            setShowPlanModal(true);
          } else {
            showCustomAlert('Error', 'Plan not found');
          }
        } else {
          showCustomAlert('Cannot Edit Plan', canEditResult.message);
        }
      } else {
        showCustomAlert('Error', 'Error checking plan editability');
      }
    } catch (error) {
      console.error('Error checking plan editability:', error);
      showCustomAlert('Error', 'Error checking plan editability');
    }
  };

  const handleCopyPlan = async (planId: string) => {
    try {
      const plan = plans.find(p => p._id === planId);
      if (!plan) {
        showCustomAlert('Error', 'Plan not found');
        return;
      }

      // Show custom input modal for plan details
      showCustomInput(
        'Copy Plan',
        [
          {
            name: 'planName',
            label: 'Plan Name',
            placeholder: 'Enter name for the copied plan',
            defaultValue: `${plan.planName} (Copy)`,
            required: true
          },
          {
            name: 'planCode',
            label: 'Plan Code (Optional)',
            placeholder: 'Enter plan code for the copied plan',
            defaultValue: `${plan.planCode || ''}-COPY`,
            required: false
          }
        ],
        async (values) => {
          const newPlanName = values.planName;
          const newPlanCode = values.planCode;

          try {
            // Call duplicate API
            const duplicateResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/duplicate`, {
              method: 'POST',
              headers: {
                'user-id': getUserId(),
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                planName: newPlanName,
                planCode: newPlanCode || undefined
              })
            });

            if (duplicateResponse.ok) {
              const result = await duplicateResponse.json();
              showCustomAlert('Success', 'Plan copied successfully!');
              loadPlans(); // Reload the plans list
            } else {
              const errorData = await duplicateResponse.json();
              showCustomAlert('Error', `Error copying plan: ${errorData.error}`);
            }
          } catch (error) {
            console.error('Error copying plan:', error);
            showCustomAlert('Error', 'Error copying plan');
          }
        }
      );
    } catch (error) {
      console.error('Error copying plan:', error);
      showCustomAlert('Error', 'Error copying plan');
    }
  };

  const handleDeletePlan = async (planId: string) => {
    try {
      // Check if plan can be deleted
      const canDeleteResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/can-delete`, {
        headers: { 'user-id': getUserId() }
      });

      if (canDeleteResponse.ok) {
        const canDeleteResult = await canDeleteResponse.json();
        if (canDeleteResult.canDelete) {
          showCustomConfirm(
            'Delete Plan',
            'Are you sure you want to delete this plan? This action cannot be undone.',
            async () => {
            try {
              const deleteResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
                method: 'DELETE',
                headers: { 'user-id': getUserId() }
              });

              if (deleteResponse.ok) {
                showCustomAlert('Success', 'Plan deleted successfully!');
                loadPlans(); // Reload the plans list
              } else {
                const errorData = await deleteResponse.json();
                showCustomAlert('Error', `Error deleting plan: ${errorData.error || 'Unknown error'}`);
              }
            } catch (deleteError) {
              console.error('Error deleting plan:', deleteError);
              showCustomAlert('Error', 'Error deleting plan. Please try again.');
            }
            }
          );
        } else {
          // Show dependencies using correct endpoint
          const dependenciesResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/dependent-assignments`, {
            headers: { 'user-id': getUserId() }
          });

          if (dependenciesResponse.ok) {
            const dependencies = await dependenciesResponse.json();
            const assignmentsList = dependencies.dependentAssignments?.map((assignment: any) =>
              `Assignment ${assignment._id}`
            ).join(', ') || 'Unknown assignments';

            showCustomAlert('Cannot Delete Plan', `${canDeleteResult.message}\n\nThis plan is referenced by ${dependencies.count} assignment(s):\n${assignmentsList}`);
          } else {
            showCustomAlert('Cannot Delete Plan', canDeleteResult.message);
          }
        }
      } else {
        showCustomAlert('Error', 'Error checking plan dependencies');
      }
    } catch (error) {
      console.error('Error deleting plan:', error);
      showCustomAlert('Error', 'Error deleting plan');
    }
  };

  const handleActivatePlan = async (planId: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/activate`, {
        method: 'POST',
        headers: { 'user-id': getUserId() }
      });

      if (response.ok) {
        showCustomAlert('Success', 'Plan activated successfully!');
        loadPlans(); // Reload the plans list
      } else {
        const errorData = await response.json();
        showCustomAlert('Error', `Error activating plan: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error activating plan:', error);
      showCustomAlert('Error', 'Error activating plan. Please try again.');
    }
  };

  const handleDeactivatePlan = async (planId: string) => {
    try {
      showCustomConfirm(
        'Convert to Draft',
        'Are you sure you want to convert this plan to draft? It will no longer be available for new assignments.',
        async () => {
        const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/convert-to-draft`, {
          method: 'POST',
          headers: { 'user-id': getUserId() }
        });

        if (response.ok) {
          showCustomAlert('Success', 'Plan converted to draft successfully!');
          loadPlans(); // Reload the plans list
        } else {
          const errorData = await response.json();
          showCustomAlert('Error', `Error converting plan to draft: ${errorData.error || 'Unknown error'}`);
        }
        }
      );
    } catch (error) {
      console.error('Error converting plan to draft:', error);
      showCustomAlert('Error', 'Error converting plan to draft. Please try again.');
    }
  };

  // Helper function to get carrier name by ID
  const getCarrierName = (carrierId: string): string => {
    const carrier = carriers.find(c => c._id === carrierId);
    return carrier ? carrier.carrierName : 'Unknown Carrier';
  };

  // Handle plan modal submission
  const handlePlanSubmit = (plan: Plan) => {
    setShowPlanModal(false);
    setEditingPlan(null);
    loadPlans(); // Reload plans list (this will also reload assignment counts)
  };

  // Handle plan modal cancel
  const handlePlanCancel = () => {
    setShowPlanModal(false);
    setEditingPlan(null);
  };

  const headerActions = (
    <button className="create-btn" onClick={() => {
      setEditingPlan(null);
      setShowPlanModal(true);
    }} style={{
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '10px 16px',
      background: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '8px',
      fontSize: '14px',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'all 0.2s'
    }}>
      <HiOutlinePlus size={16} />
      Create Plan
    </button>
  );

  return (
    <ProtectedRoute>
      <div className="plans-wrapper">
        <AIEnrollerHeader
          title="Plan Management"
          showBackButton={true}
          backUrl="/ai-enroller"
          customActions={headerActions}
        />
        <div className="plans-page">

      {/* Statistics Cards */}
      {stats && (
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">
              <RiHealthBookLine size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-number">{stats.totalPlans}</div>
              <div className="stat-label">Total Plans</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon active">
              <RiCalendarLine size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-number">{stats.plansByStatus.Active || 0}</div>
              <div className="stat-label">Active Plans</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon recent">
              <RiMoneyDollarCircleLine size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-number">{stats.recentPlans.length}</div>
              <div className="stat-label">Recent Plans</div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filter */}
      <div className="search-filter-section">
        <div className="filter-icon">
          <HiOutlineSearch size={16} />
          <span>Search & Filter</span>
        </div>

        <div className="search-controls">
          <input
            type="text"
            placeholder="Search by plan name, code, or carrier type..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />

          <select
            className="status-filter"
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="draft">Draft</option>
            <option value="template">Template</option>
            <option value="archived">Archived</option>
          </select>

          <select
            className="carrier-filter"
            value={carrierFilter}
            onChange={(e) => setCarrierFilter(e.target.value)}
          >
            <option value="all">All Carriers</option>
            {carriers.map(carrier => (
              <option key={carrier._id} value={carrier._id}>
                {carrier.carrierName}
              </option>
            ))}
          </select>

          <button className="clear-filters-btn" onClick={handleClearFilters}>
            Clear Filters
          </button>
        </div>

        <div className="results-count">
          Showing {filteredPlans.length} of {plans.length} plans
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading plans...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="error-state">
          <p>Error: {error}</p>
          <button onClick={loadPlans} className="retry-btn">
            Retry
          </button>
        </div>
      )}

      {/* Plans Table */}
      {!loading && !error && (
        <div className="plans-table-container">
          {filteredPlans.length === 0 ? (
            <div className="empty-state">
              <RiShieldCheckLine size={48} />
              <h3>No Plans Found</h3>
              <p>
                {plans.length === 0
                  ? "You haven't created any plans yet. Create your first plan to get started."
                  : "No plans match your search criteria. Try adjusting your filters."
                }
              </p>
              <button
                className="create-first-plan-btn"
                onClick={() => router.push('/ai-enroller/create-plan')}
              >
                <HiOutlinePlus size={16} />
                Create Your First Plan
              </button>
            </div>
          ) : (
            <>
              <div className="table-header">
                <h3>Plans List</h3>
              </div>

              <div className="table-wrapper">
                <table className="plans-table">
                  <thead>
                    <tr>
                      <th>Plan Name</th>
                      <th>Plan Code</th>
                      <th>Coverage Type</th>
                      <th>Status</th>
                      <th>Groups</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedPlans.map(plan => (
                      <tr key={plan._id}>
                        <td className="plan-name-cell">
                          <div className="plan-name">{plan.planName}</div>
                        </td>
                        <td className="plan-code-cell">
                          <span className="plan-code-badge">{plan.planCode}</span>
                        </td>
                        <td className="carrier-type-cell">
                          <span className={`carrier-type-badge ${(plan.coverageSubTypes?.[0] || plan.coverageType)?.toLowerCase().replace(' ', '-')}`}>
                            {plan.coverageSubTypes?.[0] || plan.coverageType}
                          </span>
                        </td>
                        <td className="status-cell">
                          <span className={`status-badge ${(plan.status || 'unknown').toLowerCase()}`}>
                            {plan.status || 'Unknown'}
                          </span>
                        </td>
                        <td className="groups-cell">
                          <span className="groups-count">
                            {planAssignmentCounts[plan._id] !== undefined ? planAssignmentCounts[plan._id] : '...'}
                          </span>
                        </td>
                        <td className="actions-cell">
                          <div className="action-buttons">
                            <button
                              className="action-btn edit"
                              onClick={() => handleEditPlan(plan._id)}
                              title="Edit Plan"
                            >
                              <HiOutlinePencil size={16} />
                            </button>
                            <button
                              className="action-btn copy"
                              onClick={() => handleCopyPlan(plan._id)}
                              title="Copy Plan"
                            >
                              <HiOutlineDuplicate size={16} />
                            </button>
                            {plan.status === 'Active' ? (
                              <button
                                className="action-btn deactivate"
                                onClick={() => handleDeactivatePlan(plan._id)}
                                title="Convert to Draft"
                              >
                                <HiOutlinePause size={16} />
                              </button>
                            ) : (
                              <button
                                className="action-btn activate"
                                onClick={() => handleActivatePlan(plan._id)}
                                title="Activate Plan"
                              >
                                <HiOutlinePlay size={16} />
                              </button>
                            )}
                            <button
                              className="action-btn delete"
                              onClick={() => handleDeletePlan(plan._id)}
                              title="Delete Plan"
                            >
                              <HiOutlineTrash size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="pagination-container">
                  <div className="pagination-info">
                    Showing {startIndex + 1}-{Math.min(endIndex, filteredPlans.length)} of {filteredPlans.length} plans
                  </div>
                  <div className="pagination-controls">
                    <button
                      className="pagination-btn"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                      <button
                        key={page}
                        className={`pagination-btn ${page === currentPage ? 'active' : ''}`}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </button>
                    ))}
                    <button
                      className="pagination-btn"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Create/Edit Plan Modal */}
      {showPlanModal && (
        <div className="modal-overlay" onClick={handlePlanCancel}>
          <div className="modal-content plan-modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{editingPlan ? 'Edit Plan' : 'Create New Plan'}</h2>
              <button className="modal-close" onClick={handlePlanCancel}>
                <HiOutlineX size={20} />
              </button>
            </div>
            <div className="modal-body">
              <CreatePlanForm
                initialData={editingPlan}
                onSubmit={handlePlanSubmit}
                onCancel={handlePlanCancel}
                isModal={true}
              />
            </div>
          </div>
        </div>
      )}

      {/* Custom Alert Modal */}
      {showAlertModal && alertModalData && (
        <div className="modal-overlay" onClick={closeAlertModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{alertModalData.title}</h2>
              <button className="modal-close" onClick={closeAlertModal}>
                <HiOutlineX size={20} />
              </button>
            </div>
            <div className="modal-body">
              <p style={{ whiteSpace: 'pre-line' }}>{alertModalData.message}</p>
            </div>
            <div className="modal-footer">
              <button className="modal-btn primary" onClick={closeAlertModal}>
                OK
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom Confirm Modal */}
      {showConfirmModal && confirmModalData && (
        <div className="modal-overlay" onClick={closeConfirmModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{confirmModalData.title}</h2>
              <button className="modal-close" onClick={closeConfirmModal}>
                <HiOutlineX size={20} />
              </button>
            </div>
            <div className="modal-body">
              <p style={{ whiteSpace: 'pre-line' }}>{confirmModalData.message}</p>
            </div>
            <div className="modal-footer">
              <button className="modal-btn secondary" onClick={closeConfirmModal}>
                Cancel
              </button>
              <button className="modal-btn primary" onClick={confirmAction}>
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom Input Modal */}
      {showInputModal && inputModalData && (
        <div className="modal-overlay" onClick={closeInputModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{inputModalData.title}</h2>
              <button className="modal-close" onClick={closeInputModal}>
                <HiOutlineX size={20} />
              </button>
            </div>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const values: Record<string, string> = {};
              inputModalData.fields.forEach(field => {
                values[field.name] = formData.get(field.name) as string || '';
              });
              inputModalData.onSubmit(values);
              closeInputModal();
            }}>
              <div className="modal-body">
                {inputModalData.fields.map((field) => (
                  <div key={field.name} className="form-group" style={{ marginBottom: '1rem' }}>
                    <label htmlFor={field.name} style={{
                      display: 'block',
                      marginBottom: '0.5rem',
                      fontSize: '14px',
                      lineHeight: '21px',
                      fontWeight: '500',
                      color: '#374151'
                    }}>
                      {field.label}
                      {field.required && <span style={{ color: '#dc2626' }}>*</span>}
                    </label>
                    <input
                      type="text"
                      id={field.name}
                      name={field.name}
                      placeholder={field.placeholder}
                      defaultValue={field.defaultValue}
                      required={field.required}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '0.5rem',
                        fontSize: '14px',
                        lineHeight: '21px',
                        fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                      }}
                    />
                  </div>
                ))}
              </div>
              <div className="modal-footer">
                <button type="button" className="modal-btn secondary" onClick={closeInputModal}>
                  Cancel
                </button>
                <button type="submit" className="modal-btn primary">
                  Submit
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

        </div>
      </div>
    </ProtectedRoute>
  );
};

export default PlansPage;
