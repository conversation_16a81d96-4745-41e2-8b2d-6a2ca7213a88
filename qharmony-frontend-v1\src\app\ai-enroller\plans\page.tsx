'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  HiOutlineArrowLeft,
  HiOutlineSearch,
  HiOutlineEye,
  HiOutlineDuplicate,
  HiOutlineTrash,
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineQuestionMarkCircle,
  HiOutlineViewGrid,
  HiOutlineX,
  HiOutlinePlay,
  HiOutlinePause,
  HiOutlineFilter,
  HiOutlineRefresh,
  HiOutlineChevronLeft,
  HiOutlineChevronRight,
  HiOutlineClipboardList
} from 'react-icons/hi';
import {
  RiHealthBookLine,
  RiCalendarLine,
  RiMoneyDollarCircleLine,
  RiShieldCheckLine,
  RiFileListLine,
  RiSettings3Line
} from 'react-icons/ri';
import ProtectedRoute from '@/components/ProtectedRoute';
import { getPlans, getCarriers, type ApiResponse, type Plan } from '../create-plan/services/planApi';
import CreatePlanForm from '../manage-groups/company/[companyId]/plans/components/CreatePlanForm';
import EnrollmentHeader from '../employee-enrol/components/EnrollmentHeader';
import './plans.css';

import { getApiBaseUrl, getUserId } from '../../../utils/env';

// API configuration
const API_BASE_URL = getApiBaseUrl();

const PlansPage: React.FC = () => {
  const router = useRouter();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [carrierFilter, setCarrierFilter] = useState('all');
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [carriers, setCarriers] = useState<any[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPlanModal, setShowPlanModal] = useState(false);
  const [editingPlan, setEditingPlan] = useState<Plan | null>(null);
  const [planAssignmentCounts, setPlanAssignmentCounts] = useState<Record<string, number>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Custom modal states
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmModalData, setConfirmModalData] = useState<{
    title: string;
    message: string;
    onConfirm: () => void;
    onCancel?: () => void;
  } | null>(null);
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [alertModalData, setAlertModalData] = useState<{
    title: string;
    message: string;
    onClose?: () => void;
  } | null>(null);
  const [showInputModal, setShowInputModal] = useState(false);
  const [inputModalData, setInputModalData] = useState<{
    title: string;
    fields: Array<{
      name: string;
      label: string;
      placeholder: string;
      defaultValue: string;
      required?: boolean;
    }>;
    onSubmit: (values: Record<string, string>) => void;
    onCancel?: () => void;
  } | null>(null);

  useEffect(() => {
    loadPlans();
  }, []);

  // Function to fetch assignment counts for all plans
  const loadPlanAssignmentCounts = async (planIds: string[]) => {
    try {
      const counts: Record<string, number> = {};

      // Fetch assignment counts for each plan
      await Promise.all(
        planIds.map(async (planId) => {
          try {
            const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments?planId=${planId}`, {
              headers: { 'user-id': getUserId() }
            });

            if (response.ok) {
              const result = await response.json();
              counts[planId] = result.count || 0;
            } else {
              counts[planId] = 0;
            }
          } catch (error) {
            console.error(`Error fetching assignment count for plan ${planId}:`, error);
            counts[planId] = 0;
          }
        })
      );

      setPlanAssignmentCounts(counts);
    } catch (error) {
      console.error('Error loading plan assignment counts:', error);
    }
  };

  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load both plans and carriers
      const [plansResult, carriersResult] = await Promise.all([
        getPlans(),
        getCarriers()
      ]);

      if (plansResult.success && plansResult.data) {
        const plans = plansResult.data.plans;
        setPlans(plans);

        // Calculate statistics
        const totalPlans = plans.length;
        const activePlans = plans.filter(p => p.status === 'Active').length;
        const recentPlans = plans.filter(p => {
          if (!p.createdAt) return false;
          const createdDate = new Date(p.createdAt);
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          return createdDate > weekAgo;
        });

        const plansByStatus = plans.reduce((acc: any, plan) => {
          const status = plan.status || 'Unknown';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, {});

        setStats({
          totalPlans,
          plansByStatus,
          recentPlans
        });

        // Load assignment counts for all plans
        const planIds = plans.map(plan => plan._id);
        loadPlanAssignmentCounts(planIds);
      } else {
        setError(plansResult.error || 'Failed to load plans');
      }

      // Load carriers for display purposes
      if (carriersResult.success && carriersResult.data) {
        setCarriers(carriersResult.data);
      }

    } catch (err) {
      setError('Failed to load plans');
      console.error('Error loading plans:', err);
    } finally {
      setLoading(false);
    }
  };

  const filteredPlans = plans.filter(plan => {
    const matchesSearch = (plan.planName || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (plan.description || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (plan.planCode || '').toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter = filterType === 'all' ||
                         plan.planType?.toLowerCase() === filterType.toLowerCase() ||
                         (plan.status || '').toLowerCase() === filterType.toLowerCase();

    const matchesCarrier = carrierFilter === 'all' ||
                          plan.carrierId === carrierFilter;

    return matchesSearch && matchesFilter && matchesCarrier;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredPlans.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedPlans = filteredPlans.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setFilterType('all');
    setCarrierFilter('all');
    setCurrentPage(1);
  };

  // Custom modal helpers
  const showCustomAlert = (title: string, message: string, onClose?: () => void) => {
    setAlertModalData({ title, message, onClose });
    setShowAlertModal(true);
  };

  const showCustomConfirm = (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => {
    setConfirmModalData({ title, message, onConfirm, onCancel });
    setShowConfirmModal(true);
  };

  const closeAlertModal = () => {
    setShowAlertModal(false);
    if (alertModalData?.onClose) {
      alertModalData.onClose();
    }
    setAlertModalData(null);
  };

  const closeConfirmModal = () => {
    setShowConfirmModal(false);
    if (confirmModalData?.onCancel) {
      confirmModalData.onCancel();
    }
    setConfirmModalData(null);
  };

  const confirmAction = () => {
    if (confirmModalData?.onConfirm) {
      confirmModalData.onConfirm();
    }
    closeConfirmModal();
  };

  const showCustomInput = (
    title: string,
    fields: Array<{
      name: string;
      label: string;
      placeholder: string;
      defaultValue: string;
      required?: boolean;
    }>,
    onSubmit: (values: Record<string, string>) => void,
    onCancel?: () => void
  ) => {
    setInputModalData({ title, fields, onSubmit, onCancel });
    setShowInputModal(true);
  };

  const closeInputModal = () => {
    setShowInputModal(false);
    if (inputModalData?.onCancel) {
      inputModalData.onCancel();
    }
    setInputModalData(null);
  };

  const handleEditPlan = async (planId: string) => {
    try {
      // Check if plan can be edited
      const canEditResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/can-edit`, {
        headers: { 'user-id': getUserId() }
      });

      if (canEditResponse.ok) {
        const canEditResult = await canEditResponse.json();
        if (canEditResult.canEdit) {
          // Find the plan and open edit modal
          const plan = plans.find(p => p._id === planId);
          if (plan) {
            setEditingPlan(plan);
            setShowPlanModal(true);
          } else {
            showCustomAlert('Error', 'Plan not found');
          }
        } else {
          showCustomAlert('Cannot Edit Plan', canEditResult.message);
        }
      } else {
        showCustomAlert('Error', 'Error checking plan editability');
      }
    } catch (error) {
      console.error('Error checking plan editability:', error);
      showCustomAlert('Error', 'Error checking plan editability');
    }
  };

  const handleCopyPlan = async (planId: string) => {
    try {
      const plan = plans.find(p => p._id === planId);
      if (!plan) {
        showCustomAlert('Error', 'Plan not found');
        return;
      }

      // Show custom input modal for plan details
      showCustomInput(
        'Copy Plan',
        [
          {
            name: 'planName',
            label: 'Plan Name',
            placeholder: 'Enter name for the copied plan',
            defaultValue: `${plan.planName} (Copy)`,
            required: true
          },
          {
            name: 'planCode',
            label: 'Plan Code (Optional)',
            placeholder: 'Enter plan code for the copied plan',
            defaultValue: `${plan.planCode || ''}-COPY`,
            required: false
          }
        ],
        async (values) => {
          const newPlanName = values.planName;
          const newPlanCode = values.planCode;

          try {
            // Call duplicate API
            const duplicateResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/duplicate`, {
              method: 'POST',
              headers: {
                'user-id': getUserId(),
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                planName: newPlanName,
                planCode: newPlanCode || undefined
              })
            });

            if (duplicateResponse.ok) {
              const result = await duplicateResponse.json();
              showCustomAlert('Success', 'Plan copied successfully!');
              loadPlans(); // Reload the plans list
            } else {
              const errorData = await duplicateResponse.json();
              showCustomAlert('Error', `Error copying plan: ${errorData.error}`);
            }
          } catch (error) {
            console.error('Error copying plan:', error);
            showCustomAlert('Error', 'Error copying plan');
          }
        }
      );
    } catch (error) {
      console.error('Error copying plan:', error);
      showCustomAlert('Error', 'Error copying plan');
    }
  };

  const handleDeletePlan = async (planId: string) => {
    try {
      // Check if plan can be deleted
      const canDeleteResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/can-delete`, {
        headers: { 'user-id': getUserId() }
      });

      if (canDeleteResponse.ok) {
        const canDeleteResult = await canDeleteResponse.json();
        if (canDeleteResult.canDelete) {
          showCustomConfirm(
            'Delete Plan',
            'Are you sure you want to delete this plan? This action cannot be undone.',
            async () => {
            try {
              const deleteResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
                method: 'DELETE',
                headers: { 'user-id': getUserId() }
              });

              if (deleteResponse.ok) {
                showCustomAlert('Success', 'Plan deleted successfully!');
                loadPlans(); // Reload the plans list
              } else {
                const errorData = await deleteResponse.json();
                showCustomAlert('Error', `Error deleting plan: ${errorData.error || 'Unknown error'}`);
              }
            } catch (deleteError) {
              console.error('Error deleting plan:', deleteError);
              showCustomAlert('Error', 'Error deleting plan. Please try again.');
            }
            }
          );
        } else {
          // Show dependencies using correct endpoint
          const dependenciesResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/dependent-assignments`, {
            headers: { 'user-id': getUserId() }
          });

          if (dependenciesResponse.ok) {
            const dependencies = await dependenciesResponse.json();
            const assignmentsList = dependencies.dependentAssignments?.map((assignment: any) =>
              `Assignment ${assignment._id}`
            ).join(', ') || 'Unknown assignments';

            showCustomAlert('Cannot Delete Plan', `${canDeleteResult.message}\n\nThis plan is referenced by ${dependencies.count} assignment(s):\n${assignmentsList}`);
          } else {
            showCustomAlert('Cannot Delete Plan', canDeleteResult.message);
          }
        }
      } else {
        showCustomAlert('Error', 'Error checking plan dependencies');
      }
    } catch (error) {
      console.error('Error deleting plan:', error);
      showCustomAlert('Error', 'Error deleting plan');
    }
  };

  const handleActivatePlan = async (planId: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/activate`, {
        method: 'POST',
        headers: { 'user-id': getUserId() }
      });

      if (response.ok) {
        showCustomAlert('Success', 'Plan activated successfully!');
        loadPlans(); // Reload the plans list
      } else {
        const errorData = await response.json();
        showCustomAlert('Error', `Error activating plan: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error activating plan:', error);
      showCustomAlert('Error', 'Error activating plan. Please try again.');
    }
  };

  const handleDeactivatePlan = async (planId: string) => {
    try {
      showCustomConfirm(
        'Convert to Draft',
        'Are you sure you want to convert this plan to draft? It will no longer be available for new assignments.',
        async () => {
        const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/convert-to-draft`, {
          method: 'POST',
          headers: { 'user-id': getUserId() }
        });

        if (response.ok) {
          showCustomAlert('Success', 'Plan converted to draft successfully!');
          loadPlans(); // Reload the plans list
        } else {
          const errorData = await response.json();
          showCustomAlert('Error', `Error converting plan to draft: ${errorData.error || 'Unknown error'}`);
        }
        }
      );
    } catch (error) {
      console.error('Error converting plan to draft:', error);
      showCustomAlert('Error', 'Error converting plan to draft. Please try again.');
    }
  };

  // Helper function to get carrier name by ID
  const getCarrierName = (carrierId: string): string => {
    const carrier = carriers.find(c => c._id === carrierId);
    return carrier ? carrier.carrierName : 'Unknown Carrier';
  };

  // Handle plan modal submission
  const handlePlanSubmit = (plan: Plan) => {
    setShowPlanModal(false);
    setEditingPlan(null);
    loadPlans(); // Reload plans list (this will also reload assignment counts)
  };

  // Handle plan modal cancel
  const handlePlanCancel = () => {
    setShowPlanModal(false);
    setEditingPlan(null);
  };

  return (
    <ProtectedRoute>
      <div className="plans-wrapper">
        <EnrollmentHeader />

        <div className="plans-page" style={{ background: 'white', minHeight: '100vh' }}>
          {/* Plan Management Header */}
          <div style={{
            background: 'white',
            padding: '24px 0',
            borderBottom: '1px solid #E5E7EB'
          }}>
            <div style={{
              maxWidth: '95%',
              margin: '0 auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '0 2%'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  background: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlineClipboardList style={{ width: '18px', height: '18px', color: 'white' }} />
                </div>
                <div>
                  <h1 style={{
                    fontSize: '24px',
                    fontWeight: '600',
                    color: '#111827',
                    margin: 0
                  }}>
                    Plan Management
                  </h1>
                  <p style={{
                    fontSize: '14px',
                    color: '#6B7280',
                    margin: 0
                  }}>
                    Manage and view all insurance plans
                  </p>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '12px' }}>
                <button style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '10px 16px',
                  background: 'white',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  color: '#374151',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  <HiOutlineQuestionMarkCircle size={16} />
                  Ask Questions
                </button>
                <button style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '10px 16px',
                  background: 'white',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  color: '#374151',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  <HiOutlineViewGrid size={16} />
                  Dashboard
                </button>
                <button
                  onClick={() => setShowPlanModal(true)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '10px 16px',
                    background: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}
                >
                  <HiOutlinePlus size={16} />
                  Create New Plan
                </button>
              </div>
            </div>
          </div>

      {/* Statistics Cards */}
      {stats && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '16px',
          maxWidth: '95%',
          margin: '24px auto',
          padding: '0 2%'
        }}>
          <div style={{
            background: '#EFF6FF',
            border: '1px solid #DBEAFE',
            borderRadius: '12px',
            padding: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <div>
              <div style={{ fontSize: '14px', color: '#2563EB', fontWeight: '500', marginBottom: '4px' }}>
                Total Plans
              </div>
              <div style={{ fontSize: '32px', fontWeight: '700', color: '#1E40AF' }}>
                {stats.totalPlans}
              </div>
            </div>
            <div style={{
              width: '48px',
              height: '48px',
              background: '#2563EB',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <RiHealthBookLine style={{ width: '24px', height: '24px', color: 'white' }} />
            </div>
          </div>

          <div style={{
            background: '#F0FDF4',
            border: '1px solid #BBF7D0',
            borderRadius: '12px',
            padding: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <div>
              <div style={{ fontSize: '14px', color: '#16A34A', fontWeight: '500', marginBottom: '4px' }}>
                Active Plans
              </div>
              <div style={{ fontSize: '32px', fontWeight: '700', color: '#15803D' }}>
                {stats.plansByStatus.Active || 0}
              </div>
            </div>
            <div style={{
              width: '48px',
              height: '48px',
              background: '#16A34A',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <RiCalendarLine style={{ width: '24px', height: '24px', color: 'white' }} />
            </div>
          </div>

          <div style={{
            background: '#FEF3C7',
            border: '1px solid #FDE68A',
            borderRadius: '12px',
            padding: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <div>
              <div style={{ fontSize: '14px', color: '#D97706', fontWeight: '500', marginBottom: '4px' }}>
                Recent Plans
              </div>
              <div style={{ fontSize: '32px', fontWeight: '700', color: '#B45309' }}>
                {stats.recentPlans.length}
              </div>
            </div>
            <div style={{
              width: '48px',
              height: '48px',
              background: '#D97706',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <RiMoneyDollarCircleLine style={{ width: '24px', height: '24px', color: 'white' }} />
            </div>
          </div>
        </div>
      )}

      {/* Search and Filter */}
      <div style={{
        background: 'white',
        border: '1px solid #E5E7EB',
        borderRadius: '12px',
        padding: '24px',
        margin: '0 auto 24px',
        maxWidth: '95%',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginBottom: '16px'
        }}>
          <HiOutlineSearch style={{ width: '16px', height: '16px', color: '#6B7280' }} />
          <span style={{ fontSize: '16px', fontWeight: '500', color: '#374151' }}>Search & Filter</span>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px',
          marginBottom: '16px'
        }}>
          <input
            type="text"
            placeholder="Search by plan name, code, or carrier type..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{
              padding: '10px 12px',
              border: '1px solid #D1D5DB',
              borderRadius: '8px',
              fontSize: '14px',
              outline: 'none',
              transition: 'border-color 0.2s',
              gridColumn: 'span 2'
            }}
          />

          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            style={{
              padding: '10px 12px',
              border: '1px solid #D1D5DB',
              borderRadius: '8px',
              fontSize: '14px',
              outline: 'none',
              background: 'white'
            }}
          >
            <option value="all">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="draft">Draft</option>
            <option value="template">Template</option>
            <option value="archived">Archived</option>
          </select>

          <select
            value={carrierFilter}
            onChange={(e) => setCarrierFilter(e.target.value)}
            style={{
              padding: '10px 12px',
              border: '1px solid #D1D5DB',
              borderRadius: '8px',
              fontSize: '14px',
              outline: 'none',
              background: 'white'
            }}
          >
            <option value="all">All Carriers</option>
            {carriers.map(carrier => (
              <option key={carrier._id} value={carrier._id}>
                {carrier.carrierName}
              </option>
            ))}
          </select>

          <button
            onClick={handleClearFilters}
            style={{
              padding: '10px 16px',
              background: 'white',
              border: '1px solid #D1D5DB',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              cursor: 'pointer'
            }}
          >
            Clear Filters
          </button>
        </div>

        <div style={{
          fontSize: '14px',
          color: '#6B7280'
        }}>
          Showing {filteredPlans.length} of {plans.length} plans
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading plans...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="error-state">
          <p>Error: {error}</p>
          <button onClick={loadPlans} className="retry-btn">
            Retry
          </button>
        </div>
      )}

      {/* Plans Cards */}
      {!loading && !error && (
        <div style={{ maxWidth: '95%', margin: '0 auto', padding: '0 2%' }}>
          {filteredPlans.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '48px 24px',
              background: 'white',
              borderRadius: '12px',
              border: '1px solid #E5E7EB',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}>
              <RiShieldCheckLine size={48} style={{ color: '#9CA3AF', margin: '0 auto 16px' }} />
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: '0 0 8px' }}>No Plans Found</h3>
              <p style={{ fontSize: '14px', color: '#6B7280', margin: '0 0 24px' }}>
                {plans.length === 0
                  ? "You haven't created any plans yet. Create your first plan to get started."
                  : "No plans match your search criteria. Try adjusting your filters."
                }
              </p>
              <button
                onClick={() => router.push('/ai-enroller/create-plan')}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '10px 16px',
                  background: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                <HiOutlinePlus size={16} />
                Create Your First Plan
              </button>
            </div>
          ) : (
            <>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '24px'
              }}>
                <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>Plans List</h3>
                <span style={{ fontSize: '14px', color: '#6B7280' }}>
                  {filteredPlans.length} plan{filteredPlans.length !== 1 ? 's' : ''}
                </span>
              </div>

              {/* Table */}
              <div style={{
                background: 'white',
                borderRadius: '12px',
                border: '1px solid #E5E7EB',
                overflow: 'hidden',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ background: '#F9FAFB', borderBottom: '1px solid #E5E7EB' }}>
                      <th style={{
                        padding: '12px 16px',
                        textAlign: 'left',
                        fontSize: '12px',
                        fontWeight: '500',
                        color: '#6B7280',
                        textTransform: 'uppercase',
                        letterSpacing: '0.05em'
                      }}>
                        Plan Name
                      </th>
                      <th style={{
                        padding: '12px 16px',
                        textAlign: 'left',
                        fontSize: '12px',
                        fontWeight: '500',
                        color: '#6B7280',
                        textTransform: 'uppercase',
                        letterSpacing: '0.05em'
                      }}>
                        Plan Code
                      </th>
                      <th style={{
                        padding: '12px 16px',
                        textAlign: 'left',
                        fontSize: '12px',
                        fontWeight: '500',
                        color: '#6B7280',
                        textTransform: 'uppercase',
                        letterSpacing: '0.05em'
                      }}>
                        Coverage Type
                      </th>
                      <th style={{
                        padding: '12px 16px',
                        textAlign: 'left',
                        fontSize: '12px',
                        fontWeight: '500',
                        color: '#6B7280',
                        textTransform: 'uppercase',
                        letterSpacing: '0.05em'
                      }}>
                        Status
                      </th>
                      <th style={{
                        padding: '12px 16px',
                        textAlign: 'center',
                        fontSize: '12px',
                        fontWeight: '500',
                        color: '#6B7280',
                        textTransform: 'uppercase',
                        letterSpacing: '0.05em'
                      }}>
                        Groups
                      </th>
                      <th style={{
                        padding: '12px 16px',
                        textAlign: 'center',
                        fontSize: '12px',
                        fontWeight: '500',
                        color: '#6B7280',
                        textTransform: 'uppercase',
                        letterSpacing: '0.05em'
                      }}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedPlans.map((plan, index) => {
                      const getCategoryColor = (coverageType: string) => {
                        switch (coverageType?.toLowerCase()) {
                          case 'medical':
                          case 'health':
                            return {
                              bg: '#EFF6FF',
                          border: '#DBEAFE',
                          text: '#1E40AF',
                          icon: '#3B82F6'
                        };
                      case 'dental':
                        return {
                          bg: '#F0FDF4',
                          border: '#BBF7D0',
                          text: '#166534',
                          icon: '#22C55E'
                        };
                      case 'vision':
                        return {
                          bg: '#FEF3C7',
                          border: '#FDE68A',
                          text: '#92400E',
                          icon: '#F59E0B'
                        };
                      default:
                        return {
                          bg: '#F9FAFB',
                          border: '#E5E7EB',
                          text: '#374151',
                          icon: '#6B7280'
                        };
                    }
                  };

                  const colors = getCategoryColor(plan.coverageType);
                  const statusColor = plan.status === 'Active' ? '#10B981' : plan.status === 'Draft' ? '#F59E0B' : '#6B7280';

                  return (
                    <div key={plan._id} style={{
                      background: 'white',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '12px',
                      overflow: 'hidden',
                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                      transition: 'all 0.2s',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}>
                      {/* Card Header */}
                      <div style={{
                        background: colors.bg,
                        padding: '16px',
                        borderBottom: `1px solid ${colors.border}`,
                        position: 'relative'
                      }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                          <div style={{
                            width: '40px',
                            height: '40px',
                            background: colors.icon,
                            borderRadius: '8px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <HiOutlineClipboardList style={{ width: '20px', height: '20px', color: 'white' }} />
                          </div>
                          <div style={{ flex: 1 }}>
                            <h4 style={{
                              fontSize: '16px',
                              fontWeight: '600',
                              color: '#111827',
                              margin: '0 0 4px 0'
                            }}>
                              {plan.planName}
                            </h4>
                            <span style={{
                              fontSize: '12px',
                              fontWeight: '500',
                              color: colors.text,
                              background: 'rgba(255, 255, 255, 0.8)',
                              padding: '2px 8px',
                              borderRadius: '4px'
                            }}>
                              {plan.planCode}
                            </span>
                          </div>
                          <div style={{
                            position: 'absolute',
                            top: '12px',
                            right: '12px',
                            background: statusColor,
                            color: 'white',
                            fontSize: '11px',
                            fontWeight: '500',
                            padding: '4px 8px',
                            borderRadius: '12px'
                          }}>
                            {plan.status || 'Unknown'}
                          </div>
                        </div>
                      </div>

                      {/* Card Body */}
                      <div style={{ padding: '16px' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                          <span style={{ fontSize: '14px', color: '#6B7280' }}>Coverage Type</span>
                          <span style={{
                            fontSize: '12px',
                            fontWeight: '500',
                            color: colors.text,
                            background: colors.bg,
                            padding: '4px 8px',
                            borderRadius: '6px'
                          }}>
                            {plan.coverageSubTypes?.[0] || plan.coverageType}
                          </span>
                        </div>

                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                          <span style={{ fontSize: '14px', color: '#6B7280' }}>Groups Assigned</span>
                          <span style={{
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#111827'
                          }}>
                            {planAssignmentCounts[plan._id] !== undefined ? planAssignmentCounts[plan._id] : '...'}
                          </span>
                        </div>

                        {/* Action Buttons */}
                        <div style={{
                          display: 'flex',
                          gap: '8px',
                          paddingTop: '12px',
                          borderTop: '1px solid #F3F4F6'
                        }}>
                          <button
                            onClick={() => handleEditPlan(plan._id)}
                            style={{
                              flex: 1,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              gap: '6px',
                              padding: '8px 12px',
                              background: '#F3F4F6',
                              border: 'none',
                              borderRadius: '6px',
                              fontSize: '12px',
                              fontWeight: '500',
                              color: '#374151',
                              cursor: 'pointer'
                            }}
                          >
                            <HiOutlinePencil size={14} />
                            Edit
                          </button>
                          <button
                            onClick={() => handleCopyPlan(plan._id)}
                            style={{
                              flex: 1,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              gap: '6px',
                              padding: '8px 12px',
                              background: '#F3F4F6',
                              border: 'none',
                              borderRadius: '6px',
                              fontSize: '12px',
                              fontWeight: '500',
                              color: '#374151',
                              cursor: 'pointer'
                            }}
                          >
                            <HiOutlineDuplicate size={14} />
                            Copy
                          </button>
                          {plan.status === 'Active' ? (
                            <button
                              onClick={() => handleDeactivatePlan(plan._id)}
                              style={{
                                padding: '8px',
                                background: '#FEF3C7',
                                border: 'none',
                                borderRadius: '6px',
                                color: '#92400E',
                                cursor: 'pointer'
                              }}
                            >
                              <HiOutlinePause size={14} />
                            </button>
                          ) : (
                            <button
                              onClick={() => handleActivatePlan(plan._id)}
                              style={{
                                padding: '8px',
                                background: '#D1FAE5',
                                border: 'none',
                                borderRadius: '6px',
                                color: '#065F46',
                                cursor: 'pointer'
                              }}
                            >
                              <HiOutlinePlay size={14} />
                            </button>
                          )}
                          <button
                            onClick={() => handleDeletePlan(plan._id)}
                            style={{
                              padding: '8px',
                              background: '#FEE2E2',
                              border: 'none',
                              borderRadius: '6px',
                              color: '#DC2626',
                              cursor: 'pointer'
                            }}
                          >
                            <HiOutlineTrash size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: '24px',
                  padding: '16px',
                  background: 'white',
                  borderRadius: '8px',
                  border: '1px solid #E5E7EB'
                }}>
                  <div style={{ fontSize: '14px', color: '#6B7280' }}>
                    Showing {startIndex + 1}-{Math.min(endIndex, filteredPlans.length)} of {filteredPlans.length} plans
                  </div>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      style={{
                        padding: '8px 12px',
                        background: currentPage === 1 ? '#F9FAFB' : 'white',
                        border: '1px solid #D1D5DB',
                        borderRadius: '6px',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: currentPage === 1 ? '#9CA3AF' : '#374151',
                        cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
                      }}
                    >
                      <HiOutlineChevronLeft size={16} />
                    </button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        style={{
                          padding: '8px 12px',
                          background: page === currentPage ? '#6366F1' : 'white',
                          border: '1px solid #D1D5DB',
                          borderRadius: '6px',
                          fontSize: '14px',
                          fontWeight: '500',
                          color: page === currentPage ? 'white' : '#374151',
                          cursor: 'pointer'
                        }}
                      >
                        {page}
                      </button>
                    ))}
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      style={{
                        padding: '8px 12px',
                        background: currentPage === totalPages ? '#F9FAFB' : 'white',
                        border: '1px solid #D1D5DB',
                        borderRadius: '6px',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: currentPage === totalPages ? '#9CA3AF' : '#374151',
                        cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'
                      }}
                    >
                      <HiOutlineChevronRight size={16} />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Create/Edit Plan Modal */}
      {showPlanModal && (
        <div className="modal-overlay" onClick={handlePlanCancel}>
          <div className="modal-content plan-modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{editingPlan ? 'Edit Plan' : 'Create New Plan'}</h2>
              <button className="modal-close" onClick={handlePlanCancel}>
                <HiOutlineX size={20} />
              </button>
            </div>
            <div className="modal-body">
              <CreatePlanForm
                initialData={editingPlan}
                onSubmit={handlePlanSubmit}
                onCancel={handlePlanCancel}
                isModal={true}
              />
            </div>
          </div>
        </div>
      )}

      {/* Custom Alert Modal */}
      {showAlertModal && alertModalData && (
        <div className="modal-overlay" onClick={closeAlertModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{alertModalData.title}</h2>
              <button className="modal-close" onClick={closeAlertModal}>
                <HiOutlineX size={20} />
              </button>
            </div>
            <div className="modal-body">
              <p style={{ whiteSpace: 'pre-line' }}>{alertModalData.message}</p>
            </div>
            <div className="modal-footer">
              <button className="modal-btn primary" onClick={closeAlertModal}>
                OK
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom Confirm Modal */}
      {showConfirmModal && confirmModalData && (
        <div className="modal-overlay" onClick={closeConfirmModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{confirmModalData.title}</h2>
              <button className="modal-close" onClick={closeConfirmModal}>
                <HiOutlineX size={20} />
              </button>
            </div>
            <div className="modal-body">
              <p style={{ whiteSpace: 'pre-line' }}>{confirmModalData.message}</p>
            </div>
            <div className="modal-footer">
              <button className="modal-btn secondary" onClick={closeConfirmModal}>
                Cancel
              </button>
              <button className="modal-btn primary" onClick={confirmAction}>
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom Input Modal */}
      {showInputModal && inputModalData && (
        <div className="modal-overlay" onClick={closeInputModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{inputModalData.title}</h2>
              <button className="modal-close" onClick={closeInputModal}>
                <HiOutlineX size={20} />
              </button>
            </div>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const values: Record<string, string> = {};
              inputModalData.fields.forEach(field => {
                values[field.name] = formData.get(field.name) as string || '';
              });
              inputModalData.onSubmit(values);
              closeInputModal();
            }}>
              <div className="modal-body">
                {inputModalData.fields.map((field) => (
                  <div key={field.name} className="form-group" style={{ marginBottom: '1rem' }}>
                    <label htmlFor={field.name} style={{
                      display: 'block',
                      marginBottom: '0.5rem',
                      fontSize: '14px',
                      lineHeight: '21px',
                      fontWeight: '500',
                      color: '#374151'
                    }}>
                      {field.label}
                      {field.required && <span style={{ color: '#dc2626' }}>*</span>}
                    </label>
                    <input
                      type="text"
                      id={field.name}
                      name={field.name}
                      placeholder={field.placeholder}
                      defaultValue={field.defaultValue}
                      required={field.required}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '0.5rem',
                        fontSize: '14px',
                        lineHeight: '21px',
                        fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                      }}
                    />
                  </div>
                ))}
              </div>
              <div className="modal-footer">
                <button type="button" className="modal-btn secondary" onClick={closeInputModal}>
                  Cancel
                </button>
                <button type="submit" className="modal-btn primary">
                  Submit
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

        </div>
      </div>
    </ProtectedRoute>
  );
};

export default PlansPage;
