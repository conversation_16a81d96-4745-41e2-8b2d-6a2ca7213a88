# BenoSphere AI Chatbot

A comprehensive AI-powered chatbot microservice designed to assist employees with inquiries related to company benefits, policies, and wellness recommendations.

## Architecture Overview

### Core Components

1. **FastAPI Server**: Central API layer handling HTTP requests and responses
2. **Session Management**: Maintains user chat sessions across memory and Redis
3. **Vector Store**: Semantic document retrieval system using Pinecone
4. **AI Models**: OpenAI-based language models for natural language processing
5. **Wellness Module**: Health assessment and personalized recommendations

### System Flow Diagram

```
┌─────────────┐     ┌─────────────┐     ┌─────────────────┐
│  User Query ├────►│ FastAPI     ├────►│ Session Manager │
└─────────────┘     │ Endpoints   │     └────────┬────────┘
                    └─────────────┘              │
                           ▲                     ▼
                           │             ┌───────────────┐
┌─────────────┐            │             │ Chat Session  │
│  Response   │◄───────────┘             └───────┬───────┘
└─────────────┘                                  │
                                                 ▼
┌─────────────┐            ┌─────────────────────────────┐
│  Document   │◄───────────┤ Vector Store & Retriever    │
│  Storage    │            └─────────────────────────────┘
└─────────────┘                          │
                                         ▼
                              ┌─────────────────────┐
                              │ AI Model Processing │
                              └─────────────────────┘
```

## Session Management

BenoSphere implements a dual-tier session management system:

### In-Memory Session Storage
- Fast access (microseconds)
- Limited by server RAM
- Default timeout: 30 minutes (configurable)
- Eviction policy: Least Recently Used (LRU)

### Redis Session Persistence
- Network-based access (milliseconds)
- Higher capacity than memory
- Extended timeout: 5x memory timeout
- Survives application restarts

### Session Management Flow

When a user sends a message:

1. **Session Lookup**:
   - First check in-memory storage (fastest)
   - If not found, check Redis (slower but persistent)
   - If not in Redis, create new session

2. **Session Restoration**:
   - When restoring from Redis, deserialize session data
   - Restore chat history and user context
   - Update last activity timestamp

3. **Session Eviction**:
   - When memory reaches capacity, evict least recently used sessions
   - Evicted sessions remain in Redis for later restoration
   - Background task periodically removes expired sessions

4. **Session Expiration**:
   - Memory sessions expire after inactivity timeout
   - Redis sessions expire after extended timeout
   - Expired sessions are completely removed from both tiers

## Vector Store System

BenoSphere uses vector databases to enable semantic search across company documents:

### Document Processing
- PDF documents are chunked into smaller segments
- Text is embedded using OpenAI embeddings
- Embeddings are stored in Pinecone with metadata

### Namespace Isolation
- Each team has a dedicated namespace
- Prevents cross-team data access
- Enables team-specific document retrieval

### Retrieval Optimization
- Cached vector store connections
- Namespace-specific instances to prevent collisions
- Asynchronous operations for non-blocking performance

## API Endpoints

### Chat Endpoints

#### POST `/chat`
Process a user message and stream the AI response.

**Request Body**:
```json
{
  "user_message": "What are my dental benefits?",
  "user_id": "user123",
  "team_id": "team456"
}
```

**Response**: Streaming response with AI-generated text

#### POST `/teams-chat`
Process a message from Microsoft Teams integration.

**Request Body**:
```json
{
  "user_message": "What are my vision benefits?",
  "user_email": "<EMAIL>"
}
```

**Response**: Streaming response with AI-generated text

### Wellness Endpoints

#### POST `/wellness/predictions`
Generate health predictions and personalized recommendations.

**Request Body**:
```json
{
  "user_id": "user123",
  "team_id": "team456",
  "answers": [
    {"question_id": "q1", "answer": "Yes"},
    {"question_id": "q2", "answer": "No"}
  ]
}
```

**Response**: Health predictions and personalized recommendations

#### GET `/wellness/questions`
Retrieve wellness assessment questions.

**Response**: List of questions for wellness assessment

### Vector Store Management

#### POST `/update-pinecone-index`
Add or update documents in the vector store.

**Request Body**:
```json
{
  "team_id": "team456",
  "object_keys": ["document1.pdf", "document2.pdf"]
}
```

#### POST `/delete-from-pinecone-index`
Remove documents from the vector store.

**Request Body**:
```json
{
  "team_id": "team456",
  "object_keys": ["document1.pdf"]
}
```

#### POST `/search-using-retriever`
Test document retrieval functionality.

**Request Body**:
```json
{
  "user_message": "dental benefits",
  "user_id": "user123",
  "team_id": "team456"
}
```

### Utility Endpoints

#### GET `/check`
Verify the AI backend status.

**Response**: Status message confirming system availability

## Configuration

The application is configured through `config/config.yaml` with environment variable overrides:

```yaml
# API Keys and Connections
OPENAI_API_KEY: your_openai_key
PINECONE_API_KEY: your_pinecone_key
REDIS_URL: redis://localhost:6379
BLOB_CONNECTION_STRING: your_blob_connection_string
MONGO_DB_URI: mongodb://localhost:27017

# Session Management
SESSION_TIMEOUT: 30  # minutes
TASK_CLEANUP_TIME: 300  # seconds
MAX_MEMORY_SESSIONS: 100

# Model Configuration
OPENAI_MODEL_NAME: gpt-4
OPENAI_MODEL_CATEGORY: openai
CHAT_MODEL_NAME: gpt-4
CHAT_MODEL_CATEGORY: openai
MODEL_TEMPERATURE: 0.7
MEMORY: true
MEMORY_ORDER: 10

# Vector Database
INDEX_NAME: your_pinecone_index
NAMESPACE_PREFIX: benosphere
CHUNK_SIZE: 1000
CHUNK_OVERLAP: 200
K_FILTER: 5

# Caching
LRU_CACHE_TTL: 3600  # seconds
LRU_CACHE_MAXSIZE: 100
```

## Deployment

### Docker Deployment

The application is containerized and can be deployed using Docker Compose:

```bash
docker-compose up -d
```

This starts:
- BenoSphere application container (port 8000)
- Redis container for session persistence (port 6379)

### Environment Variables

Critical environment variables:
- `OPENAI_API_KEY`: OpenAI API key for language models
- `PINECONE_API_KEY`: Pinecone API key for vector database
- `MONGO_DB_URI`: MongoDB connection string
- `REDIS_URL`: Redis connection string

## Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/benosphere-ai.git
   cd benosphere-ai
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure the environment:
   - Copy `config/config.example.yaml` to `config/config.yaml`
   - Fill in the required API keys and settings

4. Run the server:
   ```bash
   python server.py
   ```

## Key Classes and Components

### BenoSphereAI
Main application class that initializes FastAPI, MongoDB, and vector store connections.

```python
class BenoSphereAI:
    def __init__(self):
        # Initialize FastAPI, MongoDB, vector store
        # Set up routes and middleware
    
    async def chat_endpoint(self, request, user_input):
        # Process chat requests with session management
        # Return streaming response
```

### SessionManager
Manages user chat sessions with memory and Redis persistence.

```python
class SessionManager:
    def __init__(self, redis_url, timeout_minutes, max_memory_sessions):
        # Initialize session storage and Redis client
        # Set up background cleanup task
    
    async def get_session(self, user_id, team_id):
        # Get existing session or create new one
        # Handle session restoration from Redis
```

### ChatSession
Represents a single user's chat session with history and context.

```python
class ChatSession:
    def __init__(self, user_id, team_id, vector_store, mongo_client):
        # Initialize session with user context
        # Set up retriever for document access
    
    async def process_message(self, user_message):
        # Retrieve relevant documents
        # Generate AI response
        # Update chat history
```

### VectorStore
Manages document embeddings and retrieval using vector databases.

```python
class VectorStore:
    def __init__(self, vector_db_type, embedding_available_option):
        # Initialize vector database connection
        # Set up embedding model
    
    async def load_vector_store_async(self, index_name, namespace):
        # Load vector store with caching
        # Prevent collisions between concurrent users
```

### Wellness
Provides health assessments and personalized recommendations.

```python
class Wellness:
    def __init__(self, mongo_client, mongo_environment, vector_store_manager):
        # Initialize wellness models and data
    
    async def initiate_wellness_app(self, wellness_data):
        # Process wellness questionnaire
        # Generate health predictions
        # Provide personalized recommendations
```

## Performance Considerations

1. **Asynchronous Processing**: Non-blocking I/O operations using `async/await`
2. **Session Caching**: Two-tier caching strategy (memory + Redis)
3. **Vector Store Optimization**: Cached connections and namespace isolation
4. **Background Tasks**: Periodic cleanup to manage resources
5. **Streaming Responses**: Immediate feedback to users while processing continues

## Security Considerations

1. **Data Isolation**: Team-specific namespaces prevent cross-team data access
2. **Session Expiration**: Automatic session cleanup after inactivity
3. **Input Validation**: Pydantic models validate all user inputs
4. **Error Handling**: Sanitized error messages prevent information leakage

## Monitoring and Logging

The application includes comprehensive logging:
- Request/response logging
- Session creation and expiration events
- Vector store operations
- Error conditions and exceptions

Logs use the standard Python logging module with configurable levels.
