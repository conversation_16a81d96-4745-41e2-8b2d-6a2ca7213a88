import { Router, Request, Response } from 'express';
import Controller from '../interfaces/controller.interface';
import WaitlistModelClass from '../nosql/waitlist.model';

export class WaitlistController implements Controller {
  public router = Router();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post('/test/waitlist/join', this.joinWaitlist);
    this.router.get('/test/waitlist/entries', this.getWaitlistEntries);
  }

  private joinWaitlist = async (req: Request, res: Response) => {
    try {
      const { email, phone } = req.body;
      console.log(req.body)
      
      // Add validation
      if (!email || !phone) {
        return res.status(400).json({ 
          success: false, 
          message: 'Email and phone are required fields' 
        });
      }
      
      const result = await WaitlistModelClass.addToWaitlist({
        email,
        phone,
        joinedAt: new Date(),
        source: 'benefits_waitlist'
      });
      res.status(200).json(result);
    } catch (error) {
      console.error('Controller error:', error); // Add this for debugging
      res.status(500).json({ error: 'Failed to join waitlist' });
    }
  };

  private getWaitlistEntries = async (_req: Request, res: Response) => {
    try {
      const entries = await WaitlistModelClass.getWaitlistEntries();
      res.status(200).json({ entries });
    } catch (error) {
      res.status(500).json({ error: 'Failed to get waitlist entries' });
    }
  };
}

export default WaitlistController;
