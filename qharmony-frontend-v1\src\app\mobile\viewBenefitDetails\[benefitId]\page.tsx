"use client";

import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { getDocumentsForBenefit } from "@/middleware/benefits_middleware";
import { useParams, useRouter } from "next/navigation";
import { Box, Typography, Button, Grid, CircularProgress } from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew"; // Small back arrow icon
import { useSelector } from "react-redux";
import { selectBenefitById } from "@/redux/reducers/benefitsSlice";
import { getUsersCompanyId } from "@/redux/reducers/userSlice";
import OpenInNewIcon from "@mui/icons-material/OpenInNew"; // Import the icon
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";
import withMobileEdgeFill from "@/components/mobile_edge_fill";
import * as microsoftTeams from "@microsoft/teams-js";
import { maskedSubCategory } from "@/middleware/company_middleware";


const DocumentsForBenefitsView = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { benefitId } = useParams();

  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));

  const documents = useAppSelector(
    (state: RootState) => state.benefits.documentsPerBenefit,
  );

  const viewableDocuments = useAppSelector(
    (state: RootState) => state.benefits.viewableDocuments,
  );

  const benefitInfo = useSelector((state: RootState) =>
    selectBenefitById(state, benefitId as string),
  );

  const loadingDocuments = useAppSelector(
    (state: RootState) => state.benefits.loadingDocuments,
  );

  useEffect(() => {
    if (benefitId !== "") {
      getDocumentsForBenefit(dispatch, benefitId as string, companyId, "view_benefits");
    }
  }, [benefitId, companyId, dispatch]);

  const handleBack = () => {
    router.back();
  };

  const openPdfExternally = (objectId: string, companyId: string) => {
    window.open(`https://api.benosphere.com/benefits/document?objectKey=${objectId}&companyId=${companyId}`, "_blank");
  };

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6F8",
          height: "100vh",
          padding: "32px",
          overflow: "auto",
        }}
      >
        <Typography sx={{ fontWeight: 800, fontSize: "42px", mb: 0 }}>
          {maskedSubCategory(benefitInfo?.benefit?.subType || "")}
        </Typography>
        <Typography
          variant="body1"
          sx={{ color: "#6c757d", mb: 6, fontSize: "16px" }}
        >
          You can find all your health insurance details here, including
          coverage options, policy documents, and claim information.
        </Typography>

        <Grid container spacing={3} alignItems="flex-start">
          <Grid item xs={12}>
            <Typography sx={{ mb: 3, fontWeight: 700, fontSize: "24px" }}>
              ☕ Documents
            </Typography>
            {documents.documents.length === 0 ? (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  minHeight: "150px",
                  borderRadius: "8px",
                  border: "2px dashed #e0e0e0",
                  bgcolor: "#f9f9f9",
                  p: 4,
                  textAlign: "left",
                  maxWidth: "400px",
                }}
              >
                <Typography
                  variant="body1"
                  sx={{ color: "#6c757d", fontSize: "1rem" }}
                >
                  No documents available at the moment.
                </Typography>
              </Box>
            ) : (
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "repeat(auto-fill, minmax(160px, 1fr))",
                  gap: "20px",
                }}
              >
                {documents.documents
                  .map((documentObjectKey, index) => {
                    const gradientOptions = [
                      "linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)",
                      "linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)",
                      "linear-gradient(135deg, #43cea2 0%, #185a9d 100%)",
                      "linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)",
                      "linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)",
                    ];

                    const gradient =
                      gradientOptions[index % gradientOptions.length];

                    const viewableDocument = viewableDocuments.find(
                      (doc) => doc.documentObjectKey === documentObjectKey,
                    );
                    return (
                      <Box
                        key={documentObjectKey}
                        sx={{
                          position: "relative",
                          width: "100%",
                          height: "auto",
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Box
                          sx={{
                            width: "100%",
                            minHeight: "200px",
                            borderRadius: "12px",
                            overflow: "hidden",
                            boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
                            position: "relative",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            background: gradient,
                            color: "#ffffff",
                            cursor: "pointer",
                          }}
                          onClick={() => {
                            if (viewableDocument?.document) {
                              console.log("viewableDocument ===>", viewableDocument)
                              openPdfExternally(viewableDocument.documentObjectKey, companyId);
                            }
                          }}
                        >
                          {loadingDocuments.includes(documentObjectKey) ||
                            !viewableDocument ? (
                            <CircularProgress />
                          ) : (
                            <Box>
                              <Typography
                                sx={{
                                  fontSize: "16px",
                                  fontWeight: "bold",
                                  textAlign: "center",
                                  padding: "8px",
                                }}
                              >
                                {viewableDocument?.originalFileName ||
                                  "Document Preview"}
                              </Typography>
                              <OpenInNewIcon
                                sx={{
                                  position: "absolute",
                                  top: 8,
                                  right: 8,
                                  color: "#ffffff",
                                  cursor: "pointer",
                                  height: 20,
                                  width: 20,
                                }}
                                onClick={() =>
                                  // openDocumentInBrowser(
                                  //   viewableDocument?.document,
                                  // )
                                  openPdfExternally(viewableDocument?.documentObjectKey, companyId)
                                }
                              />
                            </Box>
                          )}
                        </Box>
                      </Box>
                    );
                  })}
              </Box>
            )}
          </Grid>

          <Grid item xs={12}>
            <Typography
              sx={{ mb: 3, fontWeight: 700, mt: 10, fontSize: "17px" }}
            >
              Other helpful links
            </Typography>
            <Box
              sx={{
                borderRadius: "12px",
                width: "100%", // Use full width for mobile responsiveness
                maxWidth: "400px", // Set a max width to prevent it from being too wide
                mx: "auto", // Center the box horizontally
              }}
            >
              {documents.links.length === 0 ? (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    minHeight: "150px",
                    borderRadius: "8px",
                    border: "2px dashed #e0e0e0",
                    bgcolor: "#f9f9f9",
                    py: 4,
                    textAlign: "left",
                    width: "100%", // Ensure full width
                    maxWidth: "400px", // Set a max width
                    mx: "auto", // Center the box horizontally
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ color: "#6c757d", fontSize: "16px" }}
                  >
                    No links available right now.
                  </Typography>
                </Box>
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 2, // Space between each link
                  }}
                >
                  {documents.links.map((link, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between", // Ensures the text and icon are on opposite ends
                        alignItems: "center",
                        width: "100%", // Ensure full width
                      }}
                    >
                      <Typography
                        component="a"
                        href={
                          link.startsWith("http") ? link : `https://${link}`
                        }
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{
                          color: "#1A7ECF", // Black color for text
                          textDecoration: "none",
                          fontWeight: 500, // Semi-bold font weight for link text
                          fontSize: "16px",
                          overflow: "hidden", // Hide overflow text
                          textOverflow: "ellipsis", // Add ellipsis for overflow text
                          whiteSpace: "nowrap", // Prevent text wrapping
                          maxWidth: "80%", // Limit the width of the text
                        }}
                      >
                        {link || `Link ${index + 1}`}
                      </Typography>
                      <Button
                        onClick={() => {
                          console.log("link ===>", link)
                          window.open(
                            link.startsWith("http") ? link : `https://${link}`,
                            "_blank",
                          )
                        }
                        }
                        sx={{ minWidth: 0, padding: 0 }} // Remove default button padding
                      >
                        <OpenInNewIcon
                          sx={{ color: "#6c757d", marginLeft: 2 }} // Gray color for the icon
                        />
                      </Button>
                    </Box>
                  ))}
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </ProtectedRoute>
  );
};

export default withMobileEdgeFill(DocumentsForBenefitsView);
