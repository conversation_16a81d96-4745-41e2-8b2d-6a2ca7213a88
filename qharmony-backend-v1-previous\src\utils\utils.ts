import { User } from "../controllers/group.controller";
import { InsuranceType } from "../enums/insuranceType.enum";
import { CompactBenefit } from "../interfaces/finch.interface";

export const snakeCaseToHumanReadable = (
  input: string | null | undefined
): string => {
  if (!input) {
    return 'null';
  }
  // Split the input string by underscores
  const words = input.split('_');

  // Capitalize the first letter of each word and join them with a space
  const humanReadable = words
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return humanReadable;
};

export const convertYyyyMmDdToMmDdYyyy = (input: string): string => {
  const [yyyy, mm, dd] = input.split('-');
  if (!yyyy || !mm || !dd) {
    return input;
  }
  return `${mm}/${dd}/${yyyy}`;
}

export const makeNumberHumanReadable = (input: number | string): string => {
  // Put commas in the right places.
  // E.g. 1234567 -> 1,234,567
  // E.g. 123456 -> 123,456
  // E.g. 12345 -> 12,345
  // E.g. 1234 -> 1,234
  if (typeof input === 'string') {
    input = Number(input);
  }
  return input.toLocaleString('en-US');
}

export const loremIpsum = () => {
  return 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Cras elementum ultrices diam. Maecenas ligula massa, varius a, semper congue, euismod non, mi.';
}

export const getEmojiForBenefit = (benefit: CompactBenefit): string => {
  if (benefit.type?.toLowerCase()?.trim() === InsuranceType.s125_medical) {
    return '🩺';
  }
  if (benefit.type?.toLowerCase()?.trim() === InsuranceType.s125_vision) {
    return '👀';
  }
  if (benefit.type?.toLowerCase()?.trim() === InsuranceType.s125_dental) {
    return '🦷';
  }
  if (benefit.type?.toLowerCase()?.trim() === 'commuter') {
    return '🚗';
  }
  if (benefit.type?.toLowerCase()?.trim() === '401k') {
    return '🧓';
  }
  if (benefit.type?.toLowerCase()?.trim() === 'hsa_pre') {
    return '❤️';
  }
  if (benefit.description.toLowerCase().includes('gym')) {
    return '🏋️';
  }
  if (benefit.description.toLowerCase().includes('insurance')) {
    return '🩺';
  }
  return '💰';

}

export const getLongDescriptionForBenefit = (benefit: CompactBenefit): string => {
  if (benefit.type?.toLowerCase()?.trim() === InsuranceType.s125_medical) {
    return 'Get comprehensive medical coverage.';
  }
  if (benefit.type?.toLowerCase()?.trim() === InsuranceType.s125_vision) {
    return 'Maintain clear vision with regular vision checkups.';
  }
  if (benefit.type?.toLowerCase()?.trim() === InsuranceType.s125_dental) {
    return 'Keep your smile bright with comprehensive dental coverage.';
  }
  if (benefit.type?.toLowerCase()?.trim() === 'commuter') {
    return 'Get rewarded for your daily journey with commuter stipend.';
  }
  if (benefit.type?.toLowerCase()?.trim() === '401k') {
    return 'Secure your future with a 401k retirement plan.';
  }
  if (benefit.type?.toLowerCase()?.trim() === 'hsa_pre') {
    return 'Save for medical expenses tax-free with a Health Savings Account.';
  }
  if (benefit.description.toLowerCase().trim() === 'life insurance') {
    return 'Protect your loved ones with comprehensive life insurance coverage.';
  }
  if (benefit.description.toLowerCase().trim() === 'gym membership') {
    return 'Stay fit and healthy with complimentary gym membership.';
  }
  return 'Get the benefits you deserve.';
}

interface UserCountByCompany {
  companyName: string;
  userCount: number;
}

export const countUsersByCompany = (users: User[]): UserCountByCompany[] => {
  const result: UserCountByCompany[] = [];

  // Iterate over the array and group by companyName
  users.forEach(user => {
    // Find if the companyName already exists in the result array
    const existingCompany = result.find(item => item.companyName === user.companyName);

    if (existingCompany) {
      // If found, increment the user count
      existingCompany.userCount++;
    } else {
      // If not found, create a new entry for that company
      result.push({ companyName: user.companyName, userCount: 1 });
    }
  });

  return result;
}