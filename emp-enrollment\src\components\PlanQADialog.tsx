
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Send, Bot } from 'lucide-react';

interface QAItem {
  question: string;
  answer: string;
  category: string;
}

const commonQuestions: QAItem[] = [
  {
    question: "What happens if I don't enroll in benefits?",
    answer: "If you don't enroll during open enrollment, you'll have no medical, dental, or vision coverage for 2025. You can only enroll outside of open enrollment if you have a qualifying life event.",
    category: "enrollment"
  },
  {
    question: "Can I change my plan after enrollment?",
    answer: "You can only change your benefits during the next open enrollment period or within 30 days of a qualifying life event (marriage, birth of child, job loss of spouse, etc.).",
    category: "changes"
  },
  {
    question: "What's the difference between HMO and PPO?",
    answer: "PPO plans offer more flexibility to see any doctor without referrals but cost more. HMO plans require you to choose a primary care physician and get referrals for specialists, but have lower costs.",
    category: "plans"
  },
  {
    question: "How much will I pay per paycheck?",
    answer: "Your paycheck deduction depends on the plans you select and your family coverage level. Medical plans range from $45-120/paycheck, dental $6-15/paycheck, vision $3-8/paycheck.",
    category: "costs"
  },
  {
    question: "Are my current doctors covered?",
    answer: "Use the provider directory links in each plan to check if your preferred doctors are in-network. PPO plans typically have larger networks than HMO plans.",
    category: "providers"
  }
];

interface PlanQADialogProps {
  selectedPlans?: {
    medical?: any;
    dental?: any;
    vision?: any;
  };
}

export const PlanQADialog = ({ selectedPlans }: PlanQADialogProps) => {
  const [question, setQuestion] = useState('');
  const [chatHistory, setChatHistory] = useState<Array<{ type: 'question' | 'answer'; content: string }>>([]);
  const [isOpen, setIsOpen] = useState(false);

  const handleAskQuestion = () => {
    if (!question.trim()) return;

    setChatHistory(prev => [...prev, { type: 'question', content: question }]);
    
    // Simple keyword-based answer matching
    const answer = getAnswerForQuestion(question.toLowerCase());
    
    setTimeout(() => {
      setChatHistory(prev => [...prev, { type: 'answer', content: answer }]);
    }, 500);
    
    setQuestion('');
  };

  const getAnswerForQuestion = (question: string): string => {
    if (question.includes('cost') || question.includes('price') || question.includes('pay')) {
      if (selectedPlans?.medical) {
        return `Based on your selected plans: Medical (${selectedPlans.medical.name}) costs $${selectedPlans.medical.cost}/paycheck${selectedPlans?.dental ? `, Dental costs $${selectedPlans.dental.cost}/paycheck` : ''}${selectedPlans?.vision ? `, Vision costs $${selectedPlans.vision.cost}/paycheck` : ''}. Total: $${((selectedPlans.medical?.cost || 0) + (selectedPlans.dental?.cost || 0) + (selectedPlans.vision?.cost || 0)).toFixed(2)}/paycheck.`;
      }
      return "Your total cost depends on which plans you select. Medical plans range from $45-120/paycheck, dental $6-15/paycheck, vision $3-8/paycheck.";
    }
    
    if (question.includes('doctor') || question.includes('provider')) {
      return "To check if your doctors are covered, use the provider directory for each plan. PPO plans generally have larger networks than HMO plans. You can also call the insurance company directly.";
    }
    
    if (question.includes('deductible')) {
      if (selectedPlans?.medical) {
        return `Your selected ${selectedPlans.medical.name} has a $${selectedPlans.medical.deductible || 'varies'} deductible. This is the amount you pay out-of-pocket before insurance starts covering costs.`;
      }
      return "Deductibles vary by plan. Higher deductible plans have lower monthly costs but you pay more upfront when you need care.";
    }
    
    // Find matching common question
    const matchedQ = commonQuestions.find(q => 
      question.includes(q.question.toLowerCase().split(' ').slice(0, 3).join(' '))
    );
    
    if (matchedQ) return matchedQ.answer;
    
    return "I'd be happy to help! For specific questions about plan details, costs, or coverage, please contact HR or the insurance carrier directly. You can also check the plan documents for detailed information.";
  };

  const handleCommonQuestion = (qa: QAItem) => {
    setChatHistory(prev => [
      ...prev,
      { type: 'question', content: qa.question },
      { type: 'answer', content: qa.answer }
    ]);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <MessageCircle className="w-4 h-4" />
          Ask Questions
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="w-5 h-5 text-blue-500" />
            Benefits Q&A Assistant
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Common Questions */}
          <div>
            <h4 className="font-medium mb-2">Common Questions:</h4>
            <div className="flex flex-wrap gap-2">
              {commonQuestions.map((qa, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleCommonQuestion(qa)}
                  className="text-xs"
                >
                  {qa.question.slice(0, 30)}...
                </Button>
              ))}
            </div>
          </div>

          {/* Chat History */}
          <ScrollArea className="h-64 border rounded-lg p-4">
            <div className="space-y-3">
              {chatHistory.map((item, index) => (
                <div key={index} className={`flex ${item.type === 'question' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] p-3 rounded-lg ${
                    item.type === 'question' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-100 dark:bg-gray-800'
                  }`}>
                    {item.content}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>

          {/* Question Input */}
          <div className="flex gap-2">
            <Input
              placeholder="Ask a question about your benefits..."
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAskQuestion()}
            />
            <Button onClick={handleAskQuestion} size="icon">
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
