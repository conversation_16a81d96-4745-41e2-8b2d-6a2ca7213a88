import express from 'express';
import Controller from '../interfaces/controller.interface';
import logger from '../utils/logger';
import { PlanAssignmentService } from '../services/enrollment/planAssignment.service';
import { PlanAssignmentMiddleware } from '../middleware/enrollment/planAssignment.middleware';

/**
 * Plan Assignment Controller
 *
 * Handles CRUD operations for plan assignments with proper business logic validation.
 * Plan assignments are independent of company settings but use plan and company validation.
 *
 * Business Rules:
 * - Plan must exist and be Active (not Draft/Archived/Template)
 * - Company must exist and user must have access
 * - Assignments are editable only if 0-1 employee enrollments reference them
 * - Assignments can be deleted only if no employee enrollments reference them
 * - Assignments auto-expire after planEndDate
 * - Assignments can be activated/deactivated within effective period
 * - Once expired, assignments cannot be reactivated
 */

class PlanAssignmentController implements Controller {
  public router = express.Router();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    // Core CRUD operations (Section 3.5)
    this.router.post('/api/pre-enrollment/plan-assignments',
      ...PlanAssignmentMiddleware.forPlanAssignmentCreation(),
      this.createPlanAssignment
    );
    this.router.get('/api/pre-enrollment/plan-assignments',
      ...PlanAssignmentMiddleware.forPlanAssignmentListing(),
      this.getPlanAssignments
    );
    this.router.get('/api/pre-enrollment/plan-assignments/company/:companyId',
      ...PlanAssignmentMiddleware.forPlanAssignmentListing(),
      this.getPlanAssignmentsByCompany
    );
    this.router.get('/api/pre-enrollment/plan-assignments/:id',
      ...PlanAssignmentMiddleware.forPlanAssignmentRetrieval(),
      this.getPlanAssignmentById
    );
    this.router.put('/api/pre-enrollment/plan-assignments/:id',
      ...PlanAssignmentMiddleware.forPlanAssignmentUpdate(),
      this.updatePlanAssignment
    );
    this.router.post('/api/pre-enrollment/plan-assignments/:id/activate',
      ...PlanAssignmentMiddleware.forStatusManagement(),
      this.activatePlanAssignment
    );
    this.router.post('/api/pre-enrollment/plan-assignments/:id/deactivate',
      ...PlanAssignmentMiddleware.forStatusManagement(),
      this.deactivatePlanAssignment
    );
    this.router.post('/api/pre-enrollment/plan-assignments/:id/clone',
      ...PlanAssignmentMiddleware.forPlanAssignmentRetrieval(),
      this.clonePlanAssignment
    );
    this.router.delete('/api/pre-enrollment/plan-assignments/:id',
      ...PlanAssignmentMiddleware.forPlanAssignmentRetrieval(),
      this.deletePlanAssignment
    );

    // Validation endpoints (Section 3.6)
    this.router.get('/api/pre-enrollment/plan-assignments/:id/can-edit',
      ...PlanAssignmentMiddleware.forPlanAssignmentRetrieval(),
      this.canEditPlanAssignment
    );
    this.router.get('/api/pre-enrollment/plan-assignments/:id/can-delete',
      ...PlanAssignmentMiddleware.forPlanAssignmentRetrieval(),
      this.canDeletePlanAssignment
    );
    this.router.get('/api/pre-enrollment/plan-assignments/:id/enrollment-references',
      ...PlanAssignmentMiddleware.forPlanAssignmentRetrieval(),
      this.getEnrollmentReferences
    );
    this.router.post('/api/pre-enrollment/plan-assignments/check-expired',
      ...PlanAssignmentMiddleware.forBasicOperation(),
      this.checkExpiredAssignments
    );

    // ✅ NEW: Missing documented APIs
    this.router.get('/api/pre-enrollment/plan-assignments/effective',
      ...PlanAssignmentMiddleware.forPlanAssignmentListing(),
      this.getEffectiveAssignments
    );
    this.router.get('/api/pre-enrollment/plan-assignments/enrollment-period',
      ...PlanAssignmentMiddleware.forPlanAssignmentListing(),
      this.getEnrollmentPeriodAssignments
    );
    this.router.post('/api/pre-enrollment/plan-assignments/:id/reassign-plan',
      ...PlanAssignmentMiddleware.forPlanReassignment(),
      this.reassignPlan
    );
    this.router.put('/api/pre-enrollment/plan-assignments/:id/time-constraints',
      ...PlanAssignmentMiddleware.forTimeConstraints(),
      this.updateTimeConstraints
    );
  }

  /**
   * 3.5.1 Create Plan Assignment
   * POST /api/pre-enrollment/plan-assignments
   *
   * Business Rules:
   * - Only Brokers can create plan assignments (SuperAdmins manage system resources only)
   * - Plan must exist and be Active (not Draft/Archived/Template)
   * - Templates cannot be assigned directly (must be duplicated first)
   * - Brokers can only assign their own plans
   * - Company must exist
   * - Assignment dates must be valid
   * - Rate structure must be valid for the plan type
   */
  private createPlanAssignment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Create plan assignment using service
      const result = await PlanAssignmentService.createPlanAssignment(request.body, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to create plan assignment');
      }

      logger.info(`Plan assignment created successfully: ${result.assignment!._id} by user: ${userId}`);
      return PlanAssignmentMiddleware.assignmentCreated(response, result.assignment!);

    } catch (error) {
      logger.error('Error creating plan assignment:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.5.2 Get Plan Assignments (Role-Based with Filtering) - OPTIMIZED
   * GET /api/pre-enrollment/plan-assignments
   *
   * Returns plan assignments based on user role and access level with optional filtering:
   * - SuperAdmins: All assignments in the system
   * - Brokers: Only assignments for their own plans or assignments they created
   * - Employers: Only assignments for their company (via company-specific endpoint)
   * - Employees: Only assignments for their company (via company-specific endpoint)
   *
   * Query Parameters:
   * - page: number (optional) - Page number for pagination (starts from 1)
   * - limit: number (optional) - Items per page (1-100, default: no pagination)
   * - includeInactive: 'true' | 'false' (default: 'false') - Include inactive assignments
   * - status: 'Active' | 'Expired' | 'Deactivated' - Filter by status
   * - companyId: string - Filter by specific company (SuperAdmin only)
   * - planId: string - Filter by specific plan
   * - assignmentYear: number - Filter by assignment year
   * - referenceDate: ISO date string - Use specific date for time-based filtering
   * - includePlanData: 'true' | 'false' (default: 'true') - Include complete plan and carrier data
   * - enrollmentPeriodOnly: 'true' | 'false' - Returns only assignments in enrollment period
   * - effectiveOnly: 'true' | 'false' - Returns only currently effective assignments
   * - futureOnly: 'true' | 'false' - Returns only future assignments
   *
   * Response Formats:
   *
   * Non-Paginated Response (default):
   * {
   *   "assignments": [...],
   *   "count": 150,
   *   "userRole": "SuperAdmin" | "Broker",
   *   "appliedFilters": ["activeOnly", "status:Active"],
   *   "message": "All plan assignments retrieved successfully",
   *   "expiryInfo": { "expiredCount": 0, "updatedAssignments": [] }
   * }
   *
   * Paginated Response (when page & limit provided):
   * {
   *   "assignments": [...],
   *   "count": 20,
   *   "pagination": {
   *     "currentPage": 2,
   *     "totalPages": 8,
   *     "totalItems": 150,
   *     "hasNext": true,
   *     "hasPrev": true
   *   },
   *   "userRole": "SuperAdmin" | "Broker",
   *   "appliedFilters": ["activeOnly", "status:Active"],
   *   "message": "All plan assignments retrieved successfully",
   *   "expiryInfo": { "expiredCount": 0, "updatedAssignments": [] }
   * }
   *
   * OPTIMIZATION: All filtering and pagination now happens at database level for maximum performance
   */
  private getPlanAssignments = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { page, limit } = request.query as Record<string, string>;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Check for SuperAdmin company filter access (preserve existing business rule)
      if (request.query.companyId && !user.isSuperAdmin) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'Only SuperAdmins can filter by company ID');
      }

      // Setup pagination if requested
      const pagination = (page && limit) ? {
        page: parseInt(page),
        limit: parseInt(limit)
      } : undefined;

      // 🎯 OPTIMIZED: Use database-level filtering and pagination
      const result = await PlanAssignmentService.getPlanAssignmentsOptimized(
        userId,
        user,
        request.query,
        pagination
      );

      if (pagination) {
        // Return paginated response
        const responseData = {
          assignments: result.assignments,
          count: result.assignments.length,
          pagination: {
            currentPage: pagination.page,
            totalPages: result.totalPages!,
            totalItems: result.totalCount,
            hasNext: pagination.page < result.totalPages!,
            hasPrev: pagination.page > 1
          },
          userRole: user.isSuperAdmin ? 'SuperAdmin' : 'Broker',
          appliedFilters: result.appliedFilters,
          message: user.isSuperAdmin ? 'All plan assignments retrieved successfully' : 'Broker plan assignments retrieved successfully',
          expiryInfo: result.expiryInfo
        };

        return PlanAssignmentMiddleware.assignmentsListed(response, responseData);
      } else {
        // Return non-paginated response (backward compatibility)
        const responseData = {
          assignments: result.assignments,
          count: result.assignments.length,
          userRole: user.isSuperAdmin ? 'SuperAdmin' : 'Broker',
          appliedFilters: result.appliedFilters,
          message: user.isSuperAdmin ? 'All plan assignments retrieved successfully' : 'Broker plan assignments retrieved successfully',
          expiryInfo: result.expiryInfo
        };

        return PlanAssignmentMiddleware.assignmentsListed(response, responseData);
      }

    } catch (error) {
      logger.error('Error getting plan assignments:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.5.3 Get Plan Assignments by Company
   * GET /api/pre-enrollment/plan-assignments/company/:companyId
   *
   * 🎯 UNIFIED: Now supports ALL the same query parameters as the general API
   * Default behavior: Returns ACTIVE plan assignments only (excludes inactive/expired)
   *
   * Query Parameters (All Optional):
   * - page: number - Page number for pagination (starts from 1)
   * - limit: number - Items per page (1-100, default: no pagination)
   * - status: 'Active' | 'Expired' | 'Deactivated' - Filter by assignment status
   * - planId: string - Filter by specific plan within the company
   * - assignmentYear: number - Filter by assignment year (e.g., 2024, 2025)
   * - rateStructure: string - Filter by rate structure type
   * - brokerId: string - Filter by broker ID (SuperAdmin only)
   * - includeInactive: 'true' | 'false' (default: 'false') - Include inactive assignments
   * - includeExpired: 'true' | 'false' - Backward compatibility (maps to includeInactive)
   * - includePlanData: 'true' | 'false' (default: 'true') - Include complete plan and carrier data
   * - referenceDate: ISO date string - Use specific date for time-based filtering
   * - enrollmentPeriodOnly: 'true' | 'false' - Returns only assignments in enrollment period
   * - effectiveOnly: 'true' | 'false' - Returns only currently effective assignments
   * - futureOnly: 'true' | 'false' - Returns only future assignments
   *
   * Response Format: Same as getPlanAssignments (supports pagination)
   */
  private getPlanAssignmentsByCompany = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { companyId } = request.params;
      const { page, limit } = request.query as Record<string, string>;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // 🎯 UNIFIED: Setup pagination (same as general API)
      const pagination = (page && limit) ? {
        page: parseInt(page),
        limit: parseInt(limit)
      } : undefined;

      // 🎯 UNIFIED: Get plan assignments by company using service
      const result = await PlanAssignmentService.getPlanAssignmentsByCompany(
        companyId,
        userId,
        user,
        request.query,
        pagination
      );

      // 🎯 UNIFIED: Handle response (same format as general API)
      if (result.assignments.length === 0 && result.appliedFilters.includes('error:access-denied')) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'Access denied to company assignments');
      }

      if (result.appliedFilters.includes('error:internal-server-error')) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'Failed to get plan assignments');
      }

      return PlanAssignmentMiddleware.assignmentsListed(response, result);

    } catch (error) {
      logger.error('Error fetching plan assignments by company:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.5.3 Get Plan Assignment by ID
   * GET /api/pre-enrollment/plan-assignments/:id
   */
  private getPlanAssignmentById = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Get plan assignment by ID using service
      const result = await PlanAssignmentService.getPlanAssignmentById(id, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Plan assignment not found');
      }

      return PlanAssignmentMiddleware.assignmentRetrieved(response, result.assignment);

    } catch (error) {
      logger.error('Error fetching plan assignment by ID:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.5.4 Update Plan Assignment
   * PUT /api/pre-enrollment/plan-assignments/:id
   */
  private updatePlanAssignment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Update plan assignment using service
      const result = await PlanAssignmentService.updatePlanAssignment(id, request.body, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to update plan assignment');
      }

      logger.info(`Plan assignment updated successfully: ${id} by user: ${userId}`);
      return PlanAssignmentMiddleware.assignmentUpdated(response, result.assignment!);

    } catch (error) {
      logger.error('Error updating plan assignment:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.5.5 Activate Plan Assignment
   * POST /api/pre-enrollment/plan-assignments/:id/activate
   */
  private activatePlanAssignment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Activate plan assignment using service
      const result = await PlanAssignmentService.modifyPlanAssignmentStatus(id, 'activate', userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to activate plan assignment');
      }

      logger.info(`Plan assignment activated: ${id} by user: ${userId}`);
      return PlanAssignmentMiddleware.assignmentStatusChanged(response, result.message!);

    } catch (error) {
      logger.error('Error activating plan assignment:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.5.6 Deactivate Plan Assignment
   * POST /api/pre-enrollment/plan-assignments/:id/deactivate
   */
  private deactivatePlanAssignment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Deactivate plan assignment using service
      const result = await PlanAssignmentService.modifyPlanAssignmentStatus(id, 'deactivate', userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to deactivate plan assignment');
      }

      logger.info(`Plan assignment deactivated: ${id} by user: ${userId}`);
      return PlanAssignmentMiddleware.assignmentStatusChanged(response, result.message!);

    } catch (error) {
      logger.error('Error deactivating plan assignment:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.5.7 Clone Plan Assignment for Next Year
   * POST /api/pre-enrollment/plan-assignments/:id/clone
   *
   * Creates an active copy of the assignment for the next year.
   * The cloned assignment is immediately active and ready for use.
   *
   * Business Rules:
   * - Cloned assignment is created with isActive: true and status: 'Active'
   * - Assignment is immediately available for enrollment operations
   * - Smart date handling: enrollment dates use current year, plan dates use next year
   * - Month and day are preserved, only years are updated
   * - All rate structures and configurations are preserved exactly
   */
  private clonePlanAssignment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Clone plan assignment using service
      const result = await PlanAssignmentService.clonePlanAssignment(id, request.body, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to clone plan assignment');
      }

      logger.info(`Plan assignment cloned: ${id} -> ${result.assignment!._id} by user: ${userId}`);
      return PlanAssignmentMiddleware.assignmentCloned(response, result);

    } catch (error) {
      logger.error('Error cloning plan assignment:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.5.8 Delete Plan Assignment
   * DELETE /api/pre-enrollment/plan-assignments/:id
   */
  private deletePlanAssignment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Delete plan assignment using service
      const result = await PlanAssignmentService.deletePlanAssignment(id, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to delete plan assignment');
      }

      logger.info(`Plan assignment deleted: ${id} by user: ${userId}`);
      return PlanAssignmentMiddleware.assignmentDeleted(response);

    } catch (error) {
      logger.error('Error deleting plan assignment:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.6.1 Check if Plan Assignment Can Be Edited
   * GET /api/pre-enrollment/plan-assignments/:id/can-edit
   */
  private canEditPlanAssignment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Check if plan assignment can be edited using service
      const result = await PlanAssignmentService.canEditPlanAssignment(id, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to check edit permissions');
      }

      return PlanAssignmentMiddleware.operationResult(response, result.result);

    } catch (error) {
      logger.error('Error checking if assignment can be edited:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.6.2 Check if Plan Assignment Can Be Deleted
   * GET /api/pre-enrollment/plan-assignments/:id/can-delete
   */
  private canDeletePlanAssignment = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Check if plan assignment can be deleted using service
      const result = await PlanAssignmentService.canDeletePlanAssignment(id, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to check delete permissions');
      }

      return PlanAssignmentMiddleware.operationResult(response, result.result);

    } catch (error) {
      logger.error('Error checking if assignment can be deleted:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.6.3 Get Enrollment References for Plan Assignment
   * GET /api/pre-enrollment/plan-assignments/:id/enrollment-references
   */
  private getEnrollmentReferences = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Get enrollment references using service
      const result = await PlanAssignmentService.getEnrollmentReferences(id, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to get enrollment references');
      }

      return PlanAssignmentMiddleware.operationResult(response, result.result);

    } catch (error) {
      logger.error('Error getting enrollment references:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * 3.6.4 Manual Check for Expired Assignments
   * POST /api/pre-enrollment/plan-assignments/check-expired
   *
   * Body Parameters:
   * - yearsBack: number (optional, default: 3) - How many years back to check for performance optimization
   */
  private checkExpiredAssignments = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { yearsBack = 3 } = request.body; // Optional optimization parameter

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Validate yearsBack parameter
      if (yearsBack && (typeof yearsBack !== 'number' || yearsBack < 1 || yearsBack > 20)) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'yearsBack must be a number between 1 and 20');
      }

      // Check expired assignments using service with optimization
      const result = await PlanAssignmentService.checkExpiredAssignments(userId, user, yearsBack);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to check expired assignments');
      }

      return PlanAssignmentMiddleware.operationResult(response, {
        message: 'Expired assignments check completed',
        expiredCount: result.expiredCount,
        updatedAssignments: result.updatedAssignments,
        yearRange: result.yearRange,
        skippedHistoricalCount: result.skippedHistoricalCount,
        optimizationNote: result.optimizationNote
      });

    } catch (error) {
      logger.error('Error checking expired assignments:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  // ===== NEW MISSING APIs IMPLEMENTATION =====

  /**
   * Get Effective Assignments
   * GET /api/pre-enrollment/plan-assignments/effective
   *
   * Returns assignments that are effective on a reference date (default: today)
   *
   * Query Parameters:
   * - page: number (optional) - Page number for pagination (starts from 1)
   * - limit: number (optional) - Items per page (1-100, default: no pagination)
   * - date: ISO date string (default: current date)
   * - companyId: Filter by specific company (optional)
   * - assignmentYear: number (optional) - Filter by assignment year (e.g., 2024, 2025)
   *
   * Response Format: Same as getPlanAssignments (supports pagination)
   */
  private getEffectiveAssignments = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Get effective assignments using service
      const result = await PlanAssignmentService.getEffectiveAssignments(userId, user, request.query);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to get effective assignments');
      }

      return PlanAssignmentMiddleware.assignmentsListed(response, result);

    } catch (error) {
      logger.error('Error getting effective assignments:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * Get Enrollment Period Assignments
   * GET /api/pre-enrollment/plan-assignments/enrollment-period
   *
   * Returns assignments that are in enrollment period on a reference date (default: today)
   *
   * Query Parameters:
   * - page: number (optional) - Page number for pagination (starts from 1)
   * - limit: number (optional) - Items per page (1-100, default: no pagination)
   * - date: ISO date string (default: current date)
   * - companyId: Filter by specific company (optional)
   * - assignmentYear: number (optional) - Filter by assignment year (e.g., 2024, 2025)
   *
   * Response Format: Same as getPlanAssignments (supports pagination)
   */
  private getEnrollmentPeriodAssignments = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Get enrollment period assignments using service
      const result = await PlanAssignmentService.getEnrollmentPeriodAssignments(userId, user, request.query);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to get enrollment period assignments');
      }

      return PlanAssignmentMiddleware.assignmentsListed(response, result);

    } catch (error) {
      logger.error('Error getting enrollment period assignments:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * Reassign Plan
   * POST /api/pre-enrollment/plan-assignments/:id/reassign-plan
   *
   * Reassigns a plan assignment to a different plan
   * Body: { newPlanId: string }
   */
  private reassignPlan = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Reassign plan using service
      const result = await PlanAssignmentService.reassignPlan(id, request.body.newPlanId, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to reassign plan');
      }

      logger.info(`Plan assignment reassigned: ${id} to plan ${request.body.newPlanId} by user: ${userId}`);

      // Return the complete result with message and assignment
      return response.status(200).json({
        success: result.success,
        message: result.message,
        assignment: result.assignment
      });

    } catch (error) {
      logger.error('Error reassigning plan:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };

  /**
   * Update Time Constraints
   * PUT /api/pre-enrollment/plan-assignments/:id/time-constraints
   *
   * Updates only the time-related fields of a plan assignment
   * Body: { planEffectiveDate?, planEndDate?, enrollmentStartDate?, enrollmentEndDate? }
   */
  private updateTimeConstraints = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request as any; // From middleware
      const { id } = request.params;

      // Validate user access
      const { hasAccess, user } = await PlanAssignmentService.validateUserAccess(userId);
      if (!hasAccess) {
        return PlanAssignmentMiddleware.handleServiceError(response, 'User not found');
      }

      // Update time constraints using service
      const result = await PlanAssignmentService.updateTimeConstraints(id, request.body, userId, user);
      if (!result.success) {
        return PlanAssignmentMiddleware.handleServiceError(response, result.error || 'Failed to update time constraints');
      }

      logger.info(`Plan assignment time constraints updated: ${id} by user: ${userId}`);
      return PlanAssignmentMiddleware.timeConstraintsUpdated(response, result.assignment!);

    } catch (error) {
      logger.error('Error updating time constraints:', error);
      return PlanAssignmentMiddleware.internalError(response);
    }
  };
}

export default PlanAssignmentController;
