"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/signature_pad";
exports.ids = ["vendor-chunks/signature_pad"];
exports.modules = {

/***/ "(ssr)/./node_modules/signature_pad/dist/signature_pad.js":
/*!**********************************************************!*\
  !*** ./node_modules/signature_pad/dist/signature_pad.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignaturePad)\n/* harmony export */ });\n/*!\n * Signature Pad v5.0.9 | https://github.com/szimek/signature_pad\n * (c) 2025 Szymon Nowak | Released under the MIT license\n */\n\nclass Point {\n    constructor(x, y, pressure, time) {\n        if (isNaN(x) || isNaN(y)) {\n            throw new Error(`Point is invalid: (${x}, ${y})`);\n        }\n        this.x = +x;\n        this.y = +y;\n        this.pressure = pressure || 0;\n        this.time = time || Date.now();\n    }\n    distanceTo(start) {\n        return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));\n    }\n    equals(other) {\n        return (this.x === other.x &&\n            this.y === other.y &&\n            this.pressure === other.pressure &&\n            this.time === other.time);\n    }\n    velocityFrom(start) {\n        return this.time !== start.time\n            ? this.distanceTo(start) / (this.time - start.time)\n            : 0;\n    }\n}\n\nclass Bezier {\n    static fromPoints(points, widths) {\n        const c2 = this.calculateControlPoints(points[0], points[1], points[2]).c2;\n        const c3 = this.calculateControlPoints(points[1], points[2], points[3]).c1;\n        return new Bezier(points[1], c2, c3, points[2], widths.start, widths.end);\n    }\n    static calculateControlPoints(s1, s2, s3) {\n        const dx1 = s1.x - s2.x;\n        const dy1 = s1.y - s2.y;\n        const dx2 = s2.x - s3.x;\n        const dy2 = s2.y - s3.y;\n        const m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n        const m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n        const l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n        const l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n        const dxm = m1.x - m2.x;\n        const dym = m1.y - m2.y;\n        const k = l1 + l2 == 0 ? 0 : l2 / (l1 + l2);\n        const cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n        const tx = s2.x - cm.x;\n        const ty = s2.y - cm.y;\n        return {\n            c1: new Point(m1.x + tx, m1.y + ty),\n            c2: new Point(m2.x + tx, m2.y + ty),\n        };\n    }\n    constructor(startPoint, control2, control1, endPoint, startWidth, endWidth) {\n        this.startPoint = startPoint;\n        this.control2 = control2;\n        this.control1 = control1;\n        this.endPoint = endPoint;\n        this.startWidth = startWidth;\n        this.endWidth = endWidth;\n    }\n    length() {\n        const steps = 10;\n        let length = 0;\n        let px;\n        let py;\n        for (let i = 0; i <= steps; i += 1) {\n            const t = i / steps;\n            const cx = this.point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);\n            const cy = this.point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);\n            if (i > 0) {\n                const xdiff = cx - px;\n                const ydiff = cy - py;\n                length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n            }\n            px = cx;\n            py = cy;\n        }\n        return length;\n    }\n    point(t, start, c1, c2, end) {\n        return (start * (1.0 - t) * (1.0 - t) * (1.0 - t))\n            + (3.0 * c1 * (1.0 - t) * (1.0 - t) * t)\n            + (3.0 * c2 * (1.0 - t) * t * t)\n            + (end * t * t * t);\n    }\n}\n\nclass SignatureEventTarget {\n    constructor() {\n        try {\n            this._et = new EventTarget();\n        }\n        catch (_a) {\n            this._et = document;\n        }\n    }\n    addEventListener(type, listener, options) {\n        this._et.addEventListener(type, listener, options);\n    }\n    dispatchEvent(event) {\n        return this._et.dispatchEvent(event);\n    }\n    removeEventListener(type, callback, options) {\n        this._et.removeEventListener(type, callback, options);\n    }\n}\n\nfunction throttle(fn, wait = 250) {\n    let previous = 0;\n    let timeout = null;\n    let result;\n    let storedContext;\n    let storedArgs;\n    const later = () => {\n        previous = Date.now();\n        timeout = null;\n        result = fn.apply(storedContext, storedArgs);\n        if (!timeout) {\n            storedContext = null;\n            storedArgs = [];\n        }\n    };\n    return function wrapper(...args) {\n        const now = Date.now();\n        const remaining = wait - (now - previous);\n        storedContext = this;\n        storedArgs = args;\n        if (remaining <= 0 || remaining > wait) {\n            if (timeout) {\n                clearTimeout(timeout);\n                timeout = null;\n            }\n            previous = now;\n            result = fn.apply(storedContext, storedArgs);\n            if (!timeout) {\n                storedContext = null;\n                storedArgs = [];\n            }\n        }\n        else if (!timeout) {\n            timeout = window.setTimeout(later, remaining);\n        }\n        return result;\n    };\n}\n\nclass SignaturePad extends SignatureEventTarget {\n    constructor(canvas, options = {}) {\n        var _a, _b, _c;\n        super();\n        this.canvas = canvas;\n        this._drawingStroke = false;\n        this._isEmpty = true;\n        this._lastPoints = [];\n        this._data = [];\n        this._lastVelocity = 0;\n        this._lastWidth = 0;\n        this._handleMouseDown = (event) => {\n            if (!this._isLeftButtonPressed(event, true) || this._drawingStroke) {\n                return;\n            }\n            this._strokeBegin(this._pointerEventToSignatureEvent(event));\n        };\n        this._handleMouseMove = (event) => {\n            if (!this._isLeftButtonPressed(event, true) || !this._drawingStroke) {\n                this._strokeEnd(this._pointerEventToSignatureEvent(event), false);\n                return;\n            }\n            this._strokeMoveUpdate(this._pointerEventToSignatureEvent(event));\n        };\n        this._handleMouseUp = (event) => {\n            if (this._isLeftButtonPressed(event)) {\n                return;\n            }\n            this._strokeEnd(this._pointerEventToSignatureEvent(event));\n        };\n        this._handleTouchStart = (event) => {\n            if (event.targetTouches.length !== 1 || this._drawingStroke) {\n                return;\n            }\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            this._strokeBegin(this._touchEventToSignatureEvent(event));\n        };\n        this._handleTouchMove = (event) => {\n            if (event.targetTouches.length !== 1) {\n                return;\n            }\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            if (!this._drawingStroke) {\n                this._strokeEnd(this._touchEventToSignatureEvent(event), false);\n                return;\n            }\n            this._strokeMoveUpdate(this._touchEventToSignatureEvent(event));\n        };\n        this._handleTouchEnd = (event) => {\n            if (event.targetTouches.length !== 0) {\n                return;\n            }\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            this._strokeEnd(this._touchEventToSignatureEvent(event));\n        };\n        this._handlePointerDown = (event) => {\n            if (!event.isPrimary ||\n                !this._isLeftButtonPressed(event) ||\n                this._drawingStroke ||\n                (typeof this._strokePointerId !== 'undefined' &&\n                    this._strokePointerId !== this._getPointerId(event))) {\n                return;\n            }\n            this._strokePointerId = this._getPointerId(event);\n            event.preventDefault();\n            this._strokeBegin(this._pointerEventToSignatureEvent(event));\n        };\n        this._handlePointerMove = (event) => {\n            if (!event.isPrimary ||\n                this._strokePointerId !== this._getPointerId(event)) {\n                return;\n            }\n            if (!this._isLeftButtonPressed(event, true) || !this._drawingStroke) {\n                this._strokeEnd(this._pointerEventToSignatureEvent(event), false);\n                return;\n            }\n            event.preventDefault();\n            this._strokeMoveUpdate(this._pointerEventToSignatureEvent(event));\n        };\n        this._handlePointerUp = (event) => {\n            if (!event.isPrimary ||\n                this._isLeftButtonPressed(event) ||\n                this._strokePointerId !== this._getPointerId(event)) {\n                return;\n            }\n            this._strokePointerId = undefined;\n            event.preventDefault();\n            this._strokeEnd(this._pointerEventToSignatureEvent(event));\n        };\n        this.velocityFilterWeight = options.velocityFilterWeight || 0.7;\n        this.minWidth = options.minWidth || 0.5;\n        this.maxWidth = options.maxWidth || 2.5;\n        this.throttle = (_a = options.throttle) !== null && _a !== void 0 ? _a : 16;\n        this.minDistance = (_b = options.minDistance) !== null && _b !== void 0 ? _b : 5;\n        this.dotSize = options.dotSize || 0;\n        this.penColor = options.penColor || 'black';\n        this.backgroundColor = options.backgroundColor || 'rgba(0,0,0,0)';\n        this.compositeOperation = options.compositeOperation || 'source-over';\n        this.canvasContextOptions = (_c = options.canvasContextOptions) !== null && _c !== void 0 ? _c : {};\n        this._strokeMoveUpdate = this.throttle\n            ? throttle(SignaturePad.prototype._strokeUpdate, this.throttle)\n            : SignaturePad.prototype._strokeUpdate;\n        this._ctx = canvas.getContext('2d', this.canvasContextOptions);\n        this.clear();\n        this.on();\n    }\n    clear() {\n        const { _ctx: ctx, canvas } = this;\n        ctx.fillStyle = this.backgroundColor;\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        ctx.fillRect(0, 0, canvas.width, canvas.height);\n        this._data = [];\n        this._reset(this._getPointGroupOptions());\n        this._isEmpty = true;\n    }\n    fromDataURL(dataUrl, options = {}) {\n        return new Promise((resolve, reject) => {\n            const image = new Image();\n            const ratio = options.ratio || window.devicePixelRatio || 1;\n            const width = options.width || this.canvas.width / ratio;\n            const height = options.height || this.canvas.height / ratio;\n            const xOffset = options.xOffset || 0;\n            const yOffset = options.yOffset || 0;\n            this._reset(this._getPointGroupOptions());\n            image.onload = () => {\n                this._ctx.drawImage(image, xOffset, yOffset, width, height);\n                resolve();\n            };\n            image.onerror = (error) => {\n                reject(error);\n            };\n            image.crossOrigin = 'anonymous';\n            image.src = dataUrl;\n            this._isEmpty = false;\n        });\n    }\n    toDataURL(type = 'image/png', encoderOptions) {\n        switch (type) {\n            case 'image/svg+xml':\n                if (typeof encoderOptions !== 'object') {\n                    encoderOptions = undefined;\n                }\n                return `data:image/svg+xml;base64,${btoa(this.toSVG(encoderOptions))}`;\n            default:\n                if (typeof encoderOptions !== 'number') {\n                    encoderOptions = undefined;\n                }\n                return this.canvas.toDataURL(type, encoderOptions);\n        }\n    }\n    on() {\n        this.canvas.style.touchAction = 'none';\n        this.canvas.style.msTouchAction = 'none';\n        this.canvas.style.userSelect = 'none';\n        const isIOS = /Macintosh/.test(navigator.userAgent) && 'ontouchstart' in document;\n        if (window.PointerEvent && !isIOS) {\n            this._handlePointerEvents();\n        }\n        else {\n            this._handleMouseEvents();\n            if ('ontouchstart' in window) {\n                this._handleTouchEvents();\n            }\n        }\n    }\n    off() {\n        this.canvas.style.touchAction = 'auto';\n        this.canvas.style.msTouchAction = 'auto';\n        this.canvas.style.userSelect = 'auto';\n        this.canvas.removeEventListener('pointerdown', this._handlePointerDown);\n        this.canvas.removeEventListener('mousedown', this._handleMouseDown);\n        this.canvas.removeEventListener('touchstart', this._handleTouchStart);\n        this._removeMoveUpEventListeners();\n    }\n    _getListenerFunctions() {\n        var _a;\n        const canvasWindow = window.document === this.canvas.ownerDocument\n            ? window\n            : ((_a = this.canvas.ownerDocument.defaultView) !== null && _a !== void 0 ? _a : this.canvas.ownerDocument);\n        return {\n            addEventListener: canvasWindow.addEventListener.bind(canvasWindow),\n            removeEventListener: canvasWindow.removeEventListener.bind(canvasWindow),\n        };\n    }\n    _removeMoveUpEventListeners() {\n        const { removeEventListener } = this._getListenerFunctions();\n        removeEventListener('pointermove', this._handlePointerMove);\n        removeEventListener('pointerup', this._handlePointerUp);\n        removeEventListener('mousemove', this._handleMouseMove);\n        removeEventListener('mouseup', this._handleMouseUp);\n        removeEventListener('touchmove', this._handleTouchMove);\n        removeEventListener('touchend', this._handleTouchEnd);\n    }\n    isEmpty() {\n        return this._isEmpty;\n    }\n    fromData(pointGroups, { clear = true } = {}) {\n        if (clear) {\n            this.clear();\n        }\n        this._fromData(pointGroups, this._drawCurve.bind(this), this._drawDot.bind(this));\n        this._data = this._data.concat(pointGroups);\n    }\n    toData() {\n        return this._data;\n    }\n    _isLeftButtonPressed(event, only) {\n        if (only) {\n            return event.buttons === 1;\n        }\n        return (event.buttons & 1) === 1;\n    }\n    _pointerEventToSignatureEvent(event) {\n        return {\n            event: event,\n            type: event.type,\n            x: event.clientX,\n            y: event.clientY,\n            pressure: 'pressure' in event ? event.pressure : 0,\n        };\n    }\n    _touchEventToSignatureEvent(event) {\n        const touch = event.changedTouches[0];\n        return {\n            event: event,\n            type: event.type,\n            x: touch.clientX,\n            y: touch.clientY,\n            pressure: touch.force,\n        };\n    }\n    _getPointerId(event) {\n        return event.persistentDeviceId || event.pointerId;\n    }\n    _getPointGroupOptions(group) {\n        return {\n            penColor: group && 'penColor' in group ? group.penColor : this.penColor,\n            dotSize: group && 'dotSize' in group ? group.dotSize : this.dotSize,\n            minWidth: group && 'minWidth' in group ? group.minWidth : this.minWidth,\n            maxWidth: group && 'maxWidth' in group ? group.maxWidth : this.maxWidth,\n            velocityFilterWeight: group && 'velocityFilterWeight' in group\n                ? group.velocityFilterWeight\n                : this.velocityFilterWeight,\n            compositeOperation: group && 'compositeOperation' in group\n                ? group.compositeOperation\n                : this.compositeOperation,\n        };\n    }\n    _strokeBegin(event) {\n        const cancelled = !this.dispatchEvent(new CustomEvent('beginStroke', { detail: event, cancelable: true }));\n        if (cancelled) {\n            return;\n        }\n        const { addEventListener } = this._getListenerFunctions();\n        switch (event.event.type) {\n            case 'mousedown':\n                addEventListener('mousemove', this._handleMouseMove, {\n                    passive: false,\n                });\n                addEventListener('mouseup', this._handleMouseUp, { passive: false });\n                break;\n            case 'touchstart':\n                addEventListener('touchmove', this._handleTouchMove, {\n                    passive: false,\n                });\n                addEventListener('touchend', this._handleTouchEnd, { passive: false });\n                break;\n            case 'pointerdown':\n                addEventListener('pointermove', this._handlePointerMove, {\n                    passive: false,\n                });\n                addEventListener('pointerup', this._handlePointerUp, {\n                    passive: false,\n                });\n                break;\n        }\n        this._drawingStroke = true;\n        const pointGroupOptions = this._getPointGroupOptions();\n        const newPointGroup = Object.assign(Object.assign({}, pointGroupOptions), { points: [] });\n        this._data.push(newPointGroup);\n        this._reset(pointGroupOptions);\n        this._strokeUpdate(event);\n    }\n    _strokeUpdate(event) {\n        if (!this._drawingStroke) {\n            return;\n        }\n        if (this._data.length === 0) {\n            this._strokeBegin(event);\n            return;\n        }\n        this.dispatchEvent(new CustomEvent('beforeUpdateStroke', { detail: event }));\n        const point = this._createPoint(event.x, event.y, event.pressure);\n        const lastPointGroup = this._data[this._data.length - 1];\n        const lastPoints = lastPointGroup.points;\n        const lastPoint = lastPoints.length > 0 && lastPoints[lastPoints.length - 1];\n        const isLastPointTooClose = lastPoint\n            ? point.distanceTo(lastPoint) <= this.minDistance\n            : false;\n        const pointGroupOptions = this._getPointGroupOptions(lastPointGroup);\n        if (!lastPoint || !(lastPoint && isLastPointTooClose)) {\n            const curve = this._addPoint(point, pointGroupOptions);\n            if (!lastPoint) {\n                this._drawDot(point, pointGroupOptions);\n            }\n            else if (curve) {\n                this._drawCurve(curve, pointGroupOptions);\n            }\n            lastPoints.push({\n                time: point.time,\n                x: point.x,\n                y: point.y,\n                pressure: point.pressure,\n            });\n        }\n        this.dispatchEvent(new CustomEvent('afterUpdateStroke', { detail: event }));\n    }\n    _strokeEnd(event, shouldUpdate = true) {\n        this._removeMoveUpEventListeners();\n        if (!this._drawingStroke) {\n            return;\n        }\n        if (shouldUpdate) {\n            this._strokeUpdate(event);\n        }\n        this._drawingStroke = false;\n        this.dispatchEvent(new CustomEvent('endStroke', { detail: event }));\n    }\n    _handlePointerEvents() {\n        this._drawingStroke = false;\n        this.canvas.addEventListener('pointerdown', this._handlePointerDown, {\n            passive: false,\n        });\n    }\n    _handleMouseEvents() {\n        this._drawingStroke = false;\n        this.canvas.addEventListener('mousedown', this._handleMouseDown, {\n            passive: false,\n        });\n    }\n    _handleTouchEvents() {\n        this.canvas.addEventListener('touchstart', this._handleTouchStart, {\n            passive: false,\n        });\n    }\n    _reset(options) {\n        this._lastPoints = [];\n        this._lastVelocity = 0;\n        this._lastWidth = (options.minWidth + options.maxWidth) / 2;\n        this._ctx.fillStyle = options.penColor;\n        this._ctx.globalCompositeOperation = options.compositeOperation;\n    }\n    _createPoint(x, y, pressure) {\n        const rect = this.canvas.getBoundingClientRect();\n        return new Point(x - rect.left, y - rect.top, pressure, new Date().getTime());\n    }\n    _addPoint(point, options) {\n        const { _lastPoints } = this;\n        _lastPoints.push(point);\n        if (_lastPoints.length > 2) {\n            if (_lastPoints.length === 3) {\n                _lastPoints.unshift(_lastPoints[0]);\n            }\n            const widths = this._calculateCurveWidths(_lastPoints[1], _lastPoints[2], options);\n            const curve = Bezier.fromPoints(_lastPoints, widths);\n            _lastPoints.shift();\n            return curve;\n        }\n        return null;\n    }\n    _calculateCurveWidths(startPoint, endPoint, options) {\n        const velocity = options.velocityFilterWeight * endPoint.velocityFrom(startPoint) +\n            (1 - options.velocityFilterWeight) * this._lastVelocity;\n        const newWidth = this._strokeWidth(velocity, options);\n        const widths = {\n            end: newWidth,\n            start: this._lastWidth,\n        };\n        this._lastVelocity = velocity;\n        this._lastWidth = newWidth;\n        return widths;\n    }\n    _strokeWidth(velocity, options) {\n        return Math.max(options.maxWidth / (velocity + 1), options.minWidth);\n    }\n    _drawCurveSegment(x, y, width) {\n        const ctx = this._ctx;\n        ctx.moveTo(x, y);\n        ctx.arc(x, y, width, 0, 2 * Math.PI, false);\n        this._isEmpty = false;\n    }\n    _drawCurve(curve, options) {\n        const ctx = this._ctx;\n        const widthDelta = curve.endWidth - curve.startWidth;\n        const drawSteps = Math.ceil(curve.length()) * 2;\n        ctx.beginPath();\n        ctx.fillStyle = options.penColor;\n        for (let i = 0; i < drawSteps; i += 1) {\n            const t = i / drawSteps;\n            const tt = t * t;\n            const ttt = tt * t;\n            const u = 1 - t;\n            const uu = u * u;\n            const uuu = uu * u;\n            let x = uuu * curve.startPoint.x;\n            x += 3 * uu * t * curve.control1.x;\n            x += 3 * u * tt * curve.control2.x;\n            x += ttt * curve.endPoint.x;\n            let y = uuu * curve.startPoint.y;\n            y += 3 * uu * t * curve.control1.y;\n            y += 3 * u * tt * curve.control2.y;\n            y += ttt * curve.endPoint.y;\n            const width = Math.min(curve.startWidth + ttt * widthDelta, options.maxWidth);\n            this._drawCurveSegment(x, y, width);\n        }\n        ctx.closePath();\n        ctx.fill();\n    }\n    _drawDot(point, options) {\n        const ctx = this._ctx;\n        const width = options.dotSize > 0\n            ? options.dotSize\n            : (options.minWidth + options.maxWidth) / 2;\n        ctx.beginPath();\n        this._drawCurveSegment(point.x, point.y, width);\n        ctx.closePath();\n        ctx.fillStyle = options.penColor;\n        ctx.fill();\n    }\n    _fromData(pointGroups, drawCurve, drawDot) {\n        for (const group of pointGroups) {\n            const { points } = group;\n            const pointGroupOptions = this._getPointGroupOptions(group);\n            if (points.length > 1) {\n                for (let j = 0; j < points.length; j += 1) {\n                    const basicPoint = points[j];\n                    const point = new Point(basicPoint.x, basicPoint.y, basicPoint.pressure, basicPoint.time);\n                    if (j === 0) {\n                        this._reset(pointGroupOptions);\n                    }\n                    const curve = this._addPoint(point, pointGroupOptions);\n                    if (curve) {\n                        drawCurve(curve, pointGroupOptions);\n                    }\n                }\n            }\n            else {\n                this._reset(pointGroupOptions);\n                drawDot(points[0], pointGroupOptions);\n            }\n        }\n    }\n    toSVG({ includeBackgroundColor = false } = {}) {\n        const pointGroups = this._data;\n        const ratio = Math.max(window.devicePixelRatio || 1, 1);\n        const minX = 0;\n        const minY = 0;\n        const maxX = this.canvas.width / ratio;\n        const maxY = this.canvas.height / ratio;\n        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n        svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n        svg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');\n        svg.setAttribute('viewBox', `${minX} ${minY} ${maxX} ${maxY}`);\n        svg.setAttribute('width', maxX.toString());\n        svg.setAttribute('height', maxY.toString());\n        if (includeBackgroundColor && this.backgroundColor) {\n            const rect = document.createElement('rect');\n            rect.setAttribute('width', '100%');\n            rect.setAttribute('height', '100%');\n            rect.setAttribute('fill', this.backgroundColor);\n            svg.appendChild(rect);\n        }\n        this._fromData(pointGroups, (curve, { penColor }) => {\n            const path = document.createElement('path');\n            if (!isNaN(curve.control1.x) &&\n                !isNaN(curve.control1.y) &&\n                !isNaN(curve.control2.x) &&\n                !isNaN(curve.control2.y)) {\n                const attr = `M ${curve.startPoint.x.toFixed(3)},${curve.startPoint.y.toFixed(3)} ` +\n                    `C ${curve.control1.x.toFixed(3)},${curve.control1.y.toFixed(3)} ` +\n                    `${curve.control2.x.toFixed(3)},${curve.control2.y.toFixed(3)} ` +\n                    `${curve.endPoint.x.toFixed(3)},${curve.endPoint.y.toFixed(3)}`;\n                path.setAttribute('d', attr);\n                path.setAttribute('stroke-width', (curve.endWidth * 2.25).toFixed(3));\n                path.setAttribute('stroke', penColor);\n                path.setAttribute('fill', 'none');\n                path.setAttribute('stroke-linecap', 'round');\n                svg.appendChild(path);\n            }\n        }, (point, { penColor, dotSize, minWidth, maxWidth }) => {\n            const circle = document.createElement('circle');\n            const size = dotSize > 0 ? dotSize : (minWidth + maxWidth) / 2;\n            circle.setAttribute('r', size.toString());\n            circle.setAttribute('cx', point.x.toString());\n            circle.setAttribute('cy', point.y.toString());\n            circle.setAttribute('fill', penColor);\n            svg.appendChild(circle);\n        });\n        return svg.outerHTML;\n    }\n}\n\n\n//# sourceMappingURL=signature_pad.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/signature_pad/dist/signature_pad.js\n");

/***/ })

};
;