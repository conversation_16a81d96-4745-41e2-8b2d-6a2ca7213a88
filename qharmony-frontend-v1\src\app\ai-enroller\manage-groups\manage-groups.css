.manage-groups-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-icon {
  color: #7c3aed;
}

.page-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.add-new-group-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #000000;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-new-group-btn:hover {
  transform: translateY(-1px);
  background: #1f2937;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Search */
.search-container {
  margin-bottom: 2rem;
}

.search-input-wrapper {
  position: relative;
  max-width: 100%;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 3rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  background: white;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #000000;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

/* Companies List */
.companies-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.company-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.company-card:hover {
  border-color: #000000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.company-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0rem;
}

.company-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.select-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-btn:hover {
  background: #000000;
  color: white;
  border-color: #000000;
}

.company-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.industry-tag {
  display: inline-block;
  background: #f1f5f9;
  color: #475569;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  width: fit-content;
}

.company-stats {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 14px;
}

.stat svg {
  color: #94a3b8;
}

.plans-assigned {
  color: #000000;
  font-weight: 500;
}

/* No Companies */
.no-companies {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.no-companies svg {
  color: #d1d5db;
  margin-bottom: 1rem;
}

.no-companies h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.no-companies p {
  margin: 0;
  font-size: 14px;
}

/* Back Button */
.back-button-container {
  display: flex;
  justify-content: flex-start;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background: #f8fafc;
  color: #475569;
  border-color: #cbd5e1;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Broker Dashboard Styles */
.broker-dashboard {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  background: #fafafa;
  min-height: 100vh;
}

.dashboard-header {
  background: transparent;
  color: #1f2937;
  padding: 2rem 0;
  margin-bottom: 2rem;
  text-align: left;
}

/* Header content uses design system classes - page-title and subtitle-text */
.header-content h1 {
  margin: 0 0 0.5rem 0;
  color: #1f2937 !important;
  text-align: left !important;
  font-size: 30px !important;
  line-height: 36px !important;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #6b7280 !important;
  text-align: left !important;
  font-size: 16px;
  line-height: 1.5;
}

/* Specific rule for Broker Dashboard title left alignment */
.dashboard-header .page-title,
.dashboard-header h1,
.dashboard-header .header-content h1 {
  text-align: left !important;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.stat-icon {
  background: #ede9fe;
  border-radius: 12px;
  padding: 12px;
  color: #7c3aed;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 3rem;
}

/* Quick actions uses design system classes - section-header and body-text */
.quick-actions h2 {
  margin: 0 0 0.5rem 0;
}

.quick-actions > p {
  margin: 0 0 2rem 0;
}

.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.action-card:hover {
  border-color: #000000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.action-icon {
  background: #ede9fe;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  color: #7c3aed;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

/* Action content uses design system classes - section-header and body-text */
.action-content h3 {
  margin: 0 0 0.5rem 0;
}

.action-content p {
  margin: 0 0 1rem 0;
}

.action-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* Select Company Page Styles */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 14px;
}

.breadcrumb-link {
  color: #000000;
  cursor: pointer;
  text-decoration: none;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  color: #9ca3af;
}

.breadcrumb-current {
  color: #6b7280;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.header-text p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.company-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.assign-plans-btn {
  background: #000000;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.assign-plans-btn:hover {
  transform: translateY(-1px);
  background: #1f2937;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.company-meta {
  display: flex;
  gap: 1rem;
  font-size: 12px;
  color: #6b7280;
}

.company-ein {
  font-family: monospace;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

/* Responsive */
@media (max-width: 768px) {
  .manage-groups-page,
  .broker-dashboard {
    padding: 1rem;
  }

  .dashboard-header {
    padding: 2rem 1rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .header-content p {
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-cards {
    grid-template-columns: 1fr;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .company-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .company-stats {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .company-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .company-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
