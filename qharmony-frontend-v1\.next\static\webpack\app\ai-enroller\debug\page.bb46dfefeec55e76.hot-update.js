"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/debug/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/debug/page.tsx":
/*!********************************************!*\
  !*** ./src/app/ai-enroller/debug/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DebugPage() {\n    _s();\n    const [localStorageData, setLocalStorageData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get all localStorage data\n        const data = {};\n        for(let i = 0; i < localStorage.length; i++){\n            const key = localStorage.key(i);\n            if (key) {\n                data[key] = localStorage.getItem(key) || \"\";\n            }\n        }\n        setLocalStorageData(data);\n    }, []);\n    const clearUserData = ()=>{\n        localStorage.removeItem(\"userid1\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"user\");\n        // Set the ACTUAL user ID that exists in the database\n        localStorage.setItem(\"userid1\", \"67bf65bf50bad0a4b3d805bc\");\n        localStorage.setItem(\"companyId1\", \"67bf65bf50bad0a4b3d805ba\");\n        // Refresh the data\n        const data = {};\n        for(let i = 0; i < localStorage.length; i++){\n            const key = localStorage.key(i);\n            if (key) {\n                data[key] = localStorage.getItem(key) || \"\";\n            }\n        }\n        setLocalStorageData(data);\n        alert(\"User data cleared and reset to DATABASE user ID: 67bf65bf50bad0a4b3d805bc\");\n    };\n    const testDirectAPI = async ()=>{\n        const userId = localStorage.getItem(\"userid1\");\n        const companyId = localStorage.getItem(\"companyId1\");\n        if (!userId || !companyId) {\n            alert(\"Missing userId or companyId in localStorage\");\n            return;\n        }\n        try {\n            var _result_assignments;\n            // Test direct API call\n            const response = await fetch(\"http://localhost:8080/api/pre-enrollment/plan-assignments/company/\".concat(companyId, \"?includePlanData=true\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD0D Direct API Test Result:\", {\n                status: response.status,\n                ok: response.ok,\n                result\n            });\n            alert(\"API Test Result:\\nStatus: \".concat(response.status, \"\\nAssignments: \").concat(((_result_assignments = result.assignments) === null || _result_assignments === void 0 ? void 0 : _result_assignments.length) || 0, \"\\nTotal Count: \").concat(result.totalCount || 0, \"\\nApplied Filters: \").concat(JSON.stringify(result.appliedFilters)));\n        } catch (error) {\n            console.error(\"API Test Error:\", error);\n            alert(\"API Test Error: \".concat(error.message));\n        }\n    };\n    const testUserAPI = async ()=>{\n        const userId = localStorage.getItem(\"userid1\");\n        if (!userId) {\n            alert(\"Missing userId in localStorage\");\n            return;\n        }\n        try {\n            // Test user API to see what user data the backend has\n            const response = await fetch(\"http://localhost:8080/api/users/\".concat(userId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD0D User API Test Result:\", {\n                status: response.status,\n                ok: response.ok,\n                result\n            });\n            if (response.ok) {\n                alert(\"User API Result:\\nName: \".concat(result.name, \"\\nEmail: \").concat(result.email, \"\\nCompany ID: \").concat(result.companyId, \"\\nisBroker: \").concat(result.isBroker, \"\\nisAdmin: \").concat(result.isAdmin));\n            } else {\n                alert(\"User API Error:\\nStatus: \".concat(response.status, \"\\nError: \").concat(JSON.stringify(result)));\n            }\n        } catch (error) {\n            console.error(\"User API Test Error:\", error);\n            alert(\"User API Test Error: \".concat(error.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"monospace\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Debug - LocalStorage Data & API Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\",\n                    display: \"flex\",\n                    gap: \"10px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearUserData,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#007bff\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Clear & Reset User Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: testDirectAPI,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#28a745\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Test Direct API Call\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: testUserAPI,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#ffc107\",\n                            color: \"black\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Test User API\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Current LocalStorage Contents:\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                style: {\n                    backgroundColor: \"#f5f5f5\",\n                    padding: \"10px\",\n                    borderRadius: \"4px\"\n                },\n                children: JSON.stringify(localStorageData, null, 2)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Broker Access Analysis:\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: \"#f8f9fa\",\n                    padding: \"15px\",\n                    borderRadius: \"4px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Issue:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 12\n                            }, this),\n                            \" Applied Filters show no broker-specific filtering\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Expected:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                children: '[\"activeOnly\", \"companyId:...\", \"brokerAccess:...\"]'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 39\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Actual:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                children: '[\"activeOnly\", \"companyId:6838677aef6db0212bcfdacb\"]'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Broker Access Conditions (both must be true):\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"brokerAssignments.length > 0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" - Broker owns plans assigned to this company\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"isOwnCompany\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" - user.companyId === companyId\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Your Data:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"User ID: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: localStorage.getItem(\"userid1\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"Company ID: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: localStorage.getItem(\"companyId1\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 27\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"From localStorage: User company should match target company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Likely Issue:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 12\n                            }, this),\n                            \" The \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                children: \"getBrokerAssignmentsForCompany()\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 47\n                            }, this),\n                            \" method is returning empty array, AND the company ID comparison is failing.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Current User ID from getUserId():\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"userid1: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"userid1\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 18\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 81\n                    }, this),\n                    \"userId: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"userId\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 79\n                    }, this),\n                    \"companyId1: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"companyId1\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugPage, \"aIby2hUaZfOHmhr0X+VFs6100zo=\");\n_c = DebugPage;\nvar _c;\n$RefreshReg$(_c, \"DebugPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/debug/page.tsx\n"));

/***/ })

});