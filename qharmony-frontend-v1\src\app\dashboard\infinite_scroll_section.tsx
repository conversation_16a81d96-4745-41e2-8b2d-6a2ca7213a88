import React, { useState, useEffect, useRef } from "react";
import { Box, Typography, CircularProgress } from "@mui/material";

// Define the types for your section items
interface Section {
  label: string;
  icon: React.ReactNode;
  description?: string;
}

interface InfiniteScrollComponentProps {
  sections1: Section[];
}

const InfiniteScrollComponent: React.FC<InfiniteScrollComponentProps> = ({
  sections1,
}) => {
  const [visibleSections, setVisibleSections] = useState<Section[]>(
    sections1.slice(0, 10),
  ); // Start with a slice of the sections
  const observerRef = useRef<HTMLDivElement | null>(null);

  const handleLoadMore = () => {
    // Append more sections when called
    setVisibleSections((prevSections) => [
      ...prevSections,
      ...sections1.slice(prevSections.length, prevSections.length + 10),
    ]);
  };

  useEffect(() => {
    // IntersectionObserver callback function
    const observerCallback: IntersectionObserverCallback = (entries) => {
      const [entry] = entries;
      if (entry.isIntersecting) {
        handleLoadMore();
      }
    };

    // Create the observer
    const observer = new IntersectionObserver(observerCallback, {
      root: null, // Default is the browser viewport
      threshold: 1.0, // Trigger when 100% of the target is visible
    });

    // Attach observer to the ref element (the "load more" trigger)
    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    // Cleanup the observer on unmount
    return () => {
      if (observerRef.current) {
        observer.unobserve(observerRef.current);
      }
    };
  }, []);

  return (
    <Box sx={{ overflowY: "auto", maxHeight: "80vh" }}>
      {visibleSections.map((item, index) => (
        <Box
          key={index}
          onClick={() => handleFAQClick(item.label)}
          sx={{
            display: "flex",
            alignItems: "center",
            px: 2,
            py: 1,
            mx: 1,
            borderRadius: "12px",
            bgcolor: "#F6F6F6",
            minWidth: "300px",
            cursor: "pointer",
            flexShrink: 0, // Prevent shrinking
            overflow: "hidden", // Ensure text stays inside
            textOverflow: "ellipsis", // Add ellipsis if text is too long
            "&:hover": {
              backgroundColor: "#e9ecef",
            },
          }}
        >
          <Typography
            variant="body1"
            sx={{ fontSize: "40px", marginRight: "10px" }}
          >
            {item.icon}
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography
              variant="body1"
              sx={{
                fontWeight: 800,
                fontSize: "16px",
                whiteSpace: "nowrap", // Prevent line breaks
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {item.label}
            </Typography>
            {item.description && (
              <Typography
                variant="caption"
                sx={{
                  fontWeight: 500,
                  color: "#6c757d",
                  fontSize: "13px",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
              >
                {item.description}
              </Typography>
            )}
          </Box>
        </Box>
      ))}
      {/* Load more trigger */}
      <Box ref={observerRef} sx={{ height: "50px", textAlign: "center" }}>
        <CircularProgress />
      </Box>
    </Box>
  );
};

export default InfiniteScrollComponent;

// Helper function - you may already have this, but adding here for completeness
const handleFAQClick = (label: string): void => {
  console.log(`FAQ clicked: ${label}`);
};
