"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/employee-enrol/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/manage-groups/services/planAssignmentApi.ts":
/*!*************************************************************************!*\
  !*** ./src/app/ai-enroller/manage-groups/services/planAssignmentApi.ts ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activatePlanAssignment: function() { return /* binding */ activatePlanAssignment; },\n/* harmony export */   canEditPlanAssignment: function() { return /* binding */ canEditPlanAssignment; },\n/* harmony export */   clonePlanAssignment: function() { return /* binding */ clonePlanAssignment; },\n/* harmony export */   createPlanAssignment: function() { return /* binding */ createPlanAssignment; },\n/* harmony export */   deactivatePlanAssignment: function() { return /* binding */ deactivatePlanAssignment; },\n/* harmony export */   deletePlanAssignment: function() { return /* binding */ deletePlanAssignment; },\n/* harmony export */   getActivePlanAssignmentsByCompany: function() { return /* binding */ getActivePlanAssignmentsByCompany; },\n/* harmony export */   getAssignablePlans: function() { return /* binding */ getAssignablePlans; },\n/* harmony export */   getBrokerPlanAssignmentsCount: function() { return /* binding */ getBrokerPlanAssignmentsCount; },\n/* harmony export */   getPlanAssignmentsByCompany: function() { return /* binding */ getPlanAssignmentsByCompany; },\n/* harmony export */   updatePlanAssignment: function() { return /* binding */ updatePlanAssignment; }\n/* harmony export */ });\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../utils/env */ \"(app-pages-browser)/./src/utils/env.ts\");\n\n// API configuration\nconst API_BASE_URL = (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getApiBaseUrl)();\nconst getHeaders = ()=>({\n        \"Content-Type\": \"application/json\",\n        \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getUserId)()\n    });\n// Get plan assignments for a company with optimized API\nconst getPlanAssignmentsByCompany = async function(companyId) {\n    let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, pagination = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getUserId)();\n        console.log(\"\\uD83D\\uDD0D Plan Assignment API Debug:\", {\n            companyId,\n            userId,\n            filters,\n            pagination,\n            apiBaseUrl: API_BASE_URL\n        });\n        const queryParams = new URLSearchParams();\n        // Add filters to query params\n        if (filters.status) queryParams.append(\"status\", filters.status);\n        if (filters.planId) queryParams.append(\"planId\", filters.planId);\n        if (filters.assignmentYear) queryParams.append(\"assignmentYear\", filters.assignmentYear.toString());\n        if (filters.referenceDate) queryParams.append(\"referenceDate\", filters.referenceDate);\n        if (filters.includePlanData !== undefined) queryParams.append(\"includePlanData\", filters.includePlanData.toString());\n        if (filters.enrollmentPeriodOnly) queryParams.append(\"enrollmentPeriodOnly\", \"true\");\n        if (filters.effectiveOnly) queryParams.append(\"effectiveOnly\", \"true\");\n        if (filters.futureOnly) queryParams.append(\"futureOnly\", \"true\");\n        if (filters.includeInactive) queryParams.append(\"includeInactive\", \"true\");\n        if (filters.includeExpired) queryParams.append(\"includeExpired\", \"true\");\n        if (filters.brokerId) queryParams.append(\"brokerId\", filters.brokerId);\n        // Add pagination if provided\n        if (pagination) {\n            queryParams.append(\"page\", pagination.page.toString());\n            queryParams.append(\"limit\", pagination.limit.toString());\n        }\n        const url = \"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/company/\").concat(companyId).concat(queryParams.toString() ? \"?\".concat(queryParams.toString()) : \"\");\n        console.log(\"\\uD83D\\uDCE1 Fetching plan assignments from optimized API:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        console.log(\"Plan assignments API response status:\", response.status);\n        if (!response.ok) {\n            let errorMessage = \"HTTP error! status: \".concat(response.status);\n            if (response.status === 403) {\n                const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getUserId)();\n                console.error(\"\\uD83D\\uDEAB 403 Forbidden Error Details:\", {\n                    url,\n                    userId,\n                    companyId,\n                    userIdSource: localStorage.getItem(\"userid1\") ? \"userid1\" : localStorage.getItem(\"userId\") ? \"userId\" : \"none\"\n                });\n                // For 403 errors, return a special response that indicates no access but allows page to load\n                console.log(\"\\uD83D\\uDD27 Broker has no existing plan assignments for this company - returning empty result to allow plan creation\");\n                return {\n                    success: true,\n                    data: {\n                        assignments: [],\n                        count: 0,\n                        message: \"No existing plan assignments. You can create new plan assignments for this company.\",\n                        canCreateAssignments: true,\n                        accessDeniedToExisting: true\n                    }\n                };\n            }\n            try {\n                const errorData = await response.json();\n                console.error(\"API Error Response:\", errorData);\n                errorMessage += \" - \".concat(errorData.error || errorData.message || \"Unknown error\");\n            } catch (e) {\n                console.log(\"No additional error details available\");\n            }\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        console.log(\"Plan assignments result:\", result);\n        console.log(\"First assignment details:\", result.assignments[0]);\n        return {\n            success: true,\n            data: result\n        };\n    } catch (error) {\n        console.error(\"Error fetching plan assignments:\", error);\n        return {\n            success: false,\n            error: \"Failed to fetch plan assignments\"\n        };\n    }\n};\n// Get active plan assignments for a company\nconst getActivePlanAssignmentsByCompany = async (companyId, referenceDate)=>{\n    try {\n        const queryParams = new URLSearchParams();\n        queryParams.append(\"includeExpired\", \"false\");\n        if (referenceDate) {\n            queryParams.append(\"referenceDate\", referenceDate);\n        }\n        const url = \"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/company/\").concat(companyId, \"?\").concat(queryParams.toString());\n        console.log(\"Fetching active plan assignments from:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: result\n        };\n    } catch (error) {\n        console.error(\"Error fetching active plan assignments:\", error);\n        return {\n            success: false,\n            error: \"Failed to fetch active plan assignments\"\n        };\n    }\n};\n// Create a new plan assignment\nconst createPlanAssignment = async (assignmentData)=>{\n    try {\n        console.log(\"Creating plan assignment with data:\", assignmentData);\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments\"), {\n            method: \"POST\",\n            headers: getHeaders(),\n            body: JSON.stringify(assignmentData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            const errorMessage = errorData.error || errorData.message || \"HTTP error! status: \".concat(response.status);\n            const errorDetails = errorData.details || errorData.required || [];\n            console.error(\"Plan assignment creation failed:\", {\n                status: response.status,\n                error: errorMessage,\n                details: errorDetails,\n                data: assignmentData\n            });\n            return {\n                success: false,\n                error: \"\".concat(errorMessage).concat(errorDetails.length > 0 ? \" - \".concat(errorDetails.join(\", \")) : \"\")\n            };\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: result.assignment\n        };\n    } catch (error) {\n        console.error(\"Error creating plan assignment:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to create plan assignment\"\n        };\n    }\n};\n// Update a plan assignment\nconst updatePlanAssignment = async (assignmentId, updateData)=>{\n    try {\n        console.log(\"Updating plan assignment:\", assignmentId, \"with data:\", updateData);\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId), {\n            method: \"PUT\",\n            headers: getHeaders(),\n            body: JSON.stringify(updateData)\n        });\n        console.log(\"Update response status:\", response.status);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            const errorMessage = errorData.error || errorData.message || \"HTTP error! status: \".concat(response.status);\n            const errorDetails = errorData.details || [];\n            console.error(\"Plan assignment update failed:\", {\n                status: response.status,\n                error: errorMessage,\n                details: errorDetails,\n                updateData: updateData\n            });\n            return {\n                success: false,\n                error: \"\".concat(errorMessage).concat(errorDetails.length > 0 ? \" - \".concat(errorDetails.join(\", \")) : \"\")\n            };\n        }\n        const result = await response.json();\n        console.log(\"Update successful:\", result);\n        return {\n            success: true,\n            data: result.assignment\n        };\n    } catch (error) {\n        console.error(\"Error updating plan assignment:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to update plan assignment\"\n        };\n    }\n};\n// Activate a plan assignment\nconst activatePlanAssignment = async (assignmentId)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId, \"/activate\"), {\n            method: \"POST\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: {\n                message: result.message || \"Plan assignment activated successfully\"\n            }\n        };\n    } catch (error) {\n        console.error(\"Error activating plan assignment:\", error);\n        return {\n            success: false,\n            error: \"Failed to activate plan assignment\"\n        };\n    }\n};\n// Deactivate a plan assignment\nconst deactivatePlanAssignment = async (assignmentId)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId, \"/deactivate\"), {\n            method: \"POST\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: {\n                message: result.message || \"Plan assignment deactivated successfully\"\n            }\n        };\n    } catch (error) {\n        console.error(\"Error deactivating plan assignment:\", error);\n        return {\n            success: false,\n            error: \"Failed to deactivate plan assignment\"\n        };\n    }\n};\n// Delete a plan assignment\nconst deletePlanAssignment = async (assignmentId)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId), {\n            method: \"DELETE\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: {\n                message: result.message || \"Plan assignment deleted successfully\"\n            }\n        };\n    } catch (error) {\n        console.error(\"Error deleting plan assignment:\", error);\n        return {\n            success: false,\n            error: \"Failed to delete plan assignment\"\n        };\n    }\n};\n// Clone a plan assignment\nconst clonePlanAssignment = async (assignmentId, cloneData)=>{\n    try {\n        console.log(\"Cloning plan assignment:\", assignmentId, \"with overrides:\", cloneData);\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId, \"/clone\"), {\n            method: \"POST\",\n            headers: getHeaders(),\n            body: JSON.stringify({\n                overrides: cloneData || {}\n            })\n        });\n        console.log(\"Clone response status:\", response.status);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            const errorMessage = errorData.error || errorData.message || \"HTTP error! status: \".concat(response.status);\n            const errorDetails = errorData.details || [];\n            console.error(\"Plan assignment clone failed:\", {\n                status: response.status,\n                error: errorMessage,\n                details: errorDetails,\n                cloneData: cloneData\n            });\n            return {\n                success: false,\n                error: \"\".concat(errorMessage).concat(errorDetails.length > 0 ? \" - \".concat(errorDetails.join(\", \")) : \"\")\n            };\n        }\n        const result = await response.json();\n        console.log(\"Clone successful:\", result);\n        return {\n            success: true,\n            data: result.assignment\n        };\n    } catch (error) {\n        console.error(\"Error cloning plan assignment:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to clone plan assignment\"\n        };\n    }\n};\n// Check if plan assignment can be edited\nconst canEditPlanAssignment = async (assignmentId)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId, \"/can-edit\"), {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: result\n        };\n    } catch (error) {\n        console.error(\"Error checking if plan assignment can be edited:\", error);\n        return {\n            success: false,\n            error: \"Failed to check edit permissions\"\n        };\n    }\n};\n// Get assignable plans (Active plans only)\nconst getAssignablePlans = async ()=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/assignable\"), {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        // Backend returns { plans: [...] } format for assignable plans\n        return {\n            success: true,\n            data: result.plans || []\n        };\n    } catch (error) {\n        console.error(\"Error fetching assignable plans:\", error);\n        return {\n            success: false,\n            error: \"Failed to fetch assignable plans\"\n        };\n    }\n};\n// Get aggregated plan assignments count for broker dashboard\nconst getBrokerPlanAssignmentsCount = async ()=>{\n    try {\n        const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getUserId)();\n        console.log(\"\\uD83D\\uDD0D Fetching broker plan assignments count for userId:\", userId);\n        // Use the optimized general API without pagination to get all assignments\n        const url = \"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments?includePlanData=false\");\n        console.log(\"\\uD83D\\uDCE1 Fetching broker assignments count from:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        console.log(\"Broker assignments count API response status:\", response.status);\n        if (response.ok) {\n            const data = await response.json();\n            console.log(\"✅ Broker assignments count response:\", data);\n            return {\n                success: true,\n                data: {\n                    count: data.count || 0\n                },\n                error: null\n            };\n        } else {\n            const errorText = await response.text();\n            console.error(\"❌ Failed to fetch broker assignments count:\", response.status, errorText);\n            return {\n                success: false,\n                data: null,\n                error: \"Failed to fetch assignments count: \".concat(response.status)\n            };\n        }\n    } catch (error) {\n        console.error(\"❌ Error fetching broker assignments count:\", error);\n        return {\n            success: false,\n            data: null,\n            error: \"Network error while fetching assignments count\"\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/manage-groups/services/planAssignmentApi.ts\n"));

/***/ })

});