import { getApiBaseUrl, getUserId } from '../../../../utils/env';

// API configuration
const API_BASE_URL = getApiBaseUrl();

const getHeaders = () => {
  const userId = getUserId();
  console.log('🔍 BULK WAIVE API - User ID for headers:', userId);
  return {
    'Content-Type': 'application/json',
    'user-id': userId,
  };
};

export interface BulkWaiveRequest {
  employeeId: string;
  planAssignmentIds: string[];
  waiveReason: string;
  waiveDate?: string;
  enrollmentType?: string;
}

export interface BulkWaiveResponse {
  success: boolean;
  message: string;
  createdEnrollments?: Array<{
    enrollmentId: string;
    planAssignmentId: string;
    planName: string;
    coverageType: string;
  }>;
  errors?: Array<{
    planAssignmentId: string;
    error: string;
  }>;
  waiveReason: string;
  waiveDate: string;
  enrollmentType: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Bulk waive multiple plan assignments for an employee
 */
export const bulkWaiveEnrollments = async (
  request: BulkWaiveRequest
): Promise<ApiResponse<BulkWaiveResponse>> => {
  try {
    console.log('🚫 BULK WAIVE API - Starting request...');
    console.log('🔍 Request details:', {
      employeeId: request.employeeId,
      planAssignmentIds: request.planAssignmentIds,
      waiveReason: request.waiveReason,
      enrollmentType: request.enrollmentType || 'Open Enrollment',
      planAssignmentCount: request.planAssignmentIds.length
    });

    // Validate request before sending
    const validation = validateBulkWaiveRequest(request);
    if (!validation.isValid) {
      console.error('❌ Request validation failed:', validation.errors);
      return {
        success: false,
        error: `Validation failed: ${validation.errors.join(', ')}`
      };
    }

    const requestBody = {
      employeeId: request.employeeId,
      planAssignmentIds: request.planAssignmentIds,
      waiveReason: request.waiveReason,
      waiveDate: request.waiveDate || new Date().toISOString(),
      enrollmentType: request.enrollmentType || 'Open Enrollment'
    };

    console.log('🔍 Request body being sent:', requestBody);
    console.log('🔍 API URL:', `${API_BASE_URL}/api/pre-enrollment/employee-enrollments/bulk-waive`);
    console.log('🔍 Headers:', getHeaders());

    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/employee-enrollments/bulk-waive`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(requestBody),
    });

    console.log('🔍 Bulk waive API response status:', response.status);
    console.log('🔍 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;

      try {
        const errorData = await response.json();
        console.error('❌ Bulk waive API error response:', errorData);
        errorMessage = errorData.error || errorData.message || errorMessage;

        // Log detailed error information
        if (errorData.errors) {
          console.error('❌ Individual plan assignment errors:', errorData.errors);
        }
      } catch (e) {
        console.log('⚠️ Could not parse error response as JSON');
        const errorText = await response.text();
        console.error('❌ Raw error response:', errorText);
        errorMessage = errorText || errorMessage;
      }

      return {
        success: false,
        error: errorMessage
      };
    }

    const result = await response.json();
    console.log('✅ Bulk waive API successful response:', result);

    // Log success details
    if (result.createdEnrollments) {
      console.log('✅ Created waived enrollments:', result.createdEnrollments);
    }
    if (result.errors && result.errors.length > 0) {
      console.warn('⚠️ Some plan assignments failed:', result.errors);
    }

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('❌ Network/fetch error in bulk waive enrollments:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to bulk waive enrollments'
    };
  }
};

/**
 * Helper function to group plan assignments by coverage type
 */
export const groupPlanAssignmentsByCategory = (planAssignments: any[]): { [category: string]: any[] } => {
  const grouped: { [category: string]: any[] } = {};

  planAssignments.forEach(assignment => {
    // Get the coverage type from the plan data
    const coverageType = assignment.plan?.coverageType || assignment.planData?.coverageType || 'Other';
    const category = coverageType.toLowerCase();

    if (!grouped[category]) {
      grouped[category] = [];
    }

    grouped[category].push(assignment);
  });

  return grouped;
};

/**
 * Helper function to get plan assignment IDs from a category
 */
export const getPlanAssignmentIdsFromCategory = (categoryPlans: any[]): string[] => {
  return categoryPlans.map(assignment => assignment._id).filter(id => id);
};

/**
 * Helper function to get coverage type display name
 */
export const getCoverageTypeDisplayName = (category: string): string => {
  const displayNames: { [key: string]: string } = {
    'health insurance': 'Medical',
    'medical': 'Medical',
    'dental': 'Dental',
    'vision': 'Vision',
    'life insurance': 'Life Insurance',
    'term life': 'Term Life',
    'supplemental life insurance': 'Supplemental Life',
    'whole life': 'Whole Life',
    'group (employer) life': 'Group Life',
    'short-term disability': 'Short-Term Disability',
    'long-term disability': 'Long-Term Disability',
    'other': 'Other'
  };

  return displayNames[category.toLowerCase()] || category;
};

/**
 * Validate bulk waive request
 */
export const validateBulkWaiveRequest = (request: BulkWaiveRequest): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!request.employeeId?.trim()) {
    errors.push('Employee ID is required');
  }

  if (!request.planAssignmentIds || request.planAssignmentIds.length === 0) {
    errors.push('At least one plan assignment ID is required');
  }

  if (!request.waiveReason?.trim()) {
    errors.push('Waive reason is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
