import os
import time
import requests
from typing import List, Dict, <PERSON>, Tuple
from langchain.schema import Document
from PyPDF2 import PdfReader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma, Milvus
from langchain_community.retrievers import BM25Retriever
from langchain_community.embeddings import GooglePalmEmbeddings, HuggingFaceInferenceAPIEmbeddings,JinaEmbeddings
from langchain_openai import OpenAIEmbeddings
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_milvus import Milvus as MilvusStore
from pymilvus import connections
from sentence_transformers import SentenceTransformer
import re
from io import BytesIO
from langchain_pinecone import PineconeVectorStore
from pinecone import ServerlessSpec, Pinecone
from langchain_core.embeddings import Embeddings
import uuid
import fitz
import logging
import easyocr
from config.config import config

class VectorStore:
    def __init__(self, 
                source_directory=os.path.join("MAS", 'Database', 'vectorSources'), 
                vector_directory=os.path.join("MAS", 'Database', 'vectorData'), 
                vector_db_type="chroma",  # Default to Chroma; can be "milvus" or "chroma"
                milvus_uri="http://localhost:19530",  # Milvus connection URI
                collection_name="vector_store", # milvus/chroma collection,
                embedding_model=None,
                embedding_available_option="huggingface_default",
                documents: List[Document] = None):
        """
        Initialize the VectorStore class with dynamic vector database selection.

        Args:
            source_directory (str): Directory containing source documents (PDFs and TXTs).
            vector_directory (str): Directory to store vector embeddings (used for Chroma).
            vector_db_type (str): Type of vector database to use ("chroma" or "milvus").
            milvus_uri (str): URI for connecting to Milvus.
            collection_name (str): Name of the collection in Milvus.
            embedding_model (str, optional): Name of the SentenceTransformer model to use for generating embeddings. 
                Examples include 'all-MiniLM-L6-v2', 'paraphrase-MiniLM-L6-v2', 'distilbert-base-nli-stsb-mean-tokens', etc. 
                Must be a valid model name from the Hugging Face hub.
            embedding_available_option: If embedding model is None. Choose from default options [google_default, huggingface_default, jina_default, openai_default]. Requires api key.
            documents (List[Document]): Optional list of preloaded documents.
            
            Use PINECONE_API_KEY and Milvus_Cluster_Token as environment variables.
            
            
        """
        self.source_directory = source_directory
        self.vector_directory = vector_directory
        if vector_db_type:
            self.vector_db_type = vector_db_type.lower()
        else:
            raise ValueError('Provide a vector db type')
        self.milvus_uri = milvus_uri
        self.collection_name = collection_name
        self.documents = documents or []
        self.vector_store = None
        self.retriever = None
        self.embedding_dict = {
            "google_default":GooglePalmEmbeddings(google_api_key=os.getenv('GOOGLE_GEMINI_API',"")),
            "huggingface_default":HuggingFaceInferenceAPIEmbeddings(api_key=os.getenv('HUGGINGFACEHUB_API_TOKEN',"")),
            "jina_default":JinaEmbeddings(jina_api_key=os.getenv('JINA_API_KEY',"")),
            # SentenceTransformer("all-MiniLM-L6-v2"),
            "openai_default":OpenAIEmbeddings(openai_api_key=os.getenv("OPENAI_API_KEY",""))
        }
        if embedding_model is not None:
            # self.embedding_model = SentenceTransformer(embedding_model)
            self.embedding_model = HuggingFaceEmbeddings(model_name=embedding_model)
            # self.embedding_dimension = self.embedding_model.get_sentence_embedding_dimension()
            self.embedding_dimension = self.get_embedding_dimension(self.embedding_model)

        else:
            self.embedding_model = self.embedding_dict[embedding_available_option]
            self.embedding_dimension = self.get_embedding_dimension(self.embedding_model)

    def connect_to_milvus(self):
        """
        Connect to the Milvus server.
        """
        connections.connect("default", uri=self.milvus_uri, token=os.getenv("Milvus_Cluster_Token"))
        print(f"Connected to Milvus at {self.milvus_uri}")
    
    def get_pinecone_instance(self, api_key=None):
        api_key = api_key or os.getenv("PINECONE_API_KEY")
        if not api_key:
            raise ValueError("PINECONE_API_KEY environment variable is not set.")

        pc = Pinecone(api_key=api_key)
        return pc

    
    def get_embedding_dimension(self,embeddings: Embeddings) -> int:
        """
        Get the embedding dimension for a given LangChain Embeddings model.

        Args:
            embeddings: An instance of a LangChain Embeddings model.

        Returns:
            int: The dimension of the embeddings produced by the model.
        """
        # Use a simple sample text to generate an embedding
        sample_text = "sample text"
        embedding = embeddings.embed_query(sample_text)
        
        # Return the length of the embedding vector
        return len(embedding)
    
    from typing import Union, Tuple

    def create_embeddings_from_files(self, file_paths: Union[str, List[str]], addMetaData=False) -> Tuple[List[List[float]], List[Document]]:
        """
        Create embeddings for PDF documents using the embedding model.

        Args:
            file_paths (Union[str, List[str]]): A single file path or list of file paths to PDF documents.
            addMetaData (bool): If true, ensures metadata is included (already handled by extraction).

        Returns:
            Tuple[List[List[float]], List[Document]]: A tuple of embeddings and Document objects.
        """
        if isinstance(file_paths, str):
            file_paths = [file_paths]

        documents = []
        for file_path in file_paths:
            if file_path.endswith('.pdf'):
                # Use default image_dir (next to PDF) since file paths may vary
                docs = self.extract_documents_from_file(file_path)
                documents.extend(docs)
            # Skip non-PDF files as per original behavior

        if not documents:
            return [], []

        embeddings = self.embedding_model.embed_documents([doc.page_content for doc in documents])
        return embeddings, documents
    def extract_documents_from_file(self, file_path: str, image_dir: str = None) -> List[Document]:
        """
        Extract documents from a single file, supporting PDF and TXT formats.
        For PDFs, extracts text, links, and images per page, and performs OCR on images.

        Args:
            file_path (str): Path to the file.
            image_dir (str, optional): Directory to save extracted images for PDFs. Defaults to 'extracted_images' beside the PDF.

        Returns:
            List[Document]: A list of extracted documents.
        """
        if file_path.endswith('.txt'):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return [Document(page_content=content, metadata={"source": file_path})]
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                return []
        elif file_path.endswith('.pdf'):
            try:
                # Determine image directory
                pdf_dir = os.path.dirname(file_path)
                pdf_name = os.path.splitext(os.path.basename(file_path))[0]
                if image_dir is None:
                    image_dir = os.path.join(pdf_dir, "extracted_images")
                os.makedirs(image_dir, exist_ok=True)

                # Initialize EasyOCR reader (only once)
                try:
                    reader = easyocr.Reader(['en'], gpu=False)  # Initialize with English language
                    if reader:
                        if config.ocr_available:
                            print(config.ocr_available, type(config.ocr_available))
                            ocr_available = True
                        else:
                            ocr_available = False
                    print("EasyOCR initialized successfully")
                except ImportError:
                    print("EasyOCR not available. Install with: pip install easyocr")
                    ocr_available = False
                except Exception as ocr_error:
                    print(f"Error initializing EasyOCR: {ocr_error}")
                    ocr_available = False

                # Open PDF and process each page
                doc = fitz.open(file_path)
                documents = []
                for page_num, page in enumerate(doc):
                    # Extract text
                    text = page.get_text("text")

                    # Extract and integrate links
                    links = page.get_links()
                    for link in links:
                        if 'uri' in link:
                            rect = link['from'] + (+1, 0, -1, 0)
                            link_text = page.get_textbox(rect)
                            if link_text and link_text in text:
                                text = text.replace(link_text, f"[{link_text} (href={link['uri']})]")

                    # Extract and save images
                    images = page.get_images()
                    image_filenames = []
                    image_texts = []
                    
                    for img_index, img in enumerate(images):
                        xref = img[0]
                        image_data = doc.extract_image(xref)
                        image_bytes = image_data["image"]
                        image_ext = image_data["ext"]
                        image_filename = f"{pdf_name}_page{page_num+1}_img{img_index+1}.{image_ext}"
                        image_path = os.path.join(image_dir, image_filename)
                        
                        # Save the image
                        with open(image_path, "wb") as img_file:
                            img_file.write(image_bytes)
                        
                        # Perform OCR on the image if available
                        if ocr_available:
                            try:
                                # Use EasyOCR to extract text from the image
                                ocr_results = reader.readtext(image_path)
                                
                                # Extract text from OCR results
                                if ocr_results:
                                    extracted_text = "\n".join([result[1] for result in ocr_results])
                                    if extracted_text.strip():
                                        image_texts.append(f"Text from image {image_filename}: {extracted_text}")
                                        print(f"OCR text from {image_filename}: {extracted_text}")
                            except Exception as ocr_error:
                                print(f"OCR error for {image_filename}: {ocr_error}")
                        
                        image_filenames.append(image_filename)

                    # Append image references to text
                    if image_filenames:
                        text += "\n\nImages on this page: " + ", ".join(image_filenames)
                    
                    # Append OCR text from images
                    if image_texts:
                        text += "\n\nExtracted text from images:\n" + "\n".join(image_texts)

                    # Create document with metadata
                    metadata = {"source": file_path, "page": page_num + 1}
                    documents.append(Document(page_content=text, metadata=metadata))
                    
                doc.close()
                if os.path.exists(image_dir):
                    import shutil
                    shutil.rmtree(image_dir)

                return documents
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                return []
        else:
            # Unsupported file type
            return []
        
    def load_documents_from_directory(self):
        """
        Load documents from the source directory, supporting PDF and TXT formats.
        For PDFs, extracts text, links, and images per page.

        Returns:
            List[Document]: A list of loaded documents.
        """
        print(f"Loading documents from: {self.source_directory}")
        documents = []
        # Central image directory for all PDFs in the source directory
        image_dir = os.path.join(self.source_directory, "extracted_images")
        os.makedirs(image_dir, exist_ok=True)

        for root, _, files in os.walk(self.source_directory):
            for file in files:
                file_path = os.path.join(root, file)
                if file.endswith('.txt') or file.endswith('.pdf'):
                    docs = self.extract_documents_from_file(file_path, image_dir=image_dir)
                    documents.extend(docs)

        print(f"Loaded {len(documents)} documents.")
        self.documents.extend(documents)  # Maintain original behavior
        return self.documents
        
    def fetch_text_from_urls(self, urls: List[str], type: str = "jina"):
        """
        Fetch clean text from a list of URLs.
        If type is 'jina', use Jina AI's Reader API; if it fails or if type is not 'jina', use a custom scraper.
        """
        documents = []
        
        if type == "jina":
            base_url = "https://r.jina.ai/"
            api_key = os.getenv("JINA_API_KEY")
            if not api_key:
                raise ValueError("JINA_API_KEY environment variable is not set.")
            headers = {"Authorization": f"Bearer {api_key}"}
        
        for url in urls:
            if type == "jina":
                try:
                    response = requests.get(base_url + url, headers=headers)
                    response.raise_for_status()
                    clean_text = response.text.strip()
                except Exception as e:
                    print(f"Jina failed for {url}: {e}. Falling back to custom scraper.")
                    clean_text = self._custom_scrape_url(url)
            else:
                clean_text = self._custom_scrape_url(url)
            
            document = Document(page_content=clean_text, metadata={"source": url})
            documents.append(document)

        self.documents.extend(documents)
        return self.documents
    
    def load_pdf_from_google_drive(self,urls:List[str]=None, metadata:dict=None):
        documents=[]
        if len(urls)>0:
            for url in urls:
                try:
                    match = re.search(r'/d/([a-zA-Z0-9_-]+)', url)
                    if not match:
                        raise ValueError("Invalid Google Drive URL")
                    file_id = match.group(1)
                    
                    # Construct the direct download URL.
                    direct_url = f"https://drive.google.com/uc?export=download&id={file_id}"
                    
                    # Fetch the PDF content (stream it into memory).
                    response = requests.get(direct_url)
                    response.raise_for_status()
                    
                    # Use BytesIO to handle the PDF in memory.
                    pdf_stream = BytesIO(response.content)
                    
                    # Extract text using PyPDF2.
                    reader = PdfReader(pdf_stream)
                    extracted_text = ""
                    for page in reader.pages:
                        text = page.extract_text() or ""
                        extracted_text += text
                    documents.append(Document(page_content=extracted_text,metadata=metadata))
                    
                except Exception as e:
                    return e
            if len(documents)>0:
                self.documents.extend(documents)

    
    def _custom_scrape_url(self, url: str) -> str:
        try:
            response = requests.get(url)
            response.raise_for_status()
        except Exception as e:
            print(f"Custom scraper: Failed to retrieve {url}: {e}")
            return ""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.content, "html.parser")
        for script in soup(["script", "style"]):
            script.decompose()
        text = soup.get_text(separator=" ", strip=True)
        return text

    def manage_pinecone_index(
        self,
        documents: List[Document],
        embedding_model,
        embedding_dimension: int,
        index_name: str,
        namespace: str,
        api_key: str = None
    ) -> PineconeVectorStore:
        """
        Manage Pinecone index: create if it doesn't exist, and store document embeddings.

        Args:
            documents (List[Document]): List of documents to embed and store.
            embedding_model: The embedding model to use for generating embeddings.
            embedding_dimension (int): Dimension of the embeddings.
            index_name (str): Name of the Pinecone index.
            namespace (str): Namespace within the Pinecone index.
            api_key (str, optional): Pinecone API key. If None, fetched from environment.

        Returns:
            PineconeVectorStore: The Pinecone vector store with the stored embeddings.
        """
        
        pc = self.get_pinecone_instance()
        if index_name not in pc.list_indexes().names():
            print(f"Creating Pinecone index: {index_name}")
            pc.create_index(
                name=index_name,
                dimension=embedding_dimension,
                metric="cosine",
                spec=ServerlessSpec(cloud="aws", region="us-east-1")
            )
            print(f"Pinecone index {index_name} created successfully.")

        vector_store = PineconeVectorStore.from_documents(
            documents=documents,
            embedding=embedding_model,
            index_name=index_name,
            namespace=namespace
        )
        print(f"Vector embeddings stored in Pinecone index: {index_name}, namespace: {namespace}.")

        return vector_store


    def makevectorembeddings(self,collection_name:str=None,index_name:str=None, chunk_size:int = 1000, chunk_overlap=500, namespace=""):
        """
        Create vector embeddings from the loaded documents and store them in the chosen vector database.
        index and namespace for pinecone.
        collection_name for chroma, milvus.

        Returns:
            VectorStore: The created vector store (Chroma or Milvus).
        """
        if not self.documents:
            raise ValueError("No documents loaded. Please load documents before creating vector embeddings.")

        # Define embedding model (HuggingFace used here; replace as needed)
        # embedding_model = HuggingFaceEmbeddings(model_name="sentence-transformers/all-mpnet-base-v2")

        # Split text into smaller chunks for embedding creation
        text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
        )
        chunks = text_splitter.split_documents(documents=self.documents)

        print('Creating vector embeddings...')

        if self.vector_db_type == "chroma":
            # Use Chroma for storing vectors
            os.makedirs(self.vector_directory, exist_ok=True)
            self.vector_store = Chroma.from_documents(
                documents=chunks,
                embedding=self.embedding_model,
                collection_name=self.collection_name if not collection_name else collection_name,
                persist_directory=self.vector_directory  # Save vectors to disk
            )
            print("Vector embeddings created and stored in Chroma.")

        elif self.vector_db_type == "milvus":
            # Use Milvus for storing vectors
            self.connect_to_milvus()
            print('Creating vector store...')
            self.vector_store = Milvus.from_documents(
                documents=chunks,
                embedding=self.embedding_model,
                connection_args={
                    "uri": self.milvus_uri,
                    "token":  os.getenv("Milvus_Cluster_Token") # Replace with your actual token
                },
                collection_name=self.collection_name if not collection_name else collection_name,
                drop_old=True  # Drop old collection if it exists
            )
            print(f"Vector embeddings created and stored in Milvus collection: {self.collection_name}.")

        elif self.vector_db_type == "pinecone":
                if not index_name:
                    raise ValueError("index_name must be provided for Pinecone.")
                self.vector_store = self.manage_pinecone_index(
                    documents=chunks,
                    embedding_model=self.embedding_model,
                    embedding_dimension=self.embedding_dimension,
                    index_name=index_name,
                    namespace=namespace
                )
            

        else:
            raise ValueError("Invalid vector_db_type. Choose either 'chroma' or 'milvus'.")

        return self.vector_store

    def makeretriever(self, top_k:int=5,search_type:str="similarity", search_kwargs:dict=None):
        """
        Create a retriever using similarity search from the chosen vector database.

        PARAMETERS:
        top_k (int): Top n documents to return. N is integer.
        Returns:
            Retriever: A similarity-based retriever.
        """
        if not self.vector_store:
            raise ValueError("No vector store available. Please create vector embeddings first.")
        if not search_kwargs:
            search_kwargs={"k":top_k}
        # print(self.vector_store)

        try:
        

            retriever = self.vector_store.as_retriever(
                search_type=search_type,
                search_kwargs=search_kwargs
            )
            
            self.retriever = retriever
        
            print("Retriever created.")
        
            return self.retriever
        except Exception as e:
            raise e


    
    def load_vector_store(self, collection_name: str = None, index_name: str = None, namespace: str = None):
        """
        Load an existing vector store from the chosen database.
        """
        if self.vector_db_type == "chroma":
            if not os.path.exists(self.vector_directory):
                raise FileNotFoundError(f"Vector directory does not exist: {self.vector_directory}")
            print(f"Loading vectors from Chroma directory: {self.vector_directory}")
            self.vector_store = Chroma(
                collection_name=self.collection_name if not collection_name else collection_name,
                persist_directory=self.vector_directory,
                embedding_function=self.embedding_model
            )
            print("Vectors loaded successfully from Chroma.")

        elif self.vector_db_type == "milvus":
            print(f"Loading vectors from Milvus collection: {self.collection_name if not collection_name else collection_name}")
            self.connect_to_milvus()
            self.vector_store = MilvusStore(
                embedding_function=self.embedding_model,
                connection_args={
                    "uri": self.milvus_uri,
                    "token": os.getenv("Milvus_Cluster_Token")
                },
                collection_name=self.collection_name if not collection_name else collection_name,
            )
            print("Vectors loaded successfully from Milvus.")

        elif self.vector_db_type == "pinecone":
            if not index_name:
                raise ValueError("index_name must be provided for Pinecone.")
            print(f"Loading vectors from Pinecone index: {index_name}")
            self.vector_store = PineconeVectorStore.from_existing_index(
                index_name=index_name,
                embedding=self.embedding_model,
                namespace=namespace
            )
            # print(self.vector_store)
            print(f"Vectors loaded successfully from Pinecone index: {index_name}.")

        else:
            raise ValueError("Invalid vector_db_type. Choose either 'chroma', 'milvus', or 'pinecone'.")

        return self.vector_store

    def update_vector_store(self, documents: List[Document], collection_name: str = None, index_name: str = None, namespace: str = None) -> Dict[str, str]:
        """
        Update the vector store by adding new documents.

        Args:
            documents (List[Document]): List of new documents to add.
            collection_name (str, optional): Collection name for Chroma or Milvus.
            index_name (str, optional): Index name for Pinecone.
            namespace (str, optional): Namespace for Pinecone.

        Returns:
            Dict[str, str]: Status of the update operation.
        """
        if not documents:
            return {"status": "No documents provided to update."}

        try:
            if self.vector_db_type == "chroma":
                if not self.vector_store:
                    self.vector_store = Chroma(
                        collection_name=self.collection_name if not collection_name else collection_name,
                        persist_directory=self.vector_directory,
                        embedding_function=self.embedding_model
                    )
                self.vector_store.add_documents(documents)
                print(f"Updated Chroma vector store with {len(documents)} new documents.")
                return {"status": "Vector store updated successfully"}

            elif self.vector_db_type == "milvus":
                if not self.vector_store:
                    self.connect_to_milvus()
                    self.vector_store = MilvusStore(
                        embedding_function=self.embedding_model,
                        connection_args={
                            "uri": self.milvus_uri,
                            "token": os.getenv("Milvus_Cluster_Token")
                        },
                        collection_name=self.collection_name if not collection_name else collection_name,
                    )
                self.vector_store.add_documents(documents)
                print(f"Updated Milvus vector store with {len(documents)} new documents.")
                return {"status": "Vector store updated successfully"}

            elif self.vector_db_type == "pinecone":
                print("Updating Pinecone Index")
                if not index_name:
                    raise ValueError("index_name must be provided for Pinecone.")
                if not self.vector_store:
                    # print(namespace, index_name, self.embedding_model)
                    self.vector_store = PineconeVectorStore.from_existing_index(
                        index_name=index_name,
                        embedding=self.embedding_model,
                        namespace=namespace
                    )
                for doc in documents:
                    if not doc.metadata["id"]:
                        doc.metadata["id"] = str(uuid.uuid4())  # Ensure each document has a unique ID
                    
                print("adding docs")
                self.vector_store.add_documents(documents)
                print(f"Updated Pinecone index {index_name} with {len(documents)} new documents in namespace {namespace}.")
                return {"status": "Index updated successfully"}

            else:
                raise ValueError("Invalid vector_db_type. Choose either 'chroma', 'milvus', or 'pinecone'.")

        except Exception as e:
            print(f"Error updating vector store: {str(e)}")
            return {"status": f"Error updating vector store: {str(e)}"}



    def delete_from_vector_store(
        self,
        metadata_filter: Dict[str, Union[str, List[str]]]={},
        collection_name: str = None,
        index_name: str = None,
        namespace: str = None
    ) -> Dict[str, str]:
        """
        Delete documents from the vector store based on a metadata filter.

        Args:
            metadata_filter (Dict[str, Union[str, List[str]]]): Metadata filter to identify documents to delete
                (e.g., {"file_key": "doc1.pdf"} or {"file_key": ["doc1.pdf", "doc2.pdf"]}).
            collection_name (str, optional): Collection name for Chroma or Milvus.
            index_name (str, optional): Index name for Pinecone.
            namespace (str, optional): Namespace for Pinecone.

        Returns:
            Dict[str, str]: Status of the deletion operation.
        """
        try:
            if self.vector_db_type == "chroma":
                if not self.vector_store:
                    self.vector_store = Chroma(
                        collection_name=self.collection_name if not collection_name else collection_name,
                        persist_directory=self.vector_directory,
                        embedding_function=self.embedding_model
                    )
                # Chroma deletion by metadata
                # Chroma uses a "where" clause for filtering; it doesn't support $in directly, so we need to handle lists
                ids_to_delete = []
                for key, value in metadata_filter.items():
                    if isinstance(value, list):
                        # For lists, perform multiple queries and combine results
                        for val in value:
                            partial_ids = self.vector_store.get(where={key: val}).get("ids", [])
                            ids_to_delete.extend(partial_ids)
                    else:
                        ids_to_delete.extend(self.vector_store.get(where={key: value}).get("ids", []))
                ids_to_delete = list(set(ids_to_delete))  # Remove duplicates
                if not ids_to_delete:
                    return {"status": "No matching documents found to delete."}
                self.vector_store.delete(ids=ids_to_delete)
                print(f"Deleted {len(ids_to_delete)} documents from Chroma vector store.")
                return {"status": "Documents deleted successfully"}

            elif self.vector_db_type == "milvus":
                if not self.vector_store:
                    self.connect_to_milvus()
                    self.vector_store = MilvusStore(
                        embedding_function=self.embedding_model,
                        connection_args={
                            "uri": self.milvus_uri,
                            "token": os.getenv("Milvus_Cluster_Token")
                        },
                        collection_name=self.collection_name if not collection_name else collection_name,
                    )
                # Milvus deletion by metadata
                # Milvus uses an expression for filtering; we need to construct an "IN" expression for lists
                expressions = []
                for key, value in metadata_filter.items():
                    if isinstance(value, list):
                        # Convert list to a Milvus IN expression, e.g., "file_key IN ['doc1.pdf', 'doc2.pdf']"
                        values_str = ", ".join([f"'{v}'" for v in value])
                        expressions.append(f"{key} IN [{values_str}]")
                    else:
                        expressions.append(f"{key} == '{value}'")
                filter_expr = " AND ".join(expressions)
                # Milvus requires IDs for deletion; we need to query first
                results = self.vector_store._collection.query(expr=filter_expr, output_fields=["pk"])
                ids_to_delete = [str(result["pk"]) for result in results]
                if not ids_to_delete:
                    return {"status": "No matching documents found to delete."}
                self.vector_store.delete(expr=f"pk IN [{', '.join(ids_to_delete)}]")
                print(f"Deleted {len(ids_to_delete)} documents from Milvus vector store.")
                return {"status": "Documents deleted successfully"}

            elif self.vector_db_type == "pinecone":
                if not index_name:
                    raise ValueError("index_name must be provided for Pinecone.")
                if not self.vector_store:

                   self.load_vector_store(index_name=index_name, namespace=namespace)
                # Pinecone deletion by metadata filter
                # Adjust filter to use $in for lists
                adjusted_filter = {}
                for key, value in metadata_filter.items():
                    if isinstance(value, list):
                        adjusted_filter[key] = {"$in": value}
                    else:
                        adjusted_filter[key] = value
                
                logging.info(f'adjusted filter {adjusted_filter}{self.vector_store}{index_name}')
                results = self.vector_store.similarity_search(
                    query="",
                    filter=adjusted_filter,
                    k=10000
                )
                logging.info(f'results {results}')
                # ids_to_delete = [result.metadata.get("id") for result in results if "id" in result.metadata]
                ids_to_delete = [result.id for result in results]
                logging.info(f"ids_to_delete: {ids_to_delete}")
                if not ids_to_delete:
                    return {"status": "No matching documents found to delete."}
                self.vector_store.delete(ids=ids_to_delete)
                print(f"Deleted {len(ids_to_delete)} documents from Pinecone index {index_name} in namespace {namespace}.")
                return {"status": "Documents deleted successfully"}

            else:
                raise ValueError("Invalid vector_db_type. Choose either 'chroma', 'milvus', or 'pinecone'.")

        except Exception as e:
            print(f"Error deleting documents from vector store: {str(e)}")
            return {"status": f"Error deleting documents: {str(e)}"}