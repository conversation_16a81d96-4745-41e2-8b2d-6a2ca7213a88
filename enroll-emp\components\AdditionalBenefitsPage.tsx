import React, { useState } from 'react';
import { BotQuestion } from './BotQuestion';
import { UserProfile } from '../page';

interface AdditionalBenefitsPageProps {
  userProfile: UserProfile;
  onComplete: (additionalPlans: any) => void;
}

export const AdditionalBenefitsPage = ({ userProfile, onComplete }: AdditionalBenefitsPageProps) => {
  const [selectedPlans, setSelectedPlans] = useState<any>({});

  const additionalBenefits = [
    {
      id: 'pet',
      name: 'Pet Insurance',
      cost: 12.50,
      description: 'Coverage for your furry family members',
      features: ['Accident & illness coverage', 'Wellness add-on available', 'Multiple pet discounts']
    },
    {
      id: 'hospital',
      name: 'Hospital Indemnity',
      cost: 8.20,
      description: 'Extra cash when you need it most',
      features: ['Cash payments for hospital stays', 'No network restrictions', 'Supplements your medical plan']
    }
  ];

  const handlePlanToggle = (planId: string, plan: any) => {
    setSelectedPlans((prev: any) => ({
      ...prev,
      [planId]: prev[planId] ? null : plan
    }));
  };

  const handleContinue = () => {
    onComplete(selectedPlans);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <BotQuestion 
        question="Want to add some extra protection?"
        context="These optional benefits can provide additional peace of mind and financial protection."
      />

      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Additional Benefits (Optional)</h3>
        <div className="space-y-4">
          {additionalBenefits.map(benefit => (
            <div key={benefit.id} className={`border rounded-lg p-4 transition-colors ${
              selectedPlans[benefit.id] ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
            }`}>
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{benefit.name}</h4>
                  <p className="text-sm text-gray-600">{benefit.description}</p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-green-600">${benefit.cost}/paycheck</p>
                </div>
              </div>
              <ul className="space-y-1 mb-4">
                {benefit.features.map((feature, index) => (
                  <li key={index} className="text-sm">✓ {feature}</li>
                ))}
              </ul>
              <button
                onClick={() => handlePlanToggle(benefit.id, benefit)}
                className={`w-full px-4 py-2 rounded-lg transition-colors ${
                  selectedPlans[benefit.id]
                    ? 'bg-blue-500 text-white hover:bg-blue-600'
                    : 'border border-blue-500 text-blue-500 hover:bg-blue-50'
                }`}
              >
                {selectedPlans[benefit.id] ? 'Selected' : 'Add This Benefit'}
              </button>
            </div>
          ))}
        </div>

        <div className="mt-6 pt-4 border-t">
          <button
            onClick={handleContinue}
            className="w-full px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
          >
            Continue to Summary
          </button>
        </div>
      </div>
    </div>
  );
};
