'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import ChatModal from './ChatModal';

const FloatingChatButton: React.FC = () => {
  const [showChatModal, setShowChatModal] = useState(false);

  const openChatModal = () => {
    setShowChatModal(true);
  };

  const closeChatModal = () => {
    setShowChatModal(false);
  };

  return (
    <>
      {/* Floating Chat <PERSON> */}
      <div
        onClick={openChatModal}
        style={{
          position: 'fixed',
          bottom: '24px',
          right: '24px',
          width: '60px',
          height: '60px',
          backgroundColor: '#000000',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          zIndex: 1000,
          transition: 'all 0.3s ease',
          border: '2px solid #ffffff'
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'scale(1.1)';
          e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.25)';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        }}
      >
        <Image
          src="/brea.png"
          alt="Chat with Brea"
          width={40}
          height={40}
          style={{
            borderRadius: '50%',
            objectFit: 'cover'
          }}
        />
      </div>

      {/* Floating Tooltip */}
      <div
        style={{
          position: 'fixed',
          bottom: '32px',
          right: '92px',
          backgroundColor: '#000000',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          zIndex: 999,
          opacity: showChatModal ? 0 : 1,
          visibility: showChatModal ? 'hidden' : 'visible',
          transition: 'all 0.3s ease',
          whiteSpace: 'nowrap',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
        }}
      >
        Chat with Brea
        <div
          style={{
            position: 'absolute',
            top: '50%',
            right: '-6px',
            transform: 'translateY(-50%)',
            width: 0,
            height: 0,
            borderLeft: '6px solid #000000',
            borderTop: '6px solid transparent',
            borderBottom: '6px solid transparent'
          }}
        />
      </div>

      {/* Chat Modal */}
      {showChatModal && (
        <ChatModal
          isOpen={showChatModal}
          onClose={closeChatModal}
        />
      )}
    </>
  );
};

export default FloatingChatButton;
