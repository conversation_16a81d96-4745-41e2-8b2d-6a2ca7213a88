'use client';

import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import {
  HiOutlineArrowLeft,
  HiOutlineHeart,
  HiOutlineEye,
  HiOutlineShieldCheck,
  HiOutlineUsers,
  HiOutlineCalendar,
  HiOutlineCheckCircle,
  HiOutlineXCircle
} from 'react-icons/hi';
import { FaRegSmile } from 'react-icons/fa';
import '../renewal.css';
import './plan-detail.css';

// Types
interface PlanDetails {
  id: string;
  name: string;
  carrier: string;
  type: 'Medical' | 'Dental' | 'Vision' | 'Life';
  deductible: string;
  copay: string;
  monthlyPremiums: {
    employeeOnly: string;
    employeeSpouse: string;
    employeeChild: string;
    family: string;
  };
  enrolled: string;
  expiration: string;
  selected: boolean;
}

// Sample plan data
const samplePlans: PlanDetails[] = [
  {
    id: '1',
    name: 'Blue Cross Blue Shield PPO',
    carrier: 'BCBS',
    type: 'Medical',
    deductible: '$2,500',
    copay: '$30',
    monthlyPremiums: {
      employeeOnly: '$458.50',
      employeeSpouse: '$917.00',
      employeeChild: '$687.75',
      family: '$1,374.00'
    },
    enrolled: '142/145',
    expiration: '31/12/2024',
    selected: false
  },
  {
    id: '2',
    name: 'Delta Dental PPO',
    carrier: 'Delta Dental',
    type: 'Dental',
    deductible: '$75',
    copay: '$0',
    monthlyPremiums: {
      employeeOnly: '$42.50',
      employeeSpouse: '$85.00',
      employeeChild: '$63.75',
      family: '$127.50'
    },
    enrolled: '138/145',
    expiration: '31/12/2024',
    selected: false
  },
  {
    id: '3',
    name: 'VSP Vision Care',
    carrier: 'VSP',
    type: 'Vision',
    deductible: '$15',
    copay: '$15',
    monthlyPremiums: {
      employeeOnly: '$18.75',
      employeeSpouse: '$37.50',
      employeeChild: '$28.13',
      family: '$56.25'
    },
    enrolled: '124/145',
    expiration: '31/12/2024',
    selected: false
  },
  {
    id: '4',
    name: 'Basic Life Insurance',
    carrier: 'MetLife',
    type: 'Life',
    deductible: 'N/A',
    copay: 'N/A',
    monthlyPremiums: {
      employeeOnly: '$15.60',
      employeeSpouse: '$0.00',
      employeeChild: '$0.00',
      family: '$0.00'
    },
    enrolled: '145/145',
    expiration: '31/12/2024',
    selected: false
  }
];

const PlanRenewalDetail: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [plans, setPlans] = useState<PlanDetails[]>(samplePlans);
  const [currentStep, setCurrentStep] = useState(1);

  const groupName = 'TechCorp Solutions'; // This would come from API based on groupId

  const handlePlanToggle = (planId: string) => {
    setPlans(plans.map(plan =>
      plan.id === planId ? { ...plan, selected: !plan.selected } : plan
    ));
  };

  const handleSelectAll = () => {
    const allSelected = plans.every(plan => plan.selected);
    setPlans(plans.map(plan => ({ ...plan, selected: !allSelected })));
  };

  const handleClearSelection = () => {
    setPlans(plans.map(plan => ({ ...plan, selected: false })));
  };

  const selectedPlansCount = plans.filter(plan => plan.selected).length;

  const handleContinue = () => {
    if (selectedPlansCount > 0) {
      router.push(`/ai-enroller/renewal/${params.groupId}/renewal-options`);
    }
  };

  const getIconForPlanType = (type: string) => {
    switch (type) {
      case 'Medical':
        return <HiOutlineHeart size={24} />;
      case 'Dental':
        return <FaRegSmile size={24} />;
      case 'Vision':
        return <HiOutlineEye size={24} />;
      case 'Life':
        return <HiOutlineShieldCheck size={24} />;
      default:
        return <HiOutlineHeart size={24} />;
    }
  };

  const steps = [
    { number: 1, title: 'Review Current Plans', subtitle: 'View existing benefit plans', active: currentStep === 1 },
    { number: 2, title: 'Renewal Options', subtitle: 'Choose renewal type', active: currentStep === 2 },
    { number: 3, title: 'Plan Configuration', subtitle: 'Set dates and modifications', active: currentStep === 3 },
    { number: 4, title: 'Document Upload', subtitle: 'Upload plan documents', active: currentStep === 4 },
    { number: 5, title: 'Validation', subtitle: 'Review and validate setup', active: currentStep === 5 },
    { number: 6, title: 'Finalize', subtitle: 'Complete renewal process', active: currentStep === 6 },
    { number: 7, title: 'Export', subtitle: 'Download and share data', active: currentStep === 7 }
  ];

  return (
    <div className="plan-renewal-detail">
      {/* Header */}
      <div className="detail-header">
        <button
          className="back-btn"
          onClick={() => router.back()}
        >
          <HiOutlineArrowLeft size={20} />
          Back to Dashboard
        </button>

        <div className="header-info">
          <h1>Plan Renewal</h1>
          <h2>{groupName}</h2>
          <div className="step-indicator">Step {currentStep} of 7</div>
        </div>

        <div className="completion-status">
          14% Complete
        </div>
      </div>

      {/* Progress Steps */}
      <div className="renewal-steps">
        {steps.map((step, index) => (
          <div key={step.number} className={`renewal-step ${step.active ? 'active' : ''}`}>
            <div className="step-number">{step.number}</div>
            <div className="step-content">
              <div className="step-title">{step.title}</div>
              <div className="step-subtitle">{step.subtitle}</div>
            </div>
            {index < steps.length - 1 && <div className="step-connector"></div>}
          </div>
        ))}
      </div>

      {/* Current Plan Portfolio */}
      <div className="plan-portfolio">
        <div className="portfolio-header">
          <div className="portfolio-title">
            <HiOutlineHeart size={20} />
            <h3>Current Plan Portfolio</h3>
          </div>
          <p>Review the existing benefit plans for {groupName}. Select the plans you want to renew for the next plan year.</p>
        </div>

        <div className="plans-grid">
          {plans.map((plan) => (
            <div key={plan.id} className={`plan-card ${plan.selected ? 'selected' : ''}`}>
              <div className="plan-header">
                <div className="plan-select">
                  <button
                    className={`select-btn ${plan.selected ? 'selected' : ''}`}
                    onClick={() => handlePlanToggle(plan.id)}
                  >
                    {plan.selected ? <HiOutlineCheckCircle size={20} /> : <HiOutlineXCircle size={20} />}
                  </button>
                </div>

                <div className="plan-info">
                  <div className="plan-icon">
                    {getIconForPlanType(plan.type)}
                  </div>
                  <div>
                    <h4>{plan.name}</h4>
                    <span className="plan-carrier">{plan.carrier}</span>
                  </div>
                </div>

                <div className="plan-type-badge">
                  {plan.type}
                </div>
              </div>

              <div className="plan-details">
                <div className="detail-row">
                  <span className="label">Deductible</span>
                  <span className="value">{plan.deductible}</span>
                </div>
                <div className="detail-row">
                  <span className="label">Copay</span>
                  <span className="value">{plan.copay}</span>
                </div>
              </div>

              <div className="premium-section">
                <h5>Monthly Premiums</h5>
                <div className="premium-grid">
                  <div className="premium-item">
                    <span className="premium-label">Employee Only:</span>
                    <span className="premium-value">{plan.monthlyPremiums.employeeOnly}</span>
                  </div>
                  <div className="premium-item">
                    <span className="premium-label">EE + Spouse:</span>
                    <span className="premium-value">{plan.monthlyPremiums.employeeSpouse}</span>
                  </div>
                  <div className="premium-item">
                    <span className="premium-label">EE + Child:</span>
                    <span className="premium-value">{plan.monthlyPremiums.employeeChild}</span>
                  </div>
                  <div className="premium-item">
                    <span className="premium-label">Family:</span>
                    <span className="premium-value">{plan.monthlyPremiums.family}</span>
                  </div>
                </div>
              </div>

              <div className="plan-footer">
                <div className="enrollment-info">
                  <HiOutlineUsers size={16} />
                  <span>{plan.enrolled} enrolled</span>
                </div>
                <div className="expiration-info">
                  <HiOutlineCalendar size={16} />
                  <span>Exp: {plan.expiration}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="quick-actions">
          <div className="actions-info">
            <HiOutlineCheckCircle size={20} />
            <div>
              <h4>Quick Actions</h4>
              <p>You can select all plans to renew with existing terms, or choose specific plans to modify. Individual plan modifications can be made in the next steps.</p>
            </div>
          </div>

          <div className="action-buttons">
            <button className="action-btn secondary" onClick={handleSelectAll}>
              Select All Plans
            </button>
            <button className="action-btn secondary" onClick={handleClearSelection}>
              Clear Selection
            </button>
          </div>
        </div>

        {/* Continue Button */}
        <div className="continue-section">
          <button
            className={`continue-btn ${selectedPlansCount > 0 ? 'enabled' : 'disabled'}`}
            disabled={selectedPlansCount === 0}
            onClick={handleContinue}
            type="button"
          >
            Continue with {selectedPlansCount} Plans
          </button>
          {selectedPlansCount === 0 && (
            <p style={{ marginTop: '0.5rem', fontSize: '0.875rem', color: '#6b7280' }}>
              Please select at least one plan to continue
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PlanRenewalDetail;
