'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  HiOutlineOfficeBuilding, 
  HiOutlinePhone, 
  HiOutlineMail, 
  HiOutlineGlobe,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlinePlus,
  HiOutlineTrash
} from 'react-icons/hi';
import '../../globals.css';

// Constants for form options
const PLAN_TYPES = [
  'PPO', 'HMO', 'HDHP', 'MEC', 'EPO', 'POS', 'Indemnity',
  'Term Life', 'Whole Life', 'STD', 'LTD'
];

const COVERAGE_TYPES = [
  'Health Insurance',
  'Ancillary Benefits',
  'Life & Disability Insurance',
  'Voluntary Benefits',
  'Wellness & Mental Health',
  'Spending & Savings Accounts',
  'Financial Benefits',
  'Retirement Benefits',
  'Time Off & Leave',
  'Family & Caregiver Support',
  'Career & Development',
  'Workplace Environment',
  'Life Events'
];

const COVERAGE_SUBTYPES = [
  // Health Insurance
  'Medical',
  // Ancillary Benefits
  'Dental', 'Vision',
  // Life & Disability Insurance
  'Term Life', 'Supplemental Life Insurance', 'Short-Term Disability', 'Long-Term Disability', 'Whole Life', 'Group (Employer) Life',
  // Voluntary Benefits
  'Hospital Indemnity', 'Accident Insurance', 'Critical Illness Insurance', 'Cancer Insurance', 'Gap Insurance', 'Legal Insurance', 'Identity Theft Protection', 'Accident & Illness (Pets)', 'Nursing Care / Custodial Care',
  // Wellness & Mental Health
  'Wellness Programs', 'Employee Assistance Program', 'Gym Membership',
  // Spending & Savings Accounts
  'Health Savings Account', 'Flexible Savings Accounts', 'Commuter Benefits', 'Technology Stipend',
  // Financial Benefits
  'Pay & Bonus', 'Stock Options', 'Student Loan Assistance',
  // Retirement Benefits
  '401(k)', '403(b)', 'Pension Plan',
  // Time Off & Leave
  'Paid Time Off (PTO)', 'Parental Leave', 'Family and Medical Leave', 'Paid Volunteer Time',
  // Family & Caregiver Support
  'On-site Child Care',
  // Career & Development
  'Employee Training & Development', 'Tuition Reimbursement', 'Employee Recognition', 'Performance Goals & Process',
  // Workplace Environment
  'Pet-friendly Workplace', 'Ergonomic Workplace', 'Company Handbook',
  // Life Events
  'Marriage or Divorce', 'New Baby or Adoption', 'Loss of Insurance'
];

const US_STATES = [
  'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
  'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
  'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
  'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
  'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
];

const AUTH_METHODS = ['API_KEY', 'OAUTH', 'BASIC_AUTH', 'CERTIFICATE'];
const DATA_FORMATS = ['EDI', 'JSON', 'XML'];

interface CarrierFormData {
  carrierName: string;
  carrierCode: string;
  displayName: string;
  isSystemCarrier: boolean;
  contactInfo: {
    phone: string;
    email: string;
    website: string;
    supportEmail: string;
    claimsPhone: string;
    memberServicesPhone: string;
  };
  supportedPlanTypes: string[];
  supportedCoverageTypes: string[];
  supportedCoverageSubTypes: string[];
  integration: {
    ediCapable: boolean;
    apiEndpoint: string;
    apiVersion: string;
    authMethod: string;
    dataFormat: string;
  };
  licenseStates: string[];
  amRating: string;
  networkName: string;
}

const CreateCarrierPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState<CarrierFormData>({
    carrierName: '',
    carrierCode: '',
    displayName: '',
    isSystemCarrier: true, // Default to system carrier for superadmins
    contactInfo: {
      phone: '',
      email: '',
      website: '',
      supportEmail: '',
      claimsPhone: '',
      memberServicesPhone: ''
    },
    supportedPlanTypes: [],
    supportedCoverageTypes: [],
    supportedCoverageSubTypes: [],
    integration: {
      ediCapable: false,
      apiEndpoint: '',
      apiVersion: '',
      authMethod: 'API_KEY',
      dataFormat: 'JSON'
    },
    licenseStates: [],
    amRating: '',
    networkName: ''
  });

  // Check if user is superadmin (disabled for testing)
  useEffect(() => {
    // Temporarily disabled for testing - allow all users to access create-carrier
    // const userRole = localStorage.getItem('userRole');
    // if (userRole !== 'superadmin') {
    //   router.push('/ai-enroller');
    //   return;
    // }
    console.log('Create carrier page loaded - superadmin check disabled for testing');
  }, [router]);

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => {
        const parentKey = parent as keyof CarrierFormData;
        const parentValue = prev[parentKey];

        // Type guard to ensure parentValue is an object
        if (typeof parentValue === 'object' && parentValue !== null && !Array.isArray(parentValue)) {
          return {
            ...prev,
            [parent]: {
              ...parentValue,
              [child]: value
            }
          };
        }

        // Fallback for non-object parent values
        return prev;
      });
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleArrayToggle = (field: string, value: string) => {
    setFormData(prev => {
      const currentArray = prev[field as keyof CarrierFormData] as string[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];

      return {
        ...prev,
        [field]: newArray
      };
    });
  };

  const handleSelectAll = (field: string, allValues: string[]) => {
    setFormData(prev => {
      const currentArray = prev[field as keyof CarrierFormData] as string[];
      const isAllSelected = allValues.every(value => currentArray.includes(value));

      return {
        ...prev,
        [field]: isAllSelected ? [] : allValues
      };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const userId = (typeof window !== 'undefined')
        ? (localStorage.getItem('userId') || localStorage.getItem('userid1'))
        : null;

      if (!userId) {
        throw new Error('User ID not found. Please authenticate first.');
      }

      console.log('Creating carrier with data:', formData);
      console.log('API URL:', `${API_BASE_URL}/api/pre-enrollment/carriers/create`);
      console.log('User ID:', userId);

      // Validate required fields before sending
      if (!formData.carrierName || !formData.carrierCode) {
        throw new Error('Carrier name and code are required');
      }

      console.log('Form validation passed, sending request...');

      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/carriers/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId
        },
        body: JSON.stringify(formData)
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('Error response text:', errorText);

        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.error || 'Failed to create carrier');
        } catch (parseError) {
          throw new Error(`HTTP ${response.status}: ${errorText || 'Failed to create carrier'}`);
        }
      }

      const result = await response.json();
      console.log('Success response:', result);
      setSuccess(true);
      
      // Redirect after success
      setTimeout(() => {
        router.push('/ai-enroller');
      }, 2000);

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create carrier');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <HiOutlineCheck className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Carrier Created Successfully!</h2>
          <p className="text-gray-600 mb-4">The carrier has been created and is now available in the system.</p>
          <p className="text-sm text-gray-500">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <style jsx>{`
        input, select, textarea {
          background-color: white !important;
          color: black !important;
        }
        input::placeholder {
          color: #9ca3af !important;
        }
      `}</style>
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gray-900 rounded-lg flex items-center justify-center">
              <HiOutlineOfficeBuilding className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 style={{ fontSize: 'clamp(20px, 5vw, 24px)', fontWeight: '600', color: '#111827', fontFamily: 'sans-serif' }}>Create New Carrier</h1>
              <p style={{ fontSize: 'clamp(12px, 3vw, 14px)', lineHeight: '1.4', color: '#6b7280', fontFamily: 'sans-serif' }}>Add a new insurance carrier to the system</p>
            </div>
          </div>
          
          {/* Breadcrumb */}
          <nav className="text-sm text-gray-500">
            <span className="hover:text-gray-700 cursor-pointer" onClick={() => router.push('/ai-enroller')}>
              Dashboard
            </span>
            <span className="mx-2">/</span>
            <span className="text-gray-900">Create Carrier</span>
          </nav>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <HiOutlineX className="w-5 h-5 text-red-600" />
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 style={{ fontSize: 'clamp(18px, 4vw, 20px)', fontWeight: '600', color: '#111827', marginBottom: '1rem', fontFamily: 'sans-serif' }}>Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Carrier Name *
                </label>
                <input
                  type="text"
                  required
                  value={formData.carrierName}
                  onChange={(e) => handleInputChange('carrierName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  style={{ backgroundColor: 'white', color: 'black' }}
                  placeholder="e.g., Blue Cross Blue Shield"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Carrier Code *
                </label>
                <input
                  type="text"
                  required
                  value={formData.carrierCode}
                  onChange={(e) => handleInputChange('carrierCode', e.target.value.toUpperCase())}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  style={{ backgroundColor: 'white', color: 'black' }}
                  placeholder="e.g., BCBS"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Display Name
                </label>
                <input
                  type="text"
                  value={formData.displayName}
                  onChange={(e) => handleInputChange('displayName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  style={{ backgroundColor: 'white', color: 'black' }}
                  placeholder="User-friendly display name"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isSystemCarrier"
                  checked={formData.isSystemCarrier}
                  onChange={(e) => handleInputChange('isSystemCarrier', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="isSystemCarrier" className="ml-2 text-sm text-gray-700">
                  System Carrier (Available to all brokers)
                </label>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 style={{ fontSize: 'clamp(18px, 4vw, 20px)', fontWeight: '600', color: '#111827', marginBottom: '1rem', fontFamily: 'sans-serif' }}>Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <HiOutlinePhone className="w-4 h-4 inline mr-1" />
                  Phone
                </label>
                <input
                  type="tel"
                  value={formData.contactInfo.phone}
                  onChange={(e) => handleInputChange('contactInfo.phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="(*************"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <HiOutlineMail className="w-4 h-4 inline mr-1" />
                  Email
                </label>
                <input
                  type="email"
                  value={formData.contactInfo.email}
                  onChange={(e) => handleInputChange('contactInfo.email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <HiOutlineGlobe className="w-4 h-4 inline mr-1" />
                  Website
                </label>
                <input
                  type="url"
                  value={formData.contactInfo.website}
                  onChange={(e) => handleInputChange('contactInfo.website', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://www.carrier.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Support Email
                </label>
                <input
                  type="email"
                  value={formData.contactInfo.supportEmail}
                  onChange={(e) => handleInputChange('contactInfo.supportEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Claims Phone
                </label>
                <input
                  type="tel"
                  value={formData.contactInfo.claimsPhone}
                  onChange={(e) => handleInputChange('contactInfo.claimsPhone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="(*************"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Member Services Phone
                </label>
                <input
                  type="tel"
                  value={formData.contactInfo.memberServicesPhone}
                  onChange={(e) => handleInputChange('contactInfo.memberServicesPhone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="(*************"
                />
              </div>
            </div>
          </div>

          {/* Supported Plan Types */}
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Supported Plan Types</h3>
              <button
                type="button"
                onClick={() => handleSelectAll('supportedPlanTypes', PLAN_TYPES)}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                {PLAN_TYPES.every(type => formData.supportedPlanTypes.includes(type)) ? 'Deselect All' : 'Select All'}
              </button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {PLAN_TYPES.map((planType) => (
                <label key={planType} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.supportedPlanTypes.includes(planType)}
                    onChange={() => handleArrayToggle('supportedPlanTypes', planType)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{planType}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Supported Coverage Types */}
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Supported Coverage Types</h3>
              <button
                type="button"
                onClick={() => handleSelectAll('supportedCoverageTypes', COVERAGE_TYPES)}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                {COVERAGE_TYPES.every(type => formData.supportedCoverageTypes.includes(type)) ? 'Deselect All' : 'Select All'}
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {COVERAGE_TYPES.map((coverageType) => (
                <label key={coverageType} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.supportedCoverageTypes.includes(coverageType)}
                    onChange={() => handleArrayToggle('supportedCoverageTypes', coverageType)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{coverageType}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Supported Coverage Subtypes */}
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Supported Coverage Subtypes</h3>
              <button
                type="button"
                onClick={() => handleSelectAll('supportedCoverageSubTypes', COVERAGE_SUBTYPES)}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                {COVERAGE_SUBTYPES.every(subtype => formData.supportedCoverageSubTypes.includes(subtype)) ? 'Deselect All' : 'Select All'}
              </button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
              {COVERAGE_SUBTYPES.map((subtype) => (
                <label key={subtype} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.supportedCoverageSubTypes.includes(subtype)}
                    onChange={() => handleArrayToggle('supportedCoverageSubTypes', subtype)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{subtype}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Integration Settings */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Integration Settings</h3>
            <div className="space-y-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="ediCapable"
                  checked={formData.integration.ediCapable}
                  onChange={(e) => handleInputChange('integration.ediCapable', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="ediCapable" className="ml-2 text-sm text-gray-700">
                  EDI Capable (Electronic Data Interchange)
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API Endpoint
                  </label>
                  <input
                    type="url"
                    value={formData.integration.apiEndpoint}
                    onChange={(e) => handleInputChange('integration.apiEndpoint', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://api.carrier.com/v1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API Version
                  </label>
                  <input
                    type="text"
                    value={formData.integration.apiVersion}
                    onChange={(e) => handleInputChange('integration.apiVersion', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="v1.0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Authentication Method
                  </label>
                  <select
                    value={formData.integration.authMethod}
                    onChange={(e) => handleInputChange('integration.authMethod', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {AUTH_METHODS.map((method) => (
                      <option key={method} value={method}>{method}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Data Format
                  </label>
                  <select
                    value={formData.integration.dataFormat}
                    onChange={(e) => handleInputChange('integration.dataFormat', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {DATA_FORMATS.map((format) => (
                      <option key={format} value={format}>{format}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Business Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  A.M. Best Rating
                </label>
                <input
                  type="text"
                  value={formData.amRating}
                  onChange={(e) => handleInputChange('amRating', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., A++"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Network Name
                </label>
                <input
                  type="text"
                  value={formData.networkName}
                  onChange={(e) => handleInputChange('networkName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Choice Plus Network"
                />
              </div>
            </div>

            {/* License States */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  Licensed States
                </label>
                <button
                  type="button"
                  onClick={() => handleSelectAll('licenseStates', US_STATES)}
                  className="px-2 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  {US_STATES.every(state => formData.licenseStates.includes(state)) ? 'Deselect All' : 'Select All'}
                </button>
              </div>
              <div className="grid grid-cols-5 md:grid-cols-10 gap-2 max-h-40 overflow-y-auto">
                {US_STATES.map((state) => (
                  <label key={state} className="flex items-center space-x-1 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.licenseStates.includes(state)}
                      onChange={() => handleArrayToggle('licenseStates', state)}
                      className="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="text-xs text-gray-700">{state}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={() => router.push('/ai-enroller')}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Creating...
                </>
              ) : (
                <>
                  <HiOutlinePlus className="w-4 h-4" />
                  Create Carrier
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCarrierPage;
