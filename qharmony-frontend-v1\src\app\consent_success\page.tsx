"use client";

import { Box, Typography, Container } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useTheme } from "@mui/material/styles";

export default function ConsentSuccess() {
  const theme = useTheme();

  return (
    <Container>
      <Box
        sx={{
          minHeight: "100vh",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          textAlign: "center",
          padding: theme.spacing(2),
          color: "white",
        }}
      >
        <CheckCircleIcon
          sx={{
            fontSize: "4rem",
            color: "#4CAF50",
            marginBottom: theme.spacing(2),
          }}
        />
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          sx={{ fontWeight: "bold" }}
        >
          Admin Consent Complete
        </Typography>
        <Typography variant="body1" sx={{ maxWidth: 500, mb: 2 }}>
          Thank you for providing consent! Your employees won&apos;t need to go through
          the consent process—it&apos;s already been approved by you as the
          administrator.
        </Typography>
        <Typography variant="body1" sx={{ maxWidth: 500 }}>
          Enjoy the seamless benefits experience in Microsoft Teams!
        </Typography>
      </Box>
    </Container>
  );
}
