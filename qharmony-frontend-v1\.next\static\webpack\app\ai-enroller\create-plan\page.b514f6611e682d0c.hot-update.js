"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/create-plan/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/create-plan/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/ai-enroller/create-plan/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/usePerformanceMonitor */ \"(app-pages-browser)/./src/app/ai-enroller/hooks/usePerformanceMonitor.ts\");\n/* harmony import */ var _components_CreatePlanOptimizer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/CreatePlanOptimizer */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/components/CreatePlanOptimizer.tsx\");\n/* harmony import */ var _employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../employee-enrol/components/EnrollmentHeader */ \"(app-pages-browser)/./src/app/ai-enroller/employee-enrol/components/EnrollmentHeader.tsx\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineArrowLeft,HiOutlineCheckCircle,HiOutlineCloudUpload,HiOutlineDocumentText,HiOutlinePlus,HiOutlineX!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=RiCheckboxCircleLine,RiFileTextLine,RiFileUploadLine,RiHealthBookLine,RiShieldCheckLine,RiVideoLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _create_plan_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./create-plan.css */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/create-plan.css\");\n/* harmony import */ var _services_planApi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./services/planApi */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/services/planApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CreatePlanPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [createdPlan, setCreatedPlan] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // State for plan name duplicate checking\n    const [planNameStatus, setPlanNameStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isChecking: false,\n        isDuplicate: false\n    });\n    // State for plan code duplicate checking\n    const [planCodeStatus, setPlanCodeStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isChecking: false,\n        isDuplicate: false\n    });\n    // Monitor page performance\n    (0,_hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_7__.usePerformanceMonitor)(\"Create Plan Page\");\n    // Debug: Log state changes (removed for production)\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        // Step 1: Basic Info\n        planName: \"\",\n        planCode: \"\",\n        carrier: \"\",\n        planType: \"\",\n        coverageCategory: \"\",\n        coverageType: \"\",\n        metalTier: \"\",\n        // Step 2: Media & Documents\n        videoUrl: \"\",\n        documents: [],\n        // Step 3: Description & Highlights\n        description: \"\",\n        highlights: [\n            \"\"\n        ],\n        // Legacy fields (for compatibility)\n        effectiveDate: \"\",\n        endDate: \"\",\n        copay: \"\",\n        deductible: \"\"\n    });\n    const steps = [\n        {\n            number: 1,\n            title: \"Documents\",\n            subtitle: \"Upload files\",\n            active: currentStep === 1,\n            completed: currentStep > 1\n        },\n        {\n            number: 2,\n            title: \"Basic Info\",\n            subtitle: \"Plan details\",\n            active: currentStep === 2,\n            completed: currentStep > 2\n        },\n        {\n            number: 3,\n            title: \"Description\",\n            subtitle: \"Details & video\",\n            active: currentStep === 3,\n            completed: currentStep > 3\n        },\n        {\n            number: 4,\n            title: \"Preview\",\n            subtitle: \"Review & create\",\n            active: currentStep === 4,\n            completed: currentStep > 4\n        },\n        {\n            number: 5,\n            title: \"Success\",\n            subtitle: \"Plan created\",\n            active: currentStep === 5,\n            completed: false\n        }\n    ];\n    // Memoize constants data to avoid recalculation\n    const constantsData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>(0,_services_planApi__WEBPACK_IMPORTED_MODULE_11__.getConstantsData)(), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Clear any problematic draft data on component mount\n        localStorage.removeItem(\"ai-enroller-draft-plan\");\n        // Prefetch related routes\n        router.prefetch(\"/ai-enroller\");\n        router.prefetch(\"/ai-enroller/plans\");\n        // Load data from backend APIs and hardcoded constants\n        const loadData = async ()=>{\n            setIsLoading(true);\n            try {\n                var _constantsResult_data, _constantsResult_data1, _constantsResult_data2, _constantsResult_data3;\n                // Check cache first\n                const cachedData = sessionStorage.getItem(\"ai-enroller-create-plan-data\");\n                const cacheTimestamp = sessionStorage.getItem(\"ai-enroller-create-plan-cache-time\");\n                const cacheAge = cacheTimestamp ? Date.now() - parseInt(cacheTimestamp) : Infinity;\n                // Use cache if less than 5 minutes old\n                if (cachedData && cacheAge < 5 * 60 * 1000) {\n                    const parsedCache = JSON.parse(cachedData);\n                    setData(parsedCache);\n                    setIsLoading(false);\n                    // Load draft data only if form is empty\n                    const draftData = localStorage.getItem(\"ai-enroller-draft-plan\");\n                    if (draftData && !formData.planName && !formData.coverageCategory) {\n                        const parsedDraft = JSON.parse(draftData);\n                        console.log(\"Loading draft data from cache:\", parsedDraft);\n                        setFormData(parsedDraft);\n                    // Duplicate checking will be triggered automatically by useEffect\n                    }\n                    // Data loaded from cache\n                    return;\n                }\n                // Load fresh data\n                const constantsResult = constantsData;\n                // Load carriers in parallel with a timeout\n                const carriersPromise = Promise.race([\n                    (0,_services_planApi__WEBPACK_IMPORTED_MODULE_11__.getCarriers)(),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Carriers API timeout\")), 3000))\n                ]);\n                const carriersResult = await carriersPromise.catch((error)=>{\n                    console.warn(\"Carriers API failed or timed out:\", error);\n                    return {\n                        success: false,\n                        error: error.message\n                    };\n                });\n                const backendData = {\n                    plans: [],\n                    templates: [],\n                    carriers: carriersResult.success ? carriersResult.data || [] : [],\n                    planTypes: ((_constantsResult_data = constantsResult.data) === null || _constantsResult_data === void 0 ? void 0 : _constantsResult_data.planTypes) || [],\n                    coverageCategories: ((_constantsResult_data1 = constantsResult.data) === null || _constantsResult_data1 === void 0 ? void 0 : _constantsResult_data1.coverageCategories) || [],\n                    coverageMap: ((_constantsResult_data2 = constantsResult.data) === null || _constantsResult_data2 === void 0 ? void 0 : _constantsResult_data2.coverageMap) || {},\n                    metalTiers: ((_constantsResult_data3 = constantsResult.data) === null || _constantsResult_data3 === void 0 ? void 0 : _constantsResult_data3.metalTiers) || []\n                };\n                setData(backendData);\n                console.log(\"Data loaded successfully:\", backendData);\n                console.log(\"Coverage categories:\", backendData.coverageCategories);\n                console.log(\"Loaded carriers:\", backendData.carriers);\n                // Cache the data\n                sessionStorage.setItem(\"ai-enroller-create-plan-data\", JSON.stringify(backendData));\n                sessionStorage.setItem(\"ai-enroller-create-plan-cache-time\", Date.now().toString());\n                // Load draft data only if form is empty\n                const draftData = localStorage.getItem(\"ai-enroller-draft-plan\");\n                if (draftData && !formData.planName && !formData.coverageCategory) {\n                    const parsedDraft = JSON.parse(draftData);\n                    console.log(\"Loading draft data from fresh load:\", parsedDraft);\n                    setFormData(parsedDraft);\n                // Duplicate checking will be triggered automatically by useEffect\n                }\n            // Data loaded successfully\n            } catch (error) {\n                var _constantsData_data, _constantsData_data1, _constantsData_data2, _constantsData_data3;\n                console.error(\"Error loading data:\", error);\n                // Set fallback data structure\n                setData({\n                    plans: [],\n                    templates: [],\n                    carriers: [],\n                    planTypes: ((_constantsData_data = constantsData.data) === null || _constantsData_data === void 0 ? void 0 : _constantsData_data.planTypes) || [],\n                    coverageCategories: ((_constantsData_data1 = constantsData.data) === null || _constantsData_data1 === void 0 ? void 0 : _constantsData_data1.coverageCategories) || [],\n                    coverageMap: ((_constantsData_data2 = constantsData.data) === null || _constantsData_data2 === void 0 ? void 0 : _constantsData_data2.coverageMap) || {},\n                    metalTiers: ((_constantsData_data3 = constantsData.data) === null || _constantsData_data3 === void 0 ? void 0 : _constantsData_data3.metalTiers) || []\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        router,\n        constantsData\n    ]);\n    // Debounced auto-save to localStorage\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        let timeoutId;\n        return (data)=>{\n            clearTimeout(timeoutId);\n            timeoutId = setTimeout(()=>{\n                localStorage.setItem(\"ai-enroller-draft-plan\", JSON.stringify(data));\n            }, 500);\n        };\n    }, []);\n    // Debounced plan name duplicate check\n    const debouncedNameCheck = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        let timeoutId;\n        return (planName)=>{\n            clearTimeout(timeoutId);\n            if (!planName.trim()) {\n                setPlanNameStatus({\n                    isChecking: false,\n                    isDuplicate: false\n                });\n                return;\n            }\n            setPlanNameStatus((prev)=>({\n                    ...prev,\n                    isChecking: true,\n                    error: undefined\n                }));\n            timeoutId = setTimeout(async ()=>{\n                try {\n                    // No excludeId needed for create-plan page since it's always creating new plans\n                    const result = await (0,_services_planApi__WEBPACK_IMPORTED_MODULE_11__.checkPlanNameDuplicate)(planName);\n                    if (result.success && result.data) {\n                        setPlanNameStatus({\n                            isChecking: false,\n                            isDuplicate: result.data.isDuplicate,\n                            existingPlan: result.data.existingPlan\n                        });\n                    } else {\n                        setPlanNameStatus({\n                            isChecking: false,\n                            isDuplicate: false,\n                            error: result.error || \"Failed to check for duplicates\"\n                        });\n                    }\n                } catch (error) {\n                    setPlanNameStatus({\n                        isChecking: false,\n                        isDuplicate: false,\n                        error: \"Error checking for duplicates\"\n                    });\n                }\n            }, 800);\n        };\n    }, []);\n    // Debounced plan code duplicate check\n    const debouncedCodeCheck = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        let timeoutId;\n        return (planCode)=>{\n            clearTimeout(timeoutId);\n            if (!planCode.trim()) {\n                setPlanCodeStatus({\n                    isChecking: false,\n                    isDuplicate: false\n                });\n                return;\n            }\n            setPlanCodeStatus((prev)=>({\n                    ...prev,\n                    isChecking: true,\n                    error: undefined\n                }));\n            timeoutId = setTimeout(async ()=>{\n                try {\n                    // No excludeId needed for create-plan page since it's always creating new plans\n                    const result = await (0,_services_planApi__WEBPACK_IMPORTED_MODULE_11__.checkPlanCodeDuplicate)(planCode);\n                    if (result.success && result.data) {\n                        setPlanCodeStatus({\n                            isChecking: false,\n                            isDuplicate: result.data.isDuplicate,\n                            existingPlan: result.data.existingPlan\n                        });\n                    } else {\n                        setPlanCodeStatus({\n                            isChecking: false,\n                            isDuplicate: false,\n                            error: result.error || \"Failed to check for duplicates\"\n                        });\n                    }\n                } catch (error) {\n                    setPlanCodeStatus({\n                        isChecking: false,\n                        isDuplicate: false,\n                        error: \"Error checking for duplicates\"\n                    });\n                }\n            }, 800);\n        };\n    }, []);\n    // Check for duplicates when form data changes (for any source - AI Assist, localStorage, manual input)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (formData.planName && formData.planName.trim()) {\n            console.log(\"\\uD83D\\uDD04 Form data changed: Checking plan name for duplicates\");\n            debouncedNameCheck(formData.planName);\n        }\n    }, [\n        formData.planName,\n        debouncedNameCheck\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (formData.planCode && formData.planCode.trim()) {\n            console.log(\"\\uD83D\\uDD04 Form data changed: Checking plan code for duplicates\");\n            debouncedCodeCheck(formData.planCode);\n        }\n    }, [\n        formData.planCode,\n        debouncedCodeCheck\n    ]);\n    // JavaScript-based tooltip system\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let activeTooltip = null;\n        const showTooltip = (icon, text)=>{\n            // Remove any existing tooltip\n            hideTooltip();\n            const rect = icon.getBoundingClientRect();\n            const tooltipWidth = 280;\n            const tooltipHeight = 120; // Approximate\n            // Calculate horizontal position (centered on icon, but keep within viewport)\n            let leftPosition = rect.left + rect.width / 2 - tooltipWidth / 2;\n            if (leftPosition < 10) leftPosition = 10;\n            if (leftPosition + tooltipWidth > window.innerWidth - 10) {\n                leftPosition = window.innerWidth - tooltipWidth - 10;\n            }\n            // Determine if tooltip should show above or below\n            const spaceAbove = rect.top;\n            const spaceBelow = window.innerHeight - rect.bottom;\n            const showBelow = spaceAbove < tooltipHeight && spaceBelow > tooltipHeight;\n            // Create tooltip element\n            const tooltip = document.createElement(\"div\");\n            tooltip.className = \"custom-tooltip\";\n            tooltip.textContent = text;\n            tooltip.style.cssText = \"\\n        position: fixed;\\n        \".concat(showBelow ? \"top: \".concat(rect.bottom + 4, \"px;\") : \"bottom: \".concat(window.innerHeight - rect.top + 4, \"px;\"), \"\\n        left: \").concat(leftPosition, \"px;\\n        width: 280px;\\n        background: #1f2937;\\n        color: white;\\n        padding: 0.75rem 1rem;\\n        border-radius: 0.5rem;\\n        font-size: 0.8rem;\\n        font-weight: 400;\\n        line-height: 1.4;\\n        text-align: left;\\n        white-space: normal;\\n        word-wrap: break-word;\\n        hyphens: auto;\\n        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\\n        z-index: 99999;\\n        pointer-events: none;\\n        opacity: 0;\\n        transition: opacity 0.2s ease-in-out;\\n      \");\n            // Create arrow\n            const arrow = document.createElement(\"div\");\n            arrow.className = \"custom-tooltip-arrow\";\n            arrow.style.cssText = \"\\n        position: fixed;\\n        \".concat(showBelow ? \"top: \".concat(rect.bottom - 2, \"px;\") : \"bottom: \".concat(window.innerHeight - rect.top - 2, \"px;\"), \"\\n        left: \").concat(rect.left + rect.width / 2 - 6, \"px;\\n        width: 0;\\n        height: 0;\\n        border-left: 6px solid transparent;\\n        border-right: 6px solid transparent;\\n        \").concat(showBelow ? \"border-bottom: 6px solid #1f2937;\" : \"border-top: 6px solid #1f2937;\", \"\\n        z-index: 100000;\\n        pointer-events: none;\\n        opacity: 0;\\n        transition: opacity 0.2s ease-in-out;\\n      \");\n            document.body.appendChild(tooltip);\n            document.body.appendChild(arrow);\n            // Fade in\n            requestAnimationFrame(()=>{\n                tooltip.style.opacity = \"1\";\n                arrow.style.opacity = \"1\";\n            });\n            activeTooltip = tooltip;\n            activeTooltip.arrow = arrow;\n        };\n        const hideTooltip = ()=>{\n            if (activeTooltip) {\n                activeTooltip.remove();\n                if (activeTooltip.arrow) {\n                    activeTooltip.arrow.remove();\n                }\n                activeTooltip = null;\n            }\n        };\n        const handleMouseEnter = (e)=>{\n            const icon = e.target;\n            const tooltipText = icon.getAttribute(\"data-tooltip\");\n            if (tooltipText) {\n                showTooltip(icon, tooltipText);\n            }\n        };\n        const handleMouseLeave = ()=>{\n            hideTooltip();\n        };\n        // Add event listeners to all tooltip icons\n        const tooltipIcons = document.querySelectorAll(\".tooltip-icon[data-tooltip]\");\n        tooltipIcons.forEach((icon)=>{\n            icon.addEventListener(\"mouseenter\", handleMouseEnter);\n            icon.addEventListener(\"mouseleave\", handleMouseLeave);\n        });\n        // Cleanup function\n        return ()=>{\n            hideTooltip();\n            tooltipIcons.forEach((icon)=>{\n                icon.removeEventListener(\"mouseenter\", handleMouseEnter);\n                icon.removeEventListener(\"mouseleave\", handleMouseLeave);\n            });\n        };\n    }, [\n        currentStep\n    ]);\n    const handleInputChange = (field, value)=>{\n        console.log(\"handleInputChange called:\", {\n            field,\n            value,\n            currentFormData: formData\n        });\n        const updatedData = {\n            ...formData,\n            [field]: value\n        };\n        console.log(\"Updated form data:\", updatedData);\n        setFormData(updatedData);\n        // Log after a small delay to see if state updated\n        setTimeout(()=>{\n            console.log(\"Form data after setState (delayed):\", formData);\n        }, 100);\n        // Debounced auto-save\n        debouncedSave(updatedData);\n    // Duplicate checking will be triggered automatically by useEffect\n    };\n    const handleFileUpload = (files)=>{\n        if (files) {\n            const fileArray = Array.from(files);\n            setFormData((prev)=>({\n                    ...prev,\n                    documents: [\n                        ...prev.documents,\n                        ...fileArray\n                    ]\n                }));\n        }\n    };\n    const removeDocument = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                documents: prev.documents.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleContinue = ()=>{\n        if (currentStep < 5) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleCreatePlan = async ()=>{\n        try {\n            // Use the selected coverage category as the main coverage type\n            const coverageType = formData.coverageCategory;\n            // Create the plan using backend API\n            const planData = {\n                planName: formData.planName,\n                planCode: formData.planCode,\n                carrier: formData.carrier,\n                coverageType: coverageType,\n                coverageSubTypes: [\n                    formData.coverageType\n                ],\n                planType: formData.planType,\n                metalTier: formData.metalTier,\n                description: formData.description,\n                highlights: formData.highlights.filter((h)=>h.trim() !== \"\"),\n                informativeLinks: formData.videoUrl ? [\n                    formData.videoUrl\n                ] : [],\n                carrierId: formData.carrier,\n                isTemplate: false,\n                status: \"Active\" // Set status to Active by default for company assignment\n            };\n            console.log(\"Creating plan with data:\", planData);\n            console.log(\"Form data mapping:\");\n            console.log(\"- Coverage Category (formData.coverageCategory):\", formData.coverageCategory, \"→ coverageType\");\n            console.log(\"- Coverage Type (formData.coverageType):\", formData.coverageType, \"→ coverageSubTypes\");\n            console.log(\"- Carrier ID:\", formData.carrier);\n            console.log(\"- Plan Type:\", formData.planType);\n            console.log(\"- Available carriers:\", data === null || data === void 0 ? void 0 : data.carriers);\n            const result = await (0,_services_planApi__WEBPACK_IMPORTED_MODULE_11__.createPlan)(planData);\n            if (result.success && result.data) {\n                // Plan created successfully\n                const finalPlan = result.data.plan;\n                // Upload documents if any\n                if (formData.documents.length > 0) {\n                    const uploadResult = await (0,_services_planApi__WEBPACK_IMPORTED_MODULE_11__.uploadPlanDocuments)(finalPlan._id, formData.documents);\n                    if (!uploadResult.success) {\n                        console.warn(\"Failed to upload some documents:\", uploadResult.error);\n                    }\n                }\n                // Store the created plan for display\n                setCreatedPlan(finalPlan);\n                // Clear draft data\n                localStorage.removeItem(\"ai-enroller-draft-plan\");\n                // Move to success step\n                setCurrentStep(5);\n            } else {\n                throw new Error(result.error || \"Failed to create plan\");\n            }\n        } catch (error) {\n            // Error creating plan\n            alert(\"Error creating plan: \".concat(error instanceof Error ? error.message : \"Please try again.\"));\n        }\n    };\n    const addHighlight = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                highlights: [\n                    ...prev.highlights,\n                    \"\"\n                ]\n            }));\n    };\n    const updateHighlight = (index, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                highlights: prev.highlights.map((h, i)=>i === index ? value : h)\n            }));\n    };\n    const removeHighlight = (index)=>{\n        if (formData.highlights.length > 1) {\n            setFormData((prev)=>({\n                    ...prev,\n                    highlights: prev.highlights.filter((_, i)=>i !== index)\n                }));\n        }\n    };\n    // Memoized validation functions for better performance\n    const isStep1Valid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return true; // Documents are optional, so step 1 is always valid\n    }, []);\n    const isStep2Valid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return formData.planName && formData.planCode && formData.carrier && formData.planType && formData.coverageCategory && formData.coverageType && // metalTier is now optional\n        !planNameStatus.isDuplicate && !planNameStatus.isChecking && !planCodeStatus.isDuplicate && !planCodeStatus.isChecking;\n    }, [\n        formData.planName,\n        formData.planCode,\n        formData.carrier,\n        formData.planType,\n        formData.coverageCategory,\n        formData.coverageType,\n        planNameStatus.isDuplicate,\n        planNameStatus.isChecking,\n        planCodeStatus.isDuplicate,\n        planCodeStatus.isChecking\n    ]);\n    const isStep3Valid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return formData.description && formData.highlights.some((h)=>h.trim() !== \"\");\n    }, [\n        formData.description,\n        formData.highlights\n    ]);\n    // Loading component\n    const LoadingSpinner = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"400px\",\n                flexDirection: \"column\",\n                gap: \"16px\"\n            },\n            className: \"jsx-ff161281ed666c63\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        width: \"40px\",\n                        height: \"40px\",\n                        border: \"3px solid #f3f4f6\",\n                        borderTop: \"3px solid #3b82f6\",\n                        borderRadius: \"50%\",\n                        animation: \"spin 1s linear infinite\"\n                    },\n                    className: \"jsx-ff161281ed666c63\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: \"#6b7280\",\n                        fontSize: \"14px\",\n                        lineHeight: \"1.6\",\n                        fontFamily: \"sans-serif\"\n                    },\n                    className: \"jsx-ff161281ed666c63\",\n                    children: \"Loading plan data...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"ff161281ed666c63\",\n                    children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n                }, void 0, false, void 0, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 651,\n            columnNumber: 5\n        }, undefined);\n    const renderStep1 = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiFileUploadLine, {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Plan Documents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-content\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                children: [\n                                    \"Plan Documents (Optional)\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"tooltip-icon\",\n                                        \"data-tooltip\": \"Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"file-upload-area\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        id: \"documents\",\n                                        multiple: true,\n                                        accept: \".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png\",\n                                        onChange: (e)=>handleFileUpload(e.target.files),\n                                        className: \"file-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"documents\",\n                                        className: \"file-upload-label\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineCloudUpload, {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Click to upload documents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                children: \"PDF, DOC, TXT, or Image files\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 11\n                            }, undefined),\n                            formData.documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"uploaded-files\",\n                                children: formData.documents.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"uploaded-file\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineDocumentText, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"file-name\",\n                                                children: file.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"file-size\",\n                                                children: [\n                                                    \"(\",\n                                                    (file.size / 1024).toFixed(1),\n                                                    \" KB)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"remove-file\",\n                                                onClick: ()=>removeDocument(index),\n                                                title: \"Remove this document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineX, {\n                                                    size: 14\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 686,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-navigation\",\n                    style: {\n                        justifyContent: \"flex-end\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"nav-btn primary enabled\",\n                        onClick: handleContinue,\n                        children: \"Continue to Basic Info\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 678,\n            columnNumber: 5\n        }, undefined);\n    const renderStep2 = ()=>{\n        var _data_coverageMap_formData_coverageCategory, _data_coverageMap, _data_carriers, _data_planTypes, _data_metalTiers;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiHealthBookLine, {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 747,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Basic Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 746,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 745,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"coverageCategory\",\n                                    children: [\n                                        \"Coverage Category\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"coverageCategory\",\n                                    value: formData.coverageCategory,\n                                    onChange: (e)=>{\n                                        console.log(\"Coverage category dropdown changed:\", e.target.value);\n                                        console.log(\"Current formData.coverageCategory before change:\", formData.coverageCategory);\n                                        // Update both coverageCategory and reset coverageType in a single state update\n                                        const updatedData = {\n                                            ...formData,\n                                            coverageCategory: e.target.value,\n                                            coverageType: \"\" // Reset coverage type when category changes\n                                        };\n                                        console.log(\"Updated form data (combined):\", updatedData);\n                                        setFormData(updatedData);\n                                        // Debounced auto-save\n                                        debouncedSave(updatedData);\n                                    },\n                                    title: \"Choose the main category of benefits\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select coverage category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        ((data === null || data === void 0 ? void 0 : data.coverageCategories) || []).map((category)=>{\n                                            console.log(\"Rendering category option:\", category);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category,\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 754,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"coverageType\",\n                                    children: [\n                                        \"Coverage Type\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"coverageType\",\n                                    value: formData.coverageType,\n                                    onChange: (e)=>handleInputChange(\"coverageType\", e.target.value),\n                                    disabled: !formData.coverageCategory,\n                                    title: \"Choose the specific type of benefits covered\",\n                                    style: {\n                                        backgroundColor: !formData.coverageCategory ? \"#f9fafb\" : \"white\",\n                                        cursor: !formData.coverageCategory ? \"not-allowed\" : \"pointer\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select coverage type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 815,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        formData.coverageCategory && (data === null || data === void 0 ? void 0 : (_data_coverageMap = data.coverageMap) === null || _data_coverageMap === void 0 ? void 0 : (_data_coverageMap_formData_coverageCategory = _data_coverageMap[formData.coverageCategory]) === null || _data_coverageMap_formData_coverageCategory === void 0 ? void 0 : _data_coverageMap_formData_coverageCategory.map((subType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: subType,\n                                                children: subType\n                                            }, subType, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 15\n                                            }, undefined))) || []\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 804,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"carrier\",\n                                    children: [\n                                        \"Carrier\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"carrier\",\n                                    value: formData.carrier,\n                                    onChange: (e)=>handleInputChange(\"carrier\", e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select carrier\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        (data === null || data === void 0 ? void 0 : (_data_carriers = data.carriers) === null || _data_carriers === void 0 ? void 0 : _data_carriers.map((carrier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: carrier._id,\n                                                children: carrier.displayName || carrier.carrierName\n                                            }, carrier._id, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 15\n                                            }, undefined))) || []\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"planName\",\n                                    children: [\n                                        \"Plan Name\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 849,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\",\n                                        width: \"100%\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"planName\",\n                                            placeholder: \"e.g. Blue Shield PPO 500\",\n                                            value: formData.planName,\n                                            onChange: (e)=>handleInputChange(\"planName\", e.target.value),\n                                            style: {\n                                                width: \"100%\",\n                                                borderColor: planNameStatus.isDuplicate ? \"#ef4444\" : planNameStatus.isChecking ? \"#f59e0b\" : formData.planName && !planNameStatus.isDuplicate ? \"#10b981\" : \"#d1d5db\",\n                                                paddingRight: \"40px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                right: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                fontSize: \"14px\",\n                                                fontFamily: \"sans-serif\"\n                                            },\n                                            children: [\n                                                planNameStatus.isChecking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        border: \"2px solid #f59e0b\",\n                                                        borderTop: \"2px solid transparent\",\n                                                        borderRadius: \"50%\",\n                                                        animation: \"spin 1s linear infinite\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                !planNameStatus.isChecking && formData.planName && !planNameStatus.isDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#10b981\",\n                                                        fontWeight: \"600\",\n                                                        fontFamily: \"sans-serif\"\n                                                    },\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                planNameStatus.isDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#ef4444\",\n                                                        fontWeight: \"600\",\n                                                        fontFamily: \"sans-serif\"\n                                                    },\n                                                    children: \"✗\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 11\n                                }, undefined),\n                                planNameStatus.isDuplicate && planNameStatus.existingPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#fef2f2\",\n                                        border: \"1px solid #fecaca\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#dc2626\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Plan name already exists:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        ' \"',\n                                        planNameStatus.existingPlan.planName,\n                                        '\"',\n                                        planNameStatus.existingPlan.planCode && \" (\".concat(planNameStatus.existingPlan.planCode, \")\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: \"#7f1d1d\",\n                                                fontFamily: \"sans-serif\"\n                                            },\n                                            children: \"Please choose a different name.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 13\n                                }, undefined),\n                                planNameStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#fef3cd\",\n                                        border: \"1px solid #fde68a\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#92400e\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Warning:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 935,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        planNameStatus.error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 13\n                                }, undefined),\n                                formData.planName && !planNameStatus.isChecking && !planNameStatus.isDuplicate && !planNameStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#f0fdf4\",\n                                        border: \"1px solid #bbf7d0\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#166534\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: \"✓ Plan name is available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 848,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"planCode\",\n                                    children: [\n                                        \"Plan Code\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 960,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 958,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\",\n                                        width: \"100%\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"planCode\",\n                                            placeholder: \"e.g. BS-PPO-500\",\n                                            value: formData.planCode,\n                                            onChange: (e)=>handleInputChange(\"planCode\", e.target.value),\n                                            style: {\n                                                width: \"100%\",\n                                                borderColor: planCodeStatus.isDuplicate ? \"#ef4444\" : planCodeStatus.isChecking ? \"#f59e0b\" : formData.planCode && !planCodeStatus.isDuplicate ? \"#10b981\" : \"#d1d5db\",\n                                                paddingRight: \"40px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                right: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                fontSize: \"14px\",\n                                                fontFamily: \"sans-serif\"\n                                            },\n                                            children: [\n                                                planCodeStatus.isChecking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        border: \"2px solid #f59e0b\",\n                                                        borderTop: \"2px solid transparent\",\n                                                        borderRadius: \"50%\",\n                                                        animation: \"spin 1s linear infinite\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                !planCodeStatus.isChecking && formData.planCode && !planCodeStatus.isDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#10b981\",\n                                                        fontWeight: \"600\",\n                                                        fontFamily: \"sans-serif\"\n                                                    },\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                planCodeStatus.isDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#ef4444\",\n                                                        fontWeight: \"600\",\n                                                        fontFamily: \"sans-serif\"\n                                                    },\n                                                    children: \"✗\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 981,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 11\n                                }, undefined),\n                                planCodeStatus.isDuplicate && planCodeStatus.existingPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#fef2f2\",\n                                        border: \"1px solid #fecaca\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#dc2626\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Plan code already exists:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        ' \"',\n                                        planCodeStatus.existingPlan.planCode,\n                                        '\"',\n                                        planCodeStatus.existingPlan.planName && \" (\".concat(planCodeStatus.existingPlan.planName, \")\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: \"#7f1d1d\",\n                                                fontFamily: \"sans-serif\"\n                                            },\n                                            children: \"Please choose a different code.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1012,\n                                    columnNumber: 13\n                                }, undefined),\n                                planCodeStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#fef3cd\",\n                                        border: \"1px solid #fde68a\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#92400e\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Warning:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        planCodeStatus.error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 13\n                                }, undefined),\n                                formData.planCode && !planCodeStatus.isChecking && !planCodeStatus.isDuplicate && !planCodeStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#f0fdf4\",\n                                        border: \"1px solid #bbf7d0\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#166534\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: \"✓ Plan code is available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1049,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 957,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"planType\",\n                                    children: [\n                                        \"Plan Type\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"planType\",\n                                    value: formData.planType,\n                                    onChange: (e)=>handleInputChange(\"planType\", e.target.value),\n                                    title: \"Select the plan structure (PPO, HMO, etc.)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        (data === null || data === void 0 ? void 0 : (_data_planTypes = data.planTypes) === null || _data_planTypes === void 0 ? void 0 : _data_planTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: type,\n                                                children: type\n                                            }, type, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 15\n                                            }, undefined))) || []\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"metalTier\",\n                                    children: [\n                                        \"Metal Tier (Optional)\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"metalTier\",\n                                    value: formData.metalTier,\n                                    onChange: (e)=>handleInputChange(\"metalTier\", e.target.value),\n                                    title: \"Choose the coverage level (Bronze, Silver, Gold, Platinum) - Optional\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select tier (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1104,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        (data === null || data === void 0 ? void 0 : (_data_metalTiers = data.metalTiers) === null || _data_metalTiers === void 0 ? void 0 : _data_metalTiers.map((tier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: tier,\n                                                children: tier\n                                            }, tier, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1106,\n                                                columnNumber: 15\n                                            }, undefined))) || []\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1098,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1090,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 752,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-navigation\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/ai-enroller\",\n                            prefetch: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"nav-btn secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineArrowLeft, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Back to Main\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1116,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1115,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn primary \".concat(isStep2Valid ? \"enabled\" : \"disabled\"),\n                            onClick: handleContinue,\n                            disabled: !isStep2Valid,\n                            title: \"Continue to description and video\",\n                            children: \"Continue to Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1122,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1114,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 744,\n            columnNumber: 5\n        }, undefined);\n    };\n    const renderStep3 = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiVideoLine, {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Description & Video\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1139,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1137,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1136,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"videoUrl\",\n                                    children: [\n                                        \"Video URL (Optional)\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Add a YouTube or Vimeo URL to help explain plan benefits and features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1147,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1145,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    id: \"videoUrl\",\n                                    placeholder: \"e.g. https://youtube.com/watch?v=...\",\n                                    value: formData.videoUrl,\n                                    onChange: (e)=>handleInputChange(\"videoUrl\", e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"field-hint\",\n                                    children: \"Add a video to help explain plan benefits and features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1144,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"description\",\n                                    children: [\n                                        \"Plan Description\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Provide a detailed description of the plan benefits, coverage, and key features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1165,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    placeholder: \"Describe the plan benefits and features...\",\n                                    value: formData.description,\n                                    onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                    rows: 4\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1170,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"field-hint\",\n                                    children: \"Describe the key benefits, coverage details, and what makes this plan unique\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1177,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1162,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    children: [\n                                        \"Plan Highlights\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1181,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"field-hint\",\n                                    children: \"Add the most important features that make this plan attractive\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 11\n                                }, undefined),\n                                formData.highlights.map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"highlight-input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"e.g. Low deductible, Nationwide network\",\n                                                value: highlight,\n                                                onChange: (e)=>updateHighlight(index, e.target.value),\n                                                title: \"Enter a key benefit or feature\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            formData.highlights.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"remove-highlight\",\n                                                onClick: ()=>removeHighlight(index),\n                                                title: \"Remove this highlight\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineX, {\n                                                    size: 14\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1190,\n                                        columnNumber: 13\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"add-highlight\",\n                                    onClick: addHighlight,\n                                    title: \"Add another highlight\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlinePlus, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        \"Add Highlight\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1210,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1180,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1143,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-navigation\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn secondary\",\n                            onClick: handleBack,\n                            title: \"Go back to basic information\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineArrowLeft, {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1224,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Back\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1223,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn primary \".concat(isStep3Valid ? \"enabled\" : \"disabled\"),\n                            onClick: handleContinue,\n                            disabled: !isStep3Valid,\n                            title: \"Continue to preview your plan\",\n                            children: \"Preview Plan\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1228,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1222,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1135,\n            columnNumber: 5\n        }, undefined);\n    const renderStep4 = ()=>{\n        var _data_carriers_find, _data_carriers, _data_carriers_find1, _data_carriers1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-header-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiCheckboxCircleLine, {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"AI-Powered Plan Preview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1243,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ready-badge\",\n                            title: \"All required information has been provided\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineCheckCircle, {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1248,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Ready to Create\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1247,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1242,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"review-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"review-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-section-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiHealthBookLine, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1256,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Plan Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1257,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1255,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-items\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"The name of this plan\",\n                                                    children: \"Plan Name:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1261,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value\",\n                                                    children: formData.planName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1262,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1260,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Unique identifier for this plan\",\n                                                    children: \"Plan Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1265,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value plan-code\",\n                                                    children: formData.planCode\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1266,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Insurance carrier providing this plan\",\n                                                    children: \"Carrier:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value\",\n                                                    children: (data === null || data === void 0 ? void 0 : (_data_carriers = data.carriers) === null || _data_carriers === void 0 ? void 0 : (_data_carriers_find = _data_carriers.find((c)=>c._id === formData.carrier)) === null || _data_carriers_find === void 0 ? void 0 : _data_carriers_find.displayName) || (data === null || data === void 0 ? void 0 : (_data_carriers1 = data.carriers) === null || _data_carriers1 === void 0 ? void 0 : (_data_carriers_find1 = _data_carriers1.find((c)=>c._id === formData.carrier)) === null || _data_carriers_find1 === void 0 ? void 0 : _data_carriers_find1.carrierName) || \"Unknown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1270,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1268,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Type of health plan structure\",\n                                                    children: \"Plan Type:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1276,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value\",\n                                                    children: formData.planType\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1277,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1275,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Type of coverage provided\",\n                                                    children: \"Coverage Type:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value\",\n                                                    children: formData.coverageType\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1279,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Metal tier level indicating coverage level\",\n                                                    children: \"Metal Tier:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1284,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value metal-tier\",\n                                                    children: formData.metalTier\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1285,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1283,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1254,\n                            columnNumber: 9\n                        }, undefined),\n                        (formData.videoUrl || formData.documents.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"review-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-section-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineDocumentText, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1293,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Media & Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1294,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1292,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-items\",\n                                    children: [\n                                        formData.videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item full-width\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Video URL for plan explanation\",\n                                                    children: \"Video URL:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1299,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: formData.videoUrl,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"review-link\",\n                                                    children: formData.videoUrl\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1298,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        formData.documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item full-width\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Documents uploaded for this plan\",\n                                                    children: \"Documents:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1307,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"review-documents\",\n                                                    children: formData.documents.map((doc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"review-document\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineDocumentText, {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                                    lineNumber: 1311,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    children: [\n                                                                        \"(\",\n                                                                        (doc.size / 1024).toFixed(1),\n                                                                        \" KB)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                                    lineNumber: 1313,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                            lineNumber: 1310,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1308,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1306,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1296,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1291,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"review-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-section-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiFileTextLine, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1325,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Description & Highlights\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1326,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1324,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-items\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item full-width\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Detailed plan description\",\n                                                    children: \"Description:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1330,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"review-description\",\n                                                    children: formData.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1331,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1329,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item full-width\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Key plan features and benefits\",\n                                                    children: \"Highlights:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1334,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"review-highlights\",\n                                                    children: formData.highlights.filter((h)=>h.trim()).map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: highlight\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                            lineNumber: 1337,\n                                                            columnNumber: 19\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1335,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1333,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1328,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1323,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"create-confirmation\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"confirmation-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiCheckboxCircleLine, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1346,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1345,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"confirmation-text\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Ready to Create Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1349,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                'Your new plan \"',\n                                                formData.planName,\n                                                '\" will be added to your catalog and available for assignment to employer groups.'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1350,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1348,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1344,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1253,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-navigation\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn secondary\",\n                            onClick: handleBack,\n                            title: \"Go back to description and video\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineArrowLeft, {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1357,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Back\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1356,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn primary enabled\",\n                            onClick: handleCreatePlan,\n                            title: \"Create this plan and add it to your catalog\",\n                            children: \"Create Plan\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1361,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1355,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1241,\n            columnNumber: 5\n        }, undefined);\n    };\n    const renderStep5 = ()=>{\n        var _data_carriers_find, _data_carriers_find1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section success-section\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"success-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"success-icon\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiShieldCheckLine, {\n                            size: 32\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1376,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1375,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Plan Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1378,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Your plan '\",\n                            formData.planName,\n                            \"' is now available in your catalog.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1379,\n                        columnNumber: 9\n                    }, undefined),\n                    createdPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"plan-details-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"plan-details-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiHealthBookLine, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1385,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Plan Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1386,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1384,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"plan-details-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Plan ID:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1390,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value plan-id\",\n                                                children: createdPlan._id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1391,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1389,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Plan Code:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1394,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value plan-code\",\n                                                children: createdPlan.planCode\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1395,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value status-active\",\n                                                children: createdPlan.status || \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1399,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Created:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value\",\n                                                children: createdPlan.createdAt ? new Date(createdPlan.createdAt).toLocaleString() : \"Just now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1403,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1401,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Carrier:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1406,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value\",\n                                                children: (data === null || data === void 0 ? void 0 : (_data_carriers_find = data.carriers.find((c)=>c._id === (createdPlan.carrierId || createdPlan.carrier))) === null || _data_carriers_find === void 0 ? void 0 : _data_carriers_find.displayName) || (data === null || data === void 0 ? void 0 : (_data_carriers_find1 = data.carriers.find((c)=>c._id === (createdPlan.carrierId || createdPlan.carrier))) === null || _data_carriers_find1 === void 0 ? void 0 : _data_carriers_find1.carrierName) || \"Unknown\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1407,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1405,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1388,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1383,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"success-actions\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/ai-enroller/manage-groups\",\n                                prefetch: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"nav-btn primary\",\n                                    children: \"Assign to Group Now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1418,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1417,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/ai-enroller\",\n                                prefetch: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"nav-btn secondary\",\n                                    children: \"Back to Main Menu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1424,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1423,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1416,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                lineNumber: 1374,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1373,\n            columnNumber: 5\n        }, undefined);\n    };\n    const renderStepContent = ()=>{\n        switch(currentStep){\n            case 1:\n                return renderStep1();\n            case 2:\n                return renderStep2();\n            case 3:\n                return renderStep3();\n            case 4:\n                return renderStep4();\n            case 5:\n                return renderStep5();\n            default:\n                return renderStep1();\n        }\n    };\n    // Show loading state while data is being fetched\n    if (isLoading || !data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"create-plan-wrapper\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"create-plan-page\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1455,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                lineNumber: 1454,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1453,\n            columnNumber: 7\n        }, undefined);\n    }\n    const getStepMessage = ()=>{\n        switch(currentStep){\n            case 1:\n                return {\n                    title: \"Hi! I'm Brea, your AI Benefits Assistant. Let's create an amazing plan together! \\uD83D\\uDE0A\",\n                    subtitle: \"I'll help you set up the basic plan information including name, carrier, and coverage details. This should only take a few minutes!\"\n                };\n            case 2:\n                return {\n                    title: \"Great progress! Now let's add some media to make your plan shine ✨\",\n                    subtitle: \"You can upload videos, brochures, or any documents that help explain your plan. Don't worry, this step is completely optional!\"\n                };\n            case 3:\n                return {\n                    title: \"Perfect! Now tell me what makes this plan special \\uD83C\\uDF1F\",\n                    subtitle: \"Help me understand the key benefits and highlights. I'll use this to create compelling descriptions that really sell your plan!\"\n                };\n            case 4:\n                return {\n                    title: \"Almost there! Let's review everything before we launch your plan \\uD83D\\uDE80\",\n                    subtitle: \"Take a moment to review all the details. Once you're happy with everything, I'll create your plan and make it available immediately!\"\n                };\n            case 5:\n                return {\n                    title: \"Congratulations! Your plan is now live and ready to go! \\uD83C\\uDF89\",\n                    subtitle: \"I've successfully created your plan and it's now available for assignment to employer groups. Great work!\"\n                };\n            default:\n                return {\n                    title: \"Hi there! Ready to create something amazing? \\uD83D\\uDCAB\",\n                    subtitle: \"I'm here to help you build the perfect benefits plan. Let's get started!\"\n                };\n        }\n    };\n    const getPageIcon = (stepNumber)=>{\n        switch(stepNumber){\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiHealthBookLine, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1498,\n                    columnNumber: 22\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiOutlineDocumentText, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1499,\n                    columnNumber: 22\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiFileTextLine, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1500,\n                    columnNumber: 22\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiCheckboxCircleLine, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1501,\n                    columnNumber: 22\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_12__.RiHealthBookLine, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1502,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"create-plan-wrapper\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1509,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CreatePlanOptimizer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1511,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"create-plan-page\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"progress-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"progress-title\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"page-title\",\n                                            children: \"Plan Creation Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1516,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"subtitle-text\",\n                                            children: [\n                                                Math.min(currentStep, 4),\n                                                \" of 4\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1517,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1515,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"progress-bar-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"progress-bar-fill\",\n                                        style: {\n                                            width: \"\".concat(Math.min(currentStep, 4) / 4 * 100, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1520,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1519,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1514,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-navigation\",\n                            children: steps.slice(0, 4).map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"page-nav-item \".concat(step.active ? \"active\" : \"\", \" \").concat(step.completed ? \"completed\" : \"\"),\n                                    onClick: ()=>setCurrentStep(step.number),\n                                    disabled: step.number > currentStep,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"nav-icon\",\n                                            children: getPageIcon(step.number)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1536,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        step.title\n                                    ]\n                                }, step.number, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1530,\n                                    columnNumber: 13\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1528,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ai-assistant-message\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ai-message-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ai-avatar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"avatar-circle\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: \"/brea.png\",\n                                                alt: \"Brea - AI Assistant\",\n                                                className: \"brea-avatar\",\n                                                width: 48,\n                                                height: 48,\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1547,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1546,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1545,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"chat-bubble\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"chat-message\",\n                                                children: getStepMessage().title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1558,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"chat-subtitle\",\n                                                children: getStepMessage().subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1561,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1557,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1544,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1543,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"main-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-container\",\n                                children: renderStepContent()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1570,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1569,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1512,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1508,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n        lineNumber: 1507,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreatePlanPage, \"1EZ8mez6EkQqFLBApQVXXOj4RAk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_7__.usePerformanceMonitor\n    ];\n});\n_c = CreatePlanPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreatePlanPage);\nvar _c;\n$RefreshReg$(_c, \"CreatePlanPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/create-plan/page.tsx\n"));

/***/ })

});