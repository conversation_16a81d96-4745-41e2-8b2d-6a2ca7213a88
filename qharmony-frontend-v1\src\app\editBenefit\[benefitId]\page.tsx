"use client";

import { useEffect, useState, useRef } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import {
  getDocumentsForBenefit,
  addDocumentForBenefit,
  deleteDocumentForBenefit,
  addLinkForBenefit,
  deleteLinkForBenefit,
} from "@/middleware/benefits_middleware";
import { useParams, useRouter } from "next/navigation";
import {
  Box,
  Typography,
  Button,
  Grid,
  IconButton,
  Tooltip,
  Divider,
  Snackbar,
  Alert,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
} from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import CancelIcon from "@mui/icons-material/Cancel";
import ClearIcon from "@mui/icons-material/Clear";
import AddLinkIcon from "@mui/icons-material/AddLink";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import { useSelector } from "react-redux";
import { selectBenefitById } from "@/redux/reducers/benefitsSlice";
import { getUsersCompanyId } from "@/redux/reducers/userSlice";
import { clearSnackbarMessage } from "@/redux/reducers/benefitsSlice";
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";
import * as microsoftTeams from "@microsoft/teams-js";
import { maskBenefitCategory, maskedSubCategory } from "@/middleware/company_middleware";

const EditBenefitsView = () => {
  const { benefitId } = useParams();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [newLink, setNewLink] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [deletingDocumentKey, setDeletingDocumentKey] = useState<string | null>(
    null,
  );
  const [openDialog, setOpenDialog] = useState(false);
  const [confirmText, setConfirmText] = useState("");
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [linkToDelete, setLinkToDelete] = useState<number | null>(null);
  const [isAddingLink, setIsAddingLink] = useState(false);
  const [deletingLinkIndex, setDeletingLinkIndex] = useState<number | null>(
    null,
  );
  const [isInTeams, setIsInTeams] = useState(false);

  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));

  const documents = useAppSelector(
    (state: RootState) => state.benefits.documentsPerBenefit,
  );

  const viewableDocuments = useAppSelector(
    (state: RootState) => state.benefits.viewableDocuments,
  );

  const loadingDocuments = useAppSelector(
    (state: RootState) => state.benefits.loadingDocuments,
  );

  const benefitInfo = useSelector((state: RootState) =>
    selectBenefitById(state, benefitId as string),
  );

  const snackbarMessage = useAppSelector(
    (state: RootState) => state.benefits.snackbarMessage,
  );

  useEffect(() => {
    // Initialize Teams SDK
    microsoftTeams.app.initialize().then(() => {
      microsoftTeams.app.getContext().then((context) => {
        if (context.app.host.name === microsoftTeams.HostName.teams) {
          setIsInTeams(true);
        }
      });
    });
  }, []);

  useEffect(() => {
    console.log("documents:", documents);
  }, [documents]);

  useEffect(() => {
    if (companyId && benefitId) {
      getDocumentsForBenefit(
        dispatch,
        benefitId as string,
        companyId,
        "edit_benefit",
      );
    }
  }, [companyId, benefitId, dispatch]);

  const openPdfExternally = (objectId: string, companyId: string) => {
    window.open(`https://api.benosphere.com/benefits/document?objectKey=${objectId}&companyId=${companyId}`, "_blank");
  };

  const handleOpenDialog = (documentObjectKey: string) => {
    setDocumentToDelete(documentObjectKey);
    setOpenDialog(true);
  };

  const handleOpenLinkDialog = (index: number) => {
    setLinkToDelete(index);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setConfirmText("");
    setDocumentToDelete(null);
    setLinkToDelete(null);
  };

  const handleConfirmDelete = () => {
    if (
      confirmText === "delete document" &&
      documentToDelete &&
      companyId &&
      benefitId
    ) {
      setDeletingDocumentKey(documentToDelete);
      deleteDocumentForBenefit(
        dispatch,
        benefitId as string,
        companyId,
        documentToDelete,
      ).finally(() => {
        setDeletingDocumentKey(null);
        handleCloseDialog();
      });
    }
  };

  const handleConfirmDeleteLink = async () => {
    if (
      confirmText === "delete link" &&
      linkToDelete !== null &&
      companyId &&
      benefitId
    ) {
      setDeletingLinkIndex(linkToDelete);
      handleCloseDialog();
      await deleteLinkForBenefit(
        dispatch,
        benefitId as string,
        companyId as string,
        documents.links[linkToDelete],
      );
      setDeletingLinkIndex(null);
      setLinkToDelete(null);
    }
  };

  const handleAddDocument = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      if (Array.from(files).every((file) => file.type === "application/pdf")) {
        if (companyId && benefitId) {
          setIsUploading(true);
          addDocumentForBenefit(
            dispatch,
            benefitId as string,
            companyId,
            Array.from(files),
          ).finally(() => {
            setIsUploading(false);
            if (fileInputRef.current) {
              fileInputRef.current.value = ""; // Clear the file input
            }
          });
        }
      } else {
        alert("Please select PDF files only.");
      }
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewLink(event.target.value);
  };

  const handleAddLink = async () => {
    if (newLink) {
      setIsAddingLink(true);
      await addLinkForBenefit(
        dispatch,
        benefitId as string,
        companyId as string,
        newLink,
      );
      setNewLink("");
      setIsAddingLink(false);
    }
  };

  const handleCloseSnackbar = () => {
    dispatch(clearSnackbarMessage());
  };

  return (
    <ProtectedRoute>
      <Box sx={{ bgcolor: "#F5F6F8", minHeight: "98vh", padding: "32px" }}>
        {/* Back Arrow */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
          <Button
            startIcon={<ArrowBackIosNewIcon sx={{ fontSize: 16 }} />}
            onClick={() => router.back()}
            sx={{
              color: "#6c757d",
              fontWeight: "normal",
              textTransform: "none",
              fontSize: "1.2rem",
              "&:hover": {
                bgcolor: "transparent",
              },
            }}
          >
            {maskBenefitCategory(benefitInfo?.benefitType || "")} /
            <span
              style={{ fontWeight: "bold", color: "#000000", marginLeft: 5 }}
            >
              {maskedSubCategory(benefitInfo?.benefit?.subType || "")}
            </span>
          </Button>
        </Box>

        {/* White Background Container */}
        <Box
          sx={{
            bgcolor: "#ffffff",
            borderRadius: "12px",
            padding: "32px",
            boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
          }}
        >
          {/* Title Section */}
          <Typography
            variant="h4"
            sx={{ fontWeight: 600, fontSize: "28px", mb: 2 }}
          >
            {maskedSubCategory(benefitInfo?.benefit?.subType || "")}
          </Typography>
          <Typography variant="body1" sx={{ color: "#6c757d", mb: 3 }}>
            You can find all your health insurance details here, including
            coverage options, policy documents, and claim information.
          </Typography>

          {/* Document Section */}
          <Box
            sx={{
              display: "flex",
              flexWrap: "wrap",
              gap: "35px",
              overflowY: "auto",
              maxHeight: "300px",
            }}
          >
            {documents.documents.map((documentObjectKey, index) => {
              const viewableDocument = viewableDocuments.find(
                (doc) => doc.documentObjectKey === documentObjectKey,
              );
              console.log("viewable document >>>", viewableDocument);
              const gradientOptions = [
                "linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)",
                "linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)",
                "linear-gradient(135deg, #43cea2 0%, #185a9d 100%)",
                "linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)",
                "linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)",
              ];
              const gradient = gradientOptions[index % gradientOptions.length];
              return (
                <Box
                  key={documentObjectKey}
                  sx={{
                    position: "relative",
                    width: "160px",
                    height: "245px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-start",
                    justifyContent: "flex-start",
                  }}
                >
                  <Box
                    sx={{
                      width: "160px",
                      minHeight: "215px",
                      borderRadius: "12px",
                      overflow: "hidden",
                      boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
                      position: "relative",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      background: gradient,
                      color: "#ffffff",
                      cursor: "pointer",
                      textAlign: "center",
                      padding: "8px",
                    }}
                    onClick={() =>
                      // window.open(viewableDocument?.document, "_blank")
                      openPdfExternally(viewableDocument!.documentObjectKey, companyId)
                    }
                  >
                    {loadingDocuments.includes(documentObjectKey) ||
                      !viewableDocument ? (
                      <CircularProgress />
                    ) : (
                      <Typography
                        sx={{
                          fontSize: "16px",
                          fontWeight: "bold",
                          wordWrap: "break-word",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          maxHeight: "100%",
                          display: "-webkit-box",
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: "vertical",
                        }}
                      >
                        {viewableDocument?.originalFileName ||
                          "Document Preview"}
                      </Typography>
                    )}
                  </Box>
                  <IconButton
                    className="delete-icon"
                    onClick={() => handleOpenDialog(documentObjectKey)}
                    sx={{
                      position: "absolute",
                      top: -2,
                      right: -2,
                      padding: "5px",
                      color: "black",
                      "&:hover": {
                        color: "red",
                      },
                    }}
                  >
                    {deletingDocumentKey === documentObjectKey ? (
                      <CircularProgress size={20} />
                    ) : (
                      <CancelIcon sx={{ color: "white" }} />
                    )}
                  </IconButton>
                </Box>
              );
            })}

            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                marginLeft: "0px",
              }}
            >
              <Box
                onClick={handleAddDocument}
                sx={{
                  border: "2px dashed #d3d3d3",
                  bgcolor: "#f9f9f9",
                  borderRadius: "12px",
                  width: "160px",
                  height: "215px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  color: "#d3d3d3",
                  fontSize: "2.5rem",
                  "&:hover": {
                    backgroundColor: "#f0f0f0",
                  },
                }}
              >
                {isUploading ? <CircularProgress /> : "+"}
              </Box>
              <input
                type="file"
                ref={fileInputRef}
                style={{ display: "none" }}
                onChange={handleFileChange}
                accept="application/pdf" // Only allow PDF files
              />
              <Typography
                sx={{ color: "#a9a9a9", fontSize: "0.875rem", mt: 1 }}
              >
                Add up to 5 pdf files
              </Typography>
            </Box>
          </Box>

          {/* Links Section */}
          <Box sx={{ mt: 8 }}>
            <Box sx={{ alignItems: "center", mb: 3 }}>
              <Box sx={{ display: "flex", alignItems: "center", width: "40%" }}>
                <input
                  type="text"
                  value={newLink}
                  onChange={handleInputChange}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleAddLink();
                    }
                  }}
                  placeholder="Enter Link To Add (eg. www.BenOsphere.com)"
                  style={{
                    padding: "15px 14px",
                    borderRadius: "8px",
                    border: "none",
                    width: "70%",
                    backgroundColor: "#f3f3f3",
                    color: "#000000",
                    fontSize: "0.9rem",
                  }}
                />
                <Button
                  onClick={handleAddLink}
                  disabled={!newLink || isAddingLink}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "#45a049",
                    textTransform: "none",
                    fontSize: "1rem",
                    fontWeight: newLink ? "bold" : "normal",
                    padding: 0,
                    backgroundColor: "transparent",
                    border: "none",
                    cursor: "pointer",
                    marginLeft: "20px",
                    "&:disabled": {
                      color: "#9E9E9E",
                    },
                  }}
                >
                  {isAddingLink ? (
                    <CircularProgress size={20} />
                  ) : (
                    <>
                      <span
                        style={{
                          fontSize: "1.8rem",
                          marginRight: "8px",
                          fontWeight: "normal",
                        }}
                      >
                        +
                      </span>
                      Add Link
                    </>
                  )}
                </Button>
              </Box>
            </Box>

            <Grid container spacing={2}>
              {documents.links.map((link, index) => (
                <Grid item xs={12} key={index}>
                  <Box sx={{ paddingLeft: 1 }}>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <Typography
                        component="a"
                        href={
                          link.startsWith("http") ? link : `https://${link}`
                        }
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{
                          textDecoration: "none",
                          color: "primary.main",
                          display: "block",
                          paddingBottom: "10px",
                        }}
                      >
                        {link || `Link ${index + 1}`}
                      </Typography>
                      <Tooltip title="Remove Link">
                        <IconButton
                          onClick={() => handleOpenLinkDialog(index)}
                          sx={{
                            color: "gray",
                            marginLeft: "8px",
                            padding: "4px",
                            marginBottom: "7px",
                            "&:hover": { color: "#ff4d4d" },
                          }}
                        >
                          {deletingLinkIndex === index ? (
                            <CircularProgress size={20} />
                          ) : (
                            <ClearIcon sx={{ fontSize: "20px" }} />
                          )}
                        </IconButton>
                      </Tooltip>
                    </Box>
                    <Divider sx={{ width: "10%" }} />
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>

        <Snackbar
          open={!!snackbarMessage}
          autoHideDuration={3000}
          onClose={handleCloseSnackbar}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity="success"
            sx={{ width: "100%" }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>

        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          PaperProps={{
            style: {
              borderRadius: "16px",
              boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
            },
          }}
        >
          <DialogTitle
            sx={{ fontWeight: "bold", fontSize: "1.5rem", color: "#000000" }}
          >
            Confirm Deletion
          </DialogTitle>
          <DialogContent>
            <DialogContentText
              sx={{ color: "#6c757d", fontSize: "1rem", mb: 2 }}
            >
              To confirm deletion, please type &quot;
              <strong style={{ color: "black" }}>
                {linkToDelete !== null ? "delete link" : "delete document"}
              </strong>
              &quot; in the box below.
            </DialogContentText>
            <TextField
              autoFocus
              fullWidth
              variant="outlined"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "12px",
                  backgroundColor: "#f9f9f9",
                },
              }}
            />
          </DialogContent>
          <DialogActions sx={{ padding: "16px" }}>
            <Button
              onClick={handleCloseDialog}
              sx={{
                color: "#6c757d",
                backgroundColor: "#ffffff",
                borderRadius: "8px",
                padding: "8px 16px",
                textTransform: "none",
                boxShadow: "none",
                "&:hover": {
                  backgroundColor: "#f0f0f0",
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={
                linkToDelete !== null
                  ? handleConfirmDeleteLink
                  : handleConfirmDelete
              }
              disabled={
                confirmText !==
                (linkToDelete !== null ? "delete link" : "delete document")
              }
              sx={{
                color: "#ffffff",
                backgroundColor:
                  confirmText ===
                    (linkToDelete !== null ? "delete link" : "delete document")
                    ? "#000000" // Changed to black
                    : "#9E9E9E",
                borderRadius: "8px",
                padding: "8px 16px",
                textTransform: "none",
                "&:hover": {
                  backgroundColor:
                    confirmText ===
                      (linkToDelete !== null ? "delete link" : "delete document")
                      ? "#333333" // Darker shade of black for hover
                      : "#9E9E9E",
                },
              }}
            >
              {deletingDocumentKey || deletingLinkIndex !== null ? (
                <CircularProgress size={20} sx={{ color: "white" }} />
              ) : (
                "Confirm"
              )}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ProtectedRoute>
  );
};

export default withSidebar(EditBenefitsView);
