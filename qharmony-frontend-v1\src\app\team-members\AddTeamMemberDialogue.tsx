"use client";

import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  adminAddsUsers,
  getCompanyTeamMembers,
  updateUser,
} from "@/middleware/company_middleware";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  IconButton,
  CircularProgress,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { putRequest } from "@/APILayer/axios_helper";

interface AddTeamMemberDialogProps {
  open: boolean;
  onClose: () => void;
  member?: any; // New prop for member details
}

const AddTeamMemberDialog: React.FC<AddTeamMemberDialogProps> = ({
  open,
  onClose,
  member,
}) => {
  const dispatch = useDispatch();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [department, setDepartment] = useState(""); // New state for department
  const [title, setTitle] = useState(""); // New state for title
  const [successMessage, setSuccessMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [errors, setErrors] = useState({
    firstName: false,
    lastName: false,
    email: false,
    phoneNumber: false,
  });

  const validateFields = () => {
    const newErrors = {
      firstName: !firstName,
      lastName: !lastName,
      email: !email,
      phoneNumber: !phoneNumber,
    };
    setErrors(newErrors);
    return !Object.values(newErrors).some((error) => error);
  };

  useEffect(() => {
    if (member) {
      setFirstName(member.name.split(" ")[0]);
      setLastName(member.name.split(" ")[1]);
      setEmail(member.email);
      setPhoneNumber(member.details?.phoneNumber || "");
      setDepartment(member.details?.department || ""); // Set department if editing
      setTitle(member.details?.title || ""); // Set title if editing
    } else {
      setFirstName("");
      setLastName("");
      setEmail("");
      setPhoneNumber("");
      setDepartment(""); // Reset department
      setTitle(""); // Reset title
    }
  }, [member]);

  const handleAddMember = async () => {
    if (!validateFields()) {
      setErrorMessage("Please fill out all required fields.");
      return;
    }

    setLoading(true);
    setErrorMessage("");
    const newMember = {
      name: `${firstName} ${lastName}`,
      email: email,
      phoneNumber: phoneNumber,
      department: department,
      title: title,
    };

    if (member) {
      console.log("Editing team member:", newMember);
      await updateUser(dispatch, member._id, newMember);
    } else {
      console.log("Adding team member:", newMember);
      await adminAddsUsers(dispatch, [newMember]);
    }

    await getCompanyTeamMembers(dispatch);
    setLoading(false);
    setSuccessMessage(
      member
        ? "Team member updated successfully!"
        : "Team member added successfully!",
    );
    setTimeout(() => {
      if (member) {
        setSuccessMessage("");
        onClose();
      } else {
        setSuccessMessage("");
        setFirstName("");
        setLastName("");
        setEmail("");
        setPhoneNumber("");
        setDepartment(""); // Reset department
        setTitle(""); // Reset title
      }
    }, 1500);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        style: {
          borderRadius: "16px",
          boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
          padding: "5px",
          width: "550px",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          fontWeight: "bold",
          fontSize: "1.5rem",
        }}
      >
        {member ? "Edit Team Member" : "Add Team Member"}
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Grid
          container
          spacing={3}
          sx={{ marginBottom: "16px", marginTop: "0px" }}
        >
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="First name"
              variant="outlined"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              required
              error={errors.firstName}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Last name"
              variant="outlined"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              required
              error={errors.lastName}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Email"
              variant="outlined"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              error={errors.email}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
              disabled={!!member}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Phone number"
              variant="outlined"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              required
              error={errors.phoneNumber}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Department"
              variant="outlined"
              value={department}
              onChange={(e) => setDepartment(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Title"
              variant="outlined"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
        </Grid>
        {loading && <CircularProgress />}
        {errorMessage && (
          <div
            style={{
              color: "red",
              marginTop: "10px",
              display: "flex",
              alignItems: "center",
            }}
          >
            {errorMessage}
          </div>
        )}
        {successMessage && (
          <div
            style={{
              color: "green",
              marginTop: "10px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <CheckCircleIcon style={{ marginRight: "5px" }} />
            {successMessage}
          </div>
        )}
      </DialogContent>

      <DialogActions sx={{ padding: "16px" }}>
        <Button
          onClick={handleAddMember}
          sx={{
            color: "#ffffff",
            backgroundColor: "#000000",
            borderRadius: "12px",
            padding: "8px 24px",
            textTransform: "none",
            fontWeight: "bold",
            "&:hover": {
              backgroundColor: "#333333",
            },
          }}
          disabled={loading}
        >
          {loading
            ? member
              ? "Saving..."
              : "Adding..."
            : member
              ? "Save Changes"
              : "Add"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddTeamMemberDialog;
