
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Play, Video } from 'lucide-react';

interface VideoPlayerProps {
  title: string;
  description: string;
  thumbnailUrl?: string;
  videoUrl?: string;
  planType: 'medical' | 'dental' | 'vision';
}

export const VideoPlayer = ({ title, description, thumbnailUrl, videoUrl, planType }: VideoPlayerProps) => {
  const [isOpen, setIsOpen] = useState(false);

  // Mock video content for demo purposes
  const getVideoContent = () => {
    switch (planType) {
      case 'medical':
        return {
          title: "Understanding Medical Plans: PPO vs HMO",
          description: "Learn the key differences between PPO and HMO plans, including costs, flexibility, and how to choose the right one for your needs.",
          duration: "3:24"
        };
      case 'dental':
        return {
          title: "Dental Benefits Explained",
          description: "Discover how dental insurance works, what's covered, and how to maximize your benefits for routine and major dental work.",
          duration: "2:45"
        };
      case 'vision':
        return {
          title: "Vision Benefits Overview", 
          description: "Learn about vision coverage for eye exams, glasses, contacts, and how to use your benefits effectively.",
          duration: "2:15"
        };
      default:
        return { title, description, duration: "3:00" };
    }
  };

  const videoContent = getVideoContent();

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Play className="w-3 h-3" />
          Watch Video
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Video className="w-5 h-5" />
            {videoContent.title}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {/* Video Player Placeholder */}
          <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center relative">
            <div className="text-center text-white">
              <Play className="w-16 h-16 mx-auto mb-4 opacity-75" />
              <p className="text-lg font-medium">{videoContent.title}</p>
              <p className="text-sm opacity-75">Duration: {videoContent.duration}</p>
            </div>
            <Button 
              size="lg"
              className="absolute"
              onClick={() => {
                // In a real implementation, this would start video playback
                console.log('Playing video:', videoContent.title);
              }}
            >
              <Play className="w-6 h-6 mr-2" />
              Play Video
            </Button>
          </div>
          
          {/* Video Description */}
          <Card>
            <CardContent className="p-4">
              <h4 className="font-medium mb-2">What you'll learn:</h4>
              <p className="text-sm text-muted-foreground">{videoContent.description}</p>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
