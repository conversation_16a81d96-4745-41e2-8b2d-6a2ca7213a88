
import React, { useState } from 'react';
import { MessageCircle, Send, Bot, X } from 'lucide-react';

interface QAItem {
  question: string;
  answer: string;
  category: string;
}

const commonQuestions: QAItem[] = [
  {
    question: "What happens if I don't enroll in benefits?",
    answer: "If you don't enroll during open enrollment, you'll have no medical, dental, or vision coverage for 2025. You can only enroll outside of open enrollment if you have a qualifying life event.",
    category: "enrollment"
  },
  {
    question: "Can I change my plan after enrollment?",
    answer: "You can only change your benefits during the next open enrollment period or within 30 days of a qualifying life event (marriage, birth of child, job loss of spouse, etc.).",
    category: "changes"
  },
  {
    question: "What's the difference between HMO and PPO?",
    answer: "PPO plans offer more flexibility to see any doctor without referrals but cost more. HMO plans require you to choose a primary care physician and get referrals for specialists, but have lower costs.",
    category: "plans"
  },
  {
    question: "How much will I pay per paycheck?",
    answer: "Your paycheck deduction depends on the plans you select and your family coverage level. Medical plans range from $45-120/paycheck, dental $6-15/paycheck, vision $3-8/paycheck.",
    category: "costs"
  },
  {
    question: "Are my current doctors covered?",
    answer: "Use the provider directory links in each plan to check if your preferred doctors are in-network. PPO plans typically have larger networks than HMO plans.",
    category: "providers"
  }
];

interface PlanQADialogProps {
  selectedPlans?: {
    medical?: any;
    dental?: any;
    vision?: any;
  };
}

export const PlanQADialog = ({ selectedPlans }: PlanQADialogProps) => {
  const [question, setQuestion] = useState('');
  const [chatHistory, setChatHistory] = useState<Array<{ type: 'question' | 'answer'; content: string }>>([]);
  const [isOpen, setIsOpen] = useState(false);

  const handleAskQuestion = () => {
    if (!question.trim()) return;

    setChatHistory(prev => [...prev, { type: 'question', content: question }]);
    
    // Simple keyword-based answer matching
    const answer = getAnswerForQuestion(question.toLowerCase());
    
    setTimeout(() => {
      setChatHistory(prev => [...prev, { type: 'answer', content: answer }]);
    }, 500);
    
    setQuestion('');
  };

  const getAnswerForQuestion = (question: string): string => {
    if (question.includes('cost') || question.includes('price') || question.includes('pay')) {
      if (selectedPlans?.medical) {
        return `Based on your selected plans: Medical (${selectedPlans.medical.name}) costs $${selectedPlans.medical.cost}/paycheck${selectedPlans?.dental ? `, Dental costs $${selectedPlans.dental.cost}/paycheck` : ''}${selectedPlans?.vision ? `, Vision costs $${selectedPlans.vision.cost}/paycheck` : ''}. Total: $${((selectedPlans.medical?.cost || 0) + (selectedPlans.dental?.cost || 0) + (selectedPlans.vision?.cost || 0)).toFixed(2)}/paycheck.`;
      }
      return "Your total cost depends on which plans you select. Medical plans range from $45-120/paycheck, dental $6-15/paycheck, vision $3-8/paycheck.";
    }
    
    if (question.includes('doctor') || question.includes('provider')) {
      return "To check if your doctors are covered, use the provider directory for each plan. PPO plans generally have larger networks than HMO plans. You can also call the insurance company directly.";
    }
    
    if (question.includes('deductible')) {
      if (selectedPlans?.medical) {
        return `Your selected ${selectedPlans.medical.name} has a $${selectedPlans.medical.deductible || 'varies'} deductible. This is the amount you pay out-of-pocket before insurance starts covering costs.`;
      }
      return "Deductibles vary by plan. Higher deductible plans have lower monthly costs but you pay more upfront when you need care.";
    }
    
    // Find matching common question
    const matchedQ = commonQuestions.find(q => 
      question.includes(q.question.toLowerCase().split(' ').slice(0, 3).join(' '))
    );
    
    if (matchedQ) return matchedQ.answer;
    
    return "I'd be happy to help! For specific questions about plan details, costs, or coverage, please contact HR or the insurance carrier directly. You can also check the plan documents for detailed information.";
  };

  const handleCommonQuestion = (qa: QAItem) => {
    setChatHistory(prev => [
      ...prev,
      { type: 'question', content: qa.question },
      { type: 'answer', content: qa.answer }
    ]);
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
      >
        <MessageCircle className="w-4 h-4" />
        Ask Questions
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Bot className="w-5 h-5 text-blue-500" />
                  Benefits Q&A Assistant
                </h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              {/* Common Questions */}
              <div>
                <h4 className="font-medium mb-2">Common Questions:</h4>
                <div className="flex flex-wrap gap-2">
                  {commonQuestions.map((qa, index) => (
                    <button
                      key={index}
                      onClick={() => handleCommonQuestion(qa)}
                      className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                    >
                      {qa.question.slice(0, 30)}...
                    </button>
                  ))}
                </div>
              </div>

              {/* Chat History */}
              <div className="h-64 border rounded-lg p-4 overflow-y-auto">
                <div className="space-y-3">
                  {chatHistory.map((item, index) => (
                    <div key={index} className={`flex ${item.type === 'question' ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-[80%] p-3 rounded-lg ${
                        item.type === 'question'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100'
                      }`}>
                        {item.content}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Question Input */}
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Ask a question about your benefits..."
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAskQuestion()}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                />
                <button
                  onClick={handleAskQuestion}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
