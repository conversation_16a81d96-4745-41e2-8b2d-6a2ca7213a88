'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../../../redux/store';
import {
  addMessage,
  ChatMessage,
  setIsLoading,
} from '../../../../redux/reducers/qHarmonyBotSlice';
import { sendChatMessage } from '../../../../middleware/chatbot_middleware';
import { getUsersCompanyId } from '../../../../redux/reducers/userSlice';

interface ChatModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ChatModal: React.FC<ChatModalProps> = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [inputMessage, setInputMessage] = useState('');

  // Redux selectors
  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));
  const userId = useSelector((state: RootState) => state.user._id);
  const userDetails = useSelector((state: RootState) => state.user.userProfile);
  const chatHistory = useSelector((state: RootState) => state.qHarmonyBot.chatHistory);
  const isLoading = useSelector((state: RootState) => state.qHarmonyBot.isLoading);

  const handleSendMessage = (message: string) => {
    if (message.trim() === '') return;

    const newMessage: ChatMessage = {
      sender: 'user',
      message: message.replace(/\n/g, '<br/>'),
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(newMessage));
    dispatch(setIsLoading(true));
    sendChatMessage(dispatch, message, userId, companyId);
    setInputMessage('');
  };

  const getInitials = (name: string) => {
    if (!name) return '';
    const [firstName, lastName] = name.split(' ');
    return `${firstName[0].toUpperCase()}${lastName ? lastName[0].toUpperCase() : ''}`;
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage(inputMessage);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize chat with greeting
  useEffect(() => {
    if (isOpen && chatHistory.length === 0 && userDetails.name) {
      dispatch(setIsLoading(true));
      setTimeout(() => {
        const defaultMessage: ChatMessage = {
          sender: 'bot',
          message: `Hey ${userDetails.name}, how can I help you with your benefits enrollment today?`,
          timestamp: new Date().toISOString(),
        };
        dispatch(addMessage(defaultMessage));
        dispatch(setIsLoading(false));
      }, 1000);
    }
  }, [isOpen, chatHistory.length, userDetails.name, dispatch]);

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory]);

  const quickReplies = [
    'Explain my plan options',
    'Help me choose coverage',
    'What are the costs?',
    'Enrollment deadline'
  ];

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 10000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      {/* Modal Container */}
      <div style={{
        backgroundColor: '#f6f8fc',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '800px',
        height: '600px',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
        overflow: 'hidden'
      }}>
        {/* Header */}
        <div style={{
          backgroundColor: '#ffffff',
          padding: '16px 24px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Image
              src="/brea.png"
              alt="Brea"
              width={40}
              height={40}
              style={{ borderRadius: '50%' }}
            />
            <div>
              <h3 style={{
                margin: 0,
                fontSize: '18px',
                fontWeight: '600',
                color: '#111827'
              }}>
                Chat with Brea
              </h3>
              <p style={{
                margin: 0,
                fontSize: '14px',
                color: '#6b7280'
              }}>
                Your Benefits Specialist
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#6b7280',
              padding: '4px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            ×
          </button>
        </div>

        {/* Chat Messages */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '16px',
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }}>
          {chatHistory.map((chat, index) => (
            <div
              key={index}
              style={{
                display: 'flex',
                flexDirection: chat.sender === 'user' ? 'row-reverse' : 'row',
                alignItems: 'flex-start',
                gap: '8px'
              }}
            >
              {/* Avatar */}
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: chat.sender === 'user' ? '#000000' : 'transparent',
                color: 'white',
                fontSize: '14px',
                fontWeight: '600',
                flexShrink: 0
              }}>
                {chat.sender === 'user' ? (
                  getInitials(userDetails.name)
                ) : (
                  <Image
                    src="/brea.png"
                    alt="Brea"
                    width={32}
                    height={32}
                    style={{ borderRadius: '50%' }}
                  />
                )}
              </div>

              {/* Message */}
              <div style={{
                maxWidth: '70%',
                backgroundColor: chat.sender === 'user' ? '#000000' : '#ffffff',
                color: chat.sender === 'user' ? '#ffffff' : '#000000',
                padding: '12px 16px',
                borderRadius: chat.sender === 'user' ? '16px 16px 4px 16px' : '16px 16px 16px 4px',
                fontSize: '14px',
                lineHeight: '1.5',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <div
                  dangerouslySetInnerHTML={{
                    __html: chat.sender === 'bot'
                      ? `${chat.message}<br/><small style="color: #6b7280; font-size: 12px;">AI-generated content—verify before use.</small>`
                      : chat.message,
                  }}
                  style={{
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                  }}
                />
                
                {/* Quick Replies */}
                {chat.sender === 'bot' &&
                  chat.message.includes('how can I help you') &&
                  index === chatHistory.length - 1 && (
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '8px',
                      marginTop: '12px'
                    }}>
                      {quickReplies.map((reply) => (
                        <button
                          key={reply}
                          onClick={() => handleSendMessage(reply)}
                          style={{
                            padding: '6px 12px',
                            backgroundColor: '#f3f4f6',
                            color: '#374151',
                            border: '1px solid #d1d5db',
                            borderRadius: '16px',
                            fontSize: '12px',
                            cursor: 'pointer',
                            transition: 'all 0.2s'
                          }}
                          onMouseOver={(e) => {
                            e.currentTarget.style.backgroundColor = '#e5e7eb';
                          }}
                          onMouseOut={(e) => {
                            e.currentTarget.style.backgroundColor = '#f3f4f6';
                          }}
                        >
                          {reply}
                        </button>
                      ))}
                    </div>
                  )}
              </div>
            </div>
          ))}

          {/* Loading Indicator */}
          {isLoading && (
            <div style={{
              display: 'flex',
              alignItems: 'flex-start',
              gap: '8px'
            }}>
              <Image
                src="/brea.png"
                alt="Brea"
                width={32}
                height={32}
                style={{ borderRadius: '50%' }}
              />
              <div style={{
                backgroundColor: '#ffffff',
                padding: '12px 16px',
                borderRadius: '16px 16px 16px 4px',
                fontSize: '14px',
                color: '#6b7280',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                Brea is typing...
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div style={{
          backgroundColor: '#ffffff',
          padding: '16px',
          borderTop: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            gap: '12px',
            alignItems: 'flex-end'
          }}>
            <textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              style={{
                flex: 1,
                padding: '12px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '14px',
                resize: 'none',
                minHeight: '44px',
                maxHeight: '120px',
                outline: 'none',
                fontFamily: 'inherit',
                color: '#000000', // Make text black
                backgroundColor: '#ffffff' // Ensure white background
              }}
              rows={1}
            />
            <button
              onClick={() => handleSendMessage(inputMessage)}
              disabled={!inputMessage.trim()}
              style={{
                padding: '12px 20px',
                backgroundColor: inputMessage.trim() ? '#000000' : '#e5e7eb',
                color: inputMessage.trim() ? '#ffffff' : '#9ca3af',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: inputMessage.trim() ? 'pointer' : 'not-allowed',
                transition: 'all 0.2s'
              }}
            >
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatModal;
