
import React, { useState } from 'react';
import { Play, Video, X } from 'lucide-react';

interface VideoPlayerProps {
  title: string;
  description: string;
  thumbnailUrl?: string;
  videoUrl?: string;
  planType: 'medical' | 'dental' | 'vision';
}

export const VideoPlayer = ({ title, description, thumbnailUrl, videoUrl, planType }: VideoPlayerProps) => {
  const [isOpen, setIsOpen] = useState(false);

  // Mock video content for demo purposes
  const getVideoContent = () => {
    switch (planType) {
      case 'medical':
        return {
          title: "Understanding Medical Plans: PPO vs HMO",
          description: "Learn the key differences between PPO and HMO plans, including costs, flexibility, and how to choose the right one for your needs.",
          duration: "3:24"
        };
      case 'dental':
        return {
          title: "Dental Benefits Explained",
          description: "Discover how dental insurance works, what's covered, and how to maximize your benefits for routine and major dental work.",
          duration: "2:45"
        };
      case 'vision':
        return {
          title: "Vision Benefits Overview", 
          description: "Learn about vision coverage for eye exams, glasses, contacts, and how to use your benefits effectively.",
          duration: "2:15"
        };
      default:
        return { title, description, duration: "3:00" };
    }
  };

  const videoContent = getVideoContent();

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm"
      >
        <Play className="w-3 h-3" />
        Watch Video
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Video className="w-5 h-5" />
                  {videoContent.title}
                </h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              {/* Video Player Placeholder */}
              <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center relative">
                <div className="text-center text-white">
                  <Play className="w-16 h-16 mx-auto mb-4 opacity-75" />
                  <p className="text-lg font-medium">{videoContent.title}</p>
                  <p className="text-sm opacity-75">Duration: {videoContent.duration}</p>
                </div>
                <button
                  className="absolute px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center gap-2"
                  onClick={() => {
                    // In a real implementation, this would start video playback
                    console.log('Playing video:', videoContent.title);
                  }}
                >
                  <Play className="w-6 h-6" />
                  Play Video
                </button>
              </div>

              {/* Video Description */}
              <div className="bg-white border rounded-lg p-4">
                <h4 className="font-medium mb-2">What you'll learn:</h4>
                <p className="text-sm text-gray-600">{videoContent.description}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
