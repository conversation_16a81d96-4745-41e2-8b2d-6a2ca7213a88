import express from 'express';
import logger from '../utils/logger';
import UserModelClass from '../nosql/user.model';
import { isValidObjectId } from '../utils/validation';

/**
 * EmployeeEnrollmentMiddleware
 * 
 * Middleware for Employee Enrollment operations with:
 * - Authentication and authorization
 * - Request validation
 * - Response formatting
 * - Error handling
 * 
 * 🎯 PATTERN: Follows Plan Assignment middleware pattern
 */
class EmployeeEnrollmentMiddleware {

  // ===== AUTHENTICATION & AUTHORIZATION =====

  /**
   * Validate user authentication and get user data
   */
  static async validateUserAuth(request: express.Request, response: express.Response, next: express.NextFunction) {
    try {
      const userId = request.headers['user-id'] as string;

      if (!userId) {
        return response.status(401).json({ error: 'User ID required in headers' });
      }

      if (!isValidObjectId(userId)) {
        return response.status(400).json({ error: 'Invalid user ID format' });
      }

      // Get user data
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        return response.status(404).json({ error: 'User not found' });
      }

      // Attach user data to request
      (request as any).userId = userId;
      (request as any).user = user;

      next();
    } catch (error) {
      logger.error('Error in validateUserAuth middleware:', error);
      return response.status(500).json({ error: 'Internal server error during authentication' });
    }
  }

  /**
   * Validate user has enrollment permissions
   */
  static validateEnrollmentPermissions(request: express.Request, response: express.Response, next: express.NextFunction) {
    try {
      const user = (request as any).user;

      // SuperAdmins and Brokers have full access
      if (UserModelClass.isSuperAdmin(user) || user.isBroker) {
        return next();
      }

      // Company admins and employees have limited access
      if (user.isAdmin || user.companyId) {
        return next();
      }

      return response.status(403).json({ 
        error: 'Access denied. Insufficient permissions for enrollment operations.' 
      });
    } catch (error) {
      logger.error('Error in validateEnrollmentPermissions middleware:', error);
      return response.status(500).json({ error: 'Internal server error during permission validation' });
    }
  }

  // ===== REQUEST VALIDATION =====

  /**
   * Validate enrollment creation request
   */
  static validateEnrollmentCreation(request: express.Request, response: express.Response, next: express.NextFunction) {
    try {
      const { employeeId, planAssignmentId, coverageTier } = request.body;

      const errors: string[] = [];

      // Required fields
      if (!employeeId) errors.push('employeeId is required');
      if (!planAssignmentId) errors.push('planAssignmentId is required');
      if (!coverageTier) errors.push('coverageTier is required');

      // ObjectId validation
      if (employeeId && !isValidObjectId(employeeId)) {
        errors.push('Invalid employeeId format');
      }
      if (planAssignmentId && !isValidObjectId(planAssignmentId)) {
        errors.push('Invalid planAssignmentId format');
      }

      if (errors.length > 0) {
        return response.status(400).json({ 
          error: 'Validation failed', 
          details: errors 
        });
      }

      next();
    } catch (error) {
      logger.error('Error in validateEnrollmentCreation middleware:', error);
      return response.status(500).json({ error: 'Internal server error during request validation' });
    }
  }

  /**
   * Validate enrollment update request
   */
  static validateEnrollmentUpdate(request: express.Request, response: express.Response, next: express.NextFunction) {
    try {
      const { enrollmentId } = request.params;

      if (!enrollmentId) {
        return response.status(400).json({ error: 'enrollmentId parameter is required' });
      }

      if (!isValidObjectId(enrollmentId)) {
        return response.status(400).json({ error: 'Invalid enrollmentId format' });
      }

      next();
    } catch (error) {
      logger.error('Error in validateEnrollmentUpdate middleware:', error);
      return response.status(500).json({ error: 'Internal server error during request validation' });
    }
  }

  /**
   * Validate bulk operation request
   */
  static validateBulkOperation(request: express.Request, response: express.Response, next: express.NextFunction) {
    try {
      const { employeeId, planAssignmentIds } = request.body;

      const errors: string[] = [];

      if (!employeeId) errors.push('employeeId is required');
      if (!planAssignmentIds || !Array.isArray(planAssignmentIds)) {
        errors.push('planAssignmentIds array is required');
      }

      if (employeeId && !isValidObjectId(employeeId)) {
        errors.push('Invalid employeeId format');
      }

      if (planAssignmentIds && Array.isArray(planAssignmentIds)) {
        planAssignmentIds.forEach((id, index) => {
          if (!isValidObjectId(id)) {
            errors.push(`Invalid planAssignmentId format at index ${index}`);
          }
        });
      }

      if (errors.length > 0) {
        return response.status(400).json({ 
          error: 'Validation failed', 
          details: errors 
        });
      }

      next();
    } catch (error) {
      logger.error('Error in validateBulkOperation middleware:', error);
      return response.status(500).json({ error: 'Internal server error during request validation' });
    }
  }

  // ===== RESPONSE FORMATTING =====

  /**
   * Format enrollment created response
   */
  static enrollmentCreated(response: express.Response, enrollment: any, eligibilityDetails?: any) {
    return response.status(201).json({
      success: true,
      message: 'Enrollment created and activated successfully',
      enrollment,
      eligibilityDetails,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Format enrollment retrieved response
   */
  static enrollmentRetrieved(response: express.Response, enrollment: any) {
    return response.status(200).json({
      success: true,
      message: 'Enrollment retrieved successfully',
      enrollment,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Format enrollments listed response
   */
  static enrollmentsListed(response: express.Response, data: any) {
    return response.status(200).json({
      success: true,
      message: 'Enrollments retrieved successfully',
      enrollments: data.enrollments,
      count: data.count || data.enrollments?.length || 0,
      appliedFilters: data.appliedFilters,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Format enrollment updated response
   */
  static enrollmentUpdated(response: express.Response, enrollment: any) {
    return response.status(200).json({
      success: true,
      message: 'Enrollment updated successfully',
      enrollment,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Format enrollment deleted response
   */
  static enrollmentDeleted(response: express.Response) {
    return response.status(200).json({
      success: true,
      message: 'Enrollment deleted successfully',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Format status operation response
   */
  static statusOperationCompleted(response: express.Response, result: any) {
    return response.status(200).json({
      success: true,
      message: result.message || 'Status operation completed successfully',
      enrollment: result.enrollment,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Format bulk operation response
   */
  static bulkOperationCompleted(response: express.Response, result: any) {
    return response.status(200).json({
      success: true,
      message: result.message || 'Bulk operation completed successfully',
      enrollmentIds: result.enrollmentIds,
      calculatedCosts: result.calculatedCosts,
      successCount: result.successCount,
      failureCount: result.failureCount || 0,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Format eligibility check response
   */
  static eligibilityChecked(response: express.Response, result: any) {
    return response.status(200).json({
      success: true,
      message: 'Eligibility check completed',
      isEligible: result.isEligible,
      eligibilityDetails: result.eligibilityDetails,
      employee: result.employee,
      planAssignment: result.planAssignment,
      reasons: result.reasons,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Format cost calculation response
   */
  static costCalculated(response: express.Response, result: any) {
    return response.status(200).json({
      success: true,
      message: 'Cost calculation completed',
      calculatedCost: result.calculatedCost,
      planAssignment: result.planAssignment,
      employee: result.employee,
      eligibility: result.eligibility,
      timestamp: new Date().toISOString()
    });
  }

  // ===== ERROR HANDLING =====

  /**
   * Handle service errors with appropriate HTTP status
   */
  static handleServiceError(response: express.Response, error: string, statusCode: number = 400) {
    return response.status(statusCode).json({
      success: false,
      error,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle internal server errors
   */
  static internalError(response: express.Response, customMessage?: string) {
    return response.status(500).json({
      success: false,
      error: customMessage || 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle not found errors
   */
  static notFound(response: express.Response, resource: string = 'Resource') {
    return response.status(404).json({
      success: false,
      error: `${resource} not found`,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle access denied errors
   */
  static accessDenied(response: express.Response, message: string = 'Access denied') {
    return response.status(403).json({
      success: false,
      error: message,
      timestamp: new Date().toISOString()
    });
  }

  // ===== MIDDLEWARE CHAINS =====

  /**
   * Middleware chain for enrollment creation
   */
  static forEnrollmentCreation() {
    return [
      this.validateUserAuth,
      this.validateEnrollmentPermissions,
      this.validateEnrollmentCreation
    ];
  }

  /**
   * Middleware chain for enrollment retrieval
   */
  static forEnrollmentRetrieval() {
    return [
      this.validateUserAuth,
      this.validateEnrollmentPermissions
    ];
  }

  /**
   * Middleware chain for enrollment update
   */
  static forEnrollmentUpdate() {
    return [
      this.validateUserAuth,
      this.validateEnrollmentPermissions,
      this.validateEnrollmentUpdate
    ];
  }

  /**
   * Middleware chain for bulk operations
   */
  static forBulkOperations() {
    return [
      this.validateUserAuth,
      this.validateEnrollmentPermissions,
      this.validateBulkOperation
    ];
  }
}

export default EmployeeEnrollmentMiddleware;
