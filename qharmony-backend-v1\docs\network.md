# Azure Benosphere Application Secure Cloud Architecture & Access Documentation

## 1. Executive Summary

This document outlines the network architecture and access control mechanisms implemented for the Benosphere application hosted on Microsoft Azure. The primary purpose is to provide secure remote access for authorized developers and administrators to manage the system's Virtual Machines (VMs) and infrastructure, while ensuring the protection of electronic Protected Health Information (ePHI) in accordance with HIPAA compliance requirements. The architecture leverages Azure Virtual Networking, VPN Gateway Point-to-Site connectivity with Microsoft Entra ID authentication, Network Security Groups, and secure SSH access.

## 2. Architecture Overview

The Benosphere application infrastructure resides within a dedicated Azure Virtual Network (VNet). This VNet is segmented into subnets to organize resources and facilitate security policy enforcement. Remote access for management and development activities is facilitated through a secure Point-to-Site (P2S) VPN connection.

The core components of the architecture are:

* **Azure Virtual Network (VNet):** The private network in Azure hosting application resources.
* **Subnets:** Divisions of the VNet's IP space for resource organization (`default`, `GatewaySubnet`, optional `PrivateEndpointSubnet`).
* **Virtual Machines (VMs):** Hosting the Frontend and Backend application components.
* **Azure Bot Service:** Integrated with the VNet for secure internal communication.
* **MongoDB Atlas:** A managed database service accessed securely via a Private Endpoint within the VNet.
* **VPN Gateway:** Provides secure connectivity between remote clients and the Azure VNet.
* **Network Security Group (NSG):** Acts as a firewall to filter network traffic to the VMs.
* **Microsoft Entra ID (Azure AD):** Used for authenticating developers connecting via the P2S VPN.

The network diagram below visually represents this architecture:

```mermaid
graph TD
    %% External Entities
    EndUsers[End Users<br><i>(Public Internet)</i>]
    Developers[Authorized Developers<br><i>(Remote Locations)</i>]

    %% Azure Cloud Boundary
    subgraph Azure Cloud

        subgraph Azure Subscription 1
            subgraph qharmonyprod Resource Group
                subgraph qharmony-bot-vnet (10.0.0.0/16)

                    subgraph default Subnet (10.0.0.0/24)
                        FrontendVM[Frontend VM<br><i>(Private IP)</i>]
                        BackendVM1[Backend VM 1<br><i>(Private IP)</i>]
                        BackendVM2[Backend VM 2<br><i>(Private IP)</i>]
                        BotVNetInt(Azure Bot Service<br><i>VNet Integrated</i>)
                    end

                    subgraph GatewaySubnet (********/27)
                        VPN_GW(VPN Gateway<br><i>P2S VPN Endpoint</i>)
                    end

                     subgraph PrivateEndpointSubnet (Optional Subnet) %% Represent PE connection within the VNet
                         MongoPE(MongoDB Atlas<br><i>Private Endpoint<br>(Private IP)</i>)
                     end
                end

                NSG[Network Security Group<br><i>(qharmony-bot-nsg)</i>]
            end

            MongoDB_Atlas[(MongoDB Atlas Cluster<br><i>Managed Service</i>)]

            AAD[Microsoft Entra ID<br><i>(Identity & Access Management)</i>]

        end

    %% Connections and Traffic Flow

    %% End User Access (Assuming Public Frontend)
    EndUsers -- HTTPS/443 --> Internet(Public Internet)
    Internet -- HTTPS/443<br><i>(via Frontend Public IP)</i> --> FrontendVM

    %% Developer Access via VPN
    Developers -- P2S VPN Connection --> Internet
    Internet -- Encrypted Tunnel<br>UDP/500,4500<br>TCP/443 --> VPN_GW
    VPN_GW -- Authenticates against --> AAD
    AAD -- Authorizes VPN Access --> VPN_GW
    VPN_GW -- Secure Tunnel<br>established --> qharmony-bot-vnet

    Developers -- SSH/22<br><i>via VPN (Private IP)</i> --> FrontendVM
    Developers -- SSH/22<br><i>via VPN (Private IP)</i> --> BackendVM1
    Developers -- SSH/22<br><i>via VPN (Private IP)</i> --> BackendVM2

    %% Internal Application Communication
    FrontendVM -- App Traffic --> BackendVM1
    FrontendVM -- App Traffic --> BackendVM2
    BackendVM1 -- App Traffic --> BackendVM2

    %% Bot Communication (Assuming VNet Integration for secure access)
    BotVNetInt -- Internal Traffic --> FrontendVM
    BotVNetInt -- Internal Traffic --> BackendVM1
    BotVNetInt -- Internal Traffic --> BackendVM2

    %% Database Access (Assuming Private Endpoint for secure access)
    BackendVM1 -- MongoDB Access<br><i>(SSL/TLS)</i> --> MongoPE
    BackendVM2 -- MongoDB Access<br><i>(SSL/TLS)</i> --> MongoPE
    BotVNetInt -- MongoDB Access<br><i>(SSL/TLS)</i> --> MongoPE
    MongoPE -- Secure Connection --> MongoDB_Atlas

    %% NSG Enforcement
    NSG -- Filters Traffic --> FrontendVM
    NSG -- Filters Traffic --> BackendVM1
    NSG -- Filters Traffic --> BackendVM2

    %% Key NSG Rules & Security Controls
    NSG -- ALLOW SSH/22<br><i>Source: VirtualNetwork</i> --> FrontendVM
    NSG -- ALLOW SSH/22<br><i>Source: VirtualNetwork</i> --> BackendVM1
    NSG -- ALLOW SSH/22<br><i>Source: VirtualNetwork</i> --> BackendVM2
    Internet -- DENY SSH/22<br><i>(Implicitly by NSG Allow Rule Source)</i> --> FrontendVM %% Representing the denial of public SSH


    %% Authentication and Authorization Flow Detail
    Developers -- Authenticates with --> AAD
    AAD -- Assigned to --> "Azure VPN Client" App

    MongoDB_Atlas -- Encryption at Rest --> MongoDB_Atlas %% Self-referencing for data at rest security
```










Does this logic align with the documentation that

Carriers are blue prints that can be plugged and played similalry plans. Carriers are plugged to plans. Plans are plugged to plan assignment. And plan Assignment reference this plan.

So here is a dependency chain

carrier referenceby plan referencedby planassignment referencedby enrllment models.

And we want a way to ensure compatibility.

DELETE AND STATUS HANDLING LOGIC

1) If we ever delete carrier we would want that no plan is associated with that carrier. If we ever delete a plan we would want that not planassignment is associated with that plan, etc. Notice the fundamental logic here.

2) If for example a plan is present and is referenced by planassignment then it can't be deleted. But it can be made inactive. That's where statuses help. (status based)

3) Similarly if there is a carrier being referenced by plan we can not delete it, it can only be made inactive and not be used by future assignments. So that way referencing won't break but at the same time we can use the status fact and not assign that carrier to a plan in future. (status based)

4)  Now an Enrollment object is also referencing a planassignment object. So the plan assignment object can not be deleted. But after the enrollmentendDate and definitely after plan end date not Enrollment object should reference that plan assignment object. (date based) So the apis should be there to filter and give date based here and status based restwhere.

EDIT LOGIC

1) Carrier referencedby Plan. So if carrier changes the plan will reference the same object id. But can create inconsistencies. So similar to delete this shouldn't be done unless the carrier is not referenced by any plan.

2) Plan referenced by planassignment. If the plan is assigned then there must not be edit.

3) For planassignment referenced by enrollment objects there must not be edits during enrollment period, after enrollment and after plan end date.