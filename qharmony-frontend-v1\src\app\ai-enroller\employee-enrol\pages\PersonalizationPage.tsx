'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { UserProfile } from '../components/BenefitsEnrollmentBot';

interface PersonalizationPageProps {
  onComplete: (profile: Partial<UserProfile>) => void;
}

export const PersonalizationPage: React.FC<PersonalizationPageProps> = ({ onComplete }) => {
  const [familyCoverage, setFamilyCoverage] = useState('');
  const [healthcareUsage, setHealthcareUsage] = useState('');
  const [budgetPreference, setBudgetPreference] = useState('');
  const [maxBudget, setMaxBudget] = useState(100);
  const [healthConsiderations, setHealthConsiderations] = useState<string[]>([]);

  const handleHealthConsiderationChange = (consideration: string, checked: boolean) => {
    if (checked) {
      setHealthConsiderations(prev => [...prev, consideration]);
    } else {
      setHealthConsiderations(prev => prev.filter(item => item !== consideration));
    }
  };

  const handleSubmit = () => {
    const profile: Partial<UserProfile> = {
      familyStatus: familyCoverage as any,
      healthStatus: healthcareUsage as any,
      budgetPreference: budgetPreference as any,
      wearGlasses: healthConsiderations.includes('glasses'),
      needsDentalCare: healthConsiderations.includes('dental'),
      hasChronicConditions: healthConsiderations.includes('chronic'),
      prefersLowDeductible: healthConsiderations.includes('doctors')
    };
    onComplete(profile);
  };

  const isFormValid = familyCoverage && healthcareUsage && budgetPreference;

  return (
    <div className="space-y-6">
      {/* Bot Question */}
      <div className="flex gap-3">
        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
          <Bot className="w-5 h-5 text-blue-600" />
        </div>
        <div className="flex-1">
          <div className="bg-gray-100 rounded-2xl px-4 py-3">
            <p className="text-gray-900 font-medium">
              👋 Let me learn about you to give the best recommendations!
            </p>
            <p className="text-gray-600 text-sm mt-1">
              I&apos;ll ask a few quick questions about your healthcare needs, budget preferences, and family situation.
            </p>
          </div>
        </div>
      </div>

      {/* Main Form Card */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="p-6">
          <div className="flex items-center gap-2 mb-6">
            <Brain className="w-6 h-6 text-purple-500" />
            <h2 className="text-xl font-semibold">AI-Powered Personalization</h2>
            <span className="text-yellow-500">✨</span>
          </div>

          <p className="text-gray-700 mb-6">Let me learn about you to provide the most accurate recommendations:</p>

          <div className="space-y-6">
            {/* Enhanced Health Profile */}
            <div className="bg-pink-50 rounded-lg p-4 border border-pink-200">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-pink-500">❤️</span>
                <h3 className="font-medium text-gray-900">Enhanced Health Profile</h3>
              </div>

              {/* Family Coverage Needed */}
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-gray-700">👥</span>
                  <label className="font-medium text-gray-900">Family Coverage Needed</label>
                </div>
                <div className="space-y-2">
                  {[
                    { value: 'single', label: 'Just me (Employee only)' },
                    { value: 'couple', label: 'Me + Spouse/Partner' },
                    { value: 'family', label: 'Me + Family (includes children)' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="familyCoverage"
                        value={option.value}
                        checked={familyCoverage === option.value}
                        onChange={(e) => setFamilyCoverage(e.target.value)}
                        className="w-4 h-4 text-blue-600 mr-3"
                      />
                      <span className="text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Expected Healthcare Usage */}
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-gray-700">🏥</span>
                  <label className="font-medium text-gray-900">Expected Healthcare Usage</label>
                </div>
                <div className="space-y-2">
                  {[
                    { value: 'low', label: 'Low - Just preventive care & checkups' },
                    { value: 'moderate', label: 'Moderate - Occasional visits & some prescriptions' },
                    { value: 'high', label: 'High - Regular specialists, procedures, or chronic conditions' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="healthcareUsage"
                        value={option.value}
                        checked={healthcareUsage === option.value}
                        onChange={(e) => setHealthcareUsage(e.target.value)}
                        className="w-4 h-4 text-blue-600 mr-3"
                      />
                      <span className="text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Budget Preference */}
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-gray-700">💰</span>
                  <label className="font-medium text-gray-900">Budget Preference</label>
                </div>
                <div className="space-y-2">
                  {[
                    { value: 'low', label: 'Lower monthly cost, higher deductible' },
                    { value: 'balanced', label: 'Balanced monthly cost and deductible' },
                    { value: 'high', label: 'Higher monthly cost, lower deductible' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="budgetPreference"
                        value={option.value}
                        checked={budgetPreference === option.value}
                        onChange={(e) => setBudgetPreference(e.target.value)}
                        className="w-4 h-4 text-blue-600 mr-3"
                      />
                      <span className="text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Maximum monthly budget */}
              <div className="mb-4">
                <label className="block font-medium text-gray-900 mb-2">
                  Maximum monthly budget: ${maxBudget}/paycheck
                </label>
                <input
                  type="range"
                  min="50"
                  max="300"
                  value={maxBudget}
                  onChange={(e) => setMaxBudget(Number(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* Health Considerations */}
              <div>
                <label className="block font-medium text-gray-900 mb-3">Health Considerations</label>
                <div className="space-y-2">
                  {[
                    { value: 'glasses', label: 'I wear glasses or contacts' },
                    { value: 'dental', label: 'I need regular dental care or have dental work planned' },
                    { value: 'chronic', label: 'I have ongoing health conditions requiring regular care' },
                    { value: 'doctors', label: 'I have preferred doctors I want to keep seeing' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={healthConsiderations.includes(option.value)}
                        onChange={(e) => handleHealthConsiderationChange(option.value, e.target.checked)}
                        className="w-4 h-4 text-blue-600 mr-3"
                      />
                      <span className="text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-4">
              <button
                onClick={handleSubmit}
                disabled={!isFormValid}
                className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                  isFormValid
                    ? 'bg-gray-800 text-white hover:bg-gray-900'
                    : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                }`}
              >
                Get My Personalized Recommendations
              </button>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4">
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                📺 Watch Video
              </button>
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                ❓ Ask Questions
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};


