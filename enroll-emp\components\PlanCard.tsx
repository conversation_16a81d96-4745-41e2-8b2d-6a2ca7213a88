
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle } from 'lucide-react';

interface PlanCardProps {
  type: 'medical' | 'dental' | 'vision';
  title: string;
  cost: string;
  period: string;
  features: string[];
  recommended?: boolean;
  onSelect: () => void;
}

export const PlanCard = ({ 
  type, 
  title, 
  cost, 
  period, 
  features, 
  recommended = false, 
  onSelect 
}: PlanCardProps) => {
  const getTypeColor = () => {
    switch (type) {
      case 'medical': return 'border-red-200 bg-red-50 dark:bg-red-950 dark:border-red-800';
      case 'dental': return 'border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800';
      case 'vision': return 'border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800';
      default: return 'border-gray-200 bg-gray-50 dark:bg-gray-950 dark:border-gray-800';
    }
  };

  return (
    <Card className={`${getTypeColor()} transition-all hover:shadow-md ${recommended ? 'ring-2 ring-blue-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{title}</CardTitle>
          {recommended && (
            <Badge className="bg-blue-500 text-white">
              💡 Recommended
            </Badge>
          )}
        </div>
        <div className="flex items-baseline gap-1">
          <span className="text-2xl font-bold">{cost}</span>
          <span className="text-sm text-muted-foreground">/{period}</span>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <ul className="space-y-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-2 text-sm">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
        <Button 
          onClick={onSelect}
          className="w-full"
          variant={recommended ? "default" : "outline"}
        >
          Select This Plan
        </Button>
      </CardContent>
    </Card>
  );
};
