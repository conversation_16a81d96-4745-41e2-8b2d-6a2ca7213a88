"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/manage-groups/company/[companyId]/plans/page",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"965ef517a0f5\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZDk1OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk2NWVmNTE3YTBmNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/ai-enroller/manage-groups/company/[companyId]/plans/page.tsx":
/*!******************************************************************************!*\
  !*** ./src/app/ai-enroller/manage-groups/company/[companyId]/plans/page.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineCalendar,HiOutlineClipboardList,HiOutlineCurrencyDollar,HiOutlineOfficeBuilding,HiOutlinePencil,HiOutlinePlus,HiOutlineToggleLeft,HiOutlineToggleRight,HiOutlineTrash!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineCalendar,HiOutlineClipboardList,HiOutlineCurrencyDollar,HiOutlineOfficeBuilding,HiOutlinePencil,HiOutlinePlus,HiOutlineToggleLeft,HiOutlineToggleRight,HiOutlineTrash!=!react-icons/hi */ \"(app-pages-browser)/__barrel_optimize__?names=HiOutlineCalendar,HiOutlineClipboardList,HiOutlineCurrencyDollar,HiOutlineOfficeBuilding,HiOutlinePencil,HiOutlinePlus,HiOutlineToggleLeft,HiOutlineToggleRight,HiOutlineTrash!=!./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../globals.css */ \"(app-pages-browser)/./src/app/globals.css\");\n/* harmony import */ var _components_PlanSelectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/PlanSelectionModal */ \"(app-pages-browser)/./src/app/ai-enroller/manage-groups/company/[companyId]/plans/components/PlanSelectionModal.tsx\");\n/* harmony import */ var _employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../employee-enrol/components/EnrollmentHeader */ \"(app-pages-browser)/./src/app/ai-enroller/employee-enrol/components/EnrollmentHeader.tsx\");\n/* harmony import */ var _services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../services/planAssignmentApi */ \"(app-pages-browser)/./src/app/ai-enroller/manage-groups/services/planAssignmentApi.ts\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../../../../utils/env */ \"(app-pages-browser)/./src/utils/env.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CompanyPlansPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const companyId = params.companyId;\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPlans, setSelectedPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [editingPlan, setEditingPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [carrierFilter, setCarrierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [plansPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [showPlanSelectionModal, setShowPlanSelectionModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contributionType, setContributionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"percentage\");\n    const [coverageTiers, setCoverageTiers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Calculate current plan year in 2025-2026 format\n    const getCurrentPlanYear = ()=>{\n        const currentDate = new Date();\n        const currentYear = currentDate.getFullYear();\n        const nextYear = currentYear + 1;\n        return \"\".concat(currentYear, \"-\").concat(nextYear);\n    };\n    // State to store plan assignment details for cost calculation\n    const [planAssignmentDetails, setPlanAssignmentDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [companyEmployeeCount, setCompanyEmployeeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [estimatedMonthlyCost, setEstimatedMonthlyCost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Calculate estimated monthly cost from plan assignments\n    const calculateEstimatedMonthlyCost = ()=>{\n        if (!companyEmployeeCount || Object.keys(planAssignmentDetails).length === 0) {\n            return 0;\n        }\n        console.log(\"Calculating estimated monthly cost...\");\n        console.log(\"Company employee count:\", companyEmployeeCount);\n        console.log(\"Plan assignment details:\", planAssignmentDetails);\n        // Sum all employee-only coverage tier employer costs from all plan assignments\n        let totalEmployeeOnlyEmployerCost = 0;\n        Object.values(planAssignmentDetails).forEach((assignmentDetails)=>{\n            if (assignmentDetails && assignmentDetails.coverageTiers) {\n                // Find the \"Employee Only\" tier\n                const employeeOnlyTier = assignmentDetails.coverageTiers.find((tier)=>{\n                    var _tier_tierName, _tier_tierName1;\n                    return ((_tier_tierName = tier.tierName) === null || _tier_tierName === void 0 ? void 0 : _tier_tierName.toLowerCase().includes(\"employee only\")) || ((_tier_tierName1 = tier.tierName) === null || _tier_tierName1 === void 0 ? void 0 : _tier_tierName1.toLowerCase()) === \"employee\";\n                });\n                if (employeeOnlyTier) {\n                    console.log(\"Found employee-only tier:\", employeeOnlyTier);\n                    // Add the employer cost (what employer pays) for this plan\n                    totalEmployeeOnlyEmployerCost += employeeOnlyTier.employerCost || 0;\n                }\n            }\n        });\n        console.log(\"Total employee-only employer cost per employee:\", totalEmployeeOnlyEmployerCost);\n        // Calculate total monthly employer cost: employee count × sum of all employee-only employer costs\n        const totalMonthlyEmployerCost = companyEmployeeCount * totalEmployeeOnlyEmployerCost;\n        console.log(\"Total monthly employer cost:\", totalMonthlyEmployerCost);\n        return Math.round(totalMonthlyEmployerCost);\n    };\n    // Get the estimated monthly cost to display\n    const getEstimatedMonthlyCost = ()=>{\n        return estimatedMonthlyCost;\n    };\n    // Fetch plan assignment details for cost calculation\n    const fetchAllPlanAssignmentDetails = async ()=>{\n        const details = {};\n        for (const plan of plans){\n            try {\n                const assignmentDetails = await fetchPlanAssignmentDetails(plan.assignmentId);\n                if (assignmentDetails) {\n                    details[plan.assignmentId] = assignmentDetails;\n                }\n            } catch (error) {\n                console.warn(\"Failed to fetch details for assignment \".concat(plan.assignmentId, \":\"), error);\n            }\n        }\n        setPlanAssignmentDetails(details);\n    };\n    // Function to fetch plan details by ID\n    const fetchPlanDetails = async (planId)=>{\n        const API_BASE_URL = (0,_utils_env__WEBPACK_IMPORTED_MODULE_7__.getApiBaseUrl)();\n        const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_7__.getUserId)();\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId), {\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            if (response.ok) {\n                var _data_plan, _data_plan1, _data_plan2, _data_plan3, _data_plan4, _data_plan5, _data_carrier;\n                const data = await response.json();\n                return {\n                    planName: ((_data_plan = data.plan) === null || _data_plan === void 0 ? void 0 : _data_plan.planName) || \"Unknown Plan\",\n                    planCode: ((_data_plan1 = data.plan) === null || _data_plan1 === void 0 ? void 0 : _data_plan1.planCode) || \"N/A\",\n                    planType: ((_data_plan2 = data.plan) === null || _data_plan2 === void 0 ? void 0 : _data_plan2.planType) || \"N/A\",\n                    coverageType: ((_data_plan3 = data.plan) === null || _data_plan3 === void 0 ? void 0 : _data_plan3.coverageType) || \"Unknown\",\n                    coverageSubTypes: ((_data_plan4 = data.plan) === null || _data_plan4 === void 0 ? void 0 : _data_plan4.coverageSubTypes) || [],\n                    metalTier: ((_data_plan5 = data.plan) === null || _data_plan5 === void 0 ? void 0 : _data_plan5.metalTier) || \"\",\n                    carrierName: ((_data_carrier = data.carrier) === null || _data_carrier === void 0 ? void 0 : _data_carrier.carrierName) || \"Unknown Carrier\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Error fetching plan details for planId:\", planId, error);\n        }\n        return {\n            planName: \"Unknown Plan\",\n            planCode: \"N/A\",\n            planType: \"N/A\",\n            coverageType: \"Unknown\",\n            coverageSubTypes: [],\n            metalTier: \"\",\n            carrierName: \"Unknown Carrier\"\n        };\n    };\n    // Function to fetch company details with employee count (handles broker's own company)\n    const fetchCompanyDetails = async (companyId)=>{\n        const API_BASE_URL = (0,_utils_env__WEBPACK_IMPORTED_MODULE_7__.getApiBaseUrl)();\n        const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_7__.getUserId)();\n        try {\n            // First, check if this is the broker's own company using /employee/company-details\n            const ownCompanyResponse = await fetch(\"\".concat(API_BASE_URL, \"/employee/company-details\"), {\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            if (ownCompanyResponse.ok) {\n                const ownCompanyData = await ownCompanyResponse.json();\n                if (ownCompanyData.company && ownCompanyData.company.isBrokerage && ownCompanyData.company._id === companyId) {\n                    console.log(\"Found broker's own company with employee count:\", ownCompanyData.company.companySize);\n                    return {\n                        _id: ownCompanyData.company._id,\n                        companyName: ownCompanyData.company.name || \"Unknown Company\",\n                        employeeCount: ownCompanyData.company.companySize || 250\n                    };\n                }\n            }\n            // If not broker's own company, try to get from /admin/all-companies (client companies)\n            const companiesResponse = await fetch(\"\".concat(API_BASE_URL, \"/admin/all-companies\"), {\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            if (companiesResponse.ok) {\n                var _companiesData_companies;\n                const companiesData = await companiesResponse.json();\n                const targetCompany = (_companiesData_companies = companiesData.companies) === null || _companiesData_companies === void 0 ? void 0 : _companiesData_companies.find((company)=>company._id === companyId);\n                if (targetCompany) {\n                    console.log(\"Found client company with employee count:\", targetCompany.companySize);\n                    return {\n                        _id: targetCompany._id,\n                        companyName: targetCompany.name || \"Unknown Company\",\n                        employeeCount: targetCompany.companySize || 250\n                    };\n                }\n            }\n            // Fallback: try the pre-enrollment companies API\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/companies/\").concat(companyId), {\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            if (response.ok) {\n                var _data_company, _data_company1, _data_company2, _data_company3;\n                const data = await response.json();\n                return {\n                    _id: ((_data_company = data.company) === null || _data_company === void 0 ? void 0 : _data_company._id) || companyId,\n                    companyName: ((_data_company1 = data.company) === null || _data_company1 === void 0 ? void 0 : _data_company1.companyName) || \"Unknown Company\",\n                    employeeCount: ((_data_company2 = data.company) === null || _data_company2 === void 0 ? void 0 : _data_company2.companySize) || ((_data_company3 = data.company) === null || _data_company3 === void 0 ? void 0 : _data_company3.employeeCount) || 250\n                };\n            }\n        } catch (error) {\n            console.error(\"Error fetching company details:\", error);\n        }\n        // Final fallback\n        return {\n            _id: companyId,\n            companyName: \"Unknown Company\",\n            employeeCount: 250\n        };\n    };\n    const fetchCompanyAndPlans = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch actual company details with real employee count\n            const companyDetails = await fetchCompanyDetails(companyId);\n            setCompanyEmployeeCount(companyDetails.employeeCount || 250);\n            console.log(\"Company employee count set to:\", companyDetails.employeeCount || 250);\n            // Fetch plan assignments for the company using optimized API\n            const result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.getPlanAssignmentsByCompany)(companyId, {\n                includeExpired: true,\n                includePlanData: true\n            });\n            if (result.success && result.data) {\n                const planAssignments = result.data.assignments;\n                console.log(\"Fetched plan assignments:\", planAssignments);\n                // Handle special case where broker has no existing assignments but can create new ones\n                if (result.data.accessDeniedToExisting && result.data.canCreateAssignments) {\n                    console.log(\"\\uD83D\\uDD27 Broker can create new plan assignments for this company\");\n                    // Show empty state but allow plan creation\n                    setCompany(companyDetails);\n                    setPlans([]);\n                    return;\n                }\n                // Transform plan assignments to display format\n                const displayPlans = await Promise.all(planAssignments.map(async (assignment)=>{\n                    var _assignment_planId;\n                    // Get planId as string\n                    const planIdString = typeof assignment.planId === \"string\" ? assignment.planId : ((_assignment_planId = assignment.planId) === null || _assignment_planId === void 0 ? void 0 : _assignment_planId._id) || \"\";\n                    console.log(\"Processing assignment:\", assignment._id, \"with planId:\", planIdString);\n                    // Fetch plan details using the planId\n                    let planDetails = null;\n                    if (planIdString) {\n                        planDetails = await fetchPlanDetails(planIdString);\n                        console.log(\"Fetched plan details:\", planDetails);\n                    }\n                    // Determine display type based on coverage subtypes or coverage type\n                    let displayType = \"Medical\";\n                    if ((planDetails === null || planDetails === void 0 ? void 0 : planDetails.coverageSubTypes) && planDetails.coverageSubTypes.length > 0) {\n                        const primarySubtype = planDetails.coverageSubTypes[0].toLowerCase();\n                        if (primarySubtype.includes(\"dental\")) {\n                            displayType = \"Dental\";\n                        } else if (primarySubtype.includes(\"vision\")) {\n                            displayType = \"Vision\";\n                        } else if (primarySubtype.includes(\"medical\") || primarySubtype.includes(\"health\")) {\n                            displayType = \"Medical\";\n                        } else {\n                            displayType = \"Ancillary\";\n                        }\n                    } else if (planDetails === null || planDetails === void 0 ? void 0 : planDetails.coverageType) {\n                        const coverageType = planDetails.coverageType.toLowerCase();\n                        if (coverageType.includes(\"dental\")) {\n                            displayType = \"Dental\";\n                        } else if (coverageType.includes(\"vision\")) {\n                            displayType = \"Vision\";\n                        } else if (coverageType.includes(\"medical\") || coverageType.includes(\"health\")) {\n                            displayType = \"Medical\";\n                        } else {\n                            displayType = \"Ancillary\";\n                        }\n                    }\n                    // Check if assignment can be edited\n                    let canEdit = false;\n                    let canDelete = false;\n                    try {\n                        const editCheckResult = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.canEditPlanAssignment)(assignment._id);\n                        canEdit = editCheckResult.success && editCheckResult.data ? editCheckResult.data.canEdit : false;\n                        // For simplicity, assume canDelete is same as canEdit for now\n                        canDelete = canEdit;\n                    } catch (error) {\n                        console.warn(\"Failed to check edit permissions for assignment:\", assignment._id);\n                    }\n                    return {\n                        _id: assignment._id,\n                        planName: (planDetails === null || planDetails === void 0 ? void 0 : planDetails.planName) || \"Unknown Plan\",\n                        planCode: (planDetails === null || planDetails === void 0 ? void 0 : planDetails.planCode) || \"N/A\",\n                        carrier: (planDetails === null || planDetails === void 0 ? void 0 : planDetails.carrierName) || \"Unknown Carrier\",\n                        type: displayType,\n                        metalTier: (planDetails === null || planDetails === void 0 ? void 0 : planDetails.metalTier) || \"\",\n                        period: \"\".concat(new Date(assignment.planEffectiveDate).toLocaleDateString(\"en-US\", {\n                            year: \"numeric\",\n                            month: \"short\",\n                            day: \"numeric\"\n                        }), \" - \").concat(new Date(assignment.planEndDate).toLocaleDateString(\"en-US\", {\n                            year: \"numeric\",\n                            month: \"short\",\n                            day: \"numeric\"\n                        })),\n                        status: assignment.status,\n                        assignmentId: assignment._id,\n                        planId: planIdString,\n                        canEdit,\n                        canDelete\n                    };\n                }));\n                console.log(\"Final display plans:\", displayPlans);\n                setCompany(companyDetails);\n                setPlans(displayPlans);\n            } else {\n                setError(result.error || \"Failed to fetch plan assignments\");\n                setPlans([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching data:\", error);\n            setError(\"Failed to fetch plan assignments\");\n            setPlans([]);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        companyId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCompanyAndPlans();\n    }, [\n        fetchCompanyAndPlans\n    ]);\n    // Fetch plan assignment details when plans are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (plans.length > 0) {\n            fetchAllPlanAssignmentDetails();\n        }\n    }, [\n        plans\n    ]);\n    // Calculate estimated monthly cost when plan assignment details and employee count are available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (Object.keys(planAssignmentDetails).length > 0 && companyEmployeeCount > 0) {\n            const calculatedCost = calculateEstimatedMonthlyCost();\n            setEstimatedMonthlyCost(calculatedCost);\n        }\n    }, [\n        planAssignmentDetails,\n        companyEmployeeCount\n    ]);\n    const handleSelectAll = ()=>{\n        // Check if all filtered plans are selected (across all pages)\n        const filteredPlanIds = filteredPlans.map((plan)=>plan._id);\n        const allFilteredSelected = filteredPlanIds.every((id)=>selectedPlans.includes(id));\n        if (allFilteredSelected) {\n            // Deselect all filtered plans\n            setSelectedPlans((prev)=>prev.filter((id)=>!filteredPlanIds.includes(id)));\n        } else {\n            // Select all filtered plans (add to existing selection)\n            setSelectedPlans((prev)=>[\n                    ...new Set([\n                        ...prev,\n                        ...filteredPlanIds\n                    ])\n                ]);\n        }\n    };\n    const handleSelectCurrentPage = ()=>{\n        // Check if all plans on current page are selected\n        const currentPagePlanIds = paginatedPlans.map((plan)=>plan._id);\n        const allCurrentPageSelected = currentPagePlanIds.every((id)=>selectedPlans.includes(id));\n        if (allCurrentPageSelected) {\n            // Deselect all plans on current page\n            setSelectedPlans((prev)=>prev.filter((id)=>!currentPagePlanIds.includes(id)));\n        } else {\n            // Select all plans on current page\n            setSelectedPlans((prev)=>[\n                    ...new Set([\n                        ...prev,\n                        ...currentPagePlanIds\n                    ])\n                ]);\n        }\n    };\n    const handlePlanSelect = (planId)=>{\n        setSelectedPlans((prev)=>prev.includes(planId) ? prev.filter((id)=>id !== planId) : [\n                ...prev,\n                planId\n            ]);\n    };\n    const handleAddNewPlan = ()=>{\n        setShowPlanSelectionModal(true);\n    };\n    const handlePlanSelected = async (selectedPlan)=>{\n        // Workflow 2: Create Fresh Plan Assignment from Template\n        console.log(\"\\uD83D\\uDD04 Workflow 2: Creating fresh plan assignment from template:\", selectedPlan);\n        try {\n            // Create plan assignment with all required fields\n            const currentYear = new Date().getFullYear();\n            const nextYear = currentYear + 1;\n            // Set dates for next year enrollment\n            const planEffectiveDate = \"\".concat(nextYear, \"-01-01\");\n            const planEndDate = \"\".concat(nextYear, \"-12-31\");\n            const enrollmentStartDate = \"\".concat(currentYear, \"-11-01\"); // Current year November\n            const enrollmentEndDate = \"\".concat(currentYear, \"-11-30\"); // Current year November end\n            const assignmentData = {\n                planId: selectedPlan._id,\n                companyId: companyId,\n                // Required fields with defaults\n                rateStructure: \"Composite\",\n                coverageTiers: [\n                    {\n                        tierName: \"Employee Only\",\n                        totalCost: 500,\n                        employerCost: 400,\n                        employeeCost: 100\n                    },\n                    {\n                        tierName: \"Employee + Spouse\",\n                        totalCost: 1000,\n                        employerCost: 800,\n                        employeeCost: 200\n                    },\n                    {\n                        tierName: \"Employee + Child(ren)\",\n                        totalCost: 800,\n                        employerCost: 640,\n                        employeeCost: 160\n                    },\n                    {\n                        tierName: \"Family\",\n                        totalCost: 1500,\n                        employerCost: 1200,\n                        employeeCost: 300\n                    }\n                ],\n                planEffectiveDate,\n                planEndDate,\n                enrollmentStartDate,\n                enrollmentEndDate,\n                // Optional fields with defaults\n                groupNumber: \"GRP-\".concat(companyId, \"-\").concat(selectedPlan._id.slice(-6)),\n                waitingPeriod: {\n                    enabled: false,\n                    days: 0,\n                    rule: \"Immediate\"\n                },\n                enrollmentType: \"Active\",\n                employerContribution: {\n                    contributionType: \"Percentage\",\n                    contributionAmount: 80\n                },\n                employeeContribution: {\n                    contributionType: \"Percentage\",\n                    contributionAmount: 20\n                },\n                ageBandedRates: [],\n                salaryBasedRates: [],\n                planCustomizations: {},\n                status: \"Draft\" // Start as Draft for new assignments\n            };\n            const result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.createPlanAssignment)(assignmentData);\n            if (result.success && result.data) {\n                console.log(\"✅ Fresh plan assignment created successfully:\", result.data);\n                // Immediately refresh the plans list to show the new assignment\n                await fetchCompanyAndPlans();\n            } else {\n                console.error(\"❌ Failed to create plan assignment:\", result.error);\n                alert(\"Failed to assign plan to company: \" + (result.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error creating plan assignment:\", error);\n            alert(\"Failed to assign plan to company. Please try again.\");\n        }\n    };\n    const handleModalClose = ()=>{\n        // No need to refresh here since we refresh immediately after each plan selection\n        setShowPlanSelectionModal(false);\n    };\n    const handlePlanCreated = async (newPlan)=>{\n        // Workflow 2: Create Fresh Plan Assignment for newly created plan\n        console.log(\"\\uD83D\\uDD04 Workflow 2: Creating fresh plan assignment for new plan:\", newPlan);\n        try {\n            // Create plan assignment with all required fields\n            const currentYear = new Date().getFullYear();\n            const nextYear = currentYear + 1;\n            // Set dates for next year enrollment\n            const planEffectiveDate = \"\".concat(nextYear, \"-01-01\");\n            const planEndDate = \"\".concat(nextYear, \"-12-31\");\n            const enrollmentStartDate = \"\".concat(currentYear, \"-11-01\"); // Current year November\n            const enrollmentEndDate = \"\".concat(currentYear, \"-11-30\"); // Current year November end\n            const assignmentData = {\n                planId: newPlan._id,\n                companyId: companyId,\n                // Required fields with defaults\n                rateStructure: \"Composite\",\n                coverageTiers: [\n                    {\n                        tierName: \"Employee Only\",\n                        totalCost: 500,\n                        employerCost: 400,\n                        employeeCost: 100\n                    },\n                    {\n                        tierName: \"Employee + Spouse\",\n                        totalCost: 1000,\n                        employerCost: 800,\n                        employeeCost: 200\n                    },\n                    {\n                        tierName: \"Employee + Child(ren)\",\n                        totalCost: 800,\n                        employerCost: 640,\n                        employeeCost: 160\n                    },\n                    {\n                        tierName: \"Family\",\n                        totalCost: 1500,\n                        employerCost: 1200,\n                        employeeCost: 300\n                    }\n                ],\n                planEffectiveDate,\n                planEndDate,\n                enrollmentStartDate,\n                enrollmentEndDate,\n                // Optional fields with defaults\n                groupNumber: \"GRP-\".concat(companyId, \"-\").concat(newPlan._id.slice(-6)),\n                waitingPeriod: {\n                    enabled: false,\n                    days: 0,\n                    rule: \"Immediate\"\n                },\n                enrollmentType: \"Active\",\n                employerContribution: {\n                    contributionType: \"Percentage\",\n                    contributionAmount: 80\n                },\n                employeeContribution: {\n                    contributionType: \"Percentage\",\n                    contributionAmount: 20\n                },\n                ageBandedRates: [],\n                salaryBasedRates: [],\n                planCustomizations: {},\n                status: \"Draft\" // Start as Draft for new assignments\n            };\n            const result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.createPlanAssignment)(assignmentData);\n            if (result.success && result.data) {\n                // Refresh the plans list to include the new assignment\n                await fetchCompanyAndPlans();\n                alert(\"✅ Plan created and assigned to company successfully!\");\n            } else {\n                console.error(\"❌ Failed to create plan assignment:\", result.error);\n                alert(\"Plan created but failed to assign to company: \" + (result.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error creating plan assignment:\", error);\n            alert(\"Plan created but failed to assign to company. Please try again.\");\n        }\n        setShowPlanSelectionModal(false);\n    };\n    // Function to fetch plan assignment details including coverage tiers\n    const fetchPlanAssignmentDetails = async (assignmentId)=>{\n        const API_BASE_URL = (0,_utils_env__WEBPACK_IMPORTED_MODULE_7__.getApiBaseUrl)();\n        const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_7__.getUserId)();\n        try {\n            console.log(\"Fetching plan assignment details for ID:\", assignmentId);\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId), {\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            console.log(\"Plan assignment fetch response status:\", response.status);\n            if (response.ok) {\n                const data = await response.json();\n                console.log(\"Plan assignment fetch response data:\", data);\n                console.log(\"Assignment object:\", data.assignment);\n                // Handle Mongoose document structure - data might be in _doc\n                const assignment = data.assignment._doc || data.assignment;\n                console.log(\"Processed assignment:\", assignment);\n                console.log(\"Coverage tiers in assignment:\", assignment === null || assignment === void 0 ? void 0 : assignment.coverageTiers);\n                return assignment;\n            } else {\n                console.error(\"Failed to fetch plan assignment details. Status:\", response.status);\n                const errorText = await response.text();\n                console.error(\"Error response:\", errorText);\n            }\n        } catch (error) {\n            console.error(\"Error fetching plan assignment details:\", error);\n        }\n        return null;\n    };\n    const handleEditPlan = async (planId)=>{\n        const plan = plans.find((p)=>p._id === planId);\n        if (plan) {\n            // Check backend can-edit functionality\n            const editCheckResult = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.canEditPlanAssignment)(plan.assignmentId);\n            if (!editCheckResult.success) {\n                alert(\"Failed to check edit permissions. Please try again.\");\n                return;\n            }\n            // Note: Workflow type is determined automatically based on edit permissions\n            // Workflow 2: Direct update (no enrollments) - editCheckResult.data?.canEdit === true\n            // Workflow 1: Clone for rollover (has enrollments) - editCheckResult.data?.canEdit === false\n            // Fetch actual plan assignment details to get current coverage tiers\n            console.log(\"Fetching assignment details for:\", plan.assignmentId);\n            const assignmentDetails = await fetchPlanAssignmentDetails(plan.assignmentId);\n            console.log(\"Assignment details received:\", assignmentDetails);\n            if (assignmentDetails && assignmentDetails.coverageTiers && assignmentDetails.coverageTiers.length > 0) {\n                // Load actual coverage tiers from the assignment\n                console.log(\"Raw coverage tiers from assignment:\", assignmentDetails.coverageTiers);\n                const actualTiers = assignmentDetails.coverageTiers.map((tier, index)=>{\n                    const employerPercent = tier.totalCost > 0 ? Math.round(tier.employerCost / tier.totalCost * 100) : 80;\n                    const tierData = {\n                        id: (index + 1).toString(),\n                        tier: tier.tierName,\n                        premium: tier.totalCost,\n                        employeePercent: employerPercent,\n                        employerPays: tier.employerCost,\n                        employeePays: tier.employeeCost\n                    };\n                    console.log(\"Mapped tier \".concat(index + 1, \":\"), tierData);\n                    return tierData;\n                });\n                console.log(\"Setting actual coverage tiers:\", actualTiers);\n                setCoverageTiers(actualTiers);\n            } else {\n                console.warn(\"No coverage tiers found in assignment, using defaults. Assignment details:\", assignmentDetails);\n                // Reset to default tiers if no data found\n                const defaultTiers = [\n                    {\n                        id: \"1\",\n                        tier: \"Employee Only\",\n                        premium: 450.00,\n                        employeePercent: 80,\n                        employerPays: 360.00,\n                        employeePays: 90.00\n                    },\n                    {\n                        id: \"2\",\n                        tier: \"Employee + Spouse\",\n                        premium: 880.00,\n                        employeePercent: 80,\n                        employerPays: 712.00,\n                        employeePays: 178.00\n                    },\n                    {\n                        id: \"3\",\n                        tier: \"Employee + Children\",\n                        premium: 720.00,\n                        employeePercent: 80,\n                        employerPays: 576.00,\n                        employeePays: 144.00\n                    },\n                    {\n                        id: \"4\",\n                        tier: \"Employee + Family\",\n                        premium: 1250.00,\n                        employeePercent: 80,\n                        employerPays: 1000.00,\n                        employeePays: 250.00\n                    }\n                ];\n                console.log(\"Setting default tiers:\", defaultTiers);\n                setCoverageTiers(defaultTiers);\n            }\n            setEditingPlan(plan);\n            setShowEditModal(true);\n        }\n    };\n    const handleSaveEdit = async ()=>{\n        if (!editingPlan) return;\n        try {\n            var _editCheckResult_data;\n            // Check if the plan can be edited first\n            const editCheckResult = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.canEditPlanAssignment)(editingPlan.assignmentId);\n            if (!editCheckResult.success) {\n                alert(\"Failed to check edit permissions. Please try again.\");\n                return;\n            }\n            let result;\n            const updateData = {\n                rateStructure: \"Composite\",\n                coverageTiers: coverageTiers.map((tier)=>{\n                    // Ensure costs add up correctly to avoid validation errors\n                    const totalCost = parseFloat(tier.premium.toFixed(2));\n                    const employerCost = parseFloat(tier.employerPays.toFixed(2));\n                    const employeeCost = parseFloat(tier.employeePays.toFixed(2));\n                    // Verify the math adds up (backend validation requirement)\n                    const calculatedTotal = employerCost + employeeCost;\n                    const finalTotalCost = Math.abs(totalCost - calculatedTotal) > 0.01 ? calculatedTotal : totalCost;\n                    console.log(\"Tier \".concat(tier.tier, \": total=\").concat(finalTotalCost, \", employer=\").concat(employerCost, \", employee=\").concat(employeeCost));\n                    return {\n                        tierName: tier.tier,\n                        totalCost: finalTotalCost,\n                        employerCost: employerCost,\n                        employeeCost: employeeCost\n                    };\n                }),\n                ageBandedRates: [] // Required to be empty for non-age-banded structures\n            };\n            if ((_editCheckResult_data = editCheckResult.data) === null || _editCheckResult_data === void 0 ? void 0 : _editCheckResult_data.canEdit) {\n                // Workflow 2: Direct Update (no enrollments assigned)\n                console.log(\"\\uD83D\\uDD04 Workflow 2: Updating plan assignment directly (no enrollments)\");\n                result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.updatePlanAssignment)(editingPlan.assignmentId, updateData);\n                if (result.success) {\n                    alert(\"✅ Plan assignment updated successfully.\");\n                } else {\n                    console.error(\"Update failed with error:\", result.error);\n                    alert(\"❌ Failed to update plan assignment: \" + result.error);\n                }\n            } else {\n                // Workflow 1: Clone for Rollover (has enrollments)\n                console.log(\"\\uD83D\\uDD04 Workflow 1: Cloning plan assignment for rollover (has enrollments)\");\n                // Prepare clone data with next year dates\n                const currentYear = new Date().getFullYear();\n                const nextYear = currentYear + 1;\n                const cloneData = {\n                    rateStructure: updateData.rateStructure,\n                    coverageTiers: updateData.coverageTiers,\n                    ageBandedRates: updateData.ageBandedRates,\n                    // Update dates for next year\n                    planEffectiveDate: \"\".concat(nextYear, \"-01-01\"),\n                    planEndDate: \"\".concat(nextYear, \"-12-31\"),\n                    enrollmentStartDate: \"\".concat(currentYear, \"-11-01\"),\n                    enrollmentEndDate: \"\".concat(currentYear, \"-11-30\"),\n                    // Reset status for new assignment\n                    status: \"Draft\"\n                };\n                result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.clonePlanAssignment)(editingPlan.assignmentId, cloneData);\n                if (result.success) {\n                    alert(\"✅ Plan has active enrollments. Created a new \".concat(nextYear, \" version with your changes.\"));\n                } else {\n                    console.error(\"Clone failed with error:\", result.error);\n                    alert(\"❌ Failed to create new version: \" + result.error);\n                }\n            }\n            if (result.success) {\n                // Refresh the plans list\n                await fetchCompanyAndPlans();\n                setShowEditModal(false);\n                setEditingPlan(null);\n            }\n        } catch (error) {\n            console.error(\"Error updating plan assignment:\", error);\n            alert(\"Failed to update plan assignment. Please try again.\");\n        }\n    };\n    const handleCloseModal = ()=>{\n        setShowEditModal(false);\n        setEditingPlan(null);\n    };\n    const handleStatusToggle = async (plan)=>{\n        try {\n            setLoading(true);\n            let result;\n            if (plan.status === \"Active\") {\n                result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.deactivatePlanAssignment)(plan.assignmentId);\n            } else if (plan.status === \"Draft\" || plan.status === \"Inactive\") {\n                result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.activatePlanAssignment)(plan.assignmentId);\n            } else {\n                console.error(\"Cannot toggle status for plan with status:\", plan.status);\n                return;\n            }\n            if (result.success) {\n                var _result_data;\n                // Refresh the plans list to show updated status\n                await fetchCompanyAndPlans();\n                console.log(\"Plan status updated successfully:\", (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.message);\n            } else {\n                console.error(\"Failed to update plan status:\", result.error);\n                setError(result.error || \"Failed to update plan status\");\n            }\n        } catch (error) {\n            console.error(\"Error toggling plan status:\", error);\n            setError(\"Failed to update plan status\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeletePlan = async (plan)=>{\n        if (!confirm('Are you sure you want to delete the plan assignment \"'.concat(plan.planName, '\"? This action cannot be undone.'))) {\n            return;\n        }\n        try {\n            setLoading(true);\n            const result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.deletePlanAssignment)(plan.assignmentId);\n            if (result.success) {\n                // Refresh the plans list to remove the deleted plan\n                await fetchCompanyAndPlans();\n                console.log(\"Plan assignment deleted successfully\");\n            } else {\n                console.error(\"Failed to delete plan assignment:\", result.error);\n                setError(result.error || \"Failed to delete plan assignment\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting plan assignment:\", error);\n            setError(\"Failed to delete plan assignment\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleClonePlan = async (plan)=>{\n        try {\n            setLoading(true);\n            // Clone the plan assignment with updated dates for next year\n            const currentYear = new Date().getFullYear();\n            const nextYear = currentYear + 1;\n            const cloneData = {\n                planEffectiveDate: \"\".concat(nextYear, \"-01-01\"),\n                planEndDate: \"\".concat(nextYear, \"-12-31\"),\n                enrollmentStartDate: \"\".concat(currentYear, \"-11-01\"),\n                enrollmentEndDate: \"\".concat(currentYear, \"-11-30\"),\n                status: \"Draft\" // Start cloned assignments as Draft\n            };\n            const result = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.clonePlanAssignment)(plan.assignmentId, cloneData);\n            if (result.success) {\n                // Refresh the plans list to show the cloned plan\n                await fetchCompanyAndPlans();\n                console.log(\"Plan assignment cloned successfully\");\n            } else {\n                console.error(\"Failed to clone plan assignment:\", result.error);\n                setError(result.error || \"Failed to clone plan assignment\");\n            }\n        } catch (error) {\n            console.error(\"Error cloning plan assignment:\", error);\n            setError(\"Failed to clone plan assignment\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateTier = (id, field, value)=>{\n        setCoverageTiers((prev)=>prev.map((tier)=>{\n                if (tier.id === id) {\n                    const updated = {\n                        ...tier,\n                        [field]: value\n                    };\n                    // Recalculate employer/employee pays based on contribution type\n                    if (contributionType === \"percentage\") {\n                        // In percentage mode: employer % is editable, employer pays is calculated\n                        if (field === \"premium\" || field === \"employeePercent\") {\n                            updated.employerPays = updated.premium * updated.employeePercent / 100;\n                            updated.employeePays = updated.premium - updated.employerPays;\n                        }\n                    } else if (contributionType === \"fixed\") {\n                        // In fixed mode: employer pays is editable, employer % is calculated\n                        if (field === \"premium\" || field === \"employerPays\") {\n                            updated.employeePays = updated.premium - updated.employerPays;\n                            updated.employeePercent = updated.premium > 0 ? updated.employerPays / updated.premium * 100 : 0;\n                        }\n                    }\n                    return updated;\n                }\n                return tier;\n            }));\n    };\n    const deleteTier = (id)=>{\n        setCoverageTiers((prev)=>prev.filter((tier)=>tier.id !== id));\n    };\n    const addNewTier = ()=>{\n        const newId = (coverageTiers.length + 1).toString();\n        const newTier = {\n            id: newId,\n            tier: \"New Coverage Tier\",\n            premium: 500.00,\n            employeePercent: 80,\n            employerPays: 400.00,\n            employeePays: 100.00\n        };\n        setCoverageTiers((prev)=>[\n                ...prev,\n                newTier\n            ]);\n    };\n    const handleContinueWithSelected = ()=>{\n        if (selectedPlans.length === 0) {\n            alert(\"Please select at least one plan to continue.\");\n            return;\n        }\n        // Navigate directly to set dates page (skip enrollment-dates step)\n        const selectedPlanIds = selectedPlans.join(\",\");\n        router.push(\"/ai-enroller/manage-groups/company/\".concat(companyId, \"/set-dates?plans=\").concat(selectedPlanIds));\n    };\n    // Filter plans based on selected filters\n    const filteredPlans = plans.filter((plan)=>{\n        const statusMatch = statusFilter === \"all\" || plan.status === statusFilter;\n        const carrierMatch = carrierFilter === \"all\" || plan.carrier === carrierFilter;\n        return statusMatch && carrierMatch;\n    });\n    // Pagination logic\n    const totalPages = Math.ceil(filteredPlans.length / plansPerPage);\n    const startIndex = (currentPage - 1) * plansPerPage;\n    const endIndex = startIndex + plansPerPage;\n    const paginatedPlans = filteredPlans.slice(startIndex, endIndex);\n    // Reset to page 1 when filters change\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        setCurrentPage(1);\n    }, [\n        statusFilter,\n        carrierFilter\n    ]);\n    const groupedPlans = paginatedPlans.reduce((acc, plan)=>{\n        if (!acc[plan.type]) {\n            acc[plan.type] = [];\n        }\n        acc[plan.type].push(plan);\n        return acc;\n    }, {});\n    // Function to get sorted category order\n    const getSortedCategories = ()=>{\n        const preferredOrder = [\n            \"Medical\",\n            \"Dental\",\n            \"Vision\",\n            \"Ancillary\"\n        ];\n        const availableCategories = Object.keys(groupedPlans);\n        // First add categories in preferred order\n        const sortedCategories = [];\n        for (const preferred of preferredOrder){\n            if (availableCategories.includes(preferred)) {\n                sortedCategories.push(preferred);\n            }\n        }\n        // Then add any remaining categories not in preferred order\n        for (const category of availableCategories){\n            if (!sortedCategories.includes(category)) {\n                sortedCategories.push(category);\n            }\n        }\n        return sortedCategories;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                        lineNumber: 983,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading plan assignments...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                        lineNumber: 984,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                lineNumber: 982,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n            lineNumber: 981,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-bold\",\n                            children: \"Error Loading Plans\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                            lineNumber: 995,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                            lineNumber: 996,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchCompanyAndPlans,\n                            className: \"mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                            lineNumber: 997,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                    lineNumber: 994,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                lineNumber: 993,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n            lineNumber: 992,\n            columnNumber: 7\n        }, this);\n    }\n    const headerActions = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleAddNewPlan,\n        style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"8px\",\n            padding: \"10px 16px\",\n            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"8px\",\n            fontSize: \"14px\",\n            fontWeight: \"500\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlinePlus, {\n                size: 16\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                lineNumber: 1027,\n                columnNumber: 7\n            }, this),\n            \"Add Plan\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n        lineNumber: 1010,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                lineNumber: 1034,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"20px 0\",\n                    borderBottom: \"1px solid #E5E7EB\",\n                    marginBottom: \"24px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"95%\",\n                        margin: \"0 auto\",\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        padding: \"0 2%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/ai-enroller/manage-groups\"),\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"8px\",\n                                        padding: \"8px 12px\",\n                                        background: \"transparent\",\n                                        border: \"1px solid #D1D5DB\",\n                                        borderRadius: \"8px\",\n                                        color: \"#374151\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"← Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                    lineNumber: 1052,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        fontSize: \"24px\",\n                                        fontWeight: \"600\",\n                                        color: \"#111827\",\n                                        margin: 0\n                                    },\n                                    children: company ? \"\".concat(company.companyName, \" - Plans\") : \"Company Plans\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                            lineNumber: 1051,\n                            columnNumber: 11\n                        }, this),\n                        headerActions\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                    lineNumber: 1043,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                lineNumber: 1037,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"95%\",\n                    margin: \"0 auto\",\n                    padding: \"0 2%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"24px 0\",\n                            borderBottom: \"1px solid #E5E7EB\",\n                            marginBottom: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"center\",\n                                    marginBottom: \"24px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"12px\",\n                                        background: \"#EFF6FF\",\n                                        padding: \"16px 24px\",\n                                        borderRadius: \"12px\",\n                                        border: \"1px solid #DBEAFE\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                background: \"#2563EB\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineOfficeBuilding, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1102,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        fontSize: \"20px\",\n                                                        fontWeight: \"600\",\n                                                        color: \"#111827\",\n                                                        margin: 0\n                                                    },\n                                                    children: (company === null || company === void 0 ? void 0 : company.companyName) || \"Loading...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1114,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#6B7280\",\n                                                        margin: 0\n                                                    },\n                                                    children: [\n                                                        (company === null || company === void 0 ? void 0 : company.employeeCount) || 0,\n                                                        \" employees • San Francisco, CA\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1122,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1113,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                lineNumber: 1092,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                                    gap: \"clamp(16px, 2vw, 24px)\",\n                                    maxWidth: \"90%\",\n                                    margin: \"0 auto\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 rounded-xl p-4 border border-blue-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600 font-medium\",\n                                                            children: \"Active Plans\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1144,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-900\",\n                                                            children: filteredPlans.filter((p)=>p.status === \"Active\").length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1145,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-blue-600\",\n                                                            children: \"Currently enrolled\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1146,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1143,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineClipboardList, {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1149,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1148,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1142,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 rounded-xl p-4 border border-green-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600 font-medium\",\n                                                            children: \"Total Premium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1157,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                estimatedMonthlyCost.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1158,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-green-600\",\n                                                            children: \"Monthly cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1159,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1156,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineCurrencyDollar, {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1162,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1161,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1155,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 rounded-xl p-4 border border-purple-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-purple-600 font-medium\",\n                                                            children: \"Employees\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-purple-900\",\n                                                            children: (company === null || company === void 0 ? void 0 : company.employeeCount) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1171,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-purple-600\",\n                                                            children: \"Total covered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1172,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1169,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineOfficeBuilding, {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1175,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1174,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1168,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1167,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-orange-50 rounded-xl p-4 border border-orange-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-orange-600 font-medium\",\n                                                            children: \"Per Employee\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1183,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-orange-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                (company === null || company === void 0 ? void 0 : company.employeeCount) ? Math.round(estimatedMonthlyCost / company.employeeCount) : 0\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1184,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-orange-600\",\n                                                            children: \"Average cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1185,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1182,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineCurrencyDollar, {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1187,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1181,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1180,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                lineNumber: 1134,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                        lineNumber: 1086,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white px-4 sm:px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-md lg:max-w-lg xl:max-w-xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search plans by name, carrier, or type...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                    lineNumber: 1198,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-5 w-5 text-gray-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                    lineNumber: 1203,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                            lineNumber: 1197,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                        lineNumber: 1196,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 sm:px-6 py-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 lg:gap-8\",\n                            children: filteredPlans.map((plan)=>{\n                                var _plan_coverageTiers_, _plan_coverageTiers, _plan_coverageType, _plan_coverageType1, _plan_coverageType2;\n                                const getCategoryColor = (coverageType)=>{\n                                    switch(coverageType === null || coverageType === void 0 ? void 0 : coverageType.toLowerCase()){\n                                        case \"medical\":\n                                        case \"health\":\n                                            return {\n                                                bg: \"bg-blue-50\",\n                                                border: \"border-blue-200\",\n                                                icon: \"bg-blue-600\",\n                                                text: \"text-blue-900\",\n                                                badge: \"bg-blue-100 text-blue-800\"\n                                            };\n                                        case \"dental\":\n                                            return {\n                                                bg: \"bg-green-50\",\n                                                border: \"border-green-200\",\n                                                icon: \"bg-green-600\",\n                                                text: \"text-green-900\",\n                                                badge: \"bg-green-100 text-green-800\"\n                                            };\n                                        case \"vision\":\n                                            return {\n                                                bg: \"bg-purple-50\",\n                                                border: \"border-purple-200\",\n                                                icon: \"bg-purple-600\",\n                                                text: \"text-purple-900\",\n                                                badge: \"bg-purple-100 text-purple-800\"\n                                            };\n                                        default:\n                                            return {\n                                                bg: \"bg-gray-50\",\n                                                border: \"border-gray-200\",\n                                                icon: \"bg-gray-600\",\n                                                text: \"text-gray-900\",\n                                                badge: \"bg-gray-100 text-gray-800\"\n                                            };\n                                    }\n                                };\n                                const colors = getCategoryColor(plan.coverageType);\n                                const monthlyPremium = ((_plan_coverageTiers = plan.coverageTiers) === null || _plan_coverageTiers === void 0 ? void 0 : (_plan_coverageTiers_ = _plan_coverageTiers[0]) === null || _plan_coverageTiers_ === void 0 ? void 0 : _plan_coverageTiers_.totalCost) || 450;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(colors.bg, \" \").concat(colors.border, \" border rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-200 hover:-translate-y-1\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 \".concat(colors.icon, \" rounded-xl flex items-center justify-center shadow-sm\"),\n                                                            children: ((_plan_coverageType = plan.coverageType) === null || _plan_coverageType === void 0 ? void 0 : _plan_coverageType.toLowerCase()) === \"medical\" || ((_plan_coverageType1 = plan.coverageType) === null || _plan_coverageType1 === void 0 ? void 0 : _plan_coverageType1.toLowerCase()) === \"health\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineClipboardList, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1264,\n                                                                columnNumber: 25\n                                                            }, this) : ((_plan_coverageType2 = plan.coverageType) === null || _plan_coverageType2 === void 0 ? void 0 : _plan_coverageType2.toLowerCase()) === \"dental\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M10 2C6.686 2 4 4.686 4 8c0 1.5.5 3 1.5 4.5L10 18l4.5-5.5C15.5 11 16 9.5 16 8c0-3.314-2.686-6-6-6z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1266,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 1271,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 1272,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1270,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-lg \".concat(colors.text, \" truncate\"),\n                                                                    children: plan.planName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 1277,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 truncate\",\n                                                                    children: plan.carrier\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 1278,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(colors.badge),\n                                                            children: plan.coverageType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1283,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        plan.metalTier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(plan.metalTier === \"Gold\" ? \"bg-yellow-100 text-yellow-800\" : plan.metalTier === \"Silver\" ? \"bg-gray-100 text-gray-800\" : plan.metalTier === \"Bronze\" ? \"bg-orange-100 text-orange-800\" : plan.metalTier === \"Platinum\" ? \"bg-purple-100 text-purple-800\" : \"bg-blue-100 text-blue-800\"),\n                                                            children: plan.metalTier\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1287,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(plan.status === \"Active\" ? \"bg-green-100 text-green-800\" : plan.status === \"Draft\" ? \"bg-yellow-100 text-yellow-800\" : plan.status === \"Expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                            children: plan.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1282,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineCalendar, {\n                                                            className: \"w-4 h-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1311,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: plan.period || \"Since \".concat(plan.planEffectiveDate ? new Date(plan.planEffectiveDate).toLocaleDateString() : \"1/1/2024\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1312,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1310,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-baseline gap-2 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-3xl font-bold \".concat(colors.text),\n                                                            children: [\n                                                                \"$\",\n                                                                monthlyPremium\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1318,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500 font-medium\",\n                                                            children: \"per month\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1319,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1317,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleStatusToggle(plan),\n                                                                disabled: loading || plan.status === \"Expired\",\n                                                                className: \"flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(plan.status === \"Active\" ? \"bg-green-100 text-green-700 hover:bg-green-200 hover:shadow-sm\" : plan.status === \"Expired\" ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-yellow-100 text-yellow-700 hover:bg-yellow-200 hover:shadow-sm\"),\n                                                                title: plan.status === \"Active\" ? \"Deactivate plan\" : plan.status === \"Expired\" ? \"Cannot modify expired plan\" : \"Activate plan\",\n                                                                children: plan.status === \"Active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineToggleRight, {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                            lineNumber: 1344,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Active\"\n                                                                    ]\n                                                                }, void 0, true) : plan.status === \"Expired\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineToggleLeft, {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                            lineNumber: 1349,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Expired\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineToggleLeft, {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                            lineNumber: 1354,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Inactive\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1326,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1324,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleEditPlan(plan._id),\n                                                                    disabled: loading,\n                                                                    className: \"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 disabled:opacity-50\",\n                                                                    title: \"Edit plan\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlinePencil, {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 1369,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 1363,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleClonePlan(plan),\n                                                                    disabled: loading,\n                                                                    className: \"p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200 disabled:opacity-50\",\n                                                                    title: \"Clone plan for next year\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                            lineNumber: 1380,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 1379,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDeletePlan(plan),\n                                                                    disabled: loading,\n                                                                    className: \"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 disabled:opacity-50\",\n                                                                    title: \"Delete plan\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineCalendar_HiOutlineClipboardList_HiOutlineCurrencyDollar_HiOutlineOfficeBuilding_HiOutlinePencil_HiOutlinePlus_HiOutlineToggleLeft_HiOutlineToggleRight_HiOutlineTrash_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineTrash, {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 1385,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 1361,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1309,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, plan._id, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                                    lineNumber: 1258,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                        lineNumber: 1213,\n                        columnNumber: 7\n                    }, this),\n                    showPlanSelectionModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PlanSelectionModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: showPlanSelectionModal,\n                        onClose: handleModalClose,\n                        onSelectPlan: handlePlanSelected,\n                        onCreatePlan: handlePlanCreated,\n                        companyId: companyId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                        lineNumber: 1404,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n                lineNumber: 1083,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\company\\\\[companyId]\\\\plans\\\\page.tsx\",\n        lineNumber: 1033,\n        columnNumber: 5\n    }, this);\n}\n_s(CompanyPlansPage, \"eumHui4vlN1ymykJE6P9BYCzjPY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = CompanyPlansPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CompanyPlansPage);\nvar _c;\n$RefreshReg$(_c, \"CompanyPlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/manage-groups/company/[companyId]/plans/page.tsx\n"));

/***/ })

});