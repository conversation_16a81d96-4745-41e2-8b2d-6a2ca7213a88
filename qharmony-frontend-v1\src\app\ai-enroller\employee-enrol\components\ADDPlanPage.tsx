'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { CheckCircle, Play, Shield, BarChart3 } from 'lucide-react';
import { FloatingHelp } from './FloatingHelp';
import { VideoPlayer } from './VideoPlayer';
import { CompareModal } from './CompareModal';
import ChatModal from './ChatModal';
import CustomModal from './CustomModal';

interface ADDPlan {
  id: string;
  name: string;
  cost: number;
  features: string[];
  originalPlan?: any; // Original plan data from API
  coverageTiers?: any[]; // Coverage tiers for cost calculation
}

interface ADDPlanPageProps {
  onPlanSelect: (plan: ADDPlan | null) => void;
  planAssignments?: any[]; // Real plan assignments from API
  selectedCoverageTier?: string; // Coverage tier selected in personalization
}

const ADDPlanPage: React.FC<ADDPlanPageProps> = ({ onPlanSelect, planAssignments, selectedCoverageTier }) => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [showHelp, setShowHelp] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [showWaiveConfirm, setShowWaiveConfirm] = useState(false);
  const [waiveReason, setWaiveReason] = useState<string>('');
  const [showCompare, setShowCompare] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    title: '',
    message: '',
    type: 'alert' as 'alert' | 'confirm' | 'success' | 'error',
    onConfirm: () => {},
    confirmText: 'OK',
    cancelText: 'Cancel'
  });

  // Modal helper function
  const showAlert = (title: string, message: string, type: 'alert' | 'success' | 'error' = 'alert') => {
    setModalConfig({
      title,
      message,
      type,
      onConfirm: () => {},
      confirmText: 'OK',
      cancelText: 'Cancel'
    });
    setShowModal(true);
  };

  // Helper function to calculate cost based on selected coverage tier
  const calculateCostForTier = (plan: any): number => {
    if (!selectedCoverageTier || !plan.coverageTiers) {
      return plan.cost || 0;
    }

    const matchingTier = plan.coverageTiers.find(
      (tier: any) => tier.tierName === selectedCoverageTier
    );

    return matchingTier ? matchingTier.employeeCost : (plan.cost || 0);
  };

  // Use real plan assignments if available, otherwise show empty state
  const addPlans: ADDPlan[] = planAssignments && planAssignments.length > 0
    ? planAssignments.map((plan: any) => ({
        id: plan.id,
        name: plan.name,
        cost: calculateCostForTier(plan),
        features: plan.features || ['Accidental death coverage', 'Dismemberment benefits', '24/7 protection'],
        // Preserve original plan data for summary page
        originalPlan: plan,
        coverageTiers: plan.coverageTiers // Keep coverage tiers for cost calculation
      }))
    : [];

  console.log('🛡️ AD&D plans to display:', addPlans);
  console.log('🛡️ Plan assignments received:', planAssignments);
  console.log('🛡️ Selected coverage tier:', selectedCoverageTier);

  // Load previous selection on component mount
  React.useEffect(() => {
    const savedSelection = localStorage.getItem('selectedADDPlan');
    const savedWaive = localStorage.getItem('addWaived');

    if (savedWaive === 'true') {
      setSelectedPlan('WAIVE');
    } else if (savedSelection) {
      try {
        const planData = JSON.parse(savedSelection);
        setSelectedPlan(planData.id);
      } catch (e) {
        console.error('Error parsing saved AD&D plan:', e);
      }
    }
  }, []);

  const handlePlanSelect = (planId: string) => {
    // Toggle selection - if clicking the same plan, deselect it
    if (selectedPlan === planId) {
      setSelectedPlan(null);
      localStorage.removeItem('selectedADDPlan');
      localStorage.removeItem('addWaived');
      onPlanSelect(null);
      return;
    }

    // Single plan selection
    setSelectedPlan(planId);

    // Store selected plan in localStorage
    const selectedPlanData = addPlans.find(p => p.id === planId);
    localStorage.setItem('selectedADDPlan', JSON.stringify(selectedPlanData));
    localStorage.removeItem('addWaived'); // Clear waive status

    // Call the parent callback with the selected plan
    onPlanSelect(selectedPlanData || null);
  };

  const handleWaiveSelect = () => {
    // Toggle waive selection - if already waived, deselect it
    if (selectedPlan === 'WAIVE') {
      setSelectedPlan(null);
      localStorage.removeItem('addWaived');
      localStorage.removeItem('addWaiveReason');
      onPlanSelect(null);
      return;
    }

    setShowWaiveConfirm(true);
  };

  const confirmWaive = () => {
    if (!waiveReason) {
      showAlert('Validation Error', 'Please select a reason for waiving AD&D coverage.', 'error');
      return;
    }

    setSelectedPlan('WAIVE');
    localStorage.setItem('addWaived', 'true');
    localStorage.setItem('addWaiveReason', waiveReason);
    localStorage.removeItem('selectedADDPlan'); // Clear plan selection
    setShowWaiveConfirm(false);
    setWaiveReason(''); // Reset reason

    // Call parent callback with null to indicate waived
    onPlanSelect(null);
  };

  const cancelWaive = () => {
    setShowWaiveConfirm(false);
    setWaiveReason(''); // Reset reason when canceling
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '24px',
      fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: 0 
          }}>
            🛡️ Let&apos;s add Accidental Death & Dismemberment (AD&D) coverage to your plan.
          </p>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: '8px 0 0 0' 
          }}>
            This provides additional financial protection for you and your family in case of covered accidents.
          </p>
        </div>
      </div>

      {/* AD&D Plan Selection */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        border: '1px solid #e5e7eb', 
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <Shield style={{ width: '20px', height: '20px', color: '#7c3aed' }} />
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: '600', 
            color: '#111827',
            margin: 0
          }}>
            Accidental Death & Dismemberment (AD&D) Plan Selection
          </h2>
        </div>

        <p style={{ 
          color: '#6b7280', 
          marginBottom: '24px',
          margin: 0
        }}>
          Choose your AD&D coverage for additional protection:
        </p>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {addPlans.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '40px 20px',
              backgroundColor: '#f9fafb',
              borderRadius: '8px',
              border: '2px dashed #e5e7eb'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>🛡️</div>
              <h3 style={{
                fontSize: '18px',
                fontWeight: '600',
                color: '#374151',
                margin: '0 0 8px 0'
              }}>
                No AD&D Plans Available
              </h3>
              <p style={{
                color: '#6b7280',
                fontSize: '14px',
                margin: 0,
                lineHeight: '1.5'
              }}>
                Your company hasn&apos;t set up any AD&D plan assignments yet.
                <br />
                Please contact your HR administrator for more information.
              </p>
            </div>
          ) : (
            <>
              {/* Regular Plan Options */}
              {addPlans.map((plan) => {
            const isSelected = selectedPlan === plan.id;
            return (
              <div
                key={plan.id}
                style={{
                  border: isSelected ? '2px solid #7c3aed' : '2px solid #e5e7eb',
                  borderRadius: '8px',
                  padding: '20px',
                  backgroundColor: isSelected ? '#faf5ff' : '#f9fafb',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={() => handlePlanSelect(plan.id)}
              >
              <div style={{ marginBottom: '12px' }}>
                <h3 style={{ 
                  fontSize: '18px', 
                  fontWeight: '600', 
                  color: '#111827',
                  margin: 0
                }}>
                  {plan.name}
                </h3>
                <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '4px' }}>
                  <span style={{ 
                    fontSize: '24px', 
                    fontWeight: '700', 
                    color: '#111827' 
                  }}>
                    ${plan.cost.toFixed(2)}
                  </span>
                  <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                {plan.features.map((feature, index) => (
                  <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <CheckCircle style={{ width: '16px', height: '16px', color: '#10b981', flexShrink: 0 }} />
                    <span style={{ color: '#374151', fontSize: '14px' }}>{feature}</span>
                  </div>
                ))}
              </div>

              <button
                style={{
                  width: '100%',
                  backgroundColor: isSelected ? '#000000' : '#f3f4f6',
                  color: isSelected ? 'white' : '#6b7280',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  fontWeight: '500',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                {isSelected ? '✓ Selected' : 'Select This Plan'}
              </button>
              </div>
            );
          })}

              {/* Waive Coverage Option */}
              <div
                style={{
                  border: selectedPlan === 'WAIVE'
                    ? '2px solid #ef4444'
                    : '2px solid #e5e7eb',
                  borderRadius: '8px',
                  padding: '20px',
                  backgroundColor: selectedPlan === 'WAIVE'
                    ? '#fef2f2'
                    : '#f9fafb',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={handleWaiveSelect}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                  <div>
                    <h3 style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color: '#111827',
                      margin: 0
                    }}>
                      Waive AD&D Coverage
                    </h3>
                    <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '4px' }}>
                      <span style={{
                        fontSize: '24px',
                        fontWeight: '700',
                        color: '#111827'
                      }}>
                        $0.00
                      </span>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                    </div>
                  </div>
                  <div style={{
                    backgroundColor: '#ef4444',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}>
                    No Coverage
                  </div>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>No AD&D coverage or benefits</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>No financial protection for covered accidents</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>Can only enroll during next open enrollment</span>
                  </div>
                </div>

                <button
                  style={{
                    width: '100%',
                    backgroundColor: selectedPlan === 'WAIVE' ? '#ef4444' : '#f3f4f6',
                    color: selectedPlan === 'WAIVE' ? 'white' : '#6b7280',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    fontWeight: '500',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  {selectedPlan === 'WAIVE' ? '✓ Coverage Waived' : 'Waive Coverage'}
                </button>
              </div>
            </>
          )}
        </div>

        {/* Selection Summary */}
        {selectedPlan && (
          <div style={{
            backgroundColor: selectedPlan === 'WAIVE' ? '#fef2f2' : '#faf5ff',
            border: selectedPlan === 'WAIVE' ? '1px solid #fecaca' : '1px solid #e9d5ff',
            borderRadius: '8px',
            padding: '16px',
            marginTop: '16px'
          }}>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '600',
              color: selectedPlan === 'WAIVE' ? '#dc2626' : '#7c3aed',
              margin: '0 0 12px 0'
            }}>
              {selectedPlan === 'WAIVE' ? 'AD&D Coverage Waived' : 'Selected AD&D Plan'}
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {selectedPlan === 'WAIVE' ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: 'white',
                  padding: '12px',
                  borderRadius: '6px'
                }}>
                  <span style={{ fontWeight: '500', color: '#111827' }}>No AD&D Coverage</span>
                  <span style={{ color: '#dc2626', fontWeight: '600' }}>$0.00/paycheck</span>
                </div>
              ) : (() => {
                const plan = addPlans.find(p => p.id === selectedPlan);
                return plan ? (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    padding: '12px',
                    borderRadius: '6px'
                  }}>
                    <span style={{ fontWeight: '500', color: '#111827' }}>{plan.name}</span>
                    <span style={{ color: '#7c3aed', fontWeight: '600' }}>${plan.cost.toFixed(2)}/paycheck</span>
                  </div>
                ) : null;
              })()}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          gap: '12px',
          paddingTop: '24px',
          borderTop: '1px solid #e5e7eb',
          marginTop: '24px'
        }}>
          <button
            onClick={() => setShowChatModal(true)}
            style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <span style={{ color: '#2563eb' }}>❓</span>
            Ask Questions
          </button>
          <button
            onClick={() => setShowVideo(true)}
            style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <Play style={{ width: '16px', height: '16px', color: '#2563eb' }} />
            Watch Video
          </button>
          <button
            onClick={() => setShowCompare(true)}
            style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <BarChart3 style={{ width: '16px', height: '16px', color: '#2563eb' }} />
            Compare Plans
          </button>
        </div>
      </div>

      {/* Waive Confirmation Modal */}
      {showWaiveConfirm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            padding: '32px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '90vh',
            overflowY: 'auto',
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
            position: 'relative'
          }}>
            {/* Close Button */}
            <button
              onClick={cancelWaive}
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: '#6b7280',
                padding: '4px'
              }}
            >
              ×
            </button>

            {/* Header */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{
                fontSize: '24px',
                fontWeight: '600',
                color: '#111827',
                margin: '0 0 12px 0'
              }}>
                Waive AD&D Coverage
              </h3>
              <p style={{
                color: '#6b7280',
                fontSize: '14px',
                margin: 0,
                lineHeight: '21px'
              }}>
                Please select a reason for waiving AD&D coverage. This information helps us understand your decision.
              </p>
            </div>

            {/* Reason Options */}
            <div style={{ marginBottom: '32px' }}>
              {[
                {
                  value: 'spouse_partner_plan',
                  title: "Covered under spouse/partner's plan",
                  description: "I have AD&D coverage through my spouse or partner's insurance plan"
                },
                {
                  value: 'parent_plan',
                  title: "Covered under parent's plan",
                  description: "I am covered under my parent's AD&D plan"
                },
                {
                  value: 'other_insurance',
                  title: 'Have other AD&D insurance',
                  description: 'I have AD&D coverage through another insurance provider'
                },
                {
                  value: 'dont_need',
                  title: "Don't need AD&D coverage",
                  description: 'I prefer not to have AD&D coverage at this time'
                },
                {
                  value: 'cost_too_high',
                  title: 'Cost is too high',
                  description: 'The premium cost is beyond my budget'
                }
              ].map((option) => (
                <div
                  key={option.value}
                  style={{
                    border: waiveReason === option.value ? '2px solid #3b82f6' : '1px solid #e5e7eb',
                    borderRadius: '8px',
                    padding: '16px',
                    marginBottom: '12px',
                    cursor: 'pointer',
                    backgroundColor: waiveReason === option.value ? '#eff6ff' : 'white',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={() => setWaiveReason(option.value)}
                >
                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                    <div style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      border: waiveReason === option.value ? '6px solid #3b82f6' : '2px solid #d1d5db',
                      backgroundColor: waiveReason === option.value ? '#3b82f6' : 'white',
                      marginTop: '2px',
                      flexShrink: 0
                    }} />
                    <div>
                      <div style={{
                        fontSize: '14px',
                        fontWeight: '600',
                        color: '#111827',
                        marginBottom: '4px',
                        lineHeight: '21px'
                      }}>
                        {option.title}
                      </div>
                      <div style={{
                        fontSize: '14px',
                        color: '#6b7280',
                        lineHeight: '21px'
                      }}>
                        {option.description}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
              <button
                onClick={cancelWaive}
                style={{
                  padding: '12px 24px',
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  color: '#374151',
                  cursor: 'pointer',
                  fontWeight: '500',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={confirmWaive}
                disabled={!waiveReason}
                style={{
                  padding: '12px 24px',
                  backgroundColor: waiveReason ? '#1f2937' : '#9ca3af',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  cursor: waiveReason ? 'pointer' : 'not-allowed',
                  fontWeight: '500',
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                Continue
                <span style={{ fontSize: '14px' }}>→</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Components */}
      {showHelp && (
        <FloatingHelp onClose={() => setShowHelp(false)} />
      )}

      {showVideo && (
        <VideoPlayer
          title="AD&D Plan Overview"
          description="Learn about your AD&D plan options and benefits"
          planType="vision" // Using vision as planType since AD&D isn't defined in VideoPlayer
          onClose={() => setShowVideo(false)}
        />
      )}

      {showCompare && (
        <CompareModal
          plans={addPlans.map(plan => ({
            id: plan.id,
            name: plan.name,
            type: 'vision' as const, // Using vision type for AD&D (closest match)
            tier: 'Gold' as const,
            monthlyPremium: plan.cost,
            deductible: 0,
            outOfPocketMax: 0, // AD&D doesn't have out-of-pocket max
            features: plan.features,
            network: 'Nationwide'
          }))}
          onClose={() => setShowCompare(false)}
        />
      )}

      {showChatModal && (
        <ChatModal
          isOpen={showChatModal}
          onClose={() => setShowChatModal(false)}
        />
      )}

      {showModal && (
        <CustomModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={modalConfig.title}
          message={modalConfig.message}
          type={modalConfig.type}
          onConfirm={modalConfig.onConfirm}
          confirmText={modalConfig.confirmText}
          cancelText={modalConfig.cancelText}
        />
      )}
    </div>
  );
};

export default ADDPlanPage;
