import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface BenefitsState {
  benefitsPerType: {
    benefitType: string;
    benefits: {
      _id: string;
      companyId: string;
      type: string;
      subType: string;
      heading: string;
      description: string;
      imageS3Urls: string[];
      links: string[];
      isActivated: boolean;
      __v: number;
    }[];
  }[];
  documentsPerBenefit: {
    benefitId: string;
    documents: string[]; // documentObjectKeys
    links: string[]; // links
  };
  viewableDocuments: {
    documentObjectKey: string;
    document: string;
    originalFileName: string;
  }[];
  loadingDocuments: string[];
  snackbarMessage: string;
}

const initialState: BenefitsState = {
  benefitsPerType: [],
  documentsPerBenefit: {
    benefitId: "",
    documents: [],
    links: [],
  },
  viewableDocuments: [],
  loadingDocuments: [],
  snackbarMessage: "",
};

export const benefitsSlice = createSlice({
  name: "benefits",
  initialState,
  reducers: {
    setAllBenefitsPerType: (
      state,
      action: PayloadAction<BenefitsState["benefitsPerType"]>,
    ) => {
      state.benefitsPerType = action.payload;
    },
    upsertBenefitsPerType: (
      state,
      action: PayloadAction<{
        benefitType: string;
        benefits: BenefitsState["benefitsPerType"][0]["benefits"];
      }>,
    ) => {
      const { benefitType, benefits } = action.payload;
      const index = state.benefitsPerType.findIndex(
        (item) => item.benefitType === benefitType,
      );
      if (index !== -1) {
        state.benefitsPerType[index] = { benefitType, benefits };
      } else {
        state.benefitsPerType.push({ benefitType, benefits });
      }
    },
    setDocumentsPerBenefit: (
      state,
      action: PayloadAction<BenefitsState["documentsPerBenefit"]>,
    ) => {
      state.documentsPerBenefit = action.payload;
    },
    setViewableDocuments: (
      state,
      action: PayloadAction<BenefitsState["viewableDocuments"]>,
    ) => {
      const newDocuments = action.payload.filter(
        (newDoc) =>
          !state.viewableDocuments.some(
            (existingDoc) =>
              existingDoc.documentObjectKey === newDoc.documentObjectKey,
          ),
      );
      state.viewableDocuments = [...state.viewableDocuments, ...newDocuments];
    },
    addViewableDocument: (
      state,
      action: PayloadAction<BenefitsState["viewableDocuments"][0]>,
    ) => {
      state.viewableDocuments.push(action.payload);
    },
    clearViewableDocuments: (state) => {
      state.viewableDocuments = [];
    },
    clearBenefitsState: (state) => {
      state.benefitsPerType = [];
      state.documentsPerBenefit = {
        benefitId: "",
        documents: [],
        links: [],
      };
      state.viewableDocuments = [];
      state.loadingDocuments = [];
    },
    setLoadingDocument: (state, action: PayloadAction<string>) => {
      state.loadingDocuments.push(action.payload);
    },
    clearLoadingDocument: (state, action: PayloadAction<string>) => {
      state.loadingDocuments = state.loadingDocuments.filter(
        (doc) => doc !== action.payload,
      );
    },
    // Add a document to a benefit
    addDocument: (
      state,
      action: PayloadAction<{ benefitId: string; document: string }>,
    ) => {
      const { benefitId, document } = action.payload;
      if (state.documentsPerBenefit.benefitId === benefitId) {
        state.documentsPerBenefit.documents = [
          ...state.documentsPerBenefit.documents,
          document,
        ];
      }
    },
    // Delete a document from a benefit
    deleteDocument: (
      state,
      action: PayloadAction<{ benefitId: string; document: string }>,
    ) => {
      const { benefitId, document } = action.payload;
      if (state.documentsPerBenefit.benefitId === benefitId) {
        state.documentsPerBenefit.documents =
          state.documentsPerBenefit.documents.filter((doc) => doc !== document);
        // Remove corresponding viewableDocument entry
        state.viewableDocuments = state.viewableDocuments.filter(
          (viewableDoc) => viewableDoc.documentObjectKey !== document,
        );
      }
    },
    // Add a link to a benefit
    addLink: (
      state,
      action: PayloadAction<{ benefitId: string; link: string }>,
    ) => {
      const { benefitId, link } = action.payload;
      if (state.documentsPerBenefit.benefitId === benefitId) {
        state.documentsPerBenefit.links = [
          ...state.documentsPerBenefit.links,
          link,
        ];
      }
    },
    // Delete a link from a benefit
    deleteLink: (
      state,
      action: PayloadAction<{ benefitId: string; link: string }>,
    ) => {
      const { benefitId, link } = action.payload;
      console.log("DELETE LINK REDUCER: ", state.documentsPerBenefit.benefitId);
      if (state.documentsPerBenefit.benefitId === benefitId) {
        state.documentsPerBenefit.links = state.documentsPerBenefit.links.filter(
          (l) => l !== link,
        );
      }
    },
    setSnackbarMessage: (state, action: PayloadAction<string>) => {
      state.snackbarMessage = action.payload;
    },
    clearSnackbarMessage: (state) => {
      state.snackbarMessage = "";
    },
  },
});

export const {
  setAllBenefitsPerType,
  upsertBenefitsPerType,
  setDocumentsPerBenefit,
  setViewableDocuments,
  addViewableDocument,
  clearViewableDocuments,
  clearBenefitsState,
  setLoadingDocument,
  clearLoadingDocument,
  addDocument,
  deleteDocument,
  addLink,
  deleteLink,
  setSnackbarMessage,
  clearSnackbarMessage,
} = benefitsSlice.actions;

export const selectBenefitById = (
  state: { benefits: BenefitsState },
  benefitId: string,
) => {
  for (const { benefitType, benefits } of state.benefits.benefitsPerType) {
    const benefit = benefits.find((b) => b._id === benefitId);
    if (benefit) {
      return { benefitType, benefit };
    }
  }
  return null;
};

export const selectBenefitsByType = (
  state: { benefits: BenefitsState },
  benefitType: string,
) => {
  return state.benefits.benefitsPerType.find(
    (item) => item.benefitType === benefitType,
  ) || null;
};

export default benefitsSlice.reducer;
