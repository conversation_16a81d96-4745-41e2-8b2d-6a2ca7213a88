import * as admin from 'firebase-admin';
import EnvService from './env.service';

let firebaseAdminApp: admin.app.App | null = null;

/**
 * Get Firebase Admin service account based on environment
 * Uses MONGO_DB_NAME to determine which Firebase project to use
 */
function getServiceAccount() {
  // Only access environment variables when actually needed
  const env = EnvService.env();
  const isTestEnvironment = env.MONGO_DB_NAME === 'testing';

  if (isTestEnvironment) {
    // Test Environment - qharmony-test project
    return {
      type: 'service_account',
      project_id: 'qharmony-test',
      private_key_id: '6daefc4087675803a7777d839615ea2cedcff5fa',
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      client_email: '<EMAIL>',
      client_id: '105084579194679236243',
      auth_uri: 'https://accounts.google.com/o/oauth2/auth',
      token_uri: 'https://oauth2.googleapis.com/token',
      auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
      client_x509_cert_url: 'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40qharmony-test.iam.gserviceaccount.com',
      universe_domain: 'googleapis.com',
    };
  } else {
    // Production Environment - qharmony-dev project (your original)
    return {
      type: 'service_account',
      project_id: 'qharmony-dev',
      private_key_id: '5253a574bcce3d79ba73bcafa608d0631086d4da',
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      client_email: '<EMAIL>',
      client_id: '108829980583547471756',
      auth_uri: 'https://accounts.google.com/o/oauth2/auth',
      token_uri: 'https://oauth2.googleapis.com/token',
      auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
      client_x509_cert_url: 'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-lseai%40qharmony-dev.iam.gserviceaccount.com',
      universe_domain: 'googleapis.com',
    };
  }
}

/**
 * Initialize Firebase Admin with lazy loading
 */
function initializeFirebaseAdmin(): admin.app.App {
  if (firebaseAdminApp) {
    return firebaseAdminApp;
  }

  const serviceAccount = getServiceAccount();

  // Check if admin app is already initialized
  if (admin.apps.length === 0) {
    firebaseAdminApp = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
    });
  } else {
    firebaseAdminApp = admin.apps[0];
  }

  return firebaseAdminApp;
}

/**
 * Get Firebase Admin Auth instance (lazy-loaded)
 */
export function getFirebaseAdminAuth(): admin.auth.Auth {
  const app = initializeFirebaseAdmin();
  return admin.auth(app);
}

/**
 * Get Firebase Admin App instance (lazy-loaded)
 */
export function getFirebaseAdminApp(): admin.app.App {
  return initializeFirebaseAdmin();
}

// Export auth for backward compatibility
export const auth = new Proxy({} as admin.auth.Auth, {
  get(_target, prop) {
    const adminAuth = getFirebaseAdminAuth();
    return (adminAuth as any)[prop];
  }
});
