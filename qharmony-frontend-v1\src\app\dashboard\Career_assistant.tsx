import React, { useEffect, useState } from "react";
import { Box, Typography, Avatar, Button } from "@mui/material";
import DiamondIcon from "@mui/icons-material/Diamond"; // Example icon, replace with your actual diamond image if needed

export default function CareerAssistantCard() {
  
  return (
    <Box
      sx={{
        backgroundColor: "white",
        padding: 2,
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        borderRadius: "30px",
        boxShadow: "none",
        maxWidth: "100%",
        mt: 3,
      }}
    >
      {/* Left side with Avatar */}
      <Box sx={{ display: "flex", alignItems: "center", flexDirection: "row" }}>
        <Avatar
          sx={{
            width: 50,
            height: 50,
            mr: 2,
            backgroundColor: "transparent",
          }}
        >
          <DiamondIcon
            sx={{ width: "100%", height: "100%", color: "#6ccff6" }}
          />
        </Avatar>
        <Box>
          <Box
            sx={{ display: "flex", alignItems: "center", flexDirection: "row" }}
          >
            {/* Career Assistant Text and ADMIN Badge */}
            <Typography
              sx={{
                fontWeight: 700,
                fontSize: "24px",
                display: "flex",
                alignItems: "center",
              }}
            >
              Career Assistant
            </Typography>
            <Box
              sx={{
                bgcolor: "black", // light grey background
                borderRadius: "8px",
                padding: "2px 8px", // padding for the badge
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontWeight: "bold",
                fontSize: "12px",
                color: "white",
                marginLeft: 2, // space between the text and badge
              }}
            >
              Coming Soon
            </Box>
          </Box>
          <Typography
            sx={{
              fontWeight: 500,
              fontSize: "14px",
              color: "#6c757d",
            }}
          >
            Help to plan your every career transition
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}
