import mongoose, { Document, Model, UpdateWriteOpResult } from 'mongoose';
import { RATE_STRUCTURES, PLAN_ASSIGNMENT_STATUSES, WAITING_PERIOD_RULES, ENROLLMENT_TYPES, EMPLOYEE_CLASS_TYPES, QUALIFYING_LIFE_EVENT_TYPES } from '../../constants';
import CostCalculationService from '../../services/costCalculationService';

const { Schema } = mongoose;

// Plan Customizations Interface
export interface PlanCustomizations {
  customPlanName?: string;     // Company-specific plan name override
  customDescription?: string;  // Company-specific description override
  additionalDocuments?: string[]; // Company-specific additional documents (in employer-companyId namespace)
  displayOrder?: number;       // Order in company's plan list
}

// Contribution Interface
export interface ContributionPolicy {
  contributionType: string;    // "Fixed", "Percentage", or "Remainder"
  contributionAmount: number;  // Amount or percentage
}

// Coverage Tier Interface
export interface CoverageTier {
  tierName: string;           // "Employee Only", "Employee + Spouse", "Family", etc.
  totalCost: number;          // Total premium cost for this tier
  employeeCost: number;       // Employee portion (calculated based on contribution rules)
  employerCost: number;       // Employer portion (calculated based on contribution rules)
}

// Age Banded Rate Interface (moved from Plan model)
export interface AgeBandedRate {
  ageMin: number;
  ageMax: number;
  rate: number;
  type?: 'fixed' | 'multiplier'; // Optional: 'fixed' for dollar amounts, 'multiplier' for factors
}

// 🎯 NEW: Salary Based Rate Interface
export interface SalaryBasedRate {
  salaryMin: number;
  salaryMax: number;
  rate: number;
  type?: 'fixed' | 'multiplier'; // Optional: 'fixed' for dollar amounts, 'multiplier' for factors
}

// Waiting Period Interface
export interface WaitingPeriod {
  enabled: boolean;
  days: number;
  rule: string;
  description?: string;
}

// Main PlanAssignment Data Interface
export interface PlanAssignmentDataInterface {
  _id?: mongoose.Types.ObjectId;

  // Core Relationship (One Plan to One Company per Year/Period)
  planId: string;             // Reference to the Plan (single plan)
  companyId: string;          // Reference to the Company

  // Carrier Group Information (Company-Specific)
  groupNumber?: string;       // Group number assigned by carrier for this company

  // 🎯 MULTI-YEAR ASSIGNMENT SUPPORT
  assignmentYear: number;     // Year extracted from planEndDate (e.g., 2024, 2025)
  assignmentExpiry: Date;     // Copy of planEndDate for quick expiry checks

  // Note: Benefits are NOT created during plan assignment
  // Benefits are transferred from plan namespace to company namespace
  // after enrollment completion via dedicated benefits transfer flow

  // Company-Specific Contribution Policies
  employerContribution: ContributionPolicy;
  employeeContribution: ContributionPolicy;

  // Rate Structure for this Assignment
  rateStructure?: string;     // "Composite", "Age-Banded", "Four-Tier", "Salary-Based"

  // Age-Banded Rates (if rateStructure = "Age-Banded")
  ageBandedRates?: AgeBandedRate[];

  // 🎯 NEW: Salary-Based Rates (if rateStructure = "Salary-Based")
  salaryBasedRates?: SalaryBasedRate[];
  salaryPercentage?: number;  // Alternative to salaryBasedRates - percentage of annual salary

  // Company-Specific Coverage Tiers and Pricing
  coverageTiers: CoverageTier[];

  // Plan-Specific Customizations (ONLY what varies per plan assignment)
  planCustomizations?: PlanCustomizations;

  // 🎯 NEW: Waiting Period Configuration
  waitingPeriod: WaitingPeriod;

  // 🎯 NEW: Employee Class Type Eligibility Rules
  eligibleEmployeeClasses: string[];  // Which employee classes are eligible (e.g., ["Full-Time", "Part-Time"])

  // 🎯 NEW: Enrollment Type - Controls user experience during enrollment
  enrollmentType: string;     // "Active" or "Passive"

  // 🎯 TIME-RELATED CONSTRAINTS (PRIMARY RESPONSIBILITY)
  planEffectiveDate: Date;     // When the plan becomes available for enrollment
  planEndDate: Date;           // When the plan coverage ends
  enrollmentStartDate: Date;   // When employees can start enrolling
  enrollmentEndDate: Date;     // When enrollment period closes

  // 🎯 NEW: Configurable QLE Window (New Hire uses existing waitingPeriod)
  qualifyingLifeEventWindow?: {
    enabled: boolean;          // Whether QLE enrollment is allowed
    windowDays: number;        // Days from event date (default 30)
    allowedEvents: string[];   // Which QLE events are allowed
    description?: string;      // Custom description
  };

  // Assignment Metadata
  assignedDate: Date;          // When plan was assigned to company
  assignedBy: string;          // User ID who assigned the plan

  // 🎯 STATUS MANAGEMENT (Auto-managed based on dates)
  isActive: boolean;           // Whether assignment is currently active
  status: string;              // 'Active', 'Expired', 'Deactivated'

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

// Updatable fields interface
export interface UpdateablePlanAssignmentDataInterface {
  // Plan Reference (for reassignment operations)
  planId?: string;            // Allow plan reassignment
  // Carrier Group Information (Company-Specific)
  groupNumber?: string;       // Group number assigned by carrier for this company
  employerContribution?: ContributionPolicy;
  employeeContribution?: ContributionPolicy;
  rateStructure?: string;
  ageBandedRates?: AgeBandedRate[];
  // 🎯 NEW: Salary-based rate fields
  salaryBasedRates?: SalaryBasedRate[];
  salaryPercentage?: number;
  coverageTiers?: CoverageTier[];
  planCustomizations?: PlanCustomizations;
  // 🎯 NEW: Waiting period
  waitingPeriod?: WaitingPeriod;
  // 🎯 NEW: Employee Class Type Eligibility Rules
  eligibleEmployeeClasses?: string[];
  // 🎯 NEW: Enrollment type - Controls user experience during enrollment
  enrollmentType?: string;
  // 🎯 TIME-RELATED CONSTRAINTS (PRIMARY RESPONSIBILITY)
  planEffectiveDate?: Date;
  planEndDate?: Date;
  enrollmentStartDate?: Date;
  enrollmentEndDate?: Date;

  // 🎯 NEW: Configurable QLE Window (New Hire uses existing waitingPeriod)
  qualifyingLifeEventWindow?: {
    enabled: boolean;
    windowDays: number;
    allowedEvents: string[];
    description?: string;
  };
  // 🎯 STATUS MANAGEMENT
  isActive?: boolean;
  status?: string;
  // 🎯 MULTI-YEAR FIELDS (Auto-calculated when planEndDate changes)
  assignmentYear?: number;
  assignmentExpiry?: Date;
}

interface PlanAssignmentDocument extends Document, Omit<PlanAssignmentDataInterface, '_id'> {}

class PlanAssignmentModelClass {
  private static planAssignmentModel: Model<PlanAssignmentDocument>;

  public static initializeModel() {
    const schema = new Schema({
      planId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Plan',
        required: true
      },
      companyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true
      },

      // 🎯 Plan Reassignment Tracking
      previousPlanId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Plan'
      }, // Track previous plan for reassignment history
      reassignedAt: { type: Date }, // Timestamp of last reassignment

      // Carrier Group Information (Company-Specific)
      groupNumber: { type: String }, // Group number assigned by carrier for this company

      // 🎯 MULTI-YEAR ASSIGNMENT SUPPORT
      assignmentYear: {
        type: Number,
        required: true
      }, // Year extracted from planEndDate
      assignmentExpiry: {
        type: Date,
        required: true
      }, // Copy of planEndDate for quick expiry checks

      // Note: Benefits are NOT created during plan assignment
      // Benefits are transferred from plan namespace to company namespace
      // after enrollment completion via dedicated benefits transfer flow

      // Company-Specific Contribution Policies
      employerContribution: {
        contributionType: { type: String, enum: ['Fixed', 'Percentage'], required: true },
        contributionAmount: { type: Number, required: true, min: 0 }
      },

      employeeContribution: {
        contributionType: { type: String, enum: ['Fixed', 'Percentage', 'Remainder'], required: true },
        contributionAmount: { type: Number, required: true, min: 0 }
      },

      // Rate Structure for this Assignment
      rateStructure: {
        type: String,
        enum: RATE_STRUCTURES
      },

      // Age-Banded Rates (if rateStructure = "Age-Banded")
      ageBandedRates: [{
        ageMin: { type: Number },
        ageMax: { type: Number },
        rate: { type: Number, min: 0 },
        type: { type: String, enum: ['fixed', 'multiplier'], default: 'fixed' }
      }],

      // 🎯 NEW: Salary-Based Rates (if rateStructure = "Salary-Based")
      salaryBasedRates: [{
        salaryMin: { type: Number },
        salaryMax: { type: Number },
        rate: { type: Number, min: 0 },
        type: { type: String, enum: ['fixed', 'multiplier'], default: 'fixed' }
      }],

      // 🎯 NEW: Salary Percentage (alternative to salaryBasedRates)
      salaryPercentage: {
        type: Number,
        min: 0,
        max: 100  // Percentage should be between 0-100%
      },

      // Company-Specific Coverage Tiers and Pricing
      coverageTiers: [{
        tierName: { type: String, required: true },
        totalCost: { type: Number, required: true, min: 0 },
        employeeCost: { type: Number, required: true, min: 0 },
        employerCost: { type: Number, required: true, min: 0 }
      }],

      // Plan-Specific Customizations (ONLY what varies per plan assignment)
      planCustomizations: {
        customPlanName: { type: String },     // Company-specific plan name override
        customDescription: { type: String },  // Company-specific description override
        additionalDocuments: [{ type: String }], // Company-specific additional documents (in employer-companyId namespace)
        displayOrder: { type: Number }        // Order in company's plan list
      },

      // 🎯 NEW: Waiting Period Configuration
      waitingPeriod: {
        enabled: { type: Boolean, default: false },
        days: { type: Number, min: 0, default: 0 },
        rule: {
          type: String,
          enum: WAITING_PERIOD_RULES,
          default: 'Immediate'
        },
        description: { type: String }
      },

      // 🎯 NEW: Employee Class Type Eligibility Rules
      eligibleEmployeeClasses: [{
        type: String,
        enum: EMPLOYEE_CLASS_TYPES,
        required: true
      }],

      // 🎯 NEW: Enrollment Type - Controls user experience during enrollment
      enrollmentType: {
        type: String,
        enum: ENROLLMENT_TYPES,
        default: 'Active',
        required: true
      },

      // 🎯 TIME-RELATED CONSTRAINTS (PRIMARY RESPONSIBILITY)
      planEffectiveDate: { type: Date, required: true },     // When the plan becomes available for enrollment
      planEndDate: { type: Date, required: true },           // When the plan coverage ends
      enrollmentStartDate: { type: Date, required: true },   // When employees can start enrolling
      enrollmentEndDate: { type: Date, required: true },     // When enrollment period closes

      // 🎯 NEW: Configurable QLE Window (New Hire uses existing waitingPeriod)
      qualifyingLifeEventWindow: {
        enabled: { type: Boolean, default: true },
        windowDays: { type: Number, default: 30, min: 1, max: 365 },
        allowedEvents: [{
          type: String,
          enum: QUALIFYING_LIFE_EVENT_TYPES
        }],
        description: { type: String, default: 'Qualifying life event enrollment window' }
      },

      // Assignment Metadata
      assignedDate: {
        type: Date,
        required: true,
        default: Date.now
      },
      assignedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },

      // 🎯 STATUS MANAGEMENT (Auto-managed based on dates)
      isActive: { type: Boolean, default: true },
      status: {
        type: String,
        enum: PLAN_ASSIGNMENT_STATUSES,
        default: 'Active'
      }
    }, {
      timestamps: true // Automatically adds createdAt and updatedAt
    });

    // Add indexes for performance
    schema.index({ planId: 1 });
    schema.index({ companyId: 1 });
    // 🎯 MULTI-YEAR SUPPORT: Allow same plan+company for different years
    schema.index({ planId: 1, companyId: 1, assignmentYear: 1 }, { unique: true }); // One assignment per plan/company/year
    schema.index({ isActive: 1 });
    schema.index({ status: 1 });
    schema.index({ assignmentYear: 1 });
    schema.index({ assignmentExpiry: 1 }); // For expiry checks
    schema.index({ 'coverageTiers.tierName': 1 });
    schema.index({ assignedDate: 1 });
    schema.index({ assignedBy: 1 });

    // Time-related indexes for efficient querying
    schema.index({ planEffectiveDate: 1 });
    schema.index({ planEndDate: 1 });
    schema.index({ enrollmentStartDate: 1 });
    schema.index({ enrollmentEndDate: 1 });
    schema.index({ planEffectiveDate: 1, planEndDate: 1 }); // Range queries
    schema.index({ enrollmentStartDate: 1, enrollmentEndDate: 1 }); // Enrollment period queries

    this.planAssignmentModel = mongoose.model<PlanAssignmentDocument>('PlanAssignment', schema);
  }

  // ===== HELPER METHODS =====

  // Calculate assignment year from plan end date
  public static calculateAssignmentYear(planEndDate: Date | string): number {
    const date = planEndDate instanceof Date ? planEndDate : new Date(planEndDate);
    return date.getFullYear();
  }

  // Calculate assignment expiry (copy of plan end date)
  public static calculateAssignmentExpiry(planEndDate: Date | string): Date {
    const date = planEndDate instanceof Date ? planEndDate : new Date(planEndDate);
    return new Date(date);
  }

  // Prepare assignment data with calculated fields
  public static prepareAssignmentData(data: Omit<PlanAssignmentDataInterface, '_id' | 'assignmentYear' | 'assignmentExpiry'>): Omit<PlanAssignmentDataInterface, '_id'> {
    const assignmentYear = this.calculateAssignmentYear(data.planEndDate);
    const assignmentExpiry = this.calculateAssignmentExpiry(data.planEndDate);

    return {
      ...data,
      assignmentYear,
      assignmentExpiry
    };
  }

  // ===== CORE CRUD OPERATIONS =====

  // Create plan assignment
  public static async addData(data: Omit<PlanAssignmentDataInterface, '_id' | 'assignmentYear' | 'assignmentExpiry'>): Promise<PlanAssignmentDataInterface | null> {
    try {
      const preparedData = this.prepareAssignmentData(data);
      const assignment = await this.planAssignmentModel.create(preparedData);
      return assignment as unknown as PlanAssignmentDataInterface;
    } catch (error) {
      console.error('Error creating plan assignment:', error);
      return null;
    }
  }

  // Get all plan assignments (ADMIN ONLY) - only active with expiry check
  public static async getAllData(): Promise<PlanAssignmentDataInterface[]> {
    try {
      const data = await this.planAssignmentModel.find({ isActive: true }) as PlanAssignmentDataInterface[];

      // 🎯 CONSISTENT: Apply expiry check like service methods
      const expiryResult = await this.checkTargetedExpiry(data);
      return expiryResult.updatedData;
    } catch (error) {
      console.error('Error fetching all plan assignments:', error);
      return [];
    }
  }



  // Get plan assignment by ID (only active assignments)
  public static async getDataById(id: string): Promise<PlanAssignmentDataInterface | null> {
    try {
      const data = await this.planAssignmentModel.findOne({
        _id: id,
        isActive: true
      }) as PlanAssignmentDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching plan assignment by ID:', error);
      return null;
    }
  }

  // Get plan assignment by ID (regardless of active status) - for reactivation and cloning
  public static async getDataByIdIncludeInactive(id: string): Promise<PlanAssignmentDataInterface | null> {
    try {
      const data = await this.planAssignmentModel.findOne({
        _id: id
      }) as PlanAssignmentDataInterface;

      return data;
    } catch (error) {
      console.error('Error fetching plan assignment by ID (include inactive):', error);
      return null;
    }
  }

  // Get plan assignments by company ID
  public static async getDataByCompanyId(companyId: string): Promise<PlanAssignmentDataInterface[]> {
    try {
      const data = await this.planAssignmentModel.find({
        companyId,
        isActive: true
      }) as PlanAssignmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching plan assignments by company ID:', error);
      return [];
    }
  }

  // Get plan assignments by company ID - active only by default (consistent with service layer)
  public static async getAllAssignmentsByCompany(companyId: string, includeInactive: boolean = false): Promise<PlanAssignmentDataInterface[]> {
    try {
      const query: any = { companyId };

      // 🎯 CONSISTENT: Default to active only (same as service layer)
      if (!includeInactive) {
        query.isActive = true;
      }

      const data = await this.planAssignmentModel.find(query) as PlanAssignmentDataInterface[];

      // 🎯 CONSISTENT: Apply expiry check like service methods
      const expiryResult = await this.checkTargetedExpiry(data);
      return expiryResult.updatedData;
    } catch (error) {
      console.error('Error fetching plan assignments by company ID:', error);
      return [];
    }
  }



  // Get specific plan assignment by plan and company
  public static async getDataByPlanAndCompany(
    planId: string,
    companyId: string
  ): Promise<PlanAssignmentDataInterface | null> {
    try {
      const data = await this.planAssignmentModel.findOne({
        planId,
        companyId,
        isActive: true
      }) as PlanAssignmentDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching plan assignment by plan and company:', error);
      return null;
    }
  }

  // Update plan assignment
  public static async updateData({
    id,
    data,
  }: {
    id: string;
    data: Partial<UpdateablePlanAssignmentDataInterface>;
  }): Promise<UpdateWriteOpResult> {
    try {
      // If planEndDate is being updated, recalculate assignment year and expiry
      let updateData = { ...data };
      if (data.planEndDate) {
        updateData.assignmentYear = this.calculateAssignmentYear(data.planEndDate);
        updateData.assignmentExpiry = this.calculateAssignmentExpiry(data.planEndDate);
      }

      return await this.planAssignmentModel.updateOne({ _id: id }, updateData);
    } catch (error) {
      console.error('Error updating plan assignment:', error);
      throw error;
    }
  }

  // Soft delete plan assignment
  public static async deleteData(id: string): Promise<UpdateWriteOpResult> {
    try {
      return await this.planAssignmentModel.updateOne({ _id: id }, { isActive: false });
    } catch (error) {
      console.error('Error deleting plan assignment:', error);
      throw error;
    }
  }







  // Note: Benefit management is now handled by the dedicated benefits transfer flow
  // Benefits are transferred from plan namespace to company namespace after enrollment completion

  // ===== CUSTOMIZATION MANAGEMENT =====

  // Update plan customizations
  public static async updatePlanCustomizations({
    assignmentId,
    customizations,
  }: {
    assignmentId: string;
    customizations: PlanCustomizations;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.planAssignmentModel.updateOne(
        { _id: assignmentId },
        { planCustomizations: customizations }
      );
    } catch (error) {
      console.error('Error updating plan customizations:', error);
      throw error;
    }
  }

  // Get plan customizations
  public static async getPlanCustomizations(assignmentId: string): Promise<PlanCustomizations | null> {
    try {
      const assignment = await this.planAssignmentModel.findById(assignmentId);
      return assignment?.planCustomizations || null;
    } catch (error) {
      console.error('Error getting plan customizations:', error);
      return null;
    }
  }

  // ===== PLAN DATA POPULATION METHODS =====



  /**
   * 🎯 REUSABLE: Populate plan data for plan assignments efficiently
   * Uses MongoDB aggregation to avoid N+1 queries
   */
  public static async populatePlanData(assignments: PlanAssignmentDataInterface[]): Promise<Array<PlanAssignmentDataInterface & { planData?: any }>> {
    try {
      if (!assignments || assignments.length === 0) {
        return [];
      }

      // Extract unique plan IDs
      const planIds = [...new Set(assignments.map(a => a.planId.toString()))];

      // Fetch all plans in a single query
      const planModel = mongoose.model('Plan');
      const plans = await planModel.find({
        _id: { $in: planIds.map(id => new mongoose.Types.ObjectId(id)) }
      });

      // Create a plan lookup map for O(1) access
      const planMap = new Map();
      plans.forEach(plan => {
        planMap.set(plan._id.toString(), plan);
      });

      // Populate assignments with plan data
      const populatedAssignments = assignments.map(assignment => {
        const planData = planMap.get(assignment.planId.toString());
        return {
          ...assignment,
          planData: planData ? {
            _id: planData._id,
            planName: planData.planName,
            coverageType: planData.coverageType,
            coverageSubTypes: planData.coverageSubTypes,
            planType: planData.planType,
            metalTier: planData.metalTier,
            carrierId: planData.carrierId,
            carrierName: planData.carrierName, // If available
            status: planData.status,
            isTemplate: planData.isTemplate,
            brokerId: planData.brokerId,
            description: planData.description,
            // Include key plan details without overwhelming the response
            planDetails: {
              deductible: planData.deductible,
              outOfPocketMax: planData.outOfPocketMax,
              coinsurance: planData.coinsurance,
              copays: planData.copays
            }
          } : null
        };
      });

      return populatedAssignments;
    } catch (error) {
      console.error('Error populating plan data:', error);
      // Return original assignments if population fails
      return assignments.map(a => ({ ...a, planData: null }));
    }
  }



  /**
   * 🎯 REUSABLE: Get plan assignments with plan AND carrier data using aggregation (most efficient)
   * Single database query with MongoDB $lookup for both plan and carrier data
   */
  public static async getAssignmentsWithPlanData(query: any = {}): Promise<Array<PlanAssignmentDataInterface & { planData?: any; carrierData?: any }>> {
    try {
      const pipeline = [
        // Match the query criteria
        { $match: query },

        // Lookup plan data
        {
          $lookup: {
            from: 'plans',
            localField: 'planId',
            foreignField: '_id',
            as: 'planData'
          }
        },

        // Unwind plan data (convert array to object)
        {
          $unwind: {
            path: '$planData',
            preserveNullAndEmptyArrays: true
          }
        },

        // Lookup carrier data
        {
          $lookup: {
            from: 'carriers',
            localField: 'planData.carrierId',
            foreignField: '_id',
            as: 'carrierData'
          }
        },

        // Unwind carrier data (convert array to object)
        {
          $unwind: {
            path: '$carrierData',
            preserveNullAndEmptyArrays: true
          }
        },

        // Project only necessary plan fields to optimize response size
        {
          $addFields: {
            'planData.planDetails': {
              deductible: '$planData.deductible',
              outOfPocketMax: '$planData.outOfPocketMax',
              coinsurance: '$planData.coinsurance',
              copays: '$planData.copays'
            }
          }
        },

        // Remove sensitive or unnecessary plan fields
        {
          $project: {
            'planData.createdAt': 0,
            'planData.updatedAt': 0,
            'planData.deductible': 0,
            'planData.outOfPocketMax': 0,
            'planData.coinsurance': 0,
            'planData.copays': 0
          }
        }
      ];

      const assignments = await this.planAssignmentModel.aggregate(pipeline);
      return assignments as Array<PlanAssignmentDataInterface & { planData?: any; carrierData?: any }>;
    } catch (error) {
      console.error('Error getting assignments with plan and carrier data:', error);
      return [];
    }
  }

  /**
   * 🎯 ENHANCED: Get plan assignment by ID with plan and carrier data
   */
  public static async getDataByIdWithPlanData(id: string): Promise<(PlanAssignmentDataInterface & { planData?: any; carrierData?: any }) | null> {
    try {
      const assignments = await this.getAssignmentsWithPlanData({
        _id: new mongoose.Types.ObjectId(id),
        isActive: true
      });
      return assignments.length > 0 ? assignments[0] : null;
    } catch (error) {
      console.error('Error fetching plan assignment by ID with plan data:', error);
      return null;
    }
  }

  /**
   * 🎯 ENHANCED: Get plan assignments by company with plan AND carrier data
   */
  public static async getDataByCompanyIdWithPlanAndCarrierData(companyId: string): Promise<Array<PlanAssignmentDataInterface & { planData?: any; carrierData?: any }>> {
    try {
      return await this.getAssignmentsWithPlanData({
        companyId: new mongoose.Types.ObjectId(companyId),
        isActive: true
      });
    } catch (error) {
      console.error('Error fetching plan assignments by company ID with plan and carrier data:', error);
      return [];
    }
  }

  /**
   * 🎯 ENHANCED: Get plan assignments by company with plan and carrier data
   */
  public static async getDataByCompanyIdWithPlanData(companyId: string): Promise<Array<PlanAssignmentDataInterface & { planData?: any; carrierData?: any }>> {
    try {
      return await this.getAssignmentsWithPlanData({
        companyId: new mongoose.Types.ObjectId(companyId),
        isActive: true
      });
    } catch (error) {
      console.error('Error fetching plan assignments by company ID with plan data:', error);
      return [];
    }
  }



  // ===== BUSINESS LOGIC METHODS =====



  // Get assignments by broker (through plan ownership)
  public static async getAssignmentsByBroker(brokerId: string): Promise<PlanAssignmentDataInterface[]> {
    try {
      // This would require joining with Plan model to filter by brokerId
      // Using aggregation pipeline to join with Plan collection
      const assignments = await this.planAssignmentModel.aggregate([
        {
          $lookup: {
            from: 'plans',
            localField: 'planId',
            foreignField: '_id',
            as: 'plan'
          }
        },
        {
          $match: {
            'plan.brokerId': brokerId,
            isActive: true
          }
        },
        {
          $project: {
            plan: 0 // Remove the joined plan data from result
          }
        }
      ]);
      return assignments as PlanAssignmentDataInterface[];
    } catch (error) {
      console.error('Error getting assignments by broker:', error);
      return [];
    }
  }

  // Get broker's assignments for a specific company (for access control)
  public static async getBrokerAssignmentsForCompany(brokerId: string, companyId: string): Promise<PlanAssignmentDataInterface[]> {
    try {
      const assignments = await this.planAssignmentModel.aggregate([
        {
          $lookup: {
            from: 'plans',
            localField: 'planId',
            foreignField: '_id',
            as: 'plan'
          }
        },
        {
          $match: {
            companyId: new mongoose.Types.ObjectId(companyId),
            $or: [
              { assignedBy: new mongoose.Types.ObjectId(brokerId) }, // Assignments created by this broker
              { 'plan.brokerId': new mongoose.Types.ObjectId(brokerId) } // Plans owned by this broker
            ]
          }
        },
        {
          $project: {
            plan: 0 // Remove the joined plan data from result
          }
        }
      ]);
      return assignments as PlanAssignmentDataInterface[];
    } catch (error) {
      console.error('Error getting broker assignments for company:', error);
      return [];
    }
  }

  // Note: Plan status validation for assignment should be done at the controller level
  // to avoid circular dependencies. The PlanAssignment controller should validate
  // that only Active, non-template plans can be assigned before creating assignments.

  // Validate assignment dates (using new time constraints)
  public static validateAssignmentDates(
    planEffectiveDate: Date,
    planEndDate: Date,
    enrollmentStartDate: Date,
    enrollmentEndDate: Date
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Enrollment period must be before or during plan effective period
    if (enrollmentStartDate > enrollmentEndDate) {
      errors.push('Enrollment start date must be before enrollment end date');
    }

    if (planEffectiveDate > planEndDate) {
      errors.push('Plan effective date must be before plan end date');
    }

    if (enrollmentEndDate > planEffectiveDate) {
      errors.push('Enrollment must end before or when plan becomes effective');
    }

    // Note: Removed arbitrary "one year in future" constraint
    // Business logic should determine appropriate date ranges, not validation

    return { isValid: errors.length === 0, errors };
  }

  // ===== PLAN ASSIGNMENT STATUS MANAGEMENT METHODS =====

  /**
   * Plan Assignment Status Transition Map - Defines valid status transitions
   */
  private static readonly ASSIGNMENT_STATUS_TRANSITION_MAP: Record<string, string[]> = {
    'Active': ['Deactivated'], // Active can only be deactivated (Expired is automatic)
    'Deactivated': ['Active'], // Deactivated can be reactivated (if not expired)
    'Expired': [] // No transitions allowed from Expired status
  };

  /**
   * Validate status transition for plan assignments
   */
  public static validateAssignmentStatusTransition(fromStatus: string, toStatus: string): boolean {
    const validTransitions = this.ASSIGNMENT_STATUS_TRANSITION_MAP[fromStatus] || [];
    return validTransitions.includes(toStatus);
  }

  /**
   * Get valid status transitions for current status
   */
  public static getValidAssignmentStatusTransitions(currentStatus: string): string[] {
    return this.ASSIGNMENT_STATUS_TRANSITION_MAP[currentStatus] || [];
  }

  // Activate assignment
  public static async activateAssignment(assignmentId: string): Promise<{ success: boolean; message: string }> {
    try {
      const assignment = await this.getDataByIdIncludeInactive(assignmentId);
      if (!assignment) {
        return { success: false, message: 'Assignment not found' };
      }

      // Validate status transition
      const currentStatus = this.getAssignmentStatus(assignment);
      if (!this.validateAssignmentStatusTransition(currentStatus, 'Active')) {
        const validTransitions = this.getValidAssignmentStatusTransitions(currentStatus);
        return {
          success: false,
          message: `Cannot activate assignment with status "${currentStatus}". Valid transitions: ${validTransitions.join(', ') || 'None'}`
        };
      }

      const result = await this.planAssignmentModel.updateOne(
        { _id: assignmentId },
        {
          isActive: true,
          status: 'Active'
        }
      );

      if (result.modifiedCount === 0) {
        return { success: false, message: 'Failed to activate assignment' };
      }

      return { success: true, message: 'Assignment activated successfully' };
    } catch (error) {
      console.error('Error activating assignment:', error);
      return { success: false, message: 'Internal error activating assignment' };
    }
  }

  // Deactivate assignment (set status to 'Deactivated' and isActive to false)
  public static async deactivateAssignment(assignmentId: string): Promise<{ success: boolean; message: string }> {
    try {
      const assignment = await this.getDataByIdIncludeInactive(assignmentId);
      if (!assignment) {
        return { success: false, message: 'Assignment not found' };
      }

      // Validate status transition
      const currentStatus = this.getAssignmentStatus(assignment);
      if (!this.validateAssignmentStatusTransition(currentStatus, 'Deactivated')) {
        const validTransitions = this.getValidAssignmentStatusTransitions(currentStatus);
        return {
          success: false,
          message: `Cannot deactivate assignment with status "${currentStatus}". Valid transitions: ${validTransitions.join(', ') || 'None'}`
        };
      }

      const result = await this.planAssignmentModel.updateOne(
        { _id: assignmentId },
        {
          isActive: false,
          status: 'Deactivated'
        }
      );

      if (result.modifiedCount === 0) {
        return { success: false, message: 'Failed to deactivate assignment' };
      }

      return { success: true, message: 'Assignment deactivated successfully' };
    } catch (error) {
      console.error('Error deactivating assignment:', error);
      return { success: false, message: 'Internal error deactivating assignment' };
    }
  }

  // ===== DEPENDENCY CHAIN VALIDATION =====

  // Check if assignment can be edited (≤1 enrollment references it)
  public static async canEditAssignment(assignmentId: string): Promise<{ canEdit: boolean; referenceCount: number; referencedBy: string[] }> {
    try {
      // Use mongoose directly to avoid circular dependency
      const enrollmentModel = mongoose.model('EmployeeEnrollment');

      // ✅ FIXED: Check ALL enrollments referencing this assignment (not just active)
      // Business rule: Plan assignments are editable if referenced by ≤1 enrollment
      const allEnrollments = await enrollmentModel.find({
        planAssignmentId: assignmentId
        // No status filter - check ALL enrollment references regardless of status
      });

      // Business rule: Can edit if referenced by 0 or 1 enrollment
      const canEdit = allEnrollments.length <= 1;
      const referenceCount = allEnrollments.length;
      const referencedBy = allEnrollments.map(enrollment =>
        `Enrollment ${enrollment._id} (${enrollment.status || 'Unknown'})`
      );

      return {
        canEdit,
        referenceCount,
        referencedBy
      };
    } catch (error) {
      console.error('Error checking if assignment can be edited:', error);
      return { canEdit: false, referenceCount: -1, referencedBy: ['Error checking references'] };
    }
  }

  // Check if assignment can be deleted (no active enrollments reference it)
  public static async canDeleteAssignment(assignmentId: string): Promise<{ canDelete: boolean; referenceCount: number; referencedBy: string[] }> {
    try {
      // Use mongoose directly to avoid circular dependency
      const enrollmentModel = mongoose.model('EmployeeEnrollment');

      // Check for any enrollments (active or inactive) referencing this assignment
      const allEnrollments = await enrollmentModel.find({
        planAssignmentId: assignmentId
      });

      const canDelete = allEnrollments.length === 0;
      const referenceCount = allEnrollments.length;
      const referencedBy = allEnrollments.map(enrollment => `Enrollment ${enrollment._id} (${enrollment.status})`);

      return {
        canDelete,
        referenceCount,
        referencedBy
      };
    } catch (error) {
      console.error('Error checking if assignment can be deleted:', error);
      return { canDelete: false, referenceCount: -1, referencedBy: ['Error checking references'] };
    }
  }

  // Get enrollments referencing this assignment
  public static async getEnrollmentsReferencingAssignment(assignmentId: string): Promise<any[]> {
    try {
      // Use mongoose directly to avoid circular dependency
      const enrollmentModel = mongoose.model('EmployeeEnrollment');

      const enrollments = await enrollmentModel.find({
        planAssignmentId: assignmentId
      }).select('_id employeeId status createdAt');

      return enrollments;
    } catch (error) {
      console.error('Error getting enrollments referencing assignment:', error);
      return [];
    }
  }

  // ===== COST CALCULATION METHODS =====

  // Calculate enrollment cost for this plan assignment
  // 🎯 CORRECTED: Removed dependentCount - cost is based on selected tier only
  public static calculateEnrollmentCost({
    planAssignment,
    employeeAge,
    selectedTier
  }: {
    planAssignment: PlanAssignmentDataInterface;
    employeeAge?: number;
    selectedTier: string;
  }) {
    return CostCalculationService.calculateEnrollmentCost({
      planAssignment,
      employeeAge,
      selectedTier
      // dependentCount removed - cost calculation is based on selected tier only
    });
  }

  // Validate cost calculation inputs
  public static validateCostCalculationInputs(input: any) {
    return CostCalculationService.validateCostCalculationInputs(input);
  }

  // ===== EXPIRY MANAGEMENT =====

  // ===== EXPIRY MANAGEMENT METHODS =====

  // BULK expiry check - updates assignments from last 3 years only (SuperAdmin only)
  public static async checkExpiredAssignments(): Promise<{ expiredCount: number; updatedAssignments: string[] }> {
    try {
      const now = new Date();

      // 🎯 OPTIMIZATION: Only check assignments from last 3 years to avoid processing historical data
      const threeYearsAgo = new Date();
      threeYearsAgo.setFullYear(now.getFullYear() - 3);

      // Find assignments that have passed their expiry date (last 3 years only)
      const expiredAssignments = await this.planAssignmentModel.find({
        assignmentExpiry: { $lt: now, $gte: threeYearsAgo }, // Only last 3 years
        status: 'Active',
        isActive: true
      });

      const updatedAssignments: string[] = [];

      // Update status to 'Expired' and set isActive to false
      for (const assignment of expiredAssignments) {
        await this.planAssignmentModel.updateOne(
          { _id: assignment._id },
          {
            status: 'Expired',
            isActive: false
          }
        );
        updatedAssignments.push(assignment._id.toString());
      }

      return {
        expiredCount: expiredAssignments.length,
        updatedAssignments
      };
    } catch (error) {
      console.error('Error checking expired assignments:', error);
      return { expiredCount: 0, updatedAssignments: [] };
    }
  }

  // TARGETED expiry check - only updates specific assignments (optimized for API calls)
  public static async checkTargetedExpiry(assignments: PlanAssignmentDataInterface[]): Promise<{
    expiredCount: number;
    updatedAssignments: string[];
    updatedData: PlanAssignmentDataInterface[];
  }> {
    try {
      const now = new Date();
      const updatedAssignments: string[] = [];
      const updatedData: PlanAssignmentDataInterface[] = [];

      for (const assignment of assignments) {
        // Check if assignment has expired
        const hasExpired = assignment.assignmentExpiry &&
                          new Date(assignment.assignmentExpiry) < now &&
                          assignment.status === 'Active' &&
                          assignment.isActive === true;

        if (hasExpired) {
          // Update in database
          await this.planAssignmentModel.updateOne(
            { _id: assignment._id },
            {
              status: 'Expired',
              isActive: false
            }
          );

          // Update in-memory object for immediate return
          const updatedAssignment = {
            ...assignment,
            status: 'Expired' as const,
            isActive: false
          };

          updatedAssignments.push(assignment._id.toString());
          updatedData.push(updatedAssignment);
        } else {
          // Keep original assignment
          updatedData.push(assignment);
        }
      }

      return {
        expiredCount: updatedAssignments.length,
        updatedAssignments,
        updatedData
      };
    } catch (error) {
      console.error('Error checking targeted expiry:', error);
      return {
        expiredCount: 0,
        updatedAssignments: [],
        updatedData: assignments // Return original data on error
      };
    }
  }

  // FILTERED expiry check - only updates assignments matching specific criteria
  public static async checkFilteredExpiry(filter: any, yearsBack: number = 3): Promise<{ expiredCount: number; updatedAssignments: string[] }> {
    try {
      const now = new Date();

      // 🎯 OPTIMIZATION: Limit to specific years back to avoid processing historical data
      const yearsAgo = new Date();
      yearsAgo.setFullYear(now.getFullYear() - yearsBack);

      // Add expiry conditions to the provided filter
      const expiryFilter = {
        ...filter,
        assignmentExpiry: { $lt: now, $gte: yearsAgo }, // Limit by years back
        status: 'Active',
        isActive: true
      };

      // Find assignments that match filter AND have expired
      const expiredAssignments = await this.planAssignmentModel.find(expiryFilter);
      const updatedAssignments: string[] = [];

      // Update only the filtered expired assignments
      for (const assignment of expiredAssignments) {
        await this.planAssignmentModel.updateOne(
          { _id: assignment._id },
          {
            status: 'Expired',
            isActive: false
          }
        );
        updatedAssignments.push(assignment._id.toString());
      }

      return {
        expiredCount: expiredAssignments.length,
        updatedAssignments
      };
    } catch (error) {
      console.error('Error checking filtered expiry:', error);
      return { expiredCount: 0, updatedAssignments: [] };
    }
  }

  // 🎯 NEW: Configurable expiry check with year limits for performance optimization
  public static async checkExpiredAssignmentsWithYearLimit(yearsBack: number = 3): Promise<{
    expiredCount: number;
    updatedAssignments: string[];
    yearRange: { from: number; to: number };
    skippedHistoricalCount?: number;
  }> {
    try {
      const now = new Date();
      const currentYear = now.getFullYear();
      const fromYear = currentYear - yearsBack;

      // Calculate date range
      const yearsAgo = new Date();
      yearsAgo.setFullYear(fromYear);

      // Count historical assignments that would be skipped (for reporting)
      const historicalCount = await this.planAssignmentModel.countDocuments({
        assignmentExpiry: { $lt: yearsAgo },
        status: 'Active',
        isActive: true
      });

      // Find assignments that have passed their expiry date (within year limit)
      const expiredAssignments = await this.planAssignmentModel.find({
        assignmentExpiry: { $lt: now, $gte: yearsAgo },
        status: 'Active',
        isActive: true
      });

      const updatedAssignments: string[] = [];

      // Update status to 'Expired' and set isActive to false
      for (const assignment of expiredAssignments) {
        await this.planAssignmentModel.updateOne(
          { _id: assignment._id },
          {
            status: 'Expired',
            isActive: false
          }
        );
        updatedAssignments.push(assignment._id.toString());
      }

      return {
        expiredCount: expiredAssignments.length,
        updatedAssignments,
        yearRange: { from: fromYear, to: currentYear },
        skippedHistoricalCount: historicalCount > 0 ? historicalCount : undefined
      };
    } catch (error) {
      console.error('Error checking expired assignments with year limit:', error);
      return {
        expiredCount: 0,
        updatedAssignments: [],
        yearRange: { from: 0, to: 0 }
      };
    }
  }

  // 🎯 NEW: Optimized query with filtering and pagination at database level
  public static async getPlanAssignmentsOptimized(
    query: any,
    pagination?: { page: number; limit: number },
    populatePlanData: boolean = false
  ): Promise<{
    assignments: PlanAssignmentDataInterface[];
    totalCount: number;
    totalPages?: number;
  }> {
    try {
      if (populatePlanData) {
        // Use existing method for plan data population
        return this.getAssignmentsWithPlanDataOptimized(query, pagination);
      } else {
        // Simple query without plan data
        if (pagination) {
          const skip = (pagination.page - 1) * pagination.limit;

          const [totalCount, assignments] = await Promise.all([
            this.planAssignmentModel.countDocuments(query),
            this.planAssignmentModel.find(query)
              .skip(skip)
              .limit(pagination.limit)
              .sort({ createdAt: -1 })
          ]);

          return {
            assignments: assignments as PlanAssignmentDataInterface[],
            totalCount,
            totalPages: Math.ceil(totalCount / pagination.limit)
          };
        } else {
          const assignments = await this.planAssignmentModel.find(query).sort({ createdAt: -1 });
          return {
            assignments: assignments as PlanAssignmentDataInterface[],
            totalCount: assignments.length
          };
        }
      }
    } catch (error) {
      console.error('Error getting optimized plan assignments:', error);
      return { assignments: [], totalCount: 0 };
    }
  }

  // 🎯 NEW: Optimized method with plan data population
  private static async getAssignmentsWithPlanDataOptimized(
    query: any,
    pagination?: { page: number; limit: number }
  ): Promise<{
    assignments: PlanAssignmentDataInterface[];
    totalCount: number;
    totalPages?: number;
  }> {
    try {
      const pipeline: any[] = [
        { $match: query },
        {
          $lookup: {
            from: 'plans',
            localField: 'planId',
            foreignField: '_id',
            as: 'planData'
          }
        },
        {
          $lookup: {
            from: 'carriers',
            localField: 'planData.carrierId',
            foreignField: '_id',
            as: 'carrierData'
          }
        }
      ];

      // Add pagination to pipeline if requested
      if (pagination) {
        const skip = (pagination.page - 1) * pagination.limit;
        pipeline.push(
          { $skip: skip },
          { $limit: pagination.limit }
        );
      }

      // Get total count and data
      const [countResult, assignments] = await Promise.all([
        this.planAssignmentModel.aggregate([
          { $match: query },
          { $count: "total" }
        ]),
        this.planAssignmentModel.aggregate(pipeline)
      ]);

      const totalCount = countResult[0]?.total || 0;

      return {
        assignments: assignments as PlanAssignmentDataInterface[],
        totalCount,
        totalPages: pagination ? Math.ceil(totalCount / pagination.limit) : undefined
      };
    } catch (error) {
      console.error('Error getting optimized assignments with plan data:', error);
      return { assignments: [], totalCount: 0 };
    }
  }

  // 🎯 NEW: Broker-specific aggregation with filtering
  public static async getAssignmentsWithBrokerFilter(
    brokerId: string,
    baseQuery: any,
    pagination?: { page: number; limit: number },
    populatePlanData: boolean = false
  ): Promise<{
    assignments: PlanAssignmentDataInterface[];
    totalCount: number;
    totalPages?: number;
  }> {
    try {
      const pipeline: any[] = [
        {
          $lookup: {
            from: 'plans',
            localField: 'planId',
            foreignField: '_id',
            as: 'plan'
          }
        },
        {
          $match: {
            ...baseQuery,
            'plan.brokerId': brokerId
          }
        }
      ];

      // Add carrier data if plan data is requested
      if (populatePlanData) {
        pipeline.push({
          $lookup: {
            from: 'carriers',
            localField: 'plan.carrierId',
            foreignField: '_id',
            as: 'carrierData'
          }
        });
      } else {
        // Remove plan data if not needed
        pipeline.push({
          $project: {
            plan: 0
          }
        });
      }

      // Get total count
      const countPipeline: any[] = [
        {
          $lookup: {
            from: 'plans',
            localField: 'planId',
            foreignField: '_id',
            as: 'plan'
          }
        },
        {
          $match: {
            ...baseQuery,
            'plan.brokerId': brokerId
          }
        },
        { $count: "total" }
      ];

      // Add pagination if requested
      if (pagination) {
        const skip = (pagination.page - 1) * pagination.limit;
        pipeline.push(
          { $skip: skip },
          { $limit: pagination.limit }
        );
      }

      const [countResult, assignments] = await Promise.all([
        this.planAssignmentModel.aggregate(countPipeline),
        this.planAssignmentModel.aggregate(pipeline)
      ]);

      const totalCount = countResult[0]?.total || 0;

      return {
        assignments: assignments as PlanAssignmentDataInterface[],
        totalCount,
        totalPages: pagination ? Math.ceil(totalCount / pagination.limit) : undefined
      };
    } catch (error) {
      console.error('Error getting broker assignments with filter:', error);
      return { assignments: [], totalCount: 0 };
    }
  }

  // ===== TIME-AWARE QUERY METHODS =====

  // Get assignments that are currently in enrollment period
  public static async getAssignmentsInEnrollmentPeriod(companyId?: string, referenceDate?: Date, assignmentYear?: number): Promise<PlanAssignmentDataInterface[]> {
    try {
      const now = referenceDate || new Date();
      const query: any = {
        isActive: true,
        enrollmentStartDate: { $lte: now },
        enrollmentEndDate: { $gte: now }
      };

      if (companyId) {
        query.companyId = companyId;
      }

      // 🎯 NEW: Assignment year filtering
      if (assignmentYear) {
        query.assignmentYear = assignmentYear;
      }

      const data = await this.planAssignmentModel.find(query) as PlanAssignmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching assignments in enrollment period:', error);
      return [];
    }
  }

  // Get assignments that are currently effective (plan is active)
  public static async getEffectiveAssignments(companyId?: string, referenceDate?: Date, assignmentYear?: number): Promise<PlanAssignmentDataInterface[]> {
    try {
      const now = referenceDate || new Date();
      const query: any = {
        isActive: true,
        planEffectiveDate: { $lte: now },
        planEndDate: { $gte: now }
      };

      if (companyId) {
        query.companyId = companyId;
      }

      // 🎯 NEW: Assignment year filtering
      if (assignmentYear) {
        query.assignmentYear = assignmentYear;
      }

      const data = await this.planAssignmentModel.find(query) as PlanAssignmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching effective assignments:', error);
      return [];
    }
  }



  // Check if assignment is currently in enrollment period
  public static isInEnrollmentPeriod(assignment: PlanAssignmentDataInterface, referenceDate?: Date): boolean {
    const now = referenceDate || new Date();
    return assignment.isActive &&
           assignment.enrollmentStartDate <= now &&
           assignment.enrollmentEndDate >= now;
  }

  // Check if assignment is currently effective
  public static isEffective(assignment: PlanAssignmentDataInterface, referenceDate?: Date): boolean {
    const now = referenceDate || new Date();
    return assignment.isActive &&
           assignment.planEffectiveDate <= now &&
           assignment.planEndDate >= now;
  }

  /**
   * Get assignment status based on documented business rules
   * Returns: 'Active', 'Deactivated', or 'Expired' (matches documentation)
   */
  public static getAssignmentStatus(assignment: PlanAssignmentDataInterface, referenceDate?: Date): string {
    const now = referenceDate || new Date();

    // Check if expired first (highest priority)
    if (assignment.planEndDate < now) {
      return 'Expired';
    }

    // Check manual deactivation
    if (!assignment.isActive) {
      return 'Deactivated';
    }

    // Default to Active if not expired and isActive is true
    return 'Active';
  }

  /**
   * Get detailed assignment status for internal operations
   * Returns detailed status including enrollment periods and future dates
   */
  public static getDetailedAssignmentStatus(assignment: PlanAssignmentDataInterface, referenceDate?: Date): string {
    const now = referenceDate || new Date();

    // Check if expired first
    if (assignment.planEndDate < now) {
      return 'Expired';
    }

    // Check manual deactivation
    if (!assignment.isActive) {
      return 'Deactivated';
    }

    // Check if future effective
    if (assignment.planEffectiveDate > now) {
      return 'Future';
    }

    // Check enrollment period
    if (assignment.planEffectiveDate <= now && assignment.planEndDate >= now) {
      if (assignment.enrollmentStartDate <= now && assignment.enrollmentEndDate >= now) {
        return 'Enrollment Open';
      }
      return 'Active';
    }

    return 'Active'; // Default fallback
  }

  // ===== PRICING CALCULATION METHODS =====

  // Validate rate structure configuration
  public static validateRateStructure(planAssignment: PlanAssignmentDataInterface): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const { rateStructure, ageBandedRates, salaryBasedRates, coverageTiers } = planAssignment;

    if (!rateStructure) {
      errors.push('Rate structure is required');
      return { isValid: false, errors };
    }

    // Validate age-banded rates
    if (rateStructure === 'Age-Banded' || rateStructure === 'Age-Banded-Four-Tier') {
      if (!ageBandedRates || ageBandedRates.length === 0) {
        errors.push(`Rate structure "${rateStructure}" requires age-banded rates`);
      } else {
        // Validate individual age bands
        ageBandedRates.forEach((band: any, index: number) => {
          if (typeof band.ageMin !== 'number' || typeof band.ageMax !== 'number') {
            errors.push(`Age band ${index + 1}: ageMin and ageMax must be numbers`);
          }
          if (band.ageMin >= band.ageMax) {
            errors.push(`Age band ${index + 1}: ageMin must be less than ageMax`);
          }
          if (typeof band.rate !== 'number' || band.rate < 0) {
            errors.push(`Age band ${index + 1}: rate must be a non-negative number`);
          }

          // Validate type field
          if (band.type && !['fixed', 'multiplier'].includes(band.type)) {
            errors.push(`Age band ${index + 1}: type must be either 'fixed' or 'multiplier'`);
          }

          // Validate rate value based on type
          if (band.type === 'multiplier' && band.rate > 10) {
            errors.push(`Age band ${index + 1}: multiplier rate should typically be between 0 and 10 (got ${band.rate})`);
          }
        });

        // Validate age bands don't overlap and cover reasonable range
        const sortedBands = [...ageBandedRates].sort((a, b) => a.ageMin - b.ageMin);
        for (let i = 0; i < sortedBands.length - 1; i++) {
          if (sortedBands[i].ageMax >= sortedBands[i + 1].ageMin) {
            errors.push(`Age bands overlap: ${sortedBands[i].ageMin}-${sortedBands[i].ageMax} and ${sortedBands[i + 1].ageMin}-${sortedBands[i + 1].ageMax}`);
          }
        }
      }
    } else {
      // Non-age-banded structures should have empty age-banded rates
      if (ageBandedRates && ageBandedRates.length > 0) {
        errors.push(`Rate structure "${rateStructure}" should not have age-banded rates`);
      }
    }

    // Validate salary-based rates
    if (rateStructure === 'Salary-Based') {
      if (!salaryBasedRates || salaryBasedRates.length === 0) {
        errors.push(`Rate structure "${rateStructure}" requires salary-based rates`);
      } else {
        // Validate individual salary bands
        salaryBasedRates.forEach((band: any, index: number) => {
          if (typeof band.salaryMin !== 'number' || typeof band.salaryMax !== 'number') {
            errors.push(`Salary band ${index + 1}: salaryMin and salaryMax must be numbers`);
          }
          if (band.salaryMin >= band.salaryMax) {
            errors.push(`Salary band ${index + 1}: salaryMin must be less than salaryMax`);
          }
          if (typeof band.rate !== 'number' || band.rate < 0) {
            errors.push(`Salary band ${index + 1}: rate must be a non-negative number`);
          }

          // Validate type field
          if (band.type && !['fixed', 'multiplier'].includes(band.type)) {
            errors.push(`Salary band ${index + 1}: type must be either 'fixed' or 'multiplier'`);
          }

          // Validate rate value based on type
          if (band.type === 'multiplier' && band.rate > 10) {
            errors.push(`Salary band ${index + 1}: multiplier rate should typically be between 0 and 10 (got ${band.rate})`);
          }
        });

        // Validate salary bands don't overlap
        const sortedBands = [...salaryBasedRates].sort((a, b) => a.salaryMin - b.salaryMin);
        for (let i = 0; i < sortedBands.length - 1; i++) {
          if (sortedBands[i].salaryMax >= sortedBands[i + 1].salaryMin) {
            errors.push(`Salary bands overlap: ${sortedBands[i].salaryMin}-${sortedBands[i].salaryMax} and ${sortedBands[i + 1].salaryMin}-${sortedBands[i + 1].salaryMax}`);
          }
        }
      }
    } else {
      // Non-salary-based structures should have empty salary-based rates
      if (salaryBasedRates && salaryBasedRates.length > 0) {
        errors.push(`Rate structure "${rateStructure}" should not have salary-based rates`);
      }
    }

    // Validate coverage tiers
    if (!coverageTiers || coverageTiers.length === 0) {
      errors.push('Coverage tiers are required');
    } else {
      // Validate four-tier structure has exactly 4 tiers
      if (rateStructure === 'Four-Tier' || rateStructure === 'Age-Banded-Four-Tier') {
        const requiredTiers = ['Employee Only', 'Employee + Spouse', 'Employee + Child(ren)', 'Family'];
        const tierNames = coverageTiers.map(t => t.tierName);

        for (const requiredTier of requiredTiers) {
          if (!tierNames.includes(requiredTier)) {
            errors.push(`Four-tier structure missing required tier: ${requiredTier}`);
          }
        }

        if (coverageTiers.length !== 4) {
          errors.push(`Four-tier structure must have exactly 4 tiers, found ${coverageTiers.length}`);
        }
      }

      // Validate tier costs
      for (const tier of coverageTiers) {
        if (tier.totalCost < 0) {
          errors.push(`Tier "${tier.tierName}" has negative total cost`);
        }
        if (tier.employeeCost < 0) {
          errors.push(`Tier "${tier.tierName}" has negative employee cost`);
        }
        if (tier.employerCost < 0) {
          errors.push(`Tier "${tier.tierName}" has negative employer cost`);
        }
        if (Math.abs(tier.totalCost - (tier.employeeCost + tier.employerCost)) > 0.01) {
          errors.push(`Tier "${tier.tierName}" cost breakdown doesn't match total`);
        }
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  // 🎯 NEW: Validate tier-specific costs when provided
  public static validateTierCosts(planAssignment: PlanAssignmentDataInterface): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const { coverageTiers, employerContribution, employeeContribution } = planAssignment;

    if (!coverageTiers || coverageTiers.length === 0) {
      return { isValid: true, errors, warnings }; // No tiers to validate
    }

    for (const tier of coverageTiers) {
      const { tierName, totalCost, employeeCost, employerCost } = tier;

      // Check if tier has specific costs defined
      const hasEmployeeCost = typeof employeeCost === 'number' && employeeCost >= 0;
      const hasEmployerCost = typeof employerCost === 'number' && employerCost >= 0;
      const hasTotalCost = typeof totalCost === 'number' && totalCost >= 0;

      if (hasEmployeeCost || hasEmployerCost) {
        // If any specific costs are provided, validate consistency
        if (!hasTotalCost) {
          errors.push(`Tier "${tierName}": totalCost is required when employeeCost or employerCost is specified`);
          continue;
        }

        if (hasEmployeeCost && hasEmployerCost) {
          // Both costs provided - validate they add up to total
          const calculatedTotal = employeeCost + employerCost;
          if (Math.abs(calculatedTotal - totalCost) > 0.01) {
            errors.push(`Tier "${tierName}": employeeCost (${employeeCost}) + employerCost (${employerCost}) = ${calculatedTotal} doesn't match totalCost (${totalCost})`);
          }
        } else if (hasEmployeeCost && !hasEmployerCost) {
          // Only employee cost provided - calculate employer cost
          const calculatedEmployerCost = totalCost - employeeCost;
          if (calculatedEmployerCost < 0) {
            errors.push(`Tier "${tierName}": employeeCost (${employeeCost}) cannot be greater than totalCost (${totalCost})`);
          } else {
            warnings.push(`Tier "${tierName}": employerCost will be calculated as ${calculatedEmployerCost.toFixed(2)} (totalCost - employeeCost)`);
          }
        } else if (!hasEmployeeCost && hasEmployerCost) {
          // Only employer cost provided - calculate employee cost
          const calculatedEmployeeCost = totalCost - employerCost;
          if (calculatedEmployeeCost < 0) {
            errors.push(`Tier "${tierName}": employerCost (${employerCost}) cannot be greater than totalCost (${totalCost})`);
          } else {
            warnings.push(`Tier "${tierName}": employeeCost will be calculated as ${calculatedEmployeeCost.toFixed(2)} (totalCost - employerCost)`);
          }
        }

        // Warn if contribution policies exist but tier-specific costs will override them
        if ((employerContribution || employeeContribution) && (hasEmployeeCost || hasEmployerCost)) {
          warnings.push(`Tier "${tierName}": tier-specific costs will override plan assignment contribution policies`);
        }
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }




}

// Initialize the model
PlanAssignmentModelClass.initializeModel();

export default PlanAssignmentModelClass;
