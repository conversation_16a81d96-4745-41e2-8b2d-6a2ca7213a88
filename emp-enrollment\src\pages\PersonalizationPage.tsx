import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Brain, Sparkles, Bot } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { VideoPlayer } from '@/components/VideoPlayer';
import { PlanQADialog } from '@/components/PlanQADialog';
import { UserProfile } from '@/components/BenefitsEnrollmentBot';
import { DidYouKnowFacts } from '@/components/DidYouKnowFacts';
import { CompactROIBadge } from '@/components/CompactROIBadge';

interface PersonalizationPageProps {
  onComplete: (profile: Partial<UserProfile>) => void;
}

export const PersonalizationPage = ({ onComplete }: PersonalizationPageProps) => {
  const [showForm, setShowForm] = useState(false);
  const [familyMembers, setFamilyMembers] = useState('');
  const [wearGlasses, setWearGlasses] = useState(false);
  const [needsDentalCare, setNeedsDentalCare] = useState(false);
  const [hasPreferredDoctors, setHasPreferredDoctors] = useState(false);

  // Streaming text state
  const [questionText, setQuestionText] = useState('');
  const [contextText, setContextText] = useState('');
  const [questionComplete, setQuestionComplete] = useState(false);
  const [contextComplete, setContextComplete] = useState(false);

  const questionString = "🤔 Let me learn about you to give the best recommendations!";
  const contextString = "I'll ask a few quick questions about your healthcare needs, budget preferences, and family situation.";

  // Streaming effect for question
  React.useEffect(() => {
    if (questionText.length < questionString.length) {
      const timer = setTimeout(() => {
        setQuestionText(prev => prev + questionString[questionText.length]);
      }, 50);
      return () => clearTimeout(timer);
    } else if (!questionComplete) {
      setQuestionComplete(true);
    }
  }, [questionText, questionComplete]);

  // Streaming effect for context
  React.useEffect(() => {
    if (questionComplete && contextText.length < contextString.length) {
      const timer = setTimeout(() => {
        setContextText(prev => prev + contextString[contextText.length]);
      }, 30);
      return () => clearTimeout(timer);
    } else if (questionComplete && contextText.length === contextString.length && !contextComplete) {
      setContextComplete(true);
    }
  }, [questionComplete, contextText, contextComplete]);

  // Show form when streaming is complete
  React.useEffect(() => {
    if (questionComplete && contextComplete) {
      setShowForm(true);
    }
  }, [questionComplete, contextComplete]);

  const handleSubmit = () => {
    onComplete({
      familyMembers,
      wearGlasses,
      needsDentalCare,
      hasPreferredDoctors,
    });
  };

  const isComplete = familyMembers !== '';

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      {/* Inline Bot Question with Streaming */}
      <div className="flex gap-3 mb-6">
        <Avatar className="w-10 h-10">
          <AvatarFallback className="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900">
            <Bot className="w-5 h-5 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent" />
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 rounded-2xl px-4 py-3 border border-blue-200 dark:border-blue-800">
            <p className="text-gray-900 dark:text-gray-100 font-medium">
              {questionText}
            </p>
            {questionComplete && (
              <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                {contextText}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Did You Know Facts */}
      {showForm && <DidYouKnowFacts />}
      
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent" />
              AI-Powered Personalization
              <Sparkles className="w-5 h-5 text-yellow-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-lg">Let me learn about you to provide the most accurate recommendations:</p>
            
            {/* Inline Personalization Form */}
            <div className="space-y-4">
              <div className="space-y-3">
                <Label className="text-base font-medium">Any family members to cover?</Label>
                <RadioGroup value={familyMembers} onValueChange={setFamilyMembers}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="self" id="self" />
                    <Label htmlFor="self" className="flex items-center gap-2">
                      Just me
                      {familyMembers === 'self' && (
                        <CompactROIBadge planCost={75} planType="medical" familySize="self" />
                      )}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="spouse" id="spouse" />
                    <Label htmlFor="spouse" className="flex items-center gap-2">
                      Me + Spouse
                      {familyMembers === 'spouse' && (
                        <CompactROIBadge planCost={150} planType="medical" familySize="spouse" />
                      )}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="family" id="family" />
                    <Label htmlFor="family" className="flex items-center gap-2">
                      Me + Family
                      {familyMembers === 'family' && (
                        <CompactROIBadge planCost={225} planType="medical" familySize="family" />
                      )}
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="glasses" 
                    checked={wearGlasses}
                    onCheckedChange={(checked) => setWearGlasses(checked as boolean)}
                  />
                  <Label htmlFor="glasses" className="flex items-center gap-2">
                    Do you wear glasses or contacts?
                    {wearGlasses && (
                      <CompactROIBadge planCost={5.25} planType="vision" familySize={familyMembers} />
                    )}
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="dental" 
                    checked={needsDentalCare}
                    onCheckedChange={(checked) => setNeedsDentalCare(checked as boolean)}
                  />
                  <Label htmlFor="dental" className="flex items-center gap-2">
                    Need regular dental care or cleanings?
                    {needsDentalCare && (
                      <CompactROIBadge planCost={12} planType="dental" familySize={familyMembers} />
                    )}
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="doctors" 
                    checked={hasPreferredDoctors}
                    onCheckedChange={(checked) => setHasPreferredDoctors(checked as boolean)}
                  />
                  <Label htmlFor="doctors">Have preferred doctors or dentists?</Label>
                </div>
              </div>

              {/* Immediate Feedback */}
              {familyMembers && (
                <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 rounded-lg border border-blue-200 dark:border-blue-800">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    💡 <strong className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Smart Insight:</strong> {familyMembers === 'family' 
                      ? 'Family coverage typically saves $2,000+ annually vs individual plans!'
                      : familyMembers === 'spouse' 
                      ? 'Couples coverage offers great value compared to separate plans!'
                      : 'Individual coverage gives you maximum flexibility and lower costs!'}
                  </p>
                </div>
              )}

              <Button 
                onClick={handleSubmit}
                disabled={!isComplete}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              >
                Continue
              </Button>
            </div>
            
            <div className="flex gap-2 pt-4">
              <VideoPlayer 
                title="How to Choose Plans" 
                description="Guide to selecting the right benefits"
                planType="medical"
              />
              <PlanQADialog selectedPlans={{}} />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
