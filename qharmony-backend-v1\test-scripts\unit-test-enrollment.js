/**
 * UNIT TEST SUITE FOR EMPLOYEE ENROLLMENT
 * 
 * This script tests all enrollment functionality using dummy data without requiring
 * a running server or database connection. It directly tests the model methods
 * and business logic with comprehensive mock data.
 * 
 * Test Coverage:
 * - All status transitions
 * - All enrollment types (Open, New Hire, QLE)
 * - All validation scenarios
 * - All cost calculation types
 * - All access control scenarios
 * - Edge cases and error conditions
 */

// Mock data structures
const MOCK_DATA = {
  // User data
  users: {
    superAdmin: {
      _id: 'super_admin_001',
      email: '<EMAIL>',
      firstName: 'Super',
      lastName: 'Admin',
      isSuperAdmin: true,
      isAdmin: true,
      isBroker: true,
      companyId: 'brokerage_001',
      details: {
        phoneNumber: '555-0001',
        dateOfBirth: '1980-01-01',
        hireDate: '2020-01-01',
        employeeClassType: 'Full-Time'
      }
    },
    broker: {
      _id: 'broker_001',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'Broker',
      isAdmin: true,
      isBroker: true,
      companyId: 'brokerage_001',
      brokerId: 'super_admin_001',
      brokerageId: 'brokerage_001',
      details: {
        phoneNumber: '555-0002',
        dateOfBirth: '1985-01-01',
        hireDate: '2021-01-01',
        employeeClassType: 'Full-Time'
      }
    },
    employer: {
      _id: 'employer_001',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'Employer',
      isAdmin: true,
      isBroker: false,
      companyId: 'company_001',
      brokerId: 'broker_001',
      brokerageId: 'brokerage_001',
      details: {
        phoneNumber: '555-0003',
        dateOfBirth: '1982-01-01',
        hireDate: '2022-01-01',
        employeeClassType: 'Full-Time'
      }
    },
    employees: [
      {
        _id: 'employee_001',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Employee',
        isAdmin: false,
        isBroker: false,
        companyId: 'company_001',
        brokerId: 'broker_001',
        brokerageId: 'brokerage_001',
        details: {
          phoneNumber: '555-0004',
          dateOfBirth: '1990-05-15',
          hireDate: '2024-01-15', // Recent hire for New Hire testing
          employeeClassType: 'Full-Time',
          annualSalary: 75000,
          dependents: [
            {
              name: 'John Spouse',
              relationship: 'Spouse',
              dateOfBirth: '1989-01-01',
              gender: 'Other'
            },
            {
              name: 'John Child',
              relationship: 'Child',
              dateOfBirth: '2015-01-01',
              gender: 'Other'
            }
          ]
        }
      },
      {
        _id: 'employee_002',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Worker',
        isAdmin: false,
        isBroker: false,
        companyId: 'company_001',
        brokerId: 'broker_001',
        brokerageId: 'brokerage_001',
        details: {
          phoneNumber: '555-0005',
          dateOfBirth: '1988-08-20',
          hireDate: '2023-06-01', // Established employee
          employeeClassType: 'Full-Time',
          annualSalary: 85000,
          dependents: [
            {
              name: 'Jane Spouse',
              relationship: 'Spouse',
              dateOfBirth: '1987-01-01',
              gender: 'Other'
            }
          ]
        }
      },
      {
        _id: 'employee_003',
        email: '<EMAIL>',
        firstName: 'Bob',
        lastName: 'Staff',
        isAdmin: false,
        isBroker: false,
        companyId: 'company_001',
        brokerId: 'broker_001',
        brokerageId: 'brokerage_001',
        details: {
          phoneNumber: '555-0006',
          dateOfBirth: '1992-12-10',
          hireDate: '2023-03-15',
          employeeClassType: 'Part-Time',
          annualSalary: 45000,
          dependents: []
        }
      }
    ]
  },

  // Plan assignment data (this is all we need for enrollment operations)
  planAssignments: [
    {
      _id: 'plan_assignment_001',
      planId: 'plan_001', // Reference only
      companyId: 'company_001',
      assignmentYear: 2024,
      enrollmentStartDate: '2024-01-01',
      enrollmentEndDate: '2024-01-31',
      planStartDate: '2024-03-01',
      planEndDate: '2025-02-28',
      status: 'Active',
      assignedBy: 'broker_001',
      eligibleEmployeeClasses: ['Full-Time', 'Part-Time'],
      waitingPeriod: { enabled: true, days: 30, rule: 'Calendar Days' },
      qualifyingLifeEventWindow: {
        enabled: true,
        windowDays: 30,
        allowedEvents: ['Marriage', 'Divorce', 'Birth', 'Adoption', 'Loss of Coverage', 'Job Change', 'Death', 'Relocation', 'Other'],
        description: 'Standard QLE enrollment window'
      },
      contributionPolicy: {
        type: 'Fixed',
        employerContribution: 400
      },
      // Mock plan data for cost calculation
      planData: {
        name: 'Composite Rate Health Plan',
        rateStructure: 'composite',
        coverageType: 'Health Insurance',
        coverageSubTypes: ['Medical', 'Prescription'],
        rates: {
          composite: {
            employeeOnly: { total: 500, employeeContribution: 100 },
            employeeSpouse: { total: 1000, employeeContribution: 200 },
            employeeChildren: { total: 800, employeeContribution: 150 },
            family: { total: 1200, employeeContribution: 250 }
          }
        }
      }
    },
    {
      _id: 'plan_assignment_002',
      planId: 'plan_002',
      companyId: 'company_001',
      assignmentYear: 2024,
      enrollmentStartDate: '2024-01-01',
      enrollmentEndDate: '2024-01-31',
      planStartDate: '2024-03-01',
      planEndDate: '2025-02-28',
      status: 'Active',
      assignedBy: 'broker_001',
      eligibleEmployeeClasses: ['Full-Time', 'Part-Time'],
      waitingPeriod: { enabled: true, days: 30, rule: 'Calendar Days' },
      qualifyingLifeEventWindow: {
        enabled: true,
        windowDays: 45, // Different window for dental
        allowedEvents: ['Marriage', 'Divorce', 'Birth', 'Adoption', 'Loss of Coverage'],
        description: 'Family-focused QLE window for dental'
      },
      contributionPolicy: {
        type: 'Percentage',
        employerContributionPercentage: 80
      },
      planData: {
        name: 'Four-Tier Dental Plan',
        rateStructure: 'four-tier',
        coverageType: 'Ancillary Benefits',
        coverageSubTypes: ['Dental'],
        rates: {
          fourTier: {
            employeeOnly: { total: 50, employeeContribution: 10 },
            employeeSpouse: { total: 100, employeeContribution: 20 },
            employeeChildren: { total: 80, employeeContribution: 15 },
            family: { total: 120, employeeContribution: 25 }
          }
        }
      }
    },
    {
      _id: 'plan_assignment_003',
      planId: 'plan_003',
      companyId: 'company_001',
      assignmentYear: 2024,
      enrollmentStartDate: '2024-01-01',
      enrollmentEndDate: '2024-01-31',
      planStartDate: '2024-03-01',
      planEndDate: '2025-02-28',
      status: 'Active',
      assignedBy: 'broker_001',
      eligibleEmployeeClasses: ['Full-Time'],
      waitingPeriod: { enabled: true, days: 60, rule: 'Calendar Days' },
      qualifyingLifeEventWindow: {
        enabled: false, // Life insurance doesn't allow QLE
        windowDays: 0,
        allowedEvents: [],
        description: 'No QLE enrollment for life insurance'
      },
      contributionPolicy: {
        type: 'Fixed',
        employerContribution: 35
      },
      planData: {
        name: 'Age-Banded Life Insurance',
        rateStructure: 'age-banded',
        coverageType: 'Life & Disability Insurance',
        coverageSubTypes: ['Life Insurance'],
        rates: {
          ageBanded: {
            ageRanges: [
              { minAge: 18, maxAge: 29, rate: 10 },
              { minAge: 30, maxAge: 39, rate: 15 },
              { minAge: 40, maxAge: 49, rate: 25 },
              { minAge: 50, maxAge: 65, rate: 40 }
            ]
          }
        }
      }
    }
  ],

  // Sample enrollments for testing status transitions
  enrollments: [
    {
      _id: 'enrollment_001',
      planAssignmentId: 'plan_assignment_001',
      employeeId: 'employee_001',
      companyId: 'company_001',
      coverageType: 'Health Insurance',
      coverageSubTypes: ['Medical', 'Prescription'],
      employeeClassType: 'Full-Time',
      coverageTier: 'Employee + Spouse',
      contribution: {
        totalAmount: 1000,
        employeeAmount: 200,
        employerAmount: 800
      },
      enrolledDependents: [
        {
          name: 'John Spouse',
          relationship: 'Spouse',
          dateOfBirth: '1989-01-01'
        }
      ],
      status: 'Pending',
      enrolledUnder: 'Open Enrollment',
      enrollmentDate: '2024-01-15',
      effectiveDate: '2024-03-01'
    },
    {
      _id: 'enrollment_002',
      planAssignmentId: 'plan_assignment_002',
      employeeId: 'employee_002',
      companyId: 'company_001',
      coverageType: 'Ancillary Benefits',
      coverageSubTypes: ['Dental'],
      employeeClassType: 'Full-Time',
      coverageTier: 'Employee Only',
      contribution: {
        totalAmount: 50,
        employeeAmount: 10,
        employerAmount: 40
      },
      enrolledDependents: [],
      status: 'Enrolled',
      enrolledUnder: 'Open Enrollment',
      enrollmentDate: '2024-01-10',
      effectiveDate: '2024-03-01'
    },
    {
      _id: 'enrollment_003',
      planAssignmentId: 'plan_assignment_001',
      employeeId: 'employee_003',
      companyId: 'company_001',
      coverageType: 'Health Insurance',
      coverageSubTypes: ['Medical', 'Prescription'],
      employeeClassType: 'Part-Time',
      coverageTier: 'Employee Only',
      contribution: {
        totalAmount: 500,
        employeeAmount: 100,
        employerAmount: 400
      },
      enrolledDependents: [],
      status: 'Waived',
      enrolledUnder: 'Open Enrollment',
      enrollmentDate: '2024-01-12',
      effectiveDate: '2024-03-01',
      waiveReason: 'Covered by spouse plan',
      waiveDate: '2024-01-20'
    }
  ]
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    debug: '🔍',
    warning: '⚠️'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message) {
  if (condition) {
    testResults.passed++;
    log(`PASS: ${message}`, 'success');
  } else {
    testResults.failed++;
    testResults.errors.push(message);
    log(`FAIL: ${message}`, 'error');
  }
}

// Mock implementations of model methods for testing
const MockEmployeeEnrollmentModel = {
  // Status transition validation
  getValidStatusTransitions(currentStatus) {
    const transitions = {
      'Pending': ['Enrolled', 'Waived', 'Terminated'],
      'Enrolled': ['Waived', 'Terminated'],
      'Waived': ['Pending', 'Enrolled'],
      'Terminated': ['Pending', 'Enrolled']
    };
    return transitions[currentStatus] || [];
  },

  // Validate status transition
  validateStatusTransition(currentStatus, newStatus) {
    const validTransitions = this.getValidStatusTransitions(currentStatus);
    return {
      isValid: validTransitions.includes(newStatus),
      validTransitions,
      reason: validTransitions.includes(newStatus) ?
        'Valid transition' :
        `Cannot transition from ${currentStatus} to ${newStatus}`
    };
  },

  // Employee profile validation
  validateEmployeeProfileForEnrollment(employee, planAssignment) {
    const missingFields = [];
    const errors = [];
    const warnings = [];

    if (!employee.details?.dateOfBirth) missingFields.push('dateOfBirth');
    if (!employee.details?.hireDate) missingFields.push('hireDate');
    if (!employee.details?.employeeClassType) missingFields.push('employeeClassType');
    if (!employee.details?.phoneNumber) missingFields.push('phoneNumber');

    if (employee.details?.annualSalary && employee.details.annualSalary < 0) {
      errors.push('Annual salary cannot be negative');
    }

    return {
      isValid: missingFields.length === 0 && errors.length === 0,
      missingFields,
      errors,
      warnings
    };
  },

  // Hire date eligibility
  isEmployeeEligibleByHireDate(employee, planAssignment) {
    if (!employee.details?.hireDate) {
      return {
        isEligible: false,
        reason: 'Hire date not found',
        eligibilityDate: null,
        daysUntilEligible: null
      };
    }

    const hireDate = new Date(employee.details.hireDate);
    // 🎯 NEW: Handle new waiting period structure
    const waitingPeriodDays = planAssignment.waitingPeriod?.days || planAssignment.waitingPeriod || 0;
    const eligibilityDate = new Date(hireDate);
    eligibilityDate.setDate(eligibilityDate.getDate() + waitingPeriodDays);

    const today = new Date();
    const isEligible = today >= eligibilityDate;
    const daysUntilEligible = isEligible ? 0 : Math.ceil((eligibilityDate - today) / (1000 * 60 * 60 * 24));

    return {
      isEligible,
      reason: isEligible ? 'Employee is eligible' : `Waiting period not met (${waitingPeriodDays} days)`,
      eligibilityDate,
      daysUntilEligible
    };
  },

  // Employee class eligibility
  isEmployeeClassEligible(employee, planAssignment) {
    const employeeClass = employee.details?.employeeClassType;
    const eligibleClasses = planAssignment.eligibleEmployeeClasses || [];

    return {
      isEligible: eligibleClasses.includes(employeeClass),
      reason: eligibleClasses.includes(employeeClass) ?
        'Employee class is eligible' :
        `Employee class ${employeeClass} not eligible for this plan`,
      employeeClass,
      eligibleClasses
    };
  },

  // Coverage tier validation
  validateCoverageTierSelection(coverageTier, employeeId, dependentIds) {
    const employee = MOCK_DATA.users.employees.find(emp => emp._id === employeeId);
    const availableDependents = employee?.details?.dependents || [];

    const tierRequirements = {
      'Employee Only': { dependents: 0 },
      'Employee + Spouse': { dependents: 1, types: ['Spouse'] },
      'Employee + Children': { dependents: 1, types: ['Child'] },
      'Family': { dependents: 1 }
    };

    const requirement = tierRequirements[coverageTier];
    if (!requirement) {
      return {
        isValid: false,
        warnings: [`Invalid coverage tier: ${coverageTier}`],
        suggestions: []
      };
    }

    const warnings = [];
    const suggestions = [];

    if (coverageTier === 'Employee Only' && dependentIds.length > 0) {
      warnings.push('Employee Only tier selected but dependents provided');
      suggestions.push('Remove dependents or select appropriate family tier');
    }

    if (coverageTier !== 'Employee Only' && dependentIds.length === 0) {
      warnings.push(`${coverageTier} tier selected but no dependents provided`);
      suggestions.push('Add dependents or select Employee Only tier');
    }

    return {
      isValid: warnings.length === 0,
      warnings,
      suggestions
    };
  },

  // 🎯 NEW: QLE validation
  validateQualifyingLifeEvent(qleData, planAssignment) {
    if (!qleData || !qleData.eventType || !qleData.eventDate) {
      return {
        isValid: false,
        reason: 'QLE event type and date are required',
        allowedEvents: planAssignment.qualifyingLifeEventWindow?.allowedEvents || []
      };
    }

    const qleConfig = planAssignment.qualifyingLifeEventWindow;
    if (!qleConfig?.enabled) {
      return {
        isValid: false,
        reason: 'Qualifying Life Events not allowed for this plan',
        allowedEvents: []
      };
    }

    const allowedEvents = qleConfig.allowedEvents || [];
    if (!allowedEvents.includes(qleData.eventType)) {
      return {
        isValid: false,
        reason: `Event type '${qleData.eventType}' not allowed for this plan`,
        allowedEvents
      };
    }

    const eventDate = new Date(qleData.eventDate);
    const today = new Date();
    const daysSinceEvent = Math.floor((today - eventDate) / (1000 * 60 * 60 * 24));
    const windowDays = qleConfig.windowDays || 30;

    if (daysSinceEvent > windowDays) {
      return {
        isValid: false,
        reason: `QLE window expired. Event occurred ${daysSinceEvent} days ago (max ${windowDays} days)`,
        allowedEvents,
        windowDays
      };
    }

    return {
      isValid: true,
      reason: `Valid QLE within ${windowDays}-day window`,
      allowedEvents,
      windowDays,
      daysSinceEvent
    };
  }
};

// Mock cost calculation service
const MockCostCalculationService = {
  calculateEnrollmentCost({ planAssignment, employeeAge, selectedTier }) {
    const planData = planAssignment.planData;
    if (!planData || !planData.rates) {
      return { success: false, message: 'Plan rate data not found' };
    }

    let cost = { totalAmount: 0, employeeAmount: 0, employerAmount: 0 };

    // Create tier mapping for consistent key lookup
    const tierMapping = {
      'Employee Only': 'employeeOnly',
      'Employee + Spouse': 'employeeSpouse',
      'Employee + Children': 'employeeChildren',
      'Family': 'family'
    };

    if (planData.rateStructure === 'composite' && planData.rates.composite) {
      const tierKey = tierMapping[selectedTier];
      const tierRates = planData.rates.composite[tierKey];
      if (tierRates) {
        cost.totalAmount = tierRates.total;
        cost.employeeAmount = tierRates.employeeContribution;
        cost.employerAmount = cost.totalAmount - cost.employeeAmount;
      }
    } else if (planData.rateStructure === 'four-tier' && planData.rates.fourTier) {
      const tierKey = tierMapping[selectedTier];
      const tierRates = planData.rates.fourTier[tierKey];
      if (tierRates) {
        cost.totalAmount = tierRates.total;
        cost.employeeAmount = tierRates.employeeContribution;
        cost.employerAmount = cost.totalAmount - cost.employeeAmount;
      }
    } else if (planData.rateStructure === 'age-banded' && planData.rates.ageBanded && employeeAge) {
      const ageRange = planData.rates.ageBanded.ageRanges.find(range =>
        employeeAge >= range.minAge && employeeAge <= range.maxAge
      );
      if (ageRange) {
        cost.totalAmount = ageRange.rate;
        cost.employeeAmount = Math.round(ageRange.rate * 0.2); // 20% employee contribution
        cost.employerAmount = cost.totalAmount - cost.employeeAmount;
      }
    }

    return {
      success: cost.totalAmount > 0,
      cost,
      message: cost.totalAmount > 0 ? 'Cost calculated successfully' : 'Unable to calculate cost'
    };
  }
};

// Test functions
function testStatusTransitions() {
  log('Testing status transitions...', 'info');

  // Test valid transitions
  const validTests = [
    { from: 'Pending', to: 'Enrolled', expected: true },
    { from: 'Pending', to: 'Waived', expected: true },
    { from: 'Enrolled', to: 'Terminated', expected: true },
    { from: 'Waived', to: 'Enrolled', expected: true },
    { from: 'Terminated', to: 'Pending', expected: true }
  ];

  validTests.forEach(test => {
    const result = MockEmployeeEnrollmentModel.validateStatusTransition(test.from, test.to);
    assert(result.isValid === test.expected,
      `Status transition ${test.from} → ${test.to} should be ${test.expected ? 'valid' : 'invalid'}`);
  });

  // Test invalid transitions
  const invalidTests = [
    { from: 'Enrolled', to: 'Pending', expected: false },
    { from: 'Waived', to: 'Terminated', expected: false },
    { from: 'Terminated', to: 'Waived', expected: false }
  ];

  invalidTests.forEach(test => {
    const result = MockEmployeeEnrollmentModel.validateStatusTransition(test.from, test.to);
    assert(result.isValid === test.expected,
      `Status transition ${test.from} → ${test.to} should be ${test.expected ? 'valid' : 'invalid'}`);
  });

  log('Status transition tests completed', 'success');
}

function testEmployeeValidation() {
  log('Testing employee validation...', 'info');

  const employee = MOCK_DATA.users.employees[0]; // John Employee
  const planAssignment = MOCK_DATA.planAssignments[0];

  // Test profile validation
  const profileResult = MockEmployeeEnrollmentModel.validateEmployeeProfileForEnrollment(employee, planAssignment);
  assert(profileResult.isValid, 'Employee profile validation should pass for complete profile');

  // Test hire date eligibility
  const hireDateResult = MockEmployeeEnrollmentModel.isEmployeeEligibleByHireDate(employee, planAssignment);
  assert(hireDateResult.isEligible, 'Employee should be eligible based on hire date');

  // Test class eligibility
  const classResult = MockEmployeeEnrollmentModel.isEmployeeClassEligible(employee, planAssignment);
  assert(classResult.isEligible, 'Full-Time employee should be eligible for plan');

  // Test part-time employee with full-time only plan
  const partTimeEmployee = MOCK_DATA.users.employees[2]; // Bob Staff (Part-Time)
  const fullTimeOnlyPlan = MOCK_DATA.planAssignments[2]; // Life insurance (Full-Time only)
  const partTimeClassResult = MockEmployeeEnrollmentModel.isEmployeeClassEligible(partTimeEmployee, fullTimeOnlyPlan);
  assert(!partTimeClassResult.isEligible, 'Part-Time employee should not be eligible for Full-Time only plan');

  log('Employee validation tests completed', 'success');
}

function testCoverageValidation() {
  log('Testing coverage tier validation...', 'info');

  const employee = MOCK_DATA.users.employees[0]; // John Employee (has spouse and child)

  // Test valid tier selections
  const validTierResult = MockEmployeeEnrollmentModel.validateCoverageTierSelection('Employee + Spouse', employee._id, ['spouse_001']);
  assert(validTierResult.isValid || validTierResult.warnings.length === 0, 'Employee + Spouse tier should be valid with spouse dependent');

  const employeeOnlyResult = MockEmployeeEnrollmentModel.validateCoverageTierSelection('Employee Only', employee._id, []);
  assert(employeeOnlyResult.isValid, 'Employee Only tier should be valid with no dependents');

  // Test invalid tier selections
  const invalidTierResult = MockEmployeeEnrollmentModel.validateCoverageTierSelection('Employee Only', employee._id, ['spouse_001']);
  assert(!invalidTierResult.isValid, 'Employee Only tier should be invalid with dependents');

  log('Coverage validation tests completed', 'success');
}

function testCostCalculations() {
  log('Testing cost calculations...', 'info');

  // Test composite rate calculation
  const compositeResult = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: MOCK_DATA.planAssignments[0],
    employeeAge: 30,
    selectedTier: 'Employee + Spouse'
  });

  // Debug logging
  if (!compositeResult.success) {
    log(`Composite calculation failed: ${compositeResult.message}`, 'debug');
    log(`Available rates: ${JSON.stringify(MOCK_DATA.planAssignments[0].planData.rates.composite)}`, 'debug');
  } else {
    log(`Composite calculation succeeded: $${compositeResult.cost.totalAmount}`, 'debug');
  }

  assert(compositeResult.success, 'Composite rate calculation should succeed');
  assert(compositeResult.cost.totalAmount === 1000, `Composite rate total should be $1000, got $${compositeResult.cost.totalAmount}`);

  // Test four-tier rate calculation
  const fourTierResult = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: MOCK_DATA.planAssignments[1],
    employeeAge: 30,
    selectedTier: 'Employee Only'
  });

  // Debug logging
  if (!fourTierResult.success) {
    log(`Four-tier calculation failed: ${fourTierResult.message}`, 'debug');
    log(`Available rates: ${JSON.stringify(MOCK_DATA.planAssignments[1].planData.rates.fourTier)}`, 'debug');
  } else {
    log(`Four-tier calculation succeeded: $${fourTierResult.cost.totalAmount}`, 'debug');
  }

  assert(fourTierResult.success, 'Four-tier rate calculation should succeed');
  assert(fourTierResult.cost.totalAmount === 50, `Four-tier rate total should be $50, got $${fourTierResult.cost.totalAmount}`);

  // Test age-banded rate calculation
  const ageBandedResult = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: MOCK_DATA.planAssignments[2],
    employeeAge: 35,
    selectedTier: 'Employee Only'
  });
  assert(ageBandedResult.success, 'Age-banded rate calculation should succeed');
  assert(ageBandedResult.cost.totalAmount === 15, 'Age-banded rate for 35-year-old should be $15');

  log('Cost calculation tests completed', 'success');
}

function testEnrollmentOperations() {
  log('Testing enrollment operations...', 'info');

  // Test enrollment creation
  const newEnrollment = {
    _id: 'enrollment_new_001',
    planAssignmentId: 'plan_assignment_001',
    employeeId: 'employee_001',
    companyId: 'company_001',
    coverageType: 'Health Insurance',
    coverageSubTypes: ['Medical', 'Prescription'],
    employeeClassType: 'Full-Time',
    coverageTier: 'Employee Only',
    contribution: {
      totalAmount: 500,
      employeeAmount: 100,
      employerAmount: 400
    },
    enrolledDependents: [],
    status: 'Pending',
    enrolledUnder: 'Open Enrollment',
    enrollmentDate: new Date().toISOString(),
    effectiveDate: '2024-03-01'
  };

  assert(newEnrollment.status === 'Pending', 'New enrollment should start in Pending status');
  assert(newEnrollment.enrolledUnder === 'Open Enrollment', 'Default enrollment type should be Open Enrollment');

  // Test enrollment activation
  const activationResult = MockEmployeeEnrollmentModel.validateStatusTransition('Pending', 'Enrolled');
  assert(activationResult.isValid, 'Should be able to activate pending enrollment');

  // Test enrollment waiving
  const waiveResult = MockEmployeeEnrollmentModel.validateStatusTransition('Enrolled', 'Waived');
  assert(waiveResult.isValid, 'Should be able to waive enrolled enrollment');

  // Test enrollment termination
  const terminateResult = MockEmployeeEnrollmentModel.validateStatusTransition('Enrolled', 'Terminated');
  assert(terminateResult.isValid, 'Should be able to terminate enrolled enrollment');

  // Test enrollment reinstatement
  const reinstateResult = MockEmployeeEnrollmentModel.validateStatusTransition('Waived', 'Enrolled');
  assert(reinstateResult.isValid, 'Should be able to reinstate waived enrollment');

  log('Enrollment operations tests completed', 'success');
}

function testEnrollmentTypes() {
  log('Testing enrollment types...', 'info');

  // Test Open Enrollment
  const openEnrollment = {
    enrolledUnder: 'Open Enrollment',
    enrollmentDate: '2024-01-15', // Within enrollment period
    effectiveDate: '2024-03-01'
  };
  assert(openEnrollment.enrolledUnder === 'Open Enrollment', 'Open enrollment type should be set correctly');

  // Test New Hire enrollment
  const newHireEnrollment = {
    enrolledUnder: 'New Hire',
    enrollmentDate: '2024-01-30', // Within 30 days of hire (2024-01-15)
    effectiveDate: '2024-02-15'
  };
  assert(newHireEnrollment.enrolledUnder === 'New Hire', 'New hire enrollment type should be set correctly');

  // 🎯 NEW: Test QLE enrollment with plan assignment validation
  const healthPlan = MOCK_DATA.planAssignments[0]; // Has QLE enabled with 30-day window
  const dentalPlan = MOCK_DATA.planAssignments[1]; // Has QLE enabled with 45-day window
  const lifePlan = MOCK_DATA.planAssignments[2]; // Has QLE disabled

  // Test valid QLE for health plan
  const validQLE = {
    eventType: 'Marriage',
    eventDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString() // 15 days ago
  };
  const validQleResult = MockEmployeeEnrollmentModel.validateQualifyingLifeEvent(validQLE, healthPlan);
  assert(validQleResult.isValid, 'Valid QLE within window should be accepted');

  // Test QLE with different allowed events for dental plan
  const dentalQLE = {
    eventType: 'Birth',
    eventDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString() // 20 days ago
  };
  const dentalQleResult = MockEmployeeEnrollmentModel.validateQualifyingLifeEvent(dentalQLE, dentalPlan);
  assert(dentalQleResult.isValid, 'Valid QLE for dental plan should be accepted');

  // Test QLE not allowed for life insurance
  const lifeQLE = {
    eventType: 'Marriage',
    eventDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() // 10 days ago
  };
  const lifeQleResult = MockEmployeeEnrollmentModel.validateQualifyingLifeEvent(lifeQLE, lifePlan);
  assert(!lifeQleResult.isValid, 'QLE should not be allowed for life insurance plan');

  // Test expired QLE window
  const expiredQLE = {
    eventType: 'Marriage',
    eventDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString() // 45 days ago
  };
  const expiredQleResult = MockEmployeeEnrollmentModel.validateQualifyingLifeEvent(expiredQLE, healthPlan);
  assert(!expiredQleResult.isValid, 'Expired QLE should be rejected');

  // Test invalid QLE event type (use an event not in the health plan's allowed list)
  const invalidEventQLE = {
    eventType: 'Invalid Event Type', // Not in any allowed events list
    eventDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
  };
  const invalidEventResult = MockEmployeeEnrollmentModel.validateQualifyingLifeEvent(invalidEventQLE, healthPlan);
  assert(!invalidEventResult.isValid, 'Invalid QLE event type should be rejected');

  // Test missing QLE data
  const missingQleResult = MockEmployeeEnrollmentModel.validateQualifyingLifeEvent(null, healthPlan);
  assert(!missingQleResult.isValid, 'Missing QLE data should be rejected');

  log('Enrollment types tests completed', 'success');
}

function testAccessControl() {
  log('Testing access control scenarios...', 'info');

  const employee = MOCK_DATA.users.employees[0];
  const employer = MOCK_DATA.users.employer;
  const broker = MOCK_DATA.users.broker;
  const superAdmin = MOCK_DATA.users.superAdmin;

  // Test employee access to own data
  const employeeAccess = employee.companyId === 'company_001';
  assert(employeeAccess, 'Employee should have access to own company data');

  // Test employer access to company employees
  const employerAccess = employer.companyId === employee.companyId && employer.isAdmin;
  assert(employerAccess, 'Employer should have access to company employee data');

  // Test broker access to client companies
  const brokerAccess = broker.isBroker && employee.brokerId === broker._id;
  assert(brokerAccess, 'Broker should have access to client company data');

  // Test super admin access
  const superAdminAccess = superAdmin.isSuperAdmin;
  assert(superAdminAccess, 'SuperAdmin should have access to all data');

  // Test unauthorized access
  const unauthorizedUser = { _id: 'unauthorized_001', companyId: 'other_company', isAdmin: false, isBroker: false };
  const unauthorizedAccess = unauthorizedUser.companyId === employee.companyId ||
                             unauthorizedUser.brokerId === employee.brokerId ||
                             unauthorizedUser.isSuperAdmin;
  assert(!unauthorizedAccess, 'Unauthorized user should not have access');

  log('Access control tests completed', 'success');
}

function testEdgeCases() {
  log('Testing edge cases...', 'info');

  // Test missing employee data
  const incompleteEmployee = {
    _id: 'incomplete_employee',
    email: '<EMAIL>',
    details: {} // Missing required fields
  };
  const planAssignment = MOCK_DATA.planAssignments[0];
  const incompleteProfileResult = MockEmployeeEnrollmentModel.validateEmployeeProfileForEnrollment(incompleteEmployee, planAssignment);
  assert(!incompleteProfileResult.isValid, 'Incomplete employee profile should fail validation');

  // Test invalid coverage tier
  const invalidTierResult = MockEmployeeEnrollmentModel.validateCoverageTierSelection('Invalid Tier', 'employee_001', []);
  assert(!invalidTierResult.isValid, 'Invalid coverage tier should fail validation');

  // Test cost calculation with missing rate data
  const invalidPlanAssignment = {
    planData: {
      rateStructure: 'composite',
      rates: {} // Missing rate data
    }
  };
  const invalidCostResult = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: invalidPlanAssignment,
    employeeAge: 30,
    selectedTier: 'Employee Only'
  });
  assert(!invalidCostResult.success, 'Cost calculation should fail with missing rate data');

  // Test age-banded calculation with age outside ranges
  const outOfRangeResult = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: MOCK_DATA.planAssignments[2],
    employeeAge: 70, // Outside defined age ranges
    selectedTier: 'Employee Only'
  });
  assert(!outOfRangeResult.success, 'Age-banded calculation should fail for age outside ranges');

  // Test duplicate enrollment scenario
  const existingEnrollment = MOCK_DATA.enrollments[0];
  const duplicateEnrollment = {
    planAssignmentId: existingEnrollment.planAssignmentId,
    employeeId: existingEnrollment.employeeId,
    status: 'Pending'
  };
  // In real implementation, this would be checked by the model
  const isDuplicate = existingEnrollment.planAssignmentId === duplicateEnrollment.planAssignmentId &&
                     existingEnrollment.employeeId === duplicateEnrollment.employeeId &&
                     existingEnrollment.status !== 'Terminated';
  assert(isDuplicate, 'Duplicate enrollment detection should work');

  log('Edge cases tests completed', 'success');
}

function testBusinessRules() {
  log('Testing business rules...', 'info');

  // Test waiting period enforcement
  const recentHireEmployee = {
    _id: 'recent_hire',
    details: {
      hireDate: new Date().toISOString(), // Hired today
      employeeClassType: 'Full-Time'
    }
  };
  const planWithWaitingPeriod = MOCK_DATA.planAssignments[2]; // 60-day waiting period
  const waitingPeriodResult = MockEmployeeEnrollmentModel.isEmployeeEligibleByHireDate(recentHireEmployee, planWithWaitingPeriod);
  assert(!waitingPeriodResult.isEligible, 'Recent hire should not be eligible due to waiting period');

  // Test employee class restrictions
  const partTimeEmployee = MOCK_DATA.users.employees[2]; // Bob Staff (Part-Time)
  const fullTimeOnlyPlan = MOCK_DATA.planAssignments[2]; // Life insurance (Full-Time only)
  const classRestrictionResult = MockEmployeeEnrollmentModel.isEmployeeClassEligible(partTimeEmployee, fullTimeOnlyPlan);
  assert(!classRestrictionResult.isEligible, 'Part-time employee should not be eligible for full-time only plan');

  // Test enrollment period validation (mock)
  const currentDate = new Date();
  const enrollmentStart = new Date('2024-01-01');
  const enrollmentEnd = new Date('2024-01-31');
  const isWithinPeriod = currentDate >= enrollmentStart && currentDate <= enrollmentEnd;
  // Note: This would normally be more complex with QLE and New Hire exceptions

  // Test status transition business rules
  const terminatedEnrollment = MOCK_DATA.enrollments.find(e => e.status === 'Terminated');
  if (!terminatedEnrollment) {
    // Create a mock terminated enrollment for testing
    const mockTerminated = { ...MOCK_DATA.enrollments[0], status: 'Terminated' };
    const cannotActivateTerminated = MockEmployeeEnrollmentModel.validateStatusTransition('Terminated', 'Enrolled');
    assert(cannotActivateTerminated.isValid, 'Should be able to reinstate terminated enrollment to enrolled');
  }

  log('Business rules tests completed', 'success');
}

async function runUnitTests() {
  try {
    log('🧪 Starting Comprehensive Unit Tests for Employee Enrollment', 'info');
    log('================================================================', 'info');

    // Test basic data structure
    assert(MOCK_DATA.users.employees.length === 3, 'Mock employee data loaded');
    assert(MOCK_DATA.planAssignments.length === 3, 'Mock plan assignment data loaded');
    assert(MOCK_DATA.enrollments.length === 3, 'Mock enrollment data loaded');

    log('', 'info');

    // Run all test suites
    testStatusTransitions();
    log('', 'info');

    testEmployeeValidation();
    log('', 'info');

    testCoverageValidation();
    log('', 'info');

    testCostCalculations();
    log('', 'info');

    testEnrollmentOperations();
    log('', 'info');

    testEnrollmentTypes();
    log('', 'info');

    testAccessControl();
    log('', 'info');

    testEdgeCases();
    log('', 'info');

    testBusinessRules();
    log('', 'info');

    // Final summary
    log('================================================================', 'info');
    log('✅ All unit tests completed', 'success');
    log(`📊 Final Results: ${testResults.passed} passed, ${testResults.failed} failed`, 'info');

    if (testResults.failed > 0) {
      log('❌ Failed Tests:', 'error');
      testResults.errors.forEach((error, index) => log(`  ${index + 1}. ${error}`, 'error'));
      log('', 'info');
      log('💡 Please review the failed tests and fix any issues in the implementation.', 'warning');
    } else {
      log('', 'info');
      log('🎉 All tests passed! The Employee Enrollment system is working correctly.', 'success');
      log('', 'info');
      log('📋 Test Coverage Summary:', 'info');
      log('  ✅ Status Management - All transitions validated', 'info');
      log('  ✅ Employee Validation - Profile, hire date, class type', 'info');
      log('  ✅ Coverage Validation - Tier selection and dependent matching', 'info');
      log('  ✅ Cost Calculations - Composite, four-tier, age-banded rates', 'info');
      log('  ✅ Enrollment Operations - Create, activate, waive, terminate, reinstate', 'info');
      log('  ✅ Enrollment Types - Open enrollment, new hire, QLE', 'info');
      log('  ✅ Access Control - Employee, employer, broker, super admin', 'info');
      log('  ✅ Edge Cases - Invalid data, missing fields, out-of-range values', 'info');
      log('  ✅ Business Rules - Waiting periods, class restrictions, duplicates', 'info');
    }

    const successRate = ((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1);
    log(`📈 Success Rate: ${successRate}%`, 'info');

    return testResults.failed === 0;

  } catch (error) {
    log(`💥 Unit test execution failed: ${error.message}`, 'error');
    console.error(error);
    return false;
  }
}

// Execute tests if run directly
if (require.main === module) {
  runUnitTests();
}

module.exports = {
  MOCK_DATA,
  testResults,
  runUnitTests,
  assert,
  log
};
