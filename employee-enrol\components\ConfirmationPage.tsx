'use client';

import React from 'react';
import { User, CheckCircle, Calendar, Mail, Download, Stethoscope, Eye, Heart, Shield } from 'lucide-react';

interface ConfirmationPageProps {
  enrollmentData?: any;
}

const ConfirmationPage: React.FC<ConfirmationPageProps> = ({ enrollmentData }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{ 
          width: '40px', 
          height: '40px', 
          backgroundColor: '#dbeafe', 
          borderRadius: '8px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <User style={{ width: '20px', height: '20px', color: '#2563eb' }} />
        </div>
        <div style={{ 
          backgroundColor: '#f9fafb', 
          borderRadius: '8px', 
          padding: '16px', 
          maxWidth: '512px' 
        }}>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: 0 
          }}>
            🎉 Congratulations! Your enrollment is complete!
          </p>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: '8px 0 0 0' 
          }}>
            You're all set for 2025! I've included some helpful next steps and tips below.
          </p>
        </div>
      </div>

      {/* Confirmation Card */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        border: '1px solid #e5e7eb', 
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '24px' }}>
          <CheckCircle style={{ width: '24px', height: '24px', color: '#10b981' }} />
          <span style={{ fontSize: '18px' }}>🎉</span>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: '600', 
            color: '#111827',
            margin: 0
          }}>
            Enrollment Complete!
          </h2>
        </div>

        {/* Success Messages */}
        <div style={{ marginBottom: '24px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
            <span style={{ fontSize: '16px' }}>🎊</span>
            <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
              Congratulations! Your benefits will start on January 1, 2025.
            </p>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span style={{ fontSize: '16px' }}>📌</span>
            <p style={{ color: '#dc2626', fontWeight: '500', margin: 0 }}>
              Remember, you can change your choices until December 15, 2024
            </p>
          </div>
        </div>

        {/* Next Steps */}
        <div style={{ 
          backgroundColor: '#f8fafc', 
          borderRadius: '8px', 
          padding: '20px',
          marginBottom: '24px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <span style={{ fontSize: '16px' }}>📋</span>
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
              Next Steps & Pro Tips:
            </h3>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <Stethoscope style={{ width: '16px', height: '16px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Need a dentist? Find one near you in your member portal
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <Eye style={{ width: '16px', height: '16px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Use your vision benefit for a free exam & $150 frames
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <span style={{ fontSize: '14px', marginTop: '2px' }}>💰</span>
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Set up your HSA/FSA to save on taxes
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <Download style={{ width: '16px', height: '16px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Download your insurance apps for easy access
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <Calendar style={{ width: '16px', height: '16px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Schedule your preventive care visits early in the year
              </span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '10px 20px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            <span style={{ color: '#2563eb' }}>❓</span>
            Ask Questions
          </button>
          
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '10px 20px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            <Mail size={16} style={{ color: '#6b7280' }} />
            Email Summary
          </button>
          
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '10px 20px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            <Calendar size={16} style={{ color: '#6b7280' }} />
            Add to Calendar
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationPage;
