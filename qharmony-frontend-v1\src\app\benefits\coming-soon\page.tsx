/* eslint-disable react/no-unescaped-entities */
'use client';

import dynamic from 'next/dynamic';
import { useState, useEffect, useRef } from "react";
import { Box, Container, Typography, Link, Button, useMediaQuery, useTheme, TextField, Select, MenuItem, InputLabel, FormControl } from "@mui/material";
import { postRequest, getRequest } from "@/APILayer/axios_helper";
import Navbar from "@/components/Navbar";
import withMobileEdgeFill from "@/components/mobile_edge_fill";
import Footer from "@/components/Footer";
import Alert from '@mui/material/Alert';
import ShareButtons from '@/components/ShareButtons';
import Head from 'next/head';

const stats = [
  "❌ 1 in 4 skip checkups yearly",
  "❌ 55% of PTO goes unused yearly",
  "❌ Up to $500 wellness perks unused yearly",
  "❌ 70% don't know their benefits"
];

const calcRows = [
  ['Preventive care', '$300', '$500', 'Skipped annual exams, delayed diagnosis (CDC)'],
  ['Unused PTO', '$600', '$1,200', 'Time off expired or forfeited (U.S. Travel)'],
  ['FSA/HSA funds', '$300', '$600', 'Forgotten or unused funds (WageWorks)'],
  ['Wellness perks', '$150', '$400', 'Gym/wellness stipends unused (SHRM)'],
  ['Mental health support', '$100', '$250', 'Low EAP utilization (NAMI)'],
  ['Financial/legal assistance', '$50', '$150', 'Underused (MetLife)'],
  ['Tuition/learning stipends', '$100', '$300', 'Missed career development (SHRM)'],
  ['Caregiver/childcare support', '$100', '$300', 'Not utilized (Care.com)'],
  ['Subtotal: Unused Benefits', '$1,700', '$3,700', ''],
  ['Chronic condition development', '$1,000', '$3,000', 'Diabetes, hypertension, etc. (CDC, ADA)'],
  ['Emergency/hospital visits', '$500', '$2,000', 'ER visits, preventable hospitalization (KFF)'],
  ['Productivity loss', '$500', '$1,000', 'Presenteeism (IBI)'],
  ['Absenteeism/disability', '$500', '$1,000', 'Short-term disability or leave (SHRM)'],
  ['Subtotal: Health & Productivity Loss', '$2,500', '$7,000', ''],
  ['Total Estimated Loss per Employee', '$4,200', '$11,000', 'Rounded for clarity']
];

const ComingSoonPage = () => {
  // Set document title using useEffect
  useEffect(() => {
    document.title = "BenOsphere: Stop Losing Your Benefits Money";
  }, []);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState("");
  const [showCalc, setShowCalc] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [counter, setCounter] = useState(0); // Start at 0 for animation
  const [targetCounter, setTargetCounter] = useState(279); // Target value to animate to
  const counterRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch initial counter value
  useEffect(() => {
    const fetchWaitlistCount = async () => {
      try {
        const response = await getRequest("/test/waitlist/entries");
        console.log("Waitlist response:", response);
        
        // The response directly contains the entries array
        if (response && response.entries && Array.isArray(response.entries)) {
          const totalCount = 279 + response.entries.length;
          setTargetCounter(totalCount);
          console.log("Setting target counter to:", totalCount);
        }
      } catch (error) {
        console.error("Error fetching waitlist entries:", error);
      }
    };
    
    fetchWaitlistCount();
  }, []);

  // Animate counter
  useEffect(() => {
    // Clear any existing interval
    if (counterRef.current) {
      clearInterval(counterRef.current);
    }

    // Start counter at 0
    setCounter(0);
    
    // Animate to target value
    const duration = 2000; // 2 seconds
    const steps = 50; // Number of steps
    const increment = Math.ceil(targetCounter / steps);
    const interval = duration / steps;
    
    counterRef.current = setInterval(() => {
      setCounter(prevCount => {
        const nextCount = prevCount + increment;
        if (nextCount >= targetCounter) {
          clearInterval(counterRef.current as NodeJS.Timeout);
          return targetCounter;
        }
        return nextCount;
      });
    }, interval);
    
    return () => {
      if (counterRef.current) {
        clearInterval(counterRef.current);
      }
    };
  }, [targetCounter]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!email) return setError("Email address is required.");
    if (!phone) return setError("Phone number is required.");
    if (phone.length !== 10 || !/^\d{10}$/.test(phone)) {
      return setError("Phone number must be exactly 10 digits.");
    }

    try {
      // Use your existing endpoint to add the user to the waitlist
      await postRequest("/test/waitlist/join", {
        email: email.toLowerCase(),
        phone: phone,
        joinedAt: new Date().toISOString()
      });
      
      // Increment the target counter after successful submission
      setTargetCounter(prevTarget => prevTarget + 1);
      setSubmitted(true);
      setShowForm(false);
    } catch (err: any) {
      console.error("Waitlist join error:", err);
      setError(err?.response?.data?.message || "Failed to join waitlist. Please check your connection and try again.");
    }
  };

  return (
    <Box 
      sx={{ 
        bgcolor: '#000', 
        color: '#fff', 
        minHeight: '99vh',
        display: 'flex', 
        flexDirection: 'column',
        fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'
      }}
    >
      {/* Remove Helmet component */}
      
      <Navbar hideGetStarted />

      <Box 
        component="main" 
        sx={{ 
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: { xs: 1, md: 3 },  // Reduced padding for mobile
          pt: { xs: 4, md: 9 },
        }}
      >
        <Container 
          maxWidth="md" 
          sx={{ 
            px: { xs: 1, sm: 2, md: 3 },  // Reduced padding for mobile
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            pt: { xs: 2, md: 6 }  // Reduced top padding for mobile
          }}
        >
          <Box 
            textAlign="center" 
            maxWidth={900} 
            mx="auto"
            sx={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: { xs: 1, sm: 1.5 },  // Reduced gap for mobile
              py: { xs: 1, sm: 2 }  // Reduced padding for mobile
            }}
          >
            <Typography 
              variant="h1" 
              sx={{ 
                fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' },
                fontWeight: 600, 
                mb: { xs: 1, sm: 2 },
                mt: { xs: 4, sm: 2 }, // Reduced top margin to move text up
                lineHeight: 1.2
              }}
            >
              You could be losing a third of your paycheck.
            </Typography>

            <Typography 
              sx={{ 
                //fontStyle: 'italic', 
                color: '#ccc', 
                mb: { xs: 0.5, sm: 1 },  // Reduced margin for mobile
                fontSize: { xs: '0.875rem', sm: '1rem', md: '1.25rem' }  // Reduced font size for mobile
              }}
            >
              Feeling sick while debt piles up? Don&apos;t skip the care your family already has.
            </Typography>

            <Link 
              href="#" 
              onClick={(e) => { e.preventDefault(); setShowCalc(true); }} 
              sx={{ 
                color: '#B983FF', 
                fontSize: { xs: '0.875rem', sm: '1rem' },
                textDecoration: 'none',
                mb: { xs: 1, sm: 2 },
                display: 'inline-block',
                '&:hover': { color: '#D4A5FF' },
                fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'
              }}
            >
              <span style={{ textDecoration: 'none' }}>👉</span>{' '}
              <span style={{ textDecoration: 'underline' }}>Find your lost employee benefits</span>
            </Link>

            <Box 
              sx={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                justifyContent: 'center', 
                gap: { xs: '4px', sm: '8px' },
                mb: { xs: 1, sm: 2 }
              }}
            >
              {stats.map((stat, i) => (
                <Box 
                  key={i} 
                  sx={{ 
                    bgcolor: '#111', 
                    border: '1px solid #333', 
                    borderRadius: '8px', 
                    px: { xs: 1.5, sm: 2 },
                    py: { xs: 0.5, sm: 0.75 },
                    fontSize: { xs: '0.8rem', sm: '0.95rem' },
                    fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'
                  }}
                >
                  {stat}
                </Box>
              ))}
            </Box>

            <Typography 
              className="counter-text" 
              sx={{ 
                fontSize: { xs: '0.875rem', sm: '1rem' },  // Reduced font size for mobile
                color: '#ccc', 
                mt: { xs: 0.5, sm: 1 }  // Reduced margin for mobile
              }}
            >
              🔥 {counter} people have already joined the waitlist
            </Typography>

            {/* Form or button or success message */}
            {!submitted ? (
              showForm ? (
                <Box 
                  component="form" 
                  onSubmit={handleSubmit} 
                  sx={{ 
                    width: '100%', 
                    maxWidth: { xs: '280px', sm: '400px' },
                    mx: 'auto', 
                    mt: { xs: 2.5, sm: 3.5 }, // Increased margin to make space for share buttons
                    display: 'flex',
                    flexDirection: 'column',
                    gap: { xs: 1.5, sm: 2 }
                  }}
                >
                  <TextField 
                    type="email" 
                    placeholder="Email address" 
                    value={email} 
                    onChange={(e) => setEmail(e.target.value)} 
                    fullWidth
                    required 
                    sx={{ 
                      '& .MuiInputBase-root': {
                        height: { xs: '35px', sm: '45px' },
                        color: '#fff',
                        fontSize: { xs: '0.75rem', sm: '1rem' }
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#444'
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#666'
                      }
                    }}
                  />

                  <TextField
                    type="tel"
                    placeholder="Phone number"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    fullWidth
                    required
                    sx={{ 
                      '& .MuiInputBase-root': {
                        height: { xs: '35px', sm: '45px' },
                        color: '#fff',
                        fontSize: { xs: '0.75rem', sm: '1rem' }
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#444'
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#666'
                      }
                    }}
                  />

                  {error && <Alert severity="error" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{error}</Alert>}

                  <Button 
                    type="submit" 
                    fullWidth 
                    variant="contained" 
                    sx={{ 
                      height: { xs: '32px', sm: '45px' },
                      borderRadius: '8px', 
                      fontWeight: 600, 
                      fontSize: { xs: '0.75rem', sm: '1rem' },
                      background: '#B983FF', 
                      color: '#000', 
                      '&:hover': { backgroundColor: '#D4A5FF' },
                      textTransform: 'none',
                      mt: { xs: 0.5, sm: 1 } // Additional top margin for the button
                    }}
                  >
                    Save Your Paycheck
                  </Button>
                </Box>
              ) : (
                <Button 
                  onClick={() => setShowForm(true)} 
                  variant="contained" 
                  sx={{ 
                    mt: { xs: 2.5, sm: 3.5 }, // Increased margin to make space for share buttons
                    px: { xs: 3, sm: 4 },
                    py: { xs: 1, sm: 1.5 },
                    borderRadius: '8px', 
                    fontWeight: 600, 
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    background: '#B983FF', 
                    color: '#000', 
                    '&:hover': { backgroundColor: '#D4A5FF' },
                    textTransform: 'none'
                  }}
                >
                  Save Your Paycheck
                </Button>
              )
            ) : (
              <Alert 
                severity="success" 
                sx={{ 
                  mt: { xs: 2.5, sm: 3.5 }, // Increased margin to make space for share buttons
                  maxWidth: { xs: '300px', sm: '400px' },
                  mx: 'auto',
                  '& .MuiAlert-message': {
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }
                }}
              >
                Thanks for joining our waitlist! We&apos;ll let you know when we launch.
              </Alert>
            )}

            {/* Always show share buttons */}
            <Box 
              sx={{ 
                mt: { xs: 2, sm: 3 }, 
                display: 'flex', 
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
                gap: 1
              }}
            >
              <Typography 
                sx={{ 
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  color: '#ccc',
                  mb: 1
                }}
              >
                Help a teammate stop losing money — share this.
              </Typography>
              <ShareButtons title="Saw this and thought it might be helpful — we might be losing thousands in employee benefits." />
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Calculator Modal */}
      {showCalc && (
        <Box sx={{ position: 'fixed', inset: 0, bgcolor: 'rgba(0, 0, 0, 0.7)', zIndex: 999, display: 'flex', alignItems: 'center', justifyContent: 'center' }} onClick={() => setShowCalc(false)}>
          <Box sx={{ bgcolor: '#111', color: '#fff', p: 3, borderRadius: 2, border: '1px solid #333', width: '90%', maxWidth: 600, maxHeight: '80vh', overflowY: 'auto', boxShadow: '0 10px 30px rgba(0,0,0,0.5)' }} onClick={(e) => e.stopPropagation()}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">You're losing up to $11,000 annually on benefits.</Typography>
              <Button onClick={() => setShowCalc(false)} sx={{ color: '#aaa', minWidth: 'auto', p: 0.5 }}>X</Button>
            </Box>
            <Box sx={{ width: '100%', overflowX: 'auto' }}>
              <Box component="table" sx={{ width: '100%', borderCollapse: 'collapse', fontSize: '0.9rem' }}>
                <thead>
                  <tr>
                    <th style={{ textAlign: 'left', padding: '8px 12px', borderBottom: '1px solid #444' }}>Category</th>
                    <th style={{ textAlign: 'right', padding: '8px 12px', borderBottom: '1px solid #444' }}>Low</th>
                    <th style={{ textAlign: 'right', padding: '8px 12px', borderBottom: '1px solid #444' }}>High</th>
                    <th style={{ textAlign: 'left', padding: '8px 12px', borderBottom: '1px solid #444' }}>Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {calcRows.map(([cat, low, high, notes], i) => (
                    <tr key={i} style={{ background: [8,13].includes(i) ? '#222' : i===14 ? '#333' : 'transparent', fontWeight: [8,13,14].includes(i) ? 'bold' : 'normal' }}>
                      <td style={{ padding: '8px 12px' }}>{cat}</td>
                      <td style={{ padding: '8px 12px', textAlign: 'right' }}>{low}</td>
                      <td style={{ padding: '8px 12px', textAlign: 'right' }}>{high}</td>
                      <td style={{ padding: '8px 12px', color: '#ccc' }}>{notes}</td>
                    </tr>
                  ))}
                </tbody>
              </Box>
            </Box>
            <Typography sx={{ fontSize: '0.8rem', mt: 3, color: '#aaa', textAlign: 'center' }}>Disclaimer: National averages based on industry research. Actual losses vary per individual.</Typography>
          </Box>
        </Box>
      )}

      <Footer />
    </Box>
  );
};

export default dynamic(() => Promise.resolve(ComingSoonPage), { ssr: false });













