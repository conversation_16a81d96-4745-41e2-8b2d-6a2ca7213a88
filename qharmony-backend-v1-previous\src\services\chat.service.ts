
import { ChatResponse } from './openai.service';

import axios from 'axios';
export class ChatService {


  async processMessage(userId: string, userMessage: string, teamId: string): Promise<ChatResponse> {
    const payload = {
      user_id: userId,
      user_message: userMessage, 
      team_id: teamId
    };
    // console.log(":rocket: ~ SlackController ~ processMessage ~ payload", teamId)
    console.log(":rocket: ~ SlackController ~ payload", payload)
    try {
      const airesponse = await axios.post('http://127.0.0.1:8080/chat',
            payload
          ,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
        );
      console.log(":rocket: ~ SlackController ~ response.data:", airesponse.data)
      console.log('Message sent successfully');
      const response: ChatResponse = { message: airesponse.data.response }; // Create a ChatResponse object
      return response;
    } catch (error) {
      console.error('Error sending message to slack:', error);
    }
    return { message: 'Error processing message' }; // Return a default message
  }
}