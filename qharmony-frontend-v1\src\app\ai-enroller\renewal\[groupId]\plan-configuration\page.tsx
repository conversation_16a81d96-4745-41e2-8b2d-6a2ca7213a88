'use client';

import React, { useState } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { 
  HiOutlineArrowLeft,
  HiOutlineCalendar,
  HiOutlineCog,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import '../../renewal.css';
import '../plan-detail.css';
import './plan-configuration.css';

const PlanConfigurationPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState(3);
  
  const [startDate, setStartDate] = useState('01/01/2025');
  const [endDate, setEndDate] = useState('31/12/2025');

  const groupName = 'TechCorp Solutions';
  const renewalOption = searchParams.get('option') || 'renew-as-is';

  const steps = [
    { number: 1, title: 'Review Current Plans', subtitle: 'View existing benefit plans', active: false, completed: true },
    { number: 2, title: 'Renewal Options', subtitle: 'Choose renewal type', active: false, completed: true },
    { number: 3, title: 'Plan Configuration', subtitle: 'Set dates and modifications', active: currentStep === 3 },
    { number: 4, title: 'Document Upload', subtitle: 'Upload plan documents', active: false },
    { number: 5, title: 'Validation', subtitle: 'Review and validate setup', active: false },
    { number: 6, title: 'Finalize', subtitle: 'Complete renewal process', active: false },
    { number: 7, title: 'Export', subtitle: 'Download and share data', active: false }
  ];

  const calculateDays = () => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { 
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric' 
    });
  };

  const handleContinue = () => {
    router.push(`/ai-enroller/renewal/${params.groupId}/document-upload`);
  };

  const handlePrevious = () => {
    router.back();
  };

  return (
    <div className="plan-renewal-detail">
      {/* Header */}
      <div className="detail-header">
        <button 
          className="back-btn"
          onClick={() => router.push('/ai-enroller/renewal')}
        >
          <HiOutlineArrowLeft size={20} />
          Back to Dashboard
        </button>
        
        <div className="header-info">
          <h1>Plan Renewal</h1>
          <h2>{groupName}</h2>
          <div className="step-indicator">Step {currentStep} of 7</div>
        </div>

        <div className="completion-status">
          43% Complete
        </div>
      </div>

      {/* Progress Steps */}
      <div className="renewal-steps">
        {steps.map((step, index) => (
          <div key={step.number} className={`renewal-step ${step.active ? 'active' : ''} ${step.completed ? 'completed' : ''}`}>
            <div className="step-number">
              {step.completed ? '✓' : step.number}
            </div>
            <div className="step-content">
              <div className="step-title">{step.title}</div>
              <div className="step-subtitle">{step.subtitle}</div>
            </div>
            {index < steps.length - 1 && <div className="step-connector"></div>}
          </div>
        ))}
      </div>

      {/* Plan Configuration */}
      <div className="plan-configuration-section">
        <div className="config-header">
          <div className="config-title">
            <HiOutlineCog size={20} />
            <h3>Plan Configuration</h3>
          </div>
          <p>Set the new plan year dates and configure plan details for {groupName}.</p>
        </div>

        <div className="config-content">
          {/* Plan Year Dates */}
          <div className="config-card">
            <div className="card-header">
              <HiOutlineCalendar size={20} />
              <h4>Plan Year Dates</h4>
            </div>
            
            <div className="date-inputs">
              <div className="date-field">
                <label htmlFor="start-date">Plan Year Start Date</label>
                <input
                  id="start-date"
                  type="date"
                  value={startDate.split('/').reverse().join('-')}
                  onChange={(e) => setStartDate(e.target.value.split('-').reverse().join('/'))}
                  className="date-input"
                />
                <span className="date-help">When the new plan year begins</span>
              </div>
              
              <div className="date-field">
                <label htmlFor="end-date">Plan Year End Date</label>
                <input
                  id="end-date"
                  type="date"
                  value={endDate.split('/').reverse().join('-')}
                  onChange={(e) => setEndDate(e.target.value.split('-').reverse().join('/'))}
                  className="date-input"
                />
                <span className="date-help">When the plan year expires</span>
              </div>
            </div>
          </div>

          {/* Plan Year Summary */}
          <div className="config-card summary-card">
            <div className="card-header">
              <HiOutlineInformationCircle size={20} />
              <h4>Plan Year Summary</h4>
            </div>
            
            <div className="summary-content">
              <p>
                The new plan year will run from <strong>{formatDate(startDate)}</strong> to <strong>{formatDate(endDate)}</strong>, 
                covering a total of <strong>{calculateDays()} days</strong>.
              </p>
            </div>
          </div>

          {/* Plan Modifications (if applicable) */}
          {renewalOption !== 'renew-as-is' && (
            <div className="config-card">
              <div className="card-header">
                <HiOutlineCog size={20} />
                <h4>Plan Modifications</h4>
              </div>
              
              <div className="modifications-content">
                <div className="modification-notice">
                  <HiOutlineInformationCircle size={20} />
                  <p>
                    Plan modifications will be configured in the next steps based on your selected renewal strategy: 
                    <strong>{renewalOption === 'copy-modify' ? 'Copy & Modify Plans' : 'Major Plan Changes'}</strong>
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="navigation-section">
          <button 
            className="nav-btn secondary"
            onClick={handlePrevious}
          >
            <HiOutlineArrowLeft size={16} />
            Previous
          </button>
          
          <button 
            className="nav-btn primary enabled"
            onClick={handleContinue}
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};

export default PlanConfigurationPage;
