
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, RefreshCw } from 'lucide-react';

const facts = [
  {
    fact: "The average American spends $4,500 annually on healthcare",
    context: "With our medical plans, your maximum out-of-pocket is capped much lower!"
  },
  {
    fact: "Dental cleanings can prevent $2,000+ in future treatments",
    context: "Our dental plan covers 100% of preventive care - that's FREE cleanings!"
  },
  {
    fact: "Vision exams can detect 270+ systemic diseases",
    context: "Including diabetes and high blood pressure - early detection saves lives and money!"
  },
  {
    fact: "HSA contributions are triple tax-advantaged",
    context: "Tax-deductible contributions, tax-free growth, and tax-free withdrawals for medical expenses!"
  },
  {
    fact: "Employer contributes 80% of your medical premium",
    context: "That's thousands in value you get for free!"
  },
  {
    fact: "Pet insurance can save you up to 90% on vet bills",
    context: "Emergency surgeries can cost $5,000+ without insurance!"
  }
];

export const DidYouKnowFacts = () => {
  const [currentFact, setCurrentFact] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentFact(prev => (prev + 1) % facts.length);
    }, 8000);
    return () => clearInterval(timer);
  }, []);

  const nextFact = () => {
    setCurrentFact(prev => (prev + 1) % facts.length);
  };

  return (
    <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950 border-purple-200 dark:border-purple-800">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="flex items-center gap-2 mb-2">
            <Lightbulb className="w-5 h-5 text-yellow-500" />
            <Badge variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
              💡 Did You Know?
            </Badge>
          </div>
          <button 
            onClick={nextFact}
            className="ml-auto p-1 hover:bg-purple-100 dark:hover:bg-purple-900 rounded"
          >
            <RefreshCw className="w-4 h-4 text-purple-600" />
          </button>
        </div>
        <div className="space-y-2">
          <p className="font-medium text-gray-900 dark:text-gray-100">
            {facts[currentFact].fact}
          </p>
          <p className="text-sm text-purple-700 dark:text-purple-300">
            {facts[currentFact].context}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
