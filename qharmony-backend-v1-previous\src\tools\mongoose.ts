import dotenv from 'dotenv';
dotenv.config();

import InitService from '../services/init.service';
import logger, { prettyJSON } from '../utils/logger';
import SlackOauthModelClass from '../nosql/slackOauth.model';

export async function append() {
  try {
    await SlackOauthModelClass.addData({
      slackTeamId: 'T01J7T0J8J8',
      slackBotOauthToken: 'xoxb-123456789012-12312',
      slackTeamName: 'Test Teswdwam',
      slackInstallerUserId: 'U01Jcdcfew7T0J8J9',
      slackInstalledAtTs: Date.now(),
    });
    await SlackOauthModelClass.deleteDataBySlackTeamId('T01J7T0J8J9');

    const j = await SlackOauthModelClass.getAllData();
    logger.info(prettyJSON(j));
  } catch (error) {
    console.error(error);
  }
}

InitService.init().then(async () => {
  await append();
});

// To run the script, run the following command:
// > NODE_ENV=debug npx ts-node src/tools/mongoose.ts
