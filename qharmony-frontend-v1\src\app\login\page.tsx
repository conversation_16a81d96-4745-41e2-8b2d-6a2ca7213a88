"use client";

import React, { useEffect, useState, FC } from "react";
import { isSignInWithEmailLink, signInWithEmailLink } from "firebase/auth";
import { auth } from "@/utils/firebase";
import { useRouter } from "next/navigation";
import { Box, Typography, CircularProgress, Button } from "@mui/material";
import {
  onboardEmployee,
  parseParamsFromUrl,
} from "@/middleware/user_middleware";
import RightPanelOnlyComponent from "@/components/RightPanelOnlyComponent";
import Link from "next/link";

const SignInWithEmailLink: FC = () => {
  const router = useRouter();
  const [email, setEmail] = useState<string | null>(null);
  const [companyId, setCompanyId] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleSignInLinkRedirect = async () => {
      const url = window.location.href;
      const params = await parseParamsFromUrl(url);

      const paramsEmail = params.email;
      const paramsCompanyId = params.companyId;
      const paramsUserId = params.userId;

      if (isSignInWithEmailLink(auth, url)) {
        let storedEmail = window.localStorage.getItem("emailForSignIn1");
        if (!storedEmail && !params.email) {
          setError("Invalid sign-in link. Email not found.");
        } else {
          setEmail(storedEmail || paramsEmail);
          setCompanyId(paramsCompanyId);
          setUserId(paramsUserId);
        }
      } else {
        setError("Invalid sign-in link.");
        setLoading(false);
      }
    };

    handleSignInLinkRedirect();
  }, []);

  useEffect(() => {
    const signIn = async () => {
      if (email) {
        try {
          await signInWithEmailLink(auth, email, window.location.href);
          console.log("Sign-in successful");
          window.localStorage.removeItem("emailForSignIn1");
          // store userId and companyId locally

          localStorage.setItem("userid1", userId as string);
          localStorage.setItem("companyId1", companyId as string);
          onboardEmployee(companyId as string, userId as string);
          setLoading(false);
          router.push("/dashboard");
        } catch (error) {
          console.error("Error signing in with email link:", error);
          setError("Failed to sign in. Please try again.");
          setLoading(false);
        }
      }
    };

    signIn();
  }, [email, companyId, userId, router]);

  return (
    <Box sx={{ mt: 5, textAlign: "center" }}>
      {loading ? (
        <>
          <CircularProgress />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Signing you in...
          </Typography>
        </>
      ) : error ? (
        <RightPanelOnlyComponent
          LeftComponent={
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                color: "white",
                textAlign: "center",
                height: "100%", // Take up the entire height
              }}
            >
              <Typography sx={{ fontWeight: "bold", fontSize: "60px", mb: 2 }}>
                ❌ Magic Link Expired
              </Typography>
              <Typography
                variant="body1"
                sx={{ fontSize: "20px", color: "#bbbbbb", mb: 4 }}
              >
                You can sign in again by requesting a new link <Link href="/" style={{ color: '#B983FF', textDecoration: 'underline' }}>here</Link>.
              </Typography>
            </Box>
          }
        />
      ) : null}
    </Box>
  );
};

export default SignInWithEmailLink;
