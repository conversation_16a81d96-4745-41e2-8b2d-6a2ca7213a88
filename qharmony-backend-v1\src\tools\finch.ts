import dotenv from 'dotenv';
dotenv.config();

import axios from 'axios';
import InitService from '../services/init.service';
import logger, { prettyJSON } from '../utils/logger';
import FinchService from '../services/finch.service';
import CompanyDocumentModelClass, { CompanyDocumentDataInterface } from '../nosql/companyDocument.model';
// import FinchIndividualModelClass from '../nosql/finchIndividualData.model';
// import CommonService from '../services/common.service';

export async function getIntrospect({
  finchAccessToken,
}: {
  finchAccessToken: string;
}) {
  try {
    logger.info('Getting introspect data');
    const response = await axios.get('https://api.tryfinch.com/introspect', {
      headers: {
        Authorization: `Bearer ${finchAccessToken}`,
        'Finch-API-Version': '2020-09-17',
        'Content-Type': 'application/json',
      },
    });
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Read basic company data
export async function getCompany({
  finchAccessToken,
}: {
  finchAccessToken: string;
}) {
  try {
    logger.info('Getting company data');
    const response = await axios.get(
      'https://api.tryfinch.com/employer/company',
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Read company directory and organization structure
export async function getCompanyDirectory({
  finchAccessToken,
}: {
  finchAccessToken: string;
}) {
  try {
    logger.info('Getting company directory');
    const response = await axios.get(
      'https://api.tryfinch.com/employer/directory',
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Read individual data, excluding income and employment data
export async function getIndividual({
  finchAccessToken,
  individualIds,
}: {
  finchAccessToken: string;
  individualIds: string[];
}) {
  try {
    logger.info(
      `Getting individual data for individual with id ${prettyJSON(
        individualIds
      )}`
    );
    const response = await axios.post(
      'https://api.tryfinch.com/employer/individual',
      {
        requests: individualIds.map((i) => {
          return {
            individual_id: i,
          };
        }),
      },
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Read individual employment and income data
export async function getEmployment({
  finchAccessToken,
  individualIds,
}: {
  finchAccessToken: string;
  individualIds: string[];
}) {
  try {
    logger.info(
      `Getting employment data for individual with id ${prettyJSON(
        individualIds
      )}`
    );
    const response = await axios.post(
      'https://api.tryfinch.com/employer/employment',
      {
        requests: individualIds.map((i) => {
          return {
            individual_id: i,
          };
        }),
      },
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Read payroll and contractor related payments by the company.
export async function getPayment({
  finchAccessToken,
  startDate,
  endDate,
}: {
  finchAccessToken: string;
  startDate: string;
  endDate: string;
}) {
  try {
    logger.info(
      `Getting payment data for start date ${startDate} and end date ${endDate}`
    );
    const response = await axios.get(
      `https://api.tryfinch.com/employer/payment?start_date=${startDate}&end_date=${endDate}`,
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Read detailed pay statements for each individual.
export async function getPayStatement({
  finchAccessToken,
  paymentIds,
}: {
  finchAccessToken: string;
  paymentIds: string[];
}) {
  try {
    logger.info('Getting pay statement data');
    const response = await axios.post(
      `https://api.tryfinch.com/employer/pay-statement`,
      {
        requests: paymentIds.map((i) => {
          return {
            payment_id: i,
          };
        }),
      },
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Reqest Forwarding
export async function getReqestForwarding({
  finchAccessToken,
}: {
  finchAccessToken: string;
}) {
  try {
    logger.info('Getting request forwarding data');
    const response = await axios.post(
      `https://api.tryfinch.com/forward`,
      {
        method: 'GET',
        // "route": "/v1/employees/0f9ed21d-ca64-4107-aba4-a42a06d80b09/employee_benefits", // Employee benefits: https://docs.gusto.com/app-integrations/reference/get-v1-employees-employee_id-employee_benefits
        // route: "/v1/employees/0f9ed21d-ca64-4107-aba4-a42a06d80b09/garnishments", // Employee garnishments: https://docs.gusto.com/app-integrations/reference/get-v1-employees-employee_id-garnishments
        // route: "/v1/employees/0f9ed21d-ca64-4107-aba4-a42a06d80b09/employment_history", // Employee employment history: https://docs.gusto.com/app-integrations/reference/get-v1-employees-employee_id-employment_history
        // route: "/v1/employees/0f9ed21d-ca64-4107-aba4-a42a06d80b09/rehire", // Employee rehire: https://docs.gusto.com/app-integrations/reference/get-v1-employees-employee_id-rehire
        // route: "/v1/employees/0f9ed21d-ca64-4107-aba4-a42a06d80b09/terminations", // Employee termination: https://docs.gusto.com/app-integrations/reference/get-v1-employees-employee_id-terminations
        // route: "/v1/employees/0f9ed21d-ca64-4107-aba4-a42a06d80b09/jobs", // Employee jobs: https://docs.gusto.com/app-integrations/reference/get-v1-employees-employee_id-jobs
        // route: "/v1/jobs/ba780824-f100-4c0c-8b1f-4b2b625da88e/compensations", // https://docs.gusto.com/app-integrations/reference/get-v1-jobs-job_id-compensations
        route: '/v1/compensations/a91f1d21-a77b-4bb6-808a-35bbf1642767', // https://docs.gusto.com/app-integrations/reference/get-v1-compensations-compensation_id
        headers: null,
        params: null,
        data: null,
      },
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Get all deductions
export async function getDeductions({
  finchAccessToken,
}: {
  finchAccessToken: string;
}) {
  try {
    logger.info('Getting deductions data');
    const response = await axios.get(
      `https://api.tryfinch.com/employer/benefits`,
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Get a specific deduction by ID
export async function getDeductionById({
  finchAccessToken,
  benefitId,
}: {
  finchAccessToken: string;
  benefitId: string;
}) {
  try {
    logger.info(`Getting deduction by id ${benefitId}`);
    const response = await axios.get(
      `https://api.tryfinch.com/employer/benefits/${benefitId}`,
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Get enrolled individuals for a specific deduction
export async function getEnrolledIndividuals({
  finchAccessToken,
  benefitId,
}: {
  finchAccessToken: string;
  benefitId: string;
}) {
  try {
    logger.info(
      `Getting enrolled individuals for deduction with id ${benefitId}`
    );
    const response = await axios.get(
      `https://api.tryfinch.com/employer/benefits/${benefitId}/enrolled`,
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

// Get deduction for an individual
export async function getDeductionForIndividual({
  finchAccessToken,
  benefitId,
}: {
  finchAccessToken: string;
  benefitId: string;
}) {
  try {
    logger.info(
      `Getting deduction for individual for deduction with id ${benefitId}`
    );
    const response = await axios.get(
      `https://api.tryfinch.com/employer/benefits/${benefitId}/individuals`,
      {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      }
    );
    logger.info(prettyJSON(response.data));
  } catch (error) {
    console.error(error);
  }
}

InitService.init().then(async () => {
  const companyDocumentData: CompanyDocumentDataInterface = {
    slackTeamId: 'T06UDHHU2M9',
    employeeHandbookUrl: 'https://qharmony-public.s3.us-east-1.amazonaws.com/meltano/Meltano_Handbook_People_Operations.pdf',
    trainingLink: 'https://example.com/T06UDHHU2M9/training',
    performanceReviewLink: 'https://example.com/T06UDHHU2M9/performance-review',
    immigrationLink: 'https://example.com/T06UDHHU2M9/immigration',
    medicalClaimLink: 'https://example.com/T06UDHHU2M9/medical-claim',
    expenseReportingLink: 'https://example.com/T06UDHHU2M9/expense-reporting',
    addDependentLink: 'https://example.com/T06UDHHU2M9/add-dependent',
    shortTermDisabilityLink: 'https://example.com/T06UDHHU2M9/short-term-disability',
    longTermDisabilityLink: 'https://example.com/T06UDHHU2M9/long-term-disability',
    maternityLeaveLink: 'https://example.com/T06UDHHU2M9/maternity-leave',
  };
  CompanyDocumentModelClass.addData(companyDocumentData);
  // const commonService = new CommonService();
  const finchAccessToken = '80065c5a-5203-4e18-bccd-053b7e986d7e';
  // Ankit: 55cd590f-cbea-46b2-8224-c5d5223e6c7f
  // Shan: 448457d0-59eb-45fb-a7c5-18f0b9580f0c


  const finchService = new FinchService();
  // await getIntrospect({finchAccessToken});
  // await getCompany({finchAccessToken});
  // await getCompanyDirectory({finchAccessToken});
  // await getIndividual({
  //   finchAccessToken,
  //   individualIds: ['55cd590f-cbea-46b2-8224-c5d5223e6c7f'],
  // });
  // await getEmployment({
  //   finchAccessToken,
  //   individualIds: ['55cd590f-cbea-46b2-8224-c5d5223e6c7f'],
  // });
  // await getPayment({ finchAccessToken, startDate: '2024-05-16', endDate: '2024-05-31' });
  // await getPayStatement({
  //   finchAccessToken,
  //   paymentIds: ['eb75de92-1a87-43eb-b5a7-709c7af49cff', '575798d8-ece5-42ca-9889-3557e15a3059'],
  // });
  // await getReqestForwarding({finchAccessToken});
  // await getDeductions({finchAccessToken});
  // await getDeductionById({ finchAccessToken, benefitId: 'c9c2bd5f-22aa-445c-abe9-a8455db6354e' });
  // await getEnrolledIndividuals({
  //   finchAccessToken,
  //   benefitId: 'c9c2bd5f-22aa-445c-abe9-a8455db6354e',
  // });
  // await getDeductionForIndividual({
  //   finchAccessToken,
  //   benefitId: 'c9c2bd5f-22aa-445c-abe9-a8455db6354e',
  // });
  const alldata = await finchService.getAllIndividualsData({
    finchAccessToken,
  });
  logger.info(prettyJSON(alldata));
  // for (const data of alldata) {
  //   FinchIndividualModelClass.addData(data as FinchIndividualDataInterface);
  // }

  // const allUsers = await commonService.getAllUsers({
  //   slackBotToken: '*********************************************************',
  // });
  // if (!allUsers) {
  //   logger.error('Failed to get all users');
  //   return;
  // }

  // const user = await commonService.getUser({
  //   slackBotToken: '*********************************************************',
  //   userId: 'U06RBK6QECR'
  // });
  // if (!user) {
  //   logger.error('Failed to get all users');
  //   return;
  // }
  // logger.info(prettyJSON(user));
  // const finchIndividualEntity = await FinchIndividualModelClass.getDataByEmail(
  //   '<EMAIL>'
  // );
  // logger.info(prettyJSON(finchIndividualEntity));
});

// To run the script, run the following command:
// > NODE_ENV=debug npx ts-node src/tools/finch.ts

// Ref: https://developer.tryfinch.com/api-reference/
