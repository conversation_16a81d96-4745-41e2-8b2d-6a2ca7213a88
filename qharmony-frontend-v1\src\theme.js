"use client";

import { createTheme } from "@mui/material/styles";
import { ThemeOptions } from "@mui/material/styles";
import "../public/font.css"; // Font import

const themeOptions = {
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 768,    // 🔥 CHANGED from 900 to 768
      lg: 1200,
      xl: 1536,
    },
  },

  typography: {
    fontFamily: ["SF Pro"].join(","),
    logoTitle: {
      fontWeight: 800,
      fontSize: "1.5rem",
      lineHeight: "1.2",
    },
    viewBenefitTypeSectionHeading: {
      fontWeight: "500",
      fontSize: "28px",
      lineHeight: "20.8px",
      color: "black",
      textAlign: "left",
      marginBottom: 4,
      marginTop: 5,
    },
    toggleViewBenefitSubType: {
      fontWeight: "500",
      fontSize: "17px",
      lineHeight: "20.8px",
      color: "black",
      textAlign: "left",
    },
  },

  chip: {
    benefitStatusAvailableChip: {
      bgcolor: "#67BA6B1F",
      color: "#67BA6B",
      borderRadius: "8px",
      "& .MuiChip-label": {
        padding: 1,
        fontWeight: "semibold",
        fontSize: "14px",
      },
    },
    benefitStatusDisabledChip: {
      bgcolor: "#f0f0f0",
      color: "black",
      borderRadius: "8px",
      "& .MuiChip-label": {
        padding: 1,
        fontWeight: "semibold",
        fontSize: "14px",
      },
    },
  },

  button: {
    editBenefitButton: {
      backgroundColor: "#f0f0f0",
      borderRadius: "8px",
      textTransform: "none",
      color: "#000",
      padding: "4px",
      marginRight: 3,
      boxShadow: "none",
      border: "none",
      "&:hover": {
        backgroundColor: "#E0E0E0",
        boxShadow: "none",
      },
    },
  },

  palette: {
    backgroundBlue: {
      main: "#1073ff",
    },
    primary: {
      main: "#1073ff",
    },
    secondary: {
      main: "#ff4081",
    },
  },
};

const theme = createTheme(themeOptions);

export default theme;
