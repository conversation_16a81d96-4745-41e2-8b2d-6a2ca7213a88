/**
 * COMPREHENSIVE RATE STRUCTURE TEST SUITE
 * Tests all rate structures with random variations and edge cases
 * 
 * Coverage:
 * - Composite Rate Structure
 * - Age-Banded (Fixed & Multiplier)
 * - Four-Tier Rate Structure  
 * - Age-Banded-Four-Tier (Fixed & Multiplier)
 * - Salary-Based (Fixed & Multiplier)
 * - Random employee/employer cost splits
 * - Various tier configurations
 */

// Mock the CostCalculationService with all rate structure support
class ComprehensiveCostCalculationService {
  
  static calculateCompositeCost(tierDetails) {
    return tierDetails.totalCost || 0;
  }

  static calculateFourTierCost(tierDetails) {
    return tierDetails.totalCost || 0;
  }

  static calculateAgeBandedCost(planAssignment, employeeAge = 30, tierDetails) {
    if (!planAssignment.ageBandedRates || !Array.isArray(planAssignment.ageBandedRates)) {
      return tierDetails.totalCost || 0;
    }

    const ageBand = planAssignment.ageBandedRates.find((band) =>
      employeeAge >= band.ageMin && employeeAge <= band.ageMax
    );

    if (ageBand) {
      const baseCost = tierDetails.totalCost || 0;
      
      if (ageBand.type === 'multiplier') {
        return baseCost * (ageBand.rate || 1.0);
      } else {
        return baseCost + (ageBand.rate || 0);
      }
    }

    return tierDetails.totalCost || 0;
  }

  static calculateAgeBandedFourTierCost(planAssignment, employeeAge = 30, tierDetails) {
    // Same logic as age-banded
    return this.calculateAgeBandedCost(planAssignment, employeeAge, tierDetails);
  }

  static calculateSalaryBasedCost(planAssignment, tierDetails, employeeSalary) {
    if (!employeeSalary || employeeSalary <= 0) {
      return tierDetails.totalCost || 0;
    }

    if (planAssignment.salaryBasedRates && Array.isArray(planAssignment.salaryBasedRates)) {
      const salaryBand = planAssignment.salaryBasedRates.find((band) =>
        employeeSalary >= band.salaryMin && employeeSalary <= band.salaryMax
      );

      if (salaryBand) {
        const baseCost = tierDetails.totalCost || 0;
        
        if (salaryBand.type === 'multiplier') {
          return baseCost * (salaryBand.rate || 1.0);
        } else {
          return baseCost + (salaryBand.rate || 0);
        }
      }
    }

    if (planAssignment.salaryPercentage && typeof planAssignment.salaryPercentage === 'number') {
      const annualCost = employeeSalary * (planAssignment.salaryPercentage / 100);
      return annualCost / 12;
    }

    return tierDetails.totalCost || 0;
  }

  static applyContributionPolicies(totalCost, employerContribution, employeeContribution, tierDetails, wasAdjusted) {
    if (tierDetails &&
        typeof tierDetails.employeeCost === 'number' &&
        typeof tierDetails.employerCost === 'number' &&
        tierDetails.employeeCost >= 0 &&
        tierDetails.employerCost >= 0) {

      const originalTierTotal = tierDetails.employeeCost + tierDetails.employerCost;
      
      if (!wasAdjusted) {
        return {
          employeeAmount: Math.round(tierDetails.employeeCost * 100) / 100,
          employerAmount: Math.round(tierDetails.employerCost * 100) / 100,
          totalAmount: Math.round(totalCost * 100) / 100
        };
      } else {
        const scaleFactor = totalCost / originalTierTotal;
        
        return {
          employeeAmount: Math.round(tierDetails.employeeCost * scaleFactor * 100) / 100,
          employerAmount: Math.round(tierDetails.employerCost * scaleFactor * 100) / 100,
          totalAmount: Math.round(totalCost * 100) / 100
        };
      }
    }

    // Fallback to contribution policies
    const employerAmount = totalCost * 0.8;
    const employeeAmount = totalCost * 0.2;
    
    return {
      employeeAmount: Math.round(employeeAmount * 100) / 100,
      employerAmount: Math.round(employerAmount * 100) / 100,
      totalAmount: Math.round(totalCost * 100) / 100
    };
  }

  static calculateEnrollmentCost(input) {
    const { planAssignment, employeeAge, selectedTier, employeeSalary } = input;

    const tierDetails = planAssignment.coverageTiers.find(tier => tier.tierName === selectedTier);
    if (!tierDetails) {
      return { success: false, error: `Coverage tier '${selectedTier}' not found` };
    }

    let baseCost = 0;
    let wasAdjusted = false;
    
    switch (planAssignment.rateStructure) {
      case 'Composite':
        baseCost = this.calculateCompositeCost(tierDetails);
        wasAdjusted = false;
        break;
      case 'Age-Banded':
        baseCost = this.calculateAgeBandedCost(planAssignment, employeeAge, tierDetails);
        wasAdjusted = Math.abs(baseCost - (tierDetails.totalCost || 0)) > 0.01;
        break;
      case 'Four-Tier':
        baseCost = this.calculateFourTierCost(tierDetails);
        wasAdjusted = false;
        break;
      case 'Age-Banded-Four-Tier':
        baseCost = this.calculateAgeBandedFourTierCost(planAssignment, employeeAge, tierDetails);
        wasAdjusted = Math.abs(baseCost - (tierDetails.totalCost || 0)) > 0.01;
        break;
      case 'Salary-Based':
        baseCost = this.calculateSalaryBasedCost(planAssignment, tierDetails, employeeSalary);
        wasAdjusted = Math.abs(baseCost - (tierDetails.totalCost || 0)) > 0.01;
        break;
      default:
        baseCost = tierDetails.totalCost || 0;
        wasAdjusted = false;
    }

    const contribution = this.applyContributionPolicies(
      baseCost,
      planAssignment.employerContribution,
      planAssignment.employeeContribution,
      tierDetails,
      wasAdjusted
    );

    return {
      success: true,
      cost: contribution
    };
  }
}

// Utility functions for generating random test data
function generateRandomCostSplit(totalCost) {
  // Generate random employee percentage between 10% and 40%
  const employeePercentage = 0.1 + Math.random() * 0.3; // 10% to 40%
  const employeeCost = Math.round(totalCost * employeePercentage * 100) / 100;
  const employerCost = Math.round((totalCost - employeeCost) * 100) / 100;
  
  return { employeeCost, employerCost };
}

function generateRandomTiers() {
  const tierNames = ['Employee Only', 'Employee + Spouse', 'Employee + Child(ren)', 'Family'];
  const baseCosts = [400, 800, 750, 1200]; // Base costs for each tier
  
  return tierNames.map((tierName, index) => {
    const totalCost = baseCosts[index] + Math.round(Math.random() * 200); // Add random variation
    const { employeeCost, employerCost } = generateRandomCostSplit(totalCost);
    
    return {
      tierName,
      totalCost,
      employeeCost,
      employerCost
    };
  });
}

function generateRandomAgeBands(type = 'fixed') {
  if (type === 'multiplier') {
    return [
      { ageMin: 18, ageMax: 29, rate: 0.7 + Math.random() * 0.4, type: 'multiplier' }, // 0.7-1.1
      { ageMin: 30, ageMax: 39, rate: 0.9 + Math.random() * 0.3, type: 'multiplier' }, // 0.9-1.2
      { ageMin: 40, ageMax: 49, rate: 1.1 + Math.random() * 0.4, type: 'multiplier' }, // 1.1-1.5
      { ageMin: 50, ageMax: 65, rate: 1.3 + Math.random() * 0.5, type: 'multiplier' }  // 1.3-1.8
    ];
  } else {
    return [
      { ageMin: 18, ageMax: 29, rate: Math.round(50 + Math.random() * 100), type: 'fixed' },  // $50-150
      { ageMin: 30, ageMax: 39, rate: Math.round(100 + Math.random() * 150), type: 'fixed' }, // $100-250
      { ageMin: 40, ageMax: 49, rate: Math.round(200 + Math.random() * 200), type: 'fixed' }, // $200-400
      { ageMin: 50, ageMax: 65, rate: Math.round(300 + Math.random() * 300), type: 'fixed' }  // $300-600
    ];
  }
}

function generateRandomSalaryBands(type = 'fixed') {
  if (type === 'multiplier') {
    return [
      { salaryMin: 30000, salaryMax: 50000, rate: 0.6 + Math.random() * 0.3, type: 'multiplier' }, // 0.6-0.9
      { salaryMin: 50001, salaryMax: 80000, rate: 0.9 + Math.random() * 0.2, type: 'multiplier' }, // 0.9-1.1
      { salaryMin: 80001, salaryMax: 120000, rate: 1.1 + Math.random() * 0.3, type: 'multiplier' }, // 1.1-1.4
      { salaryMin: 120001, salaryMax: 999999, rate: 1.3 + Math.random() * 0.5, type: 'multiplier' } // 1.3-1.8
    ];
  } else {
    return [
      { salaryMin: 30000, salaryMax: 50000, rate: Math.round(50 + Math.random() * 100), type: 'fixed' },   // $50-150
      { salaryMin: 50001, salaryMax: 80000, rate: Math.round(100 + Math.random() * 150), type: 'fixed' },  // $100-250
      { salaryMin: 80001, salaryMax: 120000, rate: Math.round(200 + Math.random() * 200), type: 'fixed' }, // $200-400
      { salaryMin: 120001, salaryMax: 999999, rate: Math.round(300 + Math.random() * 300), type: 'fixed' } // $300-600
    ];
  }
}

// Test data generators
function createTestPlanAssignment(rateStructure, ageBandType = 'fixed', salaryBandType = 'fixed') {
  const coverageTiers = rateStructure.includes('Four-Tier') ? generateRandomTiers() : [
    { tierName: 'Employee Only', totalCost: 450, ...generateRandomCostSplit(450) },
    { tierName: 'Family', totalCost: 1200, ...generateRandomCostSplit(1200) }
  ];

  const planAssignment = {
    rateStructure,
    coverageTiers,
    employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
    employeeContribution: { contributionType: 'Remainder', contributionAmount: 0 }
  };

  if (rateStructure.includes('Age-Banded')) {
    planAssignment.ageBandedRates = generateRandomAgeBands(ageBandType);
  }

  if (rateStructure === 'Salary-Based') {
    planAssignment.salaryBasedRates = generateRandomSalaryBands(salaryBandType);
  }

  return planAssignment;
}

// Test execution functions
function runSingleTest(testName, planAssignment, testParams) {
  let { employeeAge = 35, employeeSalary = 75000, selectedTier = 'Family' } = testParams;

  // Ensure the selected tier exists in the plan assignment
  const availableTiers = planAssignment.coverageTiers.map(t => t.tierName);
  if (!availableTiers.includes(selectedTier)) {
    selectedTier = availableTiers[availableTiers.length - 1]; // Use last tier as fallback
  }

  console.log(`\n--- ${testName} ---`);

  const result = ComprehensiveCostCalculationService.calculateEnrollmentCost({
    planAssignment,
    employeeAge,
    selectedTier,
    employeeSalary
  });

  if (!result.success) {
    console.log('❌ FAILED:', result.error);
    return false;
  }

  const cost = result.cost;
  const tierDetails = planAssignment.coverageTiers.find(t => t.tierName === selectedTier);

  console.log('Input:', {
    rateStructure: planAssignment.rateStructure,
    tierTotalCost: tierDetails.totalCost,
    tierEmployeeCost: tierDetails.employeeCost,
    tierEmployerCost: tierDetails.employerCost,
    employeeAge,
    employeeSalary
  });

  console.log('Result:', {
    totalAmount: cost.totalAmount,
    employeeAmount: cost.employeeAmount,
    employerAmount: cost.employerAmount,
    employeePercentage: Math.round((cost.employeeAmount / cost.totalAmount) * 100) + '%',
    employerPercentage: Math.round((cost.employerAmount / cost.totalAmount) * 100) + '%'
  });

  // Validation checks (allow for small rounding differences)
  const totalCheck = Math.abs((cost.employeeAmount + cost.employerAmount) - cost.totalAmount) <= 0.02;
  const positiveCheck = cost.employeeAmount >= 0 && cost.employerAmount >= 0 && cost.totalAmount > 0;

  if (!totalCheck) {
    console.log('❌ VALIDATION FAILED: Employee + Employer amounts don\'t equal total');
    return false;
  }

  if (!positiveCheck) {
    console.log('❌ VALIDATION FAILED: Negative amounts detected');
    return false;
  }

  console.log('✅ PASSED');
  return true;
}

function testAllRateStructures() {
  console.log('🧪 COMPREHENSIVE RATE STRUCTURE TEST SUITE');
  console.log('===========================================');

  const testResults = [];
  let testCount = 0;

  // Test scenarios - use tiers that exist in all rate structures
  const scenarios = [
    { employeeAge: 25, employeeSalary: 45000, selectedTier: 'Employee Only' },
    { employeeAge: 35, employeeSalary: 75000, selectedTier: 'Family' },
    { employeeAge: 45, employeeSalary: 95000, selectedTier: 'Employee Only' },
    { employeeAge: 55, employeeSalary: 130000, selectedTier: 'Family' }
  ];

  // 1. Composite Rate Structure
  console.log('\n🔹 TESTING COMPOSITE RATE STRUCTURE');
  for (let i = 0; i < 3; i++) {
    const planAssignment = createTestPlanAssignment('Composite');
    const scenario = scenarios[i % scenarios.length];
    const passed = runSingleTest(`Composite Test ${i + 1}`, planAssignment, scenario);
    testResults.push(passed);
    testCount++;
  }

  // 2. Age-Banded Rate Structure (Fixed)
  console.log('\n🔹 TESTING AGE-BANDED RATE STRUCTURE (FIXED)');
  for (let i = 0; i < 3; i++) {
    const planAssignment = createTestPlanAssignment('Age-Banded', 'fixed');
    const scenario = scenarios[i % scenarios.length];
    const passed = runSingleTest(`Age-Banded Fixed Test ${i + 1}`, planAssignment, scenario);
    testResults.push(passed);
    testCount++;
  }

  // 3. Age-Banded Rate Structure (Multiplier)
  console.log('\n🔹 TESTING AGE-BANDED RATE STRUCTURE (MULTIPLIER)');
  for (let i = 0; i < 3; i++) {
    const planAssignment = createTestPlanAssignment('Age-Banded', 'multiplier');
    const scenario = scenarios[i % scenarios.length];
    const passed = runSingleTest(`Age-Banded Multiplier Test ${i + 1}`, planAssignment, scenario);
    testResults.push(passed);
    testCount++;
  }

  // 4. Four-Tier Rate Structure
  console.log('\n🔹 TESTING FOUR-TIER RATE STRUCTURE');
  for (let i = 0; i < 3; i++) {
    const planAssignment = createTestPlanAssignment('Four-Tier');
    const scenario = scenarios[i % scenarios.length];
    const passed = runSingleTest(`Four-Tier Test ${i + 1}`, planAssignment, scenario);
    testResults.push(passed);
    testCount++;
  }

  // 5. Age-Banded-Four-Tier Rate Structure (Fixed)
  console.log('\n🔹 TESTING AGE-BANDED-FOUR-TIER RATE STRUCTURE (FIXED)');
  for (let i = 0; i < 3; i++) {
    const planAssignment = createTestPlanAssignment('Age-Banded-Four-Tier', 'fixed');
    const scenario = scenarios[i % scenarios.length];
    const passed = runSingleTest(`Age-Banded-Four-Tier Fixed Test ${i + 1}`, planAssignment, scenario);
    testResults.push(passed);
    testCount++;
  }

  // 6. Age-Banded-Four-Tier Rate Structure (Multiplier)
  console.log('\n🔹 TESTING AGE-BANDED-FOUR-TIER RATE STRUCTURE (MULTIPLIER)');
  for (let i = 0; i < 3; i++) {
    const planAssignment = createTestPlanAssignment('Age-Banded-Four-Tier', 'multiplier');
    const scenario = scenarios[i % scenarios.length];
    const passed = runSingleTest(`Age-Banded-Four-Tier Multiplier Test ${i + 1}`, planAssignment, scenario);
    testResults.push(passed);
    testCount++;
  }

  // 7. Salary-Based Rate Structure (Fixed)
  console.log('\n🔹 TESTING SALARY-BASED RATE STRUCTURE (FIXED)');
  for (let i = 0; i < 3; i++) {
    const planAssignment = createTestPlanAssignment('Salary-Based', 'fixed', 'fixed');
    const scenario = scenarios[i % scenarios.length];
    const passed = runSingleTest(`Salary-Based Fixed Test ${i + 1}`, planAssignment, scenario);
    testResults.push(passed);
    testCount++;
  }

  // 8. Salary-Based Rate Structure (Multiplier)
  console.log('\n🔹 TESTING SALARY-BASED RATE STRUCTURE (MULTIPLIER)');
  for (let i = 0; i < 3; i++) {
    const planAssignment = createTestPlanAssignment('Salary-Based', 'fixed', 'multiplier');
    const scenario = scenarios[i % scenarios.length];
    const passed = runSingleTest(`Salary-Based Multiplier Test ${i + 1}`, planAssignment, scenario);
    testResults.push(passed);
    testCount++;
  }

  // Summary
  const passedTests = testResults.filter(result => result).length;
  const failedTests = testCount - passedTests;

  console.log('\n📊 COMPREHENSIVE TEST SUMMARY');
  console.log('==============================');
  console.log(`Total Tests: ${testCount}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  console.log(`Success Rate: ${Math.round((passedTests / testCount) * 100)}%`);

  if (failedTests === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Rate structure calculations are working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
  }

  return failedTests === 0;
}

// Export for use in tests
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    ComprehensiveCostCalculationService,
    generateRandomCostSplit,
    generateRandomTiers,
    generateRandomAgeBands,
    generateRandomSalaryBands,
    createTestPlanAssignment,
    testAllRateStructures
  };
}

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  testAllRateStructures();
}
