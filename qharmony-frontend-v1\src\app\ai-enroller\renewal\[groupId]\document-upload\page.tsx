'use client';

import React, { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { 
  HiOutlineArrowLeft,
  HiOutlineUpload,
  HiOutlineDocumentText,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import '../../renewal.css';
import '../plan-detail.css';
import './document-upload.css';

interface DocumentSection {
  id: string;
  title: string;
  description: string;
  required: boolean;
  files: File[];
  placeholder: string;
}

const DocumentUploadPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(4);

  const groupName = 'TechCorp Solutions';

  const [documentSections, setDocumentSections] = useState<DocumentSection[]>([
    {
      id: 'sbc',
      title: 'Summary of Benefits and Coverage (SBC)',
      description: 'Upload updated plan documents for TechCorp Solutions. These documents will be tagged with the new plan year and made available to HR and employees.',
      required: true,
      files: [],
      placeholder: 'No summary of benefits and coverage (sbc) uploaded yet'
    },
    {
      id: 'plan-docs',
      title: 'Plan Documents',
      description: '',
      required: false,
      files: [],
      placeholder: 'No plan documents uploaded yet'
    },
    {
      id: 'rate-sheets',
      title: 'Rate Sheets',
      description: '',
      required: false,
      files: [],
      placeholder: 'No rate sheets uploaded yet'
    },
    {
      id: 'carrier-contracts',
      title: 'Carrier Contracts',
      description: '',
      required: false,
      files: [],
      placeholder: 'No carrier contracts uploaded yet'
    },
    {
      id: 'compliance-docs',
      title: 'Compliance Documents',
      description: '',
      required: false,
      files: [],
      placeholder: 'No compliance documents uploaded yet'
    }
  ]);

  const steps = [
    { number: 1, title: 'Review Current Plans', subtitle: 'View existing benefit plans', active: false, completed: true },
    { number: 2, title: 'Renewal Options', subtitle: 'Choose renewal type', active: false, completed: true },
    { number: 3, title: 'Plan Configuration', subtitle: 'Set dates and modifications', active: false, completed: true },
    { number: 4, title: 'Document Upload', subtitle: 'Upload plan documents', active: currentStep === 4 },
    { number: 5, title: 'Validation', subtitle: 'Review and validate setup', active: false },
    { number: 6, title: 'Finalize', subtitle: 'Complete renewal process', active: false },
    { number: 7, title: 'Export', subtitle: 'Download and share data', active: false }
  ];

  const handleFileUpload = (sectionId: string, files: FileList | null) => {
    if (!files) return;
    
    setDocumentSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, files: [...section.files, ...Array.from(files)] }
          : section
      )
    );
  };

  const handleContinue = () => {
    router.push(`/ai-enroller/renewal/${params.groupId}/validation`);
  };

  const handleSkipUpload = () => {
    router.push(`/ai-enroller/renewal/${params.groupId}/validation`);
  };

  const handlePrevious = () => {
    router.back();
  };

  return (
    <div className="plan-renewal-detail">
      {/* Header */}
      <div className="detail-header">
        <button 
          className="back-btn"
          onClick={() => router.push('/ai-enroller/renewal')}
        >
          <HiOutlineArrowLeft size={20} />
          Back to Dashboard
        </button>
        
        <div className="header-info">
          <h1>Plan Renewal</h1>
          <h2>{groupName}</h2>
          <div className="step-indicator">Step {currentStep} of 7</div>
        </div>

        <div className="completion-status">
          57% Complete
        </div>
      </div>

      {/* Progress Steps */}
      <div className="renewal-steps">
        {steps.map((step, index) => (
          <div key={step.number} className={`renewal-step ${step.active ? 'active' : ''} ${step.completed ? 'completed' : ''}`}>
            <div className="step-number">
              {step.completed ? '✓' : step.number}
            </div>
            <div className="step-content">
              <div className="step-title">{step.title}</div>
              <div className="step-subtitle">{step.subtitle}</div>
            </div>
            {index < steps.length - 1 && <div className="step-connector"></div>}
          </div>
        ))}
      </div>

      {/* Document Upload */}
      <div className="document-upload-section">
        <div className="upload-header">
          <div className="upload-title">
            <HiOutlineUpload size={20} />
            <h3>Plan Document Upload</h3>
          </div>
          <p>Upload updated plan documents for {groupName}. These documents will be tagged with the new plan year and made available to HR and employees.</p>
        </div>

        <div className="upload-content">
          {documentSections.map((section) => (
            <div key={section.id} className="upload-card">
              <div className="card-header">
                <div className="header-content">
                  <h4>
                    {section.title}
                    {section.required && <span className="required-badge">Required</span>}
                  </h4>
                  {section.description && <p>{section.description}</p>}
                </div>
                
                <label className="upload-btn">
                  <HiOutlineUpload size={16} />
                  Upload Files
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.xls,.xlsx"
                    onChange={(e) => handleFileUpload(section.id, e.target.files)}
                    style={{ display: 'none' }}
                  />
                </label>
              </div>

              <div className="upload-area">
                {section.files.length > 0 ? (
                  <div className="files-list">
                    {section.files.map((file, index) => (
                      <div key={index} className="file-item">
                        <HiOutlineDocumentText size={16} />
                        <span>{file.name}</span>
                        <span className="file-size">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    <HiOutlineDocumentText size={24} />
                    <span>{section.placeholder}</span>
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Document Guidelines */}
          <div className="guidelines-card">
            <div className="card-header">
              <HiOutlineInformationCircle size={20} />
              <h4>Document Guidelines</h4>
            </div>
            
            <div className="guidelines-content">
              <ul>
                <li>Accepted formats: PDF, DOC, DOCX, XLS, XLSX</li>
                <li>Maximum file size: 10MB per file</li>
                <li>Documents will be automatically tagged with the plan year</li>
                <li>SBC documents are required for compliance</li>
                <li>All uploads will be available to HR admins immediately</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="navigation-section">
          <button 
            className="nav-btn secondary"
            onClick={handlePrevious}
          >
            <HiOutlineArrowLeft size={16} />
            Previous
          </button>
          
          <div className="nav-actions">
            <button 
              className="nav-btn secondary"
              onClick={handleSkipUpload}
            >
              Skip Upload
            </button>
            
            <button 
              className="nav-btn primary enabled"
              onClick={handleContinue}
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUploadPage;
