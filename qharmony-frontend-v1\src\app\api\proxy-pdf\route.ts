import { NextResponse } from "next/server";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url); // Get query parameters
  const pdfUrl = searchParams.get("pdfUrl");

  if (!pdfUrl) {
    return NextResponse.json({ error: "Missing pdfUrl parameter" }, { status: 400 });
  }

  try {
    const response = await fetch(pdfUrl);
    if (!response.ok) {
      console.log("Fetch error, status code:", response.status);
      return NextResponse.json({ error: `Failed to fetch PDF: ${response.status}` }, { status: 500 });
    }
    const blob = await response.blob();

    // Return the PDF file as the response
    return new NextResponse(blob, {
      status: 200,
      headers: { "Content-Type": "application/pdf" },
    });
  } catch (error) {
    console.error("Fetch failed: ", error);
    return NextResponse.json({ error: "Failed to fetch PDF file" }, { status: 500 });
  }
}
