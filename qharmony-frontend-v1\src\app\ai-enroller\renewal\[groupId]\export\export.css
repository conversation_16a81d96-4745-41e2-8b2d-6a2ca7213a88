/* Export Styles */
.export-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
  overflow: hidden;
}

.export-header {
  padding: 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.export-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.export-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.export-header p {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Export Content */
.export-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Success Message */
.success-message {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #f0fdf4;
  border: 1px solid #22c55e;
  border-radius: 0.75rem;
  color: #166534;
}

.success-message h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.success-message p {
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.5;
}

/* Export Options */
.export-options {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.export-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.export-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.export-card-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.export-icon {
  background: #3b82f6;
  color: white;
  padding: 0.75rem;
  border-radius: 0.5rem;
  flex-shrink: 0;
}

.export-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.export-info p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.export-details {
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.export-details .filename,
.export-details .best-for {
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 0.5rem;
}

.export-details .filename:last-child,
.export-details .best-for:last-child {
  margin-bottom: 0;
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #1e293b;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.download-btn:hover {
  background: #0f172a;
  transform: translateY(-1px);
}

/* Quick Share */
.quick-share h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.quick-share p {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.share-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.share-btn:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
  transform: translateY(-2px);
}

.share-desc {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Process Summary */
.process-summary {
  background: #dbeafe;
  border: 1px solid #3b82f6;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.process-summary h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 1rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.summary-item {
  font-size: 0.875rem;
  color: #1e40af;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.375rem;
}

/* Navigation */
.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-top: 1px solid #f1f5f9;
  background: #f8fafc;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.nav-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.nav-btn.secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.nav-btn.primary {
  background: #3b82f6;
  color: white;
}

.nav-btn.primary.enabled:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Step Progress Updates */
.renewal-step.completed .step-number {
  background: #059669;
  color: white;
}

.renewal-step.completed .step-title {
  color: #059669;
}

/* Responsive Design */
@media (max-width: 768px) {
  .export-content {
    padding: 1rem;
  }
  
  .export-card-header {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }
  
  .share-buttons {
    grid-template-columns: 1fr;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .navigation-section {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-btn {
    width: 100%;
    justify-content: center;
  }
  
  .success-message {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
