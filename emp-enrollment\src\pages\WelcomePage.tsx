
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, Sparkles } from 'lucide-react';
import { PlanQADialog } from '@/components/PlanQADialog';
import { VideoPlayer } from '@/components/VideoPlayer';
import { PlanComparison } from '@/components/PlanComparison';
import { BotQuestion } from '@/components/BotQuestion';
import { EmployeeTestimonials } from '@/components/EmployeeTestimonials';
import { DidYouKnowFacts } from '@/components/DidYouKnowFacts';

interface WelcomePageProps {
  onNext: () => void;
}

export const WelcomePage = ({ onNext }: WelcomePageProps) => {
  return (
    <div className="max-w-3xl mx-auto space-y-6">
      {/* Main heading with proper h1 */}
      <div className="text-center space-y-2 mb-8">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          2025 Benefits Enrollment
        </h1>
        <p className="text-lg text-muted-foreground">Your AI assistant is here to help you choose the perfect benefits</p>
      </div>

      <BotQuestion 
        question="👋 Ready to find the perfect benefits for 2025?"
        context="I'll ask smart questions and recommend the best plans for your situation."
      />
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent" />
            <h2 className="text-xl">Quick Enrollment Overview</h2>
            <Sparkles className="w-5 h-5 text-yellow-500" />
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-red-50 dark:bg-red-950 p-4 rounded-lg border border-red-200 dark:border-red-800">
            <p className="font-semibold"><strong>⏰ Deadline:</strong> December 15, 2024</p>
          </div>
          
          <div className="space-y-3">
            <h3 className="font-medium">🧠 How I'll help you:</h3>
            <ul className="list-disc list-inside space-y-2 ml-4 text-sm">
              <li>Understand your healthcare needs and budget</li>
              <li>Recommend optimal plan combinations</li>
              <li>Show real cost examples and savings</li>
              <li>Answer questions throughout the process</li>
            </ul>
          </div>
          
          <Button 
            onClick={onNext}
            className="w-full mt-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
            size="lg"
          >
            🚀 Start Smart Enrollment
          </Button>
        </CardContent>
      </Card>

      {/* Employee Testimonials */}
      <EmployeeTestimonials />

      {/* Did You Know Facts */}
      <DidYouKnowFacts />

      {/* Resources */}
      <Card>
        <CardHeader>
          <CardTitle>
            <h2 className="text-xl">📚 Helpful Resources</h2>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <PlanQADialog selectedPlans={{}} />
            <VideoPlayer 
              title="Benefits Overview" 
              description="Learn about all your benefit options"
              planType="medical"
            />
            <PlanComparison />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
