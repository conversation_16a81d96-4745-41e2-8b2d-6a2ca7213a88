'use client';

import React, { useState } from 'react';
import { HelpCircle, X, Send } from 'lucide-react';

interface FloatingHelpProps {
  onClose?: () => void;
}

interface ChatMessage {
  question: string;
  answer: string;
  isCustom: boolean;
}

export const FloatingHelp = ({ onClose }: FloatingHelpProps) => {
  // If onClose is provided, it means this is being used as a modal, so start open
  // If no onClose, it's a standalone floating button, so start closed
  const [isOpen, setIsOpen] = useState(!!onClose);


  const [customQuestion, setCustomQuestion] = useState('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  const handleClose = () => {
    setIsOpen(false);
    if (onClose) {
      onClose();
    }
  };

  const commonQuestions = [
    {
      question: "What happens if I don't enroll...",
      fullQuestion: "What happens if I don't enroll in benefits?",
      answer: "If you don't enroll during open enrollment, you'll have no medical, dental, or vision coverage for 2025. You can only enroll outside of open enrollment if you have a qualifying life event."
    },
    {
      question: "Can I change my plan after enr...",
      fullQuestion: "Can I change my plan after enrollment?",
      answer: "You can only change your plan during the next open enrollment period or if you have a qualifying life event (marriage, birth of child, job loss, etc.)."
    },
    {
      question: "What's the difference between ...",
      fullQuestion: "What's the difference between HMO and PPO?",
      answer: "PPO plans offer more flexibility to see any doctor without referrals but cost more. HMO plans require you to choose a primary care physician and get referrals for specialists, but have lower costs."
    },
    {
      question: "How much will I pay per payche...",
      fullQuestion: "How much will I pay per paycheck?",
      answer: "Your paycheck deduction depends on the plan you choose and your family size. The costs shown during enrollment are per month - they'll be divided across your paychecks."
    },
    {
      question: "Are my current doctors covered...",
      fullQuestion: "Are my current doctors covered?",
      answer: "This depends on the plan's network. Check the provider directory for each plan to see if your doctors are covered. PPO plans typically have larger networks than HMO plans."
    }
  ];

  const handleQuestionClick = (questionData: any) => {
    const newMessage: ChatMessage = {
      question: questionData.fullQuestion,
      answer: questionData.answer,
      isCustom: false
    };
    setChatMessages(prev => [...prev, newMessage]);
  };

  const handleCustomQuestionSubmit = () => {
    if (customQuestion.trim()) {
      const newMessage: ChatMessage = {
        question: customQuestion,
        answer: "I'd be happy to help! For specific questions about plan details, costs, or coverage, please contact HR or the insurance carrier directly. You can also check the plan documents for detailed information.",
        isCustom: true
      };
      setChatMessages(prev => [...prev, newMessage]);
      setCustomQuestion('');
    }
  };

  if (!isOpen) {
    return (
      <div style={{
        position: 'fixed',
        bottom: '24px',
        right: '24px',
        zIndex: 50
      }}>
        <button
          onClick={() => setIsOpen(true)}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '12px 20px',
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '50px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
            transition: 'all 0.2s ease',
            fontFamily: 'sans-serif'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = '#1d4ed8';
            e.currentTarget.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.2)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = '#2563eb';
            e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
          }}
        >
          <HelpCircle size={20} />
          Need Help?
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Backdrop */}
      <div
        style={{
          position: 'fixed',
          inset: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 50
        }}
        onClick={handleClose}
      />

      {/* Modal */}
      <div style={{
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '95vw',
        maxWidth: '800px',
        maxHeight: '85vh',
        backgroundColor: 'white',
        borderRadius: '16px',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
        zIndex: 51,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '16px 20px',
          borderBottom: '1px solid #e5e7eb',
          backgroundColor: '#f8fafc'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div style={{
              width: '24px',
              height: '24px',
              backgroundColor: '#3b82f6',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <HelpCircle size={14} style={{ color: 'white' }} />
            </div>
            <h2 style={{
              fontSize: '16px',
              fontWeight: '600',
              color: '#111827',
              margin: 0,
              fontFamily: 'sans-serif'
            }}>
              Benefits Q&A Assistant
            </h2>
          </div>
          <button
            onClick={handleClose}
            style={{
              padding: '4px',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            <X size={16} />
          </button>
        </div>

        {/* Content */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          {/* Common Questions Section - Always visible at top */}
          <div style={{
            padding: '16px 20px',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <h3 style={{
              fontSize: '14px',
              fontWeight: '600',
              color: '#111827',
              margin: '0 0 12px 0',
              fontFamily: 'sans-serif'
            }}>
              Common Questions:
            </h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gridTemplateRows: 'repeat(2, 1fr)',
              gap: '8px'
            }}>
              {commonQuestions.map((item, index) => (
                <button
                  key={index}
                  onClick={() => handleQuestionClick(item)}
                  style={{
                    padding: '8px 12px',
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '6px',
                    fontSize: '13px',
                    color: '#475569',
                    textAlign: 'left',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    fontFamily: 'sans-serif',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                    e.currentTarget.style.borderColor = '#cbd5e1';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.borderColor = '#e2e8f0';
                  }}
                >
                  {item.question}
                </button>
              ))}
            </div>
          </div>

          {/* Chat Messages Area */}
          <div style={{
            flex: 1,
            minHeight: '300px',
            padding: '20px',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            backgroundColor: '#fafbfc'
          }}>
            {chatMessages.length === 0 ? (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                color: '#94a3b8',
                fontSize: '14px',
                fontFamily: 'sans-serif'
              }}>
                Click a question above or type your own question below
              </div>
            ) : (
              chatMessages.map((message, index) => (
                <div key={index} style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  {/* Question */}
                  <div style={{
                    padding: '12px 16px',
                    backgroundColor: '#3b82f6',
                    borderRadius: '16px',
                    alignSelf: 'flex-end',
                    maxWidth: '80%'
                  }}>
                    <p style={{
                      color: 'white',
                      fontSize: '14px',
                      margin: 0,
                      fontFamily: 'sans-serif'
                    }}>
                      {message.question}
                    </p>
                  </div>

                  {/* Answer */}
                  <div style={{
                    padding: '16px',
                    backgroundColor: '#f1f5f9',
                    borderRadius: '16px',
                    alignSelf: 'flex-start',
                    maxWidth: '90%'
                  }}>
                    <p style={{
                      color: '#334155',
                      fontSize: '14px',
                      margin: 0,
                      lineHeight: '1.5',
                      fontFamily: 'sans-serif'
                    }}>
                      {message.answer}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Chat Input Area - Always at bottom */}
          <div style={{
            padding: '16px 20px',
            backgroundColor: '#f8fafc',
            borderTop: '1px solid #e5e7eb'
          }}>
            <div style={{
              display: 'flex',
              gap: '8px',
              alignItems: 'flex-end'
            }}>
              <input
                type="text"
                value={customQuestion}
                onChange={(e) => setCustomQuestion(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleCustomQuestionSubmit();
                  }
                }}
                placeholder="Ask a question about your benefits..."
                style={{
                  flex: 1,
                  padding: '12px 16px',
                  border: '1px solid #d1d5db',
                  borderRadius: '24px',
                  fontSize: '14px',
                  outline: 'none',
                  fontFamily: 'sans-serif',
                  backgroundColor: 'white',
                  color: '#111827'
                }}
              />
              <button
                onClick={handleCustomQuestionSubmit}
                disabled={!customQuestion.trim()}
                style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: customQuestion.trim() ? '#1e293b' : '#94a3b8',
                  border: 'none',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: customQuestion.trim() ? 'pointer' : 'not-allowed',
                  transition: 'all 0.2s ease'
                }}
              >
                <Send size={16} style={{ color: 'white' }} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
