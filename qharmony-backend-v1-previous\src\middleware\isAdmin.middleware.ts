import { Request, Response, NextFunction } from 'express';
import CompanyModelClass from '../nosql/company.model';
import UserModelClass from '../nosql/user.model';

const adminAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const userId = req.headers['user-id'] as string;
    const { companyId } = req.body as { companyId: string };

    if (!companyId) {
      res.status(400).json({ error: 'Company ID is required' });
      return;
    }

    const company = await CompanyModelClass.getDataById(companyId);
    const user = await UserModelClass.getDataById(userId);

    if (!company) {
      res.status(404).json({ error: 'Company not found' });
      return;
    }
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    if (company.adminEmail !== user.email) {
      res.status(401).json({
        error: 'User is not authorized to perform this action for this company',
      });
      return;
    }

    next();
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
    return;
  }
};

export default adminAuthMiddleware;
