'use client';

import { useEffect, useState } from 'react';

export default function DebugPage() {
  const [localStorageData, setLocalStorageData] = useState<Record<string, string>>({});

  useEffect(() => {
    // Get all localStorage data
    const data: Record<string, string> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        data[key] = localStorage.getItem(key) || '';
      }
    }
    setLocalStorageData(data);
  }, []);

  const clearUserData = () => {
    localStorage.removeItem('userid1');
    localStorage.removeItem('userId');
    localStorage.removeItem('user');
    
    // Set the correct user ID
    localStorage.setItem('userid1', '6838677aef6db0212bcfdacd');
    
    // Refresh the data
    const data: Record<string, string> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        data[key] = localStorage.getItem(key) || '';
      }
    }
    setLocalStorageData(data);
    
    alert('User data cleared and reset to correct ID');
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Debug - LocalStorage Data</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={clearUserData}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Clear & Reset User Data
        </button>
      </div>

      <h2>Current LocalStorage Contents:</h2>
      <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
        {JSON.stringify(localStorageData, null, 2)}
      </pre>

      <h2>Expected User ID:</h2>
      <p>Should be: <strong>6838677aef6db0212bcfdacd</strong></p>
      
      <h2>Current User ID from getUserId():</h2>
      <p>
        userid1: <strong>{localStorage.getItem('userid1') || 'not set'}</strong><br/>
        userId: <strong>{localStorage.getItem('userId') || 'not set'}</strong>
      </p>
    </div>
  );
}
