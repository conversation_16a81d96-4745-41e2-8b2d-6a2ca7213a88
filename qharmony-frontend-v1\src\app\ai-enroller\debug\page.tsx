'use client';

import { useEffect, useState } from 'react';

export default function DebugPage() {
  const [localStorageData, setLocalStorageData] = useState<Record<string, string>>({});

  useEffect(() => {
    // Get all localStorage data
    const data: Record<string, string> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        data[key] = localStorage.getItem(key) || '';
      }
    }
    setLocalStorageData(data);
  }, []);

  const clearUserData = () => {
    localStorage.removeItem('userid1');
    localStorage.removeItem('userId');
    localStorage.removeItem('user');

    // Set the ACTUAL user ID that exists in the database
    localStorage.setItem('userid1', '67bf65bf50bad0a4b3d805bc');
    localStorage.setItem('companyId1', '67bf65bf50bad0a4b3d805ba');

    // Refresh the data
    const data: Record<string, string> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        data[key] = localStorage.getItem(key) || '';
      }
    }
    setLocalStorageData(data);

    alert('User data cleared and reset to DATABASE user ID: 67bf65bf50bad0a4b3d805bc');
  };

  const testDirectAPI = async () => {
    const userId = localStorage.getItem('userid1');
    const companyId = localStorage.getItem('companyId1');

    if (!userId || !companyId) {
      alert('Missing userId or companyId in localStorage');
      return;
    }

    try {
      // Test direct API call
      const response = await fetch(`http://localhost:8080/api/pre-enrollment/plan-assignments/company/${companyId}?includePlanData=true`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      const result = await response.json();

      console.log('🔍 Direct API Test Result:', {
        status: response.status,
        ok: response.ok,
        result
      });

      alert(`API Test Result:\nStatus: ${response.status}\nAssignments: ${result.assignments?.length || 0}\nTotal Count: ${result.totalCount || 0}\nApplied Filters: ${JSON.stringify(result.appliedFilters)}`);
    } catch (error) {
      console.error('API Test Error:', error);
      alert(`API Test Error: ${error.message}`);
    }
  };

  const testUserAPI = async () => {
    const userId = localStorage.getItem('userid1');

    if (!userId) {
      alert('Missing userId in localStorage');
      return;
    }

    try {
      // Test user API to see what user data the backend has
      const response = await fetch(`http://localhost:8080/api/users/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      const result = await response.json();

      console.log('🔍 User API Test Result:', {
        status: response.status,
        ok: response.ok,
        result
      });

      if (response.ok) {
        alert(`User API Result:\nName: ${result.name}\nEmail: ${result.email}\nCompany ID: ${result.companyId}\nisBroker: ${result.isBroker}\nisAdmin: ${result.isAdmin}`);
      } else {
        alert(`User API Error:\nStatus: ${response.status}\nError: ${JSON.stringify(result)}`);
      }
    } catch (error) {
      console.error('User API Test Error:', error);
      alert(`User API Test Error: ${error.message}`);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Debug - LocalStorage Data & API Test</h1>

      <div style={{ marginBottom: '20px', display: 'flex', gap: '10px' }}>
        <button
          onClick={clearUserData}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Clear & Reset User Data
        </button>

        <button
          onClick={testDirectAPI}
          style={{
            padding: '10px 20px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Test Direct API Call
        </button>

        <button
          onClick={testUserAPI}
          style={{
            padding: '10px 20px',
            backgroundColor: '#ffc107',
            color: 'black',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Test User API
        </button>
      </div>

      <h2>Current LocalStorage Contents:</h2>
      <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
        {JSON.stringify(localStorageData, null, 2)}
      </pre>

      <h2>Expected User ID:</h2>
      <p>Should be: <strong>6838677aef6db0212bcfdacd</strong></p>

      <h2>Current User ID from getUserId():</h2>
      <p>
        userid1: <strong>{localStorage.getItem('userid1') || 'not set'}</strong><br/>
        userId: <strong>{localStorage.getItem('userId') || 'not set'}</strong><br/>
        companyId1: <strong>{localStorage.getItem('companyId1') || 'not set'}</strong>
      </p>
    </div>
  );
}
