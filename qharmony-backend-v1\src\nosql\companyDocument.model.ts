import mongoose, { Document, Model } from 'mongoose';

const { Schema } = mongoose;

export interface CompanyDocumentDataInterface {
  slackTeamId: string;
  employeeHandbookUrl: string;
  trainingLink: string;
  performanceReviewLink: string;
  immigrationLink: string;
  medicalClaimLink: string;
  expenseReportingLink: string;
  addDependentLink: string;
  shortTermDisabilityLink: string;
  longTermDisabilityLink: string;
  maternityLeaveLink: string;
}

interface CompanyDocumentDocument extends Document {
  data: CompanyDocumentDataInterface[];
}

class CompanyDocumentModelClass {
  private static companyDocumentModel: Model<CompanyDocumentDocument>;

  public static initializeModel() {
    const schema = new Schema({
      slackTeamId: String,
      employeeHandbookUrl: String,
      trainingLink: String,
      performanceReviewLink: String,
      immigrationLink: String,
      medicalClaimLink: String,
      expenseReportingLink: String,
      addDependentLink: String,
      shortTermDisabilityLink: String,
      longTermDisabilityLink: String,
      maternityLeaveLink: String,
    });

    this.companyDocumentModel = mongoose.model<CompanyDocumentDocument>(
      'CompanyDocument',
      schema
    );
  }

  public static async addData(
    data: CompanyDocumentDataInterface
  ): Promise<void> {
    try {
      // Extract the data. If slackTeamId exists, update the data. Otherwise, insert the data.
      const { slackTeamId } = data;
      const existingData = await this.companyDocumentModel.findOne({
        slackTeamId,
      });
      if (existingData) {
        await this.companyDocumentModel.updateOne({ slackTeamId }, data);
      } else {
        await this.companyDocumentModel.create(data);
      }
    } catch (error) {
      console.error(error);
    }
  }

  public static async getDataBySlackTeamId(
    slackTeamId: string
  ): Promise<CompanyDocumentDataInterface | null> {
    try {
      const result = (await this.companyDocumentModel.findOne({
        slackTeamId,
      })) as unknown as CompanyDocumentDataInterface;
      return result;
    } catch (error) {
      console.error(error);
      return null;
    }
  }
}

CompanyDocumentModelClass.initializeModel();

export default CompanyDocumentModelClass;
