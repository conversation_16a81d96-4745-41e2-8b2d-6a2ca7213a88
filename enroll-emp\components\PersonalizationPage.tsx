import React, { useState } from 'react';
import { BotQuestion } from './BotQuestion';
import { UserProfile } from '../page';

interface PersonalizationPageProps {
  onComplete: (profile: Partial<UserProfile>) => void;
}

export const PersonalizationPage = ({ onComplete }: PersonalizationPageProps) => {
  const [profile, setProfile] = useState<Partial<UserProfile>>({});

  const handleSubmit = () => {
    onComplete(profile);
  };

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <BotQuestion 
        question="Let me ask a few quick questions to personalize your recommendations!"
        context="This helps me suggest the best plans for your specific situation."
      />
      
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Tell me about yourself</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Family situation:</label>
            <select 
              className="w-full p-3 border rounded-lg"
              onChange={(e) => setProfile(prev => ({ ...prev, familyMembers: e.target.value }))}
            >
              <option value="">Select...</option>
              <option value="single">Just me</option>
              <option value="couple">Me + spouse/partner</option>
              <option value="family">Me + family</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Do you wear glasses or contacts?</label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <input 
                  type="radio" 
                  name="glasses" 
                  value="yes"
                  onChange={() => setProfile(prev => ({ ...prev, wearGlasses: true }))}
                  className="mr-2"
                />
                Yes
              </label>
              <label className="flex items-center">
                <input 
                  type="radio" 
                  name="glasses" 
                  value="no"
                  onChange={() => setProfile(prev => ({ ...prev, wearGlasses: false }))}
                  className="mr-2"
                />
                No
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Expected medical usage:</label>
            <select 
              className="w-full p-3 border rounded-lg"
              onChange={(e) => setProfile(prev => ({ ...prev, expectedMedicalUsage: e.target.value }))}
            >
              <option value="">Select...</option>
              <option value="low">Low - Just preventive care</option>
              <option value="moderate">Moderate - Occasional visits</option>
              <option value="high">High - Regular ongoing care</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Budget preference:</label>
            <select 
              className="w-full p-3 border rounded-lg"
              onChange={(e) => setProfile(prev => ({ ...prev, budgetPreference: e.target.value }))}
            >
              <option value="">Select...</option>
              <option value="low-premium">Lower monthly cost</option>
              <option value="balanced">Balanced cost and coverage</option>
              <option value="comprehensive">More coverage, higher cost</option>
            </select>
          </div>

          <div>
            <label className="flex items-center">
              <input 
                type="checkbox"
                onChange={(e) => setProfile(prev => ({ ...prev, hasPreferredDoctors: e.target.checked }))}
                className="mr-2"
              />
              I have preferred doctors I want to keep
            </label>
          </div>
        </div>

        <button
          onClick={handleSubmit}
          className="w-full mt-6 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
        >
          Get My Recommendations
        </button>
      </div>
    </div>
  );
};
