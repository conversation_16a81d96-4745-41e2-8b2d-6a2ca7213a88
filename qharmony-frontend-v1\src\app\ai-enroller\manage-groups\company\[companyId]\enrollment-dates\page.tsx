'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { HiOutlineCalendar, HiOutlineCheckCircle, HiOutlineArrowLeft } from 'react-icons/hi';

interface Plan {
  _id: string;
  planName: string;
  planCode: string;
  carrier: string;
  type: 'Medical' | 'Dental' | 'Vision' | 'Ancillary';
  metalTier?: string;
  period: string;
  policyNumber: string;
  status: 'active' | 'inactive';
}

interface Company {
  _id: string;
  companyName: string;
}

export default function EnrollmentDatesPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const companyId = params.companyId as string;
  const selectedPlanIds = searchParams.get('plans')?.split(',') || [];
  
  const [company, setCompany] = useState<Company | null>(null);
  const [selectedPlans, setSelectedPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [enrollmentStartDate, setEnrollmentStartDate] = useState('');
  const [enrollmentEndDate, setEnrollmentEndDate] = useState('');
  const [planStartDate, setPlanStartDate] = useState('');
  const [planEndDate, setPlanEndDate] = useState('');

  const fetchCompanyAndPlans = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockCompany: Company = {
        _id: companyId,
        companyName: 'TechCorp Inc.'
      };

      const mockPlans: Plan[] = [
        {
          _id: '1',
          planName: 'Blue Cross Blue Shield PPO',
          planCode: 'BCBS-PPO-2024',
          carrier: 'Blue Cross Blue Shield',
          type: 'Medical',
          metalTier: 'PPO',
          period: '2024-01-01 - 2024-12-31',
          policyNumber: 'POL-*********',
          status: 'active'
        },
        {
          _id: '2',
          planName: 'Aetna Better Health HMO',
          planCode: 'AETNA-HMO-2024',
          carrier: 'Aetna',
          type: 'Medical',
          metalTier: 'HMO',
          period: '2024-01-01 - 2024-12-31',
          policyNumber: 'POL-*********',
          status: 'active'
        },
        {
          _id: '3',
          planName: 'Delta Dental PPO',
          planCode: 'DD-PPO-2024',
          carrier: 'Delta Dental',
          type: 'Dental',
          period: '2024-01-01 - 2024-12-31',
          policyNumber: 'POL-*********',
          status: 'active'
        },
        {
          _id: '4',
          planName: 'Guardian Dental HMO',
          planCode: 'GUARD-HMO-2024',
          carrier: 'Guardian',
          type: 'Dental',
          period: '2024-01-01 - 2024-12-31',
          policyNumber: 'POL-*********',
          status: 'active'
        },
        {
          _id: '5',
          planName: 'VSP Vision Care',
          planCode: 'VSP-2024',
          carrier: 'VSP',
          type: 'Vision',
          period: '2024-01-01 - 2024-12-31',
          policyNumber: 'POL-*********',
          status: 'active'
        }
      ];

      // Filter plans based on selected IDs
      const filteredPlans = mockPlans.filter(plan => selectedPlanIds.includes(plan._id));
      
      setCompany(mockCompany);
      setSelectedPlans(filteredPlans);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanyAndPlans();
  }, [companyId, fetchCompanyAndPlans]);

  const handleBack = () => {
    router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`);
  };

  const handleContinue = () => {
    if (!enrollmentStartDate || !enrollmentEndDate || !planStartDate || !planEndDate) {
      alert('Please fill in all date fields to continue.');
      return;
    }

    // Navigate to set dates page
    router.push(`/ai-enroller/manage-groups/company/${companyId}/set-dates?plans=${selectedPlanIds.join(',')}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading enrollment setup...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center space-x-2 text-sm text-gray-500">
            <button
              onClick={() => router.push('/ai-enroller')}
              className="hover:text-gray-700"
            >
              Home
            </button>
            <span>›</span>
            <button
              onClick={() => router.push('/ai-enroller/manage-groups/select-company')}
              className="hover:text-gray-700"
            >
              Select Company
            </button>
            <span>›</span>
            <button
              onClick={() => router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`)}
              className="hover:text-gray-700"
            >
              View Plans
            </button>
            <span>›</span>
            <span className="text-gray-900">Set Enrollment Dates</span>
          </nav>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={handleBack}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <HiOutlineArrowLeft className="w-5 h-5" />
              Back to Plans
            </button>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Set Enrollment Dates</h1>
            <p className="text-gray-600">Configure enrollment and plan effective dates for {company?.companyName}</p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Selected Plans Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-lg font-semibold text-blue-900 mb-4">Selected Plans ({selectedPlans.length})</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {selectedPlans.map((plan) => (
              <div key={plan._id} className="bg-white border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <HiOutlineCheckCircle className="w-5 h-5 text-green-600" />
                  <h3 className="font-semibold text-gray-900">{plan.planName}</h3>
                </div>
                <p className="text-sm text-gray-600">{plan.carrier}</p>
                <p className="text-xs text-gray-500">{plan.type} • {plan.planCode}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Date Configuration */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Enrollment Period */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <HiOutlineCalendar className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Enrollment Period</h2>
                <p className="text-sm text-gray-600">When employees can enroll in benefits</p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enrollment Start Date
                </label>
                <input
                  type="date"
                  value={enrollmentStartDate}
                  onChange={(e) => setEnrollmentStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white"
                  style={{ color: '#111827', backgroundColor: 'white' }}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enrollment End Date
                </label>
                <input
                  type="date"
                  value={enrollmentEndDate}
                  onChange={(e) => setEnrollmentEndDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white"
                  style={{ color: '#111827', backgroundColor: 'white' }}
                />
              </div>
            </div>
          </div>

          {/* Plan Effective Period */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <HiOutlineCalendar className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Plan Effective Period</h2>
                <p className="text-sm text-gray-600">When the benefits coverage is active</p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Plan Start Date
                </label>
                <input
                  type="date"
                  value={planStartDate}
                  onChange={(e) => setPlanStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white"
                  style={{ color: '#111827', backgroundColor: 'white' }}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Plan End Date
                </label>
                <input
                  type="date"
                  value={planEndDate}
                  onChange={(e) => setPlanEndDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white"
                  style={{ color: '#111827', backgroundColor: 'white' }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between mt-8">
          <button
            onClick={handleBack}
            className="flex items-center gap-2 px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <HiOutlineArrowLeft className="w-4 h-4" />
            Back to Plans
          </button>
          
          <button
            onClick={handleContinue}
            disabled={!enrollmentStartDate || !enrollmentEndDate || !planStartDate || !planEndDate}
            className={`px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 ${
              !enrollmentStartDate || !enrollmentEndDate || !planStartDate || !planEndDate
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-purple-600 text-white hover:bg-purple-700'
            }`}
          >
            Save & Continue
            <span>→</span>
          </button>
        </div>
      </div>
    </div>
  );
}
