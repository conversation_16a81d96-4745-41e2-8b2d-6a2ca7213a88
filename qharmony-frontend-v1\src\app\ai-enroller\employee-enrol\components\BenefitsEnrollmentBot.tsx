'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import {
  Bell,
  Heart,
  Smile,
  Eye,
  FileText,
  CheckCircle,
  Plus,
  User,
  ArrowLeft,
  ArrowRight,
  Sparkles
} from 'lucide-react';

// Import page components
import { WelcomePage } from '../pages/WelcomePage';
import { PersonalizationPage } from '../pages/PersonalizationPage';
import { MedicalPlanPage } from '../pages/MedicalPlanPage';

// Simple placeholder components

const MedicalPlanPlaceholder: React.FC<{userProfile: UserProfile, onPlanSelect: (planData: any) => void, recommendation: any}> = ({ onPlanSelect }) => (
  <div className="max-w-3xl mx-auto space-y-6">
    <div className="bg-white rounded-xl p-6 shadow-sm border text-center">
      <h2 className="text-xl font-semibold mb-4">Medical Plans Coming Soon</h2>
      <p className="text-gray-600 mb-4">Medical plan selection will be available soon.</p>
      <button
        onClick={() => onPlanSelect(null)}
        className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
      >
        Continue
      </button>
    </div>
  </div>
);

export interface UserProfile {
  age?: number;
  familyStatus?: 'single' | 'married' | 'family';
  dependents?: number;
  healthStatus?: 'excellent' | 'good' | 'fair' | 'poor';
  budgetPreference?: 'low' | 'medium' | 'high';
  riskTolerance?: 'low' | 'medium' | 'high';
  needsDentalCare?: boolean;
  wearGlasses?: boolean;
  hasChronicConditions?: boolean;
  prefersLowDeductible?: boolean;
  wantsPPO?: boolean;
}

export interface SelectedPlans {
  medical?: any;
  dental?: any;
  vision?: any;
  additional?: any[];
}

const steps = [
  { id: 'welcome', title: 'Welcome', icon: Bell, subtitle: 'Get Started' },
  { id: 'personalization', title: 'About You', icon: User, subtitle: 'Personal Info' },
  { id: 'medical', title: 'Medical', icon: Heart, subtitle: 'Health Plans' },
  { id: 'dental', title: 'Dental', icon: Smile, subtitle: 'Dental Care' },
  { id: 'vision', title: 'Vision', icon: Eye, subtitle: 'Eye Care' },
  { id: 'additional', title: 'Additional', icon: Plus, subtitle: 'Extra Benefits' },
  { id: 'summary', title: 'Summary', icon: FileText, subtitle: 'Review' },
  { id: 'confirmation', title: 'Complete', icon: CheckCircle, subtitle: 'Finished' }
];

export const BenefitsEnrollmentBot: React.FC = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [userProfile, setUserProfile] = useState<UserProfile>({});
  const [selectedPlans, setSelectedPlans] = useState<SelectedPlans>({});
  const [isLoading, setIsLoading] = useState(false);
  const [recommendation, setRecommendation] = useState<any>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = 0;
    }
  }, [currentStep]);

  const generateRecommendation = (profile: UserProfile) => {
    // Simple recommendation logic based on user profile
    let recommendedPlan = {
      name: 'Blue Shield PPO 500',
      cost: 125,
      features: [
        '$500 deductible',
        '90% coverage after deductible',
        'Large provider network',
        'No referrals needed'
      ]
    };

    let reason = 'Based on your preferences for flexibility and moderate costs.';

    if (profile.budgetPreference === 'low') {
      recommendedPlan = {
        name: 'Kaiser HMO Basic',
        cost: 85,
        features: [
          '$250 deductible',
          '80% coverage after deductible',
          'Integrated care model',
          'Lower monthly cost'
        ]
      };
      reason = 'This plan offers the best value for your budget-conscious approach.';
    } else if (profile.hasChronicConditions || profile.healthStatus === 'poor') {
      recommendedPlan = {
        name: 'Blue Shield PPO Premium',
        cost: 185,
        features: [
          '$250 deductible',
          '95% coverage after deductible',
          'Comprehensive coverage',
          'Specialist access'
        ]
      };
      reason = 'Given your health needs, this comprehensive plan provides the best coverage.';
    }

    return { plan: recommendedPlan, reason };
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePersonalizationComplete = (profile: Partial<UserProfile>) => {
    const updatedProfile = { ...userProfile, ...profile };
    setUserProfile(updatedProfile);
    const rec = generateRecommendation(updatedProfile);
    setRecommendation(rec);
    handleNext();
  };

  const handlePlanSelect = (planType: string, planData: any) => {
    setSelectedPlans(prev => ({
      ...prev,
      [planType]: planData
    }));
    handleNext();
  };

  const handleBackToMain = () => {
    router.push('/ai-enroller');
  };

  const getProgressPercentage = () => {
    return ((currentStep + 1) / steps.length) * 100;
  };

  const renderCurrentPage = () => {
    const stepId = steps[currentStep].id;

    switch (stepId) {
      case 'welcome':
        return <WelcomePage onNext={handleNext} />;
      case 'personalization':
        return <PersonalizationPage onComplete={handlePersonalizationComplete} />;
      case 'medical':
        return (
          <MedicalPlanPage
            onNext={handleNext}
            onBack={handleBack}
            onPlanSelect={(planData: any) => handlePlanSelect('medical', planData)}
          />
        );
      case 'dental':
        return (
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border text-center">
              <h2 className="text-xl font-semibold mb-4">Dental Plans Coming Soon</h2>
              <p className="text-gray-600 mb-4">Dental plan selection will be available soon.</p>
              <button
                onClick={() => handlePlanSelect('dental', null)}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Continue
              </button>
            </div>
          </div>
        );
      case 'vision':
        return (
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border text-center">
              <h2 className="text-xl font-semibold mb-4">Vision Plans Coming Soon</h2>
              <p className="text-gray-600 mb-4">Vision plan selection will be available soon.</p>
              <button
                onClick={() => handlePlanSelect('vision', null)}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Continue
              </button>
            </div>
          </div>
        );
      case 'additional':
        return (
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border text-center">
              <h2 className="text-xl font-semibold mb-4">Additional Benefits Coming Soon</h2>
              <p className="text-gray-600 mb-4">Additional benefits selection will be available soon.</p>
              <button
                onClick={() => handlePlanSelect('additional', [])}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Continue
              </button>
            </div>
          </div>
        );
      case 'summary':
        return (
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border text-center">
              <h2 className="text-xl font-semibold mb-4">Summary Coming Soon</h2>
              <p className="text-gray-600 mb-4">Plan summary will be available soon.</p>
              <button
                onClick={handleNext}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Complete Enrollment
              </button>
            </div>
          </div>
        );
      case 'confirmation':
        return (
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-8 border border-green-200 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-green-900 mb-2">Enrollment Complete!</h2>
              <p className="text-green-800 mb-4">Your benefits enrollment has been submitted successfully.</p>
              <button
                onClick={handleBackToMain}
                className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700"
              >
                Return to AI Enroller
              </button>
            </div>
          </div>
        );
      default:
        return <WelcomePage onNext={handleNext} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button
              onClick={handleBackToMain}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft size={16} />
              Back to AI Enroller
            </button>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <User className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h1 className="text-lg font-semibold flex items-center gap-2">
                AI Benefits Assistant
                <Sparkles className="w-4 h-4 text-yellow-500" />
              </h1>
              <p className="text-xs text-gray-500">2025 Medical, Dental & Vision • Powered by Smart Recommendations</p>
            </div>
          </div>
          <button className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="text-gray-600">☀️</span>
          </button>
        </div>
      </div>

      {/* Progress Section */}
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-medium">Enrollment Progress</h2>
            <span className="text-sm text-gray-500">{currentStep + 1} of {steps.length}</span>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div
              className="bg-gray-800 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage()}%` }}
            />
          </div>

          {/* Step Navigation Pills */}
          <div className="flex gap-2 overflow-x-auto pb-2">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;

              return (
                <div
                  key={step.id}
                  className={`flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all ${
                    isActive
                      ? 'bg-purple-500 text-white'
                      : isCompleted
                      ? 'bg-purple-400 text-white'
                      : 'bg-gray-100 text-gray-500'
                  }`}
                >
                  <Icon size={14} />
                  <span>{step.title}</span>
                  {isCompleted && <CheckCircle size={14} />}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="px-4 py-6">
        <div className="max-w-4xl mx-auto">
          <div ref={scrollRef}>
            {renderCurrentPage()}
          </div>
        </div>
      </div>

      {/* Navigation Footer */}
      {currentStep > 0 && currentStep < steps.length - 1 && (
        <div className="bg-white border-t border-gray-200 px-4 py-4">
          <div className="max-w-4xl mx-auto flex justify-between">
            <button
              onClick={handleBack}
              className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft size={16} />
              Back
            </button>

            {currentStep < steps.length - 2 && (
              <button
                onClick={handleNext}
                className="flex items-center gap-2 px-6 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
              >
                Next
                <ArrowRight size={16} />
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
