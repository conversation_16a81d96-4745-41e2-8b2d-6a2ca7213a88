/* Document Upload Styles */
.document-upload-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
  overflow: hidden;
}

.upload-header {
  padding: 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.upload-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.upload-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.upload-header p {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Upload Content */
.upload-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.upload-card {
  background: #fafbfc;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.header-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.required-badge {
  background: #dc2626;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.header-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.upload-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Upload Area */
.upload-area {
  padding: 1.5rem;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  text-align: center;
}

.empty-state span {
  font-size: 0.875rem;
}

/* Files List */
.files-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  color: #374151;
}

.file-item span:first-of-type {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
}

.file-size {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Guidelines Card */
.guidelines-card {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.guidelines-card .card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0;
  border: none;
  margin-bottom: 1rem;
  color: #1e40af;
}

.guidelines-card h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.guidelines-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.guidelines-content li {
  font-size: 0.875rem;
  color: #1e40af;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.guidelines-content li::before {
  content: '•';
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* Navigation */
.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-top: 1px solid #f1f5f9;
  background: #f8fafc;
}

.nav-actions {
  display: flex;
  gap: 0.75rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.nav-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.nav-btn.secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.nav-btn.primary {
  background: #3b82f6;
  color: white;
}

.nav-btn.primary.enabled:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Step Progress Updates */
.renewal-step.completed .step-number {
  background: #059669;
  color: white;
}

.renewal-step.completed .step-title {
  color: #059669;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-content {
    padding: 1rem;
  }
  
  .card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .navigation-section {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .nav-btn {
    flex: 1;
    justify-content: center;
  }
}
