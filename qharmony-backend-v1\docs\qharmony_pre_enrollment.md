# QHarmony Pre-Enrollment System Documentation

## Table of Contents

1. [System Overview](#1-system-overview)
   - 1.1 [Introduction](#11-introduction)
   - 1.2 [Architecture](#12-architecture)
   - 1.3 [Implementation Strategy](#13-implementation-strategy)

2. [Business Logic](#2-business-logic)
   - 2.1 [Employee Eligibility System](#21-employee-eligibility-system)
   - 2.2 [Cost Calculation Engine](#22-cost-calculation-engine)
   - 2.3 [Enrollment Validation](#23-enrollment-validation)
   - 2.4 [Access Control & Permissions](#24-access-control--permissions)

3. [Data Models](#3-data-models)
   - 3.1 [Carrier Model](#31-carrier-model)
   - 3.2 [Plan Model](#32-plan-model)
   - 3.3 [Plan Assignment Model](#33-plan-assignment-model)
   - 3.4 [Employee Enrollment Model](#34-employee-enrollment-model)
   - 3.5 [Company Benefits Settings Model](#35-company-benefits-settings-model)

4. [API Specifications](#4-api-specifications)
   - 4.1 [Carrier APIs (12 APIs - ✅ Complete)](#41-carrier-apis)
   - 4.2 [Plan APIs (14 APIs - ✅ Complete)](#42-plan-apis)
   - 4.3 [Plan Assignment APIs (17 APIs - ✅ Complete)](#43-plan-assignment-apis)
   - 4.4 [Company Benefits Settings APIs (8 APIs - ✅ Complete)](#44-company-benefits-settings-apis)
   - 4.5 [Employee Enrollment APIs (14 APIs - ✅ Complete)](#45-employee-enrollment-apis)
   - 4.6 [Cost Calculation Service (No Controller - Service Only)](#46-cost-calculation-service)

5. [Technical Implementation](#5-technical-implementation)
   - 5.1 [Coverage Mapping System](#51-coverage-mapping-system)
   - 5.2 [Database Schema](#52-database-schema)
   - 5.3 [Constants and Enums](#53-constants-and-enums)

6. [Status & Changelog](#6-status--changelog)
   - 6.1 [Implementation Status](#61-implementation-status)
   - 6.2 [API Coverage Summary](#62-api-coverage-summary)
   - 6.3 [Recent Changes](#63-recent-changes)

---

## 1. System Overview

### 1.1 Introduction

The QHarmony Pre-Enrollment System extends the existing benefits platform to provide comprehensive pre-enrollment functionality for insurance brokers, employers, and employees. The system enables brokers to create and manage insurance plans, assign them to companies, and facilitate employee enrollment workflows.

**Core Capabilities:**
- Insurance carrier and plan management
- Company-specific plan assignments with pricing
- Employee eligibility validation and enrollment
- Real-time cost calculation with multiple rate structures
- Role-based access control and audit trails

### 1.2 Architecture

The system implements a **hierarchical dependency chain** ensuring referential integrity and proper lifecycle management:

```
🔌 Carrier → 📄 Plan → 📋 PlanAssignment → 👥 EmployeeEnrollment
```

**Architectural Principles:**

1. **Carrier Model**: Foundation layer defining insurance carriers with capabilities and integration details
2. **Plan Model**: Blueprint layer containing plan templates and coverage definitions
3. **PlanAssignment Model**: Implementation layer with company-specific pricing and time constraints
4. **EmployeeEnrollment Model**: Execution layer handling individual employee enrollments
5. **CompanyBenefitsSettings Model**: Governance layer providing company-wide policies

**Key Design Features:**
- **Referential Integrity**: Objects cannot be deleted if referenced by dependent objects
- **Status-Based Lifecycle**: Soft deletion through status changes preserves historical data
- **Time-Based Constraints**: Enrollment periods and plan dates control access and modifications
- **Plug-and-Play Architecture**: Components can be reassigned without breaking relationships

### 1.3 Implementation Strategy

**Model-Heavy Architecture:**
- Business logic centralized in model classes as static methods
- Controllers handle HTTP concerns and delegate to model methods
- Enhanced maintainability through single source of truth for business rules
- Improved testability and code reusability across the application

---

## 2. Business Logic

### 2.1 Employee Eligibility System

The system implements comprehensive eligibility validation ensuring compliance with insurance regulations and company policies.

**Eligibility Validation Components:**

1. **Access Control Validation**
   - User permissions to access employee data
   - Role-based restrictions (SuperAdmin, Broker, Employer, Employee)

2. **Plan Assignment Validation**
   - Plan assignment exists and is active
   - Current date within plan assignment period

3. **Enrollment Period Validation**
   - Current date within enrollment period (enrollmentStartDate ≤ today ≤ enrollmentEndDate)
   - Special enrollment periods for qualifying life events

4. **Duplicate Enrollment Prevention**
   - No existing active enrollment for employee/plan combination
   - Terminated enrollments allow re-enrollment

5. **Profile Completeness Validation**
   - Required employee data present (dateOfBirth, employeeClassType, etc.)
   - Missing field identification and reporting

6. **Hire Date Eligibility**
   - Employee meets waiting period requirements
   - Configurable waiting period rules (days, first of month, etc.)

7. **Employee Class Eligibility**
   - Employee class type matches plan assignment eligibility rules
   - Support for multiple eligible classes per plan

**Business Rules:**
- Eligibility checks are performed in sequence with early termination on failure
- Detailed failure reasons provided for each validation component
- Eligibility status cached during enrollment session for performance

### 2.2 Cost Calculation Engine

Multi-rate structure cost calculation system supporting various insurance pricing models.

**Supported Rate Structures:**

1. **Composite Rates**: Flat rates regardless of employee demographics
2. **Four-Tier Rates**: Standard industry tiers (Employee, Employee+Spouse, Employee+Children, Family)
3. **Age-Banded Rates**: Rates vary by employee age with configurable age bands
4. **Salary-Based Rates**: Rates based on employee annual salary ranges
5. **Age-Banded Four-Tier**: Combination of age bands and family composition

**Contribution Policy Types:**

1. **Fixed Amount**: Employer pays fixed dollar amount, employee pays remainder
2. **Percentage**: Employer pays percentage of premium, employee pays remainder
3. **Remainder**: Employee pays fixed amount, employer pays remainder

**Cost Calculation Flow:**
```
Employee Data + Plan Assignment → Rate Structure Application → Contribution Policy → Final Costs
```

### 2.3 Enrollment Validation

Comprehensive validation system ensuring data integrity and business rule compliance.

**Validation Categories:**

1. **Dependent Validation**
   - Dependent count matches selected coverage tier
   - Relationship validation (Spouse, Child, Domestic Partner)
   - Age validation for dependents

2. **Coverage Tier Validation**
   - Selected tier available in plan assignment
   - Dependent requirements met for family tiers

3. **Date Validation**
   - Effective date within plan coverage period
   - Effective date not in the past (configurable)

4. **Business Rule Validation**
   - Single enrollment per employee per plan assignment
   - Status transition validation for updates
   - Termination date validation

### 2.4 Access Control & Permissions

Role-based access control system with hierarchical permissions.

**User Hierarchy:**
```
SuperAdmin → Broker → Employer → Employee
```

**Permission Matrix:**

| Resource | SuperAdmin | Broker | Employer | Employee |
|----------|------------|--------|----------|----------|
| System Carriers | Full CRUD | Read Only | Read Only | No Access |
| Broker Carriers | Full CRUD | Full CRUD (Own) | Read Only | No Access |
| System Plans | Full CRUD | Read Only | Read Only | No Access |
| Broker Plans | Full CRUD | Full CRUD (Own) | Read Only | No Access |
| Plan Assignments | Read Only | Full CRUD (Own) | Read Only (Own Company) | Read Only (Assigned) |
| Company Settings | Read Only | Full CRUD (Assigned Companies) | Full CRUD (Own Company) | Read Only |
| Employee Enrollments | Read Only | Full CRUD (Assigned Companies) | Read Only (Own Company) | CRUD (Own Only) |

**Access Control Rules:**
- SuperAdmins have system-wide access but cannot create plan assignments/enrollments
- Brokers can only access resources for companies they manage
- Employers can only access their company's data
- Employees can only access their own enrollment data
- All access validated through user-id header and JWT authentication

**🔐 Detailed Access Control Patterns by User Type:**

### **SuperAdmin Access Pattern:**
```typescript
// Scope: System-wide access to all resources
// Method: Direct database queries without filtering
// Example: Can view all plan assignments across all brokers and companies
```

### **Broker Access Pattern:**
```typescript
// Scope: Only resources they own/created
// Method: Filter by plan.brokerId or assignedBy field
// Two distinct patterns:

// 1. Portfolio View (All Companies):
getAllAssignmentsByBroker(brokerId)
// Returns: All assignments where plan.brokerId = brokerId
// Use Case: Broker dashboard, reporting across all clients

// 2. Company-Specific View (Single Company):
getBrokerAssignmentsForCompany(brokerId, companyId)
// Returns: Assignments where plan.brokerId = brokerId AND companyId = companyId
// Use Case: Company-specific management, access control validation
```

### **Employer/Company Admin Access Pattern:**
```typescript
// Scope: Only their own company's resources
// Method: Filter by user.companyId or company relationship
// Example: Can only view plan assignments for their company
// Security: Cannot see other companies' data or other brokers' assignments
```

### **Employee Access Pattern:**
```typescript
// Scope: Only their own enrollment data + company plan assignments
// Method: Filter by employeeId for enrollments, companyId for available plans
// Example: Can view plan assignments for their company (to see enrollment options)
// Security: Cannot see other employees' enrollments or other companies' plans
```

---

## 3. Data Models

The system implements a hierarchical data model with strict referential integrity and clear separation of concerns:

```
Carrier → Plan → PlanAssignment → EmployeeEnrollment
```

### 3.1 Carrier Model

**Purpose**: Centralized insurance carrier definitions with capabilities and contact information.

**Key Features:**
- **Hierarchical Access Control**: System carriers (created by SuperAdmins) vs Broker carriers
- **Integration Capabilities**: EDI, API, and data exchange configuration
- **Business Information**: A.M. Best ratings, license states, supported plan types
- **Contact Management**: Comprehensive contact information for member services and claims

**Business Rules:**
- Carriers cannot be deleted if referenced by active plans
- Inactive carriers cannot be assigned to new plans
- Broker-scoped uniqueness validation for carrier names and codes
- System carriers are accessible to all brokers (read-only)
- A.M. Best rating defaults to 'NR' (Not Rated) when null/undefined
- Broker-specific carriers have unique carrierCode and carrierName within broker scope

#### **3.1.1 Complete Interface**

```typescript
interface Carrier {
  // Identification
  _id?: string;
  carrierName: string;              // "Blue Cross Blue Shield"
  carrierCode: string;              // "BCBS" (unique within broker scope)
  displayName?: string;             // User-friendly display name

  // Ownership (Similar to Plan model)
  brokerId?: string;                // If broker-created, which broker owns it
  brokerageId?: string;             // If broker-created, which brokerage it belongs to
  isSystemCarrier: boolean;         // Whether this is a system-wide carrier

  // Contact Information
  contactInfo: {
    phone?: string;
    email?: string;
    website?: string;
    supportEmail?: string;
    claimsPhone?: string;
    memberServicesPhone?: string;
  };

  // Capabilities
  supportedPlanTypes: string[];     // ["PPO", "HMO", "HDHP"]

  // Supported Coverage Types (Using Pre-Enrollment Insurance Categories)
  supportedCoverageTypes: string[]; // ["Health Insurance", "Ancillary Benefits"]
  supportedCoverageSubTypes: string[]; // ["Medical", "Dental", "Vision"]

  // Integration
  integration: {
    ediCapable: boolean;
    apiEndpoint?: string;
    apiVersion?: string;
    authMethod?: string;            // "API_KEY", "OAUTH", "BASIC_AUTH"
    dataFormat?: string;            // "EDI", "JSON", "XML"
  };

  // Business Information
  licenseStates: string[];          // ["CA", "NY", "TX"]
  amRating: string;                 // A.M. Best financial rating (defaults to 'NR')
  networkName?: string;             // Provider network name

  // Status
  status: string;                   // "Active", "Inactive", "Archived"
  isActive: boolean;
  isActivated: boolean;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}
```

#### **3.1.2 Mongoose Schema**

```typescript
const CarrierSchema = new mongoose.Schema({
  carrierName: { type: String, required: true },
  carrierCode: { type: String, required: true },
  displayName: { type: String },

  // Ownership
  brokerId: { type: String },
  brokerageId: { type: String },
  isSystemCarrier: { type: Boolean, default: false },

  contactInfo: {
    phone: { type: String },
    email: { type: String },
    website: { type: String },
    supportEmail: { type: String },
    claimsPhone: { type: String },
    memberServicesPhone: { type: String }
  },

  supportedPlanTypes: [{
    type: String,
    enum: PLAN_TYPES // ["PPO", "HMO", "HDHP", "EPO", "POS"]
  }],

  supportedCoverageTypes: [{
    type: String,
    enum: PRE_ENROLLMENT_COVERAGE_TYPES
  }],
  supportedCoverageSubTypes: [{
    type: String,
    enum: PRE_ENROLLMENT_COVERAGE_SUBTYPES
  }],

  integration: {
    ediCapable: { type: Boolean, default: false },
    apiEndpoint: { type: String },
    apiVersion: { type: String },
    authMethod: { type: String, enum: ['API_KEY', 'OAUTH', 'BASIC_AUTH', 'CERTIFICATE'] },
    dataFormat: { type: String, enum: ['EDI', 'JSON', 'XML'], default: 'JSON' }
  },

  licenseStates: [{ type: String }],
  amRating: {
    type: String,
    enum: AM_BEST_RATING_CODES,
    default: 'NR',
    validate: {
      validator: function(value: string) {
        return !value || AM_BEST_RATING_CODES.includes(value as any);
      },
      message: 'Invalid A.M. Best rating. Must be one of: A++, A+, A, A-, B++, B+, B, B-, C++, C+, C, C-, D, E, F, or NR'
    },
    set: function(value: string | null | undefined) {
      if (!value || value.trim() === '') {
        return 'NR';
      }
      return value;
    }
  },
  networkName: { type: String },

  // System Fields
  status: {
    type: String,
    enum: CARRIER_STATUSES,
    default: 'Active'
  },
  isActive: { type: Boolean, default: true },
  isActivated: { type: Boolean, default: true }
}, { timestamps: true });

// Indexes
CarrierSchema.index({ carrierCode: 1 });
CarrierSchema.index({ carrierName: 1 });
CarrierSchema.index({ isActive: 1 });
CarrierSchema.index({ supportedCoverageTypes: 1 });
CarrierSchema.index({ supportedCoverageSubTypes: 1 });
CarrierSchema.index({ brokerId: 1 });
CarrierSchema.index({ isSystemCarrier: 1 });

// Broker-specific uniqueness compound indexes
CarrierSchema.index({ brokerId: 1, carrierCode: 1 }, { unique: true });
CarrierSchema.index({ brokerId: 1, carrierName: 1 }, { unique: true });
```

#### **3.1.3 Business Logic Methods**

```typescript
// Validation Methods
public static canEdit(carrierId: string, userId: string): Promise<{canEdit: boolean, reason?: string}>
public static canDelete(carrierId: string, userId: string): Promise<{canDelete: boolean, reason?: string}>
public static getDependentPlans(carrierId: string): Promise<Plan[]>

// Access Control Methods
public static getAccessibleCarriers(userId: string, filters?: any): Promise<Carrier[]>
public static validateCarrierAccess(carrierId: string, userId: string): Promise<boolean>

// Business Rule Validation
public static validateUniqueCarrierCode(carrierCode: string, brokerId?: string, excludeId?: string): Promise<boolean>
public static validateCarrierCompatibility(carrierId: string, planData: any): Promise<{compatible: boolean, issues?: string[]}>
```

### 3.2 Plan Model

**Purpose**: Plan templates with coverage details and carrier relationships. Pure blueprints without time constraints or pricing.

**Key Features:**
- **Template System**: Plans can be marked as templates for duplication
- **Coverage Definition**: Comprehensive coverage types and subtypes
- **Carrier Integration**: References to carrier for integration capabilities
- **Broker Ownership**: Plans belong to specific brokers or are system-wide

**Business Rules:**
- Plans are created as Active by default (templates get 'Template' status)
- Template plans cannot be assigned to companies
- Plans can be edited if referenced by ≤1 assignment
- Only Active plans can be assigned to new companies
- Broker-scoped uniqueness validation for plan names and codes
- SuperAdmins automatically create templates when using plan creation endpoint

**Status Transition Rules:**
- **Draft → Active**: Via activate API (requires validation)
- **Active → Draft**: Via convert-to-draft API (makes plan editable again)
- **Archived → Active**: Via activate API (allows reactivation)
- **Any Status → Archived**: Via archive API (plan becomes unusable)
- **Template Status**: Immutable (cannot be changed once set)

#### **3.2.1 Complete Interface**

```typescript
interface Plan {
  // Identification
  _id?: string;
  planName: string;                 // "Gold PPO Health Plan"
  planCode?: string;                // "GOLD_PPO_2024" (optional, unique within broker scope)

  // Coverage Definition
  coverageType: string;             // "Health Insurance"
  coverageSubTypes: string[];       // ["Medical", "Dental", "Vision"]
  planType?: string;                // "PPO", "HMO", "HDHP", "EPO", "POS"
  metalTier?: string;               // "Bronze", "Silver", "Gold", "Platinum"

  // Content
  description: string;              // Required plan description
  highlights?: string[];            // ["Low deductible", "Wide network"]
  informativeLinks?: string[];      // ["https://carrier.com/plan-details"]

  // Benefit Details
  benefitDetails?: {
    deductibleIndividual?: number;
    deductibleFamily?: number;
    pcpCopay?: number;
    specialistCopay?: number;
    emergencyRoomCopay?: number;
    urgentCareCopay?: number;
    coinsurance?: string;           // "20%"
    outOfPocketMaxIndividual?: number;
    outOfPocketMaxFamily?: number;
    preventiveCareCoinsurance?: string; // "0%"
    prescriptionCoverage?: {
      generic?: number;
      brandName?: number;
      specialty?: number;
    };
    additionalBenefits?: string[];  // ["Telehealth", "Mental health"]
  };

  // Carrier Integration
  carrierId?: string;               // Reference to Carrier
  carrierPlanId?: string;           // Carrier's internal plan ID

  // Ownership
  brokerId?: string;                // Broker ownership (null for system plans)
  brokerageId?: string;             // Brokerage ownership (null for system plans)
  isTemplate: boolean;              // Whether this is a template plan

  // Status
  status: string;                   // "Active", "Archived", "Draft", "Template"
  isActivated: boolean;

  // Document Management
  documentIds?: string[];           // Azure blob document references

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}
```

#### **3.2.2 Mongoose Schema**

```typescript
const PlanSchema = new mongoose.Schema({
  planName: { type: String, required: true },
  planCode: { type: String }, // Non-unique, optional

  // Coverage Definition
  coverageType: {
    type: String,
    enum: PRE_ENROLLMENT_COVERAGE_TYPES,
    required: true
  },
  coverageSubTypes: [{
    type: String,
    enum: PRE_ENROLLMENT_COVERAGE_SUBTYPES,
    required: true
  }],
  planType: {
    type: String,
    enum: PLAN_TYPES // ["PPO", "HMO", "HDHP", "EPO", "POS"]
  },
  metalTier: {
    type: String,
    enum: METAL_TIERS // ["Bronze", "Silver", "Gold", "Platinum"]
  },

  // Content
  description: { type: String, required: true },
  highlights: [{ type: String }],
  informativeLinks: [{
    type: String,
    validate: {
      validator: function(url: string) {
        // Basic URL validation
        try {
          new URL(url);
          return true;
        } catch {
          return false;
        }
      },
      message: 'Invalid URL format'
    }
  }],

  // Benefit Details
  benefitDetails: {
    deductibleIndividual: { type: Number },
    deductibleFamily: { type: Number },
    pcpCopay: { type: Number },
    specialistCopay: { type: Number },
    emergencyRoomCopay: { type: Number },
    urgentCareCopay: { type: Number },
    coinsurance: { type: String },
    outOfPocketMaxIndividual: { type: Number },
    outOfPocketMaxFamily: { type: Number },
    preventiveCareCoinsurance: { type: String },
    prescriptionCoverage: {
      generic: { type: Number },
      brandName: { type: Number },
      specialty: { type: Number }
    },
    additionalBenefits: [{ type: String }]
  },

  // Carrier Integration
  carrierId: { type: mongoose.Schema.Types.ObjectId, ref: 'Carrier' },
  carrierPlanId: { type: String },

  // Ownership
  brokerId: { type: String },
  brokerageId: { type: String },
  isTemplate: { type: Boolean, default: false, required: true },

  // Status
  status: {
    type: String,
    enum: PLAN_STATUSES, // ["Active", "Archived", "Draft", "Template"]
    default: function() { return this.isTemplate ? 'Template' : 'Active'; }
  },
  isActivated: { type: Boolean, default: false },

  // Document Management
  documentIds: [{ type: String }]
}, { timestamps: true });

// Indexes
PlanSchema.index({ brokerId: 1 });
PlanSchema.index({ brokerageId: 1 });
PlanSchema.index({ coverageType: 1, coverageSubTypes: 1 });
PlanSchema.index({ status: 1 });
PlanSchema.index({ isTemplate: 1 });
PlanSchema.index({ planCode: 1 }); // Non-unique index for faster lookups

// Broker-specific uniqueness compound indexes
PlanSchema.index({ brokerId: 1, planCode: 1 }, { unique: true });
PlanSchema.index({ brokerId: 1, planName: 1 }, { unique: true });
```

#### **3.2.3 Business Logic Methods**

```typescript
// Validation Methods
public static canEdit(planId: string, userId: string): Promise<{canEdit: boolean, reason?: string}>
public static canDelete(planId: string, userId: string): Promise<{canDelete: boolean, reason?: string}>
public static getDependentAssignments(planId: string): Promise<PlanAssignment[]>

// Access Control Methods
public static getAccessiblePlans(userId: string, filters?: any): Promise<Plan[]>
public static getAssignablePlans(userId: string): Promise<Plan[]>
public static getTemplates(userId: string): Promise<Plan[]>

// Business Rule Validation
public static validateUniquePlanCode(planCode: string, brokerId?: string, excludeId?: string): Promise<boolean>
public static validateCoverageCompatibility(coverageType: string, coverageSubTypes: string[]): Promise<boolean>
public static validateCarrierCompatibility(planId: string, carrierId: string): Promise<{compatible: boolean, issues?: string[]}>

// Template Operations
public static duplicatePlan(planId: string, userId: string, customizations?: any): Promise<Plan>
public static createFromTemplate(templateId: string, userId: string, planData: any): Promise<Plan>
```

### 3.3 Plan Assignment Model

**Purpose**: Company-specific plan implementations with pricing, eligibility rules, and time constraints. This is where plans become actionable for specific companies.

**Key Features:**
- **Employee Eligibility Rules**: Class type restrictions and waiting period configuration
- **Time-Based Management**: Enrollment periods and plan effectiveness dates
- **Pricing Configuration**: Multiple rate structures and contribution policies
- **Status Management**: Active/Deactivated states with automatic expiry
- **Multi-Year Support**: Same plan can be reused across years with date updates

**Business Rules:**
- Plan assignments are active by default upon creation
- Editable only with no/one enrollment dependency
- Auto-expire after plan end date
- Support year-over-year reuse with date updates
- Activate/deactivate functionality for status management
- Clone functionality creates assignments in deactivated state
- **CRITICAL**: Only brokers can create plan assignments (SuperAdmins manage system resources only)

#### **3.3.1 Complete Interface**

```typescript
interface PlanAssignment {
  // Identification
  _id?: string;
  planId: string;                    // Reference to Plan
  companyId: string;                 // Reference to Company
  assignedBy: string;                // Broker user ID who created assignment
  groupNumber?: string;              // Company-specific group number

  // Employee Eligibility Rules
  waitingPeriod: {
    enabled: boolean;
    days: number;
    rule: string;                    // "Immediate" | "Days from hire date" | "First of month after X days"
    description?: string;            // Human-readable description
  };
  eligibleEmployeeClasses: string[]; // ["Full-Time", "Part-Time", "Contractor", etc.]

  // 🎯 NEW: Qualifying Life Event Configuration
  qualifyingLifeEventWindow: {
    enabled: boolean;                // Whether QLE enrollment is allowed for this plan
    windowDays: number;              // Number of days after QLE event to allow enrollment
    allowedEvents: string[];         // Array of allowed QLE event types
    description?: string;            // Human-readable description of QLE policy
  };

  // Time Constraints
  planEffectiveDate: Date;           // When plan becomes effective
  planEndDate: Date;                 // When plan expires
  enrollmentStartDate: Date;         // When enrollment opens
  enrollmentEndDate: Date;           // When enrollment closes
  assignmentYear: number;            // Year for this assignment (extracted from planEndDate)
  assignmentExpiry: Date;            // Auto-calculated expiry date

  // Enrollment Configuration
  enrollmentType?: string;           // "Active", "Passive" - Controls user experience during enrollment

  // Pricing Structure
  rateStructure: string;             // "composite" | "four-tier" | "age-banded" | "salary-based"
  coverageTiers: Array<{
    tierName: string;                // "Employee Only", "Employee + Spouse", "Family"
    totalCost: number;               // Total cost for this tier
    employeeCost: number;            // Employee portion
    employerCost: number;            // Employer portion
  }>;

  // Age-Banded Rates (for age-banded rate structure)
  ageBandedRates?: Array<{
    ageMin: number;                  // Starting age
    ageMax: number;                  // Ending age
    rate: number;                    // Base rate for this age band
    type?: 'fixed' | 'multiplier';  // 🎯 UPDATED: Type of rate adjustment (default: 'fixed')
  }>;

  // Salary-Based Rates (for salary-based rate structure) 🎯 UPDATED: Now supports both types
  salaryBasedRates?: Array<{
    salaryMin: number;               // Starting salary
    salaryMax: number;               // Ending salary
    rate: number;                    // Base rate for this salary band
    type?: 'fixed' | 'multiplier';  // 🎯 NEW: Type of rate adjustment (default: 'fixed')
  }>;

  // Salary Percentage (alternative to salaryBasedRates)
  salaryPercentage?: number;         // Percentage of annual salary (0-100)

  // Contribution Rules
  employerContribution: {
    contributionType: string;        // "Fixed" | "Percentage" | "Remainder"
    contributionAmount: number;      // Amount or percentage
  };
  employeeContribution: {
    contributionType: string;        // "Fixed" | "Percentage" | "Remainder"
    contributionAmount: number;      // Amount or percentage
  };

  // Plan Customizations
  planCustomizations?: {
    customPlanName?: string;         // Company-specific plan name override
    customDescription?: string;      // Company-specific description override
    additionalDocuments?: string[];  // Company-specific additional documents
    displayOrder?: number;           // Order in company's plan list
  };

  // Assignment Metadata
  assignedDate: Date;                // When assignment was created

  // Status Management
  isActive: boolean;                 // Whether assignment is active
  status: string;                    // "Active" | "Deactivated" | "Expired"

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}
```

#### **3.3.2 Mongoose Schema**

```typescript
const PlanAssignmentSchema = new mongoose.Schema({
  // Core References
  planId: { type: mongoose.Schema.Types.ObjectId, ref: 'Plan', required: true },
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company', required: true },
  assignedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  groupNumber: { type: String },

  // Employee Eligibility Rules
  waitingPeriod: {
    enabled: { type: Boolean, default: false },
    days: { type: Number, min: 0, default: 0 },
    rule: {
      type: String,
      enum: WAITING_PERIOD_RULES,
      default: 'Immediate'
    },
    description: { type: String }
  },
  eligibleEmployeeClasses: [{
    type: String,
    enum: EMPLOYEE_CLASS_TYPES,
    required: true
  }],

  // 🎯 NEW: Qualifying Life Event Configuration
  qualifyingLifeEventWindow: {
    enabled: { type: Boolean, default: true },
    windowDays: { type: Number, min: 0, default: 30 },
    allowedEvents: [{
      type: String,
      enum: QUALIFYING_LIFE_EVENT_TYPES,
      default: () => [...QUALIFYING_LIFE_EVENT_TYPES]
    }],
    description: { type: String, default: 'Standard qualifying life event enrollment window' }
  },

  // Time Constraints
  planEffectiveDate: { type: Date, required: true },
  planEndDate: { type: Date, required: true },
  enrollmentStartDate: { type: Date, required: true },
  enrollmentEndDate: { type: Date, required: true },
  assignmentYear: { type: Number, required: true },
  assignmentExpiry: { type: Date, required: true },

  // Enrollment Configuration
  enrollmentType: {
    type: String,
    enum: ENROLLMENT_TYPES,        // ["Active", "Passive"]
    default: 'Active',
    required: true
  },

  // Pricing Structure
  rateStructure: {
    type: String,
    enum: RATE_STRUCTURES
  },
  coverageTiers: [{
    tierName: { type: String, required: true },
    totalCost: { type: Number, required: true, min: 0 },
    employeeCost: { type: Number, required: true, min: 0 },
    employerCost: { type: Number, required: true, min: 0 }
  }],

  // Age-Banded Rates (🎯 UPDATED: Now supports both fixed amounts and multiplier factors)
  ageBandedRates: [{
    ageMin: { type: Number },
    ageMax: { type: Number },
    rate: { type: Number, min: 0 },
    type: { type: String, enum: ['fixed', 'multiplier'], default: 'fixed' }  // 🎯 NEW: Type support
  }],

  // Salary-Based Rates (🎯 UPDATED: Now supports both fixed amounts and multiplier factors)
  salaryBasedRates: [{
    salaryMin: { type: Number },
    salaryMax: { type: Number },
    rate: { type: Number, min: 0 },
    type: { type: String, enum: ['fixed', 'multiplier'], default: 'fixed' }  // 🎯 NEW: Type support
  }],

  // Salary Percentage (alternative to salaryBasedRates)
  salaryPercentage: {
    type: Number,
    min: 0,
    max: 100  // Percentage should be between 0-100%
  },

  // Contribution Rules
  employerContribution: {
    contributionType: {
      type: String,
      enum: CONTRIBUTION_TYPES,
      required: true
    },
    contributionAmount: { type: Number, required: true, min: 0 }
  },
  employeeContribution: {
    contributionType: {
      type: String,
      enum: CONTRIBUTION_TYPES,
      required: true
    },
    contributionAmount: { type: Number, required: true, min: 0 }
  },

  // Plan Customizations
  planCustomizations: {
    customPlanName: { type: String },
    customDescription: { type: String },
    additionalDocuments: [{ type: String }],
    displayOrder: { type: Number }
  },

  // Assignment Metadata
  assignedDate: {
    type: Date,
    required: true,
    default: Date.now
  },

  // Status Management
  isActive: { type: Boolean, default: true },
  status: {
    type: String,
    enum: PLAN_ASSIGNMENT_STATUSES,
    default: 'Active'
  }
}, { timestamps: true });

// Indexes
PlanAssignmentSchema.index({ planId: 1 });
PlanAssignmentSchema.index({ companyId: 1 });
PlanAssignmentSchema.index({ assignedBy: 1 });
PlanAssignmentSchema.index({ status: 1 });
PlanAssignmentSchema.index({ isActive: 1 });
PlanAssignmentSchema.index({ assignmentYear: 1 });
PlanAssignmentSchema.index({ assignmentExpiry: 1 });
PlanAssignmentSchema.index({ assignedDate: 1 });
PlanAssignmentSchema.index({ planEffectiveDate: 1 });
PlanAssignmentSchema.index({ planEndDate: 1 });
PlanAssignmentSchema.index({ enrollmentStartDate: 1 });
PlanAssignmentSchema.index({ enrollmentEndDate: 1 });
PlanAssignmentSchema.index({ planEffectiveDate: 1, planEndDate: 1 });
PlanAssignmentSchema.index({ enrollmentStartDate: 1, enrollmentEndDate: 1 });
PlanAssignmentSchema.index({ 'coverageTiers.tierName': 1 });

// Compound indexes for business queries
PlanAssignmentSchema.index({ companyId: 1, status: 1 });
PlanAssignmentSchema.index({ assignedBy: 1, status: 1 });
PlanAssignmentSchema.index({ planId: 1, companyId: 1, assignmentYear: 1 }, { unique: true });
```

#### **3.3.3 Business Logic Methods**

```typescript
// Validation Methods
public static canEdit(assignmentId: string, userId: string): Promise<{canEdit: boolean, reason?: string}>
public static canDelete(assignmentId: string, userId: string): Promise<{canDelete: boolean, reason?: string}>
public static getEnrollmentReferences(assignmentId: string): Promise<EmployeeEnrollment[]>

// Access Control Methods
public static getAccessibleAssignments(userId: string, filters?: any): Promise<PlanAssignment[]>
public static getAssignmentsByCompany(companyId: string, userId: string): Promise<PlanAssignment[]>
public static validateAssignmentAccess(assignmentId: string, userId: string): Promise<boolean>

// Time-Based Queries
public static getEffectiveAssignments(date?: Date, userId?: string): Promise<PlanAssignment[]>
public static getEnrollmentPeriodAssignments(date?: Date, userId?: string): Promise<PlanAssignment[]>
public static getExpiringAssignments(daysAhead?: number): Promise<PlanAssignment[]>

// Status Management
public static activateAssignment(assignmentId: string, userId: string): Promise<PlanAssignment>
public static deactivateAssignment(assignmentId: string, userId: string): Promise<PlanAssignment>
public static expireAssignment(assignmentId: string): Promise<PlanAssignment>

// Assignment Operations
public static cloneAssignment(assignmentId: string, userId: string, cloneData: any): Promise<PlanAssignment>
public static reassignPlan(assignmentId: string, newPlanId: string, userId: string): Promise<PlanAssignment>
public static updateTimeConstraints(assignmentId: string, timeData: any, userId: string): Promise<PlanAssignment>

// Business Rule Validation
public static validateAssignmentDates(dates: {
  planEffectiveDate: Date;
  planEndDate: Date;
  enrollmentStartDate: Date;
  enrollmentEndDate: Date;
}): Promise<{valid: boolean, errors?: string[]}>

public static validateRateStructure(rateStructure: string, coverageTiers: any[], ageBandedRates?: any[], salaryBasedRates?: any[]): Promise<{valid: boolean, errors?: string[]}>

public static validateContributionRules(employerContribution: any, employeeContribution: any): Promise<{valid: boolean, errors?: string[]}>

// Cost Calculation Support
public static calculateBasePremium(assignmentId: string, tierName: string, employeeAge?: number, employeeSalary?: number): Promise<{premium: number, calculation: any}>

// Automatic Expiry Management
public static processExpiringAssignments(): Promise<{processed: number, expired: PlanAssignment[]}>
public static calculateAssignmentYear(planEndDate: Date): number
public static calculateAssignmentExpiry(planEndDate: Date): Date
```

### 3.4 Employee Enrollment Model

**Purpose**: Individual employee enrollments with selections, costs, and comprehensive validation. Handles the actual enrollment process with eligibility checking and cost calculation.

**Key Features:**
- **Comprehensive Eligibility Validation**: Hire date, employee class, profile completeness
- **Real-Time Cost Calculation**: Multiple rate structures with accurate cost computation
- **Dependent Management**: Validation and storage of dependent information
- **Coverage Tier Selection**: Validation of tier selection against plan options
- **Single Enrollment Per Plan**: One enrollment per employee per plan assignment

**Business Rules:**
- Unique constraint: One enrollment per employee per plan assignment
- Employee age and salary stored in User model (single source of truth)
- Dependents schema matches User model for consistency
- Cost calculations automatically retrieve current employee data
- Comprehensive hire date and waiting period validation
- Enrollment status transitions: CREATE → Enrolled → Terminated/Waived (🎯 UPDATED: Auto-enrollment)

#### **3.4.1 Complete Interface**

```typescript
interface EmployeeEnrollment {
  // Identification
  _id?: string;
  planAssignmentId: string;          // Reference to PlanAssignment
  employeeId: string;                // Employee user ID
  companyId: string;                 // Company ID (for faster queries)

  // Coverage Details
  coverageType: string;              // Coverage type from Plan
  coverageSubTypes: string[];        // ALL coverage subtypes from Plan
  employeeClassType: string;         // Employee class at enrollment time

  // Coverage Selection
  coverageTier: string;              // Selected coverage tier ("Employee Only", "Family", etc.)

  // Cost Information (Total for ALL coverage subtypes)
  contribution: {
    employeeAmount: number;          // Employee pays per month for ALL subtypes
    employerAmount: number;          // Employer pays per month for ALL subtypes
    totalAmount: number;             // Total premium per month for ALL subtypes
  };

  // 🎯 NEW: Enrolled Dependents (Reference-based with enrollment metadata)
  enrolledDependents: Array<{
    dependentId: string;             // Reference to User.details.dependents._id
    enrollmentDate: Date;            // When this dependent was enrolled in THIS plan
    effectiveDate: Date;             // When coverage starts for this dependent
    terminationDate?: Date;          // When coverage ends (if applicable)
    carrierMemberId?: string;        // Insurance carrier member ID
    isActive: boolean;               // Whether this dependent is active in THIS enrollment

    // 🎯 SNAPSHOT: Critical data at enrollment time (for historical accuracy)
    enrollmentSnapshot: {
      name: string;                  // Name at time of enrollment
      dateOfBirth: Date;            // DOB at time of enrollment
      relationship: string;         // Relationship at time of enrollment
      gender: string;               // Gender at time of enrollment
    };
  }>;

  // Status and Enrollment Type
  status: string;                    // "Enrolled", "Waived", "Pending", "Terminated"
  enrolledUnder: string;             // "Open Enrollment", "New Hire", "Qualifying Life Event"
  waiveReason?: string;              // Reason for waiving coverage
  waiveDate?: Date;                  // Date when enrollment was waived

  // Dates
  enrollmentDate: Date;              // When employee enrolled (defaults to now)
  effectiveDate: Date;               // When coverage begins
  terminationDate?: Date;            // When coverage ends (if terminated)

  // 🎯 NEW: Qualifying Life Event Data (only if enrolledUnder = "Qualifying Life Event")
  qualifyingLifeEvent?: {
    eventType: string;               // "Marriage", "Divorce", "Birth", "Adoption", etc.
    eventDate: Date;                 // When the qualifying event occurred
    documentationUrl?: string;       // URL to supporting documentation
    allowedEnrollmentWindow: {
      start: Date;                   // Start of enrollment window
      end: Date;                     // End of enrollment window
    };
    processedBy?: string;            // User ID who processed the QLE
    processedAt?: Date;              // When the QLE was processed
  };

  // Audit Trail
  lastModifiedBy?: string;           // User ID who last modified the enrollment
  lastModifiedAt?: Date;             // When the enrollment was last modified

  // Carrier Information
  carrierMemberId?: string;          // Member ID assigned by carrier

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}
```

#### **3.4.2 Mongoose Schema**

```typescript
const EmployeeEnrollmentSchema = new mongoose.Schema({
  // Core References
  planAssignmentId: { type: mongoose.Schema.Types.ObjectId, ref: 'PlanAssignment', required: true },
  employeeId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company', required: true },

  // Coverage Details
  coverageType: {
    type: String,
    enum: PRE_ENROLLMENT_COVERAGE_TYPES,
    required: true
  },
  coverageSubTypes: [{
    type: String,
    enum: PRE_ENROLLMENT_COVERAGE_SUBTYPES,
    required: true
  }],

  // Employee Information
  employeeClassType: {
    type: String,
    enum: EMPLOYEE_CLASS_TYPES,
    required: true
  },

  // Coverage Selection
  coverageTier: { type: String, required: true },

  // Cost Information
  contribution: {
    employeeAmount: { type: Number, required: true, min: 0 },
    employerAmount: { type: Number, required: true, min: 0 },
    totalAmount: { type: Number, required: true, min: 0 }
  },

  // 🎯 NEW: Reference-based enrolled dependents
  enrolledDependents: [{
    dependentId: { type: String, required: true },  // Reference to User.details.dependents._id
    enrollmentDate: { type: Date, required: true, default: Date.now },
    effectiveDate: { type: Date, required: true },
    terminationDate: { type: Date },
    carrierMemberId: { type: String },
    isActive: { type: Boolean, required: true, default: true },

    // 🎯 SNAPSHOT: Critical data at enrollment time
    enrollmentSnapshot: {
      name: { type: String, required: true },
      dateOfBirth: { type: Date, required: true },
      relationship: {
        type: String,
        enum: ['Spouse', 'Child', 'Domestic Partner', 'Stepchild', 'Adopted Child', 'Other'],
        required: true
      },
      gender: {
        type: String,
        enum: ['Male', 'Female', 'Other', 'Prefer not to say'],
        required: true
      }
    }
  }],

  // Status and Enrollment Type
  status: {
    type: String,
    enum: ENROLLMENT_STATUSES,
    default: 'Pending',
    required: true
  },
  enrolledUnder: {
    type: String,
    enum: ENROLLMENT_PERIOD_TYPES,
    default: 'Open Enrollment',
    required: true
  },
  waiveReason: { type: String },
  waiveDate: { type: Date },

  // Dates
  enrollmentDate: { type: Date, default: Date.now },
  effectiveDate: { type: Date, required: true },
  terminationDate: { type: Date },

  // 🎯 PLAN REFERENCE FIELDS (from Plan Assignment) - for expiry management
  planYear: { type: Number, required: true },
  planEndDate: { type: Date, required: true },

  // 🎯 NEW: Qualifying Life Event Data (only if enrolledUnder = "Qualifying Life Event")
  qualifyingLifeEvent: {
    eventType: {
      type: String,
      enum: QUALIFYING_LIFE_EVENT_TYPES
    },
    eventDate: { type: Date },
    documentationUrl: { type: String },
    allowedEnrollmentWindow: {
      start: { type: Date },
      end: { type: Date }
    },
    processedBy: { type: String },
    processedAt: { type: Date }
  },

  // 🎯 NEW: Audit Trail
  lastModifiedBy: { type: String },
  lastModifiedAt: { type: Date },

  // Carrier Information
  carrierMemberId: { type: String }
}, { timestamps: true });

// Indexes
EmployeeEnrollmentSchema.index({ employeeId: 1, status: 1 });
EmployeeEnrollmentSchema.index({ planAssignmentId: 1 });
EmployeeEnrollmentSchema.index({ companyId: 1 });
EmployeeEnrollmentSchema.index({ coverageType: 1 });
EmployeeEnrollmentSchema.index({ coverageSubTypes: 1 });
EmployeeEnrollmentSchema.index({ coverageType: 1, coverageSubTypes: 1 });
EmployeeEnrollmentSchema.index({ enrollmentDate: 1 });
EmployeeEnrollmentSchema.index({ effectiveDate: 1 });
EmployeeEnrollmentSchema.index({ planYear: 1 });
EmployeeEnrollmentSchema.index({ planEndDate: 1 });
EmployeeEnrollmentSchema.index({ status: 1, planEndDate: 1 }); // For expiry queries

// 🎯 UNIQUE CONSTRAINT: One enrollment per employee per plan assignment
EmployeeEnrollmentSchema.index({ employeeId: 1, planAssignmentId: 1 }, { unique: true });
```

#### **3.4.3 Business Logic Methods**

```typescript
// Eligibility Validation Methods
public static isEmployeeEligibleByHireDate(employee: any, planAssignment: any, referenceDate?: Date): Promise<{
  isEligible: boolean;
  eligibilityDate: Date;
  reason?: string;
  daysUntilEligible?: number;
}>

public static isEmployeeClassEligible(employee: any, planAssignment: any): Promise<{
  isEligible: boolean;
  reason?: string;
  employeeClass?: string;
  eligibleClasses?: string[];
}>

public static validateEmployeeProfileForEnrollment(employee: any, planAssignment: any): Promise<{
  isValid: boolean;
  missingFields: string[];
  errors: string[];
  warnings: string[];
}>

public static calculateEligibilityDate(hireDate: Date, waitingPeriod: any): Date

// Comprehensive Eligibility Check
public static checkEmployeeEligibility(employeeId: string, planAssignmentId: string, userId: string): Promise<{
  isEligible: boolean;
  eligibilityChecks: {
    accessControl: { passed: boolean; message?: string; };
    enrollmentPeriod: { passed: boolean; message?: string; };
    duplicateCheck: { passed: boolean; message?: string; };
    profileCompleteness: { passed: boolean; missingFields?: string[]; };
    hireDateEligibility: {
      passed: boolean;
      eligibilityDate?: Date;
      daysUntilEligible?: number;
      message?: string;
    };
    employeeClassEligibility: {
      passed: boolean;
      employeeClass?: string;
      eligibleClasses?: string[];
      message?: string;
    };
    planAssignmentActive: { passed: boolean; message?: string; };
  };
  overallMessage: string;
}>

// Cost Calculation Methods
public static calculateEnrollmentCost(planAssignmentId: string, employeeId: string, costData: {
  selectedTier: string;
  dependents?: any[];
  payrollFrequency?: string;
}): Promise<{
  success: boolean;
  isEligible: boolean;
  cost?: {
    employeeAmount: number;
    employerAmount: number;
    totalAmount: number;
    payrollDeduction?: number;
  };
  calculation?: {
    rateStructure: string;
    basePremium: number;
    tierMultiplier?: number;
    ageMultiplier?: number;
    salaryMultiplier?: number;
  };
  eligibilityMessage?: string;
}>

// Enrollment Management
public static createEnrollment(planAssignmentId: string, employeeId: string, enrollmentData: any, userId: string): Promise<EmployeeEnrollment>

public static updateEnrollment(enrollmentId: string, updateData: any, userId: string): Promise<EmployeeEnrollment>

public static terminateEnrollment(enrollmentId: string, terminationData: {
  terminationDate: Date;
  terminationReason: string;
}, userId: string): Promise<EmployeeEnrollment>

public static waiveEnrollment(enrollmentId: string, waiveData: {
  waiveReason: string;
}, userId: string): Promise<EmployeeEnrollment>

public static reinstateEnrollment(enrollmentId: string, reinstateData: {
  effectiveDate: Date;
  reason: string;
}, userId: string): Promise<EmployeeEnrollment>

// Access Control Methods
public static getAccessibleEnrollments(userId: string, filters?: any): Promise<EmployeeEnrollment[]>
public static getEnrollmentsByEmployee(employeeId: string, userId: string): Promise<EmployeeEnrollment[]>
public static getEnrollmentsByCompany(companyId: string, userId: string): Promise<EmployeeEnrollment[]>
public static getEnrollmentsByPlanAssignment(planAssignmentId: string, userId: string): Promise<EmployeeEnrollment[]>

// Validation Methods
public static canEditEnrollment(enrollmentId: string, userId: string): Promise<{canEdit: boolean, reason?: string}>
public static canDeleteEnrollment(enrollmentId: string, userId: string): Promise<{canDelete: boolean, reason?: string}>
public static validateEnrollmentAccess(enrollmentId: string, userId: string): Promise<boolean>

// Dependent Validation
public static validateDependents(dependents: any[], coverageTier: string): Promise<{valid: boolean, errors?: string[]}>
public static validateDependentAges(dependents: any[]): Promise<{valid: boolean, errors?: string[]}>
public static validateDependentRelationships(dependents: any[], coverageTier: string): Promise<{valid: boolean, errors?: string[]}>

// Status Management
public static getValidStatusTransitions(currentStatus: string): string[]
public static validateStatusTransition(currentStatus: string, newStatus: string): boolean
public static getStatusTransitionRequirements(fromStatus: string, toStatus: string): {
  isValid: boolean;
  requiredFields: string[];
  businessRules: string[];
}

// 🎯 NEW: Status Management Operations
public static waiveEnrollment({
  enrollmentId: string;
  waiveReason: string;
  userId: string;
  waiveDate?: Date;
}): Promise<{ success: boolean; message: string; enrollment?: any }>

public static terminateEnrollment({
  enrollmentId: string;
  terminationDate: Date;
  terminationReason: string;
  userId: string;
}): Promise<{ success: boolean; message: string; enrollment?: any }>

public static reinstateEnrollment({
  enrollmentId: string;
  newStatus: string;
  reinstateReason: string;
  newEffectiveDate?: Date;
  userId: string;
}): Promise<{ success: boolean; message: string; enrollment?: any }>

public static activateEnrollment({
  enrollmentId: string;
  activationDate?: Date;
  userId: string;
}): Promise<{ success: boolean; message: string; enrollment?: any }>

public static updateEnrollmentStatus(enrollmentId: string, newStatus: string, reason?: string, userId?: string): Promise<EmployeeEnrollment>

// 🎯 NEW: Expiry Management Methods
public static isEnrollmentExpired(enrollment: EmployeeEnrollment, referenceDate?: Date): boolean
public static getEnrollmentStatus(enrollment: EmployeeEnrollment, referenceDate?: Date): string
public static checkExpiredEnrollments(): Promise<{ expiredCount: number; updatedEnrollments: string[] }>
public static getExpiredEnrollments(): Promise<EmployeeEnrollment[]>

// Audit and History
public static addAuditEntry(enrollmentId: string, action: string, userId: string, reason?: string, previousValues?: any): Promise<void>
public static getEnrollmentHistory(enrollmentId: string): Promise<any[]>

// Business Rule Validation
public static validateEnrollmentDates(enrollmentDate: Date, effectiveDate: Date, planAssignment: any): Promise<{valid: boolean, errors?: string[]}>
public static validateBeneficiaries(beneficiaries: any[]): Promise<{valid: boolean, errors?: string[]}>
public static validateCarrierRequirements(enrollmentData: any, planAssignment: any): Promise<{valid: boolean, errors?: string[]}>
```

### 3.5 Company Benefits Settings Model

**Purpose**: Company-wide benefit policies and enrollment periods. Provides default settings that can be overridden by specific plan assignments.

**Key Features:**
- **Global Eligibility Rules**: Default payroll frequency and waiting periods
- **Enrollment Periods**: Multiple enrollment period types (Open Enrollment, New Hire, QLE)
- **Company Preferences**: Benefit change policies and default coverage levels
- **Fallback Behavior**: System defaults when settings are missing

**Business Rules:**
- One settings document per company (unique constraint on companyId)
- Payroll frequency defaults to 'Monthly' if not specified
- Multiple enrollment periods of same type allowed
- Inactive periods (isActive: false) are ignored in eligibility checks
- Missing settings trigger fallback behavior with system defaults
- Employer-controlled settings provide context for broker plan assignments

#### **3.5.1 Complete Interface**

```typescript
interface CompanyBenefitsSettings {
  // Identification
  _id?: string;
  companyId: string;                 // Reference to Company (unique)

  // Global Eligibility Rules
  globalEligibility: {
    payrollFrequency: string;        // "Weekly", "Biweekly", "Semi-Monthly", "Monthly"
    firstPayrollDate?: Date;         // First payroll date
    defaultWaitingPeriod?: string;   // Default waiting period rule
    rehirePolicy?: string;           // Policy for rehired employees
    allowCustomPayrollFrequency?: boolean; // Allow employee overrides
  };

  // Enrollment Periods
  enrollmentPeriods: Array<{
    _id?: string;
    type: string;                    // "Open Enrollment", "New Hire", "Qualifying Life Event"
    startDate: Date;                 // When enrollment period starts
    endDate: Date;                   // When enrollment period ends
    coverageStartDate: Date;         // When coverage begins
    coverageEndDate: Date;           // When coverage ends
    description?: string;            // Human-readable description
    isActive: boolean;               // Whether this period is active
  }>;

  // Company Preferences
  companyPreferences?: {
    allowEmployeeBenefitChanges?: boolean;    // Allow mid-year changes
    requireBeneficiaryDesignation?: boolean;  // Require beneficiary info
    enableDependentVerification?: boolean;    // Require dependent verification
    autoEnrollNewHires?: boolean;             // Auto-enroll new hires in default plans
    defaultCoverageLevel?: string;            // Default coverage tier
  };

  // System Fields
  isActive: boolean;                         // Whether settings are active

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}
```

#### **3.5.2 Mongoose Schema**

```typescript
const CompanyBenefitsSettingsSchema = new mongoose.Schema({
  // Company Reference (unique constraint)
  companyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: true,
    unique: true
  },

  // Global eligibility rules
  globalEligibility: {
    payrollFrequency: {
      type: String,
      enum: PAYROLL_FREQUENCIES,
      default: 'Monthly',
      required: true
    },
    firstPayrollDate: { type: Date },
    defaultWaitingPeriod: { type: String },
    rehirePolicy: { type: String },
    allowCustomPayrollFrequency: { type: Boolean, default: false }
  },

  // Enrollment periods
  enrollmentPeriods: [{
    type: {
      type: String,
      enum: ENROLLMENT_PERIOD_TYPES,
      required: true
    },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    coverageStartDate: { type: Date, required: true },
    coverageEndDate: { type: Date, required: true },
    description: { type: String },
    isActive: { type: Boolean, default: true }
  }],

  // Company preferences
  companyPreferences: {
    allowEmployeeBenefitChanges: { type: Boolean, default: true },
    requireBeneficiaryDesignation: { type: Boolean, default: false },
    enableDependentVerification: { type: Boolean, default: false },
    autoEnrollNewHires: { type: Boolean, default: false },
    defaultCoverageLevel: { type: String }
  },

  // System fields
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

// Indexes
// Note: companyId already has unique index from schema definition
CompanyBenefitsSettingsSchema.index({ isActive: 1 });
CompanyBenefitsSettingsSchema.index({ 'enrollmentPeriods.startDate': 1, 'enrollmentPeriods.endDate': 1 });
```

#### **3.5.3 Business Logic Methods**

```typescript
// Settings Management
public static getCompanySettings(companyId: string): Promise<CompanyBenefitsSettings | null>
public static createDefaultSettings(companyId: string): Promise<CompanyBenefitsSettings>
public static updateSettings(companyId: string, updateData: any, userId: string): Promise<CompanyBenefitsSettings>

// Enrollment Period Management
public static getActiveEnrollmentPeriods(companyId: string, date?: Date): Promise<any[]>
public static getEnrollmentPeriodByType(companyId: string, type: string, date?: Date): Promise<any | null>
public static isWithinEnrollmentPeriod(companyId: string, date?: Date): Promise<{withinPeriod: boolean, periods?: any[]}>

// Fallback Behavior
public static getEffectivePayrollFrequency(companyId: string, employeeId?: string): Promise<string>
public static getEffectiveWaitingPeriod(companyId: string): Promise<{enabled: boolean, days: number, rule: string}>
public static getEffectiveSettings(companyId: string): Promise<CompanyBenefitsSettings>

// Validation Methods
public static validateEnrollmentPeriods(periods: any[]): Promise<{valid: boolean, errors?: string[]}>
public static validateCompanyPreferences(preferences: any): Promise<{valid: boolean, errors?: string[]}>
public static validateNotificationSettings(settings: any): Promise<{valid: boolean, errors?: string[]}>

// Integration Support
public static getCarrierIntegrations(companyId: string): Promise<any[]>
public static updateCarrierIntegration(companyId: string, carrierId: string, integrationData: any): Promise<void>
public static isCarrierIntegrationEnabled(companyId: string, carrierId: string): Promise<boolean>

// Business Rule Helpers
public static shouldAutoEnrollNewHire(companyId: string, employeeData: any): Promise<boolean>
public static getMaxDependentAge(companyId: string): Promise<number>
public static isSSNRequiredForDependents(companyId: string): Promise<boolean>
public static canEmployeeWaiveCoverage(companyId: string): Promise<boolean>
```

---

## 4. API Specifications

### 4.1 Carrier APIs

The Carrier APIs enable management of insurance carriers with comprehensive CRUD operations and role-based access control.

#### **✅ IMPLEMENTED APIs (12 Total):**

**Core CRUD Operations:**
```http
POST   /api/pre-enrollment/carriers/create                    # Create carrier
GET    /api/pre-enrollment/carriers/assignable               # Get assignable carriers
GET    /api/pre-enrollment/carriers                          # Get carriers (role-based filtering)
GET    /api/pre-enrollment/carriers/:carrierId               # Get carrier by ID
PUT    /api/pre-enrollment/carriers/:carrierId               # Update carrier
DELETE /api/pre-enrollment/carriers/:carrierId               # Delete carrier
```

**Status Management:**
```http
POST   /api/pre-enrollment/carriers/:carrierId/activate      # Activate carrier
POST   /api/pre-enrollment/carriers/:carrierId/deactivate    # Deactivate carrier
POST   /api/pre-enrollment/carriers/:carrierId/archive       # Archive carrier
```

**Validation and Reference APIs:**
```http
POST   /api/pre-enrollment/carriers/:carrierId/validate      # Validate carrier compatibility
GET    /api/pre-enrollment/carriers/:carrierId/can-edit      # Check edit permissions
GET    /api/pre-enrollment/carriers/:carrierId/can-delete    # Check delete permissions
GET    /api/pre-enrollment/carriers/:carrierId/dependent-plans # Get dependent plans
```

#### **1. Create Carrier**
```http
POST /api/pre-enrollment/carriers/create
```

**Description:** Create carriers. Super admins create system carriers by default, brokers create broker-specific carriers.

**Authentication:** Required (JWT)
**Authorization:** Super Admin or Broker (`isSuperAdmin: true` OR `isBroker: true`)
**Headers:** `user-id: <user_id>`

**Behavior:**
- **Super Admin**: Creates system carriers (`isSystemCarrier: true`) while maintaining broker identity
- **Broker**: Creates broker-specific carriers (`isSystemCarrier: false`)
- **Status**: All new carriers start with `status: 'Active'` and `isActive: true`

**Required Fields:**
- `carrierName` (string): Name of the carrier
- `carrierCode` (string): Unique carrier code within broker scope

**Validation:**
- Only super admins can create system carriers (`isSystemCarrier: true`)
- Carrier code must be unique within broker scope (broker-scoped uniqueness)
- Supported plan types must be valid values from PLAN_TYPES constant
- Supported coverage types must be valid values from PRE_ENROLLMENT_COVERAGE_TYPES constant

**Request Body:**
```json
{
  "carrierName": "Blue Cross Blue Shield",
  "carrierCode": "BCBS",
  "displayName": "BCBS",
  "contactInfo": {
    "phone": "******-555-0123",
    "email": "<EMAIL>",
    "website": "https://bcbs.com",
    "supportEmail": "<EMAIL>",
    "claimsPhone": "******-555-0124",
    "memberServicesPhone": "******-555-0125"
  },
  "supportedPlanTypes": ["PPO", "HMO", "HDHP"],
  "supportedCoverageTypes": ["Health Insurance", "Ancillary Benefits"],
  "supportedCoverageSubTypes": ["Medical", "Dental", "Vision"],
  "integration": {
    "ediCapable": true,
    "apiEndpoint": "https://api.bcbs.com/v1",
    "apiVersion": "1.0",
    "authMethod": "API_KEY",
    "dataFormat": "JSON"
  },
  "licenseStates": ["CA", "NY", "TX"],
  "amRating": "A++",
  "networkName": "Choice Plus Network"
}
```

**Success Response (201):**
```json
{
  "message": "Carrier created successfully",
  "carrier": {
    "_id": "carrier_id_here",
    "carrierName": "Blue Cross Blue Shield",
    "carrierCode": "BCBS",
    "displayName": "BCBS",
    "brokerId": "broker_id_here",
    "brokerageId": "brokerage_id_here",
    "isSystemCarrier": false,
    "contactInfo": {
      "phone": "******-555-0123",
      "email": "<EMAIL>",
      "website": "https://bcbs.com",
      "supportEmail": "<EMAIL>",
      "claimsPhone": "******-555-0124",
      "memberServicesPhone": "******-555-0125"
    },
    "supportedPlanTypes": ["PPO", "HMO", "HDHP"],
    "supportedCoverageTypes": ["Health Insurance", "Ancillary Benefits"],
    "supportedCoverageSubTypes": ["Medical", "Dental", "Vision"],
    "integration": {
      "ediCapable": true,
      "apiEndpoint": "https://api.bcbs.com/v1",
      "apiVersion": "1.0",
      "authMethod": "API_KEY",
      "dataFormat": "JSON"
    },
    "licenseStates": ["CA", "NY", "TX"],
    "amRating": "A++",
    "networkName": "Choice Plus Network",
    "isActive": true,
    "isActivated": true,
    "createdAt": "2024-01-01T12:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Missing required fields
{
  "error": "Carrier name and carrier code are required"
}

// Permission denied for system carrier
{
  "error": "Only super admins can create system carriers"
}

// Broker-scoped uniqueness violation
{
  "error": "Carrier code 'BCBS' already exists for this broker"
}

// Invalid plan type
{
  "error": "Invalid plan type: InvalidType"
}

// Invalid coverage type
{
  "error": "Invalid coverage type: InvalidCoverage"
}

// User not found
{
  "error": "User not found"
}

// Missing user ID
{
  "error": "User ID is required"
}
```

#### **2. Get Assignable Carriers (Active Only)**
```http
GET /api/pre-enrollment/carriers/assignable
```

**Description:** Get carriers available for plan assignment (Active status only).

**Authentication:** Required (JWT)
**Authorization:** All authenticated users
**Headers:** `user-id: <user_id>`

**Access Control:**
- **Super Admin**: Can see all assignable carriers (system + all broker carriers)
- **Broker**: Can see system carriers + their own assignable carriers
- **Company Admin/Employee**: Can see system carriers only (read-only)

**Business Logic:**
- Only returns carriers with `status: 'Active'`
- Used when creating/updating plans to ensure only operational carriers are selectable
- Inactive/Archived carriers are excluded from assignment operations

**Success Response (200):**
```json
{
  "carriers": [
    {
      "_id": "carrier_id_here",
      "carrierName": "Blue Cross Blue Shield",
      "carrierCode": "BCBS",
      "displayName": "BCBS",
      "isSystemCarrier": false,
      "supportedCoverageTypes": ["Health Insurance", "Ancillary Benefits"],
      "supportedCoverageSubTypes": ["Medical", "Dental", "Vision"],
      "supportedPlanTypes": ["PPO", "HMO", "HDHP"],
      "amRating": "A++",
      "licenseStates": ["CA", "NY", "TX"],
      "networkName": "Choice Plus Network",
      "isActive": true,
      "isActivated": true,
      "createdAt": "2024-01-01T12:00:00.000Z"
    }
  ],
  "count": 1
}
```

**Error Responses:**
```json
// User not found
{
  "error": "User not found"
}

// Missing user ID
{
  "error": "User ID is required"
}
```

#### **3. Get All Carriers (Role-Based Access)**
```http
GET /api/pre-enrollment/carriers
```

**Description:** Get all carriers based on user role and access permissions with comprehensive filtering.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users
**Headers:** `user-id: <user_id>`

**Query Parameters (All Optional):**
- `status` (string): Filter by carrier status (`Active`, `Inactive`, `Archived`)
- `isSystemCarrier` (string): Filter by system carrier flag (`'true'` | `'false'`)
- `planType` (string): Filter by supported plan type (`PPO`, `HMO`, `HDHP`, etc.)
- `coverageType` (string): Filter by supported coverage type
- `coverageSubTypes` (string): Filter by supported coverage subtypes (comma-separated)
- `state` (string): Filter by license state
- `ediCapable` (string): Filter by EDI capability (`'true'` | `'false'`)
- `amRating` (string): Filter by A.M. Best rating
- `page` (number): Page number for pagination (default: 1)
- `limit` (number): Number of results per page (default: 20)

**Access Control:**
- **Super Admin**: Can see all carriers (system + all broker carriers)
- **Broker**: Can see system carriers + their own carriers
- **Company Admin/Employee**: Can see system carriers only (read-only)

**Success Response (200):**
```json
{
  "carriers": [
    {
      "_id": "carrier_id_here",
      "carrierName": "Blue Cross Blue Shield",
      "carrierCode": "BCBS",
      "displayName": "BCBS",
      "isSystemCarrier": false,
      "supportedCoverageTypes": ["Health Insurance", "Ancillary Benefits"],
      "supportedCoverageSubTypes": ["Medical", "Dental", "Vision"],
      "supportedPlanTypes": ["PPO", "HMO", "HDHP"],
      "amRating": "A++",
      "licenseStates": ["CA", "NY", "TX"],
      "networkName": "Choice Plus Network",
      "contactInfo": {
        "phone": "******-555-0123",
        "email": "<EMAIL>",
        "website": "https://bcbs.com",
        "supportEmail": "<EMAIL>",
        "claimsPhone": "******-555-0124",
        "memberServicesPhone": "******-555-0125"
      },
      "integration": {
        "ediCapable": true,
        "apiEndpoint": "https://api.bcbs.com/v1",
        "apiVersion": "1.0",
        "authMethod": "API_KEY",
        "dataFormat": "JSON"
      },
      "isActive": true,
      "isActivated": true,
      "brokerId": "broker_id_here",
      "brokerageId": "brokerage_id_here",
      "createdAt": "2024-01-01T12:00:00.000Z",
      "updatedAt": "2024-01-01T12:00:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 1,
    "totalCarriers": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

**Error Responses:**
```json
// User not found
{
  "error": "User not found"
}

// Missing user ID
{
  "error": "User ID is required"
}
```

#### **4. Validate Plan-Carrier Compatibility**
```http
POST /api/pre-enrollment/carriers/:carrierId/validate
```

**Description:** Validate if a carrier supports specific plan criteria for compatibility checking.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID to validate

**Request Body:**
```json
{
  "planType": "PPO",
  "coverageType": "Health Insurance",
  "coverageSubTypes": ["Medical"]
}
```

**Success Response (200) - Compatible:**
```json
{
  "carrierId": "carrier_id_here",
  "carrierName": "Blue Cross Blue Shield",
  "validation": {
    "isCompatible": true,
    "errors": []
  },
  "checkedCriteria": {
    "planType": "PPO",
    "coverageType": "Health Insurance",
    "coverageSubTypes": ["Medical"]
  }
}
```

**Success Response (200) - Incompatible:**
```json
{
  "carrierId": "carrier_id_here",
  "carrierName": "Blue Cross Blue Shield",
  "validation": {
    "isCompatible": false,
    "errors": [
      "Carrier \"Blue Cross Blue Shield\" does not support plan type \"HMO\"",
      "Carrier \"Blue Cross Blue Shield\" does not support coverage subtype \"Vision\""
    ]
  },
  "checkedCriteria": {
    "planType": "HMO",
    "coverageType": "Ancillary Benefits",
    "coverageSubTypes": ["Vision"]
  }
}
```

**Error Responses:**
```json
// Carrier not found
{
  "error": "Carrier not found"
}

// Access denied
{
  "error": "Carrier not found or access denied"
}

// Missing request body
{
  "error": "Request body is required"
}

// User not found
{
  "error": "User not found"
}
```

#### **5. Activate Carrier**
```http
POST /api/pre-enrollment/carriers/:carrierId/activate
```

**Description:** Activate a carrier (change status to Active).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (carrier owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID to activate

**Success Response (200):**
```json
{
  "message": "Carrier activated successfully",
  "carrier": {
    "_id": "carrier_id_here",
    "carrierName": "Blue Cross Blue Shield",
    "status": "Active",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### **6. Deactivate Carrier**
```http
POST /api/pre-enrollment/carriers/:carrierId/deactivate
```

**Description:** Deactivate a carrier (change status to Inactive).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (carrier owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID to deactivate

**Success Response (200):**
```json
{
  "message": "Carrier deactivated successfully",
  "carrier": {
    "_id": "carrier_id_here",
    "carrierName": "Blue Cross Blue Shield",
    "status": "Inactive",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### **7. Get Carrier By ID**
```http
GET /api/pre-enrollment/carriers/:carrierId
```

**Description:** Retrieve a specific carrier by ID with role-based access control.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID

**Access Control:**
- **Super Admin**: Can access any carrier
- **Broker**: Can access system carriers + their own carriers
- **Company Admin/Employee**: Can access system carriers only (read-only)

**Success Response (200):**
```json
{
  "carrier": {
    "_id": "carrier_id_here",
    "carrierName": "Blue Cross Blue Shield",
    "carrierCode": "BCBS",
    "displayName": "BCBS",
    "isSystemCarrier": false,
    "contactInfo": {
      "phone": "******-555-0123",
      "email": "<EMAIL>",
      "website": "https://bcbs.com"
    },
    "supportedPlanTypes": ["PPO", "HMO", "HDHP"],
    "supportedCoverageTypes": ["Health Insurance", "Ancillary Benefits"],
    "supportedCoverageSubTypes": ["Medical", "Dental", "Vision"],
    "integration": {
      "ediCapable": true,
      "apiEndpoint": "https://api.bcbs.com/v1"
    },
    "licenseStates": ["CA", "NY", "TX"],
    "amRating": "A++",
    "networkName": "Choice Plus Network",
    "status": "Active",
    "isActive": true,
    "brokerId": "broker_id_here",
    "brokerageId": "brokerage_id_here",
    "createdAt": "2024-01-01T12:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Carrier not found
{
  "error": "Carrier not found or access denied"
}

// Invalid carrier ID format
{
  "error": "Invalid carrier ID format"
}

// Access denied to non-system carrier
{
  "error": "Access denied to non-system carrier"
}
```

#### **8. Update Carrier**
```http
PUT /api/pre-enrollment/carriers/:carrierId
```

**Description:** Update an existing carrier with validation and reference checks.

**Authentication:** Required (JWT)
**Authorization:** Super Admin (all carriers), Broker (own carriers only)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID

**Business Rules:**
- Can only update if carrier has ≤1 plan reference
- Cannot change `isSystemCarrier` flag
- Cannot change `carrierCode` if referenced by plans
- Broker-scoped uniqueness validation applies
- Brokers cannot update system carriers (except Super Admins)

**Request Body (all fields optional):**
```json
{
  "carrierName": "Updated Carrier Name",
  "displayName": "Updated Display Name",
  "contactInfo": {
    "phone": "******-555-9999",
    "email": "<EMAIL>",
    "website": "https://updated-carrier.com"
  },
  "supportedPlanTypes": ["PPO", "HMO"],
  "supportedCoverageTypes": ["Health Insurance"],
  "supportedCoverageSubTypes": ["Medical"],
  "integration": {
    "ediCapable": false,
    "apiEndpoint": "https://api.updated-carrier.com"
  },
  "licenseStates": ["CA", "NY"],
  "amRating": "A+",
  "networkName": "Updated Network"
}
```

**Success Response (200):**
```json
{
  "message": "Carrier updated successfully",
  "carrier": {
    "_id": "carrier_id_here",
    "carrierName": "Updated Carrier Name",
    "carrierCode": "BCBS",
    "displayName": "Updated Display Name",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Cannot edit with multiple references
{
  "error": "Cannot edit carrier with multiple plan references",
  "referenceCount": 5,
  "referencedBy": ["plan1", "plan2", "plan3"]
}

// Brokers cannot update system carriers
{
  "error": "Brokers cannot update system carriers"
}

// Permission denied
{
  "error": "Only super admins and brokers can update carriers"
}
```

#### **9. Can Edit Carrier**
```http
GET /api/pre-enrollment/carriers/:carrierId/can-edit
```

**Description:** Check if a carrier can be edited based on plan references.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID

**Success Response (200) - Can Edit:**
```json
{
  "carrierId": "carrier_id_here",
  "carrierName": "Blue Cross Blue Shield",
  "canEdit": true,
  "referenceCount": 1,
  "referencedBy": ["plan_id_1"],
  "reason": "Carrier has 1 plan reference (≤1 allowed)"
}
```

**Success Response (200) - Cannot Edit:**
```json
{
  "carrierId": "carrier_id_here",
  "carrierName": "Blue Cross Blue Shield",
  "canEdit": false,
  "referenceCount": 3,
  "referencedBy": ["plan_id_1", "plan_id_2", "plan_id_3"],
  "reason": "Carrier has 3 plan references (>1 not allowed)"
}
```

#### **10. Delete Carrier**
```http
DELETE /api/pre-enrollment/carriers/:carrierId
```

**Description:** Permanently delete a carrier with comprehensive validation.

**Authentication:** Required (JWT)
**Authorization:** Super Admin (all carriers), Broker (own carriers only)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID

**Business Rules:**
- Can only delete if carrier has no plan references
- Cannot delete system carriers (except Super Admins)
- Permanent deletion (not soft delete)

**Success Response (200):**
```json
{
  "message": "Carrier deleted successfully",
  "deletedCarrierId": "carrier_id_here"
}
```

**Error Responses:**
```json
// Cannot delete with plan references
{
  "error": "Cannot delete carrier with plan references",
  "referenceCount": 3,
  "referencedBy": ["plan1", "plan2", "plan3"]
}

// Permission denied
{
  "error": "Only super admins and brokers can delete carriers"
}
```

#### **11. Archive Carrier**
```http
POST /api/pre-enrollment/carriers/:carrierId/archive
```

**Description:** Archive a carrier (change status to Archived).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (carrier owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID

**Success Response (200):**
```json
{
  "message": "Carrier archived successfully",
  "carrier": {
    "_id": "carrier_id_here",
    "carrierName": "Blue Cross Blue Shield",
    "status": "Archived",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### **12. Can Delete Carrier**
```http
GET /api/pre-enrollment/carriers/:carrierId/can-delete
```

**Description:** Check if a carrier can be deleted based on plan references.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID

**Success Response (200) - Can Delete:**
```json
{
  "carrierId": "carrier_id_here",
  "carrierName": "Blue Cross Blue Shield",
  "canDelete": true,
  "referenceCount": 0,
  "referencedBy": [],
  "reason": "Carrier has no plan references"
}
```

**Success Response (200) - Cannot Delete:**
```json
{
  "carrierId": "carrier_id_here",
  "carrierName": "Blue Cross Blue Shield",
  "canDelete": false,
  "referenceCount": 2,
  "referencedBy": ["plan_id_1", "plan_id_2"],
  "reason": "Carrier has 2 plan references"
}
```

#### **13. Get Dependent Plans**
```http
GET /api/pre-enrollment/carriers/:carrierId/dependent-plans
```

**Description:** Get all plans that reference this carrier.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `carrierId` (string, required): Carrier ID

**Success Response (200):**
```json
{
  "carrierId": "carrier_id_here",
  "carrierName": "Blue Cross Blue Shield",
  "dependentPlans": [
    {
      "_id": "plan_id_1",
      "planName": "Gold PPO Health Plan",
      "planCode": "GOLD_PPO_2024",
      "status": "Active",
      "createdAt": "2024-01-01T12:00:00.000Z"
    },
    {
      "_id": "plan_id_2",
      "planName": "Silver HMO Plan",
      "planCode": "SILVER_HMO_2024",
      "status": "Active",
      "createdAt": "2024-01-01T12:00:00.000Z"
    }
  ],
  "count": 2
}
```

**Success Response (200) - No Dependencies:**
```json
{
  "carrierId": "carrier_id_here",
  "carrierName": "Blue Cross Blue Shield",
  "dependentPlans": [],
  "count": 0
}
```

### 4.2 Plan APIs

The Plan APIs enable management of insurance plan templates with comprehensive CRUD operations and role-based access control.

#### **✅ IMPLEMENTED APIs (14 Total):**

**Core CRUD Operations:**
```http
POST   /api/pre-enrollment/plans                             # Create plan
GET    /api/pre-enrollment/plans/assignable                  # Get assignable plans
GET    /api/pre-enrollment/plans                             # Get plans (role-based filtering)
GET    /api/pre-enrollment/plans/templates                   # Get template plans
GET    /api/pre-enrollment/plans/:planId                     # Get plan by ID
PUT    /api/pre-enrollment/plans/:planId                     # Update plan
DELETE /api/pre-enrollment/plans/:planId                     # Delete plan
```

**Status Management:**
```http
POST   /api/pre-enrollment/plans/:planId/activate            # Activate plan (Draft → Active)
POST   /api/pre-enrollment/plans/:planId/convert-to-draft    # Convert to draft (Active → Draft)
POST   /api/pre-enrollment/plans/:planId/archive             # Archive plan (Any → Archived)
POST   /api/pre-enrollment/plans/:planId/duplicate           # Duplicate plan
```

**Document Management:**
```http
POST   /api/pre-enrollment/plans/:planId/documents           # Upload documents
```

**Validation and Reference APIs:**
```http
GET    /api/pre-enrollment/plans/:planId/can-edit            # Check edit permissions
GET    /api/pre-enrollment/plans/:planId/can-delete          # Check delete permissions
GET    /api/pre-enrollment/plans/:planId/dependent-assignments # Get dependent assignments
```

#### **1. Create Plan**
```http
POST /api/pre-enrollment/plans
```

**Description:** Create plans. Super admins create templates by default, brokers create broker-specific plans.

**Authentication:** Required (JWT)
**Authorization:** Super Admin or Broker (`isSuperAdmin: true` OR `isBroker: true`)
**Headers:** `user-id: <user_id>`

**Behavior:**
- **Super Admin**: Creates template plans (`isTemplate: true`) while maintaining broker identity
- **Broker**: Creates broker-specific plans (`isTemplate: false`)
- **Status**: All new plans start with `status: 'Active'` and `isActive: true`
- **Duplication**: Duplicated plans start with `status: 'Active'` and are immediately usable

**Required Fields:**
- `planName` (string): Name of the plan
- `coverageType` (string): Must be from PRE_ENROLLMENT_COVERAGE_TYPES
- `coverageSubTypes` (string[]): Must be from PRE_ENROLLMENT_COVERAGE_SUBTYPES
- `description` (string): Plan description

**Validation:**
- Only super admins can create template plans (`isTemplate: true`)
- Plan name must be unique within broker scope (broker-scoped uniqueness)
- Coverage type and subtypes must be valid values from pre-enrollment constants
- Plan type must be valid value from PLAN_TYPES constant if provided

**Request Body:**
```json
{
  "planName": "Gold PPO Health Plan",
  "planCode": "GOLD_PPO_2024",
  "coverageType": "Health Insurance",
  "coverageSubTypes": ["Medical"],
  "planType": "PPO",
  "metalTier": "Gold",
  "description": "Comprehensive health plan with low deductibles and wide network",
  "highlights": ["Low deductible", "Wide network", "Preventive care covered"],
  "informativeLinks": ["https://carrier.com/plan-details"],
  "benefitDetails": {
    "deductibleIndividual": 500,
    "deductibleFamily": 1500,
    "pcpCopay": 25,
    "specialistCopay": 50,
    "emergencyRoomCopay": 200,
    "urgentCareCopay": 75,
    "coinsurance": "20%",
    "outOfPocketMaxIndividual": 3000,
    "outOfPocketMaxFamily": 6000,
    "preventiveCareCoinsurance": "0%",
    "prescriptionCoverage": {
      "generic": 10,
      "brandName": 30,
      "specialty": 100
    },
    "additionalBenefits": ["Telehealth", "Mental health coverage"]
  },
  "carrierId": "carrier_id_here",
  "carrierPlanId": "CARRIER_PLAN_123"
}
```

**Success Response (201):**
```json
{
  "message": "Plan created successfully",
  "plan": {
    "_id": "plan_id_here",
    "planName": "Gold PPO Health Plan",
    "planCode": "GOLD_PPO_2024",
    "coverageType": "Health Insurance",
    "coverageSubTypes": ["Medical"],
    "planType": "PPO",
    "metalTier": "Gold",
    "description": "Comprehensive health plan with low deductibles and wide network",
    "highlights": ["Low deductible", "Wide network", "Preventive care covered"],
    "informativeLinks": ["https://carrier.com/plan-details"],
    "benefitDetails": {
      "deductibleIndividual": 500,
      "deductibleFamily": 1500,
      "pcpCopay": 25,
      "specialistCopay": 50,
      "emergencyRoomCopay": 200,
      "urgentCareCopay": 75,
      "coinsurance": "20%",
      "outOfPocketMaxIndividual": 3000,
      "outOfPocketMaxFamily": 6000,
      "preventiveCareCoinsurance": "0%",
      "prescriptionCoverage": {
        "generic": 10,
        "brandName": 30,
        "specialty": 100
      },
      "additionalBenefits": ["Telehealth", "Mental health coverage"]
    },
    "carrierId": "carrier_id_here",
    "carrierPlanId": "CARRIER_PLAN_123",
    "brokerId": "broker_id_here",
    "brokerageId": "brokerage_id_here",
    "isTemplate": false,
    "status": "Active",
    "isActive": true,
    "isActivated": true,
    "createdAt": "2024-01-01T12:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Missing required fields
{
  "error": "Plan name, coverage type, coverage subtypes, and description are required"
}

// Permission denied for template plan
{
  "error": "Only super admins can create template plans"
}

// Broker-scoped uniqueness violation
{
  "error": "Plan name 'Gold PPO Health Plan' already exists for this broker"
}

// Invalid coverage type
{
  "error": "Invalid coverage type: InvalidCoverage"
}

// Invalid coverage subtype
{
  "error": "Invalid coverage subtype: InvalidSubtype"
}

// User not found
{
  "error": "User not found"
}

// Missing user ID
{
  "error": "User ID is required"
}
```

**2. Get Assignable Plans**
```http
GET /api/pre-enrollment/plans/assignable
```

**Purpose**: Get plans available for company assignment (Active status only)

**Authentication**: Required (JWT)
**Authorization**: SuperAdmins and Brokers only

**Query Parameters (Optional)**:
- `includeCarrierData` (string): Include complete carrier data (`'true'` | `'false'`, default: `'true'`)

**Access Control**:
- **SuperAdmins**: Can see all assignable plans
- **Brokers**: Can see their own assignable plans only
- **Company Admin/Employee**: ❌ Access denied

**Response**:
```typescript
{
  plans: Array<{
    _id: string;
    planName: string;
    planCode: string;
    status: "Active";
    isTemplate: false;
    coverageType: string;
    coverageSubTypes: string[];
    planType?: string;
    metalTier?: string;
    description: string;
    brokerId?: string;
    carrierId?: string;
    isActivated: boolean;
    createdAt: string;
    carrierData?: {                    // ✅ NEW: Included when includeCarrierData=true
      _id: string;
      carrierName: string;
      carrierCode: string;
      amBestRating?: string;
      financialStrength?: string;
      supportedCoverageTypes: string[];
      integrationCapabilities: string[];
      contactInfo: {
        phone?: string;
        email?: string;
        website?: string;
      };
      status: string;
      isActive: boolean;
    };
  }>;
  count: number;
}
```

**3. Get All Plans (Role-Based Access with Enhanced Filtering)**
```http
GET /api/pre-enrollment/plans
```

**Purpose**: Get all plans based on user role and access permissions with comprehensive filtering

**Authentication**: Required (JWT)
**Authorization**: All authenticated users

**Query Parameters (All Optional)**:
- `status` (string): Filter by plan status (`Draft`, `Active`, `Archived`)
- `coverageType` (string): Filter by coverage type
- `planType` (string): Filter by plan type (`PPO`, `HMO`, `HDHP`)
- `isTemplate` (string): Filter by template flag (`'true'` | `'false'`)
- `planName` (string): Filter by plan name (partial or exact match)
- `planCode` (string): Filter by plan code (partial or exact match)
- `coverageSubtype` (string): Filter by coverage subtype
- `strict` (string): `'true'` | `'false'` (default: `'false'`) - Enable strict filtering
- `includeCarrierData` (string): Include complete carrier data (`'true'` | `'false'`, default: `'true'`)
- `page` (number): Page number for pagination (optional, starts from 1)
- `limit` (number): Items per page (optional, 1-100, default: no pagination)

**Access Control**:
- **SuperAdmins**: Can see all plans (system templates + all broker plans)
- **Brokers**: Can see system templates + their own plans
- **Company Admin/Employee**: Can see system templates only (read-only)

**Filtering Modes:**
- **Normal mode** (`strict=false`): Case-insensitive partial matching for planName and planCode
- **Strict mode** (`strict=true`): Case-insensitive exact matching for planName and planCode

**Response Formats:**

**Non-Paginated Response (default):**
```json
{
  "plans": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "planName": "Gold PPO Health Plan",
      "planCode": "GOLD_PPO_2024",
      "coverageType": "Health Insurance",
      "coverageSubTypes": ["Medical", "Prescription"],
      "planType": "PPO",
      "metalTier": "Gold",
      "carrierId": "60f7b3b3b3b3b3b3b3b3b3b6",
      "status": "Active",
      "isTemplate": false,
      "brokerId": "60f7b3b3b3b3b3b3b3b3b3b7",
      "description": "Comprehensive health coverage with nationwide network",
      "carrierData": {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b6",
        "carrierName": "Blue Cross Blue Shield",
        "carrierCode": "BCBS",
        "amBestRating": "A+",
        "financialStrength": "Excellent",
        "supportedCoverageTypes": ["Health Insurance", "Dental", "Vision"],
        "integrationCapabilities": ["API", "EDI", "Real-time"],
        "contactInfo": {
          "phone": "1-************",
          "email": "<EMAIL>",
          "website": "https://www.bcbs.com"
        },
        "status": "Active",
        "isActive": true
      }
    }
  ],
  "count": 150
}
```

**Paginated Response (when page & limit provided):**
```json
{
  "plans": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "planName": "Gold PPO Health Plan",
      "carrierData": {
        "carrierName": "Blue Cross Blue Shield",
        "carrierCode": "BCBS"
      }
    }
  ],
  "pagination": {
    "currentPage": 2,
    "totalPages": 8,
    "totalPlans": 150,
    "hasNext": true,
    "hasPrev": true
  }
}
```

**🎯 OPTIMIZATION:** All filtering and pagination now happens at database level for maximum performance.

**4. Duplicate Plan**
```http
POST /api/pre-enrollment/plans/:planId/duplicate
```

**Purpose**: Duplicate a system template or existing plan to create a broker-specific plan

**Authentication**: Required (JWT)
**Authorization**: Brokers only

**Request Body**:
```typescript
{
  planName?: string;                     // Optional: New plan name (defaults to "Copy of [Original Name]")
  planCode?: string;                     // Optional: New plan code (auto-generated if not provided)
  description?: string;                  // Optional: New description
  customizations?: {                     // Optional: Plan customizations
    highlights?: string[];
    benefitDetails?: object;
    informativeLinks?: string[];
  };
}
```

**Response**:
```typescript
{
  message: string;
  plan: {
    _id: string;
    planName: string;
    planCode: string;
    coverageType: string;
    coverageSubTypes: string[];
    description: string;
    brokerId: string;                    // Broker who duplicated the plan
    brokerageId: string;
    isTemplate: false;                   // Always false for duplicated plans
    status: "Draft";                     // Always Draft for duplicated plans
    createdAt: string;
  }
}
```

#### **2. Get Assignable Plans**
```http
GET /api/pre-enrollment/plans/assignable
```

**Description:** Get plans available for assignment (Active status only, excludes templates).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker
**Headers:** `user-id: <user_id>`

**Query Parameters (Optional)**:
- `includeCarrierData` (string): Include complete carrier data (`'true'` | `'false'`, default: `'true'`)

**Access Control:**
- **Super Admin**: Can see all assignable plans
- **Broker**: Can see their own assignable plans only
- **Company Admin/Employee**: ❌ Access denied

**Success Response (200):**
```json
{
  "plans": [
    {
      "_id": "plan_id_here",
      "planName": "Gold PPO Health Plan",
      "planCode": "GOLD_PPO_2024",
      "coverageType": "Health Insurance",
      "coverageSubTypes": ["Medical"],
      "planType": "PPO",
      "metalTier": "Gold",
      "description": "Comprehensive health plan",
      "carrierId": "carrier_id_here",
      "status": "Active",
      "isTemplate": false,
      "brokerId": "broker_id_here",
      "createdAt": "2024-01-01T12:00:00.000Z",
      "carrierData": {
        "_id": "carrier_id_here",
        "carrierName": "Blue Cross Blue Shield",
        "carrierCode": "BCBS",
        "amBestRating": "A+",
        "financialStrength": "Excellent",
        "supportedCoverageTypes": ["Health Insurance", "Dental", "Vision"],
        "integrationCapabilities": ["API", "EDI", "Real-time"],
        "contactInfo": {
          "phone": "1-************",
          "email": "<EMAIL>",
          "website": "https://www.bcbs.com"
        },
        "status": "Active",
        "isActive": true
      }
    }
  ],
  "count": 1
}
```

**Error Responses:**
```json
// Access denied
{
  "error": "Only super admins and brokers can access assignable plans"
}
```


      "brokerId": "broker_id_here",
      "createdAt": "2024-01-01T12:00:00.000Z",
      "updatedAt": "2024-01-01T12:00:00.000Z"
    }
  ],
  "count": 150
}
```

**Paginated Response (when page & limit provided):**
```json
{
  "plans": [
    {
      "_id": "plan_id_here",
      "planName": "Gold PPO Health Plan",
      "planCode": "GOLD_PPO_2024",
      "coverageType": "Health Insurance",
      "coverageSubTypes": ["Medical"],
      "planType": "PPO",
      "metalTier": "Gold",
      "description": "Comprehensive health plan",
      "highlights": ["Low deductible", "Wide network"],
      "benefitDetails": {
        "deductibleIndividual": 500,
        "deductibleFamily": 1500,
        "pcpCopay": 25
      },
      "carrierId": "carrier_id_here",
      "status": "Active",
      "isTemplate": false,
      "brokerId": "broker_id_here",
      "createdAt": "2024-01-01T12:00:00.000Z",
      "updatedAt": "2024-01-01T12:00:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 2,
    "totalPages": 8,
    "totalPlans": 150,
    "hasNext": true,
    "hasPrev": true
  }
}
```

**🎯 OPTIMIZATION:** All filtering and pagination now happens at database level for maximum performance.

#### **4. Get System Templates**
```http
GET /api/pre-enrollment/plans/templates
```

**Description:** Get all system template plans (accessible to all users).

**Authentication:** Required (JWT)
**Authorization:** All authenticated users
**Headers:** `user-id: <user_id>`

**Query Parameters (Optional)**:
- `includeCarrierData` (string): Include complete carrier data (`'true'` | `'false'`, default: `'true'`)

**Success Response (200):**
```json
{
  "templates": [
    {
      "_id": "template_id_here",
      "planName": "Standard Health Template",
      "planCode": "STD_HEALTH_TEMPLATE",
      "coverageType": "Health Insurance",
      "coverageSubTypes": ["Medical"],
      "planType": "PPO",
      "description": "Standard health insurance template",
      "status": "Template",
      "isTemplate": true,
      "brokerId": "super_admin_id",
      "carrierId": "carrier_id_here",
      "createdAt": "2024-01-01T12:00:00.000Z",
      "carrierData": {
        "_id": "carrier_id_here",
        "carrierName": "Blue Cross Blue Shield",
        "carrierCode": "BCBS",
        "amBestRating": "A+",
        "financialStrength": "Excellent",
        "supportedCoverageTypes": ["Health Insurance", "Dental", "Vision"],
        "integrationCapabilities": ["API", "EDI", "Real-time"],
        "contactInfo": {
          "phone": "1-************",
          "email": "<EMAIL>",
          "website": "https://www.bcbs.com"
        },
        "status": "Active",
        "isActive": true
      }
    }
  ],
  "count": 1
}
```

#### **5. Get Plan By ID**
```http
GET /api/pre-enrollment/plans/:planId
```

**Description:** Retrieve a specific plan by ID with role-based access control.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Access Control:**
- **Super Admin**: Can access any plan
- **Broker**: Can access system templates + their own plans
- **Company Admin/Employee**: Can access system templates only (read-only)

**Success Response (200):**
```json
{
  "plan": {
    "_id": "plan_id_here",
    "planName": "Gold PPO Health Plan",
    "planCode": "GOLD_PPO_2024",
    "coverageType": "Health Insurance",
    "coverageSubTypes": ["Medical"],
    "planType": "PPO",
    "metalTier": "Gold",
    "description": "Comprehensive health plan with low deductibles",
    "highlights": ["Low deductible", "Wide network", "Preventive care covered"],
    "informativeLinks": ["https://carrier.com/plan-details"],
    "benefitDetails": {
      "deductibleIndividual": 500,
      "deductibleFamily": 1500,
      "pcpCopay": 25,
      "specialistCopay": 50,
      "emergencyRoomCopay": 200,
      "prescriptionCoverage": {
        "generic": 10,
        "brandName": 30,
        "specialty": 100
      }
    },
    "carrierId": "carrier_id_here",
    "carrierPlanId": "CARRIER_PLAN_123",
    "documentIds": ["doc1", "doc2"],
    "status": "Active",
    "isTemplate": false,
    "brokerId": "broker_id_here",
    "brokerageId": "brokerage_id_here",
    "createdAt": "2024-01-01T12:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Plan not found
{
  "error": "Plan not found or access denied"
}

// Invalid plan ID format
{
  "error": "Invalid plan ID format"
}

// Access denied to non-template plan
{
  "error": "Access denied to non-template plan"
}
```

#### **6. Update Plan**
```http
PUT /api/pre-enrollment/plans/:planId
```

**Description:** Update an existing plan with validation and reference checks.

**Authentication:** Required (JWT)
**Authorization:** Super Admin (all plans), Broker (own plans only)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Business Rules:**
- Can only update if plan has ≤1 assignment reference
- Cannot change `isTemplate` flag
- Cannot change `planCode` if referenced by assignments
- Broker-scoped uniqueness validation applies
- Brokers cannot update system templates (except Super Admins)

**Request Body (all fields optional):**
```json
{
  "planName": "Updated Gold PPO Health Plan",
  "description": "Updated comprehensive health plan",
  "highlights": ["Updated benefits", "Enhanced network"],
  "informativeLinks": ["https://updated-carrier.com/plan"],
  "benefitDetails": {
    "deductibleIndividual": 400,
    "deductibleFamily": 1200,
    "pcpCopay": 20
  },
  "carrierId": "new_carrier_id_here",
  "carrierPlanId": "NEW_CARRIER_PLAN_456"
}
```

**Success Response (200):**
```json
{
  "message": "Plan updated successfully",
  "plan": {
    "_id": "plan_id_here",
    "planName": "Updated Gold PPO Health Plan",
    "planCode": "GOLD_PPO_2024",
    "description": "Updated comprehensive health plan",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Cannot edit with multiple references
{
  "error": "Cannot edit plan with multiple assignment references",
  "referenceCount": 3,
  "referencedBy": ["assignment1", "assignment2", "assignment3"]
}

// Brokers cannot update templates
{
  "error": "Brokers cannot update system templates"
}

// Permission denied
{
  "error": "Only super admins and brokers can update plans"
}
```

#### **7. Activate Plan**
```http
POST /api/pre-enrollment/plans/:planId/activate
```

**Description:** Activate a plan (change status from Draft to Active, or Archived to Active).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (plan owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Business Rules:**
- Plans with status `"Draft"` or `"Archived"` can be activated
- Plan must pass validation (required fields, carrier compatibility)
- Templates can only be activated by Super Admins

**Success Response (200):**
```json
{
  "message": "Plan activated successfully",
  "plan": {
    "_id": "plan_id_here",
    "planName": "Gold PPO Health Plan",
    "status": "Active",
    "isActivated": true,
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Invalid status transition
{
  "error": "Cannot activate plan with status \"Active\". Only Draft and Archived plans can be activated."
}

// Validation failed
{
  "error": "Plan validation failed: Plan name is required, At least one coverage subtype is required"
}
```

#### **8. Convert Plan to Draft**
```http
POST /api/pre-enrollment/plans/:planId/convert-to-draft
```

**Description:** Convert an active plan back to draft status (change status from Active to Draft).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (plan owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Business Rules:**
- Only plans with status `"Active"` can be converted to draft
- Converted plans become editable again
- Templates can only be converted by Super Admins
- Plan assignments referencing this plan may be affected

**Success Response (200):**
```json
{
  "message": "Plan converted to draft successfully",
  "plan": {
    "_id": "plan_id_here",
    "planName": "Gold PPO Health Plan",
    "status": "Draft",
    "isActivated": false,
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Invalid status transition
{
  "error": "Cannot convert plan with status \"Draft\" to draft. Only Active plans can be converted to draft."
}

// Access denied
{
  "error": "Access denied to convert this plan to draft"
}

// Plan not found
{
  "error": "Plan not found or access denied"
}
```

#### **9. Archive Plan**
```http
POST /api/pre-enrollment/plans/:planId/archive
```

**Description:** Archive a plan (change status to Archived).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (plan owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Success Response (200):**
```json
{
  "message": "Plan archived successfully",
  "plan": {
    "_id": "plan_id_here",
    "planName": "Gold PPO Health Plan",
    "status": "Archived",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### **9. Duplicate Plan (Template to Broker Plan)**
```http
POST /api/pre-enrollment/plans/:planId/duplicate
```

**Description:** Duplicate a template plan to create a broker-specific plan.

**Authentication:** Required (JWT)
**Authorization:** Broker
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Template plan ID to duplicate

**Request Body (optional customizations):**
```json
{
  "planName": "Custom Gold PPO Health Plan",
  "planCode": "CUSTOM_GOLD_PPO_2024",
  "description": "Customized version of the template",
  "highlights": ["Custom benefits", "Tailored for our clients"],
  "carrierId": "broker_carrier_id_here"
}
```

**Success Response (201):**
```json
{
  "message": "Plan duplicated successfully",
  "plan": {
    "_id": "new_plan_id_here",
    "planName": "Custom Gold PPO Health Plan",
    "planCode": "CUSTOM_GOLD_PPO_2024",
    "status": "Draft",
    "isTemplate": false,
    "brokerId": "broker_id_here",
    "createdAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Can only duplicate templates
{
  "error": "Can only duplicate template plans"
}

// Template not found
{
  "error": "Template plan not found"
}

// Permission denied
{
  "error": "Only brokers can duplicate plans"
}
```

#### **10. Upload Plan Documents**
```http
POST /api/pre-enrollment/plans/:planId/documents
```

**Description:** Upload documents for a plan (SPDs, benefit summaries, etc.).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (plan owner)
**Headers:** `user-id: <user_id>`, `Content-Type: multipart/form-data`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Request Body (multipart/form-data):**
```
documents: File[] (multiple files allowed)
```

**Success Response (200):**
```json
{
  "message": "Documents uploaded successfully",
  "uploadedDocuments": [
    {
      "documentId": "doc_id_1",
      "fileName": "SPD_2024.pdf",
      "blobUrl": "https://storage.azure.com/container/doc_id_1",
      "fileSize": 1024000,
      "mimeType": "application/pdf"
    },
    {
      "documentId": "doc_id_2",
      "fileName": "Benefits_Summary.pdf",
      "blobUrl": "https://storage.azure.com/container/doc_id_2",
      "fileSize": 512000,
      "mimeType": "application/pdf"
    }
  ],
  "totalDocuments": 2
}
```

**Error Responses:**
```json
// No files uploaded
{
  "error": "No files uploaded"
}

// File too large
{
  "error": "File size exceeds limit"
}

// Invalid file type
{
  "error": "Invalid file type. Only PDF, DOC, DOCX allowed"
}
```

#### **11. Can Edit Plan**
```http
GET /api/pre-enrollment/plans/:planId/can-edit
```

**Description:** Check if a plan can be edited based on assignment references.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Success Response (200) - Can Edit:**
```json
{
  "planId": "plan_id_here",
  "planName": "Gold PPO Health Plan",
  "canEdit": true,
  "referenceCount": 1,
  "referencedBy": ["assignment_id_1"],
  "message": "Plan can be edited"
}
```

**Success Response (200) - Cannot Edit:**
```json
{
  "planId": "plan_id_here",
  "planName": "Gold PPO Health Plan",
  "canEdit": false,
  "referenceCount": 3,
  "referencedBy": ["assignment_id_1", "assignment_id_2", "assignment_id_3"],
  "reason": "Plan has 3 assignment references (>1 not allowed)"
}
```

#### **12. Can Delete Plan**
```http
GET /api/pre-enrollment/plans/:planId/can-delete
```

**Description:** Check if a plan can be deleted based on assignment references.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Success Response (200) - Can Delete:**
```json
{
  "planId": "plan_id_here",
  "planName": "Gold PPO Health Plan",
  "canDelete": true,
  "referenceCount": 0,
  "referencedBy": [],
  "reason": "Plan has no assignment references"
}
```

**Success Response (200) - Cannot Delete:**
```json
{
  "planId": "plan_id_here",
  "planName": "Gold PPO Health Plan",
  "canDelete": false,
  "referenceCount": 2,
  "referencedBy": ["assignment_id_1", "assignment_id_2"],
  "reason": "Plan has 2 assignment references"
}
```

#### **13. Get Dependent Assignments**
```http
GET /api/pre-enrollment/plans/:planId/dependent-assignments
```

**Description:** Get all plan assignments that reference this plan.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Success Response (200):**
```json
{
  "planId": "plan_id_here",
  "planName": "Gold PPO Health Plan",
  "dependentAssignments": [
    {
      "_id": "assignment_id_1",
      "companyId": "company_id_1",
      "companyName": "Acme Corporation",
      "groupNumber": "GRP001",
      "status": "Active",
      "assignedDate": "2024-01-01T12:00:00.000Z"
    },
    {
      "_id": "assignment_id_2",
      "companyId": "company_id_2",
      "companyName": "Tech Startup Inc",
      "groupNumber": "GRP002",
      "status": "Active",
      "assignedDate": "2024-01-15T12:00:00.000Z"
    }
  ],
  "count": 2
}
```

**Success Response (200) - No Dependencies:**
```json
{
  "planId": "plan_id_here",
  "planName": "Gold PPO Health Plan",
  "dependentAssignments": [],
  "count": 0
}
```

#### **14. Delete Plan**
```http
DELETE /api/pre-enrollment/plans/:planId
```

**Description:** Conditionally delete a plan with comprehensive validation and reference checking.

**Authentication:** Required (JWT)
**Authorization:** Super Admin (all plans), Broker (own plans only)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `planId` (string, required): Plan ID

**Business Rules:**
- **Hard Delete**: If plan has NO assignment references → Permanent removal from database
- **Deletion Blocked**: If plan has assignment references → Must use archive API instead
- Cannot delete system templates (except Super Admins)
- Azure container cleanup included for hard deletions
- Associated documents are also deleted

**Success Response (200) - Hard Delete:**
```json
{
  "message": "Plan permanently deleted (no assignment references found)",
  "deletionType": "hard",
  "planId": "60f7b3b3b3b3b3b3b3b3b3b4"
}
```

**Error Responses:**
```json
// Cannot delete with assignment references (409 Conflict)
{
  "error": "Cannot delete plan with assignment references. Reference count: 2. Use archive API instead.",
  "suggestion": "Use POST /api/pre-enrollment/plans/:planId/archive to safely archive this plan",
  "action": "archive_instead"
}

// Cannot delete template (403 Forbidden)
{
  "error": "Cannot delete system template. Only super admins can delete templates."
}

// Permission denied (403 Forbidden)
{
  "error": "Only super admins and brokers can delete plans"
}

// Plan not found (404 Not Found)
{
  "error": "Plan not found or access denied"
}
```

**Alternative Action:**
If deletion is blocked due to assignment references, use the archive API instead:
```http
POST /api/pre-enrollment/plans/:planId/archive
```

### 4.3 Plan Assignment APIs

The Plan Assignment APIs enable brokers to create, manage, and configure plan assignments for companies. These APIs handle the relationship between plans and companies with company-specific pricing and eligibility rules.

#### **🎯 BUSINESS RULES:**

1. **Enrollment Dependency**: Plan assignments cannot be enrolled unless they are active
2. **Edit Restrictions**: Plan assignments are editable only if ≤1 employee enrollment references them
3. **Delete Restrictions**: Plan assignments can be deleted only if 0 enrollments reference them
4. **Auto-Expiration**: Plan assignments automatically expire after `planEndDate` and become non-activatable
5. **Status Management**: Active assignments can be deactivated/reactivated within the plan year, but **expired assignments cannot be activated or deactivated** (complete expiry protection)
6. **Year-over-Year Reuse**: Previous year assignments can be cloned with updated dates for continuity

#### **🔧 DETAILED BUSINESS LOGIC:**

##### **Edit/Delete Permission Rules:**
```typescript
// canEdit Business Rule
canEdit = true  if enrollmentReferenceCount <= 1
canEdit = false if enrollmentReferenceCount > 1

// canDelete Business Rule
canDelete = true  if enrollmentReferenceCount === 0
canDelete = false if enrollmentReferenceCount > 0
```

**Rationale**:
- **≤1 enrollment**: Safe to edit (minimal impact, can handle edge cases)
- **0 enrollments**: Safe to delete (no dependencies)
- **>1 enrollments**: Editing could affect multiple employees, deletion blocked

##### **Access Control Rules:**
```typescript
// User Access Permissions
SuperAdmin:    Can access any plan assignment
Broker:        Can access assignments for their client companies
Company Admin: Can access their company's assignments only
Employee:      Can access assignments they're enrolled in only
```

##### **Plan Status Validation for Assignment:**
```typescript
// Plan Status Requirements for Assignment/Reassignment
Active:     ✅ Can be assigned/reassigned
Draft:      ❌ Cannot be assigned (incomplete)
Archived:   ❌ Cannot be assigned (deprecated)
Template:   ❌ Cannot be assigned (use duplicate first)
```

##### **Rate Structure Validation Rules:**
```typescript
// Rate Structure Requirements
Composite:              Must have single composite rate
Age-Banded:            Must have age ranges with rates/multipliers
Four-Tier:             Must have Employee/Spouse/Child/Family tiers
Age-Banded-Four-Tier:  Must have both age bands AND tier structure
Salary-Based:          Must have salary ranges with rates/percentages

// Validation Triggers
- Plan assignment creation
- Plan assignment updates (if rate structure modified)
- Coverage tier updates
- Rate structure changes
```

##### **Date Validation Rules:**
```typescript
// Date Relationship Requirements
planEffectiveDate < planEndDate                    // Plan period validity
enrollmentStartDate < enrollmentEndDate            // Enrollment period validity
enrollmentEndDate <= planEffectiveDate             // Enrollment before coverage
All dates must be valid ISO date strings           // Format validation

// Validation Triggers
- Plan assignment creation
- Plan assignment updates (if dates modified)
- Time constraints updates
- Clone operations (with smart date handling)
```

#### **📋 STATUS TRANSITION RULES:**

| **Transition** | **API Endpoint** | **Allowed When** | **Blocked When** | **Result** |
|----------------|------------------|------------------|------------------|------------|
| **Deactivated → Active** | `POST /:id/activate` | Assignment not expired | Assignment expired | `isActive: true, status: 'Active'` |
| **Active → Deactivated** | `POST /:id/deactivate` | Assignment not expired | Assignment expired | `isActive: false, status: 'Deactivated'` |
| **Any → Expired** | Automatic (date-based) | Past `planEndDate` | N/A | `isActive: false, status: 'Expired'` |

**⚠️ Expiry Protection:** Once an assignment reaches its `planEndDate`, it becomes **permanently expired** and cannot be activated or deactivated.

#### **📋 PLAN ASSIGNMENT STATUSES:**

| **Status** | **Description** | **Enrollment Allowed** | **Can Activate** | **Can Deactivate** |
|------------|-----------------|------------------------|------------------|-------------------|
| **Active** | Currently assignable | ✅ Yes | ❌ Already Active | ✅ Yes (if not expired) |
| **Deactivated** | Manually disabled | ❌ No | ✅ Yes (if not expired) | ❌ Already Inactive |
| **Expired** | Past `planEndDate` | ❌ No | ❌ **BLOCKED** | ❌ **BLOCKED** |

#### **✅ IMPLEMENTED APIs (17 Total):**

**🔐 ACCESS CONTROL SUMMARY:**
- **Broker Only**: Create, Reassign (business operations)
- **SuperAdmin + Broker**: Update, Activate, Deactivate, Delete, Time Constraints (management operations)
- **SuperAdmin Only**: Check Expired (system maintenance)
- **All Users**: Get operations (with role-based filtering)

**Core CRUD Operations:**
```http
POST   /api/pre-enrollment/plan-assignments                    # ✅ IMPLEMENTED - Create plan assignment (Broker only)
GET    /api/pre-enrollment/plan-assignments                     # ✅ IMPLEMENTED - Get assignments (role-based filtering)
GET    /api/pre-enrollment/plan-assignments/company/:companyId  # ✅ IMPLEMENTED - Get assignments by company
GET    /api/pre-enrollment/plan-assignments/:id                 # ✅ IMPLEMENTED - Get assignment by ID
PUT    /api/pre-enrollment/plan-assignments/:id                 # ✅ IMPLEMENTED - Update assignment (SuperAdmin + Broker)
DELETE /api/pre-enrollment/plan-assignments/:id                 # ✅ IMPLEMENTED - Delete assignment (SuperAdmin + Broker)
```

**Status Management:**
```http
POST   /api/pre-enrollment/plan-assignments/:id/activate        # ✅ IMPLEMENTED - Activate assignment (SuperAdmin + Broker)
POST   /api/pre-enrollment/plan-assignments/:id/deactivate      # ✅ IMPLEMENTED - Deactivate assignment (SuperAdmin + Broker)
POST   /api/pre-enrollment/plan-assignments/:id/clone           # ✅ IMPLEMENTED - Clone assignment (SuperAdmin + Broker)
```

**⚠️ Expiry Protection:** Both activate and deactivate operations are **blocked** for expired assignments (past `planEndDate`).

**📋 Impact on Enrollments:**
- **New Enrollments**: Only allowed for `Active` assignments
- **Existing Enrollments**: Continue unchanged regardless of assignment status (Active/Deactivated/Expired)
- **Cost Calculations**: Work for all existing enrollments regardless of assignment status

**Validation APIs:**
```http
GET    /api/pre-enrollment/plan-assignments/:id/can-edit        # ✅ IMPLEMENTED - Check edit permissions
GET    /api/pre-enrollment/plan-assignments/:id/can-delete      # ✅ IMPLEMENTED - Check delete permissions
GET    /api/pre-enrollment/plan-assignments/:id/enrollment-references # ✅ IMPLEMENTED - Get enrollment references
```

**Management APIs:**
```http
POST   /api/pre-enrollment/plan-assignments/check-expired       # ✅ IMPLEMENTED - Check and update expired assignments (SuperAdmin only)
GET    /api/pre-enrollment/plan-assignments/effective           # ✅ IMPLEMENTED - Get assignments effective on date (SuperAdmin + Broker)
GET    /api/pre-enrollment/plan-assignments/enrollment-period   # ✅ IMPLEMENTED - Get assignments in enrollment period (SuperAdmin + Broker)
POST   /api/pre-enrollment/plan-assignments/:id/reassign-plan   # ✅ IMPLEMENTED - Reassign to different plan (Broker only)
PUT    /api/pre-enrollment/plan-assignments/:id/time-constraints # ✅ IMPLEMENTED - Update time constraints (SuperAdmin + Broker)
```

#### **API Details:**

#### **1. Create Plan Assignment**
```http
POST /api/pre-enrollment/plan-assignments
```

**Description:** Assign a plan to a company with pricing and time constraints.

**Authentication:** Required (JWT)
**Authorization:** Broker only (SuperAdmins manage system resources only)
**Headers:** `user-id: <user_id>`

**Request Body:**
```json
{
  "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
  "groupNumber": "GRP001",
  "waitingPeriod": {
    "enabled": true,
    "days": 60,
    "rule": "First of month after X days",
    "description": "New hires wait 60 days"
  },
  "eligibleEmployeeClasses": ["Full-Time", "Part-Time"],
  "qualifyingLifeEventWindow": {
    "enabled": true,
    "windowDays": 30,
    "allowedEvents": ["Marriage", "Divorce", "Birth", "Adoption", "Loss of Coverage"],
    "description": "Standard QLE enrollment window"
  },
  "enrollmentType": "Active",
  "employerContribution": {
    "contributionType": "Percentage",
    "contributionAmount": 80
  },
  "employeeContribution": {
    "contributionType": "Percentage",
    "contributionAmount": 20
  },
  "rateStructure": "Composite",
  "coverageTiers": [
    {
      "tierName": "Employee Only",
      "totalCost": 500,
      "employeeCost": 100,
      "employerCost": 400
    },
    {
      "tierName": "Family",
      "totalCost": 1200,
      "employeeCost": 240,
      "employerCost": 960
    }
  ],
  "ageBandedRates": [
    {
      "ageMin": 18,
      "ageMax": 29,
      "rate": 450
    },
    {
      "ageMin": 30,
      "ageMax": 39,
      "rate": 500
    }
  ],
  "salaryBasedRates": [
    {
      "salaryMin": 30000,
      "salaryMax": 50000,
      "rate": 400
    }
  ],
  "salaryPercentage": 2.5,
  "planCustomizations": {
    "customPlanName": "Company Health Plan",
    "customDescription": "Customized for our employees",
    "additionalDocuments": ["doc1.pdf", "doc2.pdf"],
    "displayOrder": 1
  },
  "planEffectiveDate": "2024-01-01T00:00:00Z",
  "planEndDate": "2024-12-31T23:59:59Z",
  "enrollmentStartDate": "2023-11-01T00:00:00Z",
  "enrollmentEndDate": "2023-11-30T23:59:59Z"
}
```

**Success Response (201):**
```json
{
  "message": "Plan assignment created successfully",
  "assignment": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
    "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "groupNumber": "GRP001",
    "assignmentYear": 2024,
    "assignmentExpiry": "2024-12-31T23:59:59Z",
    "waitingPeriod": {
      "enabled": true,
      "days": 60,
      "rule": "First of month after X days",
      "description": "New hires wait 60 days"
    },
    "qualifyingLifeEventWindow": {
      "enabled": true,
      "windowDays": 30,
      "allowedEvents": ["Marriage", "Divorce", "Birth", "Adoption", "Loss of Coverage"],
      "description": "Standard QLE enrollment window"
    },
    "enrollmentType": "Active",
    "employerContribution": {
      "contributionType": "Percentage",
      "contributionAmount": 80
    },
    "employeeContribution": {
      "contributionType": "Percentage",
      "contributionAmount": 20
    },
    "rateStructure": "Composite",
    "coverageTiers": [
      {
        "tierName": "Employee Only",
        "totalCost": 500,
        "employeeCost": 100,
        "employerCost": 400
      }
    ],
    "planEffectiveDate": "2024-01-01T00:00:00Z",
    "planEndDate": "2024-12-31T23:59:59Z",
    "enrollmentStartDate": "2023-11-01T00:00:00Z",
    "enrollmentEndDate": "2023-11-30T23:59:59Z",
    "assignedDate": "2023-10-15T10:30:00Z",
    "isActive": true,
    "status": "Active",
    "createdAt": "2023-10-15T10:30:00Z",
    "updatedAt": "2023-10-15T10:30:00Z"
  }
}
```

#### **2. Get Plan Assignments (Role-Based Access)**
```http
GET /api/pre-enrollment/plan-assignments
```

**Purpose**: Get plan assignments based on user role and access permissions

**Authentication**: Required (JWT)
**Authorization**: All authenticated users

**Query Parameters (All Optional)**:
- `page` (number): Page number for pagination (optional, starts from 1)
- `limit` (number): Items per page (optional, 1-100, default: no pagination)
- `status` (string): Filter by assignment status (`Active`, `Deactivated`, `Expired`)
- `companyId` (string): Filter by company ID (SuperAdmin only)
- `planId` (string): Filter by plan ID
- `assignmentYear` (number): Filter by assignment year
- `rateStructure` (string): Filter by rate structure type (`Composite`, `Age-Banded`, `Four-Tier`, `Age-Banded-Four-Tier`, `Salary-Based`) - 🎯 **NEW**
- `brokerId` (string): Filter by broker ID (SuperAdmin only) - 🎯 **NEW**
- `includeInactive` (string): Include inactive assignments (`'true'` | `'false'`, default: `'false'`)
- `includePlanData` (string): Include complete plan and carrier data (`'true'` | `'false'`, default: `'true'`)
- `referenceDate` (string): Use specific date for time-based filtering (ISO date string)
- `enrollmentPeriodOnly` (string): Return only assignments currently in enrollment period (`'true'` | `'false'`)
- `effectiveOnly` (string): Return only assignments currently effective/active (`'true'` | `'false'`)
- `futureOnly` (string): Return only assignments that will be effective in the future (`'true'` | `'false'`)

**Access Control**:
- **SuperAdmins**: Can see all plan assignments across all companies
- **Brokers**: Can see assignments for plans they own (plan.brokerId === userId) across all companies
- **Company Admin/Employee**: Must use company-specific endpoint (`/company/:id`)

**🎯 UNIFIED IMPLEMENTATION:**
- **Query Parameters**: Shares the same parameter set as the company API for consistency
- **Response Format**: Uses unified response structure with pagination and error handling
- **Database Optimization**: Database-level filtering for optimal performance
- **Default Behavior**: `includeInactive` defaults to `'false'` (active only) - different from company API

**🔍 Broker Access Control Implementation:**

This endpoint uses different methods based on context:

| **Endpoint Context** | **Method Used** | **Reason** |
|---------------------|-----------------|------------|
| **Portfolio/Dashboard View** | `getAllAssignmentsByBroker(brokerId)` | Show broker's complete business across all companies |
| **Company-Specific View** | `getBrokerAssignmentsForCompany(brokerId, companyId)` | Ensure proper access control per company |
| **Access Validation** | `getBrokerAssignmentsForCompany(brokerId, companyId)` | Verify broker has rights to specific company |

**Example Implementation:**
```typescript
// In GET /api/pre-enrollment/plan-assignments (Portfolio View):
if (user.isBroker) {
  assignments = await getAllAssignmentsByBroker(userId); // All companies
}

// In GET /api/pre-enrollment/plan-assignments/company/:companyId (Company View):
if (user.isBroker) {
  const brokerAssignments = await getBrokerAssignmentsForCompany(userId, companyId); // Single company
  // Then apply additional filtering (time-based, etc.)
}
```

**Response Formats:**

**Non-Paginated Response (default):**
```json
{
  "assignments": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
      "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
      "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
      "assignedBy": "broker_id_here",
      "groupNumber": "GRP001",
      "planEffectiveDate": "2024-01-01T00:00:00Z",
      "planEndDate": "2024-12-31T23:59:59Z",
      "enrollmentStartDate": "2023-11-01T00:00:00Z",
      "enrollmentEndDate": "2023-11-30T23:59:59Z",
      "assignmentYear": 2024,
      "rateStructure": "Composite",
      "status": "Active",
      "isActive": true,
      "createdAt": "2023-10-15T10:30:00Z",
      "updatedAt": "2023-10-15T10:30:00Z",
      "planData": {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
        "planName": "Comprehensive Health Plan",
        "coverageType": "Health Insurance",
        "coverageSubTypes": ["Medical", "Prescription"],
        "planType": "PPO",
        "metalTier": "Gold",
        "carrierId": "60f7b3b3b3b3b3b3b3b3b3b6",
        "carrierName": "Blue Cross Blue Shield",
        "status": "Active",
        "isTemplate": false,
        "brokerId": "60f7b3b3b3b3b3b3b3b3b3b7",
        "description": "Comprehensive health coverage with nationwide network"
      }
    }
  ],
  "count": 150,
  "userRole": "Broker",
  "appliedFilters": ["activeOnly"],
  "message": "Broker plan assignments retrieved successfully",
  "expiryInfo": {
    "expiredCount": 0,
    "updatedAssignments": []
  }
}
```

**Paginated Response (when page & limit provided):**
```json
{
  "assignments": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
      "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
      "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
      "assignedBy": "broker_id_here",
      "groupNumber": "GRP001",
      "planEffectiveDate": "2024-01-01T00:00:00Z",
      "planEndDate": "2024-12-31T23:59:59Z",
      "enrollmentStartDate": "2023-11-01T00:00:00Z",
      "enrollmentEndDate": "2023-11-30T23:59:59Z",
      "assignmentYear": 2024,
      "rateStructure": "Composite",
      "status": "Active",
      "isActive": true,
      "createdAt": "2023-10-15T10:30:00Z",
      "updatedAt": "2023-10-15T10:30:00Z"
    }
  ],
  "count": 20,
  "pagination": {
    "currentPage": 2,
    "totalPages": 8,
    "totalItems": 150,
    "hasNext": true,
    "hasPrev": true
  },
  "userRole": "Broker",
  "appliedFilters": ["activeOnly"],
  "message": "Broker plan assignments retrieved successfully",
  "expiryInfo": {
    "expiredCount": 0,
    "updatedAssignments": []
  }
}
```

**🎯 OPTIMIZATION:** All filtering and pagination now happens at database level for maximum performance.

---

### **📊 API COMPARISON SUMMARY**

| Aspect | General API (`/plan-assignments`) | Company API (`/plan-assignments/company/:id`) |
|--------|-----------------------------------|-----------------------------------------------|
| **Purpose** | Cross-company view for brokers/admins | Single company focus for all users |
| **Access** | Broker + SuperAdmin only | Everyone (with hierarchy) |
| **Scope** | Multiple companies | Single company |
| **Default Behavior** | `includeInactive: 'false'` (active only) | `includeInactive: 'false'` (active only) - 🎯 **UNIFIED** |
| **Query Parameters** | ✅ Full unified support | ✅ Full unified support |
| **Response Format** | ✅ Unified structure | ✅ Unified structure |
| **Pagination** | ✅ Supported | ✅ Supported |
| **Database Optimization** | ✅ Database-level filtering | ✅ Database-level filtering |
| **Carrier Data** | ✅ Included by default | ✅ Included by default |

**🎯 Key Insight:** Both APIs now provide identical functionality with different access scopes and default behaviors appropriate for their use cases.

#### **3. Get Plan Assignments By Company**
```http
GET /api/pre-enrollment/plan-assignments/company/:companyId
```

**Description:** Get all plan assignments for a specific company with role-based access control and advanced time-based filtering.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `companyId` (string, required): Company ID

**Query Parameters (All Optional):**
- `page` (number): Page number for pagination (starts from 1)
- `limit` (number): Items per page (1-100, default: no pagination)
- `status` (string): Filter by assignment status (`Active`, `Deactivated`, `Expired`) - 🎯 **UNIFIED**
- `planId` (string): Filter by specific plan within the company - 🎯 **UNIFIED**
- `assignmentYear` (number): Filter by assignment year (e.g., 2024, 2025)
- `rateStructure` (string): Filter by rate structure type (`Composite`, `Age-Banded`, `Four-Tier`, `Age-Banded-Four-Tier`, `Salary-Based`) - 🎯 **NEW**
- `brokerId` (string): Filter by broker ID (SuperAdmin only) - 🎯 **NEW**
- `includeInactive` (string): Include inactive assignments (`'true'` | `'false'`, **default: `'false'`**) - 🎯 **UNIFIED** with General API
- `includeExpired` (string): Backward compatibility parameter (maps to `includeInactive`)
- `referenceDate` (string): ISO date for time-based filtering - 🎯 **NEW**
- `enrollmentPeriodOnly` (string): Show only assignments in enrollment period (`'true'` | `'false'`) - 🎯 **NEW**
- `effectiveOnly` (string): Show only assignments effective on reference date (`'true'` | `'false'`) - 🎯 **NEW**
- `futureOnly` (string): Show only assignments with future effective dates (`'true'` | `'false'`) - 🎯 **NEW**
- `includePlanData` (string): Include complete plan and carrier data (`'true'` | `'false'`, **default: `'true'`**) - 🎯 **UNIFIED**
- `referenceDate` (string): Use specific date for time-based filtering (ISO date string, default: current date)
- `enrollmentPeriodOnly` (string): Return only assignments currently in enrollment period (`'true'` | `'false'`)
- `effectiveOnly` (string): Return only assignments currently effective/active (`'true'` | `'false'`)
- `futureOnly` (string): Return only assignments that will be effective in the future (`'true'` | `'false'`)

**🎯 UNIFIED IMPLEMENTATION:**
- **Query Parameters**: Now supports ALL the same parameters as the general API (`GET /plan-assignments`)
- **Response Format**: Uses the same response structure as the general API (unified pagination, error handling)
- **Database Optimization**: Uses the same database-level filtering for optimal performance
- **Carrier Data**: Always includes complete plan and carrier data by default (same as general API)

**⚠️ Filter Validation:**
- Only ONE of `enrollmentPeriodOnly`, `effectiveOnly`, or `futureOnly` can be `'true'` at a time
- These filters are mutually exclusive and will return a 400 error if multiple are specified

**Access Control Hierarchy:**
- **SuperAdmin**: Can access ANY company's assignments
- **Broker**: Can access assignments for companies they manage (company.brokerId === userId) + their own brokerage company
- **Company Admin**: Can access only their own company's assignments (user.companyId === companyId)
- **Employee**: Can access only their own company's assignments (user.companyId === companyId)

**🎯 Broker Dual Role Explained:**
Brokers have **dual access** through this API:
1. **Client Management**: View assignments for companies they manage as a broker
2. **Own Company**: View assignments for their own brokerage company (as company admin)

**📋 API Usage Scenarios:**

| User Type | Scenario | API to Use | Example |
|-----------|----------|------------|---------|
| **Broker** | View all my plan assignments across companies | `GET /plan-assignments` | Dashboard overview |
| **Broker** | View assignments for specific client company | `GET /plan-assignments/company/:id` | Client company page |
| **Broker** | View assignments for my own brokerage | `GET /plan-assignments/company/myCompanyId` | Own company benefits |
| **Company Admin** | View my company's assignments | `GET /plan-assignments/company/myCompanyId` | Company benefits page |
| **Employee** | View my company's assignments | `GET /plan-assignments/company/myCompanyId` | Employee benefits view |
| **SuperAdmin** | View all assignments system-wide | `GET /plan-assignments` | System overview |
| **SuperAdmin** | View assignments for specific company | `GET /plan-assignments/company/:id` | Company audit |

**🔍 Broker Access Control Methods Explained:**

The system uses two distinct methods for broker access control with different scopes:

| **Method** | **Scope** | **Filter Logic** | **Use Case** |
|------------|-----------|------------------|--------------|
| `getAllAssignmentsByBroker(brokerId)` | **All Companies** | `plan.brokerId = brokerId` | Broker portfolio view across all clients |
| `getBrokerAssignmentsForCompany(brokerId, companyId)` | **Single Company** | `plan.brokerId = brokerId AND companyId = companyId` | Company-specific broker assignments |

**📊 Practical Example:**
```typescript
// Broker "broker123" has assignments across multiple companies:
Assignment1: { planId: "plan1", companyId: "companyA", plan: { brokerId: "broker123" } }
Assignment2: { planId: "plan2", companyId: "companyA", plan: { brokerId: "broker123" } }
Assignment3: { planId: "plan3", companyId: "companyB", plan: { brokerId: "broker123" } }

// getAllAssignmentsByBroker("broker123") returns:
// [Assignment1, Assignment2, Assignment3] - ALL companies

// getBrokerAssignmentsForCompany("broker123", "companyA") returns:
// [Assignment1, Assignment2] - ONLY companyA

// getBrokerAssignmentsForCompany("broker123", "companyB") returns:
// [Assignment3] - ONLY companyB
```

**⚠️ Critical Distinction:**
- **Company-specific endpoints** use `getBrokerAssignmentsForCompany()` for proper access control
- **Portfolio/dashboard endpoints** use `getAllAssignmentsByBroker()` for comprehensive views
- This prevents brokers from seeing other brokers' assignments for the same company

**Success Response (200):**
```json
{
  "assignments": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
      "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
      "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
      "groupNumber": "GRP001",
      "assignmentYear": 2024,
      "status": "Active",
      "isActive": true,
      "planEffectiveDate": "2024-01-01T00:00:00Z",
      "planEndDate": "2024-12-31T23:59:59Z",
      "enrollmentStartDate": "2023-11-01T00:00:00Z",
      "enrollmentEndDate": "2023-11-30T23:59:59Z",
      "assignedBy": "broker_id_here",
      "assignedDate": "2023-10-15T10:30:00Z",
      "planData": {
        "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
        "planName": "Comprehensive Health Plan",
        "coverageType": "Health Insurance",
        "coverageSubTypes": ["Medical", "Prescription"],
        "planType": "PPO",
        "metalTier": "Gold",
        "carrierId": "60f7b3b3b3b3b3b3b3b3b3b6",
        "carrierName": "Blue Cross Blue Shield",
        "status": "Active",
        "isTemplate": false,
        "brokerId": "60f7b3b3b3b3b3b3b3b3b3b7",
        "description": "Comprehensive health coverage with nationwide network",
        "planDetails": {
          "deductible": {
            "individual": 1000,
            "family": 2000
          },
          "outOfPocketMax": {
            "individual": 5000,
            "family": 10000
          },
          "coinsurance": 0.2,
          "copays": {
            "primaryCare": 25,
            "specialist": 50,
            "urgentCare": 75,
            "emergencyRoom": 200
          }
        }
      }
    }
  ],
  "count": 1,
  "appliedFilters": ["enrollmentPeriodOnly"],
  "filterMethod": "enrollmentPeriod",
  "referenceDate": "2024-11-15T10:30:00.000Z",
  "expiryInfo": {
    "expiredCount": 0,
    "updatedAssignments": []
  }
}
```

**Error Responses:**
```json
// Access denied
{
  "error": "Access denied to this company's assignments"
}

// Invalid company ID
{
  "error": "Invalid company ID format"
}

// Invalid reference date
{
  "error": "Invalid reference date format. Use ISO date string"
}

// Conflicting time filters
{
  "error": "Only one time-based filter can be applied at a time",
  "conflictingFilters": {
    "enrollmentPeriodOnly": "true",
    "effectiveOnly": "true",
    "futureOnly": "false"
  }
}
```

#### **4. Get Plan Assignment By ID**
```http
GET /api/pre-enrollment/plan-assignments/:id
```

**Description:** Retrieve a specific plan assignment by ID with complete plan and carrier data and role-based access control.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Access Control:**
- **Super Admin**: Can access any assignment
- **Broker**: Can access assignments they created OR for their own company
- **Company Admin/Employee**: Can access assignments for their company only

**Data Included:**
- Complete plan assignment details
- Full plan information (benefits, coverage details)
- Complete carrier information (ratings, contact details)

**Success Response (200):**
```json
{
  "assignment": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
    "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "groupNumber": "GRP001",
    "waitingPeriod": {
      "enabled": true,
      "days": 60,
      "rule": "First of month after X days"
    },
    "eligibleEmployeeClasses": ["Full-Time", "Part-Time"],
    "enrollmentType": "Active",
    "rateStructure": "Composite",
    "coverageTiers": [
      {
        "tierName": "Employee Only",
        "totalCost": 500,
        "employeeCost": 100,
        "employerCost": 400
      }
    ],
    "employerContribution": {
      "contributionType": "Percentage",
      "contributionAmount": 80
    },
    "employeeContribution": {
      "contributionType": "Percentage",
      "contributionAmount": 20
    },
    "planEffectiveDate": "2024-01-01T00:00:00Z",
    "planEndDate": "2024-12-31T23:59:59Z",
    "enrollmentStartDate": "2023-11-01T00:00:00Z",
    "enrollmentEndDate": "2023-11-30T23:59:59Z",
    "assignmentYear": 2024,
    "assignedBy": "broker_id_here",
    "assignedDate": "2023-10-15T10:30:00Z",
    "status": "Active",
    "isActive": true,
    "createdAt": "2023-10-15T10:30:00Z",
    "updatedAt": "2023-10-15T10:30:00Z",
    "planData": {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "planName": "Comprehensive Health Plan",
      "coverageType": "Health Insurance",
      "coverageSubTypes": ["Medical", "Prescription"],
      "planType": "PPO",
      "metalTier": "Gold",
      "carrierId": "60f7b3b3b3b3b3b3b3b3b3b6",
      "carrierName": "Blue Cross Blue Shield",
      "status": "Active",
      "isTemplate": false,
      "brokerId": "60f7b3b3b3b3b3b3b3b3b3b7",
      "description": "Comprehensive health coverage with nationwide network",
      "planDetails": {
        "deductible": {
          "individual": 1000,
          "family": 2000
        },
        "outOfPocketMax": {
          "individual": 5000,
          "family": 10000
        },
        "coinsurance": 0.2,
        "copays": {
          "primaryCare": 25,
          "specialist": 50,
          "urgentCare": 75,
          "emergencyRoom": 200
        }
      }
    },
    "carrierData": {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b6",
      "carrierName": "Blue Cross Blue Shield",
      "carrierCode": "BCBS",
      "amBestRating": "A+",
      "financialStrength": "Excellent",
      "supportedCoverageTypes": ["Health Insurance", "Dental", "Vision"],
      "integrationCapabilities": ["API", "EDI", "Real-time"],
      "contactInfo": {
        "phone": "1-************",
        "email": "<EMAIL>",
        "website": "https://www.bcbs.com"
      },
      "status": "Active",
      "isActive": true
    }
  }
}
```

**Error Responses:**
```json
// Assignment not found
{
  "error": "Plan assignment not found or access denied"
}

// Invalid assignment ID
{
  "error": "Invalid plan assignment ID format"
}
```

#### **5. Clone Plan Assignment**
```http
POST /api/pre-enrollment/plan-assignments/:id/clone
```

**Purpose**: Clone an existing plan assignment for reuse with smart date handling (creates in active state)

**Authentication**: Required (JWT)
**Authorization**: Brokers only (must be the original creator)

**Business Rules**:
- Cloned assignment is created with `isActive: true` and `status: 'Active'`
- Assignment is immediately available for enrollment operations
- Smart date handling: enrollment dates use current year, plan dates use next year
- Month and day are preserved from original, only years are updated
- All rate structures and configurations are preserved exactly

**Request Body**:
```typescript
{
  companyId?: string;                    // Optional: Different company (defaults to same)
  planEffectiveDate: string;             // Required: New effective date
  planEndDate: string;                   // Required: New end date
  enrollmentStartDate: string;           // Required: New enrollment start
  enrollmentEndDate: string;             // Required: New enrollment end
  groupNumber?: string;                  // Optional: New group number
  customizations?: {                     // Optional: Pricing customizations
    coverageTiers?: Array<{
      tierName: string;
      rate: number;
    }>;
    employerContribution?: {
      contributionType: string;
      contributionAmount: number;
    };
    employeeContribution?: {
      contributionType: string;
      contributionAmount: number;
    };
  };
}
```

**Success Response (201)**:
```json
{
  "success": true,
  "error": null,
  "assignment": {
    "_id": "new_assignment_id",
    "planId": "original_plan_id",
    "companyId": "company_id",
    "status": "Deactivated",             // Always created in deactivated state
    "isActive": false,                   // Always false for cloned assignments
    "assignedBy": "user_id",
    "assignmentYear": 2025,
    // ... all other fields preserved from original
  },
  "originalId": "original_assignment_id",
  "requiresActivation": true,            // Always true for cloned assignments
  "activationEndpoint": "/api/pre-enrollment/plan-assignments/new_assignment_id/activate",
  "message": "Plan assignment cloned successfully. Assignment is deactivated and requires manual activation.",
  "dateHandling": {
    "enrollmentYear": 2024,              // Current year for enrollment dates
    "planYear": 2025,                    // Next year for plan dates
    "preservedMonthDay": true            // Month/day preserved from original
  }
}
```

**4. Update Plan Assignment Time Constraints**
```http
PUT /api/pre-enrollment/plan-assignments/:id/time-constraints
```

**Purpose**: Update time-related fields for a plan assignment

**Authentication**: Required (JWT)
**Authorization**: Brokers only (must be the original creator)

**Request Body**:
```typescript
{
  planEffectiveDate?: string;            // Optional: New effective date
  planEndDate?: string;                  // Optional: New end date
  enrollmentStartDate?: string;          // Optional: New enrollment start
  enrollmentEndDate?: string;            // Optional: New enrollment end
}
```

**Business Rules**:
- Can only update if assignment has ≤1 enrollment
- Dates must be valid (start before end)
- Cannot update past effective dates
- Assignment year is automatically recalculated from planEndDate

**Response**:
```typescript
{
  success: boolean;
  message: string;
  data: {
    _id: string;
    planEffectiveDate: string;
    planEndDate: string;
    enrollmentStartDate: string;
    enrollmentEndDate: string;
    assignmentYear: number;              // Recalculated
    updatedAt: string;
  }
}
```

#### **5. Reassign Plan**
```http
POST /api/pre-enrollment/plan-assignments/:id/reassign-plan
```

**Description:** Reassign a plan assignment to a different plan while preserving all other settings.

**Authentication:** Required (JWT)
**Authorization:** Broker only (assignment owner) - SuperAdmins cannot reassign plans
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Request Body:**
```json
{
  "newPlanId": "60f7b3b3b3b3b3b3b3b3b3b6"
}
```

**Business Rules:**
- **Access Control**: Broker must have access to the plan assignment (ownership or client company)
- **canEdit Check**: Can only reassign if assignment has ≤1 enrollment references (uses canEdit logic)
- **Plan Status Validation**: New plan must be Active status only (Draft/Archived/Template blocked)
- **Plan Existence**: New plan must exist and be accessible to the broker
- **Preservation**: All pricing, time constraints, and configurations are preserved exactly

**Success Response (200):**
```json
{
  "success": true,
  "error": null,
  "message": "Plan reassigned successfully",
  "assignment": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
    "planId": "60f7b3b3b3b3b3b3b3b3b3b6",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "previousPlanId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "reassignedAt": "2024-01-01T12:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Cannot reassign with active enrollments
{
  "success": false,
  "error": "Cannot reassign plan with active enrollments. Reference count: 5",
  "assignment": null
}

// New plan not found
{
  "success": false,
  "error": "New plan not found",
  "assignment": null
}

// Cannot reassign to template
{
  "success": false,
  "error": "Cannot reassign to template plans. Please duplicate the template first.",
  "assignment": null
}

// Cannot reassign to archived plan
{
  "success": false,
  "error": "Cannot reassign to archived plan. Plan must be Active or Deactivated.",
  "assignment": null
}

// Only brokers can reassign
{
  "success": false,
  "error": "Only Brokers can reassign plan assignments",
  "assignment": null
}

// Broker ownership required
{
  "success": false,
  "error": "Brokers can only reassign to their own plans",
  "assignment": null
}
```

#### **6. Get Effective Assignments**
```http
GET /api/pre-enrollment/plan-assignments/effective
```

**Description:** Get plan assignments that are effective on a specific date.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker only
**Headers:** `user-id: <user_id>`

**Query Parameters:**
- `page` (number): Page number for pagination (optional, starts from 1)
- `limit` (number): Items per page (optional, 1-100, default: no pagination)
- `date` (string, optional): ISO date string (default: current date)
- `companyId` (string, optional): Filter by company ID
- `assignmentYear` (number, optional): Filter by assignment year (e.g., 2024, 2025)

**Success Response (200):**
```json
{
  "assignments": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
      "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
      "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
      "planEffectiveDate": "2024-01-01T00:00:00Z",
      "planEndDate": "2024-12-31T23:59:59Z",
      "status": "Active",
      "isActive": true
    }
  ],
  "count": 1,
  "referenceDate": "2024-06-15T00:00:00Z",
  "companyId": "all",
  "expiryInfo": {
    "expiredCount": 0,
    "updatedAssignments": []
  }
}
```

#### **7. Get Enrollment Period Assignments**
```http
GET /api/pre-enrollment/plan-assignments/enrollment-period
```

**Description:** Get plan assignments that are in their enrollment period on a specific date.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker only
**Headers:** `user-id: <user_id>`

**Query Parameters:**
- `page` (number): Page number for pagination (optional, starts from 1)
- `limit` (number): Items per page (optional, 1-100, default: no pagination)
- `date` (string, optional): ISO date string (default: current date)
- `companyId` (string, optional): Filter by company ID
- `assignmentYear` (number, optional): Filter by assignment year (e.g., 2024, 2025)

**Response Format:** Same as getPlanAssignments (supports pagination)

**Success Response (200):**
```json
{
  "assignments": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
      "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
      "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
      "enrollmentStartDate": "2023-11-01T00:00:00Z",
      "enrollmentEndDate": "2023-11-30T23:59:59Z",
      "status": "Active",
      "isActive": true
    }
  ],
  "count": 1,
  "referenceDate": "2023-11-15T00:00:00Z",
  "companyId": "all",
  "expiryInfo": {
    "expiredCount": 0,
    "updatedAssignments": []
  }
}
```

#### **6. Update Plan Assignment** ✅ IMPLEMENTED
```http
PUT /api/pre-enrollment/plan-assignments/:id
```

**Purpose:** Update an existing plan assignment with comprehensive validation and reference checks

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **User Authentication:** Valid user-id header required
2. **Assignment Ownership:**
   - **SuperAdmin:** Can update any plan assignment
   - **Broker:** Can only update assignments they created (assignedBy field matches user ID)
   - **Employer:** Cannot update plan assignments (read-only access)

**📋 Edit Permission Validation:**
1. **Reference Check:** Can only update if assignment has ≤1 enrollment reference
   - Uses `PlanAssignmentModelClass.canEditAssignment()` method
   - Checks ALL enrollments (regardless of status) referencing the assignment
   - Business rule: Assignments with multiple enrollments cannot be edited to prevent data inconsistency
2. **Status Validation:** Assignment must exist and be accessible

**🚫 Field Restrictions:**
1. **planId Field:** Cannot be changed through this endpoint
   - Use dedicated `/reassign-plan` endpoint for plan changes
   - Prevents accidental plan reassignment
2. **System Fields:** Cannot modify system-calculated fields (assignmentYear, assignmentExpiry)
3. **Company Assignment:** Cannot change companyId after creation

**💰 Cost Structure Validation:**
1. **Rate Structure Consistency:** If updating rate structure, validate all related fields
2. **Coverage Tier Validation:** Ensure coverage tiers are properly formatted and valid
3. **Contribution Policy Validation:** Employer and employee contributions must be valid and sum appropriately

**📅 Date Field Handling:**
1. **Time Constraints:** If updating date fields, use dedicated `/time-constraints` endpoint
2. **Date Validation:** Automatic validation of date sequences and business rules
3. **Assignment Year Recalculation:** Automatically recalculated when planEndDate changes

**🏢 Employee Class Eligibility:**
1. **Valid Classes:** Eligible employee classes must be from valid EMPLOYEE_CLASS_TYPES
2. **Business Impact:** Changes affect which employees can enroll in the plan
3. **Default Behavior:** If not specified, defaults to ["Full-Time"]

**⏰ Waiting Period Configuration:**
1. **Rule Validation:** Waiting period rules must be from valid WAITING_PERIOD_RULES
2. **Business Logic:** Changes affect new employee eligibility calculations
3. **Consistency Check:** Days and rule must be consistent

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (assignment owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Request Body (all fields optional):**
```json
{
  "groupNumber": "GRP002",
  "waitingPeriod": {
    "enabled": true,
    "days": 90,
    "rule": "First of month after X days"
  },
  "eligibleEmployeeClasses": ["Full-Time"],
  "enrollmentType": "Active",
  "coverageTiers": [
    {
      "tierName": "Employee Only",
      "totalCost": 600,
      "employeeCost": 120,
      "employerCost": 480
    }
  ],
  "employerContribution": {
    "contributionType": "Percentage",
    "contributionAmount": 80
  },
  "employeeContribution": {
    "contributionType": "Percentage",
    "contributionAmount": 20
  }
}
```

**Success Response (200):**
```json
{
  "message": "Plan assignment updated successfully",
  "assignment": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
    "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "groupNumber": "GRP002",
    "waitingPeriod": {
      "enabled": true,
      "days": 90,
      "rule": "First of month after X days"
    },
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### **7. Activate Plan Assignment**
```http
POST /api/pre-enrollment/plan-assignments/:id/activate
```

**Description:** Activate a plan assignment (change status from Deactivated to Active).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (assignment owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Business Rules:**
- Only assignments with status `"Deactivated"` can be activated
- **Expired assignments cannot be activated** (past `planEndDate`)
- Assignment must exist and be accessible to the user

**Success Response (200):**
```json
{
  "message": "Plan assignment activated successfully",
  "assignment": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
    "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "isActive": true,
    "status": "Active",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Expired assignment (NEW - Expiry Protection)
{
  "error": "Cannot activate expired plan assignment",
  "currentStatus": "Expired"
}

// Assignment not found
{
  "error": "Plan assignment not found"
}

// Access denied
{
  "error": "Access denied to activate this assignment"
}
```

#### **8. Deactivate Plan Assignment**
```http
POST /api/pre-enrollment/plan-assignments/:id/deactivate
```

**Description:** Deactivate a plan assignment (change status from Active to Deactivated).

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (assignment owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Business Rules:**
- Only assignments with status `"Active"` can be deactivated
- **Expired assignments cannot be deactivated** (past `planEndDate`)
- Assignment must exist and be accessible to the user
- Existing enrollments continue unchanged

**Success Response (200):**
```json
{
  "message": "Plan assignment deactivated successfully",
  "assignment": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
    "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "isActive": false,
    "status": "Deactivated",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Expired assignment (NEW - Expiry Protection)
{
  "error": "Cannot deactivate expired plan assignment",
  "currentStatus": "Expired"
}

// Assignment not found
{
  "error": "Plan assignment not found"
}

// Access denied
{
  "error": "Access denied to deactivate this assignment"
}
```

#### **9. Delete Plan Assignment**
```http
DELETE /api/pre-enrollment/plan-assignments/:id
```

**Description:** Permanently delete a plan assignment with validation.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (assignment owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Business Rules:**
- Can only delete if assignment has no enrollment references
- Permanent deletion (not soft delete)

**Success Response (200):**
```json
{
  "message": "Plan assignment deleted successfully",
  "deletedAssignmentId": "60f7b3b3b3b3b3b3b3b3b3b5"
}
```

**Error Responses:**
```json
// Cannot delete with enrollment references
{
  "error": "Cannot delete plan assignment with enrollment references",
  "referenceCount": 2,
  "referencedBy": ["enrollment1", "enrollment2"]
}
```

#### **10. Can Edit Plan Assignment**
```http
GET /api/pre-enrollment/plan-assignments/:id/can-edit
```

**Description:** Check if a plan assignment can be edited based on enrollment references.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Business Logic:**
- **canEdit = true** if enrollmentReferenceCount ≤ 1 (safe to edit)
- **canEdit = false** if enrollmentReferenceCount > 1 (multiple enrollments affected)
- Checks all enrollment statuses (Enrolled, Pending, Waived, Terminated)
- Access control ensures user can only check assignments they have access to

**Success Response (200) - Can Edit:**
```json
{
  "canEdit": true,
  "referenceCount": 1,
  "referencedBy": ["enrollment_id_1"],
  "message": "Assignment can be edited (≤1 enrollment reference)"
}
```

**Success Response (200) - Cannot Edit:**
```json
{
  "canEdit": false,
  "referenceCount": 3,
  "referencedBy": ["enrollment_id_1", "enrollment_id_2", "enrollment_id_3"],
  "message": "Assignment cannot be edited due to multiple active enrollments"
}
```

}
```

#### **11. Can Delete Plan Assignment**
```http
GET /api/pre-enrollment/plan-assignments/:id/can-delete
```

**Description:** Check if a plan assignment can be deleted based on enrollment references.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Business Logic:**
- **canDelete = true** if enrollmentReferenceCount === 0 (no dependencies)
- **canDelete = false** if enrollmentReferenceCount > 0 (has dependencies)
- Checks all enrollment statuses (Enrolled, Pending, Waived, Terminated)
- More restrictive than canEdit (requires zero references vs ≤1)
- Access control ensures user can only check assignments they have access to

**Success Response (200) - Can Delete:**
```json
{
  "canDelete": true,
  "referenceCount": 0,
  "referencedBy": [],
  "message": "Assignment can be deleted"
}
```

**Success Response (200) - Cannot Delete:**
```json
{
  "canDelete": false,
  "referenceCount": 2,
  "referencedBy": ["enrollment1", "enrollment2"],
  "message": "Assignment cannot be deleted due to enrollment references"
}
```

#### **12. Get Enrollment References**
```http
GET /api/pre-enrollment/plan-assignments/:id/enrollment-references
```

**Description:** Get all employee enrollments that reference this plan assignment.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Success Response (200):**
```json
{
  "enrollments": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b6",
      "employeeId": "60f7b3b3b3b3b3b3b3b3b3b3",
      "employeeName": "John Doe",
      "coverageTier": "Employee + Spouse",
      "status": "Active",
      "enrollmentDate": "2024-01-15T10:30:00Z"
    },
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b7",
      "employeeId": "60f7b3b3b3b3b3b3b3b3b3b8",
      "employeeName": "Jane Smith",
      "coverageTier": "Employee Only",
      "status": "Active",
      "enrollmentDate": "2024-01-20T14:15:00Z"
    }
  ],
  "count": 2
}
```

**Success Response (200) - No References:**
```json
{
  "assignmentId": "60f7b3b3b3b3b3b3b3b3b3b5",
  "enrollmentReferences": [],
  "count": 0
}
```

#### **13. Check Expired Assignments**
```http
POST /api/pre-enrollment/plan-assignments/check-expired
```

**Description:** Check and update expired plan assignments based on current date.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker
**Headers:** `user-id: <user_id>`

**Request Body (optional):**
```json
{
  "referenceDate": "2024-12-31T23:59:59Z",
  "companyId": "60f7b3b3b3b3b3b3b3b3b3b4"
}
```

**Success Response (200):**
```json
{
  "message": "Expired assignments check completed",
  "checkedCount": 25,
  "expiredCount": 3,
  "updatedAssignments": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
      "planId": "60f7b3b3b3b3b3b3b3b3b3b3",
      "companyId": "60f7b3b3b3b3b3b3b3b3b3b4",
      "status": "Expired",
      "planEndDate": "2024-12-31T23:59:59Z",
      "updatedAt": "2024-01-01T12:00:00.000Z"
    }
  ],
  "referenceDate": "2024-12-31T23:59:59Z"
}
```






    "reassignmentHistory": [
      {
        "previousPlanId": "60f7b3b3b3b3b3b3b3b3b3b3",
        "newPlanId": "60f7b3b3b3b3b3b3b3b3b3b6",
        "effectiveDate": "2024-01-01T00:00:00Z",
        "reason": "Plan upgrade requested by client",
        "reassignedAt": "2024-01-01T12:00:00.000Z"
      }
    ],
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### **17. Update Time Constraints**
```http
PUT /api/pre-enrollment/plan-assignments/:id/time-constraints
```

**Description:** Update time-related fields of a plan assignment.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker (assignment owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `id` (string, required): Plan assignment ID

**Request Body:**
```json
{
  "planEffectiveDate": "2024-01-01T00:00:00Z",
  "planEndDate": "2024-12-31T23:59:59Z",
  "enrollmentStartDate": "2023-11-01T00:00:00Z",
  "enrollmentEndDate": "2023-11-30T23:59:59Z"
}
```

**Business Rules:**
- Can only update if assignment has ≤1 enrollment reference
- Date validation applies (enrollment before effective, end after start, etc.)
- Assignment year is automatically recalculated

**Success Response (200):**
```json
{
  "message": "Time constraints updated successfully",
  "assignment": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b5",
    "planEffectiveDate": "2024-01-01T00:00:00Z",
    "planEndDate": "2024-12-31T23:59:59Z",
    "enrollmentStartDate": "2023-11-01T00:00:00Z",
    "enrollmentEndDate": "2023-11-30T23:59:59Z",
    "assignmentYear": 2024,
    "assignmentExpiry": "2024-12-31T23:59:59Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Cannot update with multiple references
{
  "error": "Cannot update time constraints with multiple enrollment references",
  "referenceCount": 3
}

// Invalid date sequence
{
  "error": "Invalid assignment dates",
  "details": ["Enrollment end date must be before plan effective date"]
}
```

### 4.4 Company Benefits Settings APIs

The Company Benefits Settings APIs enable brokers and employers to configure company-wide benefit policies, enrollment periods, and eligibility rules that apply to all plan assignments for a company.

#### **🎯 BUSINESS RULES:**

1. **Creation Rights**: Only Super Admins and Brokers can create company benefits settings
2. **Management Rights**: Brokers and Employers can update settings for their companies
3. **Access Control**: Users can only access settings for companies they have permission to manage
4. **Uniqueness**: Each company can have only one benefits settings record
5. **Validation**: Payroll frequencies and enrollment period types must be from predefined constants
6. **Soft Delete**: Settings are deactivated (soft deleted) rather than permanently removed

#### **✅ IMPLEMENTED APIs (8 Total):**

**Core CRUD Operations:**
```http
POST   /api/pre-enrollment/company-benefits-settings                    # ✅ IMPLEMENTED - Create company settings
GET    /api/pre-enrollment/company-benefits-settings/company/:companyId # ✅ IMPLEMENTED - Get company settings
PUT    /api/pre-enrollment/company-benefits-settings/company/:companyId # ✅ IMPLEMENTED - Update company settings
DELETE /api/pre-enrollment/company-benefits-settings/company/:companyId # ✅ IMPLEMENTED - Deactivate settings
```

**Management APIs:**
```http
GET    /api/pre-enrollment/company-benefits-settings/broker-companies   # ✅ IMPLEMENTED - Get broker companies with settings status
POST   /api/pre-enrollment/company-benefits-settings/company/:companyId/enrollment-periods # ✅ IMPLEMENTED - Add enrollment period
PUT    /api/pre-enrollment/company-benefits-settings/company/:companyId/enrollment-periods/:periodId # ✅ IMPLEMENTED - Update enrollment period
GET    /api/pre-enrollment/company-benefits-settings/company/:companyId/validate # ✅ IMPLEMENTED - Validate settings completeness
```

#### **1. Create Company Benefits Settings**
```http
POST /api/pre-enrollment/company-benefits-settings
```

**Description:** Create company-wide benefits settings and policies.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker
**Headers:** `user-id: <user_id>`

**Request Body:**
```json
{
  "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "globalEligibility": {
    "payrollFrequency": "Biweekly",
    "defaultWaitingPeriod": {
      "enabled": true,
      "days": 60,
      "rule": "First of month after X days"
    },
    "eligibleEmployeeClasses": ["Full-Time", "Part-Time"],
    "minimumHoursPerWeek": 30,
    "probationaryPeriod": 90
  },
  "enrollmentPeriods": [
    {
      "type": "Open Enrollment",
      "startDate": "2024-11-01T00:00:00Z",
      "endDate": "2024-11-30T23:59:59Z",
      "coverageStartDate": "2025-01-01T00:00:00Z",
      "coverageEndDate": "2025-12-31T23:59:59Z",
      "description": "Annual open enrollment period",
      "isActive": true
    }
  ],
  "companyPreferences": {
    "allowMidYearChanges": false,
    "requireBeneficiaryDesignation": true,
    "enableDependentVerification": true,
    "defaultCoverageEffectiveDate": "First of month following enrollment",
    "communicationPreferences": {
      "emailNotifications": true,
      "smsNotifications": false,
      "paperDocuments": false
    }
  }
}
```

**Success Response (201):**
```json
{
  "message": "Company benefits settings created successfully",
  "settings": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b4",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "globalEligibility": {
      "payrollFrequency": "Biweekly",
      "defaultWaitingPeriod": {
        "enabled": true,
        "days": 60,
        "rule": "First of month after X days"
      },
      "eligibleEmployeeClasses": ["Full-Time", "Part-Time"],
      "minimumHoursPerWeek": 30,
      "probationaryPeriod": 90
    },
    "enrollmentPeriods": [
      {
        "type": "Open Enrollment",
        "startDate": "2024-11-01T00:00:00Z",
        "endDate": "2024-11-30T23:59:59Z",
        "coverageStartDate": "2025-01-01T00:00:00Z",
        "coverageEndDate": "2025-12-31T23:59:59Z",
        "description": "Annual open enrollment period",
        "isActive": true
      }
    ],
    "companyPreferences": {
      "allowMidYearChanges": false,
      "requireBeneficiaryDesignation": true,
      "enableDependentVerification": true,
      "defaultCoverageEffectiveDate": "First of month following enrollment",
      "communicationPreferences": {
        "emailNotifications": true,
        "smsNotifications": false,
        "paperDocuments": false
      }
    },
    "isActive": true,
    "createdAt": "2024-01-01T12:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Missing company ID
{
  "error": "Company ID is required"
}

// Company not found
{
  "error": "Company not found"
}

// Settings already exist
{
  "error": "Company benefits settings already exist. Use PUT to update."
}

// Invalid payroll frequency
{
  "error": "Invalid payroll frequency. Must be one of: Weekly, Biweekly, Semi-Monthly, Monthly"
}

// Invalid enrollment period type
{
  "error": "Invalid enrollment period type: InvalidType. Must be one of: Open Enrollment, New Hire, Qualifying Life Event"
}

// Access denied
{
  "error": "Access denied to manage this company"
}

// Permission denied
{
  "error": "Only super admins and brokers can create company benefits settings"
}
```

#### **2. Get Company Benefits Settings**
```http
GET /api/pre-enrollment/company-benefits-settings/company/:companyId
```

**Description:** Retrieve company benefits settings by company ID.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `companyId` (string, required): Company ID

**Success Response (200):**
```json
{
  "settings": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b4",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "globalEligibility": {
      "payrollFrequency": "Biweekly",
      "defaultWaitingPeriod": {
        "enabled": true,
        "days": 60,
        "rule": "First of month after X days"
      },
      "eligibleEmployeeClasses": ["Full-Time", "Part-Time"],
      "minimumHoursPerWeek": 30,
      "probationaryPeriod": 90
    },
    "enrollmentPeriods": [
      {
        "type": "Open Enrollment",
        "startDate": "2024-11-01T00:00:00Z",
        "endDate": "2024-11-30T23:59:59Z",
        "coverageStartDate": "2025-01-01T00:00:00Z",
        "coverageEndDate": "2025-12-31T23:59:59Z",
        "description": "Annual open enrollment period",
        "isActive": true
      }
    ],
    "companyPreferences": {
      "allowMidYearChanges": false,
      "requireBeneficiaryDesignation": true,
      "enableDependentVerification": true,
      "defaultCoverageEffectiveDate": "First of month following enrollment",
      "communicationPreferences": {
        "emailNotifications": true,
        "smsNotifications": false,
        "paperDocuments": false
      }
    },
    "isActive": true,
    "createdAt": "2024-01-01T12:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Company not found
{
  "error": "Company not found"
}

// Settings not found
{
  "error": "Company benefits settings not found"
}

// Access denied
{
  "error": "Access denied to this company"
}
```

#### **3. Get Broker Companies with Settings Status**
```http
GET /api/pre-enrollment/company-benefits-settings/broker-companies
```

**Description:** Get all companies managed by a broker with their benefits settings status.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker
**Headers:** `user-id: <user_id>`

**Success Response (200):**
```json
{
  "companies": [
    {
      "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
      "companyName": "Acme Corporation",
      "hasSettings": true,
      "settingsId": "60f7b3b3b3b3b3b3b3b3b3b4",
      "lastUpdated": "2024-01-01T12:00:00.000Z"
    },
    {
      "companyId": "60f7b3b3b3b3b3b3b3b3b3b5",
      "companyName": "Tech Startup Inc",
      "hasSettings": false,
      "settingsId": null,
      "lastUpdated": null
    }
  ],
  "count": 2
}
```

**Error Responses:**
```json
// Permission denied
{
  "error": "Only super admins and brokers can access broker companies"
}
```

#### **4. Validate Settings Completeness**
```http
GET /api/pre-enrollment/company-benefits-settings/company/:companyId/validate
```

**Description:** Validate if company benefits settings are complete and ready for plan assignment.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `companyId` (string, required): Company ID

**Success Response (200) - Complete Settings:**
```json
{
  "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "validation": {
    "isComplete": true,
    "missingFields": [],
    "warnings": []
  },
  "readyForPlanAssignment": true
}
```

**Success Response (200) - Incomplete Settings:**
```json
{
  "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "validation": {
    "isComplete": false,
    "missingFields": [
      "globalEligibility.payrollFrequency",
      "enrollmentPeriods"
    ],
    "warnings": [
      "No enrollment periods defined",
      "Default waiting period not configured"
    ]
  },
  "readyForPlanAssignment": false
}
```

**Success Response (200) - No Settings:**
```json
{
  "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "validation": {
    "isComplete": false,
    "missingFields": ["Company benefits settings not created"],
    "warnings": []
  },
  "readyForPlanAssignment": false
}
```

#### **5. Update Company Benefits Settings**
```http
PUT /api/pre-enrollment/company-benefits-settings/company/:companyId
```

**Description:** Update existing company benefits settings with partial updates.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker, Employer (company owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `companyId` (string, required): Company ID

**Request Body (all fields optional):**
```json
{
  "globalEligibility": {
    "payrollFrequency": "Weekly",
    "defaultWaitingPeriod": {
      "enabled": false,
      "days": 0,
      "rule": "Immediate"
    },
    "eligibleEmployeeClasses": ["Full-Time", "Part-Time", "Contractor"],
    "minimumHoursPerWeek": 20,
    "probationaryPeriod": 60
  },
  "enrollmentPeriods": [
    {
      "type": "New Hire",
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-12-31T23:59:59Z",
      "coverageStartDate": "2024-01-01T00:00:00Z",
      "coverageEndDate": "2024-12-31T23:59:59Z",
      "description": "New hire enrollment period",
      "isActive": true
    }
  ],
  "companyPreferences": {
    "allowMidYearChanges": true,
    "requireBeneficiaryDesignation": false,
    "enableDependentVerification": false,
    "defaultCoverageEffectiveDate": "Immediate",
    "communicationPreferences": {
      "emailNotifications": false,
      "smsNotifications": true,
      "paperDocuments": true
    }
  }
}
```

**Success Response (200):**
```json
{
  "message": "Company benefits settings updated successfully",
  "settings": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b4",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "globalEligibility": {
      "payrollFrequency": "Weekly",
      "defaultWaitingPeriod": {
        "enabled": false,
        "days": 0,
        "rule": "Immediate"
      },
      "eligibleEmployeeClasses": ["Full-Time", "Part-Time", "Contractor"],
      "minimumHoursPerWeek": 20,
      "probationaryPeriod": 60
    },
    "enrollmentPeriods": [
      {
        "type": "New Hire",
        "startDate": "2024-01-01T00:00:00Z",
        "endDate": "2024-12-31T23:59:59Z",
        "coverageStartDate": "2024-01-01T00:00:00Z",
        "coverageEndDate": "2024-12-31T23:59:59Z",
        "description": "New hire enrollment period",
        "isActive": true
      }
    ],
    "companyPreferences": {
      "allowMidYearChanges": true,
      "requireBeneficiaryDesignation": false,
      "enableDependentVerification": false,
      "defaultCoverageEffectiveDate": "Immediate",
      "communicationPreferences": {
        "emailNotifications": false,
        "smsNotifications": true,
        "paperDocuments": true
      }
    },
    "isActive": true,
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
```json
// Settings not found
{
  "error": "Company benefits settings not found"
}

// Invalid payroll frequency
{
  "error": "Invalid payroll frequency. Must be one of: Weekly, Biweekly, Semi-Monthly, Monthly"
}

// Access denied
{
  "error": "Access denied to manage this company"
}
```

#### **6. Add Enrollment Period**
```http
POST /api/pre-enrollment/company-benefits-settings/company/:companyId/enrollment-periods
```

**Description:** Add a new enrollment period to existing company settings.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker, Employer (company owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `companyId` (string, required): Company ID

**Request Body:**
```json
{
  "type": "Qualifying Life Event",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-12-31T23:59:59Z",
  "coverageStartDate": "2024-01-01T00:00:00Z",
  "coverageEndDate": "2024-12-31T23:59:59Z",
  "description": "Special enrollment for qualifying life events",
  "isActive": true
}
```

**Success Response (201):**
```json
{
  "message": "Enrollment period added successfully",
  "settings": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b4",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "enrollmentPeriods": [
      {
        "type": "Open Enrollment",
        "startDate": "2024-11-01T00:00:00Z",
        "endDate": "2024-11-30T23:59:59Z",
        "coverageStartDate": "2025-01-01T00:00:00Z",
        "coverageEndDate": "2025-12-31T23:59:59Z",
        "description": "Annual open enrollment period",
        "isActive": true
      },
      {
        "type": "Qualifying Life Event",
        "startDate": "2024-01-01T00:00:00Z",
        "endDate": "2024-12-31T23:59:59Z",
        "coverageStartDate": "2024-01-01T00:00:00Z",
        "coverageEndDate": "2024-12-31T23:59:59Z",
        "description": "Special enrollment for qualifying life events",
        "isActive": true
      }
    ],
    "updatedAt": "2024-01-01T12:00:00.000Z"
  },
  "addedPeriod": {
    "type": "Qualifying Life Event",
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-12-31T23:59:59Z",
    "coverageStartDate": "2024-01-01T00:00:00Z",
    "coverageEndDate": "2024-12-31T23:59:59Z",
    "description": "Special enrollment for qualifying life events",
    "isActive": true
  }
}
```

**Error Responses:**
```json
// Missing required fields
{
  "error": "Type, startDate, endDate, coverageStartDate, and coverageEndDate are required"
}

// Invalid enrollment period type
{
  "error": "Invalid enrollment period type: InvalidType. Must be one of: Open Enrollment, New Hire, Qualifying Life Event"
}
```

#### **7. Update Enrollment Period**
```http
PUT /api/pre-enrollment/company-benefits-settings/company/:companyId/enrollment-periods/:periodId
```

**Description:** Update an existing enrollment period within company settings.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker, Employer (company owner)
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `companyId` (string, required): Company ID
- `periodId` (string, required): Enrollment period ID

**Request Body (all fields optional):**
```json
{
  "type": "Open Enrollment",
  "startDate": "2024-10-01T00:00:00Z",
  "endDate": "2024-10-31T23:59:59Z",
  "coverageStartDate": "2025-01-01T00:00:00Z",
  "coverageEndDate": "2025-12-31T23:59:59Z",
  "description": "Updated annual open enrollment period",
  "isActive": false
}
```

**Success Response (200):**
```json
{
  "message": "Enrollment period updated successfully",
  "settings": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b4",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "enrollmentPeriods": [
      {
        "_id": "period_id_here",
        "type": "Open Enrollment",
        "startDate": "2024-10-01T00:00:00Z",
        "endDate": "2024-10-31T23:59:59Z",
        "coverageStartDate": "2025-01-01T00:00:00Z",
        "coverageEndDate": "2025-12-31T23:59:59Z",
        "description": "Updated annual open enrollment period",
        "isActive": false
      }
    ],
    "updatedAt": "2024-01-01T12:00:00.000Z"
  },
  "updatedPeriod": {
    "_id": "period_id_here",
    "type": "Open Enrollment",
    "startDate": "2024-10-01T00:00:00Z",
    "endDate": "2024-10-31T23:59:59Z",
    "coverageStartDate": "2025-01-01T00:00:00Z",
    "coverageEndDate": "2025-12-31T23:59:59Z",
    "description": "Updated annual open enrollment period",
    "isActive": false
  }
}
```

**Error Responses:**
```json
// Enrollment period not found
{
  "error": "Enrollment period not found"
}

// Invalid enrollment period type
{
  "error": "Invalid enrollment period type: InvalidType. Must be one of: Open Enrollment, New Hire, Qualifying Life Event"
}
```

#### **8. Deactivate Company Settings**
```http
DELETE /api/pre-enrollment/company-benefits-settings/company/:companyId
```

**Description:** Deactivate (soft delete) company benefits settings.

**Authentication:** Required (JWT)
**Authorization:** Super Admin, Broker
**Headers:** `user-id: <user_id>`

**Path Parameters:**
- `companyId` (string, required): Company ID

**Success Response (200):**
```json
{
  "message": "Company benefits settings deactivated successfully",
  "deactivatedSettingsId": "60f7b3b3b3b3b3b3b3b3b3b4"
}
```

**Error Responses:**
```json
// Settings not found
{
  "error": "Company benefits settings not found"
}

// Permission denied
{
  "error": "Only super admins and brokers can deactivate company benefits settings"
}

// Access denied
{
  "error": "Access denied to manage this company"
}
```

### 4.5 Employee Enrollment APIs

The Employee Enrollment APIs handle the complete enrollment workflow with comprehensive eligibility validation, cost calculation, and enrollment management.

#### **🏗️ Service Layer Architecture (Updated)**

**All enrollment validation and business logic has been centralized in the service layer** (`EmployeeEnrollmentService`) for consistency, maintainability, and code reuse:

**✅ Service Methods:**
1. **`createEnrollmentWithCostCalculation`** - Complete enrollment creation with validation
2. **`validateEnrollmentPeriod`** - Comprehensive period validation for all enrollment types
3. **`validateEnrollmentPeriodForExistingEnrollment`** - Period validation for update/delete/waive operations
4. **`enrollEmployeeInMultiplePlans`** - Bulk enrollment with rollback functionality

**✅ Centralized Validation:**
- **Profile Completeness:** Employee must have complete profile data
- **Hire Date Eligibility:** Employee must meet hire date requirements
- **Employee Class Eligibility:** Employee class must match plan requirements
- **Plan Assignment Status:** Plan assignment must be active and valid
- **Enrollment Period Validation:** Based on enrollment type with SuperAdmin override
- **Cost Calculation:** Automatic cost calculation with contribution policies

**✅ Controller Responsibilities (Reduced):**
- **Request Validation:** Required fields, format validation
- **Access Control:** User permissions and role-based access
- **Response Formatting:** API response structure
- **Business-Specific Logic:** Status transitions, date computations

**✅ Benefits Achieved:**
- **No Code Duplication:** Validation logic exists only in service layer
- **Consistent Behavior:** All APIs use identical validation rules
- **Better Testing:** Service methods can be unit tested independently
- **Easier Maintenance:** Changes to business logic only need to be made once

#### **🎯 DEPENDENT REFERENCE SYSTEM**

**NEW ARCHITECTURE: Reference-Based Dependent Management**

The Employee Enrollment system now uses a **reference-based approach** for managing dependents:

1. **Single Source of Truth**: Dependent information is stored in `User.details.dependents[]` array
2. **Reference Storage**: Enrollments store only `dependentId` references to User model dependents
3. **Enrollment Metadata**: Each enrolled dependent includes enrollment-specific data (dates, status, carrier IDs)
4. **Historical Snapshots**: Critical dependent data is captured at enrollment time for historical accuracy

**Benefits:**
- ✅ **Data Normalization**: No duplicate dependent information across enrollments
- ✅ **Automatic Updates**: Changes to dependents in User model reflect across all enrollments
- ✅ **Cross-Plan Tracking**: Track which dependents are enrolled across multiple plans
- ✅ **Storage Efficiency**: Reduced data duplication and storage requirements

**API Usage:**
- **Input**: Provide `dependentIds[]` array referencing `User.details.dependents._id`
- **Output**: Returns `enrolledDependents[]` with references + enrollment metadata + snapshots
- **Validation**: System validates dependent IDs exist and belong to the employee

#### **🎯 COMPREHENSIVE ENROLLMENT LOGIC**

**EMPLOYEE ELIGIBILITY VALIDATION FACTORS:**

The system performs comprehensive eligibility validation using multiple data sources:

**1. 📅 ENROLLMENT PERIOD VALIDATION**
- **Source**: Plan Assignment Model (`enrollmentStartDate`, `enrollmentEndDate`)
- **Logic**: Current date must be within enrollment window
- **Validation**: `now >= enrollmentStartDate && now <= enrollmentEndDate`

**2. 🏢 PLAN ASSIGNMENT STATUS**
- **Source**: Plan Assignment Model (`status`)
- **Logic**: Only Active plan assignments allow enrollment
- **Validation**: `planAssignment.status === 'Active'`

**3. 👥 EMPLOYEE CLASS TYPE ELIGIBILITY**
- **Source**: Plan Assignment (`eligibleEmployeeClasses[]`) + User Model (`details.employeeClassType`)
- **Logic**: Employee's class must be in plan's eligible classes
- **Validation**: `eligibleEmployeeClasses.includes(employee.employeeClassType)` OR empty array = all eligible

**4. ⏰ HIRE DATE & WAITING PERIOD**
- **Source**: Plan Assignment (`waitingPeriod`) + User Model (`details.hireDate`)
- **Logic**: Employee must satisfy waiting period from hire date
- **Calculation**: `eligibilityDate = calculateEligibilityDate(hireDate, waitingPeriod)`

**5. 🏢 COMPANY MEMBERSHIP**
- **Source**: User Model (`companyId`) + Plan Assignment (`companyId`)
- **Logic**: Employee must belong to plan's assigned company
- **Validation**: `employee.companyId === planAssignment.companyId`

**6. 📋 EMPLOYEE PROFILE COMPLETENESS**
- **Source**: User Model (`details.*`)
- **Required Fields**: `dateOfBirth`, `employeeClassType`, `hireDate`
- **Conditional**: `annualSalary` (salary-based plans), `ssn`, `address` (carrier processing)

**7. 🚫 DUPLICATE ENROLLMENT CHECK**
- **Source**: Employee Enrollment Model
- **Logic**: No existing active enrollment for same plan
- **Validation**: Check for existing enrollment with status 'Enrolled' or 'Pending'

**8. 👨‍👩‍👧‍👦 DEPENDENT VALIDATION**
- **Source**: User Model (`details.dependents[]`)
- **Logic**: Dependent IDs must exist, be active, and have valid ages for relationships
- **Validation**: Age limits (spouse ≥18, child <26), active status

**COST CALCULATION DATA SOURCES:**

**Primary Source: Plan Assignment Model**
- **Rate Structure**: `rateStructure` ("Composite", "Age-Banded", "Four-Tier", etc.)
- **Coverage Tiers**: `coverageTiers[]` with tier names and total costs
- **Age-Based Rates**: `ageBandedRates[]` for age-banded structures
- **Salary-Based Rates**: `salaryBasedRates[]` for salary-based structures
- **Contribution Policies**: `employerContribution`, `employeeContribution`

**Secondary Source: User Model**
- **Demographics**: `details.dateOfBirth` (age calculation), `details.annualSalary`
- **Employee Class**: `details.employeeClassType` (eligibility)

**Cost Calculation Flow:**
1. **Base Cost**: Calculated from rate structure + selected tier + employee demographics
2. **Contribution Split**: Applied based on employer/employee contribution policies
3. **Multiple Frequencies**: Monthly, annual, and payroll-specific amounts calculated

#### **✅ IMPLEMENTED APIs (14 Total):**

**Core Enrollment Workflow:**
```http
POST /api/pre-enrollment/employee-enrollments/check-eligibility         # ✅ IMPLEMENTED - Check enrollment eligibility
GET  /api/pre-enrollment/employee-enrollments/enrollment-periods/:id    # ✅ IMPLEMENTED - Get available enrollment periods
POST /api/pre-enrollment/employee-enrollments/calculate-cost            # ✅ IMPLEMENTED - Calculate enrollment cost
POST /api/pre-enrollment/employee-enrollments/estimate-plan-costs       # ✅ IMPLEMENTED - Estimate plan assignment costs
POST /api/pre-enrollment/employee-enrollments                           # ✅ IMPLEMENTED - Create enrollment (with intelligent type detection)
```

**Enrollment Management:**
```http
GET  /api/pre-enrollment/employee-enrollments/employee/:employeeId      # ✅ IMPLEMENTED - Get enrollments by employee
PUT  /api/pre-enrollment/employee-enrollments/:enrollmentId             # ✅ IMPLEMENTED - Update enrollment
DELETE /api/pre-enrollment/employee-enrollments/:enrollmentId           # ✅ IMPLEMENTED - Delete enrollment
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/terminate   # ✅ IMPLEMENTED - Terminate enrollment
```

**Status Management:**
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/waive       # ✅ IMPLEMENTED - Waive enrollment
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/reinstate   # ✅ IMPLEMENTED - Reinstate enrollment
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/activate    # ✅ IMPLEMENTED - Activate enrollment
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/terminate  # ✅ IMPLEMENTED - Terminate enrollment
```

**Expiry Management:**
```http
GET  /api/pre-enrollment/employee-enrollments/expired                   # ✅ IMPLEMENTED - Get expired enrollments (user/planAssignments modes)
POST /api/pre-enrollment/employee-enrollments/check-expired             # ✅ IMPLEMENTED - Manual expiry check (SuperAdmin)
```

**Utility:**
```http
# No utility endpoints currently implemented
```

#### **📋 DOCUMENTED BUT NOT IMPLEMENTED (1 API):**

**Additional Management:**
```http
GET  /api/pre-enrollment/employee-enrollments                           # 📋 PENDING - Get enrollments (role-based)
```

**📋 PENDING APIs (16 Total - Future Implementation):**

**Additional Management (5 APIs):**
```http
GET    /api/pre-enrollment/employee-enrollments                         # PENDING - Get enrollments (role-based)
GET    /api/pre-enrollment/employee-enrollments/company/:companyId      # PENDING - Get enrollments by company
GET    /api/pre-enrollment/employee-enrollments/plan-assignment/:planAssignmentId # PENDING - Get enrollments by assignment
GET    /api/pre-enrollment/employee-enrollments/:enrollmentId           # PENDING - Get enrollment by ID
DELETE /api/pre-enrollment/employee-enrollments/:enrollmentId           # PENDING - Delete enrollment
```

**Status Management (4 APIs):**
```http
POST   /api/pre-enrollment/employee-enrollments/:enrollmentId/waive     # ✅ IMPLEMENTED - Waive enrollment
POST   /api/pre-enrollment/employee-enrollments/:enrollmentId/reinstate # ✅ IMPLEMENTED - Reinstate enrollment
POST   /api/pre-enrollment/employee-enrollments/:enrollmentId/activate  # ✅ IMPLEMENTED - Activate enrollment
POST   /api/pre-enrollment/employee-enrollments/:enrollmentId/terminate # ✅ IMPLEMENTED - Terminate enrollment
```

**Validation and Audit (4 APIs):**
```http
GET    /api/pre-enrollment/employee-enrollment/:enrollmentId/can-edit     # PENDING - Check edit permissions
GET    /api/pre-enrollment/employee-enrollment/:enrollmentId/can-delete   # PENDING - Check delete permissions
GET    /api/pre-enrollment/employee-enrollment/:enrollmentId/history      # PENDING - Get enrollment history
POST   /api/pre-enrollment/employee-enrollment/:enrollmentId/validate     # PENDING - Validate enrollment data
```

**Bulk Operations (3 APIs):**
```http
POST   /api/pre-enrollment/employee-enrollments/bulk-terminate           # ❌ NOT IMPLEMENTED - Bulk termination
POST   /api/pre-enrollment/employee-enrollments/bulk-update              # ❌ NOT IMPLEMENTED - Bulk update
GET    /api/pre-enrollment/employee-enrollments/bulk-status              # ❌ NOT IMPLEMENTED - Bulk operation status
```

**Reporting and Analytics (4 APIs):**
```http
GET    /api/pre-enrollment/employee-enrollment/reports/summary           # PENDING - Enrollment summary
GET    /api/pre-enrollment/employee-enrollment/reports/by-plan           # PENDING - Enrollment by plan
GET    /api/pre-enrollment/employee-enrollment/reports/by-company        # PENDING - Enrollment by company
GET    /api/pre-enrollment/employee-enrollment/reports/cost-analysis     # PENDING - Cost analysis report
```

#### **API Details:**

#### **1. Check Employee Eligibility** ✅ FULLY IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/check-eligibility
```

**Description:** Comprehensive eligibility validation before enrollment with detailed checks.

**🎯 IMPLEMENTATION STATUS**: **COMPLETE** - Fully integrated with comprehensive eligibility validation, employee profile validation, and enhanced response structure.

**Authentication:** Required (JWT)
**Authorization:** All authenticated users (with access control)
**Headers:** `user-id: <user_id>`

**Request Body:**
```json
{
  "employeeId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "planAssignmentId": "60f7b3b3b3b3b3b3b3b3b3b4",
  "enrollmentType": "Open Enrollment",  // Optional: "Open Enrollment", "New Hire", "Qualifying Life Event"
  "qualifyingLifeEvent": {              // Required if enrollmentType = "Qualifying Life Event"
    "eventType": "Marriage",            // Must be in plan's qualifyingLifeEventWindow.allowedEvents
    "eventDate": "2024-01-15",          // Date when the qualifying event occurred
    "allowOutsideEnrollmentPeriod": true // Whether to allow enrollment outside normal period
  },
  "newHireDate": "2024-01-15"          // Required if enrollmentType = "New Hire"
}
```

**🎯 NEW: Intelligent Enrollment Type Detection**

The API now automatically determines the best enrollment type based on employee data and context:

**1. Open Enrollment (Default):**
- Standard annual enrollment period
- Must be within plan assignment enrollment dates
- All eligible employees can enroll

**2. New Hire Enrollment:**
- For newly hired employees
- Configurable window (default 30 days) from hire date
- Uses `planAssignment.newHireEnrollmentWindow.windowDays` if configured
- Auto-detected if employee hired within window

**3. Qualifying Life Event (QLE):**
- For employees with qualifying life changes
- **🎯 NEW: Plan-Specific Configuration**: Each plan assignment has its own QLE rules
- **Window Days**: Uses `planAssignment.qualifyingLifeEventWindow.windowDays` (not hardcoded 30 days)
- **Allowed Events**: Only events in `planAssignment.qualifyingLifeEventWindow.allowedEvents` are valid
- **Enable/Disable**: Plans can disable QLE enrollment via `qualifyingLifeEventWindow.enabled: false`
- Can override enrollment period restrictions with `allowOutsideEnrollmentPeriod: true`

**🎯 NEW: Data-Driven Configuration:**
- Enrollment windows are now configurable per plan assignment
- No more hardcoded 30-day periods
- Frontend can query available periods before enrollment
- Intelligent type detection reduces frontend complexity

**Comprehensive Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **User Authentication:** Valid user-id header required
2. **Employee Access Rights:**
   - **SuperAdmin:** Can check eligibility for any employee
   - **Employee:** Can only check their own eligibility
   - **Employer/Admin:** Can check eligibility for employees in their company
   - **Broker:** Can check eligibility for employees in companies they manage (read-only access)

**📋 Plan Assignment Validation:**
1. **Plan Assignment Exists:** Must be valid plan assignment ID
2. **Plan Assignment Status:** Must be "Active" status (only active assignments allow enrollment)
3. **Company Matching:** Employee must belong to the company assigned to the plan

**📅 Enrollment Period Validation:**
1. **Current Date Check:** Must be within enrollment period (enrollmentStartDate ≤ today ≤ enrollmentEndDate)
2. **Period Status Messages:**
   - Before start: "Enrollment period has not started yet. Starts on [date]"
   - After end: "Enrollment period has ended. Ended on [date]"

**🚫 Duplicate Enrollment Prevention:**
1. **Existing Enrollment Check:** Employee cannot have existing enrollment for same plan assignment
2. **Status Consideration:** Checks all enrollment statuses (Enrolled, Pending, Waived, Terminated)

**👤 Employee Profile Completeness Validation:**
1. **Required Fields:**
   - `details.dateOfBirth` (required for age-based cost calculations)
   - `details.employeeClassType` (required for plan eligibility validation)
   - `details.hireDate` (required for waiting period validation)
2. **Conditional Requirements:**
   - `details.annualSalary` (required for salary-based rate structures)
   - `details.address` (recommended for enrollment)
   - `details.phoneNumber` (recommended for enrollment)
   - `details.emergencyContact` (recommended for enrollment)

**⏰ Hire Date Eligibility Validation:**
1. **Waiting Period Rules:**
   - **Immediate:** Employee eligible immediately upon hire
   - **Days from hire date:** Employee eligible after specified days from hire date
   - **First of month after X days:** Employee eligible on first of month after waiting period
2. **Calculation Logic:**
   - Calculates exact eligibility date based on hire date and waiting period configuration
   - Returns days until eligible if not yet eligible
   - Provides clear reason messages for ineligibility

### **📅 Employee Eligibility vs Coverage Effective Date**

**🎯 Key Concept:** There are two distinct dates in the enrollment process:

1. **Employee Eligibility Date:** When the employee becomes eligible to enroll in the plan
2. **Coverage Effective Date:** When the insurance coverage actually starts

**📋 Business Logic:**
- **Plan Coverage Period:** Defined by `planEffectiveDate` to `planEndDate` in plan assignment
- **Coverage Rule:** Coverage can only start during the plan's coverage period
- **Immediate Coverage:** If employee becomes eligible during coverage period, coverage starts immediately
- **Delayed Coverage:** If employee becomes eligible before coverage period, coverage starts when plan becomes effective

**📊 Real-World Examples:**

| **Scenario** | **Employee Eligibility Date** | **Plan Coverage Period** | **Coverage Starts** | **Rationale** |
|-------------|-------------------------------|--------------------------|------------------|---------------|
| New hire during plan year | 2024-04-14 (hire + 30 days) | 2024-01-01 to 2024-12-31 | **2024-04-14** | Immediate coverage upon eligibility |
| New hire before plan year | 2023-12-01 (hire + 30 days) | 2024-01-01 to 2024-12-31 | **2024-01-01** | Coverage waits for plan to start |
| QLE during plan year | 2024-06-15 (marriage date) | 2024-01-01 to 2024-12-31 | **2024-06-15** | Immediate coverage upon QLE |
| QLE before plan year | 2023-11-15 (marriage date) | 2024-01-01 to 2024-12-31 | **2024-01-01** | Coverage waits for plan to start |
| Open enrollment | 2023-11-15 (enrollment date) | 2024-01-01 to 2024-12-31 | **2024-01-01** | Always starts on plan effective date |

**📋 Three Enrollment Cases Support:**
1. **Open Enrollment (Annual):**
   - **Who:** All existing employees during annual enrollment period
   - **Validation:** Plan assignment enrollment period + employee eligibility + waiting period
   - **Period Logic:** Must be within `enrollmentStartDate` to `enrollmentEndDate` from plan assignment
   - **Eligibility:** Employee is eligible during enrollment period (current date)
   - **Coverage Start:** Always starts on plan's `planEffectiveDate` regardless of when enrolled
   - **Waiting Period:** Still validated (existing employees must have met waiting period)

2. **New Hire Enrollment:**
   - **Who:** Newly hired employees within their enrollment window
   - **Validation:** Hire date + waiting period window + employee eligibility (IGNORES normal enrollment period dates)
   - **Period Logic:** Must enroll within `waitingPeriod.days` from hire date (plan assignment configurable)
   - **Eligibility:** Employee becomes eligible after `hireDate + waitingPeriod.days`
   - **Coverage Start:**
     - If eligible **during** plan coverage period → Coverage starts **immediately** upon eligibility
     - If eligible **before** plan coverage period → Coverage starts when plan becomes effective
   - **Special Behavior:** Can enroll outside normal enrollment period dates

3. **Qualifying Life Event (QLE):**
   - **Who:** Existing employees with qualifying life changes
   - **Validation:** QLE event type + QLE window + employee eligibility (CAN override enrollment period dates)
   - **Period Logic:** Must enroll within `qualifyingLifeEventWindow.windowDays` from event date (plan assignment configurable)
   - **Eligibility:** Employee becomes eligible immediately on QLE event date
   - **Coverage Start:**
     - If QLE occurs **during** plan coverage period → Coverage starts **immediately** on event date
     - If QLE occurs **before** plan coverage period → Coverage starts when plan becomes effective
   - **Event Validation:** Event type must be in plan's `qualifyingLifeEventWindow.allowedEvents` array
   - **Special Behavior:** Can override normal enrollment period restrictions with `allowOutsideEnrollmentPeriod: true`

**🎯 Enrollment Type Behavior:**
1. **Active Enrollment:**
   - **User Experience:** Employee must actively select plans and coverage options
   - **Default Behavior:** No automatic enrollments, requires explicit choices
   - **Use Case:** New plans, plan changes, first-time enrollment

2. **Passive Enrollment:**
   - **User Experience:** Employee automatically continues previous elections
   - **Default Behavior:** Previous year's selections carry forward unless changed
   - **Use Case:** Annual renewals, maintaining existing coverage

**🏢 Employee Class Type Eligibility:**
1. **Class Type Validation:** Employee's class type must be in plan's eligible classes list
2. **Default Behavior:** If no eligible classes specified, all employee classes are eligible
3. **Business Justification:**
   - **Full-Time:** Standard eligibility for all benefits
   - **Part-Time:** Limited eligibility or different waiting periods
   - **Contractor:** Usually not eligible (legal/tax implications)
   - **Temporary:** Often excluded from benefits
   - **Seasonal:** Special eligibility rules

**Success Response (200) - Eligible:**
```json
{
  "success": true,
  "message": "Eligibility check completed",
  "isEligible": true,
  "eligibilityDetails": {
    "enrollmentType": "Open Enrollment",
    "reason": null,
    "checks": {
      "profileComplete": true,
      "hireDateEligible": true,
      "classEligible": true,
      "planActive": true,
      "companyMatch": true
    },
    "reasons": [],
    "warnings": []
  },
  "employee": {
    "employeeId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "John Doe",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b5",
    "employeeClass": "Full-Time",
    "age": 35,
    "annualSalary": 75000
  },
  "planAssignment": {
    "planAssignmentId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "planName": "Health Plan Premium",
    "coverageType": "Your Health",
    "coverageSubTypes": ["Medical", "Prescription"],
    "rateStructure": "Age-Banded",
    "enrollmentPeriod": {
      "start": "2024-11-01T00:00:00Z",
      "end": "2024-11-30T23:59:59Z"
    }
  },
  "reasons": [],
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**Success Response (200) - Not Eligible:**
```json
{
  "success": true,
  "message": "Eligibility check completed",
  "isEligible": false,
  "eligibilityDetails": {
    "enrollmentType": "Open Enrollment",
    "reason": "Employee profile incomplete; Employee class 'Part-Time' is not eligible for this plan",
    "checks": {
      "profileComplete": false,
      "hireDateEligible": true,
      "classEligible": false,
      "planActive": true,
      "companyMatch": true
    },
    "reasons": [
      "Employee profile incomplete",
      "Employee class 'Part-Time' is not eligible for this plan"
    ],
    "warnings": [
      "Annual salary not provided - may affect cost calculation"
    ]
  },
  "employee": {
    "employeeId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "Jane Smith",
    "companyId": "60f7b3b3b3b3b3b3b3b3b3b5",
    "employeeClass": "Part-Time",
    "age": null,
    "annualSalary": null
  },
  "planAssignment": {
    "planAssignmentId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "planName": "Health Plan Premium",
    "coverageType": "Your Health",
    "coverageSubTypes": ["Medical", "Prescription"],
    "rateStructure": "Composite",
    "enrollmentPeriod": {
      "start": "2024-11-01T00:00:00Z",
      "end": "2024-11-30T23:59:59Z"
    }
  },
  "reasons": [
    "Employee profile incomplete",
    "Employee class 'Part-Time' is not eligible for this plan"
  ],
  "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing required fields
{
  error: "employeeId and planAssignmentId are required"
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied: Cannot access employee data"
}

// 404 Not Found - Employee or plan assignment not found
{
  error: "Employee not found" | "Plan assignment not found"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Edge Cases**:
- **Employee not found**: Returns 404 with clear message
- **Plan assignment expired**: Eligibility check fails with expiry message
- **Employee already enrolled**: Duplicate check fails with existing enrollment ID
- **Incomplete profile**: Lists specific missing fields (dateOfBirth, employeeClassType, etc.)
- **Waiting period not met**: Shows exact eligibility date and days remaining
- **Wrong employee class**: Shows current class vs eligible classes

**2. Calculate Enrollment Cost** ✅ FULLY IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/calculate-cost
```

**Purpose**: Real-time cost calculation with comprehensive eligibility validation and multiple rate structure support

**🎯 IMPLEMENTATION STATUS**: **COMPLETE** - Fully integrated with CostCalculationService, employee data extraction, eligibility validation, and comprehensive response structure.

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
- Same access control rules as Check Eligibility API
- User must have read access to employee data

**📊 Cost Calculation Engine:**
1. **Rate Structure Support:**
   - **Composite:** Flat rates regardless of employee demographics
   - **Four-Tier:** Standard industry tiers (Employee, Employee+Spouse, Employee+Children, Family)
   - **Age-Banded:** Rates vary by employee age with configurable age bands
   - **Salary-Based:** Rates based on employee annual salary ranges
   - **Age-Banded Four-Tier:** Combination of age bands and family composition

2. **Contribution Policy Types:**
   - **Fixed Amount:** Employer pays fixed dollar amount, employee pays remainder
   - **Percentage:** Employer pays percentage of premium, employee pays remainder
   - **Remainder:** Employee pays fixed amount, employer pays remainder

3. **Employee Data Requirements:**
   - **Age Calculation:** Automatically calculated from `details.dateOfBirth`
   - **Salary Validation:** Required for salary-based rate structures
   - **Coverage Tier Validation:** Must be valid tier for the plan assignment

**✅ Validation Performed:**
1. **All Eligibility Checks:** Same comprehensive validation as Check Eligibility API
2. **Coverage Tier Validation:** Selected tier must exist in plan assignment
3. **Employee Data Completeness:** Required fields for cost calculation present
4. **Rate Structure Compatibility:** Employee data matches rate structure requirements

**Authentication**: Required (JWT)
**Authorization**: Brokers, Employers, Employees (with access control)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

**Request Body**:
```typescript
{
  employeeId: string;                  // Required: Employee user ID
  planAssignmentId: string;            // Required: Plan assignment ID
  coverageTier: string;                // Required: "Employee Only", "Employee + Spouse", "Family", etc.
  dependentIds?: string[];              // Optional: Array of dependent IDs from User.details.dependents
                                       // These will be validated for eligibility and used for cost calculation
}
```

**Success Response** (200):
```json
{
  "success": true,
  "message": "Cost calculation completed",
  "calculatedCost": {
    "employeeAmount": 150.00,
    "employerAmount": 350.00,
    "totalAmount": 500.00,
    "payrollDeduction": 150.00,
    "coverageTier": "Employee + Spouse",
    "dependentCount": 1,
    "payrollFrequency": "Monthly",
    "rateStructure": "Composite"
  },
  "planAssignment": {
    "planAssignmentId": "plan_assignment_id_here",
    "planName": "Comprehensive Health Plan",
    "coverageType": "Health Insurance",
    "coverageSubTypes": ["Medical", "Prescription"],
    "rateStructure": "Composite"
  },
  "employee": {
    "employeeId": "employee_id_here",
    "name": "John Doe",
    "age": 35,
    "annualSalary": 75000,
    "companyId": "company_id_here"
  },
  "eligibility": {
    "isEligible": true,
    "checks": {
      "accessControl": true,
      "planAssignmentActive": true,
      "enrollmentPeriod": true,
      "waitingPeriod": true,
      "employeeClass": true,
      "duplicateCheck": true
    },
    "reasons": [],
    "warnings": []
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**Error Responses**:
```json
// 400 Bad Request - Missing required fields
{
  "success": false,
  "error": "employeeId, planAssignmentId, and coverageTier are required",
  "timestamp": "2024-01-01T12:00:00.000Z"
}

// 400 Bad Request - Invalid coverage tier
{
  "success": false,
  "error": "Invalid coverage tier for this plan assignment",
  "timestamp": "2024-01-01T12:00:00.000Z"
}

// 403 Forbidden - Access denied
{
  "success": false,
  "error": "Access denied: Cannot access employee data",
  "timestamp": "2024-01-01T12:00:00.000Z"
}

// 404 Not Found - Plan assignment not found
{
  "success": false,
  "error": "Plan assignment not found",
  "timestamp": "2024-01-01T12:00:00.000Z"
}

// 500 Internal Server Error
{
  "success": false,
  "error": "Internal server error",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**Edge Cases**:
- **Invalid coverage tier**: Returns 400 with available tiers
- **Dependent count mismatch**: Validates dependents against selected tier
- **Missing employee age/salary**: Uses defaults or returns partial calculation
- **Plan assignment expired**: Returns error with expiry information
- **Employee not eligible**: Returns cost but flags eligibility issues
- **Rate structure missing**: Returns error with configuration issue



**🎯 NEW: Get Enrollment Periods** ✅ IMPLEMENTED
```http
GET /api/pre-enrollment/employee-enrollments/enrollment-periods/:planAssignmentId
```

**Purpose**: Get all available enrollment periods for a plan assignment. Perfect for frontend to display enrollment options and validate user selections.

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
- SuperAdmin: Can view any plan assignment periods
- Broker: Can view plan assignments they have access to
- Company Admin: Can view their company's plan assignment periods
- Employee: No access (this is for planning purposes)

**📅 Period Information Provided:**
1. **Open Enrollment:** Standard annual enrollment period
2. **New Hire:** Configurable window from hire date
3. **Qualifying Life Event:** Configurable window from event date

**Authentication**: Required (JWT)
**Authorization**: Brokers, Company Admins only

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
user-id: <user_id>
```

**Success Response** (200):
```typescript
{
  success: true;
  planAssignmentId: string;
  enrollmentPeriods: {
    openEnrollment: {
      enabled: boolean;                  // Always true
      startDate: string;                 // ISO date
      endDate: string;                   // ISO date
      isCurrentlyActive: boolean;        // Whether we're in open enrollment now
      description: string;               // "Annual open enrollment period"
    };
    newHire: {
      enabled: boolean;                  // From plan assignment configuration
      windowDays: number;                // Days from hire date (configurable)
      description: string;               // Configurable description
    };
    qualifyingLifeEvent: {
      enabled: boolean;                  // From plan assignment configuration
      windowDays: number;                // Days from event date (configurable)
      allowedEvents: string[];           // Configurable list of allowed events
      description: string;               // Configurable description
    };
  };
  currentDate: string;                   // ISO timestamp for reference
}
```

**Example Response**:
```typescript
{
  "success": true,
  "planAssignmentId": "60f7b3b3b3b3b3b3b3b3b3b4",
  "enrollmentPeriods": {
    "openEnrollment": {
      "enabled": true,
      "startDate": "2024-11-01T00:00:00.000Z",
      "endDate": "2024-11-30T23:59:59.999Z",
      "isCurrentlyActive": false,
      "description": "Annual open enrollment period"
    },
    "newHire": {
      "enabled": true,
      "windowDays": 30,
      "description": "New hire enrollment window"
    },
    "qualifyingLifeEvent": {
      "enabled": true,
      "windowDays": 30,
      "allowedEvents": [
        "Marriage",
        "Divorce",
        "Birth",
        "Adoption",
        "Loss of Coverage",
        "Job Change",
        "Death",
        "Relocation",
        "Other"
      ],
      "description": "Qualifying life event enrollment window"
    }
  },
  "currentDate": "2024-01-15T10:30:00.000Z"
}
```

**3. Estimate Plan Assignment Costs** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/estimate-plan-costs
```

**Purpose**: Estimate costs for a plan assignment across all coverage tiers and employee scenarios without requiring a specific employee. Perfect for plan design, budgeting, and cost analysis.

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **Planning/Analysis Access:** Only admins and brokers can estimate costs
2. **Access Rights:**
   - **SuperAdmin:** Can estimate costs for any plan assignment
   - **Broker:** Can estimate costs for plan assignments they can view
   - **Company Admin:** Can estimate costs for their company's plan assignments only
   - **Employee:** No access (this is for planning/estimation purposes)

**📊 Cost Estimation Engine:**
1. **All Coverage Tiers:** Calculates costs for every tier in the plan assignment
2. **Multiple Scenarios:** Tests different employee demographics for comprehensive analysis
3. **Rate Structure Support:** Works with all rate structures (Composite, Age-Banded, Salary-Based, etc.)
4. **Payroll Frequency:** Provides costs in company's payroll frequency

**🎯 Default Employee Scenarios:**
If no custom scenarios provided, uses these representative profiles:
- Young Employee (25 years, $40,000 salary)
- Mid-Career Employee (35 years, $60,000 salary)
- Senior Employee (45 years, $80,000 salary)
- Executive Employee (55 years, $100,000 salary)

**Authentication**: Required (JWT)
**Authorization**: Brokers, Company Admins only

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

**Request Body**:
```typescript
{
  planAssignmentId: string;            // Required: Plan assignment ID to estimate costs for
  payrollFrequency?: string;           // Optional: "Weekly", "Biweekly", "Semi-Monthly", "Monthly" (default: "Monthly")
  scenarios?: Array<{                  // Optional: Custom employee scenarios (uses defaults if not provided)
    employeeAge: number;               // Employee age for age-banded calculations
    employeeSalary: number;            // Employee salary for salary-based calculations
    label: string;                     // Descriptive label for this scenario
  }>;
}
```

**Success Response** (200):
```typescript
{
  success: true;
  planAssignmentId: string;            // Plan assignment ID (matches implementation)
  costEstimations: Array<{
    tierName: string;                  // Coverage tier name (e.g., "Employee Only", "Family")
    baseCost: number;                  // Base monthly cost for this tier
    scenarios: Array<{
      employeeAge: number;             // Employee age used in calculation
      employeeSalary: number;          // Employee salary used in calculation
      label: string;                   // Scenario description
      calculationSuccess: boolean;     // Whether calculation succeeded
      cost?: {                         // Full cost breakdown (if calculation succeeded)
        // Backward compatibility
        employeeAmount: number;        // Monthly employee cost
        employerAmount: number;        // Monthly employer cost
        totalAmount: number;           // Monthly total cost

        // Detailed breakdown
        monthlyEmployeeAmount: number; // Monthly employee cost
        monthlyEmployerAmount: number; // Monthly employer cost
        monthlyTotalAmount: number;    // Monthly total cost

        annualEmployeeAmount: number;  // Annual employee cost
        annualEmployerAmount: number;  // Annual employer cost
        annualTotalAmount: number;     // Annual total cost

        payrollEmployeeAmount: number; // Per-paycheck employee cost
        payrollEmployerAmount: number; // Per-paycheck employer cost
        payrollTotalAmount: number;    // Per-paycheck total cost

        payrollFrequency: string;      // Payroll frequency used
        payPeriodsPerYear: number;     // Number of pay periods per year
      };
      error?: string;                  // Error message (if calculation failed)
    }>;
  }>;
  metadata: {
    payrollFrequency: string;          // Payroll frequency used for calculations
    scenarioCount: number;             // Number of scenarios calculated
    tierCount: number;                 // Number of coverage tiers
    calculatedAt: string;              // ISO timestamp when calculated (matches calculationDate from service)
  };
}
```

**Example Response**:
```typescript
{
  "success": true,
  "planAssignmentId": "60f7b3b3b3b3b3b3b3b3b3b4",
  "costEstimations": [
    {
      "tierName": "Employee Only",
      "baseCost": 400,
      "scenarios": [
        {
          "employeeAge": 25,
          "employeeSalary": 40000,
          "label": "Young Employee (25, $40k)",
          "calculationSuccess": true,
          "cost": {
            "employeeAmount": 90,
            "employerAmount": 360,
            "totalAmount": 450,
            "monthlyEmployeeAmount": 90,
            "monthlyEmployerAmount": 360,
            "monthlyTotalAmount": 450,
            "annualEmployeeAmount": 1080,
            "annualEmployerAmount": 4320,
            "annualTotalAmount": 5400,
            "payrollEmployeeAmount": 41.54,
            "payrollEmployerAmount": 166.15,
            "payrollTotalAmount": 207.69,
            "payrollFrequency": "Biweekly",
            "payPeriodsPerYear": 26
          }
        }
      ]
    },
    {
      "tierName": "Family",
      "baseCost": 1200,
      "scenarios": [
        // ... more scenarios
      ]
    }
  ],
  "metadata": {
    "payrollFrequency": "Biweekly",
    "scenarioCount": 4,
    "tierCount": 4,
    "calculatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing required fields
{
  error: "planAssignmentId is required"
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied to this plan assignment"
}

// 404 Not Found - Plan assignment not found
{
  error: "Plan assignment not found"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Use Cases**:
- **Plan Design Analysis**: Understand cost impact of different rate structures and contribution policies
- **Budget Planning**: Estimate total costs for different employee demographic mixes
- **Competitive Analysis**: Compare costs across different plan assignments
- **Payroll Integration**: Get costs in company's specific payroll frequency
- **Scenario Testing**: Test custom employee profiles for targeted analysis

**Edge Cases**:
- **No coverage tiers**: Returns empty estimations array
- **Invalid scenarios**: Skips invalid scenarios, continues with valid ones
- **Rate structure missing data**: Uses fallback calculations where possible
- **Calculation failures**: Returns error details for failed scenarios while succeeding for others

**4. Create Employee Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments
```

**Purpose**: Create new employee enrollment with comprehensive validation and automatic cost calculation

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **Write Access Required:** User must have write access to employee data
2. **Access Rights:**
   - **SuperAdmin:** Can create enrollment for any employee (can override enrollment period)
   - **Employee:** Can only create their own enrollment
   - **Employer/Admin:** Can create enrollment for employees in their company
   - **Broker:** Can create enrollment for employees in companies they manage AND their own company employees

**📅 Enrollment Period Validation (Type-Specific):**
1. **Open Enrollment:** Must be within plan assignment enrollment period (enrollmentStartDate ≤ today ≤ enrollmentEndDate)
2. **New Hire:** Must be within hire date + waiting period window (IGNORES normal enrollment period)
3. **Qualifying Life Event:** Must be within QLE event date + plan's QLE window (CAN override enrollment period)
4. **SuperAdmin Override:** SuperAdmins can create enrollments outside any period restrictions
5. **Intelligent Type Detection:** API automatically determines enrollment type if not explicitly provided

**✅ Comprehensive Pre-Enrollment Validation:**
1. **All Eligibility Checks:** Complete eligibility validation (same as Check Eligibility API)
2. **Duplicate Prevention:** No existing enrollment for employee/plan assignment combination
3. **Profile Completeness:** All required employee data must be present
4. **Hire Date Eligibility:** Employee must meet waiting period requirements
5. **Employee Class Eligibility:** Employee class must be eligible for plan

**👨‍👩‍👧‍👦 Dependent Validation:**
1. **Coverage Tier Matching:** Dependent count must match selected coverage tier
2. **Relationship Validation:** Valid relationship types (Spouse, Child, Domestic Partner)
3. **Age Validation:**
   - Spouse must be at least 18 years old
   - Child dependents must be under 26 years old
4. **Tier-Specific Rules:**
   - Employee Only: No dependents allowed
   - Employee + Spouse: Exactly one spouse/partner
   - Employee + Children: At least one child, no spouse
   - Family: At least one dependent (spouse or child)

**📅 Date Validation:**
1. **Effective Date:** Must be within plan coverage period (planEffectiveDate ≤ effectiveDate ≤ planEndDate)
2. **Future Dating:** Effective date cannot be in the past (configurable)
3. **Plan Alignment:** Effective date must align with plan assignment dates

**💰 Automatic Cost Calculation:**
1. **Real-Time Calculation:** Uses CostCalculationService for accurate pricing
2. **Employee Data Integration:** Automatically uses current employee age, salary, class type
3. **Rate Structure Application:** Applies correct rate structure based on plan assignment
4. **Contribution Policy:** Calculates employee and employer amounts based on contribution policies

**🎯 Intelligent Enrollment Type Detection:**
1. **Auto-Detection:** If no `enrollmentType` provided, API analyzes employee context to determine best type
2. **Type-Specific Validation:**
   - **Open Enrollment:** Validates against plan assignment enrollment period dates
   - **New Hire:** Uses hire date + waiting period window (ignores normal enrollment dates)
   - **QLE:** Uses event date + plan's QLE window (can override enrollment period)
3. **Period Override Logic:**
   - **New Hire:** Can enroll outside normal enrollment period if within hire window
   - **QLE:** Can enroll outside normal enrollment period if within QLE window and event type allowed
   - **SuperAdmin:** Can override any period restrictions

**📝 Enrollment Creation Process:**
1. **Single Enrollment:** Creates one enrollment covering all coverage subtypes in the plan
2. **Automatic Effective Date:** Computes effective date based on enrollment type and plan rules
3. **Status:** Created with "Pending" status by default
4. **Audit Trail:** Records enrollment date, computed effective date, and creation timestamp
5. **Cost Snapshot:** Stores calculated costs at time of enrollment
6. **Type Recording:** Records determined enrollment type for audit and reporting

**Authentication**: Required (JWT)
**Authorization**: Brokers, Employers, Employees (with access control)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

**Request Body**:
```typescript
{
  // 🎯 Plan Enrollment (Standard)
  employeeId: string;                  // Required: Employee user ID
  planAssignmentId?: string;           // Required for plan enrollment: Plan assignment ID
  coverageTier?: string;               // Required for plan enrollment: Coverage tier selection
  dependentIds?: string[];             // Optional: Array of dependent IDs from User.details.dependents

  // 🎯 Coverage-Level Waiver (Alternative)
  planAssignmentIds?: string[];        // Required for coverage waiver: List of plan assignment IDs to waive
  coverageType?: string;               // Required for coverage waiver: Coverage type being waived
  waiveReason?: string;                // Required for coverage waiver: Reason for waiving coverage

  // 🎯 Enrollment Type Configuration (Optional - API can auto-detect)
  enrollmentType?: string;             // Optional: "Open Enrollment", "New Hire", "Qualifying Life Event"

  // 🎯 New Hire Data (Required if enrollmentType = "New Hire")
  newHireDate?: string;                // Optional: ISO date when employee was hired

  // 🎯 Qualifying Life Event Data (Required if enrollmentType = "Qualifying Life Event")
  qualifyingLifeEvent?: {
    eventType: string;                 // Must be in plan's qualifyingLifeEventWindow.allowedEvents
    eventDate: string;                 // ISO date when the qualifying event occurred
    allowOutsideEnrollmentPeriod?: boolean; // Whether to allow enrollment outside normal period
  };
}
```

**📋 Request Types:**
- **Plan Enrollment**: Use `planAssignmentId`, `coverageTier` for individual plan enrollment
- **Coverage Waiver**: Use `planAssignmentIds`, `coverageType`, `waiveReason` to waive entire coverage type
```

**📅 Coverage Effective Date Computation:**
The API automatically computes when coverage starts based on employee eligibility and plan coverage period:

**🔄 Step-by-Step Logic:**

1. **Calculate Employee Eligibility Date:**
   ```typescript
   // New Hire: Employee becomes eligible after hire date + waiting period
   eligibilityDate = hireDate + waitingPeriod.days

   // QLE: Employee becomes eligible immediately on event date
   eligibilityDate = qualifyingLifeEvent.eventDate

   // Open Enrollment: Employee is eligible during enrollment period
   eligibilityDate = currentDate (during enrollment window)
   ```

2. **Determine Coverage Start Date:**
   ```typescript
   planCoveragePeriod = planEffectiveDate to planEndDate

   if (enrollmentType === 'Open Enrollment') {
     // Always starts on plan effective date
     coverageStartDate = planEffectiveDate
   } else {
     // For New Hire & QLE: coverage starts when eligible OR when plan starts
     coverageStartDate = max(eligibilityDate, planEffectiveDate)
   }
   ```

**📊 Detailed Examples:**

**Example 1: New Hire During Plan Year**
```typescript
hireDate: "2024-03-15"
waitingPeriod: 30 days
planEffectiveDate: "2024-01-01"
planEndDate: "2024-12-31"

// Calculation:
eligibilityDate = "2024-03-15" + 30 days = "2024-04-14"
planIsActive = true (plan started Jan 1)
coverageStartDate = max("2024-04-14", "2024-01-01") = "2024-04-14"
// Result: Coverage starts immediately upon eligibility
```

**Example 2: New Hire Before Plan Year**
```typescript
hireDate: "2023-11-01"
waitingPeriod: 30 days
planEffectiveDate: "2024-01-01"
planEndDate: "2024-12-31"

// Calculation:
eligibilityDate = "2023-11-01" + 30 days = "2023-12-01"
planIsActive = false (plan hasn't started yet)
coverageStartDate = max("2023-12-01", "2024-01-01") = "2024-01-01"
// Result: Coverage waits for plan to become effective
```

**Example 3: QLE During Plan Year**
```typescript
eventDate: "2024-06-15" (marriage)
planEffectiveDate: "2024-01-01"
planEndDate: "2024-12-31"

// Calculation:
eligibilityDate = "2024-06-15" (immediate)
planIsActive = true
coverageStartDate = max("2024-06-15", "2024-01-01") = "2024-06-15"
// Result: Coverage starts immediately on event date
```

**Example 4: Open Enrollment**
```typescript
enrollmentDate: "2023-11-15" (during open enrollment)
planEffectiveDate: "2024-01-01"
planEndDate: "2024-12-31"

// Calculation:
eligibilityDate = "2023-11-15" (eligible during enrollment period)
coverageStartDate = "2024-01-01" (always plan effective date)
// Result: Coverage starts when plan becomes effective
```

**Pre-Enrollment Validation**:
1. **Access Control**: User can create enrollment for employee
2. **Eligibility Validation**: All eligibility checks must pass
3. **Duplicate Prevention**: No existing enrollment for employee/plan
4. **Dependent Validation**: Dependents match selected coverage tier
5. **Date Validation**: Effective date within plan coverage period
6. **Cost Calculation**: Automatic cost calculation with current employee data

**Success Response** (201):
```json
{
  "success": true,
  "message": "Enrollment created and activated successfully",
  "enrollment": {
    "enrollmentId": "enrollment_id_here",
    "employeeId": "employee_id_here",
    "planAssignmentId": "plan_assignment_id_here",
    "coverageTier": "Employee + Spouse",
    "dependentCount": 1,
    "effectiveDate": "2024-01-01T00:00:00.000Z",
    "calculatedCost": {
      "employeeAmount": 150.00,
      "employerAmount": 350.00,
      "totalAmount": 500.00
    },
    "status": "Enrolled",
    "enrollmentType": "Open Enrollment"
  },
  "eligibilityDetails": {
    "determinedType": "Open Enrollment",
    "requestedType": "Open Enrollment",
    "wasEligible": true,
    "overriddenByAdmin": false
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**Error Responses**:
```json
// 400 Bad Request - Missing required fields
{
  "success": false,
  "error": "employeeId, planAssignmentId, and coverageTier are required",
  "timestamp": "2024-01-01T12:00:00.000Z"
}

// 400 Bad Request - Invalid enrollment type configuration
{
  "success": false,
  "error": "Invalid enrollment type configuration",
  "details": "QLE event type and date required for QLE enrollment",
  "timestamp": "2024-01-01T12:00:00.000Z"
}

// 400 Bad Request - QLE event type not allowed
{
  error: "Event type 'Marriage' not allowed for this plan",
  allowedEvents: string[]
}

// 400 Bad Request - Invalid dependent count
{
  error: "Invalid dependent count for Employee + Spouse",
  details: "Employee + Spouse requires 1 dependents, but 0 provided"
}



// 400 Bad Request - Not within valid enrollment period
{
  error: "Not within valid enrollment period",
  enrollmentType: "Open Enrollment" | "New Hire" | "Qualifying Life Event",
  details: "Outside enrollment period" | "New hire window expired" | "QLE window expired"
}

// 400 Bad Request - Enrollment creation failed
{
  error: "Enrollment creation failed",
  details: "Employee not eligible for enrollment" | "Duplicate enrollment exists" | "Invalid effective date",
  context: {
    employeeId: string,
    planAssignmentId: string,
    coverageTier: string,
    enrollmentType: string
  }
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied to employee data"
}

// 404 Not Found - Plan assignment not found
{
  error: "Plan assignment not found"
}

// 409 Conflict - Duplicate enrollment
{
  error: "Employee already enrolled in this plan assignment",
  existingEnrollmentId: string,
  existingStatus: string
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Edge Cases**:
- **Auto-detected enrollment type**: API determines type based on employee context if not provided
- **New Hire outside enrollment period**: Allowed if within hire date + waiting period window
- **QLE outside enrollment period**: Allowed if within event date + plan's QLE window and event type permitted
- **Invalid QLE event type**: Returns error with list of allowed events for the plan
- **Invalid dependent count**: Validates dependent count against coverage tier requirements using constants
- **Automatic effective date computation**: API computes effective date based on enrollment type and plan rules
- **Duplicate enrollment race condition**: Final check prevents concurrent duplicate enrollments
- **Expired enrollment windows**: Clear error messages with specific window information using safe date calculations
- **SuperAdmin override**: Can create enrollments outside any period restrictions
- **Employee already enrolled**: Returns error with existing enrollment details and status
- **Cost calculation failure**: Returns error with calculation details and context information

**4. Get Employee Enrollments (Enhanced with Status Filtering)**
```http
GET /api/pre-enrollment/employee-enrollments/employee/:employeeId
```

**Purpose**: Get enrollments for a specific employee with flexible status filtering

**Authentication**: Required (JWT)
**Authorization**: Brokers, Employers, Employees (with access control)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
user-id: <user_id>                    # Required for all authenticated requests
```

**Path Parameters**:
- `employeeId` (string): Employee user ID

**Query Parameters (Enhanced Status Filtering)**:
- `includeWaived` (boolean, default: true): Include waived enrollments
- `includeTerminated` (boolean, default: true): Include terminated enrollments
- `includeExpired` (boolean, default: false): Include expired enrollments
- `status` (string, optional): Filter by specific status (legacy support)
- `planAssignmentId` (string, optional): Filter by plan assignment

**Default Behavior**: Returns currently enrolled + waived + terminated (excludes expired)
**If all include parameters are false**: Returns only currently enrolled (Pending + Enrolled)

**Usage Examples**:
```http
# Default: Get current + waived + terminated (excludes expired)
GET /api/pre-enrollment/employee-enrollments/employee/USER_ID

# Get only currently enrolled (pending + enrolled)
GET /api/pre-enrollment/employee-enrollments/employee/USER_ID?includeWaived=false&includeTerminated=false

# Get all enrollments including expired
GET /api/pre-enrollment/employee-enrollments/employee/USER_ID?includeExpired=true

# Get only expired enrollments
GET /api/pre-enrollment/employee-enrollments/employee/USER_ID?includeWaived=false&includeTerminated=false&includeExpired=true

# Legacy status filtering (still supported)
GET /api/pre-enrollment/employee-enrollments/employee/USER_ID?status=Enrolled
```

**Access Control**:
- **SuperAdmins**: Can access any employee's enrollments
- **Brokers**: Can access enrollments for employees in their assigned companies
- **Company Admin**: Can access enrollments for their company employees
- **Employees**: Can access only their own enrollments

**Enhanced Response Format** (200):
```typescript
{
  success: true;
  enrollments: Array<{
    _id: string;                       // Enrollment ID
    planAssignmentId?: string;         // For plan enrollments: Plan assignment ID
    planAssignmentIds?: string[];      // For coverage waivers: List of waived plan IDs
    employeeId: string;
    companyId: string;
    coverageType: string;              // e.g., "Health Insurance"
    coverageSubTypes: string[];        // e.g., ["Medical", "Dental", "Vision"]
    coverageTier?: string;             // Selected coverage tier (plan enrollments only)
    employeeClassType: string;         // Employee class at enrollment time
    contribution?: {                   // For plan enrollments only
      employeeAmount: number;          // Employee monthly cost
      employerAmount: number;          // Employer monthly cost
      totalAmount: number;             // Total monthly premium
    };
    enrolledDependents?: Array<{       // For plan enrollments only
      dependentId: string;             // Reference to User.details.dependents._id
      enrollmentDate: string;          // When this dependent was enrolled
      effectiveDate: string;           // When coverage starts
      isActive: boolean;               // Whether dependent is active in this enrollment
      enrollmentSnapshot: {
        name: string;                  // Name at time of enrollment
        relationship: string;          // Relationship at time of enrollment
        dateOfBirth: string;          // DOB at time of enrollment
        gender: string;               // Gender at time of enrollment
      };
    }>;
    waiveReason?: string;              // For coverage waivers: Reason for waiving
    waiveDate?: string;                // For coverage waivers: Date waived
    status: string;                    // Current enrollment status
    enrollmentDate: string;            // ISO date when enrolled
    effectiveDate: string;             // ISO date when coverage begins
    terminationDate?: string;          // ISO date when coverage ends (if terminated)
    carrierMemberId?: string;          // Carrier-assigned member ID
    enrollmentType: string;            // "plan" or "coverage-waiver"
    createdAt: string;
    updatedAt: string;
    // Enriched data from related models
    planDetails?: {
      planName: string;
      coverageType: string;
      carrierName: string;
      customPlanName?: string;
    };
  }>;
  count: number;                       // Total number of enrollments
  // NEW: Separated enrollment types
  planEnrollments: Array<Enrollment>;  // Only plan-specific enrollments
  coverageWaivers: Array<Enrollment>;  // Only coverage-level waivers
  summary: {
    totalEnrollments: number;
    planEnrollmentsCount: number;
    coverageWaiversCount: number;
  };
  employee: {
    employeeId: string;
    name: string;
    companyId: string;
  };
  filters: {
    // Enhanced filtering information
    includeWaived: boolean;            // Whether waived enrollments are included
    includeTerminated: boolean;        // Whether terminated enrollments are included
    includeExpired: boolean;           // Whether expired enrollments are included
    appliedStatuses: string[];         // Array of statuses included in results
    // Legacy filters for backward compatibility
    status: string;                    // Applied status filter (legacy)
    planAssignmentId: string;          // Applied plan assignment filter
  };
  groupedByStatus: {
    pending: { count: number; enrollments: Array<Enrollment> };
    enrolled: { count: number; enrollments: Array<Enrollment> };
    waived: { count: number; enrollments: Array<Enrollment> };
    terminated: { count: number; enrollments: Array<Enrollment> };
    expired: { count: number; enrollments: Array<Enrollment> };
  };
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing employee ID
{
  error: "employeeId is required"
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied: Cannot access employee enrollments"
}

// 404 Not Found - Employee not found
{
  error: "Employee not found"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Enhanced Expiry Rules**:
- **Waived → Expired**: NOT ALLOWED - Waived enrollments remain waived for historical tracking
- **Terminated → Expired**: NOT ALLOWED - Terminated enrollments remain terminated for historical tracking
- **Pending → Expired**: ALLOWED - Only pending enrollments can become expired
- **Enrolled → Expired**: ALLOWED - Only enrolled enrollments can become expired
- **Historical Preservation**: Waived and terminated statuses are preserved to maintain enrollment history

**Edge Cases**:
- **No enrollments found**: Returns empty array with count 0
- **Employee in different company**: Access denied for company admins
- **Terminated enrollments**: Includes termination date and reason, preserved in system
- **Waived enrollments**: Preserved in system, never transition to expired
- **Multiple plan assignments**: Shows enrollments across all assignments
- **Enrichment failures**: Returns base enrollment data if related data unavailable
- **Status filtering**: Flexible filtering allows precise control over returned enrollment types

**5. Bulk Waive Plans** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/bulk-waive
```

**Purpose**: Create individual waived enrollments for multiple plan assignments before enrollment. This creates separate enrollment entries for each plan with "Waived" status.

**Authentication**: Required (JWT)
**Authorization**: Brokers, Employers, Employees (with access control)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>
```

**Request Body**:
```typescript
{
  employeeId: string;                  // Required: Employee user ID
  planAssignmentIds: string[];         // Required: Array of plan assignment IDs to waive
  waiveReason: string;                 // Required: Reason for waiving (same for all plans)
  waiveDate?: string;                  // Optional: ISO date when waived (default: current date)
  enrollmentType?: string;             // Optional: "Open Enrollment", "New Hire", "Qualifying Life Event" (default: "Open Enrollment")
}
```

**Success Response** (201 - All successful):
```typescript
{
  success: true;
  message: "Successfully waived 3 plan(s)";
  createdEnrollments: Array<{
    enrollmentId: string;              // Created enrollment ID
    planAssignmentId: string;          // Plan assignment ID
    planName: string;                  // Plan name
    coverageType: string;              // Coverage type
  }>;
  waiveReason: string;                 // Applied waive reason
  waiveDate: string;                   // ISO date when waived
  enrollmentType: string;              // Applied enrollment type
}
```

**Partial Success Response** (207 - Some successful, some failed):
```typescript
{
  success: true;
  message: "Partially successful: 2 waived, 1 failed";
  createdEnrollments: Array<{
    enrollmentId: string;
    planAssignmentId: string;
    planName: string;
    coverageType: string;
  }>;
  errors: Array<{
    planAssignmentId: string;
    error: string;                     // Error message for this plan
  }>;
  waiveReason: string;
  waiveDate: string;
  enrollmentType: string;
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing required fields
{
  error: "employeeId, planAssignmentIds, and waiveReason are required"
}

// 400 Bad Request - Invalid plan assignment IDs
{
  error: "planAssignmentIds must be a non-empty array"
}

// 400 Bad Request - All plans failed
{
  success: false;
  message: "Failed to waive any plans";
  errors: Array<{
    planAssignmentId: string;
    error: string;
  }>;
}

// 403 Forbidden - Access denied
{
  error: "Access denied: Cannot create enrollments for this employee"
}
```

**6. Bulk Enrollment in Multiple Plans** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/bulk
```

**Purpose**: Create multiple enrollments for a single employee in one atomic operation. If any enrollment fails, all enrollments are automatically rolled back to ensure data consistency.

**Key Features**:
- **Atomic Operation**: All enrollments succeed or all are rolled back
- **Cost Calculation**: Automatic cost calculation for each plan
- **Validation**: Comprehensive validation for each enrollment
- **Access Control**: User must have write access to employee
- **Rollback**: Automatic cleanup if any enrollment fails

**Authentication**: Required (JWT)
**Authorization**: Brokers, Employers, Employees (with access control)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>
```

**Request Body**:
```typescript
{
  employeeId: string;                    // Required: Employee user ID
  companyId: string;                     // Required: Company ID
  employeeClassType?: string;            // Optional: Employee class (default: "Full-Time")
  planSelections: Array<{                // Required: Array of plan selections
    planAssignmentId: string;            // Required: Plan assignment ID
    coverageTier: string;                // Required: Coverage tier ("Employee Only", "Employee & Spouse", etc.)
    dependentIds?: string[];             // Optional: Array of dependent IDs (default: [])
    enrollmentType?: string;             // Optional: "Open Enrollment", "New Hire", "Qualifying Life Event" (default: "Open Enrollment")
    qualifyingLifeEvent?: {              // Optional: Required if enrollmentType is "Qualifying Life Event"
      eventType: string;                 // Must be in plan's allowed events
      eventDate: string;                 // ISO date when event occurred
      documentationUrl?: string;         // Optional: Supporting documentation
    };
  }>;
  effectiveDate?: string;                // Optional: ISO date for coverage start (default: current date)
}
```

**Success Response** (201 - All enrollments successful):
```json
{
  "success": true,
  "message": "Successfully enrolled employee in 3 plans",
  "enrollmentIds": [
    "enrollment_id_1",
    "enrollment_id_2",
    "enrollment_id_3"
  ],
  "calculatedCosts": [
    {
      "employeeAmount": 150.00,
      "employerAmount": 350.00,
      "totalAmount": 500.00,
      "payrollFrequency": "Monthly"
    },
    {
      "employeeAmount": 25.00,
      "employerAmount": 75.00,
      "totalAmount": 100.00,
      "payrollFrequency": "Monthly"
    }
  ],
  "successCount": 3,
  "failureCount": 0,
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**Error Response** (400 - Enrollment failed with rollback):
```json
{
  "success": false,
  "error": "Enrollment failed for plan 2: Coverage tier 'Employee & Spouse' not found. All enrollments have been rolled back.",
  "failedPlan": "plan_assignment_id_2",
  "details": "Coverage tier validation failed",
  "rollbackPerformed": true,
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**Validation Error Responses**:
```typescript
// 400 Bad Request - Missing required fields
{
  error: "Missing required fields";
  required: ["employeeId", "companyId", "planSelections"];
  received: {
    employeeId: true,
    companyId: false,
    planSelections: true
  }
}

// 400 Bad Request - Empty plan selections
{
  error: "At least one plan selection is required"
}

// 400 Bad Request - Invalid plan selection
{
  error: "Invalid plan selection at index 1";
  required: ["planAssignmentId", "coverageTier"];
  received: {
    planAssignmentId: "plan-123",
    coverageTier: null
  }
}

// 403 Forbidden - Access denied
{
  error: "Access denied: Cannot create enrollments for this employee"
}
```

**Business Logic & Validation Rules**:

**🔐 Access Control Validation:**
1. **Write Access Required:** User must have write access to employee data
2. **Access Rights:**
   - **SuperAdmin:** Can create enrollment for any employee
   - **Employee:** Can only create their own enrollment
   - **Employer/Admin:** Can create enrollment for employees in their company
   - **Broker:** Can create enrollment for employees in companies they manage

**🏗️ Service Layer Architecture:**
All enrollment validation and business logic is now centralized in the service layer (`EmployeeEnrollmentService`) for consistency across all APIs:
- **Single Enrollment API:** Uses service validation
- **Bulk Enrollment API:** Uses same service validation with rollback
- **Update/Delete/Waive APIs:** Use service period validation
- **Terminate API:** No period validation (can terminate anytime)

**🔄 Atomic Transaction Logic:**
1. **Sequential Processing:** Enrollments are processed one by one
2. **Failure Detection:** If any enrollment fails, processing stops immediately
3. **Automatic Rollback:** All previously created enrollments are deleted
4. **Error Reporting:** Clear indication of which plan failed and why
5. **Data Consistency:** Ensures no partial enrollments remain in the system

**📋 Individual Enrollment Validation:**
Each enrollment in the batch undergoes the same validation as single enrollment (all handled in service layer):
- **Profile Completeness:** Employee must have complete profile data
- **Hire Date Eligibility:** Employee must meet hire date requirements
- **Employee Class Eligibility:** Employee class must match plan requirements
- **Plan Assignment Status:** Plan assignment must be active and valid
- **Enrollment Period Validation:** Based on enrollment type (unless SuperAdmin override):
  - **Open Enrollment:** Must be within enrollment window dates
  - **New Hire:** Must be within new hire window (hire date + waiting period)
  - **QLE:** Must have valid event type and be within QLE window
- **Coverage Tier Validation:** Tier must exist and match dependent count
- **Cost Calculation:** Automatic cost calculation with contribution policies

**💰 Cost Calculation:**
- Each enrollment gets individual cost calculation based on plan assignment
- Supports all rate structures (Composite, Age-Banded, Four-Tier, Salary-Based)
- Returns detailed cost breakdown including payroll deductions
- Summary provides total monthly cost across all enrollments

**Edge Cases**:
- **Empty plan selections**: Returns 400 error
- **Duplicate plan assignments**: Allowed (creates separate enrollments)
- **Invalid coverage tiers**: Triggers rollback of all enrollments
- **Cost calculation failure**: Triggers rollback of all enrollments
- **Access control failure**: Prevents any enrollments from being created
- **Partial success not allowed**: Either all succeed or all are rolled back

**7. Get All Enrollments (Role-Based Access)**
```http
GET /api/pre-enrollment/employee-enrollments
```

**Purpose**: Get all enrollments based on user role and access permissions

**Authentication**: Required (JWT)
**Authorization**: All authenticated users

**Query Parameters (All Optional)**:
- `status` (string): Filter by enrollment status (`Enrolled`, `Waived`, `Pending`, `Terminated`)
- `companyId` (string): Filter by company ID
- `planAssignmentId` (string): Filter by plan assignment ID
- `employeeId` (string): Filter by employee ID
- `coverageType` (string): Filter by coverage type
- `effectiveDate` (string): Filter by effective date (ISO format)
- `enrollmentDate` (string): Filter by enrollment date (ISO format)
- `page` (number): Page number for pagination (default: 1)
- `limit` (number): Number of results per page (default: 20)

**Access Control**:
- **SuperAdmins**: Can see all enrollments
- **Brokers**: Can see enrollments for their assigned companies
- **Company Admin**: Can see enrollments for their company employees
- **Employees**: Can see only their own enrollments

**Response**:
```typescript
{
  enrollments: Array<{
    _id: string;
    planAssignmentId: string;
    employeeId: string;
    companyId: string;
    coverageType: string;
    coverageSubTypes: string[];
    coverageTier: string;
    employeeClassType: string;
    contribution: {
      employeeAmount: number;
      employerAmount: number;
      totalAmount: number;
    };
    status: string;
    enrollmentDate: string;
    effectiveDate: string;
    terminationDate?: string;
    createdAt: string;
    // Populated fields
    employee?: {
      firstName: string;
      lastName: string;
      email: string;
    };
    planAssignment?: {
      plan: {
        planName: string;
        coverageType: string;
      };
      company: {
        companyName: string;
      };
    };
  }>;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
  };
}
```

**6. Update Enrollment** ✅ IMPLEMENTED
```http
PUT /api/pre-enrollment/employee-enrollments/:enrollmentId
```

**Purpose**: Update an existing enrollment with automatic cost recalculation and comprehensive validation

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **Write Access Required:** User must have write access to the enrollment
2. **Access Rights:**
   - **SuperAdmin:** Can update any enrollment
   - **Employee:** Can only update their own enrollment
   - **Employer/Admin:** Can update enrollments for employees in their company
   - **Broker:** Can update enrollments for employees in companies they manage AND their own company employees

**📋 Enrollment Status Validation:**
1. **Updateable Statuses:** Can only update enrollments with status "Pending" or "Enrolled"
2. **Status Restrictions:**
   - **Terminated:** Cannot be updated (must use reinstate if available)
   - **Waived:** Cannot be updated (must use reinstate if available)
3. **Status Transition Validation:** If status is being changed, must follow valid transitions

**🔄 Field Update Validation:**
1. **Coverage Tier Changes:**
   - Must be valid tier for the plan assignment
   - Triggers automatic cost recalculation
   - Must validate dependent count against new tier using constants
2. **Dependent Updates:**
   - Dependent count must match selected coverage tier (validated using COVERAGE_TIER_REQUIREMENTS)
   - Age validation for all dependents
   - Relationship validation (Spouse, Child, Domestic Partner)
3. **Effective Date Policy:**
   - **NOT UPDATEABLE** - Effective dates are computed automatically during creation
   - Maintains consistency with enrollment type business rules
   - If effective date change needed, recreate enrollment with appropriate type
4. **Status Changes:**
   - Must follow valid status transitions
   - Triggers appropriate business logic validation

**💰 Automatic Cost Recalculation:**
1. **Trigger Conditions:** Cost recalculation triggered when:
   - Coverage tier changes
   - Employee class type changes
   - Dependent count changes
2. **Real-Time Calculation:** Uses current employee data (age, salary) for accurate pricing
3. **Cost Update:** Updates contribution amounts in enrollment record

**📝 Audit Trail:**
1. **Change Tracking:** All updates are logged with user ID and timestamp
2. **Field-Level Tracking:** Tracks which specific fields were updated
3. **Previous Values:** Maintains history of changes for compliance

**Authentication**: Required (JWT)
**Authorization**: Brokers, Employers, Employees (with access control)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

**Path Parameters**:
- `enrollmentId` (string): Enrollment ID to update

**Request Body** (All fields optional):
```typescript
{
  coverageTier?: string;                 // Optional: New coverage tier
  dependentIds?: string[];               // Optional: Updated array of dependent IDs from User.details.dependents
                                       // These will replace existing enrolled dependents
  status?: string;                       // Optional: New status (must be valid transition)

  // 🎯 NEW: Enrollment Type Updates
  enrolledUnder?: string;                // Optional: Change enrollment type ("Open Enrollment", "New Hire", "Qualifying Life Event")

  // 🎯 NEW: QLE Data Updates (required if enrolledUnder = "Qualifying Life Event")
  qualifyingLifeEvent?: {
    eventType: string;                   // Must be in plan's qualifyingLifeEventWindow.allowedEvents
    eventDate: string;                   // ISO date when the qualifying event occurred
    documentationUrl?: string;           // Optional: URL to supporting documentation
    allowedEnrollmentWindow?: {
      start: string;                     // ISO date when enrollment window opens
      end: string;                       // ISO date when enrollment window closes
    };
    processedBy?: string;                // Optional: User ID who processed the QLE
    processedAt?: string;                // Optional: ISO date when QLE was processed
  };
}
```

**📅 Automatic Effective Date Recomputation:**
When `enrolledUnder` (enrollment type) or `qualifyingLifeEvent` data is updated, the API automatically recomputes the effective date based on the new enrollment type and business rules:
- **New Hire**: Uses employee hire date + waiting period from plan assignment
- **QLE**: Uses QLE event date or plan effective date, whichever is later
- **Open Enrollment**: Uses plan effective date

**🔄 How Update API Distinguishes Enrollment Types:**
1. **Current enrollment type** is read from `existingEnrollment.enrolledUnder`
2. **New enrollment type** comes from `updateData.enrolledUnder` (if provided)
3. **QLE data** comes from `updateData.qualifyingLifeEvent` (if provided)
4. **Employee hire date** is read from `employee.details.hireDate` in User model
5. **Effective date** is automatically recomputed when enrollment type changes

**Business Rules**:
- Can only update if enrollment status is `Pending` or `Enrolled`
- Coverage tier changes trigger automatic cost recalculation
- Dependent changes must validate against selected coverage tier using constants
- Status changes must follow valid transitions
- **Enrollment type changes** trigger automatic effective date recomputation
- **QLE event type validation** against plan assignment allowed events
- **Employee hire date required** for New Hire enrollment type updates
- Updates are logged for audit trail

**Success Response** (200):
```typescript
{
  success: true;
  message: "Enrollment updated successfully";
  enrollment: {
    _id: string;
    planAssignmentId: string;
    employeeId: string;
    companyId: string;
    coverageType: string;
    coverageSubTypes: string[];
    coverageTier: string;                // Updated tier
    employeeClassType: string;
    contribution: {
      employeeAmount: number;            // Recalculated if tier changed
      employerAmount: number;            // Recalculated if tier changed
      totalAmount: number;               // Recalculated if tier changed
    };
    enrolledDependents: Array<{          // Updated enrolled dependents
      dependentId: string;             // Reference to User.details.dependents._id
      enrollmentDate: string;          // When this dependent was enrolled
      effectiveDate: string;           // When coverage starts
      isActive: boolean;               // Whether dependent is active in this enrollment
      enrollmentSnapshot: {
        name: string;                  // Name at time of enrollment
        relationship: string;          // Relationship at time of enrollment
        dateOfBirth: string;          // DOB at time of enrollment
        gender: string;               // Gender at time of enrollment
      };
    }>;
    status: string;                      // Updated status
    enrollmentDate: string;
    effectiveDate: string;               // Original effective date (not updateable)
    updatedAt: string;                   // ISO timestamp
  };
  costRecalculated: boolean;             // Whether cost was recalculated
  previousValues?: {                     // Previous values for audit
    coverageTier?: string;
    contribution?: object;
    dependents?: Array<object>;
    status?: string;
  };
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing enrollment ID
{
  error: "enrollmentId is required"
}

// 400 Bad Request - Invalid status transition
{
  error: "Cannot edit enrollment with status: Terminated"
}

// 400 Bad Request - Invalid coverage tier
{
  error: "Invalid coverage tier for this plan assignment"
}

// 400 Bad Request - Dependent validation failed
{
  error: "Dependent validation failed",
  details: "Family tier requires at least one dependent"
}

// 400 Bad Request - QLE event type not allowed
{
  error: "Event type 'Marriage' not allowed for this plan",
  allowedEvents: ["Birth", "Adoption", "Loss of Coverage"]
}

// 400 Bad Request - Missing hire date for New Hire enrollment
{
  error: "Employee hire date required for New Hire enrollment type"
}

// 400 Bad Request - Missing QLE event date
{
  error: "QLE event date required for Qualifying Life Event enrollment type"
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied: Cannot edit enrollment"
}

// 404 Not Found - Enrollment not found
{
  error: "Enrollment not found"
}

// 500 Internal Server Error - Update failed
{
  error: "Failed to update enrollment"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Edge Cases**:
- **Terminated enrollment**: Cannot be updated, returns 400
- **Coverage tier change**: Automatically recalculates costs and validates dependent count
- **Invalid dependent count**: Validates against tier requirements using constants
- **Enrollment type changes**: Triggers automatic effective date recomputation
- **QLE event type validation**: Validates against plan assignment allowed events
- **Missing hire date**: Required for New Hire enrollment type updates
- **Missing QLE data**: Required for Qualifying Life Event enrollment type updates
- **Cost calculation failure**: Updates enrollment but flags cost issue
- **Concurrent updates**: Uses optimistic locking to prevent conflicts

**7. Delete Enrollment** ✅ IMPLEMENTED
```http
DELETE /api/pre-enrollment/employee-enrollments/:enrollmentId
```

**Purpose**: Permanently remove an enrollment from the system with comprehensive validation and audit trail.

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **Write Access Required:** User must have write access to the enrollment
2. **Access Rights:**
   - **SuperAdmin:** Can delete any enrollment
   - **Employee:** Can delete their own enrollment (with restrictions)
   - **Employer/Admin:** Can delete enrollments for employees in their company
   - **Broker:** Can delete enrollments for employees in companies they manage

**📋 Enrollment Status Validation:**
1. **Deletable Statuses:** Can only delete enrollments with status "Pending" or "Waived"
2. **Status Restrictions:**
   - **Enrolled:** Cannot delete active coverage (use terminate instead)
   - **Terminated:** Cannot delete (use reinstate if needed)
   - **Expired:** Cannot delete (read-only status)

**📅 Enrollment Period Validation:**
1. **Within Period:** Must be within enrollment period (unless SuperAdmin override)
2. **SuperAdmin Override:** SuperAdmins can delete outside enrollment period
3. **Business Rule:** Deletion is permanent and cannot be undone

**Authentication**: Required (JWT)
**Authorization**: Brokers, Employers, Employees (with access control)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
user-id: <user_id>                    # Required for all authenticated requests
```

**Path Parameters**:
- `enrollmentId` (string): Enrollment ID to delete

**Business Rules**:
- Can only delete enrollments with status `Pending` or `Waived`
- Must be within enrollment period (unless SuperAdmin)
- Deletion is permanent and cannot be undone
- Creates audit trail entry before deletion
- Validates user access to the enrollment

**Success Response** (200):
```typescript
{
  success: true;
  message: "Enrollment deleted successfully";
  deletedEnrollment: {
    enrollmentId: string;              // Deleted enrollment ID
    employeeId: string;                // Employee ID
    planAssignmentId: string;          // Plan assignment ID
    status: string;                    // Status at time of deletion
    deletedBy: string;                 // User ID who deleted
    deletedAt: string;                 // Deletion timestamp (ISO)
  };
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing enrollment ID
{
  error: "enrollmentId is required"
}

// 400 Bad Request - Invalid status for deletion
{
  error: "Cannot delete enrollment with status: Enrolled",
  allowedStatuses: ["Pending", "Waived"],
  suggestion: "Use terminate API to end active coverage"
}

// 400 Bad Request - Outside enrollment period
{
  error: "Cannot delete enrollment outside of enrollment period",
  details: "Enrollment period ended on 2024-01-31",
  suggestion: "Contact administrator for assistance"
}

// 400 Bad Request - Expired enrollment
{
  error: "Cannot delete expired enrollment",
  currentStatus: "Expired",
  planEndDate: "2024-12-31T23:59:59Z"
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied: Cannot delete enrollment"
}

// 404 Not Found - Enrollment not found
{
  error: "Enrollment not found"
}

// 500 Internal Server Error - Deletion failed
{
  error: "Failed to delete enrollment"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Edge Cases**:
- **Active enrollment**: Cannot delete enrolled coverage (use terminate)
- **Terminated enrollment**: Cannot delete (preserved for audit)
- **Expired enrollment**: Cannot delete (read-only status)
- **Outside enrollment period**: Validates period unless SuperAdmin override
- **Concurrent deletion**: Handles race conditions gracefully
- **Audit trail**: Creates audit entry before permanent deletion

**8. Terminate Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/terminate
```

**Purpose**: Terminate an active enrollment with termination date and reason, maintaining comprehensive audit trail

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **Write Access Required:** User must have write access to the enrollment
2. **Access Rights:**
   - **SuperAdmin:** Can terminate any enrollment
   - **Employee:** Can terminate their own enrollment
   - **Employer/Admin:** Can terminate enrollments for employees in their company
   - **Broker:** Can terminate enrollments for employees in companies they manage

**📋 Enrollment Status Validation:**
1. **Terminable Statuses:** Can only terminate enrollments with status "Pending" or "Enrolled"
2. **Status Restrictions:**
   - **Already Terminated:** Cannot terminate again (returns 400 with current status)
   - **Waived:** Cannot terminate (already inactive)
3. **Active Enrollment Check:** Validates enrollment exists and is accessible

**📅 Termination Date Validation:**
1. **Date Requirements:**
   - Termination date must be provided (required field)
   - Cannot be before enrollment effective date
   - Cannot be in the past (configurable grace period)
2. **Plan Period Validation:**
   - Should be within plan coverage period for consistency
   - Validates against plan assignment end date

**📅 Enrollment Period Validation:**
❌ **NO ENROLLMENT PERIOD RESTRICTION** - Termination can happen anytime during coverage period
- **Business Rationale**: Termination is an administrative action that can occur due to job loss, life changes, or other circumstances outside enrollment periods
- **Access Control**: Only authorized users (SuperAdmin/Broker/Admin) can terminate enrollments
- **Updated Logic**: Unlike other enrollment operations, termination does not require enrollment period validation

**📝 Termination Reason Validation:**
1. **Reason Required:** Termination reason must be provided for audit and compliance purposes
2. **Documentation:** Reason stored in waiveReason field for consistency with data model
3. **Audit Trail:** Creates comprehensive audit entry with user ID, timestamp, and reason

**🔄 Status Transition Process:**
1. **Status Update:** Changes enrollment status from current status to "Terminated"
2. **Field Updates:** Sets terminationDate and waiveReason (termination reason)
3. **Data Integrity:** Updates updatedAt timestamp and maintains data consistency

**💰 Business Impact:**
1. **Cost Implications:** Termination affects company's total enrollment costs and reporting
2. **Carrier Integration:** Termination data available for carrier reporting and member management
3. **Compliance:** Maintains audit trail for regulatory and compliance requirements

**Authentication**: Required (JWT)
**Authorization**: Brokers, Employers, Employees (with access control)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

**Path Parameters**:
- `enrollmentId` (string): Enrollment ID to terminate

**Request Body**:
```typescript
{
  terminationDate: string;               // Required: ISO date string
  terminationReason: string;             // Required: Reason for termination
}
```

**Business Rules**:
- Can only terminate enrollments with status `Enrolled` or `Pending`
- Termination date cannot be before enrollment effective date
- Termination date cannot be after plan assignment end date
- Creates comprehensive audit trail entry
- Validates user access to the enrollment

**Success Response** (200):
```typescript
{
  success: true;
  message: "Enrollment terminated successfully";
  enrollment: {
    _id: string;
    planAssignmentId: string;
    employeeId: string;
    companyId: string;
    coverageType: string;
    coverageSubTypes: string[];
    coverageTier: string;
    employeeClassType: string;
    contribution: {
      employeeAmount: number;
      employerAmount: number;
      totalAmount: number;
    };
    dependents: Array<{
      name: string;
      relationship: string;
      dateOfBirth: string;
    }>;
    status: "Terminated";                // Updated status
    enrollmentDate: string;
    effectiveDate: string;
    terminationDate: string;             // Termination date
    terminationReason: string;           // Termination reason
    updatedAt: string;                   // ISO timestamp
  };
  employee: {
    employeeId: string;
    name: string;
    companyId: string;
  };
  planAssignment: {
    planAssignmentId: string;
    planName: string;
    coverageType: string;
  };
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing enrollment ID
{
  error: "enrollmentId is required"
}

// 400 Bad Request - Missing required fields
{
  error: "terminationDate and terminationReason are required"
}

// 400 Bad Request - Invalid status for termination
{
  error: "Cannot terminate enrollment with status: Terminated"
}

// 400 Bad Request - Invalid termination date
{
  error: "Termination date cannot be before effective date" |
         "Termination date cannot be after plan end date"
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied: Cannot terminate enrollment"
}

// 404 Not Found - Enrollment not found
{
  error: "Enrollment not found"
}

// 500 Internal Server Error - Termination failed
{
  error: "Failed to terminate enrollment"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Edge Cases**:
- **Already terminated**: Returns 400 with current status
- **Termination date in past**: Validates against effective date
- **Termination date in future**: Validates against plan end date
- **Employee access**: Employees cannot terminate their own enrollments
- **Concurrent termination**: Handles race conditions gracefully
- **Audit trail failure**: Logs termination even if audit fails

## 🎯 **STATUS MANAGEMENT APIS**

The following APIs provide comprehensive enrollment status management with proper business logic validation and audit trails.

### **📋 Status Transition Map**

Valid enrollment status transitions:
```
Pending → [Enrolled, Waived, Terminated]
Enrolled → [Waived, Terminated]
Waived → [Pending, Enrolled]
Terminated → [Pending, Enrolled]
```

**9. Waive Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/waive
```

**Purpose**: Employee explicitly declines coverage with documented reason and audit trail.

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **User Authentication**: Valid user ID required in headers
2. **Employee Access Rights**:
   - **SuperAdmin**: Can waive any enrollment
   - **Company Admin**: Can waive enrollments for their company employees
   - **Broker**: Can waive enrollments for their own company employees (self-enrollment)
   - **Employee**: Cannot waive their own enrollments (admin-controlled process)

**📊 Status Transition Validation:**
1. **Valid From Status**: Can waive enrollments with status "Pending" or "Enrolled"
2. **Invalid Transitions**: Cannot waive "Waived" or "Terminated" enrollments
3. **Transition Requirements**: Must provide waive reason

**📅 Enrollment Period Validation:**
1. **Within Period**: Must be within enrollment period (unless SuperAdmin override)
2. **SuperAdmin Override**: SuperAdmins can waive outside enrollment period
3. **Period Bypass Warning**: Logs when enrollment period is bypassed

**Authentication**: Required (JWT)
**Authorization**: Admins, Brokers only (employees cannot waive their own enrollments)

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

**Request Body**:
```typescript
{
  waiveReason: string;                 // Required: Reason for waiving coverage (e.g., "Covered by spouse's plan")
}
```

**Success Response** (200):
```typescript
{
  success: true;
  message: "Enrollment waived successfully";
  enrollment: {
    enrollmentId: string;              // Enrollment ID
    employeeId: string;                // Employee ID
    planAssignmentId: string;          // Plan assignment ID
    status: "Waived";                  // New status
    waiveReason: string;               // Waive reason provided
    waiveDate: string;                 // ISO timestamp when waived
  };
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing waive reason
{
  error: "waiveReason is required"
}

// 400 Bad Request - Invalid status transition
{
  error: "Cannot waive enrollment with status: Terminated",
  validTransitions: ["Pending", "Enrolled"]
}

// 400 Bad Request - Outside enrollment period
{
  error: "Cannot waive enrollment outside of enrollment period",
  details: "Enrollment period ended on 2024-01-31"
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied to employee data"
}

// 404 Not Found - Enrollment not found
{
  error: "Enrollment not found"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Use Cases**:
- **Employee Declines Coverage**: Employee chooses not to enroll in offered plan
- **Alternative Coverage**: Employee has coverage through spouse or other source
- **Cost Concerns**: Employee cannot afford the premium
- **Administrative Correction**: Fixing enrollment errors

**Edge Cases**:
- **Already waived**: Returns 400 with current status and valid transitions
- **Outside enrollment period**: Validates period unless SuperAdmin override
- **Concurrent waive operations**: Handles race conditions with optimistic locking
- **Audit trail**: Maintains complete history of status changes

**10. Reinstate Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/reinstate
```

**Purpose**: Restore a waived or terminated enrollment to active status with comprehensive eligibility validation.

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **Administrative Control**: Only admins and brokers can reinstate enrollments
2. **Company Scoping**: Admins limited to their company's enrollments
3. **Broker Access**: Brokers can reinstate for their own company employees

**📊 Comprehensive Eligibility Validation:**
1. **Plan Assignment Status**: Must be Active
2. **Enrollment Period**: Must be within period (SuperAdmin can override)
3. **Employee Class**: Must be eligible employee class
4. **Waiting Period**: Must satisfy hire date + waiting period
5. **Profile Completeness**: All required employee data must be present
6. **No Duplicates**: No existing active enrollments for same plan (for new enrollments)

**🔄 Status Transition Validation:**
1. **Valid From Status**: Can reinstate "Waived" or "Terminated" enrollments
2. **Target Status**: Can reinstate to "Pending" or "Enrolled"
3. **Business Rules**: Reinstatement requires reason and may need new effective date

**Authentication**: Required (JWT)
**Authorization**: Admins, Brokers only

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

**Request Body**:
```typescript
{
  newStatus: "Pending" | "Enrolled";   // Required: Target status after reinstatement
  reinstateReason: string;             // Required: Reason for reinstatement
  newEffectiveDate?: string;           // Optional: New effective date (ISO format)
}
```

**Success Response** (200):
```typescript
{
  success: true;
  message: "Enrollment reinstated to Enrolled status successfully";
  enrollment: {
    enrollmentId: string;              // Enrollment ID
    employeeId: string;                // Employee ID
    planAssignmentId: string;          // Plan assignment ID
    status: "Enrolled";                // New status
    effectiveDate: string;             // Effective date (ISO)
    reinstateReason: string;           // Reinstatement reason
  };
  eligibilityWarnings?: string[];      // Any eligibility warnings (e.g., enrollment period bypass)
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Missing required fields
{
  error: "newStatus is required and must be either \"Pending\" or \"Enrolled\""
}

// 400 Bad Request - Missing reinstate reason
{
  error: "reinstateReason is required"
}

// 400 Bad Request - Invalid status transition
{
  error: "Cannot reinstate enrollment from Enrolled to Pending",
  validTransitions: ["Waived", "Terminated"]
}

// 400 Bad Request - Eligibility failure
{
  error: "Employee not eligible for reinstatement",
  reasons: [
    "Plan assignment is Inactive. Only Active assignments allow enrollment.",
    "Enrollment period has ended. Ended: 1/31/2024"
  ],
  checks: {
    planAssignmentActive: false,
    enrollmentPeriod: false,
    employeeClass: true,
    waitingPeriod: true,
    profileComplete: true,
    noDuplicates: true,
    adminOverride: false
  }
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied to employee data"
}

// 404 Not Found - Enrollment not found
{
  error: "Enrollment not found"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Use Cases**:
- **Employee Changes Mind**: Previously waived employee wants coverage
- **Life Event**: Qualifying life event allows reinstatement
- **Administrative Correction**: Fixing incorrect termination or waiver
- **Re-hire**: Employee returns to company and needs coverage restored

**Edge Cases**:
- **Eligibility Changes**: Validates current eligibility, not historical
- **Enrollment Period**: SuperAdmin can override period restrictions
- **Cost Recalculation**: May trigger cost recalculation if employee data changed
- **Effective Date**: Can set new effective date or use original

**11. Activate Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/activate
```

**Purpose**: Move enrollment from "Pending" to "Enrolled" status, making coverage effective.

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**
1. **Administrative Control**: Only admins and brokers can activate enrollments
2. **Company Scoping**: Admins limited to their company's enrollments
3. **Employee Restriction**: Employees cannot activate their own enrollments

**📊 Eligibility Validation for Activation:**
1. **Current Status**: Must be "Pending" status
2. **Plan Assignment**: Must still be Active
3. **Employee Eligibility**: Must still meet all eligibility requirements
4. **Profile Completeness**: All required data must be present

**📅 Effective Date Validation:**
1. **Date Validation**: Activation date cannot be in the past (unless SuperAdmin)
2. **Plan Period**: Must be within plan assignment period
3. **Default Date**: Uses current date if no activation date provided

**Authentication**: Required (JWT)
**Authorization**: Admins, Brokers only

**Request Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

**Request Body**:
```typescript
{
  activationDate?: string;             // Optional: Activation date (ISO format, defaults to current date)
}
```

**Success Response** (200):
```typescript
{
  success: true;
  message: "Enrollment activated successfully";
  enrollment: {
    enrollmentId: string;              // Enrollment ID
    employeeId: string;                // Employee ID
    planAssignmentId: string;          // Plan assignment ID
    status: "Enrolled";                // New status
    effectiveDate: string;             // Coverage effective date (ISO)
    activatedBy: string;               // User ID who activated
    activatedAt: string;               // Activation timestamp (ISO)
  };
  eligibilityWarnings?: string[];      // Any eligibility warnings
}
```

**Error Responses**:
```typescript
// 400 Bad Request - Invalid status for activation
{
  error: "Cannot activate enrollment with status: Enrolled",
  validTransitions: ["Pending"]
}

// 400 Bad Request - Invalid activation date
{
  error: "Activation date cannot be in the past"
}

// 400 Bad Request - Eligibility failure
{
  error: "Employee not eligible for enrollment activation",
  reasons: [
    "Plan assignment is Inactive. Only Active assignments allow enrollment."
  ],
  checks: {
    planAssignmentActive: false,
    enrollmentPeriod: true,
    employeeClass: true,
    waitingPeriod: true,
    profileComplete: true,
    noDuplicates: true,
    adminOverride: false
  }
}

// 401 Unauthorized - Missing user ID
{
  error: "User ID required in headers"
}

// 403 Forbidden - Access denied
{
  error: "Access denied to employee data"
}

// 404 Not Found - Enrollment not found
{
  error: "Enrollment not found"
}

// 500 Internal Server Error
{
  error: "Internal server error"
}
```

**Use Cases**:
- **Open Enrollment**: Activate pending enrollments at plan effective date
- **New Hire**: Activate enrollment after waiting period satisfied
- **Administrative Processing**: Batch activation of approved enrollments
- **Immediate Coverage**: Activate enrollment for immediate coverage needs

**Edge Cases**:
- **Batch Activation**: Can be used for multiple enrollments with same activation date
- **Retroactive Activation**: SuperAdmin can activate with past dates
- **Eligibility Changes**: Validates current eligibility before activation
- **Cost Calculation**: Triggers final cost calculation with activation

**11. Enhanced Update Enrollment** ✅ IMPLEMENTED
```http
PUT /api/pre-enrollment/employee-enrollments/:enrollmentId
```

**Purpose**: Update enrollment details with enhanced status transition validation and comprehensive business logic.

**Enhanced Features:**
- ✅ **Status Transition Validation**: Uses new status transition map
- ✅ **Flexible Editing**: Allows editing Waived enrollments (not just Pending/Enrolled)
- ✅ **Clear Error Messages**: Shows valid transitions and suggestions
- ✅ **Cost Recalculation**: Triggers on tier/dependent changes
- ✅ **Audit Trail**: Tracks all changes with user ID and timestamp

**Status Transition Rules in Update API:**
```typescript
// Enhanced validation logic
if (updateData.status && updateData.status !== existingEnrollment.status) {
  const isValidTransition = validateStatusTransition(
    existingEnrollment.status,
    updateData.status
  );

  if (!isValidTransition) {
    return {
      error: `Invalid status transition from ${existingEnrollment.status} to ${updateData.status}`,
      validTransitions: getValidStatusTransitions(existingEnrollment.status),
      suggestion: 'Use dedicated status management APIs for complex transitions (waive, reinstate, activate)'
    };
  }
}
```

**Restricted Operations:**
- ❌ **Cannot Edit Terminated**: Use reinstate API to restore terminated enrollments
- ✅ **Can Edit Waived**: Waived enrollments can be updated (unlike previous restriction)
- ✅ **Status Transitions**: Must follow valid transition rules

**12. Get Expired Enrollments** ✅ IMPLEMENTED
```http
GET /api/pre-enrollment/employee-enrollments/expired
```

**Purpose**: Get expired enrollments with dual mode support for user-specific or plan-assignment-specific filtering.

**Authentication**: Required (JWT)
**Authorization**: Role-based access control

**Query Parameters:**
- `mode` (optional): `'user'` (default) | `'planAssignments'`
- `targetUserId` (optional): User ID to get expired enrollments for (mode=user only)
- `planAssignmentIds` (required for planAssignments mode): Comma-separated plan assignment IDs

**Mode 1: User Mode (Default)**
```http
# Get current user's expired enrollments
GET /api/pre-enrollment/employee-enrollments/expired

# Get specific user's expired enrollments (admin/broker access required)
GET /api/pre-enrollment/employee-enrollments/expired?mode=user&targetUserId=USER_ID
```

**Mode 2: Plan Assignments Mode**
```http
# Get expired enrollments for specific plan assignments
GET /api/pre-enrollment/employee-enrollments/expired?mode=planAssignments&planAssignmentIds=ID1,ID2,ID3
```

**Business Logic & Validation Rules:**

**🔐 Access Control Validation:**

**User Mode Access:**
- **Employees**: Can view their own expired enrollments only
- **Company Admins**: Can view expired enrollments for employees in their company
- **Brokers**: Can view expired enrollments for employees in their managed companies
- **SuperAdmins**: Can view any user's expired enrollments

**Plan Assignments Mode Access:**
- **Company Admins**: Can filter by plan assignments in their company
- **Brokers**: Can filter by plan assignments in their managed companies (with company filtering)
- **SuperAdmins**: Can filter by any plan assignments
- **Employees**: No access to plan assignments mode

**🎯 Automatic Expiry Processing:**
1. **Expiry Check**: Automatically checks for newly expired enrollments before returning results
2. **Status Update**: Updates enrollments past their `planEndDate` to 'Expired' status
3. **Real-Time Results**: Always returns current expiry status

**Response Format:**
```json
{
  "success": true,
  "mode": "user",
  "expiredEnrollments": [
    {
      "_id": "enrollment_id",
      "employeeId": "user_id",
      "planAssignmentId": "plan_assignment_id",
      "status": "Expired",
      "planYear": 2024,
      "planEndDate": "2024-12-31T23:59:59Z",
      "effectiveDate": "2024-01-01T00:00:00Z",
      "coverageType": "Your Health",
      "coverageTier": "Employee Only",
      "companyId": "company_id"
    }
  ],
  "count": 1,
  "message": "Found 1 expired enrollments",
  "expiryCheckPerformed": true,
  "timestamp": "2025-01-15T10:30:00Z",
  "targetUserId": "user_id"
}
```

**Error Responses:**
```json
// Invalid mode
{
  "error": "Invalid mode. Must be \"user\" or \"planAssignments\"",
  "validModes": ["user", "planAssignments"]
}

// Missing plan assignment IDs
{
  "error": "planAssignmentIds query parameter is required for planAssignments mode"
}

// Access denied
{
  "error": "Access denied. You can only view your own expired enrollments unless you are an admin, broker, or super admin."
}
```

**Edge Cases:**
- **No Expired Enrollments**: Returns empty array with count 0
- **Invalid User ID**: Returns 404 error for non-existent users
- **Cross-Company Access**: Brokers and admins filtered by their company scope
- **Automatic Expiry**: Enrollments are automatically marked expired during the API call
- **Plan Assignment Filtering**: Results filtered by user's company access in plan assignments mode

**13. Manual Expiry Check** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/check-expired
```

**Purpose**: Manually trigger expiry check and update for all enrollments in the system (SuperAdmin only).

**Authentication**: Required (JWT)
**Authorization**: SuperAdmin only

**Request Body**: None required

**Response Format:**
```json
{
  "success": true,
  "message": "Expiry check completed successfully",
  "expiredCount": 15,
  "updatedEnrollments": ["enrollment_id_1", "enrollment_id_2", "..."],
  "performedBy": "superadmin_user_id",
  "timestamp": "2025-01-15T10:30:00Z"
}
```

**Business Logic:**
1. **System-Wide Check**: Scans all enrollments in the database
2. **Date Comparison**: Compares `planEndDate` with current date
3. **Status Update**: Updates expired enrollments to 'Expired' status
4. **Audit Trail**: Logs the operation with user ID and timestamp

**Edge Cases:**
- **No Expired Enrollments**: Returns count 0 with empty array
- **Large Dataset**: Processes all enrollments efficiently
- **Concurrent Access**: Safe for concurrent API calls
- **Already Expired**: Skips enrollments already marked as expired

**14. Future Enhancement Placeholders** 📋 PENDING

**Company Dashboard APIs:**
```http
GET /api/pre-enrollment/employee-enrollments/company/:companyId     # Company-wide enrollment view
```

**Broker Management APIs:**
```http
GET /api/pre-enrollment/employee-enrollments/plan-assignment/:planAssignmentId  # Plan-specific enrollments
```

**Note**: The following APIs are not yet implemented but are planned for future releases to support bulk operations and administrative management.

**13. Bulk Terminate Enrollments** ❌ NOT IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/bulk-terminate
```

**Purpose**: Terminate multiple enrollments in a single operation with rollback support

**Authentication**: Required (JWT)
**Authorization**: Admins, Brokers only

**Request Body**:
```typescript
{
  enrollmentIds: string[];                    // Required: Array of enrollment IDs to terminate
  terminationDate: string;                    // Required: ISO date when termination becomes effective
  terminationReason: string;                  // Required: Reason for bulk termination
  companyId?: string;                         // Optional: Filter by company (validation)
  planAssignmentId?: string;                  // Optional: Filter by plan assignment (validation)
  dryRun?: boolean;                          // Optional: Preview operation without executing
}
```

**Business Logic**:
1. **Validation**: All enrollments must exist and be accessible by user
2. **Status Check**: Only `Enrolled` and `Pending` enrollments can be terminated
3. **Date Validation**: Termination date cannot be before enrollment effective dates
4. **Access Control**: User must have permission to terminate each enrollment
5. **Atomic Operation**: All terminations succeed or all fail (rollback support)
6. **Audit Trail**: Creates audit entries for all terminated enrollments

**Success Response** (200):
```typescript
{
  success: true;
  message: string;                           // Summary message
  results: {
    terminated: Array<{
      enrollmentId: string;
      employeeId: string;
      planAssignmentId: string;
      previousStatus: string;
      terminationDate: string;
      terminationReason: string;
    }>;
    failed: Array<{
      enrollmentId: string;
      error: string;
      reason: string;
    }>;
  };
  summary: {
    totalRequested: number;
    successfulTerminations: number;
    failedTerminations: number;
    rollbackPerformed: boolean;
  };
}
```

**Error Response** (400/403/500):
```typescript
{
  error: string;
  details?: string;
  failedEnrollments?: Array<{
    enrollmentId: string;
    error: string;
  }>;
}
```

**14. Bulk Update Enrollments** ❌ NOT IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/bulk-update
```

**Purpose**: Update multiple enrollments with the same changes in a single operation

**Authentication**: Required (JWT)
**Authorization**: Admins, Brokers only

**Request Body**:
```typescript
{
  enrollmentIds: string[];                    // Required: Array of enrollment IDs to update
  updates: {                                  // Required: Updates to apply to all enrollments
    coverageTier?: string;                    // Optional: New coverage tier
    selectedDependents?: string[];            // Optional: New dependent selection
    employeeContribution?: {                  // Optional: Override employee contribution
      contributionType: 'Fixed' | 'Percentage';
      contributionAmount: number;
    };
    notes?: string;                          // Optional: Update notes
  };
  companyId?: string;                         // Optional: Filter by company (validation)
  planAssignmentId?: string;                  // Optional: Filter by plan assignment (validation)
  recalculateCosts?: boolean;                // Optional: Force cost recalculation (default: true)
  dryRun?: boolean;                          // Optional: Preview operation without executing
}
```

**Business Logic**:
1. **Validation**: All enrollments must exist and be in `Pending` or `Enrolled` status
2. **Consistency Check**: All enrollments must be compatible with the updates
3. **Cost Recalculation**: Automatically recalculates costs for tier changes
4. **Dependent Validation**: Validates dependent count against new coverage tier
5. **Atomic Operation**: All updates succeed or all fail (rollback support)
6. **Audit Trail**: Creates audit entries for all updated enrollments

**Success Response** (200):
```typescript
{
  success: true;
  message: string;                           // Summary message
  results: {
    updated: Array<{
      enrollmentId: string;
      employeeId: string;
      planAssignmentId: string;
      previousValues: any;
      newValues: any;
      costRecalculated: boolean;
    }>;
    failed: Array<{
      enrollmentId: string;
      error: string;
      reason: string;
    }>;
  };
  summary: {
    totalRequested: number;
    successfulUpdates: number;
    failedUpdates: number;
    rollbackPerformed: boolean;
    totalCostRecalculations: number;
  };
}
```

**15. Bulk Operation Status** ❌ NOT IMPLEMENTED
```http
GET /api/pre-enrollment/employee-enrollments/bulk-status
```

**Purpose**: Get status of bulk operations and background processing

**Authentication**: Required (JWT)
**Authorization**: Admins, Brokers only

**Query Parameters**:
- `operationId` (string): Specific operation ID to check
- `operationType` (string): Filter by operation type (`terminate`, `update`, `waive`, `enroll`)
- `status` (string): Filter by status (`pending`, `processing`, `completed`, `failed`)
- `userId` (string): Filter by user who initiated the operation
- `limit` (number): Number of results (default: 20, max: 100)

**Success Response** (200):
```typescript
{
  success: true;
  operations: Array<{
    operationId: string;
    operationType: 'terminate' | 'update' | 'waive' | 'enroll';
    status: 'pending' | 'processing' | 'completed' | 'failed';
    initiatedBy: string;                     // User ID
    initiatedAt: string;                     // ISO timestamp
    completedAt?: string;                    // ISO timestamp (if completed)
    totalItems: number;
    processedItems: number;
    successfulItems: number;
    failedItems: number;
    progress: number;                        // Percentage (0-100)
    estimatedCompletion?: string;            // ISO timestamp
    errorSummary?: {
      commonErrors: Array<{
        error: string;
        count: number;
      }>;
      totalErrors: number;
    };
  }>;
  summary: {
    totalOperations: number;
    activeOperations: number;
    completedOperations: number;
    failedOperations: number;
  };
}
```
**Authorization**: Brokers, Employers only

**Request Body**:
```typescript
{
  planAssignmentId: string;              // Required: Plan assignment for all enrollments
  enrollments: Array<{
    employeeId: string;                  // Required: Employee ID
    coverageTier: string;                // Required: Coverage tier
    dependents?: Array<{                 // Optional: Dependents
      name: string;
      relationship: string;
      dateOfBirth: string;
    }>;
    payrollFrequency?: string;           // Optional: Employee-specific payroll frequency
    enrollmentReason?: string;           // Optional: Enrollment reason
  }>;
  validateOnly?: boolean;                // Optional: Only validate, don't create
}
```

**Response**:
```typescript
{
  success: boolean;
  message: string;
  data: {
    successful: Array<{
      employeeId: string;
      enrollmentId: string;
      status: string;
    }>;
    failed: Array<{
      employeeId: string;
      error: string;
      reason: string;
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }
}
```

### 4.6 Cost Calculation APIs

The Cost Calculation APIs provide comprehensive cost calculation services for enrollment scenarios with support for multiple rate structures and contribution policies.

#### **📋 PENDING APIs (No Dedicated Controller Yet):**

**Core Cost Calculation:**
```http
POST   /api/pre-enrollment/cost-calculation/calculate                    # PENDING - Calculate enrollment cost
POST   /api/pre-enrollment/cost-calculation/batch-calculate              # PENDING - Batch cost calculation
GET    /api/pre-enrollment/cost-calculation/rate-structures              # PENDING - Get supported rate structures
POST   /api/pre-enrollment/cost-calculation/validate-rates               # PENDING - Validate rate configuration
```

**Rate Structure Management:**
```http
GET    /api/pre-enrollment/cost-calculation/rate-structures/:structure   # PENDING - Get rate structure details
POST   /api/pre-enrollment/cost-calculation/rate-structures/test         # PENDING - Test rate structure
GET    /api/pre-enrollment/cost-calculation/contribution-types           # PENDING - Get contribution types
```

**Employee and Company Totals:**
```http
GET    /api/pre-enrollment/cost-calculation/employee-total/:employeeId   # PENDING - Calculate employee total costs
GET    /api/pre-enrollment/cost-calculation/company-total/:companyId     # PENDING - Calculate company total costs
POST   /api/pre-enrollment/cost-calculation/preview-tiers                # PENDING - Preview all tier costs
```

**⚠️ NOTE:** Cost calculation functionality is currently available through:
1. **CostCalculationService** - Core service with all calculation logic ✅ IMPLEMENTED
2. **Employee Enrollment APIs** - Cost calculation during enrollment ✅ IMPLEMENTED
3. **Dedicated Cost Calculation Controller** - 📋 PENDING (needs to be created)

#### **📋 MISSING IMPLEMENTATION:**

To complete the cost calculation API suite, a dedicated `CostCalculationController` needs to be created with the following endpoints:

**Required Controller Implementation:**
```typescript
// src/controllers/costCalculation.controller.ts
class CostCalculationController implements Controller {
  public router = express.Router();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    // Core calculation endpoints
    this.router.post('/api/pre-enrollment/cost-calculation/calculate', this.calculateCost);
    this.router.post('/api/pre-enrollment/cost-calculation/batch-calculate', this.batchCalculate);
    this.router.post('/api/pre-enrollment/cost-calculation/preview-tiers', this.previewTiers);

    // Rate structure management
    this.router.get('/api/pre-enrollment/cost-calculation/rate-structures', this.getRateStructures);
    this.router.get('/api/pre-enrollment/cost-calculation/rate-structures/:structure', this.getRateStructureDetails);
    this.router.post('/api/pre-enrollment/cost-calculation/rate-structures/test', this.testRateStructure);
    this.router.get('/api/pre-enrollment/cost-calculation/contribution-types', this.getContributionTypes);

    // Employee and company totals
    this.router.get('/api/pre-enrollment/cost-calculation/employee-total/:employeeId', this.getEmployeeTotal);
    this.router.get('/api/pre-enrollment/cost-calculation/company-total/:companyId', this.getCompanyTotal);

    // Validation
    this.router.post('/api/pre-enrollment/cost-calculation/validate-rates', this.validateRates);
  }

  // Implementation methods would use CostCalculationService
}
```

**Integration Required:**
```typescript
// src/index.ts - Add to controller list
import CostCalculationController from './controllers/costCalculation.controller';

const app = new App([
  // ... existing controllers
  new CostCalculationController(), // Add this line
]);
```

#### **API Details:**

**1. Calculate Enrollment Cost**
```http
POST /api/pre-enrollment/cost-calculation/calculate
```

**Purpose**: Calculate enrollment cost for a specific employee and plan assignment

**Authentication**: Required (JWT)
**Authorization**: All authenticated users (with access control)

**Request Body**:
```typescript
{
  planAssignmentId: string;              // Required: Plan assignment ID
  employeeId: string;                    // Required: Employee ID
  selectedTier: string;                  // Required: Coverage tier
  dependents?: Array<{                   // Optional: Dependent information
    name: string;
    relationship: string;
    dateOfBirth: string;
    gender?: string;
  }>;
  payrollFrequency?: string;             // Optional: Payroll frequency override
  effectiveDate?: string;                // Optional: Effective date for calculation
}
```

**Response**:
```typescript
{
  success: boolean;
  data: {
    isEligible: boolean;
    cost: {
      employeeAmount: number;            // Employee monthly cost
      employerAmount: number;            // Employer monthly cost
      totalAmount: number;               // Total monthly premium
      payrollDeduction?: number;         // Per-paycheck deduction
      annualCost?: number;               // Annual cost breakdown
    };
    calculation: {
      rateStructure: string;             // Rate structure used
      basePremium: number;               // Base premium before adjustments
      tierRate?: number;                 // Tier base rate applied
      ageRate?: number;                  // Age band rate (if age-banded)
      salaryRate?: number;               // Salary band rate (if salary-based)
      employeeAge?: number;              // Employee age used
      employeeSalary?: number;           // Employee salary used
    };
    breakdown: {
      tierRate: number;                  // Base tier rate
      adjustments: Array<{
        type: string;                    // "Age Band", "Salary Band", "Tier"
        description: string;
        rate: number;                    // Rate amount (not multiplier)
        amount: number;
      }>;
      contributions: {
        employer: {
          type: string;                  // "Fixed", "Percentage", "Remainder"
          amount: number;
          percentage?: number;
        };
        employee: {
          type: string;
          amount: number;
          percentage?: number;
        };
      };
    };
    eligibilityMessage?: string;         // If not eligible
  }
}
```

## 💰 **COMPREHENSIVE COST CALCULATION SYSTEM**

### **🎯 Core Principle: Single Premium Per Plan Assignment**

The QHarmony system uses a **single premium model** where each plan assignment has one total cost covering ALL coverage subtypes. This approach mirrors real-world insurance practices and eliminates cost duplication issues.

### **💼 Payroll Frequency Integration & Cost Breakdown**

The system provides comprehensive payroll frequency support for accurate employee cost calculations:

#### **📊 Supported Payroll Frequencies:**
- **Weekly**: 52 pay periods per year
- **Biweekly**: 26 pay periods per year
- **Semi-Monthly**: 24 pay periods per year
- **Monthly**: 12 pay periods per year

#### **🔄 Payroll Frequency Fallback Chain:**
1. **Primary Source**: Employee custom payroll frequency (`User.details.customPayrollFrequency`)
2. **Secondary Source**: Company default payroll frequency (`CompanyBenefitsSettings.globalEligibility.payrollFrequency`)
3. **Final Fallback**: "Monthly" (12 pay periods per year) - Always works

#### **💰 Enhanced Cost Calculation Result:**
```typescript
interface EnhancedCostResult {
  success: boolean;
  cost: {
    // Monthly amounts
    monthlyEmployeeAmount: number;
    monthlyEmployerAmount: number;
    monthlyTotalAmount: number;

    // Annual amounts
    annualEmployeeAmount: number;
    annualEmployerAmount: number;
    annualTotalAmount: number;

    // Per-paycheck amounts (NEW)
    payrollEmployeeAmount: number;    // Per-paycheck deduction
    payrollEmployerAmount: number;    // Per-paycheck employer cost
    payrollTotalAmount: number;       // Per-paycheck total cost

    // Metadata
    payrollFrequency: string;         // "Weekly", "Biweekly", "Semi-Monthly", "Monthly"
    payPeriodsPerYear: number;        // 52, 26, 24, or 12
  };
  warnings?: string[];               // Fallback warnings
  payrollFrequencySource?: string;   // Source of payroll frequency
  error?: string;
}
```

#### **🧮 Payroll Deduction Calculation:**
```typescript
// Formula for per-paycheck deduction:
payrollEmployeeAmount = monthlyEmployeeAmount / (payPeriodsPerYear / 12)

// Example for Biweekly (26 pay periods):
// Monthly cost: $300
// Per-paycheck: $300 / (26 / 12) = $300 / 2.167 = $138.46
```

#### **Key Concepts:**

1. **Plan Assignment = Single Premium**: Each plan assignment defines one total cost for all coverage subtypes combined
2. **Rate Structure + Coverage Tier = One Calculation**: Cost calculation considers the entire plan as a package
3. **Employee Pays Once**: Employee pays one amount for all coverage subtypes in the plan
4. **No Subtype Splitting**: Costs are not broken down by individual coverage subtypes

### **🏗️ Cost Calculation Architecture**

#### **Data Flow:**
```
Plan (Blueprint)
├── coverageType: "Health Insurance"
├── coverageSubTypes: ["Medical", "Dental", "Vision"]
└── benefitDetails: { deductibles, copays, etc. }

↓ (Plan Assignment)

PlanAssignment (Company-Specific Pricing)
├── planId: "plan123"
├── companyId: "company456"
├── rateStructure: "Age-Banded"
├── coverageTiers: [
│   { tierName: "Employee Only", rate: 300 },    // Total for ALL subtypes
│   { tierName: "Family", rate: 800 }            // Total for ALL subtypes
│ ]
├── employerContribution: { type: "Percentage", amount: 80 }
└── employeeContribution: { type: "Remainder", amount: 0 }

↓ (Cost Calculation)

CostCalculationService.calculateEnrollmentCost()
├── Input: planAssignment, selectedTier, employeeAge, employeeSalary
├── Process: Apply rate structure + contribution policies
└── Output: { totalAmount: 800, employeeAmount: 160, employerAmount: 640 }

↓ (Employee Enrollment)

EmployeeEnrollment (Single Enrollment)
├── planAssignmentId: "assignment123"
├── coverageSubTypes: ["Medical", "Dental", "Vision"]  // ALL subtypes
├── coverageTier: "Family"
├── contribution: {
│   totalAmount: 800,     // Total for ALL subtypes
│   employeeAmount: 160,  // Employee portion for ALL subtypes
│   employerAmount: 640   // Employer portion for ALL subtypes
│ }
└── status: "Enrolled"
```

### **📊 Rate Structure Implementation**

#### **1. Composite Rate Structure**
```typescript
// Simple flat rate regardless of age/dependents/salary
PlanAssignment: {
  rateStructure: "composite",
  coverageTiers: [
    { tierName: "Employee Only", rate: 300 },
    { tierName: "Employee + Spouse", rate: 600 },
    { tierName: "Family", rate: 800 }
  ]
}

// Cost Calculation: Direct lookup
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = tier.rate; // $800 for Family
```

#### **2. Age-Banded Rate Structure (🎯 UPDATED: Supports Fixed & Multiplier Types)**
```typescript
// Option 1: Fixed Amount Adjustments (QHarmony Legacy)
PlanAssignment: {
  rateStructure: "age-banded",
  ageBandedRates: [
    { ageMin: 18, ageMax: 29, rate: 100, type: 'fixed' },   // +$100
    { ageMin: 30, ageMax: 39, rate: 200, type: 'fixed' },   // +$200
    { ageMin: 40, ageMax: 49, rate: 400, type: 'fixed' },   // +$400
    { ageMin: 50, ageMax: 59, rate: 600, type: 'fixed' }    // +$600
  ],
  coverageTiers: [
    { tierName: "Employee Only", totalCost: 300 },
    { tierName: "Family", totalCost: 800 }
  ]
}

// Cost Calculation: Age band rate + tier cost (ADDITION)
const ageBand = ageBandedRates.find(band =>
  employeeAge >= band.ageMin && employeeAge <= band.ageMax
);
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = ageBand.rate + tier.totalCost; // $400 + $800 = $1200 for 45-year-old

// Option 2: Multiplier Factor Adjustments (Industry Standard)
PlanAssignment: {
  rateStructure: "age-banded",
  ageBandedRates: [
    { ageMin: 18, ageMax: 29, rate: 0.8, type: 'multiplier' },   // 20% discount
    { ageMin: 30, ageMax: 39, rate: 1.0, type: 'multiplier' },   // Standard rate
    { ageMin: 40, ageMax: 49, rate: 1.5, type: 'multiplier' },   // 50% premium
    { ageMin: 50, ageMax: 59, rate: 1.75, type: 'multiplier' }   // 75% premium
  ],
  coverageTiers: [
    { tierName: "Employee Only", totalCost: 300 },
    { tierName: "Family", totalCost: 800 }
  ]
}

// Cost Calculation: Age multiplier * tier cost (MULTIPLICATION)
const ageBand = ageBandedRates.find(band =>
  employeeAge >= band.ageMin && employeeAge <= band.ageMax
);
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = tier.totalCost * ageBand.rate; // $800 * 1.5 = $1200 for 45-year-old
```

#### **3. Salary-Based Rate Structure (🎯 UPDATED: Supports Fixed & Multiplier Types)**
```typescript
// Option 1: Fixed Amount Adjustments (QHarmony Legacy)
PlanAssignment: {
  rateStructure: "salary-based",
  salaryBasedRates: [
    { salaryMin: 30000, salaryMax: 50000, rate: 150, type: 'fixed' },   // +$150
    { salaryMin: 50001, salaryMax: 75000, rate: 200, type: 'fixed' },   // +$200
    { salaryMin: 75001, salaryMax: 100000, rate: 240, type: 'fixed' }   // +$240
  ],
  coverageTiers: [
    { tierName: "Employee Only", totalCost: 300 },
    { tierName: "Family", totalCost: 800 }
  ]
}

// Cost Calculation: Salary band rate + tier cost (ADDITION)
const salaryBand = salaryBasedRates.find(band =>
  employeeSalary >= band.salaryMin && employeeSalary <= band.salaryMax
);
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = salaryBand.rate + tier.totalCost; // $240 + $800 = $1040 for $80k salary

// Option 2: Multiplier Factor Adjustments (Industry Standard)
PlanAssignment: {
  rateStructure: "salary-based",
  salaryBasedRates: [
    { salaryMin: 30000, salaryMax: 50000, rate: 0.8, type: 'multiplier' },   // 20% discount
    { salaryMin: 50001, salaryMax: 75000, rate: 1.0, type: 'multiplier' },   // Standard rate
    { salaryMin: 75001, salaryMax: 100000, rate: 1.3, type: 'multiplier' }   // 30% premium
  ],
  coverageTiers: [
    { tierName: "Employee Only", totalCost: 300 },
    { tierName: "Family", totalCost: 800 }
  ]
}

// Cost Calculation: Salary multiplier * tier cost (MULTIPLICATION)
const salaryBand = salaryBasedRates.find(band =>
  employeeSalary >= band.salaryMin && employeeSalary <= band.salaryMax
);
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = tier.totalCost * salaryBand.rate; // $800 * 1.3 = $1040 for $80k salary
```

#### **4. Four-Tier Rate Structure**
```typescript
// Standard industry tiers with pre-calculated costs
PlanAssignment: {
  rateStructure: "four-tier",
  coverageTiers: [
    { tierName: "Employee Only", rate: 300 },
    { tierName: "Employee + Spouse", rate: 600 },
    { tierName: "Employee + Children", rate: 550 },
    { tierName: "Family", rate: 800 }
  ]
}

// Cost Calculation: Direct lookup (same as composite)
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = tier.rate; // Direct from pre-calculated tiers
```

### **💼 Contribution Policy Implementation**

#### **Contribution Types:**

1. **Fixed Amount**: Employer/Employee pays specific dollar amount
2. **Percentage**: Employer/Employee pays percentage of total premium
3. **Remainder**: Pays whatever is left after other party's contribution

```typescript
// Example: Employer pays 80%, Employee pays remainder
employerContribution: { contributionType: "Percentage", contributionAmount: 80 }
employeeContribution: { contributionType: "Remainder", contributionAmount: 0 }

// Calculation:
const totalPremium = 1000;
const employerAmount = totalPremium * 0.80; // $800
const employeeAmount = totalPremium - employerAmount; // $200

// Example: Fixed amounts
employerContribution: { contributionType: "Fixed", contributionAmount: 400 }
employeeContribution: { contributionType: "Fixed", contributionAmount: 100 }

// Calculation:
const employerAmount = 400; // Fixed $400
const employeeAmount = 100; // Fixed $100
const totalPremium = employerAmount + employeeAmount; // $500
```

### **📅 Payroll Frequency Integration**

#### **Payroll Frequency Impact:**
```typescript
// Monthly employee cost converted to payroll deduction
const payrollFrequencies = {
  "Weekly": 52,        // 52 pay periods per year
  "Biweekly": 26,      // 26 pay periods per year
  "Semi-Monthly": 24,  // 24 pay periods per year
  "Monthly": 12        // 12 pay periods per year
}

// Calculation: Monthly cost ÷ (pay periods per year ÷ 12)
const payrollDeduction = monthlyEmployeeAmount / (payPeriodsPerYear / 12);

// Example: $200 monthly employee cost
// Weekly: $200 ÷ (52÷12) = $200 ÷ 4.33 = $46.15 per week
// Biweekly: $200 ÷ (26÷12) = $200 ÷ 2.17 = $92.31 every two weeks
// Semi-Monthly: $200 ÷ (24÷12) = $200 ÷ 2.00 = $100.00 twice per month
// Monthly: $200 ÷ (12÷12) = $200 ÷ 1.00 = $200.00 once per month
```

### **🔧 Cost Calculation Service**

#### **Service Interface:**
```typescript
interface CostCalculationResult {
  success: boolean;
  cost?: {
    // Monthly amounts
    employeeAmount: number;
    employerAmount: number;
    totalAmount: number;

    // Annual amounts
    annualEmployeeAmount: number;
    annualEmployerAmount: number;
    annualTotalAmount: number;

    // Payroll deduction amounts
    payrollEmployeeAmount: number;
    payrollEmployerAmount: number;
    payrollTotalAmount: number;

    // Metadata
    payrollFrequency: string;
    payPeriodsPerYear: number;
  };
  error?: string;
}

// Usage
const result = CostCalculationService.calculateEnrollmentCost({
  planAssignment,
  employeeAge: 35,
  selectedTier: "Family",
  employeeSalary: 75000,
  payrollFrequency: "Biweekly"
});
```

### **📋 COMPREHENSIVE COST CALCULATION EXAMPLES**

This section provides complete, step-by-step examples of how different rate structures work in practice, showing exact calculations for all supported rate structures.

#### **Example 1: Composite Rate Structure (Simple)**

**Setup:**
```typescript
Plan: {
  planName: "Gold PPO Health Plan",
  coverageType: "Health Insurance",
  coverageSubTypes: ["Medical"]
  // NO pricing fields - all pricing in PlanAssignment
}

PlanAssignment: {
  planId: "plan123",
  companyId: "company456",
  rateStructure: "composite",
  ageBandedRates: [],  // Empty for composite
  salaryBasedRates: [], // Empty for composite
  employerContribution: { contributionType: "Percentage", contributionAmount: 80 },
  employeeContribution: { contributionType: "Percentage", contributionAmount: 20 },
  coverageTiers: [
    { tierName: "Employee Only", rate: 600 },
    { tierName: "Employee + Spouse", rate: 1200 },
    { tierName: "Family", rate: 1500 }
  ]
}
```

**Employee Enrollment:**
```typescript
// 25-year-old employee selects Family coverage
EmployeeEnrollment: {
  planAssignmentId: "assignment123",
  employeeId: "employee456",
  coverageTier: "Family",
  contribution: {
    totalAmount: 1500,    // Direct from coverageTiers
    employerAmount: 1200, // 1500 * 80%
    employeeAmount: 300   // 1500 * 20%
  }
}

// 55-year-old employee selects Family coverage
EmployeeEnrollment: {
  planAssignmentId: "assignment123",
  employeeId: "employee789",
  coverageTier: "Family",
  contribution: {
    totalAmount: 1500,    // SAME COST regardless of age
    employerAmount: 1200, // SAME COST regardless of age
    employeeAmount: 300   // SAME COST regardless of age
  }
}
```

#### **Example 2: Age-Banded Rate Structure (Complex)**

**Setup:**
```typescript
Plan: {
  planName: "Gold PPO Health Plan",
  coverageType: "Health Insurance",
  coverageSubTypes: ["Medical"]
  // NO pricing fields - all pricing in PlanAssignment
}

PlanAssignment: {
  planId: "plan123",
  companyId: "company789",
  rateStructure: "age-banded",
  ageBandedRates: [
    { ageMin: 18, ageMax: 29, rate: 400 },  // Young employee base rate
    { ageMin: 30, ageMax: 39, rate: 500 },
    { ageMin: 40, ageMax: 49, rate: 650 },
    { ageMin: 50, ageMax: 59, rate: 800 }   // Older employee base rate
  ],
  salaryBasedRates: [], // Empty for age-banded
  employerContribution: { contributionType: "Percentage", contributionAmount: 75 },
  employeeContribution: { contributionType: "Percentage", contributionAmount: 25 },
  coverageTiers: [
    { tierName: "Employee Only", rate: 0 },           // = age rate + 0
    { tierName: "Employee + Spouse", rate: 600 },     // = age rate + 600
    { tierName: "Family", rate: 1100 }                // = age rate + 1100
  ]
}
```

**Employee Enrollment Calculations:**

**25-Year-Old Employee:**
```typescript
// Step 1: Find age band
const employeeAge = 25;
const ageBand = ageBandedRates.find(band =>
  employeeAge >= band.ageMin && employeeAge <= band.ageMax
);
// Result: { ageMin: 18, ageMax: 29, rate: 400 }

// Step 2: Calculate total cost for Family tier
const selectedTier = "Family";
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = ageBand.rate + tier.rate;
// Result: 400 + 1100 = 1500

// Step 3: Apply contribution policy
const employerAmount = totalPremium * 0.75;  // 75%
const employeeAmount = totalPremium * 0.25;  // 25%
// Result: employerAmount = 1125, employeeAmount = 375

EmployeeEnrollment: {
  planAssignmentId: "assignment123",
  employeeId: "employee456",
  employeeAge: 25,
  coverageTier: "Family",
  contribution: {
    totalAmount: 1500,
    employerAmount: 1125,
    employeeAmount: 375
  }
}
```

**55-Year-Old Employee:**
```typescript
// Step 1: Find age band
const employeeAge = 55;
const ageBand = ageBandedRates.find(band =>
  employeeAge >= band.ageMin && employeeAge <= band.ageMax
);
// Result: { ageMin: 50, ageMax: 59, rate: 800 }

// Step 2: Calculate total cost for Family tier
const selectedTier = "Family";
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = ageBand.rate + tier.rate;
// Result: 800 + 1100 = 1900

// Step 3: Apply contribution policy
const employerAmount = totalPremium * 0.75;  // 75%
const employeeAmount = totalPremium * 0.25;  // 25%
// Result: employerAmount = 1425, employeeAmount = 475

EmployeeEnrollment: {
  planAssignmentId: "assignment123",
  employeeId: "employee789",
  employeeAge: 55,
  coverageTier: "Family",
  contribution: {
    totalAmount: 1900,    // HIGHER cost due to age
    employerAmount: 1425, // HIGHER cost due to age
    employeeAmount: 475   // HIGHER cost due to age
  }
}
```

#### **Example 3: Salary-Based Rate Structure (Advanced)**

**Setup:**
```typescript
Plan: {
  planName: "Executive Health Plan",
  coverageType: "Ancillary Benefits",
  coverageSubTypes: ["Dental", "Vision"]
  // NO pricing fields - all pricing in PlanAssignment
}

PlanAssignment: {
  planId: "plan123",
  companyId: "company789",
  rateStructure: "salary-based",
  ageBandedRates: [], // Empty for salary-based
  salaryBasedRates: [
    { salaryMin: 30000, salaryMax: 50000, rate: 200 },   // Lower income base rate
    { salaryMin: 50001, salaryMax: 80000, rate: 300 },   // Middle income base rate
    { salaryMin: 80001, salaryMax: 120000, rate: 400 },  // Higher income base rate
    { salaryMin: 120001, salaryMax: 999999, rate: 500 }  // Executive base rate
  ],
  employerContribution: { contributionType: "Percentage", contributionAmount: 80 },
  employeeContribution: { contributionType: "Percentage", contributionAmount: 20 },
  coverageTiers: [
    { tierName: "Employee Only", rate: 0 },           // = salary rate + 0
    { tierName: "Employee + Spouse", rate: 400 },     // = salary rate + 400
    { tierName: "Family", rate: 700 }                 // = salary rate + 700
  ]
}
```

**Employee Enrollment Calculations:**

**$45,000 Salary Employee:**
```typescript
// Step 1: Find salary band
const employeeSalary = 45000;
const salaryBand = salaryBasedRates.find(band =>
  employeeSalary >= band.salaryMin && employeeSalary <= band.salaryMax
);
// Result: { salaryMin: 30000, salaryMax: 50000, rate: 200 }

// Step 2: Calculate total cost for Family tier
const selectedTier = "Family";
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = salaryBand.rate + tier.rate;
// Result: 200 + 700 = 900

// Step 3: Apply contribution policy
const employerAmount = totalPremium * 0.80;  // 80%
const employeeAmount = totalPremium * 0.20;  // 20%
// Result: employerAmount = 720, employeeAmount = 180

EmployeeEnrollment: {
  planAssignmentId: "assignment123",
  employeeId: "employee456",
  employeeSalary: 45000,
  coverageTier: "Family",
  contribution: {
    totalAmount: 900,
    employerAmount: 720,
    employeeAmount: 180
  }
}
```

**$150,000 Salary Executive:**
```typescript
// Step 1: Find salary band
const employeeSalary = 150000;
const salaryBand = salaryBasedRates.find(band =>
  employeeSalary >= band.salaryMin && employeeSalary <= band.salaryMax
);
// Result: { salaryMin: 120001, salaryMax: 999999, rate: 500 }

// Step 2: Calculate total cost for Family tier
const selectedTier = "Family";
const tier = coverageTiers.find(t => t.tierName === selectedTier);
const totalPremium = salaryBand.rate + tier.rate;
// Result: 500 + 700 = 1200

// Step 3: Apply contribution policy
const employerAmount = totalPremium * 0.80;  // 80%
const employeeAmount = totalPremium * 0.20;  // 20%
// Result: employerAmount = 960, employeeAmount = 240

EmployeeEnrollment: {
  planAssignmentId: "assignment123",
  employeeId: "employee789",
  employeeSalary: 150000,
  coverageTier: "Family",
  contribution: {
    totalAmount: 1200,    // HIGHER cost due to higher salary
    employerAmount: 960,  // HIGHER cost due to higher salary
    employeeAmount: 240   // HIGHER cost due to higher salary
  }
}
```

#### **Alternative: Salary Percentage Approach**
```typescript
PlanAssignment: {
  rateStructure: "salary-based",
  salaryPercentage: 2.5,  // 2.5% of annual salary
  coverageTiers: [
    { tierName: "Employee Only", rate: 0 },
    { tierName: "Family", rate: 100 }
  ]
}

// $60,000 salary employee calculation:
const annualSalary = 60000;
const monthlyPercentageAmount = (annualSalary * 2.5) / 100 / 12;  // $125/month
const totalPremium = monthlyPercentageAmount + 100;  // $125 + $100 = $225
```

#### **🔍 Key Insights from Rate Structure Examples**

**1. "Family Multiplier" Question Answered**

**There is NO single "family multiplier" in the system.** Instead:

- **Composite Pricing**: Family costs are pre-calculated and stored in `coverageTiers`
- **Age-Banded Pricing**: Family costs = Employee age rate + Family tier addition
- **Four-Tier Pricing**: Each tier has its own pre-calculated cost
- **Age-Banded-Four-Tier**: Combines both approaches
- **Salary-Based Pricing**: Family costs = Employee salary rate + Family tier addition

**2. Cost Calculation Logic**

```typescript
// Universal cost calculation method (with salary-based support)
function calculateEnrollmentCost(planAssignment, employeeAge, selectedTier, employeeSalary) {
  const {
    rateStructure,
    ageBandedRates,
    salaryBasedRates,
    salaryPercentage,
    coverageTiers,
    employerContribution,
    employeeContribution
  } = planAssignment;

  let totalPremium;

  if (rateStructure === "composite" || rateStructure === "four-tier") {
    // Direct lookup from pre-calculated tiers
    const tier = coverageTiers.find(t => t.tierName === selectedTier);
    totalPremium = tier.totalCost;
  }
  else if (rateStructure === "age-banded" || rateStructure === "age-banded-four-tier") {
    // Age-based calculation (ADDITION, not multiplication)
    const ageBand = ageBandedRates.find(band =>
      employeeAge >= band.ageMin && employeeAge <= band.ageMax
    );
    const tier = coverageTiers.find(t => t.tierName === selectedTier);
    totalPremium = ageBand.rate + tier.totalCost;  // ADDITION
  }
  else if (rateStructure === "salary-based") {
    // Salary-based calculation (ADDITION, not multiplication)
    const tier = coverageTiers.find(t => t.tierName === selectedTier);

    if (salaryBasedRates && salaryBasedRates.length > 0) {
      // Use salary bands
      const salaryBand = salaryBasedRates.find(band =>
        employeeSalary >= band.salaryMin && employeeSalary <= band.salaryMax
      );
      totalPremium = salaryBand.rate + tier.totalCost;  // ADDITION
    } else if (salaryPercentage) {
      // Use salary percentage
      const salaryBasedAmount = (employeeSalary * salaryPercentage) / 100 / 12; // Monthly amount
      totalPremium = salaryBasedAmount + tier.totalCost;  // ADDITION
    }
  }

  // Apply contribution policy
  let employerAmount, employeeAmount;

  if (employerContribution.contributionType === "Percentage") {
    employerAmount = totalPremium * (employerContribution.contributionAmount / 100);
    employeeAmount = totalPremium - employerAmount;
  } else if (employerContribution.contributionType === "Fixed") {
    employerAmount = employerContribution.contributionAmount;
    employeeAmount = totalPremium - employerAmount;
  }

  return { totalAmount: totalPremium, employerAmount, employeeAmount };
}
```

### 3.6 Authentication & Access Control

The system implements role-based access control with strict data isolation and validation.

**User Hierarchy:**
```
SuperAdmin (isSuperAdmin=true) > Broker (isAdmin=true, isBroker=true) > Employer (isAdmin=true, isBroker=false) > Employee (all false)
```

**Access Control Rules:**

**Plan Assignment APIs:**
- **SuperAdmins**: Full CRUD access to all plan assignments
- **Brokers**: Full CRUD access to their own plan assignments only
- **Employers**: Read-only access to plan assignments for their company
- **Employees**: No direct access to plan assignment APIs

**Employee Enrollment APIs:**
- **SuperAdmins**: Full access to all employee enrollments
- **Brokers**: Read-only access to enrollments for their assigned companies
- **Employers**: Full access to enrollments for their company employees
- **Employees**: Access only to their own enrollment data

**Data Isolation:**
- Brokers can only access data for companies they manage (via brokerId/brokerageId)
- Employers can only access data for their own company
- Employees can only access their own enrollment data
- SuperAdmins have system-wide access

**🏢 Multi-Broker Company Scenario:**

Large companies often work with multiple brokers for different types of coverage:

```typescript
// Example: "TechCorp" works with multiple brokers
Company: "TechCorp" (ID: company123)

// Broker A (Health Insurance Specialist):
Assignment1: { planId: "healthPlan1", companyId: "company123", plan: { brokerId: "brokerA" } }
Assignment2: { planId: "healthPlan2", companyId: "company123", plan: { brokerId: "brokerA" } }

// Broker B (Dental Insurance Specialist):
Assignment3: { planId: "dentalPlan1", companyId: "company123", plan: { brokerId: "brokerB" } }
Assignment4: { planId: "dentalPlan2", companyId: "company123", plan: { brokerId: "brokerB" } }

// Access Control Results:
// BrokerA calls getBrokerAssignmentsForCompany("brokerA", "company123"):
// Returns: [Assignment1, Assignment2] - Only health plans

// BrokerB calls getBrokerAssignmentsForCompany("brokerB", "company123"):
// Returns: [Assignment3, Assignment4] - Only dental plans

// Employee calls getAssignmentsByCompany("company123"):
// Returns: [Assignment1, Assignment2, Assignment3, Assignment4] - All plans

// Security Benefit: Brokers cannot see competitors' assignments for same company
```

**🔒 Why Company-Specific Broker Filtering is Critical:**

1. **Competitive Protection**: Prevents brokers from seeing competitors' plan assignments
2. **Data Privacy**: Ensures brokers only access their own business relationships
3. **Compliance**: Maintains proper data isolation for regulatory requirements
4. **Business Logic**: Supports multi-broker company relationships accurately

**Authentication Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
user-id: <user_id>                    # Required for all authenticated requests
```

---

## 5. Technical Implementation

### 5.1 Coverage Mapping System

The system implements a comprehensive 13-category insurance coverage mapping designed specifically for pre-enrollment scenarios:

**Coverage Categories:**
1. **Health Insurance** - Medical
2. **Ancillary Benefits** - Dental, Vision
3. **Life & Disability Insurance** - Term Life, Supplemental Life, STD, LTD, Whole Life, Group Life
4. **Voluntary Benefits** - Hospital Indemnity, Accident, Critical Illness, Cancer, Gap Insurance, Legal, Identity Theft, Pet Insurance, Nursing Care
5. **Wellness & Mental Health** - Wellness Programs, EAP, Gym Membership
6. **Spending & Savings Accounts** - HSA, FSA, Commuter Benefits, Technology Stipend
7. **Financial Benefits** - Pay & Bonus, Stock Options, Student Loan Assistance
8. **Retirement Benefits** - 401(k), 403(b), Pension Plan
9. **Time Off & Leave** - PTO, Parental Leave, FMLA, Paid Volunteer Time
10. **Family & Caregiver Support** - On-site Child Care
11. **Career & Development** - Training, Tuition Reimbursement, Recognition, Performance Goals
12. **Workplace Environment** - Pet-friendly, Ergonomic, Company Handbook
13. **Life Events** - Marriage/Divorce, New Baby/Adoption, Loss of Insurance

### 5.2 Database Schema

**Key Schema Features:**
- **MongoDB with Mongoose**: Full TypeScript interfaces and validation
- **Compound Indexes**: Broker-scoped uniqueness validation
- **Performance Indexes**: Optimized querying on status, dates, and IDs
- **Referential Integrity**: Proper foreign key relationships with validation
- **Audit Fields**: CreatedAt, updatedAt timestamps on all models

### 5.3 Constants and Enums

**Centralized Constants:**
```typescript
// Employee class types
export const EMPLOYEE_CLASS_TYPES = ['Full-Time', 'Part-Time', 'Contractor', 'Temporary', 'Seasonal'] as const;

// Rate structures
export const RATE_STRUCTURES = ['composite', 'four-tier', 'age-banded', 'salary-based'] as const;

// Contribution types
export const CONTRIBUTION_TYPES = ['Fixed', 'Percentage', 'Remainder'] as const;

// Enrollment statuses
export const ENROLLMENT_STATUSES = ['Enrolled', 'Waived', 'Pending', 'Terminated', 'Expired'] as const;

// 🎯 NEW: Enrollment period types
export const ENROLLMENT_PERIOD_TYPES = ['Open Enrollment', 'New Hire', 'Qualifying Life Event'] as const;

// 🎯 NEW: Qualifying Life Event types
export const QUALIFYING_LIFE_EVENT_TYPES = [
  'Marriage',
  'Divorce',
  'Birth',
  'Adoption',
  'Loss of Coverage',
  'Job Change',
  'Death',
  'Relocation',
  'Other'
] as const;
```

---

## 6. Status & Changelog

### 6.1 Implementation Status

#### **📊 API Implementation Summary**

| Module | Implemented | Pending | Total | Status |
|--------|-------------|---------|-------|---------|
| **Carrier Management** | 13 | 0 | 13 | ✅ **100% Complete** |
| **Plan Management** | 14 | 0 | 14 | ✅ **100% Complete** |
| **Plan Assignment Management** | 17 | 0 | 17 | ✅ **100% Complete** |
| **Company Benefits Settings** | 8 | 0 | 8 | ✅ **100% Complete** |
| **Employee Enrollment** | 14 | 0 | 14 | ✅ **100% Complete** |
| **Cost Calculation Controller** | 0 | 10+ | 10+ | ❌ **0% Complete** |
| **TOTALS** | **66** | **10+** | **76+** | **87% Complete** |

### 6.2 API Coverage Summary

#### **✅ FULLY IMPLEMENTED MODULES (52 APIs)**

**1. Carrier Management (13 APIs)**
- Complete CRUD operations with role-based access control
- Carrier validation and compatibility checking
- Status management and dependency validation

**2. Plan Management (14 APIs)**
- Template system with duplication functionality
- Document upload and management
- Plan status lifecycle management

**3. Plan Assignment Management (17 APIs)**
- Complete assignment lifecycle management
- Time constraint management and validation
- Clone and reassignment functionality

**4. Company Benefits Settings (8 APIs)**
- Enrollment period management
- Global eligibility configuration
- Settings validation and completeness checking

#### **🔄 PARTIALLY IMPLEMENTED**

**5. Employee Enrollment (7 of 8 APIs)**
- ✅ Core workflow: eligibility check, cost calculation, enrollment creation
- ✅ Basic management: get by employee, update, terminate
- ✅ Bulk operations: bulk waive, bulk enrollment with rollback
- ✅ **Service layer architecture**: All validation centralized in service layer
- ✅ **Consistent validation**: All APIs use identical business logic
- ✅ **Termination logic**: No enrollment period restriction (can terminate anytime)
- 📋 Pending: role-based listing

#### **📋 MISSING IMPLEMENTATION**

**6. Cost Calculation Controller (10+ APIs)**
- Service layer fully implemented
- Controller wrapper needed for dedicated endpoints
- Batch processing and validation endpoints required

### 6.3 Recent Changes

#### **Documentation Reorganization (Current Update)**

**✅ Structural Improvements:**
- Reorganized sections for better logical flow
- Moved business logic before data models
- Consolidated API specifications into dedicated section
- Added comprehensive status tracking

**✅ Content Accuracy:**
- Corrected Employee Enrollment API count (7 of 8, including bulk enrollment)
- Added implementation status markers to all APIs
- Updated section numbering for consistency
- Removed duplicate content and redundant sections

**✅ Implementation Verification:**
- Verified all controller implementations against documentation
- Confirmed 58 APIs are fully implemented and functional
- Identified 2 documented but pending Employee Enrollment APIs
- Documented missing Cost Calculation Controller requirement

#### **Production Readiness Assessment**

**✅ READY FOR PRODUCTION:**
- Core pre-enrollment workflow fully functional
- 83% of documented APIs implemented
- Comprehensive business logic validation
- Role-based access control and audit trails

**📋 ENHANCEMENT OPPORTUNITIES:**
- Complete remaining Employee Enrollment APIs
- Implement dedicated Cost Calculation Controller
- Add advanced reporting and analytics features

**The QHarmony Pre-Enrollment system provides a robust, production-ready foundation for insurance benefits management with comprehensive API coverage and well-documented business logic.**

---

## 🔧 **IMPLEMENTATION CONVENTIONS & BEST PRACTICES**

### **🛡️ Boolean Field Safety Patterns**

**Critical Issue**: Optional boolean fields can be `undefined` in the database, causing unsafe equality checks to fail.

#### **❌ Unsafe Patterns (Avoid These):**
```typescript
// UNSAFE: Will fail if isBroker is undefined
if (user.isBroker === false) {
  // undefined === false → false (unexpected behavior)
}

// UNSAFE: Will fail if isAdmin is undefined
if (!admin || !admin.isAdmin) {
  // !undefined → true (unexpected behavior)
}
```

#### **✅ Safe Patterns (Use These):**
```typescript
// ✅ RECOMMENDED: Using Boolean() constructor
Boolean(user?.isBroker)        // undefined → false
Boolean(user?.isAdmin)         // undefined → false
Boolean(user?.isDisabled)      // undefined → false

// ✅ SAFE: Negation
!Boolean(user?.isBroker)       // !false → true
!Boolean(user?.isAdmin)        // !false → true

// ✅ ALTERNATIVE: Using nullish coalescing
user?.isBroker ?? false        // undefined → false
user?.isAdmin ?? false         // undefined → false

// ✅ ALTERNATIVE: Using double negation
!!user?.isBroker              // undefined → false
!!user?.isAdmin               // undefined → false
```

#### **🎯 Why This Matters:**
- **Existing Data**: Records created before field was added
- **Partial Updates**: Updates that don't include all fields
- **External Data**: Data imported from other systems
- **Migration Issues**: Incomplete data migrations

#### **📊 Impact:**
- **Before Fixes**: Authentication failures, authorization bypasses, inconsistent behavior
- **After Fixes**: Consistent boolean evaluation, predictable behavior, production stability

### **🔍 Conditional Strict Filtering Patterns**

**Business Requirement**: Support both exact matching (strict mode) and partial matching (search mode) for enhanced user experience.

#### **📋 Implementation Pattern:**
```typescript
// Conditional filtering with strict mode parameter
function applyConditionalFiltering(query: any, filters: FilterOptions, strictMode: boolean = false) {
  if (strictMode) {
    // Exact matching for precise results
    if (filters.planName) query.planName = filters.planName;
    if (filters.planCode) query.planCode = filters.planCode;
    if (filters.coverageSubtype) query.coverageSubTypes = { $in: [filters.coverageSubtype] };
  } else {
    // Partial matching for search functionality
    if (filters.planName) query.planName = { $regex: filters.planName, $options: 'i' };
    if (filters.planCode) query.planCode = { $regex: filters.planCode, $options: 'i' };
    if (filters.coverageSubtype) query.coverageSubTypes = { $in: [filters.coverageSubtype] };
  }
}
```

#### **🎯 Use Cases:**
- **Strict Mode**: Dropdown selections, exact matches, API integrations
- **Search Mode**: User search, autocomplete, flexible filtering

### **📊 Array Validation Patterns**

**Business Rule**: Empty arrays should be treated the same as missing arrays for association logic.

#### **✅ Comprehensive Array Validation:**
```typescript
// Check both presence AND length
function hasValidArray(field: any[]): boolean {
  return Boolean(field?.length);  // undefined → false, [] → false, [item] → true
}

// Usage in business logic
if (hasValidArray(planAssignment.eligibleEmployeeClasses)) {
  // Process employee class restrictions
} else {
  // Default to all employee classes eligible
}
```

### **🏗️ Dynamic Field Extraction Patterns**

**Principle**: Avoid hardcoded field lists to prevent manual updates when interface fields are added.

#### **✅ Dynamic Interface Handling:**
```typescript
// Extract updateable fields dynamically
function extractUpdateableFields<T>(data: any, updateableInterface: T): Partial<T> {
  const updateableFields: Partial<T> = {};

  for (const key in updateableInterface) {
    if (data.hasOwnProperty(key)) {
      updateableFields[key] = data[key];
    }
  }

  return updateableFields;
}

// Usage in controllers
const updateData = extractUpdateableFields(request.body, {} as UpdateablePlanAssignmentData);
```

### **📅 Date Validation Patterns**

**Principle**: Separate format validation from business logic validation for flexibility.

#### **✅ Layered Date Validation:**
```typescript
// 1. Format validation only
function isValidDateFormat(dateString: string): boolean {
  return !isNaN(Date.parse(dateString));
}

// 2. Business logic validation (separate)
function isWithinBusinessRules(date: Date, context: BusinessContext): boolean {
  // Apply business-specific date constraints
  return date >= context.minDate && date <= context.maxDate;
}
```

### **🎯 Environment-Based Configuration Patterns**

**Principle**: Use simple environment variable naming with conditional logic rather than separate variable names.

#### **✅ Environment Configuration:**
```typescript
// Single variable names with environment-based values
const config = {
  mongoDbName: process.env.MONGO_DB_NAME,  // 'testing' or 'prod'
  azureBlobPrefix: process.env.MONGO_DB_NAME === 'testing' ? 'test-' : '',
  firebaseConfig: process.env.MONGO_DB_NAME === 'testing' ? testConfig : prodConfig
};
```

### **🎯 Status Management APIs**

The Employee Enrollment system includes comprehensive status management with business rule validation and audit trails.

#### **📋 Enrollment Status Lifecycle:**

```
🎯 UPDATED: Auto-Enrollment Flow
CREATE → Enrolled → [Waived/Terminated]
           ↓              ↑
         Waived ←→ Enrolled
           ↓              ↑
       Terminated ←→ Pending/Enrolled (via Reinstate)

Legacy Flow (Special Cases Only):
CREATE → Pending → Enrolled → [Waived/Terminated]
```

#### **✅ Status Management APIs:**

**1. Activate Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/activate
```

**Purpose**: Transition enrollment from Pending to Enrolled status

**Request Body**:
```json
{
  "activationDate": "2024-03-01",  // Optional: defaults to plan effective date
  "notes": "Enrollment activated by HR"  // Optional: activation notes
}
```

**2. Waive Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/waive
```

**Purpose**: Mark enrollment as waived (employee declined coverage)

**Request Body**:
```json
{
  "waiveReason": "Covered by spouse's plan",  // Required: reason for waiving
  "waiveDate": "2024-02-15",                  // Optional: defaults to current date
  "notes": "Employee has coverage elsewhere"   // Optional: additional notes
}
```

**3. Terminate Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/terminate
```

**Purpose**: End enrollment coverage (employee leaving, life event, etc.)

**Request Body**:
```json
{
  "terminationDate": "2024-06-30",           // Required: when coverage ends
  "terminationReason": "Employee terminated", // Required: reason for termination
  "notes": "Last day of employment"           // Optional: additional notes
}
```

**4. Reinstate Enrollment** ✅ IMPLEMENTED
```http
POST /api/pre-enrollment/employee-enrollments/:enrollmentId/reinstate
```

**Purpose**: Restore waived or terminated enrollment

**Request Body**:
```json
{
  "newStatus": "Enrolled",                    // Required: "Pending" or "Enrolled"
  "reinstateReason": "Employee changed mind", // Required: reason for reinstatement
  "newEffectiveDate": "2024-03-15",          // Optional: new coverage start date
  "notes": "Employee wants coverage back"     // Optional: additional notes
}
```

#### **🔐 Status Transition Rules:**

**Valid Transitions:**
- **Pending** → Enrolled, Waived, Terminated
- **Enrolled** → Waived, Terminated
- **Waived** → Pending, Enrolled (via reinstate)
- **Terminated** → Pending, Enrolled (via reinstate)

**Invalid Transitions:**
- **Enrolled** → Pending (must waive first)
- **Waived** → Terminated (must reinstate first)
- **Terminated** → Waived (must reinstate first)

#### **📋 Enrollment Statuses:**

```typescript
const ENROLLMENT_STATUSES = [
  'Enrolled',    // Active coverage (🎯 UPDATED: Default status for new enrollments)
  'Pending',     // Created but not yet active (🎯 UPDATED: Rarely used, mainly for special cases)
  'Waived',      // Employee declined coverage
  'Terminated',  // Coverage ended early
  'Expired'      // Coverage expired (past planEndDate) - read-only
];
```

### **🎯 NEW: Auto-Enrollment Behavior**

**All enrollments created within valid enrollment periods are automatically set to "Enrolled" status**, eliminating the need for manual activation in most cases. This applies to:

- ✅ **Open Enrollment**: Within enrollment period dates
- ✅ **New Hire**: Within new hire window (30 days from hire date)
- ✅ **Qualifying Life Event**: Within QLE window (30 days from event date)

**Benefits:**
- Immediate enrollment confirmation for employees
- Reduced administrative overhead
- Streamlined enrollment workflow
- Better user experience

**Status Transition Rules:**
- **Pending** → Enrolled, Waived, Terminated
- **Enrolled** → Waived, Terminated
- **Waived** → Pending, Enrolled (reinstatement)
- **Terminated** → Pending, Enrolled (reinstatement)
- **Expired** → ❌ **NO TRANSITIONS** (read-only)

**Status Behaviors:**
- **Enrolled**: Active coverage, can be edited, waived, or terminated (🎯 UPDATED: Most common status)
- **Pending**: Can be edited, activated, waived, or terminated (🎯 UPDATED: Rarely used)
- **Waived**: Can be reinstated to Pending or Enrolled
- **Terminated**: Can be reinstated to Pending or Enrolled
- **Expired**: **Read-only** - no edits, deletes, or status changes allowed

**⚠️ Expiry Protection:** Expired enrollments are **permanently protected** from all modifications.

### **📅 Employee Enrollment Expiry System**

#### **🎯 Expiry Mechanism**

Employee enrollments automatically expire when they reach their `planEndDate` (inherited from plan assignment). The system includes:

1. **Plan Reference Fields**: Each enrollment stores `planYear` and `planEndDate` from its plan assignment
2. **Automatic Expiry Detection**: System checks if current date > `planEndDate`
3. **Status Protection**: Expired enrollments become read-only
4. **Query Filtering**: Expired enrollments are excluded from default queries

#### **📋 Enhanced Expiry Rules**

**Status Transition Rules for Expiry:**
- ✅ **Pending → Expired**: ALLOWED - Pending enrollments can become expired
- ✅ **Enrolled → Expired**: ALLOWED - Active enrollments can become expired
- ❌ **Waived → Expired**: NOT ALLOWED - Waived enrollments remain waived for historical tracking
- ❌ **Terminated → Expired**: NOT ALLOWED - Terminated enrollments remain terminated for historical tracking

**Historical Preservation Principle:**
Waived and terminated enrollments represent important business decisions and are preserved in the system to maintain complete enrollment history and audit trails.

```typescript
// Enhanced expiry determination
function isEnrollmentExpired(enrollment: EmployeeEnrollment): boolean {
  const now = new Date();
  return new Date(enrollment.planEndDate) < now;
}

// Enhanced expiry eligibility check
function canTransitionToExpired(status: string): boolean {
  return ['Pending', 'Enrolled'].includes(status);
}

// Status transition validation
function canModifyEnrollment(enrollment: EmployeeEnrollment): boolean {
  return enrollment.status !== 'Expired';
}
```

#### **🔒 Operations Blocked for Expired Enrollments**

| Operation | **Allowed** | **Error Message** |
|-----------|-------------|-------------------|
| **Edit** | ❌ No | "Cannot edit expired enrollment" |
| **Delete** | ❌ No | "Cannot delete expired enrollment" |
| **Status Change** | ❌ No | "Expired enrollments cannot be modified" |
| **Waive** | ❌ No | "Cannot waive expired enrollment" |
| **Terminate** | ❌ No | "Cannot terminate expired enrollment" |
| **Reinstate** | ❌ No | "Cannot reinstate expired enrollment" |
| **Activate** | ❌ No | "Cannot activate expired enrollment" |
| **View** | ✅ Yes | Read-only access |

#### **📊 Query Behavior**

```typescript
// Default queries exclude expired enrollments
getDataByEmployeeId(employeeId) {
  return find({ employeeId, status: { $ne: 'Expired' } });
}

getDataByCompanyId(companyId) {
  return find({ companyId, status: { $ne: 'Expired' } });
}

// Special method to include expired enrollments
getAllDataIncludeExpired() {
  return find({}); // No status filter
}

// Dedicated expired enrollment access
getExpiredEnrollments() {
  // Performs automatic expiry check first
  await checkExpiredEnrollments();
  return find({ status: 'Expired' });
}
```

#### **🔄 Automatic Expiry Process**

```typescript
// Automatic expiry check and update
async function checkExpiredEnrollments(): Promise<{
  expiredCount: number;
  updatedEnrollments: string[];
}> {
  const now = new Date();

  // Find enrollments that should be expired
  // Only Pending and Enrolled statuses can transition to Expired
  // Waived and Terminated enrollments remain unchanged for historical tracking
  const expiredEnrollments = await find({
    planEndDate: { $lt: now },
    status: { $in: ['Pending', 'Enrolled'] } // Only these statuses can become expired
  });

  // Update status to 'Expired'
  for (const enrollment of expiredEnrollments) {
    await updateOne(
      { _id: enrollment._id },
      {
        status: 'Expired',
        lastModifiedAt: now,
        lastModifiedBy: 'system-expiry-check'
      }
    );
  }

  return {
    expiredCount: expiredEnrollments.length,
    updatedEnrollments: expiredEnrollments.map(e => e._id)
  };
}
```

#### **🎯 API Endpoints for Expiry Management**

```http
# Get expired enrollments (with automatic expiry check) - Two modes available
GET /api/pre-enrollment/employee-enrollments/expired

# Manual expiry check (SuperAdmin only)
POST /api/pre-enrollment/employee-enrollments/check-expired
```

#### **📋 Get Expired Enrollments API Specification**

**Endpoint:** `GET /api/pre-enrollment/employee-enrollments/expired`

**Query Parameters:**
- `mode` (optional): `'user'` (default) | `'planAssignments'`
- `targetUserId` (optional): User ID to get expired enrollments for (mode=user only)
- `planAssignmentIds` (required for planAssignments mode): Comma-separated plan assignment IDs

**Mode 1: User Mode (Default)**
```http
# Get current user's expired enrollments
GET /api/pre-enrollment/employee-enrollments/expired

# Get specific user's expired enrollments (admin/broker access required)
GET /api/pre-enrollment/employee-enrollments/expired?mode=user&targetUserId=USER_ID
```

**Mode 2: Plan Assignments Mode**
```http
# Get expired enrollments for specific plan assignments
GET /api/pre-enrollment/employee-enrollments/expired?mode=planAssignments&planAssignmentIds=ID1,ID2,ID3
```

**Response Format:**
```json
{
  "success": true,
  "mode": "user",
  "expiredEnrollments": [
    {
      "_id": "enrollment_id",
      "employeeId": "user_id",
      "planAssignmentId": "plan_assignment_id",
      "status": "Expired",
      "planYear": 2024,
      "planEndDate": "2024-12-31T23:59:59Z",
      "effectiveDate": "2024-01-01T00:00:00Z",
      "coverageType": "Your Health",
      "coverageTier": "Employee Only"
    }
  ],
  "count": 1,
  "message": "Found 1 expired enrollments",
  "expiryCheckPerformed": true,
  "timestamp": "2025-01-15T10:30:00Z",
  "targetUserId": "user_id"
}
```

**📋 Usage Examples:**

**1. Get assignments currently open for enrollment:**
```http
GET /api/pre-enrollment/plan-assignments/company/507f1f77bcf86cd799439011?enrollmentPeriodOnly=true
```

**2. Get assignments currently providing coverage:**
```http
GET /api/pre-enrollment/plan-assignments/company/507f1f77bcf86cd799439011?effectiveOnly=true
```

**3. Get assignments that will be effective in the future:**
```http
GET /api/pre-enrollment/plan-assignments/company/507f1f77bcf86cd799439011?futureOnly=true
```

**4. Get active assignments (exclude expired):**
```http
GET /api/pre-enrollment/plan-assignments/company/507f1f77bcf86cd799439011?includeInactive=false
```

**5. Historical view - what was available for enrollment on a specific date:**
```http
GET /api/pre-enrollment/plan-assignments/company/507f1f77bcf86cd799439011?enrollmentPeriodOnly=true&referenceDate=2023-11-15
```

**6. Get all assignments (default behavior):**
```http
GET /api/pre-enrollment/plan-assignments/company/507f1f77bcf86cd799439011
```

**🎯 Filtering Logic Explained:**

| **Filter** | **Logic** | **Use Case** |
|------------|-----------|--------------|
| `enrollmentPeriodOnly=true` | `enrollmentStartDate ≤ now ≤ enrollmentEndDate` | Employee enrollment portals - show plans available for enrollment |
| `effectiveOnly=true` | `planEffectiveDate ≤ now ≤ planEndDate` | Current benefits view - show active coverage |
| `futureOnly=true` | `planEffectiveDate > now` | Preview upcoming plan years |
| `includeInactive=false` | `isActive=true AND planEndDate ≥ now` | Active assignments only (default behavior) |
| No filters | All assignments | Administrative view - complete history |

**📊 Response Fields Explained:**

| **Field** | **Description** | **Example** |
|-----------|-----------------|-------------|
| `appliedFilters` | Array of filters that were applied | `["enrollmentPeriodOnly"]` |
| `filterMethod` | Primary filtering method used | `"enrollmentPeriod"` |
| `referenceDate` | Date used for time-based filtering | `"2024-11-15T10:30:00.000Z"` |
| `count` | Number of assignments returned | `5` |

**Access Control:**

**User Mode:**
- **Employees**: Can view their own expired enrollments only
- **Company Admins**: Can view expired enrollments for employees in their company
- **Brokers**: Can view expired enrollments for employees in their managed companies
- **SuperAdmins**: Can view any user's expired enrollments

**Plan Assignments Mode:**
- **Company Admins**: Can filter by plan assignments in their company
- **Brokers**: Can filter by plan assignments in their managed companies (with company filtering)
- **SuperAdmins**: Can filter by any plan assignments
- **Employees**: No access to plan assignments mode

**Error Responses:**
```json
// Invalid mode
{
  "error": "Invalid mode. Must be \"user\" or \"planAssignments\"",
  "validModes": ["user", "planAssignments"]
}

// Missing plan assignment IDs
{
  "error": "planAssignmentIds query parameter is required for planAssignments mode"
}

// Access denied
{
  "error": "Access denied. You can only view your own expired enrollments unless you are an admin, broker, or super admin."
}
```

#### **💡 Frontend Integration**

```typescript
// Frontend helper for enrollment status display
function getEnrollmentDisplayInfo(enrollment: EmployeeEnrollment) {
  const now = new Date();
  const planEndDate = new Date(enrollment.planEndDate);

  if (enrollment.status === 'Expired' || now > planEndDate) {
    return {
      status: 'Expired',
      displayText: 'Coverage Expired',
      color: 'gray',
      canModify: false,
      endDate: planEndDate
    };
  }

  // Handle other statuses...
}

// Multi-year enrollment history
function groupEnrollmentsByYear(enrollments: EmployeeEnrollment[]) {
  return enrollments.reduce((groups, enrollment) => {
    const year = enrollment.planYear;
    if (!groups[year]) groups[year] = [];
    groups[year].push(enrollment);
    return groups;
  }, {});
}
```

#### **🎯 Business Benefits**

1. **Data Integrity**: Prevents modification of historical enrollment data
2. **Compliance**: Maintains accurate records for audit and reporting
3. **Performance**: Excludes expired data from routine queries
4. **User Experience**: Clear distinction between active and expired coverage
5. **System Reliability**: Automatic expiry prevents manual errors

#### **🎯 Enrollment Types:**

```typescript
const ENROLLMENT_UNDER_TYPES = [
  'Open Enrollment',        // Annual enrollment period
  'New Hire',              // 30-day window from hire date
  'Qualifying Life Event'  // 30-day window from life event
];
```

#### **📋 Qualifying Life Event Types:**

```typescript
const QLE_EVENT_TYPES = [
  'Marriage',              // Getting married
  'Divorce',               // Divorce or legal separation
  'Birth',                 // Birth of child
  'Adoption',              // Adoption of child
  'Loss of Coverage',      // Loss of other health coverage
  'Job Change',            // Spouse job change affecting coverage
  'Death',                 // Death of covered family member
  'Relocation',            // Moving to new area
  'Other'                  // Other qualifying events
];
```

### **📋 System Constants & Enums**

**Business Rule**: Use centralized constants for consistency and maintainability across the system.

#### **🎯 Enrollment Types:**
```typescript
const ENROLLMENT_TYPES = [
  'Active',    // Employee must actively select plans and coverage options
  'Passive'    // Employee automatically continues previous elections
];
```

#### **⏰ Waiting Period Rules:**
```typescript
const WAITING_PERIOD_RULES = [
  'Immediate',                        // Eligible immediately upon hire
  'Days from hire date',              // Eligible after X days from hire
  'First of month after X days'       // Eligible first of month after waiting period
];
```

#### **🏢 Employee Class Types:**
```typescript
const EMPLOYEE_CLASS_TYPES = [
  'Full-Time',     // Standard eligibility for all benefits
  'Part-Time',     // Limited eligibility or different waiting periods
  'Contractor',    // Usually not eligible (legal/tax implications)
  'Temporary',     // Often excluded from benefits
  'Seasonal'       // Special eligibility rules
];
```

#### **⭐ A.M. Best Rating Codes:**
```typescript
const AM_BEST_RATING_CODES = [
  'A++', 'A+', 'A', 'A-',           // Superior to Excellent
  'B++', 'B+', 'B', 'B-',           // Good to Fair
  'C++', 'C+', 'C', 'C-',           // Marginal to Weak
  'D', 'E', 'F',                     // Poor to Liquidation
  'NR'                               // Not Rated (default)
];
```

#### **📊 Rate Structure Types:**
```typescript
const RATE_STRUCTURE_TYPES = [
  'composite',              // Flat rates regardless of demographics
  'four-tier',             // Standard family composition tiers
  'age-banded',            // Rates vary by employee age
  'age-banded-four-tier',  // Combination of age bands and family composition
  'salary-based'           // Rates based on employee salary ranges
];
```

#### **💰 Contribution Types:**
```typescript
const CONTRIBUTION_TYPES = [
  'Fixed',        // Fixed dollar amount
  'Percentage',   // Percentage of premium
  'Remainder'     // Pays remainder after other party's contribution
];
```

#### **📅 Payroll Frequencies:**
```typescript
const PAYROLL_FREQUENCIES = {
  'Weekly': 52,        // 52 pay periods per year
  'Biweekly': 26,      // 26 pay periods per year
  'Semi-Monthly': 24,  // 24 pay periods per year
  'Monthly': 12        // 12 pay periods per year
};
```

#### **🎯 Coverage Tier Names:**
```typescript
const COVERAGE_TIER_NAMES = [
  'Employee Only',
  'Employee + Spouse',
  'Employee + Children',
  'Family'
];
```

#### **📋 Plan Statuses:**
```typescript
const PLAN_STATUSES = [
  'Active',     // Available for assignment and enrollment
  'Archived',   // Not visible during assignment (final state)
  'Draft',      // Work in progress, editable
  'Template'    // Template for duplication (immutable)
];
```

**Status Transition Rules:**
- **Draft → Active**: Use `POST /plans/:id/activate` (requires validation)
- **Active → Draft**: Use `POST /plans/:id/convert-to-draft` (makes editable)
- **Archived → Active**: Use `POST /plans/:id/activate` (allows reactivation)
- **Any → Archived**: Use `POST /plans/:id/archive` (final state, unusable)
- **Template**: Cannot be changed (immutable status)

**Status Behaviors:**
- **Active**: Can be assigned to companies, enrolled by employees
- **Draft**: Can be edited, cannot be assigned until activated
- **Archived**: Cannot be assigned, can be reactivated to Active status
- **Template**: Read-only, can be duplicated by brokers

#### **📋 Plan Assignment Statuses:**
```typescript
const PLAN_ASSIGNMENT_STATUSES = [
  'Active',       // Currently assignable, allows new enrollments
  'Deactivated',  // Manually disabled, blocks new enrollments
  'Expired'       // Past planEndDate, permanently blocked
];
```

**Status Transition Rules:**
- **Deactivated → Active**: Use `POST /plan-assignments/:id/activate` (if not expired)
- **Active → Deactivated**: Use `POST /plan-assignments/:id/deactivate` (if not expired)
- **Any → Expired**: Automatic when past `planEndDate` (irreversible)

**Status Behaviors:**
- **Active**: Allows new enrollments, visible in queries
- **Deactivated**: Blocks new enrollments, hidden from default queries, existing enrollments continue
- **Expired**: Permanently blocked from all status changes, existing enrollments continue

**⚠️ Expiry Protection:** Both activate and deactivate operations are **blocked** for expired assignments.

#### **🏗️ Plan Types:**
```typescript
const PLAN_TYPES = [
  'PPO',        // Preferred Provider Organization
  'HMO',        // Health Maintenance Organization
  'HDHP',       // High Deductible Health Plan
  'EPO',        // Exclusive Provider Organization
  'POS'         // Point of Service
];
```

#### **🥇 Metal Tiers:**
```typescript
const METAL_TIERS = [
  'Bronze',     // ~60% actuarial value
  'Silver',     // ~70% actuarial value
  'Gold',       // ~80% actuarial value
  'Platinum'    // ~90% actuarial value
];
```

---

### **🧪 Testing & Validation**

The Employee Enrollment system includes comprehensive test suites to validate all functionality:

#### **📋 Available Test Scripts:**

**1. Unit Test Suite** (`test-scripts/unit-test-enrollment.js`)
- **Purpose**: Tests business logic with mock data (no server/database required)
- **Coverage**: 47 tests covering all core functionality
- **Usage**: `node test-scripts/unit-test-enrollment.js`
- **Features**:
  - Status transition validation
  - Employee eligibility checks
  - Cost calculation algorithms
  - Coverage tier validation
  - Access control logic
  - Edge case handling

**2. Enhanced Comprehensive Test Suite** (`test-scripts/comprehensive-enrollment-test-enhanced.js`)
- **Purpose**: Comprehensive testing with realistic scenarios
- **Coverage**: 69 tests covering all business scenarios
- **Usage**: `node test-scripts/comprehensive-enrollment-test-enhanced.js`
- **Features**:
  - Multiple enrollment types (Open, New Hire, QLE)
  - All rate structures (Composite, Four-Tier, Age-Banded, Salary-Based)
  - Expired plan assignment validation
  - Post-enrollment period scenarios
  - Complex family structures
  - Real-world edge cases

#### **✅ Test Results:**
- **Unit Tests**: 47/47 tests passing (100% success rate)
- **Enhanced Tests**: 69/69 tests passing (100% success rate)
- **Total Coverage**: 116 tests validating all functionality

#### **🎯 Test Coverage Areas:**
- **Status Management**: All transitions and business rules
- **Enrollment Types**: Open enrollment, new hire, QLE scenarios
- **Cost Calculations**: All rate structures and contribution types
- **Employee Validation**: Profile completeness, eligibility rules
- **Access Control**: Role-based permissions and security
- **Edge Cases**: Invalid data, boundary conditions, error handling
- **Business Rules**: Waiting periods, class restrictions, duplicates

#### **💡 Running Tests:**
```bash
# Run unit tests (mock data, no server required)
node test-scripts/unit-test-enrollment.js

# Run comprehensive tests (realistic scenarios)
node test-scripts/comprehensive-enrollment-test-enhanced.js
```

---

**These implementation patterns and comprehensive test coverage ensure consistent, maintainable, and robust code across the entire QHarmony pre-enrollment system.**
