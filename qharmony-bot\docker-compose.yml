version: "3.8"

services:
  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"

  app:
    image: qharmonyregistry.azurecr.io/qharmonybotapp:v7
    build: .
    container_name: qharmonybotapp
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://redis:6379
      - PINECONE_API_KEY
      - OPENAI_API_KEY
      - MONGO_DB_URI
      - AWS_ACCESS_KEY
      - AWS_SECRET_KEY
    depends_on:
      - redis
