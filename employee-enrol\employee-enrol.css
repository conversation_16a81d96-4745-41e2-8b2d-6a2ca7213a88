/* Employee Enrollment Styles */

/* Custom slider styling */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: #e5e7eb;
  outline: none;
  border-radius: 8px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #374151;
  cursor: pointer;
  border-radius: 50%;
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #374151;
  cursor: pointer;
  border-radius: 50%;
  border: none;
}

/* Progress bar styling */
.progress-bar {
  background: linear-gradient(to right, #374151 0%, #374151 var(--progress, 0%), #e5e7eb var(--progress, 0%), #e5e7eb 100%);
}

/* Step pill animations */
.step-pill {
  transition: all 0.3s ease;
}

.step-pill:hover {
  transform: translateY(-1px);
}

/* Card hover effects */
.plan-card {
  transition: all 0.3s ease;
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button animations */
.btn-primary {
  transition: all 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
}

/* Custom checkbox and radio styling */
input[type="checkbox"]:checked,
input[type="radio"]:checked {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom focus styles */
button:focus,
input:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .step-pills {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .step-pill {
    font-size: 12px;
    padding: 6px 12px;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .navigation-footer {
    padding: 16px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .bg-gray-100 {
    background-color: #1f2937;
  }
  
  .bg-white {
    background-color: #374151;
  }
  
  .text-gray-900 {
    color: #f9fafb;
  }
  
  .text-gray-700 {
    color: #d1d5db;
  }
  
  .border-gray-200 {
    border-color: #4b5563;
  }
}
