"use client";

import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { getBenefitsForType } from "@/middleware/benefits_middleware";
import {
  getUsersCompanyId,
  setSelectedBenefitId,
} from "@/redux/reducers/userSlice";
import {
  clearBenefitsState,
  selectBenefitsByType,
} from "@/redux/reducers/benefitsSlice";
import {
  Box,
  Card,
  CardContent,
  Button,
  Typography,
  Grid,
  CircularProgress,
  Icon,
} from "@mui/material";
import { useParams, useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";
import { getBenefitSubtypeIcon } from "@/components/benefit_vector_map";
import withMobileEdgeFill from "@/components/mobile_edge_fill";
import { maskedSubCategory } from "@/middleware/company_middleware";

const BenefitsByTypeDisplay = () => {
  const { benefitType } = useParams();
  const decodedBenefitType = decodeURIComponent(benefitType as string); // Decode the URL-encoded benefitType
  const dispatch = useAppDispatch();
  const router = useRouter();

  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));

  const userDetails = useAppSelector(
    (state: RootState) => state.user.userProfile,
  );

  const benefitsPerType = useSelector(
    (state: RootState) =>
      selectBenefitsByType(state, decodedBenefitType as string), // Use decodedBenefitType
  );

  useEffect(() => {
    if (decodedBenefitType !== "" && companyId !== "") {
      dispatch(clearBenefitsState());
      getBenefitsForType(dispatch, companyId, decodedBenefitType as string); // Use decodedBenefitType
    }
  }, [decodedBenefitType, companyId, dispatch]);

  const handleBenefitClick = (benefitId: string) => {
    dispatch(setSelectedBenefitId(benefitId));
    router.push(`/viewBenefitDetails/${benefitId}`);
  };


  const capitalizeWords = (name: string) => {
    return name.replace(/\b\w/g, (char) => char.toUpperCase());
  };

  return (
    <ProtectedRoute>
      <Box
        sx={{
          flexGrow: 1,
          minHeight: "100vh",
          width: "100%",
          bgcolor: "#F5F6FA",
          padding: 4,
        }}
      >
        <Typography variant="h4" sx={{ fontWeight: "bold", mb: 2 }}>
          Hey {capitalizeWords(userDetails.name)},
        </Typography>
        <Typography variant="body1" sx={{ color: "#6c757d", mb: 4 }}>
          Explore your benefits now—tap to dive in!
        </Typography>

        <Box sx={{ mb: 4 }}>
          {/* <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
            {decodedBenefitType}
          </Typography> */}

          {/* Updated Loading State */}
          {benefitsPerType === null ? (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start", // Align to start
                justifyContent: "center",
                height: "150px", // Adjusted height for a compact loading area
              }}
            >
              <CircularProgress size={50} sx={{ color: "#6c757d", mb: 2 }} />
            </Box>
          ) : benefitsPerType.benefits.filter((benefit) => benefit.isActivated)
              .length === 0 ? (
            // Updated No Benefits View
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start", // Align to start
                justifyContent: "center",
                height: "150px", // Compact height for no benefits
                borderRadius: "12px",
                backgroundColor: "#F5F6FA",
                textAlign: "left", // Align text to left
              }}
            >
              <Typography
                variant="h6"
                sx={{ fontWeight: "bold", color: "#6c757d", mb: 2 }}
              >
                No benefits available
              </Typography>
              <Typography variant="body2" sx={{ color: "#9E9E9E" }}>
                There are currently no active benefits for this type.
              </Typography>
            </Box>
          ) : (
            <Grid container spacing={3}>
              {benefitsPerType.benefits
                .filter((benefit) => benefit.isActivated)
                .map((benefit, index) => (
                  <Grid item xs={12} key={benefit._id}>
                    <Card
                      sx={{
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "flex-start",
                        justifyContent: "flex-start",
                        borderRadius: "16px",
                        bgcolor: "#ffffff",
                        padding: 3,
                        boxShadow: "none",
                      }}
                    >
                      {getBenefitSubtypeIcon(benefit.subType, {
                        marginBottom: 15,
                        fontSize: 35,
                        color: "#606267",
                      })}
                      <CardContent
                        sx={{ flexGrow: 1, padding: 0, width: "100%" }}
                      >
                        <Typography
                          variant="h6"
                          sx={{ fontWeight: 600, textAlign: "left" }}
                        >
                          {maskedSubCategory(benefit.subType)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: "#6c757d",
                            marginTop: 1,
                            textAlign: "left",
                          }}
                        >
                          Check your {maskedSubCategory(benefit.subType)} benefits
                        </Typography>
                      </CardContent>
                      <Box sx={{ paddingTop: 2, width: "100%" }}>
                        <Button
                          variant="contained"
                          fullWidth
                          onClick={() => handleBenefitClick(benefit._id)}
                          sx={{
                            textTransform: "none",
                            borderRadius: "8px",
                            bgcolor: "#e8e8e8",
                            color: "black",
                            boxShadow: "none",
                            padding: "10px 0",
                            "&:hover": {
                              backgroundColor: "#d8d8d8",
                              boxShadow: "none",
                            },
                          }}
                        >
                          View
                        </Button>
                      </Box>
                    </Card>
                  </Grid>
                ))}
            </Grid>
          )}
        </Box>
      </Box>
    </ProtectedRoute>
  );
};

export default withMobileEdgeFill(BenefitsByTypeDisplay);
