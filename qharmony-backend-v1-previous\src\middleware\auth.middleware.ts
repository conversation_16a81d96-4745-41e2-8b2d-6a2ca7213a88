import express from 'express';
import jwt from 'jsonwebtoken';

import HttpException from '../exceptions/http.exception';
import { DataStoredInToken } from '../interfaces/dataStoredInToken.interface';
import CompanyModelClass from '../nosql/company.model';
import UserModelClass from '../nosql/user.model';

export async function auth(
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
): Promise<void> {
  try {
    const cookies = req.cookies;
    if (!cookies?.Authorization) {
      next(new HttpException(401, 'You are not authorized'));
      // Make linter happy.
      return;
    }
    const secret = process.env.QHARMONY_SECRET as string;
    const verificationResponse = jwt.verify(
      cookies.Authorization,
      secret
    ) as DataStoredInToken;
    const email = verificationResponse.email.toLowerCase();

    // If there is no email, then the token is invalid
    if (!email) {
      res.clearCookie('Authorization');
      next(new HttpException(401, 'You are not authorized'));
      // Make linter happy.
      return;
    }

    // Verify that email exists in the database
    const userData = await UserModelClass.getDataByEmail({ email });
    if (!userData) {
      res.clearCookie('Authorization');
      next(new HttpException(401, 'You are not authorized'));
      // Make linter happy.
      return;
    }

    // Fetch the company data
    const companyData = await CompanyModelClass.getDataById(userData.companyId);
    if (!companyData) {
      res.clearCookie('Authorization');
      next(new HttpException(401, 'You are not authorized'));
      // Make linter happy.
      return;
    }
    req.body.user = userData;
    req.body.company = companyData;
  } catch (error) {
    res.clearCookie('Authorization');
    next(new HttpException(401, 'You are not authorized'));
    // Make linter happy.
    return;
  }
  next();
}

function authMiddleware(): express.RequestHandler {
  return async (
    req: express.Request,
    _res: express.Response,
    next: express.NextFunction
  ) => {
    await auth(req, _res, next);
  };
}

export default authMiddleware;
