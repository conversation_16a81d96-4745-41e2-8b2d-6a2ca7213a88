import { InsuranceType } from '../enums/insuranceType.enum';
import { CompactBenefit } from '../interfaces/finch.interface';
import EnvService from '../services/env.service';
import logger, { prettyJSON } from '../utils/logger';
import { convertYyyyMmDdToMmDdYyyy, getEmojiForBenefit, getLongDescriptionForBenefit, makeNumberHumanReadable, snakeCaseToHumanReadable } from '../utils/utils';

interface GeneratedBenefit {
  type: string;
  text?: {
    type: string;
    text: string;
  };
  image_url?: string;
  alt_text?: string;
  fields?: {
    type: string;
    text: string;
    emoji?: boolean;
  }[];
  elements?: any[];
}

function generateBlocksForBenefits(
  compactJSON: CompactBenefit[]
): GeneratedBenefit[] {
  const generatedJSON: GeneratedBenefit[] = [];

  // Put all the benefits which do not have type in InsuranceType :
  // InsuranceType.s125_dental;
  // InsuranceType.s125_medical;
  // InsuranceType.s125_vision;

  compactJSON.filter(benefit => Object.values(InsuranceType).indexOf(benefit.type as unknown as InsuranceType) === -1).forEach((benefit) => {
    generatedJSON.push({
      type: 'actions',
      elements: [
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: `${getEmojiForBenefit(benefit)} ${benefit.description}`,
            emoji: true,
          },
          value: 'random',
          action_id: `benefit_${benefit.benefit_id}`,
        }
      ],
    });
    generatedJSON.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `_${getLongDescriptionForBenefit(benefit)}_`,
      },
    });

    generatedJSON.push({
      type: 'section',
      fields: [
        { type: 'plain_text', text: 'Frequency', emoji: true },
        {
          type: 'mrkdwn',
          text: `*${snakeCaseToHumanReadable(benefit.frequency)}*`,
        },
        { type: 'plain_text', text: 'Employee deduction', emoji: true },
        {
          type: 'mrkdwn',
          text: `*$0*`,
        },
        { type: 'plain_text', text: 'Employer contribution', emoji: true },
        {
          type: 'mrkdwn',
          text: `*$100*`,
        },
      ],
    });

  });

  return generatedJSON;
}

function generateBlocksForInsurances(
  compactJSON: CompactBenefit[]
): GeneratedBenefit[] {
  const generatedJSON: GeneratedBenefit[] = [];

  compactJSON.filter(benefit => Object.values(InsuranceType).indexOf(benefit.type as unknown as InsuranceType) !== -1).forEach((benefit) => {
    if (benefit.type === 's125_medical' || benefit.type === 's125_vision' || benefit.type === 's125_dental') {
      generatedJSON.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: `${getEmojiForBenefit(benefit)} ${benefit.description}`,
              emoji: true,
            },
            value: 'random',
            action_id: `benefit_${benefit.benefit_id}`,
          }
        ],
      });
      generatedJSON.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `_${getLongDescriptionForBenefit(benefit)}_`,
        },
      });

      generatedJSON.push({
        type: 'section',
        fields: [
          // { type: 'plain_text', text: 'Description', emoji: true },
          // { type: 'mrkdwn', text: `\`${getLongDescriptionForBenefit(benefit)}\`` },
          { type: 'plain_text', text: 'Type', emoji: true },
          { type: 'mrkdwn', text: `*${(benefit.type)}*` },
          { type: 'plain_text', text: 'Frequency', emoji: true },
          {
            type: 'mrkdwn',
            text: `*${snakeCaseToHumanReadable(benefit.frequency)}*`,
          },
        ],
      });

      generatedJSON.push({
        type: 'image',
        image_url: `https://qharmony-public.s3.amazonaws.com/${benefit.type}.png`,
        alt_text: `${benefit.type} flyer`,
      });
      return;
    }
  });


  return generatedJSON;
}
interface CompactTax {
  name: string;
  employer: boolean;
  type: string;
  amount: number;
  currency: string;
}

interface GeneratedTax {
  type: string;
  fields: {
    type: string;
    text: string;
    emoji?: boolean;
  }[];
}

function generateTaxJSON(compactJSON: CompactTax[]): GeneratedTax[] {
  const generatedJSONs: GeneratedTax[] = [];

  // Only have 5 fields in a section
  let count = 0;
  compactJSON.forEach((tax) => {
    if (count === 0) {
      generatedJSONs.push({
        type: 'section',
        fields: [],
      });
    }

    const generatedJSON = generatedJSONs[generatedJSONs.length - 1];

    generatedJSON.fields.push({
      type: 'plain_text',
      text: `${tax.name}`,
      emoji: true,
    });

    generatedJSON.fields.push({
      type: 'mrkdwn',
      text: `*${tax.currency === 'usd' ? '$' : ''}${makeNumberHumanReadable(tax.amount)
        } ${tax.currency.toUpperCase()}*`,
    });

    count++;
    if (count === 5) {
      count = 0;
    }
  });

  return generatedJSONs;
}

export const homeVerifiedLayout = (userData: any) => {
  // Extract user details from userData object
  const {
    showDisconnectButton,
    pageNum,
    firstName,
    lastName,
    department,
    residence,
    notificationPaused,
    title,
    employmentType,
    startDate,
    // dob,
    slackTeamId,
    incomeUnit,
    incomeAmount,
    incomeCurrency,
    incomeEffectiveDate,
    benefits,
    taxes,
    payStub,
    timeoffs,
    companyDocument,
  } = userData;

  logger.info(`pageNum: ${pageNum}`);
  logger.info(`companyDocument: ${prettyJSON(companyDocument)}`);
  /**
   * 
   * timeoffs structure is as follows:
  [
    {
      "type": "pto",
      "fromTsInSecs": 1648771200,
      "toTsInSecs": 1680307200,
      "alloted": 10,
      "used": 4
    },
    {
      "type": "pto",
      "fromTsInSecs": 1680307200,
      "toTsInSecs": 1711929600,
      "alloted": 10,
      "used": 7
    },
    {
      "type": "sick-leave",
      "fromTsInSecs": 1648771200,
      "toTsInSecs": 1680307200,
      "alloted": 20,
      "used": 14
    },
    {
      "type": "sick-leave",
      "fromTsInSecs": 1680307200,
      "toTsInSecs": 1711929600,
      "alloted": 20,
      "used": 17
    }
  ]
  We need to extract the used and alloted for the current time period
   * 
   *  */

  const currentTs = Math.floor(Date.now() / 1000);
  let currentAllotedPto = 0;
  let currentUsedPto = 0;
  let currentAllotedSickLeave = 0;
  let currentUsedSickLeave = 0;
  
  timeoffs.forEach((timeoff: { type: string; fromTsInSecs: number; toTsInSecs: number; alloted: number; used: number; }) => {
    if (timeoff.type === 'pto' && timeoff.fromTsInSecs <= currentTs && timeoff.toTsInSecs >= currentTs) {
      currentAllotedPto = timeoff.alloted;
      currentUsedPto = timeoff.used;
    }
    if (timeoff.type === 'sick-leave' && timeoff.fromTsInSecs <= currentTs && timeoff.toTsInSecs >= currentTs) {
      currentAllotedSickLeave = timeoff.alloted;
      currentUsedSickLeave = timeoff.used;
    }
  });

  const blocks = [];
  slackTeamId;

  blocks.push(
    {
      type: 'image',
      image_url: 'https://qharmony-public.s3.amazonaws.com/banner.png',
      alt_text: 'qharmony banner',
    },
    {
      type: 'actions',
      elements: [
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: `🤗 Personal Info`,
            emoji: true,
          },
          value: 'tab1dasfiehnedsv',
          action_id: `tab1dasfiehnedsv`,
          ...(pageNum === 1 ? { style: 'primary' } : {}),
        },
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: `💰 Payroll`,
            emoji: true,
          },
          value: 'tab2dasfiehnedsv',
          action_id: `tab2dasfiehnedsv`,
          ...(pageNum === 2 ? { style: 'primary' } : {}),
        },
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: `:mending_heart: Insurance`,
            emoji: true,
          },
          value: 'tab3dasfiehnedsv',
          action_id: `tab3dasfiehnedsv`,
          ...(pageNum === 3 ? { style: 'primary' } : {}),
        },
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: `✨ Benefits`,
            emoji: true,
          },
          value: 'tab4dasfiehnedsv',
          action_id: `tab4dasfiehnedsv`,
          ...(pageNum === 4 ? { style: 'primary' } : {}),
        },
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: `:qharmony: Chat with HR`,
            emoji: true,
          },
          value: 'tab5dasfiehnedsv',
          action_id: `tab5dasfiehnedsv`,
          style: 'primary',
          url: `slack://app?team=${slackTeamId}&id=${EnvService.env().SLACK_APP_ID}&tab=messages`, // Todo: take from db
          // ...(pageNum === 5 ? { style: 'primary' } : {}),
        },
      ],
    },
  );
  if (pageNum === 1) {
    blocks.push(
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: `${firstName} ${lastName}`,
          emoji: true,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'plain_text',
            text: 'Department',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${department}*`,
          },
          {
            type: 'plain_text',
            text: 'Title',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${title}*`,
          },
          {
            type: 'plain_text',
            text: 'Employment Type',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${snakeCaseToHumanReadable(employmentType)}*`,
          },
          {
            type: 'plain_text',
            text: 'Start Date',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${convertYyyyMmDdToMmDdYyyy(startDate)}*`,
          },
          // {
          //   type: 'plain_text',
          //   text: 'Birthday',
          //   emoji: true,
          // },
          // {
          //   type: 'mrkdwn',
          //   // convert the dob from yyyy-mm-dd to mm-dd-yyyy and then chop away the year
          //   text: `*${convertYyyyMmDdToMmDdYyyy(dob).slice(0, -5)}*`,
          // },
        ],
      },
      {
        type: 'divider',
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: ':house: Residence',
          emoji: true,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'plain_text',
            text: `${residence}`,
            emoji: true,
          },
        ],
      },
      {
        type: 'divider',
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '⛱️ Leave Balances',
          emoji: true,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'plain_text',
            text: 'PTO',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${currentAllotedPto - currentUsedPto} out of ${currentAllotedPto} remaining*`,
          },
          {
            type: 'plain_text',
            text: 'Sick Leave',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${currentAllotedSickLeave - currentUsedSickLeave} out of ${currentAllotedSickLeave} remaining*`,
          }
        ],
      },
      {
        type: 'divider',
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📖 Employee Handbook',
          emoji: true,
        },
      },
      {
        "type": "section",
        "text": {
          "type": "mrkdwn",
          "text": "Access your company's policies, benefits, workplace culture, and more  👉"
        },
        "accessory": {
          "type": "button",
          "text": {
            "type": "plain_text",
            "emoji": true,
            "text": "👀 View"
          },
          value: 'random',
          url: companyDocument.employeeHandbookUrl,
          action_id: '📖 Employee Handbook',
        }
      },
      {
        type: 'divider',
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📞 Help center',
          emoji: true,
        },
      },
      {
        "type": "section",
        "text": {
          "type": "mrkdwn",
          "text": "Contact your HR team for benefits inquiries  👉"
        },
        "accessory": {
          "type": "button",
          "text": {
            "type": "plain_text",
            "emoji": true,
            "text": "👀 View"
          },
          value: 'view_help_center',
          action_id: 'view_help_center',
        }
      },
      {
        type: 'divider'
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🔔 Notifications',
          emoji: true,
        },
      },
      {
        "type": "section",
        "text": {
          "type": "mrkdwn",
          "text": "You can choose to turn notifications on or off  👉"
        },
        "accessory": {
          "type": "button",
          text: {
            type: 'plain_text',
            text: `${notificationPaused
                ? '▶ Resume Notifications'
                : '⏸️ Pause Notifications'
              }`,
            emoji: true,
          },
          value: 'random',
          action_id: `${notificationPaused ? 'resumeNotification' : 'pauseNotification'
            }`,
        }
      },
      {
        type: 'divider'
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🔗 Key Links',
          emoji: true,
        },
      },
      {
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '🤓 Training',
              emoji: true,
            },
            value: 'random',
            url: `slack://app?team=${slackTeamId}&id=${EnvService.env().SLACK_APP_ID}&tab=messages`, // Todo: take from db
            action_id: '🤓 Training',
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '📈 Performance Review',
              emoji: true,
            },
            value: 'random',
            url: companyDocument.performanceReviewLink,
            action_id: '📈 Performance Review',
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '🧳 Immigration',
              emoji: true,
            },
            value: 'random',
            url: companyDocument.immigrationLink,
            action_id: '🧳 Immigration',
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '😷 Medical Claim',
              emoji: true,
            },
            value: 'random',
            url: companyDocument.medicalClaimLink,
            action_id: '😷 Medical Claim',
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '💸 Expense Reporting',
              emoji: true,
            },
            value: 'random',
            url: companyDocument.expenseReportingLink,
            action_id: '💸 Expense Reporting',
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '👶 Add Dependent',
              emoji: true,
            },
            value: 'random',
            url: companyDocument.addDependentLink,
            action_id: '👶 Add Dependent',
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '🩼 Short Term Disability',
              emoji: true,
            },
            value: 'random',
            url: companyDocument.shortTermDisabilityLink,
            action_id: '🩼 Short Term Disability',
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '🦽 Long Term Disability',
              emoji: true,
            },
            value: 'random',
            url: companyDocument.longTermDisabilityLink,
            action_id: '🦽 Long Term Disability',
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: '🍼 Maternity Leave',
              emoji: true,
            },
            value: 'random',
            url: companyDocument.maternityLeaveLink,
            action_id: '🍼 Maternity Leave',
          },
        ],
      },
      {
        type: 'divider',
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: 'Exciting features on the horizon!',
          emoji: true,
        },
      },
      {
        "type": "section",
        "text": {
          "type": "mrkdwn",
          "text": "*Wellness Assistant* `Coming Soon`\n" + "Personalized tips and reminders for physical and mental health."
        },
        "accessory": {
          "type": "image",
          "image_url": "https://qharmony-public.s3.amazonaws.com/wellness-assistant-side.png",
          "alt_text": "Windsor Court Hotel thumbnail"
        }
      },
      {
        "type": "section",
        "text": {
          "type": "mrkdwn",
          "text": "*Financial Assistant* `Coming Soon`\n" + "Maximize financial well-being with insights, reminders, and budgeting tips."
        },
        "accessory": {
          "type": "image",
          "image_url": "https://qharmony-public.s3.amazonaws.com/financial-assistant-side.png",
          "alt_text": "Windsor Court Hotel thumbnail"
        }
      },
      {
        "type": "section",
        "text": {
          "type": "mrkdwn",
          "text": "*Career Assistant* `Coming Soon`\n" + "Elevate professional growth with advice, reminders, and skill development tips."
        },
        "accessory": {
          "type": "image",
          "image_url": "https://qharmony-public.s3.amazonaws.com/career-assistant-side.png",
          "alt_text": "Windsor Court Hotel thumbnail"
        }
      },
      // {
      //   "type": "section",
      //   "text": {
      //     "type": "mrkdwn",
      //     "text": "*Volunteering* `Coming Soon`\n" + "Find tailored volunteering to keep you engaged in giving back."
      //   },
      //   "accessory": {
      //     "type": "image",
      //     "image_url": "https://qharmony-public.s3.amazonaws.com/volunterring-side.png",
      //     "alt_text": "Windsor Court Hotel thumbnail"
      //   }
      // },
      // {
      //   "type": "section",
      //   "text": {
      //     "type": "mrkdwn",
      //     "text": "*Find Medical Providers* `Coming Soon`\n" + "Locate healthcare based on needs, preferences, and coverage."
      //   },
      //   "accessory": {
      //     "type": "image",
      //     "image_url": "https://qharmony-public.s3.amazonaws.com/find-medical-providers-side.png",
      //     "alt_text": "Windsor Court Hotel thumbnail"
      //   }
      // },
      // {
      //   "type": "section",
      //   "text": {
      //     "type": "mrkdwn",
      //     "text": "*Pharmarcy Assistant* `Coming Soon`\n" + "Stay on track with medication reminders and cost-saving insights."
      //   },
      //   "accessory": {
      //     "type": "image",
      //     "image_url": "https://qharmony-public.s3.amazonaws.com/pharmacy-assistant-side.png",
      //     "alt_text": "Windsor Court Hotel thumbnail"
      //   }
      // },
      // {
      //   "type": "section",
      //   "text": {
      //     "type": "mrkdwn",
      //     "text": "*Leave Assistant* `Coming Soon`\n" + "Streamline time-off requests and manage balances efficiently."
      //   },
      //   "accessory": {
      //     "type": "image",
      //     "image_url": "https://qharmony-public.s3.amazonaws.com/leave-assistant-side.png",
      //     "alt_text": "Windsor Court Hotel thumbnail"
      //   }
      // },
      // {
      //   "type": "section",
      //   "text": {
      //     "type": "mrkdwn",
      //     "text": "*Marketplace* `Coming Soon`\n" + "Connect with financial, telehealth, mental health services, and more!"
      //   },
      //   "accessory": {
      //     "type": "image",
      //     "image_url": "https://qharmony-public.s3.amazonaws.com/marketplace-side.png",
      //     "alt_text": "Windsor Court Hotel thumbnail"
      //   }
      // },
    );
  }

  if (pageNum === 2) {
    blocks.push(
      // {
      //   type: 'header',
      //   text: {
      //     type: 'plain_text',
      //     text: ':heavy_dollar_sign: Taxes',
      //     emoji: true,
      //   },
      // },
      // {
      //   type: 'divider',
      // },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: ':moneybag: Income',
          emoji: true,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'plain_text',
            text: `Amount (${incomeUnit})`,
            emoji: true,
          },
        ],
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*${incomeCurrency === 'usd' ? '$' : ''
            }${makeNumberHumanReadable(incomeAmount)} ${incomeCurrency.toUpperCase()}*`,
        },
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `Effective Date *${convertYyyyMmDdToMmDdYyyy(incomeEffectiveDate)}*`,
          },
        ],
      },
      {
        type: 'divider',
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: ':moneybag: Pay Stub',
          emoji: true,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'plain_text',
            text: 'Start Date',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${convertYyyyMmDdToMmDdYyyy(payStub.payPeriod.start_date)}*`,
          },
          {
            type: 'plain_text',
            text: 'End Date',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${convertYyyyMmDdToMmDdYyyy(payStub.payPeriod.end_date)}*`,
          },
          {
            type: 'plain_text',
            text: 'Debit Date',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${convertYyyyMmDdToMmDdYyyy(payStub.debitDate)}*`,
          },
          {
            type: 'plain_text',
            text: 'Gross Pay',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${payStub.grossPay.currency === 'usd' ? '$' : ''}${makeNumberHumanReadable(payStub.grossPay.amount)
              } ${payStub.grossPay.currency.toUpperCase()}*`,
          },
          {
            type: 'plain_text',
            text: 'Net Pay',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: `*${payStub.netPay.currency === 'usd' ? '$' : ''}${makeNumberHumanReadable(payStub.netPay.amount)
              } ${payStub.netPay.currency.toUpperCase()}*`,
          },
        ],
      },
      {
        "type": "rich_text",
        "elements": [
          {
            "type": "rich_text_quote",
            "elements": [
              {
                "type": "text",
                "text": "Taxes"
              }
            ]
          }
        ]
      },
      ...generateTaxJSON(taxes),
      {
        type: 'divider',
      },
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: ':money_with_wings: Payroll Deduction',
          emoji: true,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'plain_text',
            text: 'Employee Deduction',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: '*$6,823 USD* (_Fixed_)',
          },
          {
            type: 'plain_text',
            text: 'Company Contribution',
            emoji: true,
          },
          {
            type: 'mrkdwn',
            text: '*$14,296 USD* (_Fixed_)',
          },
        ],
      },
      {
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Pay History',
              emoji: true,
            },
            value: 'view_pay_history',
            action_id: 'view_pay_history',
          },
        ],
      },
    );
  }

  if (pageNum === 3) {
    blocks.push(
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: 'Your insurance coverage details, all in one place',
          emoji: true,
        },
      },
      ...generateBlocksForInsurances(benefits),
    );
  }

  if (pageNum === 4) {
    blocks.push(
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: 'All fringe benefits centralized for easy access',
          emoji: true,
        },
      },
      ...generateBlocksForBenefits(benefits),
    );
  }

  blocks.push(
    ...(showDisconnectButton
      ? [
        // {
        //   "type": "image",
        //   "image_url": "https://tmpfiles.org/dl/4613620/screenshot2024-04-03at1.10.43am.png",
        //   "alt_text": "delicious tacos"
        // },
        // {
        //   "type": "image",
        //   "image_url": "https://tmpfiles.org/dl/4613559/screenshot2024-04-03at1.05.46am.png",
        //   "alt_text": "delicious tacos"
        // },
        // {
        //   "type": "image",
        //   "image_url": "https://tmpfiles.org/dl/4613564/screenshot2024-04-03at1.06.33am.png",
        //   "alt_text": "delicious tacos"
        // },
        {
          type: 'divider',
        },
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: ':warning: Disconnect',
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: "Disconnecting will remove all your company's data from qHarmony. Are you sure you want to disconnect?",
          },
        },
        {
          type: 'actions',
          block_id: 'disconnect',
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Disconnect',
                emoji: true,
              },
              value: 'disconnect',
              action_id: 'disconnect',
              style: 'danger',
            },
          ],
        },
      ]
      : [])
  );

  return {
    type: 'home',
    blocks,
  };
};
