'use client';

import React, { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import {
  Fa<PERSON><PERSON><PERSON>,
  FaLinkedin,
  FaWhatsapp,
  FaSms,
  FaFacebook,
} from 'react-icons/fa';
import { FaSquareXTwitter } from "react-icons/fa6";

const ShareButtons = ({ title }: { title: string }) => {
  const pathname = usePathname();
  const fullUrl = `https://app.benosphere.com${pathname}`;
  const encodedURL = encodeURIComponent(fullUrl);
  const encodedTitle = encodeURIComponent(title);

  const [isMobile, setIsMobile] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);

  useEffect(() => {
    // More comprehensive device detection
    const checkDevice = () => {
      const ua = navigator.userAgent || navigator.vendor || (window as any).opera || '';
      
      // Check if mobile
      const mobileCheck = /android|iphone|ipad|ipod|blackberry|windows phone/i.test(ua);
      setIsMobile(mobileCheck);
      
      // Check specific platforms
      setIsIOS(/iphone|ipad|ipod/i.test(ua));
      setIsAndroid(/android/i.test(ua));
    };
    
    checkDevice();
  }, []);

  // Define the hover style
  const iconStyle = {
    transition: 'transform 0.3s ease, opacity 0.3s ease',
    cursor: 'pointer',
  };

  return (
    <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
      <a
        href={`https://twitter.com/intent/tweet?url=${encodedURL}&text=${encodedTitle}`}
        target="_blank"
        rel="noopener noreferrer"
        title="Share on X"
        style={{
          display: 'inline-block',
          transition: 'transform 0.3s ease',
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'scale(1.2)';
          e.currentTarget.style.opacity = '0.8';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.opacity = '1';
        }}
      >
        <FaSquareXTwitter size={24} color="#FFFFFF" />
      </a>

      <a
        href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodedURL}&text=${encodedTitle}`}
        target="_blank"
        rel="noopener noreferrer"
        title="Share on LinkedIn"
        style={{
          display: 'inline-block',
          transition: 'transform 0.3s ease',
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'scale(1.2)';
          e.currentTarget.style.opacity = '0.8';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.opacity = '1';
        }}
      >
        <FaLinkedin size={24} color="#0077B5" />
      </a>

      <a
        href={`https://api.whatsapp.com/send?text=${encodedTitle}%20${encodedURL}`}
        target="_blank"
        rel="noopener noreferrer"
        title="Share on WhatsApp"
        style={{
          display: 'inline-block',
          transition: 'transform 0.3s ease',
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'scale(1.2)';
          e.currentTarget.style.opacity = '0.8';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.opacity = '1';
        }}
      >
        <FaWhatsapp size={24} color="#25D366" />
      </a>

      {/* Show SMS only on mobile devices */}
      {isMobile && (
        <a 
          href={isIOS ? `sms:&body=${encodedTitle}%20${encodedURL}` : `sms:?body=${encodedTitle}%20${encodedURL}`} 
          title="Share via SMS"
          style={{
            display: 'inline-block',
            transition: 'transform 0.3s ease',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'scale(1.2)';
            e.currentTarget.style.opacity = '0.8';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.opacity = '1';
          }}
        >
          <FaSms size={24} color="#FFC107" />
        </a>
      )}

      {/* Show Facebook only on non-mobile or when SMS is not shown */}
      {(!isMobile || !isIOS && !isAndroid) && (
        <a
          href={`https://www.facebook.com/sharer/sharer.php?u=${encodedURL}`}
          target="_blank"
          rel="noopener noreferrer"
          title="Share on Facebook"
          style={{
            display: 'inline-block',
            transition: 'transform 0.3s ease',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'scale(1.2)';
            e.currentTarget.style.opacity = '0.8';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.opacity = '1';
          }}
        >
          <FaFacebook size={24} color="#1877F2"/>
        </a>
      )}
    </div>
  );
};

export default ShareButtons;
