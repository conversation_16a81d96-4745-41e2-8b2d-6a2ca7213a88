'use client';

import React, { useState } from 'react';
import { Play, Video, X } from 'lucide-react';

interface VideoPlayerProps {
  title: string;
  description: string;
  thumbnailUrl?: string;
  videoUrl?: string;
  planType: 'medical' | 'dental' | 'vision' | 'summary';
  onClose?: () => void;
}

export const VideoPlayer = ({ title, description, thumbnailUrl, videoUrl, planType, onClose }: VideoPlayerProps) => {
  const [isOpen, setIsOpen] = useState(true); // Start open when component is rendered

  const handleClose = () => {
    setIsOpen(false);
    if (onClose) {
      onClose();
    }
  };

  // Mock video content for demo purposes
  const getVideoContent = () => {
    switch (planType) {
      case 'medical':
        return {
          title: "Understanding Medical Plans: PPO vs HMO",
          description: "Learn the key differences between PPO and HMO plans, including costs, flexibility, and how to choose the right one for your needs.",
          duration: "3:24"
        };
      case 'dental':
        return {
          title: "Dental Benefits Explained",
          description: "Discover how dental insurance works, what's covered, and how to maximize your benefits for routine and major dental work.",
          duration: "2:45"
        };
      case 'vision':
        return {
          title: "Vision Benefits Overview",
          description: "Learn about vision coverage for eye exams, glasses, contacts, and how to use your benefits effectively.",
          duration: "2:15"
        };
      case 'summary':
        return {
          title: "Benefits Summary Overview",
          description: "Review your benefits enrollment summary and understand your next steps in the enrollment process.",
          duration: "2:30"
        };
      default:
        return { title, description, duration: "3:00" };
    }
  };

  const videoContent = getVideoContent();

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '8px 16px',
          color: '#374151',
          border: '1px solid #d1d5db',
          borderRadius: '8px',
          backgroundColor: 'white',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease',
          fontSize: '14px',
          fontWeight: '500',
          fontFamily: 'sans-serif'
        }}
        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
        onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
      >
        <Play size={16} style={{ color: '#6b7280' }} />
        Watch Video
      </button>
    );
  }

  return (
    <>
      {/* Backdrop */}
      <div
        style={{
          position: 'fixed',
          inset: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 50
        }}
        onClick={handleClose}
      />

      {/* Modal */}
      <div style={{
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '90vw',
        maxWidth: '900px',
        backgroundColor: 'white',
        borderRadius: '16px',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
        zIndex: 51,
        overflow: 'hidden'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '20px 24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Video size={20} style={{ color: '#2563eb' }} />
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#111827',
              margin: 0,
              fontFamily: 'sans-serif'
            }}>
              {videoContent.title}
            </h2>
          </div>
          <button
            onClick={handleClose}
            style={{
              padding: '8px',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div style={{ padding: '24px' }}>
          {/* YouTube Video Player */}
          <div style={{
            aspectRatio: '16/9',
            borderRadius: '8px',
            overflow: 'hidden',
            marginBottom: '16px'
          }}>
            <iframe
              width="100%"
              height="100%"
              src="https://www.youtube.com/embed/MPN66L_skBw"
              title="Benefits Enrollment Video"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
              style={{
                border: 'none',
                borderRadius: '8px'
              }}
            />
          </div>
          
          {/* Video Description */}
          <div style={{
            backgroundColor: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            padding: '16px'
          }}>
            <h4 style={{
              fontSize: '14px',
              fontWeight: '600',
              color: '#111827',
              margin: '0 0 8px 0',
              fontFamily: 'sans-serif'
            }}>
              What you&apos;ll learn:
            </h4>
            <p style={{
              fontSize: '14px',
              color: '#6b7280',
              margin: 0,
              lineHeight: '1.5',
              fontFamily: 'sans-serif'
            }}>
              {videoContent.description}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};
