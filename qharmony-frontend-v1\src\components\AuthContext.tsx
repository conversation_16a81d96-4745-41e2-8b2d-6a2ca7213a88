"use client";
import * as microsoftTeams from "@microsoft/teams-js";
import React, { createContext, useContext, useEffect, useState } from "react";
import { auth } from "../utils/firebase";
import {
  onAuthStateChanged,
  User as FirebaseUser,
  signOut,
} from "firebase/auth";
import msalInstance from "../app/teamsauth/authconfig"; // Import your MSAL instance
import { AccountInfo } from "@azure/msal-browser";
import { teamsSelfOnboard } from "@/middleware/user_middleware";
import { useRouter } from "next/navigation";

interface AuthContextType {
  user: FirebaseUser | string | null;
  loading: boolean;
  logout: () => void;
  setUser: React.Dispatch<React.SetStateAction<FirebaseUser | string | null>>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<FirebaseUser | string | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Function to clear all localStorage items
  const clearLocalStorage = () => {
    // Get all keys in localStorage
    const allKeys = Object.keys(localStorage);
    
    // Clear all items
    allKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    // For extra certainty, explicitly remove known keys
    
    localStorage.removeItem("userid1");
    localStorage.removeItem("userEmail1");
    localStorage.removeItem("isTeamsApp1");
    localStorage.removeItem("companyId1");
    localStorage.removeItem("firstTimeLogin1");
    localStorage.removeItem("wellness_results");
    localStorage.removeItem("wellness_user_answers");
    
    console.log("All localStorage items cleared");
  };

  // Function to clear cookies
  const clearCookies = () => {
    // Get all cookies
    const cookies = document.cookie.split(";");
    
    // Clear each cookie by setting expiration in the past
    cookies.forEach(cookie => {
      const cookieParts = cookie.split("=");
      const cookieName = cookieParts[0].trim();
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });
    
    console.log("All cookies cleared");
  };

  // Enhanced logout function that clears localStorage and cookies
  const logout = () => {
    signOut(auth)
      .then(() => {
        console.log("Firebase user signed out");
        setUser(null);
        clearLocalStorage();
        clearCookies();
        router.push("/login");
      })
      .catch((error) => {
        console.error("Error signing out: ", error);
      });

    msalInstance.logoutRedirect().catch((error) => {
      console.error("Error signing out from Microsoft: ", error);
    });
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser) {
        console.log("Firebase user exists:", firebaseUser);
        setUser(firebaseUser);
      } else {
        const isSSODone = localStorage.getItem("ssoDone1");
        const userId = localStorage.getItem("userid1");
        console.log("isSSODone", isSSODone);
        console.log("userid1", userId);

        microsoftTeams.app.initialize().then(async () => {
          let currentUserTeamsContext = await microsoftTeams.app.getContext();
          console.log(
            "Current user teams context:",
            currentUserTeamsContext.user?.loginHint,
          );

          let userEmail = currentUserTeamsContext.user?.loginHint;
          let tenantId = currentUserTeamsContext.user?.tenant?.id;
          localStorage.setItem("userEmail1", userEmail as string);

          localStorage.setItem("isTeamsApp1", "true");

          const userIdObj = await teamsSelfOnboard(
            userEmail as string,
            tenantId,
          );
          if (userIdObj.data === "login_user") {
            console.log("Onboarding successful:", userIdObj);
            const userId = userIdObj.userId;
            const companyId = userIdObj.companyId;
            localStorage.setItem("userid1", userId);
            localStorage.setItem("companyId1", companyId);
            localStorage.setItem("ssoDone1", "true");

            setUser(currentUserTeamsContext.user?.loginHint as string);

            router.push("/dashboard");
          } else {
            router.push("/teams-landing");
          }
        });
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  return (
    <AuthContext.Provider value={{ user, loading, logout, setUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
