import React, { useEffect, useState } from "react";
import { Box, Typography, Avatar, Button } from "@mui/material";
import chatBotIcon from "../../../public/ai_assistant.png";
import Image from "next/image";
import { setSelectedFAQQuestion } from "@/redux/reducers/userSlice";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";

const sections1 = [
  {
    label: "What benefits are available to me?",
    icon: "🔥",
    description: "Top question asked",
  },
  {
    label: "How do I enroll in benefits?",
    icon: "🧙‍♂️",
  },
  {
    label: "What benefits are available to me?",
    icon: "🔥",
    description: "Top question asked",
  },
  {
    label: "How do I enroll in benefits?",
    icon: "🧙‍♂️",
  },
  {
    label: "What benefits are available to me?",
    icon: "🔥",
    description: "Top question asked",
  },
  {
    label: "How do I enroll in benefits?",
    icon: "🧙‍♂️",
  },
  {
    label: "What benefits are available to me?",
    icon: "🔥",
    description: "Top question asked",
  },
  {
    label: "How do I enroll in benefits?",
    icon: "🧙‍♂️",
  },
  {
    label: "What benefits are available to me?",
    icon: "🔥",
    description: "Top question asked",
  },
  {
    label: "How do I enroll in benefits?",
    icon: "🧙‍♂️",
  },
];

const sections2 = [
  {
    label: "What is covered under my healthcare insurance plan?",
    icon: "🏝️",
    description: "Top question asked",
  },
  {
    label: "How can I apply for the family medical leave act (FMLA)?",
    icon: "📄",
  },
  {
    label: "What is covered under my healthcare insurance plan?",
    icon: "🏝️",
    description: "Top question asked",
  },
  {
    label: "How can I apply for the family medical leave act (FMLA)?",
    icon: "📄",
  },
  {
    label: "What is covered under my healthcare insurance plan?",
    icon: "🏝️",
    description: "Top question asked",
  },
  {
    label: "How can I apply for the family medical leave act (FMLA)?",
    icon: "📄",
  },
  {
    label: "What is covered under my healthcare insurance plan?",
    icon: "🏝️",
    description: "Top question asked",
  },
  {
    label: "How can I apply for the family medical leave act (FMLA)?",
    icon: "📄",
  },
  {
    label: "What is covered under my healthcare insurance plan?",
    icon: "🏝️",
    description: "Top question asked",
  },
  {
    label: "How can I apply for the family medical leave act (FMLA)?",
    icon: "📄",
  },
];

// Keyframe animation for seamless scrolling
const scrollingAnimation = {
  "@keyframes scrollRight": {
    "0%": { transform: "translateX(0)" },
    "100%": { transform: "translateX(-100%)" }, // Scroll left for the first box
  },
  "@keyframes scrollLeft": {
    "0%": { transform: "translateX(-100%)" }, // Start with the last item visible
    "100%": { transform: "translateX(0)" }, // Scroll right for the second box
  },
};

export default function FAQHelpSection() {
  const dispatch = useDispatch();
  const router = useRouter();

  const [message, setMessage] = useState("");

  const [isTeamsApp, setIsTeamsApp] = useState(false);

  useEffect(() => {
    const isTeamsApp = localStorage.getItem("isTeamsApp1");
    setIsTeamsApp(isTeamsApp === "true");
  }, []);

  const handleInputChange = (event: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setMessage(event.target.value);
  };

  const handleSendMessage = () => {
    if (message.trim()) {
      handleFAQClick(message);
      setMessage(""); // Clear the input after sending
    }
  };

  const handleFAQClick = (question: string) => {
    dispatch(setSelectedFAQQuestion(question));
    router.push("/qHarmonyBot");
  };

  return (
    <Box
      sx={{
        backgroundColor: "#ffffff",
        paddingY: 4,
        mt: 0,
        borderRadius: "30px",
      }}
    >
      {/* Header with Avatar */}
      {!isTeamsApp && (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 3,
            paddingX: 3,
          }}
        >
          <Avatar
            sx={{
              width: 60,
              height: 60,
              mr: 2,
              overflow: "hidden", // Ensure the image doesn't overflow the avatar
            }}
          >
            <Image
              src={chatBotIcon}
              alt="Chat Avatar"
              layout="fill" // Make the image fill the container
              objectFit="cover" // Ensure the image covers the container without distortion
            />
          </Avatar>
          <Box>
            <Typography sx={{ fontWeight: 800, fontSize: "28px" }}>
              Chat with Brea – Your benefits specialist
            </Typography>
            <Typography
              sx={{
                fontWeight: 500,
                fontSize: "14px",
                color: "rgba(0, 0, 0, 0.6)",
              }}
            >
              24/7 available
            </Typography>
          </Box>
        </Box>
      )}

      {/* Scrolling FAQ Section */}
      <Box sx={{ overflow: "hidden", position: "relative" }}>
        {/* First scroll box (scrolls right to left) */}
        <Box
          sx={{
            display: "flex",
            whiteSpace: "nowrap",
            animation: "scrollRight 30s linear infinite", // Seamless scrolling
            ...scrollingAnimation,
            marginBottom: 2,
          }}
        >
          {sections1.concat(sections1).map((item, index) => (
            <Box
              key={index}
              onClick={() => handleFAQClick(item.label)}
              sx={{
                display: "flex",
                alignItems: "center",
                px: 2,
                py: 1,
                mx: 1,
                borderRadius: "12px",
                bgcolor: "#F6F6F6",
                minWidth: "300px",
                cursor: "pointer",
                flexShrink: 0, // Prevent shrinking
                overflow: "hidden", // Ensure text stays inside
                textOverflow: "ellipsis", // Add ellipsis if text is too long
                "&:hover": {
                  backgroundColor: "#e9ecef",
                },
              }}
            >
              <Typography
                variant="body1"
                sx={{ fontSize: "40px", marginRight: "10px" }}
              >
                {item.icon}
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: 800,
                    fontSize: "16px",
                    whiteSpace: "nowrap", // Prevent line breaks
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                  }}
                >
                  {item.label}
                </Typography>
                {item.description && (
                  <Typography
                    variant="caption"
                    sx={{
                      fontWeight: 500,
                      color: "#6c757d",
                      fontSize: "13px",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    {item.description}
                  </Typography>
                )}
              </Box>
            </Box>
          ))}
        </Box>

        {/* Second scroll box (scrolls left to right) */}
        <Box
          sx={{
            display: "flex",
            whiteSpace: "nowrap",
            animation: "scrollLeft 30s linear infinite", // Seamless reverse scrolling
            ...scrollingAnimation,
          }}
        >
          {/* Duplicate the sections array to create a seamless loop */}
          {sections2.concat(sections2).map((item, index) => (
            <Box
              key={index}
              onClick={() => handleFAQClick(item.label)}
              sx={{
                display: "flex",
                alignItems: "center",
                px: 2,
                py: 1,
                mx: 1,
                borderRadius: "12px",
                bgcolor: "#F6F6F6",
                minWidth: "300px",
                cursor: "pointer",
                flexShrink: 0, // Prevent shrinking
                overflow: "hidden", // Ensure text stays inside
                textOverflow: "ellipsis", // Add ellipsis if text is too long
                "&:hover": {
                  backgroundColor: "#e9ecef",
                },
              }}
            >
              <Typography
                variant="body1"
                sx={{ fontSize: "40px", marginRight: "10px" }}
              >
                {item.icon}
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: 800,
                    fontSize: "16px",
                    whiteSpace: "nowrap", // Prevent line breaks
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                  }}
                >
                  {item.label}
                </Typography>
                {item.description && (
                  <Typography
                    variant="caption"
                    sx={{
                      fontWeight: 500,
                      color: "#6c757d",
                      fontSize: "13px",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    {item.description}
                  </Typography>
                )}
              </Box>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Chat Box */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mt: 4,
          bgcolor: "#f1f3f5",
          mx: 3,
          px: 2,
          py: 1,
          borderRadius: "100px",
        }}
      >
        <input
          type="text"
          value={message}
          onChange={handleInputChange}
          placeholder="How can I assist you?"
          style={{
            flex: 1,
            border: "none",
            outline: "none",
            backgroundColor: "transparent",
            color: "black",
            fontSize: "16px",
            marginRight: "10px",
          }}
        />
        <Button
          variant="contained"
          onClick={handleSendMessage}
          sx={{
            bgcolor: "#000000",
            color: "#ffffff",
            borderRadius: "100px",
            textTransform: "none",
            px: 3,
          }}
        >
          Send
        </Button>
      </Box>
    </Box>
  );
}
