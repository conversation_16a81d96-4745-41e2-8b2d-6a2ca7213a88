'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const CreatePlanOptimizer = () => {
  const router = useRouter();

  useEffect(() => {
    // Preload critical routes for create-plan workflow
    const preloadRoutes = [
      '/ai-enroller',
      '/ai-enroller/plans',
      '/ai-enroller/manage-groups'
    ];

    // Preload routes with a small delay
    const preloadTimer = setTimeout(() => {
      preloadRoutes.forEach(route => {
        router.prefetch(route);
      });
    }, 200);

    // Preload critical images
    const preloadImages = ['/brea.png'];
    preloadImages.forEach(src => {
      const img = new Image();
      img.src = src;
    });

    // Preload form validation scripts
    const preloadValidation = () => {
      // Pre-compile regex patterns for form validation
      const patterns = {
        planCode: /^[A-Z0-9-_]{3,20}$/i,
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        url: /^https?:\/\/.+/
      };
      
      // Store patterns for quick access
      (window as any).formPatterns = patterns;
    };

    preloadValidation();

    // Optimize localStorage operations
    const optimizeStorage = () => {
      try {
        // Pre-check localStorage availability
        const testKey = 'ai-enroller-test';
        localStorage.setItem(testKey, 'test');
        localStorage.removeItem(testKey);
        
        // Mark localStorage as available
        (window as any).storageAvailable = true;
      } catch (e) {
        (window as any).storageAvailable = false;
        console.warn('localStorage not available');
      }
    };

    optimizeStorage();

    return () => {
      clearTimeout(preloadTimer);
    };
  }, [router]);

  return null;
};

export default CreatePlanOptimizer;
