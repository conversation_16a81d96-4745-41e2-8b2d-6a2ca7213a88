'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import {
  HiOutlineArrowLeft,
  HiOutlineHeart,
  HiOutlineClipboard,
  HiOutlineEye,
  HiOutlineSparkles,
  HiOutlinePlus
} from 'react-icons/hi';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import '../manage-plans.css';

interface Plan {
  _id: string;
  planName: string;
  planCode?: string;
  coverageType: string;
  coverageSubTypes: string[];
  planType: string;
  metalTier?: string;
  carrierId?: string;
  carrier?: {
    carrierName: string;
  };
  description?: string;
  highlights?: string[];
}

const AddPlanPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const companyId = params.companyId as string;
  const category = searchParams.get('category') || 'all';

  const [availablePlans, setAvailablePlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const managedCompanies = useSelector((state: RootState) => state.user.managedCompanies);
  const company = managedCompanies?.find(c => c._id === companyId);

  const fetchAvailablePlans = async () => {
    try {
      setLoading(true);
      setError(null);

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const getUserId = () => localStorage.getItem('userid1') || localStorage.getItem('userId') || '6838677aef6db0212bcfdacd';
      const brokerId = '6838677aef6db0212bcfdacd'; // From your backend configuration

      const response = await fetch(
        `${API_BASE_URL}/api/plans/broker/${brokerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'user-id': getUserId(),
          },
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log('Available plans result:', result);
        
        let plans = result.plans || [];
        
        // Filter by category if specified
        if (category !== 'all') {
          plans = plans.filter((plan: Plan) => {
            const coverageType = plan.coverageType?.toLowerCase() || '';
            switch (category.toLowerCase()) {
              case 'medical':
                return coverageType.includes('health') || coverageType.includes('medical');
              case 'dental':
                return coverageType.includes('dental');
              case 'vision':
                return coverageType.includes('vision');
              case 'ancillary':
                return !coverageType.includes('health') && 
                       !coverageType.includes('medical') && 
                       !coverageType.includes('dental') && 
                       !coverageType.includes('vision');
              default:
                return true;
            }
          });
        }
        
        setAvailablePlans(plans);
      } else {
        throw new Error('Failed to fetch plans');
      }
    } catch (error) {
      console.error('Error fetching available plans:', error);
      setError('Failed to load available plans');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAvailablePlans();
  }, [category]); // Only re-fetch when category changes

  const handleBack = () => {
    router.push(`/ai-enroller/manage-groups/${companyId}/manage-plans`);
  };

  const handleSelectPlan = (plan: Plan) => {
    // Navigate to plan configuration page
    router.push(`/ai-enroller/manage-groups/${companyId}/manage-plans/configure-plan?planId=${plan._id}`);
  };

  const getPlanIcon = (coverageType: string) => {
    switch (coverageType.toLowerCase()) {
      case 'your health':
      case 'medical':
        return <HiOutlineHeart className="plan-icon medical" />;
      case 'dental':
        return <HiOutlineClipboard className="plan-icon dental" />;
      case 'vision':
        return <HiOutlineEye className="plan-icon vision" />;
      case 'ancillary':
      default:
        return <HiOutlineSparkles className="plan-icon ancillary" />;
    }
  };

  if (loading) {
    return (
      <div className="manage-plans-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading available plans...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="manage-plans-page">
        <div className="error-container">
          <p>{error}</p>
          <button onClick={handleBack} className="back-button">
            <HiOutlineArrowLeft size={20} />
            Back to Plans
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="manage-plans-page">
      {/* Page Header */}
      <div className="page-header">
        <button onClick={handleBack} className="back-button">
          <HiOutlineArrowLeft size={20} />
          Back to Plans
        </button>
        <div className="header-content">
          <h1>Add {category.charAt(0).toUpperCase() + category.slice(1)} Plan</h1>
          <p>Select a plan to add to {company?.companyName || 'this company'}</p>
        </div>
      </div>

      {/* Available Plans */}
      <div className="plan-category">
        <div className="category-header">
          <h2>Available Plans</h2>
          <span className="plan-count">{availablePlans.length} plan{availablePlans.length !== 1 ? 's' : ''} available</span>
        </div>

        {availablePlans.length === 0 ? (
          <div className="no-plans">
            <HiOutlineSparkles size={48} />
            <h3>No plans available</h3>
            <p>There are no {category} plans available for assignment at this time.</p>
          </div>
        ) : (
          <div className="plans-grid">
            {availablePlans.map((plan) => (
              <div 
                key={plan._id} 
                className="plan-card selectable"
                onClick={() => handleSelectPlan(plan)}
              >
                <div className="plan-header">
                  <div className="plan-icon-wrapper">
                    {getPlanIcon(plan.coverageType)}
                  </div>
                  <div className="add-indicator">
                    <HiOutlinePlus size={16} />
                  </div>
                </div>

                <div className="plan-content">
                  <h3>{plan.planName}</h3>
                  <div className="plan-carrier">{plan.carrier?.carrierName || 'Unknown Carrier'}</div>
                  <div className="plan-type">{plan.planType} {plan.metalTier && `• ${plan.metalTier}`}</div>
                  
                  {plan.description && (
                    <p className="plan-description">{plan.description}</p>
                  )}

                  {plan.planCode && (
                    <div className="plan-code">
                      <span className="code-label">Plan Code:</span>
                      <span className="code-value">{plan.planCode}</span>
                    </div>
                  )}

                  {plan.highlights && plan.highlights.length > 0 && (
                    <div className="plan-highlights">
                      {plan.highlights.slice(0, 3).map((highlight, index) => (
                        <div key={index} className="highlight-item">
                          <span>• {highlight}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="plan-actions">
                  <button className="select-plan-btn">
                    <HiOutlinePlus size={16} />
                    Add Plan
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AddPlanPage;
