"use client";

import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { getCompanyBenefitTypesWithBenefits, maskBenefitCategory, maskedSubCategory } from "@/middleware/company_middleware";
import {
  Switch,
  FormControlLabel,
  Typography,
  Box,
  Button,
  Grid,
  CircularProgress,
  Chip,
  Divider,
} from "@mui/material";
import { useRouter } from "next/navigation";
import {
  getBenefitsForType,
  toggleBenefitActivation,
} from "@/middleware/benefits_middleware";
import { useSelector } from "react-redux";
import { getUsersCompanyId } from "@/redux/reducers/userSlice";
import theme from "../../theme";
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";

const ManageBenefitsDisplay = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));

  const benefitsPerType = useAppSelector(
    (state: RootState) => state.benefits.benefitsPerType,
  );
  const [loadingBenefits, setLoadingBenefits] = useState<{
    [key: string]: boolean;
  }>({});

  useEffect(() => {
    if (companyId && companyId !== "") {
      getCompanyBenefitTypesWithBenefits(dispatch, companyId);
    }
  }, [dispatch, companyId]);

  const handleToggle = async (
    benefitId: string,
    benefitType: string,
    isActivated: boolean,
  ) => {
    setLoadingBenefits((prev) => ({ ...prev, [benefitId]: true }));
    try {
      const success = await toggleBenefitActivation(
        dispatch,
        companyId,
        benefitId,
        !isActivated,
        benefitType,
      );
      if (!success) {
        console.error("Failed to update benefit activation");
      } else {
        getCompanyBenefitTypesWithBenefits(dispatch, companyId);
      }
    } catch (error) {
      console.error("Failed to update benefit activation:", error);
    } finally {
      setLoadingBenefits((prev) => ({ ...prev, [benefitId]: false }));
    }
  };

  const handleEdit = (benefitId: string) => {
    router.push(`/editBenefit/${benefitId}`);
  };

  const renderStatusChip = (isActivated: boolean) => {
    return isActivated ? (
      <Chip
        label="Available"
        sx={{
          bgcolor: "#67BA6B1F",
          color: "#67BA6B",
          borderRadius: "8px",
          "& .MuiChip-label": {
            padding: 1, // Apply padding override to the label
            fontWeight: "semibold",
            fontSize: "14px", // Modified font size
          },
        }} // Modified radius
      />
    ) : (
      <Chip
        label="Disabled"
        sx={{
          bgcolor: "#f0f0f0",
          color: "black",
          borderRadius: "8px",
          "& .MuiChip-label": {
            padding: 1, // Apply padding override to the label
            fontWeight: "semibold",
            fontSize: "14px", // Modified font size
          },
        }} // Modified radius
      />
    );
  };

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6FA",
          p: 4,
          width: "100%",
          height: "95vh",
          overflow: "auto",
        }}
      >
        {benefitsPerType.map((benefitTypeGroup) => (
          <Box key={benefitTypeGroup.benefitType} sx={{ mb: 4 }}>
            <Typography
              sx={{
                fontWeight: "500",
                fontSize: "28px",
                lineHeight: "20.8px",
                color: "black",
                textAlign: "left", // Align text to the left
                marginBottom: 4,
                marginTop: 5,
              }}
            >
              {maskBenefitCategory(benefitTypeGroup.benefitType)}
            </Typography>

            {/* Table Headers */}
            <Grid container sx={{ mb: 2, px: 2 }}>
              <Grid item xs={4}>
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 500, color: "#939496" }}
                >
                  TYPE
                </Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 500, color: "#939496" }}
                >
                  STATUS
                </Typography>
              </Grid>
              <Grid
                item
                xs={4}
                sx={{ display: "flex", justifyContent: "flex-end", pr: 14 }} // Adjusted padding
              >
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 500, color: "#939496" }}
                >
                  ACTION
                </Typography>
              </Grid>
            </Grid>

            {/* White background for benefit rows */}
            <Box
              sx={{
                bgcolor: "#ffffff",
                py: 1,
                px: 3,
                borderRadius: "12px",
                marginBottom: 9,
              }}
            >
              {/* Benefit Rows */}
              {benefitTypeGroup.benefits.map((benefit, index) => (
                <div key={benefit._id}>
                  <Grid
                    container
                    alignItems="center"
                    sx={{ py: 1 }}
                    key={benefit._id}
                  >
                    <Grid item xs={4}>
                      <Typography
                        sx={{
                          fontWeight: "500",
                          fontSize: "17px",
                          lineHeight: "20.8px",
                          color: "black",
                          textAlign: "left",
                        }}
                      >
                        {maskedSubCategory(benefit.subType)}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      {renderStatusChip(benefit.isActivated)}
                    </Grid>
                    <Grid
                      item
                      xs={4}
                      sx={{
                        display: "flex",
                        justifyContent: "flex-end", // Align both items to the right
                        alignItems: "center",
                        gap: 2, // Add space between Edit button and Switch
                      }}
                    >
                      <Button
                        variant="contained"
                        onClick={() => handleEdit(benefit._id)}
                        sx={{
                          backgroundColor: "#f0f0f0", // Light gray background
                          borderRadius: "8px", // Rounded corners
                          textTransform: "none", // No uppercase transformation
                          color: "#000", // Black text color
                          padding: "4px", // Similar padding to the chip
                          marginRight: 3,
                          boxShadow: "none", // Remove the default button shadow
                          border: "none", // No border
                          "&:hover": {
                            backgroundColor: "#E0E0E0", // Slightly darker on hover
                            boxShadow: "none", // Ensure no shadow on hover
                          },
                        }}
                      >
                        Edit
                      </Button>

                      <FormControlLabel
                        control={
                          <Switch
                            checked={benefit.isActivated}
                            onChange={() =>
                              handleToggle(
                                benefit._id,
                                benefitTypeGroup.benefitType,
                                benefit.isActivated,
                              )
                            }
                            disabled={loadingBenefits[benefit._id]}
                            sx={{
                              "& .MuiSwitch-switchBase.Mui-checked": {
                                color: "#11C12D",
                              },
                              "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                              {
                                backgroundColor: "#11C12D",
                              },
                            }}
                          />
                        }
                        label={
                          loadingBenefits[benefit._id] ? (
                            <CircularProgress size={20} />
                          ) : (
                            ""
                          )
                        }
                      />
                    </Grid>
                  </Grid>
                  {index < benefitTypeGroup.benefits.length - 1 && (
                    <Divider sx={{ bgcolor: "black", my: 1, opacity: 0.05 }} />
                  )}
                </div>
              ))}
            </Box>
          </Box>
        ))}
      </Box>
    </ProtectedRoute>
  );
};

export default withSidebar(ManageBenefitsDisplay);
