from pydantic import BaseModel
from typing import Optional,Union,Dict,List,Any

class Question(BaseModel):
    id: str
    text: str
    type: str
    options: Optional[List[str]] = None
    feature: Optional[str] = None
    weights: Optional[Union[float, Dict[str, float]]] = None

# Model for the full questions structure
class QuestionsResponse(BaseModel):
    questions: List[Question]

# Model for user answers (flexible to handle various question types)
class UserAnswer(BaseModel):
    answers: Dict[str, Any]  # Using Any to allow flexibility (str, int, float, etc.)

# Model for life expectancy prediction output
class LifeExpectancyPrediction(BaseModel):
    predicted_baseline_age: float
    additional_adjustment: float
    final_adjusted_age: float
    survival_probability_past_100: float
    message: str
    
class WellnessRequestData(BaseModel):
    user_answer: UserAnswer
    user_id: str # Or appropriate type
    team_id: str # Or appropriate type
