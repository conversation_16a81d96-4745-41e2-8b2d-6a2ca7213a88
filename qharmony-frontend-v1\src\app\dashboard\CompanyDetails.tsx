import {
  Box,
  Typography,
  Grid,
  Paper,
  IconButton,
  CircularProgress,
} from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { useRef, useState } from "react";
import EditIcon from "@mui/icons-material/Edit";
import { updateCompanyLogo } from "@/middleware/benefits_middleware";
import Image from "next/image";

const CompanyDetails = () => {
  const dispatch = useAppDispatch();
  const companyDetails = useAppSelector(
    (state: RootState) => state.company.companyDetails,
  );
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);

  const handleEditLogoClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLoading(true);
      updateCompanyLogo(dispatch, file).finally(() => {
        setLoading(false);
      });
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        mt: 3,
      }}
    >
      {/* Company Information Section */}
      <Paper
        sx={{
          display: "flex",
          flexDirection: "column", // Change to column to stack elements
          alignItems: "flex-start",
          justifyContent: "space-between",
          width: "100%",
          boxShadow: "none",
          borderRadius: "30px",
          bgcolor: "#ffffff",
          paddingBottom: "12px",
          paddingX: "12px",
        }}
      >
        {/* Company Name, Industry, and Logo/Edit Button in a Row */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            width: "100%",
            mb: 2,
            paddingTop: "25px",
            paddingX: "12px",
          }}
        >
          <Box>
            <Typography
              variant="h5"
              sx={{ fontWeight: "bold", mb: 0.5, fontSize: "24px" }}
            >
              {companyDetails.name || "N/A"}
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: "#6c757d", fontSize: "16px" }}
            >
              {companyDetails.industry || "N/A"}
            </Typography>
          </Box>
          <Box sx={{ position: "relative", ml: 2 }}>
            <Box
              onClick={handleEditLogoClick}
              sx={{
                width: 100,
                height: 100,
                borderRadius: "12px",
                overflow: "hidden",
                position: "relative",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                bgcolor: "#f4f4f4",
                cursor: "pointer", // Add this line to change cursor on hover
              }}
            >
              {companyDetails.details?.logo ? (
                <Image
                  src={companyDetails.details.logo}
                  alt="Company Logo"
                  layout="fill"
                  objectFit="contain"
                />
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                    color: "#9e9e9e",
                  }}
                >
                  <CloudUploadIcon fontSize="large" />
                  <Typography
                    variant="caption"
                    sx={{ mb: 0.5, fontWeight: 600 }}
                  >
                    Logo
                  </Typography>
                </Box>
              )}
            </Box>
            <IconButton
              onClick={handleEditLogoClick}
              sx={{
                position: "absolute",
                top: 0,
                right: 0,
                bgcolor: "#000000",
                color: "#ffffff",
                borderRadius: "50%",
                width: "24px",
                height: "24px",
                p: "2px",
              }}
            >
              {loading ? (
                <CircularProgress size={16} />
              ) : (
                <EditIcon sx={{ fontSize: "16px" }} />
              )}
            </IconButton>
          </Box>
        </Box>

        <Paper
          sx={{
            borderRadius: "30px",
            p: 2.5,
            boxShadow: "none",
            bgcolor: "rgba(245, 245, 245, 0.7)",
            width: "100%", // Use full width to avoid cutting text
            mt: 2, // Add margin-top for spacing
          }}
        >
          <Grid container spacing={2}>
            {/* Company Size */}
            <Grid item xs={6}>
              <Typography
                variant="body2"
                sx={{ color: "#6c757d", fontSize: "14px", textAlign: "left" }}
              >
                Company Size
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography
                variant="body2"
                sx={{
                  fontSize: "14px",
                  textAlign: "right",
                  fontWeight: 600,
                  wordBreak: "break-word", // Ensures long text breaks properly
                  overflowWrap: "break-word", // Ensures text stays within its container
                }}
              >
                {companyDetails.companySize || "2"}
              </Typography>
            </Grid>

            {/* Website */}
            <Grid item xs={6}>
              <Typography
                variant="body2"
                sx={{
                  color: "#6c757d",
                  fontSize: "14px",
                  textAlign: "left",
                  wordBreak: "break-word",
                  overflowWrap: "break-word",
                }}
              >
                Website
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography
                variant="body2"
                sx={{
                  fontSize: "14px",
                  textAlign: "right",
                  fontWeight: 600,
                  wordBreak: "break-word",
                  overflowWrap: "break-word",
                }}
              >
                {companyDetails.website ? (
                  <a
                    href={`http://${companyDetails.website}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ textDecoration: "none", color: "#000" }}
                  >
                    {companyDetails.website}
                  </a>
                ) : (
                  "BenOsphere.com"
                )}
              </Typography>
            </Grid>

            {/* Admin Email */}
            <Grid item xs={6}>
              <Typography
                variant="body2"
                sx={{
                  color: "#6c757d",
                  fontSize: "14px",
                  textAlign: "left",
                  wordBreak: "break-word",
                  overflowWrap: "break-word",
                }}
              >
                Admin Email
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography
                variant="body2"
                sx={{
                  fontSize: "14px",
                  textAlign: "right",
                  fontWeight: 600,
                  wordBreak: "break-word",
                  overflowWrap: "break-word",
                }}
              >
                {companyDetails.adminEmail || "<EMAIL>"}
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        <input
          type="file"
          accept=".png, .jpg"
          ref={fileInputRef}
          style={{ display: "none" }}
          onChange={handleFileChange}
        />
      </Paper>
    </Box>
  );
};

export default CompanyDetails;
