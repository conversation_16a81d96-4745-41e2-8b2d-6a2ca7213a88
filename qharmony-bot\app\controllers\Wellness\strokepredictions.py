
import pandas as pd
from scipy.stats import norm
from .dataModels import Question,QuestionsResponse,UserAnswer,LifeExpectancyPrediction
from .helpers import preprocess_dataframe, calculate_bmi
import math

def get_stroke_prediction(user_answers: UserAnswer, preprocessors, model, training_columns):
    """
    Takes user answers, prepares DataFrame, preprocesses, and predicts stroke probability.

    Args:
        user_answers (dict): Dictionary of user responses.
        col_types (dict): Dictionary defining column types for preprocessing.
        preprocessors (dict): The FITTED preprocessor objects (loaded).
        model: The FITTED stroke prediction model (loaded).

    Returns:
        float: Predicted probability of stroke, or None if an error occurs.
    """
    try:
        # 1. Prepare Input Dictionary with Correct Column Names
        input_data = {}
        result = {}
        col_types={
                'yes_or_no': ['ever_married'],
                'numeric': ['Age', 'hypertension', 'avg_glucose_level', 'BMI'],
                'nominal': ['Sex', 'work_type', 'Residence_type', 'Smoking'],
                'ordinal': []
            }
        user_answers= user_answers.model_dump()['answers']
        # Calculate BMI
        raw_bmi = user_answers.get("bmi")
        height = user_answers.get("height")
        weight = user_answers.get("weight")
        final_bmi = calculate_bmi(height, weight)
        if final_bmi is None or math.isnan(final_bmi) or math.isinf(final_bmi):
            print("Error: Valid BMI could not be determined.")
            return None
        input_data['BMI'] = final_bmi

        # Direct mapping for numeric types
        input_data['Age'] = user_answers.get("age")
        input_data['avg_glucose_level'] = user_answers.get("avg_glucose_level")

        # Convert Yes/No for features treated as NUMERIC (1/0) before preprocessing
        input_data['hypertension'] = 1 if user_answers.get("stress") == "Yes" else 0
        input_data['heart_disease'] = 1 if user_answers.get("stroke") == "Yes" else 0 # Derived from stroke history

        # Pass Yes/No strings for features treated as 'yes_or_no' by the preprocessor
        input_data['ever_married'] = user_answers.get("ever_married") # e.g., "Yes" or "No"

        # Pass strings for nominal features
        input_data['Sex'] = user_answers.get("gender") # e.g., "Male"
        input_data['work_type'] = user_answers.get("work_type") # e.g., "Self-employed"
        input_data['Residence_type'] = user_answers.get("residence_type") # e.g., "Rural"

        # Map smoking status to categories expected by the nominal encoder
        input_data['Smoking'] = user_answers.get("smoking")

        # 2. Create Single-Row DataFrame
        # Define the expected order of columns *before* preprocessing
        # This order MUST match the order of the original training data columns
        # used when fitting the preprocessors.
        expected_input_order = ['Sex', 'Age', 'hypertension', 'heart_disease', 'ever_married', 'work_type', 'Residence_type', 'avg_glucose_level', 'BMI', 'Smoking']
        input_df = pd.DataFrame([input_data], columns=expected_input_order)

        # 3. Preprocess using the provided function and loaded preprocessors
        # Pass 'fit_mode=False' implicitly by providing fitted_preprocessors
        # print("\nInput DataFrame before preprocessing:")
        # print(input_df.to_string()) # Use to_string to see full row content

        preprocessed_df = preprocess_dataframe(
            df=input_df,
            column_types=col_types,
            fitted_preprocessors=preprocessors # Pass the loaded preprocessors
        )
        input_reindexed = preprocessed_df.reindex(columns=training_columns, fill_value=0)
        

        # print("\nDataFrame after preprocessing:")
        # print(preprocessed_df.to_string())

        # 4. Predict Probability
        # Ensure columns in preprocessed_df match exactly what the model expects
        # (preprocess_dataframe with fitted OHE should handle this)
        probabilities = model.predict_proba(input_reindexed)
        stroke_probability = probabilities[0][1] # Probability of class 1 (stroke)
        result['stroke_probability']=stroke_probability*100
        result['stroke']=['Yes' if stroke_probability>33 else "No"][0]
        

        return result

    except ValueError as ve:
        print(f"ValueError during prediction: {ve}")
        return None
    except KeyError as ke:
         print(f"KeyError during prediction - likely missing input or mismatch: {ke}")
         return None
    except Exception as e:
        print(f"An unexpected error occurred during prediction: {e}")
        import traceback
        traceback.print_exc()
        return None
