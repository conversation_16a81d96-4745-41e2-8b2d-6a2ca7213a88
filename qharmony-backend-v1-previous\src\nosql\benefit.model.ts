import mongoose, {
  Document,
  MergeType,
  Model,
  ObjectId,
  UpdateWriteOpResult,
} from 'mongoose';

const { Schema } = mongoose;

export interface BenefitDataInterface {
  _id?: mongoose.Types.ObjectId;
  companyId: string;
  type: string;
  subType: string;
  heading: string;
  description: string;
  imageS3Urls: string[];
  links: string[];
  isActivated: boolean;
}

export interface UpdateableBenefitDataInterface {
  heading: string;
  description: string;
  imageS3Urls: string[];
  links: string[];
  isActivated: boolean;
}

interface BenefitDocument extends Document {
  data: BenefitDataInterface[];
}

class BenefitModelClass {
  private static benefitModel: Model<BenefitDocument>;

  public static initializeModel() {
    const schema = new Schema({
      companyId: String,
      type: String,
      subType: String,
      heading: String,
      description: String,
      imageS3Urls: Array<String>,
      links: Array<String>,
      isActivated: Boolean,
    });

    this.benefitModel = mongoose.model<BenefitDocument>('Benefit', schema);
  }

// src/nosql/benefit.model.ts
public static async addData(data: BenefitDataInterface): Promise<BenefitDataInterface | null> {
  try {
    const benefit = await this.benefitModel.create(data);
    return benefit as unknown as BenefitDataInterface; // Type assertion
  } catch (error) {
    console.error(error);
    return null;
  }
}

public static async addDataList(data: BenefitDataInterface[]): Promise<BenefitDataInterface[] | null> {
  try {
    const benefits = await this.benefitModel.insertMany(data);
    return benefits as unknown as BenefitDataInterface[];
  } catch (error) {
    console.error(error);
    return null;
  }
}

public static async getData(): Promise<BenefitDataInterface[]> {
  try {
    const data = await this.benefitModel.find().lean();
    // Use unknown as an intermediate type to fix the TypeScript error
    return data as unknown as BenefitDataInterface[];
  } catch (error) {
    console.error(error);
    return [];
  }
}
  public static async getDataById(
    id: string
  ): Promise<BenefitDataInterface | null> {
    try {
      const data = (await this.benefitModel.findById(
        id
      )) as BenefitDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  public static async getDataByCompanyId({
    companyId,
  }: {
    companyId: string;
  }): Promise<BenefitDataInterface[]> {
    try {
      const data = (await this.benefitModel.find({
        companyId,
      })) as BenefitDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async updateData({
    id,
    data,
  }: {
    id: string;
    data: Partial<BenefitDataInterface>;
  }): Promise<void> {
    try {
      // make sure you update only the fields that are present in the data object
      await this.benefitModel.findByIdAndUpdate(id, data);
    } catch (error) {
      console.error(error);
    }
  }

  // data could be an object with all the fields in BenefitDataInterface but not type and subType and companyId as they are not updatable
  public static async updateDataByCompanyIdAndBenefitId({
    companyId,
    benefitId,
    data,
  }: {
    companyId: string;
    benefitId: string;
    data: Partial<UpdateableBenefitDataInterface>;
  }): Promise<UpdateWriteOpResult> {
    try {
      // make sure you update only the fields that are present in the data object
      return await this.benefitModel.updateOne(
        { _id: benefitId, companyId },
        data
      );
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
}

BenefitModelClass.initializeModel();

export default BenefitModelClass;
