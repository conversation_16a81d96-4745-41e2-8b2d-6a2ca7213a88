const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Schema definitions
const carrierSchema = new mongoose.Schema({
  carrierName: String, carrierCode: String, displayName: String, brokerId: String, brokerageId: String,
  isSystemCarrier: Boolean, contactInfo: Object, supportedPlanTypes: [String], supportedCoverageTypes: [String],
  supportedCoverageSubTypes: [String], integration: Object, licenseStates: [String], amRating: String,
  networkName: String, status: String, isActive: Boolean, isActivated: Boolean
}, { timestamps: true });

const planSchema = new mongoose.Schema({
  planName: String, planCode: String, brokerId: String, brokerageId: String, isTemplate: Boolean,
  coverageType: String, coverageSubTypes: [String], planType: String, metalTier: String, description: String,
  highlights: [String], informativeLinks: [String], benefitDetails: Object, documentIds: [String],
  carrierId: String, status: String, isActivated: Boolean
}, { timestamps: true });

const planAssignmentSchema = new mongoose.Schema({
  planId: String, companyId: String, groupNumber: String, assignmentYear: Number, assignmentExpiry: Date,
  employerContribution: Object, employeeContribution: Object, rateStructure: String, coverageTiers: [Object],
  planCustomizations: Object, waitingPeriod: Object, eligibleEmployeeClasses: [String], enrollmentType: String,
  planEffectiveDate: Date, planEndDate: Date, enrollmentStartDate: Date, enrollmentEndDate: Date,
  qualifyingLifeEventWindow: Object, assignedBy: String, isActive: Boolean, status: String
}, { timestamps: true });

const COMPANY_ID = "67bf65bf50bad0a4b3d805ba";
const BROKER_ID = "67bf65bf50bad0a4b3d805bc";

// Convert to ObjectIds for database operations
const COMPANY_OBJECT_ID = new mongoose.Types.ObjectId(COMPANY_ID);
const BROKER_OBJECT_ID = new mongoose.Types.ObjectId(BROKER_ID);

async function setupSBSCompany() {
  try {
    console.log('🚨 SBS COMPANY SETUP - PRODUCTION DATABASE');
    console.log('This will create carrier, plans, and plan assignments for SBS company.');
    console.log('');

    // Connect to production database
    const prodUri = process.env.MONGO_URI.replace('/?retryWrites', '/prod?retryWrites');
    await mongoose.connect(prodUri);
    console.log('✅ Connected to MongoDB PRODUCTION database');
    
    const dbName = mongoose.connection.db.databaseName;
    console.log(`📍 Current database: ${dbName}`);
    if (dbName !== 'prod') throw new Error(`Expected 'prod' database, got '${dbName}'`);

    // Create models
    const Carrier = mongoose.model('Carrier', carrierSchema);
    const Plan = mongoose.model('Plan', planSchema);
    const PlanAssignment = mongoose.model('PlanAssignment', planAssignmentSchema);

    // STEP 1: Create/Get Carrier
    console.log('\n📋 STEP 1: Setting up Equitable Carrier...');
    let carrier = await Carrier.findOne({ carrierCode: "EQUITABLE" });
    
    if (carrier) {
      console.log(`✅ Equitable carrier already exists: ${carrier._id}`);
    } else {
      const carrierData = {
        carrierName: "Equitable Financial Life Insurance Company",
        carrierCode: "EQUITABLE", displayName: "Equitable",
        brokerId: BROKER_OBJECT_ID, brokerageId: COMPANY_OBJECT_ID, isSystemCarrier: true,
        contactInfo: { phone: "1‑866‑274‑9887", website: "https://www.equitable.com" },
        supportedPlanTypes: ["PPO", "Term Life", "Whole Life"],
        supportedCoverageTypes: ["Life & Disability Insurance", "Ancillary Benefits"],
        supportedCoverageSubTypes: ["Term Life", "Accidental Death & Dismemberment (AD&D)", "Dental", "Vision"],
        integration: { ediCapable: false, authMethod: "API_KEY", dataFormat: "JSON" },
        licenseStates: ["AL","AK","AZ","AR","CA","CO","CT","DE","FL","GA","HI","ID","IL","IN","IA","KS","KY","LA","ME","MD","MA","MI","MN","MS","MO","MT","NE","NV","NH","NJ","NM","NY","NC","ND","OH","OK","OR","PA","RI","SC","SD","TN","TX","UT","VT","VA","WA","WV","WI","WY"],
        amRating: "A", status: "Active", isActive: true, isActivated: true
      };
      carrier = await Carrier.create(carrierData);
      console.log(`✅ Carrier created: ${carrier._id}`);
    }

    // STEP 2: Create Plans
    console.log('\n📋 STEP 2: Setting up SBS Plans...');
    const plansData = [
      {
        planName: "SBS Group Term Life Insurance", planCode: "P005-Q0008-LIFE",
        coverageType: "Life & Disability Insurance", coverageSubTypes: ["Term Life"], planType: "Term Life",
        description: "Employer-paid group term life insurance plan offering financial protection.",
        highlights: ["$1,700,000 total coverage", "Rate: $0.190 per $1,000", "Monthly Premium: $323.00"]
      },
      {
        planName: "SBS Accidental Death & Dismemberment (AD&D)", planCode: "P005-Q0008-AD&D",
        coverageType: "Life & Disability Insurance", coverageSubTypes: ["Accidental Death & Dismemberment (AD&D)"], planType: "Term Life",
        description: "AD&D insurance covering accidental death and serious injury, fully employer-paid.",
        highlights: ["$1,700,000 coverage", "Rate: $0.023 per $1,000", "Monthly Premium: $39.10"]
      },
      {
        planName: "SBS Dental PPO Plan", planCode: "P005-Q0007",
        coverageType: "Ancillary Benefits", coverageSubTypes: ["Dental"], planType: "PPO",
        description: "Comprehensive dental PPO plan with 100/80/50 coinsurance, $2,250 annual max.",
        highlights: ["100/80/50 coinsurance", "$2,250 Annual Max", "$50 Individual Deductible"]
      },
      {
        planName: "SBS Vision PPO Plan", planCode: "P005-Q0009",
        coverageType: "Ancillary Benefits", coverageSubTypes: ["Vision"], planType: "PPO",
        description: "VSP-administered vision PPO plan with $10 eye exam copay.",
        highlights: ["$10 Eye Exam Copay", "$25 Eyewear Copay", "$150 Frame/Contact Allowance"]
      }
    ];

    const createdPlans = [];
    for (const planData of plansData) {
      let plan = await Plan.findOne({ planCode: planData.planCode });
      if (plan) {
        console.log(`✅ Plan ${planData.planCode} already exists: ${plan._id}`);
        createdPlans.push(plan);
      } else {
        const fullPlanData = {
          ...planData,
          brokerId: BROKER_OBJECT_ID, brokerageId: COMPANY_OBJECT_ID, isTemplate: false,
          informativeLinks: [], benefitDetails: { preventiveCareCoinsurance: "0%", additionalBenefits: [] },
          documentIds: [], carrierId: carrier._id, status: "Active", isActivated: true
        };
        plan = await Plan.create(fullPlanData);
        console.log(`✅ Plan created: ${plan.planName} - ${plan._id}`);
        createdPlans.push(plan);
      }
    }

    // STEP 3: Create Plan Assignments
    console.log('\n📋 STEP 3: Setting up Plan Assignments...');
    const assignmentsData = [
      {
        planCode: "P005-Q0008-LIFE", groupNumber: "GRP-SBS-LIFE",
        employerContribution: { contributionType: "Percentage", contributionAmount: 100 },
        employeeContribution: { contributionType: "Percentage", contributionAmount: 0 },
        coverageTiers: [{ tierName: "Employee Only", totalCost: 323.00, employerCost: 323.00, employeeCost: 0 }]
      },
      {
        planCode: "P005-Q0008-AD&D", groupNumber: "GRP-SBS-AD&D",
        employerContribution: { contributionType: "Percentage", contributionAmount: 100 },
        employeeContribution: { contributionType: "Percentage", contributionAmount: 0 },
        coverageTiers: [{ tierName: "Employee Only", totalCost: 39.10, employerCost: 39.10, employeeCost: 0 }]
      },
      {
        planCode: "P005-Q0007", groupNumber: "GRP-SBS-DENTAL",
        employerContribution: { contributionType: "Percentage", contributionAmount: 0 },
        employeeContribution: { contributionType: "Percentage", contributionAmount: 100 },
        coverageTiers: [
          { tierName: "Employee Only", totalCost: 33.16, employerCost: 0, employeeCost: 33.16 },
          { tierName: "Employee & Spouse", totalCost: 67.73, employerCost: 0, employeeCost: 67.73 },
          { tierName: "Employee & Child(ren)", totalCost: 95.73, employerCost: 0, employeeCost: 95.73 },
          { tierName: "Employee & Family", totalCost: 133.81, employerCost: 0, employeeCost: 133.81 }
        ]
      },
      {
        planCode: "P005-Q0009", groupNumber: "GRP-SBS-VISION",
        employerContribution: { contributionType: "Percentage", contributionAmount: 0 },
        employeeContribution: { contributionType: "Percentage", contributionAmount: 100 },
        coverageTiers: [
          { tierName: "Employee Only", totalCost: 7.68, employerCost: 0, employeeCost: 7.68 },
          { tierName: "Employee & Spouse", totalCost: 14.99, employerCost: 0, employeeCost: 14.99 },
          { tierName: "Employee & Child(ren)", totalCost: 16.06, employerCost: 0, employeeCost: 16.06 },
          { tierName: "Employee & Family", totalCost: 25.04, employerCost: 0, employeeCost: 25.04 }
        ]
      }
    ];

    const createdAssignments = [];
    for (const assignmentData of assignmentsData) {
      const plan = createdPlans.find(p => p.planCode === assignmentData.planCode);
      if (!plan) {
        console.error(`❌ Plan not found for code: ${assignmentData.planCode}`);
        continue;
      }

      let assignment = await PlanAssignment.findOne({
        groupNumber: assignmentData.groupNumber, companyId: COMPANY_OBJECT_ID
      });

      if (assignment) {
        console.log(`✅ Assignment ${assignmentData.groupNumber} already exists: ${assignment._id}`);
        createdAssignments.push(assignment);
      } else {
        const fullAssignmentData = {
          planId: plan._id, companyId: COMPANY_OBJECT_ID, groupNumber: assignmentData.groupNumber,
          assignmentYear: 2026, assignmentExpiry: new Date("2026-06-30T23:59:59.999Z"),
          employerContribution: assignmentData.employerContribution,
          employeeContribution: assignmentData.employeeContribution,
          rateStructure: "Composite", coverageTiers: assignmentData.coverageTiers,
          planCustomizations: { additionalDocuments: [] },
          waitingPeriod: { enabled: false, days: 0, rule: "Immediate" },
          eligibleEmployeeClasses: ["Full-Time"], enrollmentType: "Active",
          planEffectiveDate: new Date("2025-07-01T00:00:00.000Z"),
          planEndDate: new Date("2026-06-30T23:59:59.999Z"),
          enrollmentStartDate: new Date("2025-06-17T00:00:00.000Z"),
          enrollmentEndDate: new Date("2025-06-16T23:59:59.999Z"),
          qualifyingLifeEventWindow: {
            enabled: true, windowDays: 30,
            allowedEvents: ["Marriage", "Divorce", "Birth", "Adoption", "Loss of Coverage", "Job Change", "Death", "Relocation", "Other"],
            description: "Qualifying life event enrollment window"
          },
          assignedBy: BROKER_OBJECT_ID, isActive: true, status: "Active"
        };
        assignment = await PlanAssignment.create(fullAssignmentData);
        console.log(`✅ Assignment created: ${assignment.groupNumber} - ${assignment._id}`);
        createdAssignments.push(assignment);
      }
    }

    // SUMMARY
    console.log('\n🎯 SBS COMPANY SETUP COMPLETE!');
    console.log(`Database: ${dbName}`);
    console.log(`Carrier: ${carrier.carrierName} (${carrier._id})`);
    console.log(`Company: ${COMPANY_ID}`);
    console.log(`Broker: ${BROKER_ID}`);
    console.log('\nPlans & Assignments:');
    createdAssignments.forEach(a => {
      const plan = createdPlans.find(p => p._id.toString() === a.planId);
      console.log(`  ${a.groupNumber}: ${plan?.planName} (${a._id})`);
    });
    
    console.log('\n✅ READY FOR ENROLLMENT:');
    console.log('- Enrollment Period: June 17, 2025 - June 16, 2025 (OPEN NOW!)');
    console.log('- Coverage Period: July 1, 2025 - June 30, 2026');
    console.log('- Life & AD&D: 100% employer paid');
    console.log('- Dental & Vision: 100% employee paid');

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');

  } catch (error) {
    console.error('❌ SBS setup failed:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
}

setupSBSCompany();
