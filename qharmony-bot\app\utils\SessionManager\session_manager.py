import asyncio
from datetime import datetime
from typing import Dict, Optional, AsyncGenerator
import redis.asyncio as redis_async
from redis.typing import ResponseT
import json
import pickle
from app.controllers.chatSession.chatSession import ChatSession
import logging
from pymongo import MongoClient
from app.Tools.vectorStore import VectorStore
from config.config import config
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)



class SessionManager:
    """Manages chat sessions for multiple users with memory optimization."""
    
    def __init__(self, redis_url: Optional[str] = None, timeout_minutes: int = 30, max_memory_sessions: int = 1000, task_cleanup_time: int = 30, model_config: dict = None):
        self.sessions: Dict[str, ChatSession] = {}
        self.redis_url = redis_url
        self.redis_client = None
        self.timeout_minutes = timeout_minutes
        self.max_memory_sessions = int(max_memory_sessions)
        self.session_access_times = {}  # Track when each session was last accessed
        self.task_cleanup_time: int = int(task_cleanup_time)
        self.model_config = model_config
        
    async def initialize(self):
        """Initialize the session manager."""
        if self.redis_url:
            try:
                self.redis_client = await redis_async.from_url(self.redis_url)
                # Test connection
                await self.redis_client.ping()
                logger.info("[SessionManager] Successfully connected to Redis")
            except Exception as e:
                logger.warning(f"[SessionManager] Redis connection failed: {str(e)}")
                self.redis_client = None
        
        # Start background task to clean expired sessions
        asyncio.create_task(self._cleanup_expired_sessions())
        return self

    # Add this new method for reconnection attempts
    async def _ensure_redis_connection(self):
        """Attempt to reconnect to Redis if the connection was lost."""
        if not self.redis_client and self.redis_url:
            try:
                # logger.info("[SessionManager] Attempting to reconnect to Redis")
                self.redis_client = await redis_async.from_url(self.redis_url)
                await self.redis_client.ping()
                logger.info("[SessionManager] Successfully reconnected to Redis")
                return True
            except Exception as e:
                logger.warning(f"[SessionManager] Redis reconnection failed: {str(e)}")
                return False
        return self.redis_client is not None
    
    async def get_session(self, user_id: str, team_id: str, vector_store: VectorStore, mongo_client: MongoClient) -> ChatSession:
        """Get or create a session for a user."""
        session_key = f"{team_id}_{user_id}"
        current_time = datetime.now()
        
        # Try to get from memory first
        if session_key in self.sessions:
            self.sessions[session_key].last_activity = current_time
            self.session_access_times[session_key] = current_time
            return self.sessions[session_key]
        
        # Try to get from Redis if available and connected
        if self.redis_client is not None:
            try:
                
                session_data = await self.redis_client.get(f"chat_session:{session_key}")
                if session_data:
                    session: ChatSession = pickle.loads(session_data)
                    session.last_activity = current_time
                    
                    # Restore connections that were removed during pickling
                    session.vector_store = vector_store
                    session.mongo_client = mongo_client
                    session.model_config = self.model_config  # Ensure model_config is restored
                    
                    # Initialize the session (this will recreate chat_model if it's None)
                    await session.initialize()
                    
                    # Check if we need to evict a session before adding this one
                    if len(self.sessions) >= self.max_memory_sessions:
                        await self._evict_least_recently_used()
                    
                    self.sessions[session_key] = session
                    self.session_access_times[session_key] = current_time
                    return session
            except Exception as e:
                logger.warning(f"[SessionManager] Redis error: {str(e)}")
                # Instead of setting self.redis_client = None, try to reconnect later
                # Don't immediately set self.redis_client = None
        
        # Create new session
        logger.info(f"[SessionManager] Creating new session for {session_key}")
        session = await ChatSession(
            user_id=user_id,
            team_id=team_id,
            vector_store=vector_store,
            mongo_client=mongo_client,
            model_config=self.model_config,
        ).initialize()
        
        # Check if we need to evict a session before adding this one
        if len(self.sessions) >= self.max_memory_sessions:
            await self._evict_least_recently_used()
        
        # Store in memory
        self.sessions[session_key] = session
        self.session_access_times[session_key] = current_time
        
                # Store in Redis only if we're near capacity or for backup purposes
        if self.redis_client is not None:
            try:
                # Only persist to Redis if we're at 80% capacity or more
                should_persist = len(self.sessions) >= (self.max_memory_sessions * 0.9)
                
                # Always persist if explicitly configured to do so
                if should_persist:
                    # Ensure TTL is a valid integer
                    ttl = int(self.timeout_minutes * 60 * config.redis_timeout_multiple)
                    if ttl <= 0:
                        ttl = 3600  # Default to 1 hour if invalid
                    
                    redis_key = f"chat_session:{session_key}"
                    serialized = pickle.dumps(session)
                    await self.redis_client.setex(redis_key, ttl, serialized)
                    logger.debug(f"[SessionManager] Session persisted to Redis: {redis_key}")
            except Exception as e:
                logger.error(f"[SessionManager] Redis error: {str(e)}")
                # Mark Redis as unavailable
                self.redis_client = None

        return session
    
    async def _evict_least_recently_used(self):
        """Evict the least recently used session from memory (but keep in Redis)."""
        logger.info("[SessionManager] Evicting least recently used session")
        if not self.session_access_times:
            return
            
        # Find the least recently used session
        oldest_key = min(self.session_access_times.items(), key=lambda x: x[1])[0]
        
        # Save to Redis if available
        if self.redis_client and oldest_key in self.sessions:
            try:
                session = self.sessions[oldest_key]
                ttl = int(self.timeout_minutes * 60 * config.redis_timeout_multiple)
                if ttl <= 0:
                    ttl = 3600
                
                await self.redis_client.setex(
                    f"chat_session:{oldest_key}",
                    ttl,
                    pickle.dumps(session)
                )
                logger.info(f"[SessionManager] Session saved to Redis with TTL: {ttl}")
            except Exception as e:
                logger.error(f"[SessionManager] Error saving to Redis: {str(e)}")
        
        # Remove from memory
        if oldest_key in self.sessions:
            del self.sessions[oldest_key]
        if oldest_key in self.session_access_times:
            del self.session_access_times[oldest_key]
    
    async def _cleanup_expired_sessions(self):
        """Background task to clean up expired sessions with optimized Redis operations."""
        while True:
            try:
                # Track Redis operations to avoid redundant work
                processed_redis_keys = set()
                
                # 1. First, clean memory sessions
                memory_expired_keys = []
                for key, session in self.sessions.items():
                    if session.is_expired(self.timeout_minutes):
                        memory_expired_keys.append(key)
                
                # 2. Process memory-expired sessions
                for key in memory_expired_keys:
                    redis_key = f"chat_session:{key}"
                    processed_redis_keys.add(redis_key)  # Mark as processed
                    
                    if self.redis_client is not None:
                        try:
                            session = self.sessions[key]
                            redis_timeout = self.timeout_minutes * config.redis_timeout_multiple
                            
                            # Check if expired for Redis too
                            if session.is_expired(redis_timeout):
                                # Use DEL instead of UNLINK for consistency
                                # DEL is fine here since we're already in a background task
                                await self.redis_client.delete(redis_key)
                                logger.debug(f"[SessionManager] Session removed from Redis: {redis_key}")
                            else:
                                # Keep in Redis with updated TTL
                                ttl = int(redis_timeout * 60)
                                serialized = pickle.dumps(session)
                                await self.redis_client.setex(redis_key, ttl, serialized)
                                logger.debug(f"[SessionManager] Session preserved in Redis: {redis_key}")
                        except Exception as e:
                            logger.debug(f"[SessionManager] Redis operation failed: {str(e)}")
                    
                    # Remove from memory
                    del self.sessions[key]
                    if key in self.session_access_times:
                        del self.session_access_times[key]
                
                # 3. Process Redis-only sessions if Redis is available
                if self.redis_client is not None:
                    try:
                        # Check Redis connection
                        await self.redis_client.ping()
                        
                        # Get all session keys from Redis
                        redis_keys = await self.redis_client.keys("chat_session:*")
                        
                        # Process only keys we haven't already handled
                        for redis_key in redis_keys:
                            if redis_key in processed_redis_keys:
                                continue
                            
                            session_key = redis_key.decode('utf-8').replace("chat_session:", "")
                            
                            # Skip keys that are in memory (already handled)
                            if session_key in self.sessions:
                                continue
                            
                            # Get and check the session
                            session_data = await self.redis_client.get(redis_key)
                            if session_data:
                                try:
                                    session = pickle.loads(session_data)
                                    redis_timeout = self.timeout_minutes * config.redis_timeout_multiple
                                    
                                    if session.is_expired(redis_timeout):
                                        # Use DEL for consistency
                                        await self.redis_client.delete(redis_key)
                                        logger.debug(f"[SessionManager] Expired Redis session removed: {redis_key}")
                                except Exception as e:
                                    # Corrupted data - remove it
                                    logger.warning(f"[SessionManager] Corrupted session data: {str(e)}")
                                    await self.redis_client.delete(redis_key)
                                    logger.debug(f"[SessionManager] Corrupted Redis session removed: {redis_key}")
                    except Exception as e:
                        logger.warning(f"[SessionManager] Redis unavailable during cleanup: {str(e)}")
                        # Don't set self.redis_client = None here
                        # Instead, just skip this cleanup cycle
                        pass
                
                # Log statistics
                logger.info(f"[SessionManager] Active sessions in memory: {len(self.sessions)}")
                
                # Sleep before next cleanup
                await asyncio.sleep(self.task_cleanup_time)
            except Exception as e:
                logger.error(f"[SessionManager] Cleanup task error: {str(e)}")
