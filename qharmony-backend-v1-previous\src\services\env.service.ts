import { cleanEnv, str } from 'envalid';

import AbstractService from './service';
import logger from '../utils/logger';

// Keep your EnvVariables type definition as is
export type EnvVariables = {
  NODE_ENV: string;
  PORT: string;
  MONGO_URI: string;
  MERGE_APIKEY: string;
  MERGE_SAMPLE_ACCOUNT_TOKEN: string;
  FINCH_CLIENT_ID: string;
  FINCH_CLIENT_SECRET: string;
  FINCH_SANDBOX_CLIENT_ID: string;
  FINCH_SANDBOX_CLIENT_SECRET: string;
  FINCH_WEBHOOK_SECRET: string;
  FINCH_REDIRECT_URI: string;
  OPENAI_API_KEY: string;
  SLACK_APP_ID: string;
  SLACK_CLIENT_ID: string;
  SLACK_CLIENT_SECRET: string;
  SLACK_APP_SIGNING_SECRET: string; // NOTE: Renamed from SLACK_SIGNING_SECRET in your previous logs, ensure consistency
  ASSISTANT_ID: string;
  QHARMONY_SECRET: string;
  AWS_KEY: string;
  AWS_SECRET: string;
  AWS_REGION: string;
  FIREBASE_API_KEY: string;
  FIREBASE_AUTH_DOMAIN: string;
  FIREBASE_PROJECT_ID: string;
  FIREBASE_STORAGE_BUCKET: string;
  FIREBASE_MESSAGING_SENDER_ID: string;
  FIREBASE_APP_ID: string;
  AZURE_STORAGE_CONNECTION_STRING: string; // Added based on your .env file dump
};

/*
 * This service is responsible for loading environment variables
 */
class EnvService implements AbstractService {
  // Keep static envVariables definition as is
  static envVariables = {
    NODE_ENV: str({
      choices: ['development', 'staging', 'debug', 'production', 'test'], // Added 'test' from your previous fallback attempt
    }),
    PORT: str(),
    MONGO_URI: str(),
    MERGE_APIKEY: str(),
    MERGE_SAMPLE_ACCOUNT_TOKEN: str(),
    FINCH_CLIENT_ID: str(),
    FINCH_CLIENT_SECRET: str(),
    FINCH_SANDBOX_CLIENT_ID: str(),
    FINCH_SANDBOX_CLIENT_SECRET: str(),
    FINCH_WEBHOOK_SECRET: str(),
    FINCH_REDIRECT_URI: str(),
    OPENAI_API_KEY: str(),
    SLACK_APP_ID: str(),
    SLACK_CLIENT_ID: str(),
    SLACK_CLIENT_SECRET: str(),
    SLACK_APP_SIGNING_SECRET: str(), // Ensure this matches your .env file and EnvVariables type
    ASSISTANT_ID: str(),
    QHARMONY_SECRET: str(),
    AWS_KEY: str(),
    AWS_SECRET: str(),
    AWS_REGION: str(),
    FIREBASE_API_KEY: str(),
    FIREBASE_AUTH_DOMAIN: str(),
    FIREBASE_PROJECT_ID: str(),
    FIREBASE_STORAGE_BUCKET: str(),
    FIREBASE_MESSAGING_SENDER_ID: str(),
    FIREBASE_APP_ID: str(),
    AZURE_STORAGE_CONNECTION_STRING: str(), // Added based on your .env file dump
  };

  static envs: Readonly<EnvVariables>;

  // Keep init() method as is
  static init(): void {
    try {
      this.envs = cleanEnv(process.env, EnvService.envVariables, {
        reporter: ({ errors }) => { // Destructure directly
          const errorKeys = Object.keys(errors);
          if (errorKeys.length > 0) {
            // Log the specific missing/invalid variables
            logger.error(`Invalid or missing environment variables: ${errorKeys.join(', ')}`);
            // Optionally re-throw here if you want cleanEnv failure to stop the app immediately
            // throw new Error(`Invalid or missing environment variables: ${errorKeys.join(', ')}`);
          }
        },
        // Consider adding this if cleanEnv should strictly only allow defined vars
        // strict: true
      });

      // Check if envs was actually populated (reporter might not throw)
      if (!this.envs) {
         throw new Error('Environment variables failed to load.');
      }

      logger.info(`Loaded environment variables for NODE_ENV: ${this.envs.NODE_ENV}`);

    } catch (error) {
       // Catch errors thrown by cleanEnv itself (e.g., missing required variables)
       logger.error('Failed to initialize environment variables:', error);
       // Exit gracefully or re-throw to stop the application
       process.exit(1);
    }
  }

  // *** MODIFIED env() METHOD ***
  static env(): Readonly<EnvVariables> {
    // Check if init() has been successfully run and populated this.envs
    if (!this.envs) {
      // Log an error for easier debugging
      logger.error("Attempted to access environment variables before EnvService.init() was successfully completed.");
      // Throw an error to stop execution, preventing use of uninitialized config
      throw new Error("Environment variables accessed before initialization.");
    }
    // If we pass the check, this.envs is guaranteed to be populated
    return this.envs;
  }
}

export default EnvService;

// Ensure init() is called at application startup
// For example, in your main entry file (e.g., src/index.ts or src/server.ts):
// import EnvService from './services/env.service';
// EnvService.init(); // Call this BEFORE any other code that might need EnvService.env()
// ... rest of your application startup code ...