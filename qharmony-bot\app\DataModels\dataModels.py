from typing import Dict, List, Union
from pydantic import BaseModel, Field

class UserInput(BaseModel):
    """Pydantic model for user input validation."""

    user_message: str = Field(..., description="The message sent by the user")
    user_id: str = Field(..., description="Unique identifier for the user")
    team_id: str = Field(..., description="Identifier for the user's team")

class TeamsUserInput(BaseModel):
    """Pydantic model for user input validation."""

    user_message: str = Field(..., description="The message sent by the user")
    user_email: str = Field(..., description="Unique identifier for the user")

class PineconeInput(BaseModel):
    """Pydantic model for Pinecone index update validation."""
    team_id: str = Field(..., description="Identifier for the user's team")
    object_keys: Union[List[str], str] = Field(..., description="List of object keys or a single object key to update the index")
