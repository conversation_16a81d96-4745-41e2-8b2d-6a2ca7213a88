import express from 'express';
import HttpException from '../exceptions/http.exception';

export async function isEmployee(
  req: express.Request,
  _res: express.Response,
  next: express.NextFunction
): Promise<void> {
  try {
    // Check if the user is an employer.
    if (req.body.user.role !== 'employee') {
      next(new HttpException(401, 'You are not authorized'));
      // Make linter happy.
      return;
    }
  } catch (error) {
    next(new HttpException(401, 'You are not authorized'));
    // Make linter happy.
    return;
  }
  next();
}

function isEmployeeMiddleware(): express.RequestHandler {
  return async (
    req: express.Request,
    _res: express.Response,
    next: express.NextFunction
  ) => {
    await isEmployee(req, _res, next);
  };
}

export default isEmployeeMiddleware;
