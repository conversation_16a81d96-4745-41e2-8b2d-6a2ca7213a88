// lib/helpers/axios_helper.ts

import axios from "axios";

// Use environment variables
export const BASE_URL = process.env.NEXT_PUBLIC_API_URL!;

// Parse admin emails from environment variable
const adminEmailsString = process.env.NEXT_PUBLIC_ADMIN_EMAILS!;
export const qHarmonyAdminEmail = adminEmailsString.split(',').map(email => email.trim());

// Create an Axios instance with default headers
export const axiosInstance = axios.create({
  baseURL: BASE_URL,
});

axiosInstance.interceptors.request.use((config) => {
  // Get user ID from environment-configured keys
  const primaryKey = process.env.NEXT_PUBLIC_USER_ID_KEY || "userid1";
  const altKey = process.env.NEXT_PUBLIC_USER_ID_ALT_KEY || "userId";

  const userId = localStorage.getItem(primaryKey) || localStorage.getItem(altKey);

  if (process.env.NEXT_PUBLIC_DEBUG_LOGGING === 'true') {
    console.log(`Retrieved userId: ${userId}`); // Log the userId only in debug mode
  }

  if (userId) {
    config.headers["user-id"] = userId;
  } else {
    console.warn('No user ID found in localStorage for API request');
  }
  return config;
});

// Helper function for GET requests
export async function getRequest(
  endpoint: string,
  params?: Record<string, any>,
  overrideUrl?: string,
) {
  const url = new URL(
    overrideUrl ? `${overrideUrl}${endpoint}` : `${BASE_URL}${endpoint}`,
  );

  if (params) {
    Object.keys(params).forEach((key) =>
      url.searchParams.append(key, params[key]),
    );
  }

  if (process.env.NEXT_PUBLIC_DEBUG_LOGGING === 'true') {
    console.log(`GET request to: ${url.toString()}`); // Log the request only in debug mode
  }
  const response = await axiosInstance.get(url.toString());
  return response.data;
}

// Helper function for POST requests with JSON data
export async function postRequest(
  endpoint: string,
  data?: Record<string, any>,
  overrideUrl?: string,
) {
  const url = overrideUrl
    ? `${overrideUrl}${endpoint}`
    : `${BASE_URL}${endpoint}`;
  if (process.env.NEXT_PUBLIC_DEBUG_LOGGING === 'true') {
    console.log(`POST request to: ${url} with data:`, data); // Log the request only in debug mode
  }
  const response = await axiosInstance.post(url, data, {
    headers: {
      "Content-Type": "application/json",
    },
  });
  return { status: response.status, data: response.data };
}

// New function for document upload
export async function uploadDocument(
  endpoint: string,
  formData: FormData,
  overrideUrl?: string,
) {
  const url = overrideUrl
    ? `${overrideUrl}${endpoint}`
    : `${BASE_URL}${endpoint}`;
  console.log(`Document upload to: ${url}`); // Log the request
  const response = await axiosInstance.post(url, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return { status: response.status, data: response.data };
}

// Helper function for GET requests with Blob response
export async function getBlobRequest(
  endpoint: string,
  params?: Record<string, any>,
  overrideUrl?: string,
) {
  const url = new URL(
    overrideUrl ? `${overrideUrl}${endpoint}` : `${BASE_URL}${endpoint}`,
  );

  if (params) {
    Object.keys(params).forEach((key) =>
      url.searchParams.append(key, params[key]),
    );
  }

  console.log(`GET Blob request to: ${url.toString()}`); // Log the request
  const response = await axiosInstance.get(url.toString(), {
    responseType: "blob", // Important: expects binary data
  });
  return response.data; // Return the binary data (Blob)
}

// Helper function for PUT requests with JSON data
export async function putRequest(
  endpoint: string,
  data?: Record<string, any>,
  overrideUrl?: string,
) {
  const url = overrideUrl
    ? `${overrideUrl}${endpoint}`
    : `${BASE_URL}${endpoint}`;
  const config = {
    headers: {
      "Content-Type": "application/json",
    },
  };
  if (process.env.NEXT_PUBLIC_DEBUG_LOGGING === 'true') {
    console.log(`PUT request to: ${url} with data:`, data); // Log the request only in debug mode
    console.log(`Request headers:`, config.headers); // Log the headers only in debug mode
  }
  const response = await axiosInstance.put(url, data, config);
  return { status: response.status, data: response.data };
}
