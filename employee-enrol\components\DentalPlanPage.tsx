'use client';

import React, { useState } from 'react';
import { User, CheckCircle, Play } from 'lucide-react';

interface DentalPlan {
  id: string;
  name: string;
  cost: number;
  features: string[];
  recommended?: boolean;
}

interface DentalPlanPageProps {
  onPlanSelect: (plan: DentalPlan | null) => void;
}

const DentalPlanPage: React.FC<DentalPlanPageProps> = ({ onPlanSelect }) => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const dentalPlans: DentalPlan[] = [
    {
      id: 'delta-ppo',
      name: 'Delta Dental PPO',
      cost: 12.00,
      features: [
        '100% preventive care (cleanings, exams)',
        '$50 deductible',
        'Large provider network'
      ],
      recommended: true
    },
    {
      id: 'guardian-dhmo',
      name: 'Guardian DHMO',
      cost: 6.50,
      features: [
        'Lower cost option',
        'Fewer provider options',
        'Good for basic care'
      ]
    }
  ];

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
    const plan = dentalPlans.find(p => p.id === planId);
    onPlanSelect(plan || null);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{ 
          width: '40px', 
          height: '40px', 
          backgroundColor: '#dbeafe', 
          borderRadius: '8px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <User style={{ width: '20px', height: '20px', color: '#2563eb' }} />
        </div>
        <div style={{ 
          backgroundColor: '#f9fafb', 
          borderRadius: '8px', 
          padding: '16px', 
          maxWidth: '512px' 
        }}>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: 0 
          }}>
            😁 Now let's take care of your smile! Which dental plan works best for you?
          </p>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: '8px 0 0 0' 
          }}>
            Even if you don't need dental care now, having coverage can save you money later.
          </p>
        </div>
      </div>

      {/* Dental Plan Selection */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        border: '1px solid #e5e7eb', 
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <span style={{ fontSize: '18px' }}>🦷</span>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: '600', 
            color: '#111827',
            margin: 0
          }}>
            Smart Dental Plan Selection
          </h2>
        </div>

        <p style={{ 
          color: '#6b7280', 
          marginBottom: '24px',
          margin: 0
        }}>
          Now let's find your perfect dental plan:
        </p>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {dentalPlans.map((plan) => (
            <div
              key={plan.id}
              style={{
                border: selectedPlan === plan.id ? '2px solid #10b981' : '2px solid #e5e7eb',
                borderRadius: '8px',
                padding: '20px',
                backgroundColor: selectedPlan === plan.id ? '#ecfdf5' : '#f9fafb',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onClick={() => handlePlanSelect(plan.id)}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                <div>
                  <h3 style={{ 
                    fontSize: '18px', 
                    fontWeight: '600', 
                    color: '#111827',
                    margin: 0
                  }}>
                    {plan.name}
                  </h3>
                  <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '4px' }}>
                    <span style={{ 
                      fontSize: '24px', 
                      fontWeight: '700', 
                      color: '#111827' 
                    }}>
                      ${plan.cost.toFixed(2)}
                    </span>
                    <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                  </div>
                </div>
                {plan.recommended && (
                  <div style={{ 
                    backgroundColor: '#f59e0b', 
                    color: 'white', 
                    padding: '4px 8px', 
                    borderRadius: '12px', 
                    fontSize: '12px', 
                    fontWeight: '500' 
                  }}>
                    Recommended
                  </div>
                )}
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                {plan.features.map((feature, index) => (
                  <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <CheckCircle style={{ width: '16px', height: '16px', color: '#10b981', flexShrink: 0 }} />
                    <span style={{ color: '#374151', fontSize: '14px' }}>{feature}</span>
                  </div>
                ))}
              </div>

              <button
                style={{
                  width: '100%',
                  backgroundColor: selectedPlan === plan.id ? '#10b981' : '#f3f4f6',
                  color: selectedPlan === plan.id ? 'white' : '#6b7280',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  fontWeight: '500',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                {selectedPlan === plan.id ? 'Selected' : 'Select This Plan'}
              </button>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div style={{ 
          display: 'flex', 
          gap: '12px', 
          paddingTop: '24px', 
          borderTop: '1px solid #e5e7eb', 
          marginTop: '24px' 
        }}>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <Play size={16} style={{ color: '#6b7280' }} />
            Watch Video
          </button>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <span style={{ color: '#2563eb' }}>❓</span>
            Ask Questions
          </button>
        </div>
      </div>
    </div>
  );
};

export default DentalPlanPage;
