
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ThemeToggle } from './ThemeToggle';
import { FloatingHelp } from './FloatingHelp';
import { ValueAccumulator } from './ValueAccumulator';
import { Bell, Brain, Stethoscope, Smile, Eye, FileText, CheckCircle, Plus, ArrowLeft, ArrowRight } from 'lucide-react';

// Import page components
import { WelcomePage } from '@/pages/WelcomePage';
import { PersonalizationPage } from '@/pages/PersonalizationPage';
import { MedicalPlanPage } from '@/pages/MedicalPlanPage';
import { DentalPlanPage } from '@/pages/DentalPlanPage';
import { VisionPlanPage } from '@/pages/VisionPlanPage';
import { AdditionalBenefitsPage } from '@/pages/AdditionalBenefitsPage';
import { SummaryPage } from '@/pages/SummaryPage';
import { ConfirmationPage } from '@/pages/ConfirmationPage';

export interface UserProfile {
  familyMembers: string;
  wearGlasses: boolean;
  needsDentalCare: boolean;
  hasPreferredDoctors: boolean;
  selectedMedical?: any;
  selectedDental?: any;
  selectedVision?: any;
  selectedPetInsurance?: any;
  selectedHospitalIndemnity?: any;
  expectedMedicalUsage?: string;
  budgetPreference?: string;
  chronicConditions?: boolean;
  prescriptionNeeds?: boolean;
  preferredProviders?: string[];
  budgetRange?: number[];
  hasPets?: boolean;
}

export interface ChatStep {
  id: string;
  title: string;
  icon: React.ReactNode;
  completed: boolean;
}

export const BenefitsEnrollmentBot = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [userProfile, setUserProfile] = useState<UserProfile>({
    familyMembers: '',
    wearGlasses: false,
    needsDentalCare: false,
    hasPreferredDoctors: false,
  });
  
  // Add a ref to store the current userProfile to avoid stale closures
  const currentUserProfileRef = useRef(userProfile);
  
  // Update the ref whenever userProfile changes
  useEffect(() => {
    currentUserProfileRef.current = userProfile;
    console.log('BenefitsEnrollmentBot - userProfile updated:', userProfile);
  }, [userProfile]);

  const steps: ChatStep[] = [
    { id: 'kickoff', title: 'Welcome & Kickoff', icon: <Bell className="w-4 h-4" />, completed: false },
    { id: 'personalization', title: 'Smart Personalization', icon: <Brain className="w-4 h-4" />, completed: false },
    { id: 'medical', title: 'Medical Plan', icon: <Stethoscope className="w-4 h-4" />, completed: false },
    { id: 'dental', title: 'Dental Plan', icon: <Smile className="w-4 h-4" />, completed: false },
    { id: 'vision', title: 'Vision Plan', icon: <Eye className="w-4 h-4" />, completed: false },
    { id: 'additional', title: 'Additional Benefits', icon: <Plus className="w-4 h-4" />, completed: false },
    { id: 'summary', title: 'Summary Review', icon: <FileText className="w-4 h-4" />, completed: false },
    { id: 'confirmation', title: 'Confirmation', icon: <CheckCircle className="w-4 h-4" />, completed: false },
  ];

  const [completedSteps, setCompletedSteps] = useState<boolean[]>(new Array(steps.length).fill(false));
  const [recommendation, setRecommendation] = useState<any>(null);

  const markStepComplete = (stepIndex: number) => {
    setCompletedSteps(prev => {
      const updated = [...prev];
      updated[stepIndex] = true;
      return updated;
    });
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      markStepComplete(currentStep);
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePersonalizationComplete = (profile: Partial<UserProfile>) => {
    console.log('Personalization completed with profile:', profile);
    setUserProfile(prev => {
      const updated = { ...prev, ...profile };
      console.log('Updated userProfile after personalization:', updated);
      return updated;
    });
    
    // Generate recommendation
    const rec = getSmartRecommendation(profile);
    setRecommendation(rec);
    
    handleNext();
  };

  const getSmartRecommendation = (profile: any) => {
    const { expectedMedicalUsage, budgetPreference, chronicConditions, hasPreferredDoctors } = profile;
    
    if (budgetPreference === 'low-premium' && expectedMedicalUsage === 'low') {
      return {
        plan: {
          name: "Blue Cross HSA Plan",
          cost: 45.20,
          deductible: 3000,
          type: 'HDHP',
          features: [
            "Lowest monthly cost: $45.20/paycheck",
            "HSA eligible - save on taxes!",
            "Perfect for healthy individuals",
            "Great for building health savings"
          ]
        },
        reason: "You prefer lower premiums and expect minimal healthcare usage. This HSA-eligible plan offers the lowest monthly cost and tax advantages."
      };
    }
    
    if (hasPreferredDoctors || chronicConditions) {
      return {
        plan: {
          name: "Anthem PPO Premium",
          cost: 89.90,
          deductible: 1500,
          type: 'PPO',
          features: [
            "Keep your current doctors",
            "No referrals needed",
            "Lower $1,500 deductible",
            "Excellent for ongoing care"
          ]
        },
        reason: "Since you have preferred doctors or ongoing health needs, this PPO plan gives you the flexibility to see any provider without referrals."
      };
    }

    if (budgetPreference === 'balanced' || expectedMedicalUsage === 'moderate') {
      return {
        plan: {
          name: "Anthem PPO 035",
          cost: 82.90,
          deductible: 2000,
          type: 'PPO',
          features: [
            "Balanced cost and coverage",
            "Deductible: $2,000",
            "Covers your PCP visits at $25",
            "Good for moderate usage & predictable costs"
          ]
        },
        reason: "This plan offers the best balance of monthly cost and coverage for someone with moderate healthcare needs."
      };
    }

    // Default recommendation
    return {
      plan: {
        name: "Kaiser HMO",
        cost: 65.40,
        deductible: 1500,
        type: 'HMO',
        features: [
          "Integrated healthcare system",
          "Lower cost: $65.40/paycheck", 
          "Coordinated care",
          "Great value for families"
        ]
      },
      reason: "This HMO plan offers great value with coordinated care and lower costs, perfect for most employees."
    };
  };

  const handlePlanSelection = (planType: 'medical' | 'dental' | 'vision', planData: any) => {
    console.log(`Plan selection for ${planType}:`, planData);
    
    setUserProfile(prev => {
      const fieldName = `selected${planType.charAt(0).toUpperCase() + planType.slice(1)}`;
      const updated = {
        ...prev,
        [fieldName]: planData
      };
      console.log(`Updated userProfile after ${planType} selection:`, updated);
      return updated;
    });

    handleNext();
  };

  const handleAdditionalRecommendationsComplete = (additionalPlans: any) => {
    console.log('Additional recommendations completed:', additionalPlans);
    console.log('Current userProfile before update:', currentUserProfileRef.current);
    
    setUserProfile(prev => {
      const updated = { ...prev, ...additionalPlans };
      console.log('Updated userProfile after additional recommendations:', updated);
      currentUserProfileRef.current = updated;
      return updated;
    });
    
    handleNext();
  };

  const handleFinalConfirmation = () => {
    handleNext();
  };

  const renderCurrentPage = () => {
    switch (currentStep) {
      case 0:
        return <WelcomePage onNext={handleNext} />;
      case 1:
        return <PersonalizationPage onComplete={handlePersonalizationComplete} />;
      case 2:
        return recommendation ? (
          <MedicalPlanPage 
            userProfile={userProfile}
            onPlanSelect={(planData) => handlePlanSelection('medical', planData)}
            recommendation={recommendation}
          />
        ) : null;
      case 3:
        return (
          <DentalPlanPage 
            userProfile={userProfile}
            onPlanSelect={(planData) => handlePlanSelection('dental', planData)}
          />
        );
      case 4:
        return (
          <VisionPlanPage 
            userProfile={userProfile}
            onPlanSelect={(planData) => handlePlanSelection('vision', planData)}
          />
        );
      case 5:
        return (
          <AdditionalBenefitsPage 
            userProfile={userProfile}
            onComplete={handleAdditionalRecommendationsComplete}
          />
        );
      case 6:
        return (
          <SummaryPage 
            userProfile={currentUserProfileRef.current}
            onConfirm={handleFinalConfirmation}
            onEdit={() => setCurrentStep(2)}
          />
        );
      case 7:
        return <ConfirmationPage />;
      default:
        return <WelcomePage onNext={handleNext} />;
    }
  };

  const progress = (currentStep / (steps.length - 1)) * 100;

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        {/* Simplified header with just theme toggle */}
        <div className="flex justify-end mb-6">
          <ThemeToggle />
        </div>

        {/* Value Accumulator - show after personalization */}
        {currentStep > 1 && (
          <ValueAccumulator userProfile={userProfile} currentStep={currentStep} />
        )}

        {/* Progress - only show after welcome page */}
        {currentStep > 0 && (
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Enrollment Progress</CardTitle>
                <Badge variant="outline">{currentStep + 1} of {steps.length}</Badge>
              </div>
              <Progress value={progress} className="w-full" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
                      completedSteps[index]
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : index === currentStep
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                    }`}
                  >
                    {step.icon}
                    <span>{step.title}</span>
                    {completedSteps[index] && <CheckCircle className="w-3 h-3" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Page Content */}
        <div className="mb-6">
          {renderCurrentPage()}
        </div>

        {/* Navigation */}
        {currentStep > 0 && currentStep < steps.length - 1 && (
          <div className="flex justify-between">
            <Button 
              variant="outline" 
              onClick={handleBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>
            {(currentStep === 1 || currentStep === 5) ? null : (
              <Button 
                onClick={handleNext}
                className="flex items-center gap-2"
              >
                Next
                <ArrowRight className="w-4 h-4" />
              </Button>
            )}
          </div>
        )}
      </div>
      
      {/* Floating Help */}
      <FloatingHelp />
    </div>
  );
};
