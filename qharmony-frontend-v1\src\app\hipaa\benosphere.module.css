/* styles/Benosphere.module.css */

.container {
  max-width: 90%;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'SF Pro', sans-serif;
  color: #fff;
  background-color: #000000;
  width: 100%;
  box-sizing: border-box;
}

.header {
  margin-bottom: 2rem;
  text-align: left;
  width: 100%;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #eeabe1;
  margin-bottom: 1rem;
  font-family: 'SF Pro', sans-serif;
  word-wrap: break-word;
  width: 100%;
}

.subtitle {
  font-size: 1.2rem;
  color: #ccc;
  width: 100%;
  margin: 0 auto 1rem auto;
  font-family: 'SF Pro', sans-serif;
  word-wrap: break-word;
  line-height: 1.5;
}

.link {
  color: #aa75ff;
  text-decoration: none;
  transition: color 0.2s;
}

.link:hover {
  color: #eeabe1;
  text-decoration: underline;
}

.tableContainer {
  background-color: #1e1e1e;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  width: 100%;
  margin-top: 20px;
}

.filterContainer {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1.5rem;
  gap: 0.5rem;
  flex-wrap: wrap;
  width: 100%;
}

.filterButton {
  padding: 0.5rem 1rem;
  background-color: #141414;
  border: 1px solid #262626;
  border-radius: 19px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  color: #fff;
  font-family: 'SF Pro', sans-serif;
}

.filterButton:hover {
  background-color: #333;
}

.filterButtonActive {
  background-image: linear-gradient(#aa75ff 50%,#6138ff);
  color: white;
  border-color: #272025;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: #000;
  font-family: 'SF Pro', sans-serif;
  table-layout: auto;
}

/* Column width adjustments */
.table th:nth-child(1), 
.table td:nth-child(1) {
  width: 40px;
}

.table th:nth-child(2), 
.table td:nth-child(2) {
  width: 25%;
}

.table th:nth-child(3), 
.table td:nth-child(3) {
  width: 15%;
}

.table th:nth-child(4), 
.table td:nth-child(4) {
  width: 60%;
}

.th {
  background-image: linear-gradient(#6138ff 10%,#aa75ff);
  color: white;
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  font-size: 1rem;
  border: 1px solid #333;
  font-family: 'SF Pro', sans-serif;
}

.td {
  padding: 1rem;
  border-bottom: 1px solid #333;
  font-size: 0.95rem;
  vertical-align: middle;
  color: #fff;
  border: 1px solid #333;
  font-family: 'SF Pro', sans-serif;
}

.tr:hover {
  background-color: #222;
}

.tr:nth-child(even) {
  background-color: #0a0a0a;
}

.td.checkmark {
  position: relative;
  text-align: center;
  font-size: 0; /* Hide the text content */
  padding: 0.3rem;
  width: 40px;
  min-width: 40px;
  vertical-align: middle;
}

.td.checkmark::before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #8cc63e;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.td.checkmark::after {
  content: "";
  position: absolute;
  transform: rotate(45deg);
  left: calc(50% - 2.5px);
  top: calc(50% - 5.5px);
  height: 9px;
  width: 4px;
  border-bottom: 2px solid white;
  border-right: 2px solid white;
}

.category {
  color: #eeabe1;
  font-size: 0.9rem;
  font-weight: bold;
  font-family: 'SF Pro', sans-serif;
}

.infoBox {
  background: #0a0a0a;
  border: 1px solid #333;
  border-radius: 10px;
  padding: 1.5rem;
  padding-top: 0.5rem;
  margin: 1.5rem auto;
  width: 100%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.infoBoxHeader {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.infoText {
  font-size: 1.1rem;
  color: #ccc;
  margin-bottom: 1rem;
  line-height: 1.6;
  font-family: 'SF Pro', sans-serif;
  text-align: left;
}

.infoText:last-child {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
    max-width: 100%;
  }
  
  .title {
    font-size: 1.8rem;
  }
  
  .th, .td {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
  
  .filterContainer {
    justify-content: center;
    margin-bottom: 1rem;
  }
  
  .filterButton {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    flex: 0 0 auto;
  }
  
  .tableContainer {
    overflow-x: auto;
  }
  
  .table {
    min-width: 500px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.5rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .filterButton {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
  }
  
  .th, .td {
    padding: 0.4rem;
    font-size: 0.75rem;
  }
  
  .table {
    min-width: 450px;
  }
}

@media (max-width: 768px) {
  .table th:nth-child(1), 
  .table td:nth-child(1) {
    width: 30px;
  }
  
  .table th:nth-child(2), 
  .table td:nth-child(2) {
    width: 30%;
  }
  
  .table th:nth-child(3), 
  .table td:nth-child(3) {
    width: 20%;
  }
  
  .table th:nth-child(4), 
  .table td:nth-child(4) {
    width: 50%;
  }
}

@media (max-width: 480px) {
  .td.checkmark {
    width: 24px;
    min-width: 24px;
  }
  
  .td.checkmark::before {
    width: 14px;
    height: 14px;
  }
  
  .td.checkmark::after {
    height: 6px;
    width: 2.5px;
  }
}

@media (max-width: 768px) {
  .infoBox {
    padding: 1.2rem;
    margin: 1rem auto;
  }
  
  .infoBoxLogo {
    width: 60px;
  }
  
  .infoText {
    font-size: 1rem;
  }
}
  
