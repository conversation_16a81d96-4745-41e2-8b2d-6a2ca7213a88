.main-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #F0F0F0;
  align-items: center;
  padding: 0px 30px 0px 30px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 20px 0;
  padding-top: 20px;
}

.header-title {
  flex-grow: 1;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.view-history-btn {
  padding: 8px 16px;
  background-color: #000;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.view-history-btn:hover {
  background-color: #4a4a4a;
}

.main-content-heading {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    /* margin-top: 10px; */
}

.select-company-dropdown, .select-group-dropdown, .select-message-type-dropdown {
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%; /* Ensure the dropdown container takes the full width */
}

.select-company-dropdown > label,
.select-group-dropdown > label,
.select-message-type-dropdown > label {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-left: 5px;
    display: block; /* Ensure the label is on its own line */
}

.select-adaptive-card-dropdown {
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%; /* Ensure the dropdown container takes the full width */
}

.select-adaptive-card-dropdown > label {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-left: 5px;
    display: block; /* Ensure the label is on its own line */
}

.wysiwyg-editor {
    margin-top: 10px;
    width: 100%;
}

.wysiwyg-editor > label {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-left: 5px;
    display: block;
}

.ql-container {
    height: 200px; /* Set a default height for the editor */
    background-color: #FFFFFF; /* Set the background to white */
    border: 1px solid #CCCCCC; /* Add a border similar to dropdowns */
    border-radius: 4px; /* Match the border radius of dropdowns */
}

.ql-editor {
    background-color: #FFFFFF; /* Ensure the editor content area is white */
}

.send-notifications-container {
    width: 100%;
    margin-top: 20px;
}

.send-notifications-container button {
    width: 100%;
    padding: 12px;
    background-color: #000000;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.send-notifications-container button:hover {
    background-color: #333333;
}

.send-notifications-container button:active {
    background-color: #1a1a1a;
}

.send-notifications-container button:disabled {
    background-color: #666666;
    cursor: not-allowed;
    opacity: 0.7;
}