import functools

from pymongo import MongoClient
from bson import ObjectId
from typing import List, Dict, Optional
import logging
from app.Tools.mongodbTools import get_mongo_collection,get_mongo_db
from langchain.text_splitter import RecursiveCharacterTextSplitter
import uuid
import os
from langchain.schema import Document
from azure.storage.blob import BlobServiceClient
from app.Tools.vectorStore import VectorStore
from pydantic import BaseModel, Field
from typing import Union, List
from app.DataModels.dataModels import PineconeInput
import functools
import time
import asyncio
from config.config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add this at the module level
_cache_registry = {}

# Cache decorator for async functions
def async_lru_cache(maxsize=config.lru_cache_maxsize, ttl=config.lru_cache_ttl):
    """
    Decorator that adds LRU caching with time-based expiration to async functions.
    
    Args:
        maxsize (int): Maximum cache size (number of items)
        ttl (int): Time to live in seconds
    """
    cache = {}
    timestamps = {}
    
    def decorator(func):
        func_name = func.__name__
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Create a cache key from the function arguments
            key = f"{func_name}:{str(args)}:{str(kwargs)}"
            
            # Check if result is in cache and not expired
            current_time = time.time()
            if key in cache and current_time - timestamps[key] < ttl:
                logger.debug(f"Cache hit for {func_name}")
                return cache[key]
            
            # Call the function and cache the result
            try:
                result = await func(*args, **kwargs)
                
                # Only cache successful results (not errors or exceptions)
                if isinstance(result, Exception) or isinstance(result, ValueError):
                    logger.warning(f"Not caching error result from {func_name}: {result}")
                    return result
                
                # Manage cache size (simple LRU implementation)
                if len(cache) >= maxsize:
                    # Remove oldest item
                    oldest_key = min(timestamps.items(), key=lambda x: x[1])[0]
                    cache.pop(oldest_key, None)
                    timestamps.pop(oldest_key, None)
                
                cache[key] = result
                timestamps[key] = current_time
                return result
            except Exception as e:
                # Don't cache exceptions
                logger.warning(f"Exception in cached function {func_name}: {e}")
                raise
        
        # Add a method to clear the cache
        async def clear_cache():
            cache.clear()
            timestamps.clear()
            logger.info(f"Cache cleared for {func_name}")
            
        wrapper.clear_cache = clear_cache
        
        # Register the function in the global registry
        _cache_registry[func_name] = wrapper
        
        return wrapper
    
    return decorator

# Add a function to clear cache for a specific function
async def clear_cache_for_function(func_name):
    """Clear the cache for a specific function by name."""
    if func_name in _cache_registry:
        await _cache_registry[func_name].clear_cache()
        logger.info(f"Cache cleared for function: {func_name}")
        return True
    logger.warning(f"No cached function found with name: {func_name}")
    return False

