
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, CheckCircle, XCircle } from 'lucide-react';

interface Plan {
  name: string;
  type: string;
  cost: number;
  deductible: number;
  features: string[];
  network: string;
  copays: {
    primaryCare: string;
    specialist: string;
    emergency: string;
  };
}

const samplePlans: Plan[] = [
  {
    name: "Anthem PPO 035",
    type: "PPO",
    cost: 82.90,
    deductible: 2000,
    features: ["Large provider network", "No referrals needed", "Out-of-network coverage"],
    network: "Large",
    copays: { primaryCare: "$25", specialist: "$50", emergency: "$200" }
  },
  {
    name: "Kaiser HMO", 
    type: "HMO",
    cost: 65.40,
    deductible: 1500,
    features: ["Integrated care", "Lower costs", "Coordinated care"],
    network: "Kaiser only",
    copays: { primaryCare: "$20", specialist: "$35", emergency: "$150" }
  },
  {
    name: "Blue Cross HSA",
    type: "HDHP",
    cost: 45.20,
    deductible: 3000,
    features: ["HSA eligible", "Lower premium", "Tax advantages"],
    network: "Large",
    copays: { primaryCare: "After deductible", specialist: "After deductible", emergency: "After deductible" }
  }
];

export const PlanComparison = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPlans, setSelectedPlans] = useState<string[]>([]);

  const togglePlanSelection = (planName: string) => {
    setSelectedPlans(prev => 
      prev.includes(planName)
        ? prev.filter(p => p !== planName)
        : prev.length < 3 ? [...prev, planName] : prev
    );
  };

  const filteredPlans = samplePlans.filter(plan => 
    selectedPlans.length === 0 || selectedPlans.includes(plan.name)
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <BarChart className="w-4 h-4" />
          Compare Plans
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart className="w-5 h-5" />
            Medical Plan Comparison
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Plan Selection */}
          <div className="flex gap-2 flex-wrap">
            <span className="text-sm text-muted-foreground">Select plans to compare:</span>
            {samplePlans.map(plan => (
              <Button
                key={plan.name}
                variant={selectedPlans.includes(plan.name) ? "default" : "outline"}
                size="sm"
                onClick={() => togglePlanSelection(plan.name)}
                disabled={!selectedPlans.includes(plan.name) && selectedPlans.length >= 3}
              >
                {plan.name}
              </Button>
            ))}
          </div>

          {/* Comparison Table */}
          <Card>
            <CardHeader>
              <CardTitle>Side-by-Side Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Feature</TableHead>
                    {filteredPlans.map(plan => (
                      <TableHead key={plan.name} className="text-center">
                        <div>
                          <div className="font-medium">{plan.name}</div>
                          <Badge variant="secondary" className="mt-1">{plan.type}</Badge>
                        </div>
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Monthly Cost</TableCell>
                    {filteredPlans.map(plan => (
                      <TableCell key={plan.name} className="text-center">
                        <div className="text-lg font-semibold">${plan.cost}</div>
                        <div className="text-xs text-muted-foreground">per paycheck</div>
                      </TableCell>
                    ))}
                  </TableRow>
                  
                  <TableRow>
                    <TableCell className="font-medium">Annual Deductible</TableCell>
                    {filteredPlans.map(plan => (
                      <TableCell key={plan.name} className="text-center">
                        ${plan.deductible.toLocaleString()}
                      </TableCell>
                    ))}
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Primary Care Visit</TableCell>
                    {filteredPlans.map(plan => (
                      <TableCell key={plan.name} className="text-center">
                        {plan.copays.primaryCare}
                      </TableCell>
                    ))}
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Specialist Visit</TableCell>
                    {filteredPlans.map(plan => (
                      <TableCell key={plan.name} className="text-center">
                        {plan.copays.specialist}
                      </TableCell>
                    ))}
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Emergency Room</TableCell>
                    {filteredPlans.map(plan => (
                      <TableCell key={plan.name} className="text-center">
                        {plan.copays.emergency}
                      </TableCell>
                    ))}
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Provider Network</TableCell>
                    {filteredPlans.map(plan => (
                      <TableCell key={plan.name} className="text-center">
                        {plan.network}
                      </TableCell>
                    ))}
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Referrals Required</TableCell>
                    {filteredPlans.map(plan => (
                      <TableCell key={plan.name} className="text-center">
                        {plan.type === 'HMO' ? 
                          <XCircle className="w-4 h-4 text-red-500 mx-auto" /> : 
                          <CheckCircle className="w-4 h-4 text-green-500 mx-auto" />
                        }
                      </TableCell>
                    ))}
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Annual Cost Calculator */}
          <Card>
            <CardHeader>
              <CardTitle>Annual Cost Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {filteredPlans.map(plan => (
                  <div key={plan.name} className="text-center p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">{plan.name}</h4>
                    <div className="text-2xl font-bold text-blue-600">
                      ${(plan.cost * 26).toFixed(0)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Annual premium (26 paychecks)
                    </div>
                    <div className="text-sm mt-2">
                      + ${plan.deductible.toLocaleString()} max deductible
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
