/**
 * Bulk Enrollment API Service
 * Handles bulk enrollment operations for employee enrollment process
 */

export interface PlanSelection {
  planAssignmentId: string;
  coverageTier: string;
  dependentIds?: string[];
  enrollmentType?: string;
  qualifyingLifeEvent?: {
    eventType: string;
    eventDate: string;
    [key: string]: any;
  };
}

export interface BulkEnrollmentRequest {
  employeeId: string;
  companyId: string;
  employeeClassType?: string;
  planSelections: PlanSelection[];
  effectiveDate?: string;
}

export interface BulkEnrollmentResponse {
  success: boolean;
  message?: string;
  enrollmentIds?: string[];
  calculatedCosts?: Array<{
    planAssignmentId: string;
    monthlyEmployeeAmount: number;
    monthlyEmployerAmount: number;
    monthlyTotalAmount: number;
  }>;
  validationWarnings?: string[];
  summary?: {
    totalEnrollments: number;
    totalMonthlyCost: number;
    hasWarnings: boolean;
  };
  error?: string;
  failedPlan?: string;
  details?: any;
  rollbackPerformed?: boolean;
}

/**
 * Get API base URL from environment
 */
const getApiBaseUrl = (): string => {
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
};

/**
 * Get user ID from localStorage
 */
const getUserId = (): string => {
  return localStorage.getItem('userid1') || localStorage.getItem('userId') || '';
};

/**
 * Get request headers for API calls
 */
const getHeaders = (): Record<string, string> => {
  return {
    'Content-Type': 'application/json',
    'user-id': getUserId(),
  };
};

/**
 * Create bulk enrollment for multiple plans
 * Uses the backend bulk enrollment API with rollback functionality
 */
export const createBulkEnrollment = async (
  request: BulkEnrollmentRequest
): Promise<BulkEnrollmentResponse> => {
  try {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    if (!userId) {
      throw new Error('User ID not found. Please log in again.');
    }

    console.log('🚀 Bulk Enrollment API Request:', {
      url: `${API_BASE_URL}/api/pre-enrollment/employee-enrollments/bulk`,
      userId: userId,
      payload: request
    });

    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/employee-enrollments/bulk`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(request),
    });

    const responseData = await response.json();

    if (response.ok) {
      console.log('✅ Bulk enrollment successful:', responseData);
      return {
        success: true,
        ...responseData
      };
    } else {
      console.error('❌ Bulk enrollment failed:', {
        status: response.status,
        statusText: response.statusText,
        error: responseData
      });

      return {
        success: false,
        error: responseData.error || 'Bulk enrollment failed',
        failedPlan: responseData.failedPlan,
        details: responseData.details,
        rollbackPerformed: responseData.rollbackPerformed || false
      };
    }
  } catch (error) {
    console.error('❌ Bulk enrollment API error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error occurred',
      rollbackPerformed: false
    };
  }
};

/**
 * Helper function to prepare plan selections from enrollment data
 */
export const preparePlanSelections = (
  enrollmentData: any,
  coverageTier: string,
  dependentIds: string[] = []
): PlanSelection[] => {
  const planSelections: PlanSelection[] = [];

  // Helper to add plan if it exists
  const addPlan = (plan: any, planType: string) => {
    if (plan?.id) {
      planSelections.push({
        planAssignmentId: plan.id,
        coverageTier: coverageTier,
        dependentIds: dependentIds,
        enrollmentType: 'Open Enrollment'
      });
      console.log(`✅ Added ${planType} plan to bulk enrollment:`, plan.id);
    }
  };

  // Add each plan type
  addPlan(enrollmentData.dentalPlan, 'Dental');
  addPlan(enrollmentData.visionPlan, 'Vision');
  addPlan(enrollmentData.lifePlan, 'Life Insurance');
  addPlan(enrollmentData.addPlan, 'AD&D');

  console.log('📋 Prepared plan selections for bulk enrollment:', planSelections);
  return planSelections;
};

/**
 * Helper function to get dependents for enrollment
 */
export const getDependentsForEnrollment = async (
  enrollmentData: any,
  coverageTier: string
): Promise<string[]> => {
  if (coverageTier === 'Employee Only') {
    return [];
  }

  let dependents = enrollmentData.dependents || [];

  // Try localStorage if no dependents in enrollment data
  if (dependents.length === 0) {
    const storedDependents = localStorage.getItem('enrollmentDependents');
    if (storedDependents) {
      try {
        const parsedDependents = JSON.parse(storedDependents);
        dependents.push(...parsedDependents);
      } catch (e) {
        console.error('❌ Error parsing stored dependents:', e);
      }
    }
  }

  // If still no dependents, could fetch from API here
  // For now, just return what we have
  const dependentIds = dependents
    .map((dep: any) => dep.id || dep._id || dep.dependentId)
    .filter(Boolean);

  console.log('👥 Dependents for bulk enrollment:', {
    coverageTier,
    dependentCount: dependents.length,
    dependentIds
  });

  return dependentIds;
};

/**
 * Validate bulk enrollment request before sending
 */
export const validateBulkEnrollmentRequest = (
  request: BulkEnrollmentRequest
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!request.employeeId) {
    errors.push('Employee ID is required');
  }

  if (!request.companyId) {
    errors.push('Company ID is required');
  }

  if (!request.planSelections || request.planSelections.length === 0) {
    errors.push('At least one plan selection is required');
  }

  // Validate each plan selection
  request.planSelections?.forEach((selection, index) => {
    if (!selection.planAssignmentId) {
      errors.push(`Plan assignment ID is required for selection ${index + 1}`);
    }
    if (!selection.coverageTier) {
      errors.push(`Coverage tier is required for selection ${index + 1}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Format bulk enrollment response for display
 */
export const formatBulkEnrollmentSummary = (
  response: BulkEnrollmentResponse
): string => {
  if (!response.success) {
    return `Enrollment failed: ${response.error}`;
  }

  const summary = response.summary;
  if (!summary) {
    return 'Enrollment completed successfully';
  }

  return `✅ Enrollment completed successfully!
📊 ${summary.totalEnrollments} plan(s) enrolled
💰 Total monthly cost: $${summary.totalMonthlyCost.toFixed(2)}
${summary.hasWarnings ? '⚠️ Some validation warnings occurred' : ''}`;
};
