'use client';

import React, { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { HiOutlineInformationCircle } from 'react-icons/hi';

export default function SetDatesPage() {
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;
  const planId = params.planId as string;

  const [useGlobalDates, setUseGlobalDates] = useState(true);
  const [waitingPeriod, setWaitingPeriod] = useState('0');
  const [gracePeriod, setGracePeriod] = useState('30');

  const handleSaveAndReturn = () => {
    router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`);
  };

  const handleContinueToReview = () => {
    router.push(`/ai-enroller/manage-groups/company/${companyId}/plans/${planId}/review`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center space-x-2 text-sm text-gray-500">
            <button
              onClick={() => router.push('/ai-enroller')}
              className="hover:text-gray-700"
            >
              Home
            </button>
            <span>›</span>
            <button
              onClick={() => router.push('/ai-enroller/manage-groups/select-company')}
              className="hover:text-gray-700"
            >
              Select Company
            </button>
            <span>›</span>
            <button
              onClick={() => router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`)}
              className="hover:text-gray-700"
            >
              View Plans
            </button>
            <span>›</span>
            <span className="text-gray-900">Define Contributions</span>
            <span>›</span>
            <span className="text-blue-600 font-medium">Set Dates</span>
            <span>›</span>
            <span className="text-gray-400">Review</span>
          </nav>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-6">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Set Enrollment & Active Dates</h1>
            <p className="text-gray-600">Configure dates for Blue Cross Blue Shield PPO for TechCorp Inc.</p>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Step 4 of 5</div>
            <div className="text-xs text-blue-600">Set plan dates</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Plan Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <div className="flex items-center gap-2">
            <HiOutlineInformationCircle className="w-5 h-5 text-blue-600" />
            <span className="font-medium text-blue-900">Plan: Blue Cross Blue Shield PPO for TechCorp Inc.</span>
          </div>
        </div>

        {/* Date Configuration */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">📅 Date Configuration</h2>
          <p className="text-gray-600 mb-6">Set enrollment and plan active dates for this specific plan</p>

          {/* Use Global Company Dates */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <label className="flex items-start gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={useGlobalDates}
                onChange={(e) => setUseGlobalDates(e.target.checked)}
                className="w-5 h-5 text-blue-600 rounded mt-0.5"
              />
              <div>
                <div className="font-medium text-blue-900">Use Global Company Dates</div>
                <div className="text-sm text-blue-700">This plan will use the company-wide enrollment and plan dates</div>
                <div className="flex items-center gap-1 mt-1">
                  <HiOutlineInformationCircle className="w-4 h-4 text-blue-600" />
                </div>
              </div>
            </label>
          </div>

          {/* Date Periods */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Global Enrollment Period</h3>
              <div className="text-sm text-gray-600">November 1 - November 30, 2024</div>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Global Plan Period</h3>
              <div className="text-sm text-gray-600">January 1 - December 31, 2025</div>
            </div>
          </div>
        </div>

        {/* Additional Settings */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Additional Settings</h2>
          <p className="text-gray-600 mb-6">Optional configuration for this plan</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Waiting Period (days)
              </label>
              <input
                type="number"
                value={waitingPeriod}
                onChange={(e) => setWaitingPeriod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="0"
                max="365"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Grace Period (days)
              </label>
              <input
                type="number"
                value={gracePeriod}
                onChange={(e) => setGracePeriod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="0"
                max="90"
              />
            </div>
          </div>
        </div>

        {/* Plan Configuration Complete */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs">✓</span>
            </div>
            <div>
              <div className="font-medium text-green-900">Plan Configuration Complete</div>
              <div className="text-sm text-green-700">You can now return to configure other plans or continue the workflow</div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <button
            onClick={handleSaveAndReturn}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Save & Return to Plans
          </button>
          <button
            onClick={handleContinueToReview}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            Continue to Review
            <span>→</span>
          </button>
        </div>
      </div>
    </div>
  );
}
