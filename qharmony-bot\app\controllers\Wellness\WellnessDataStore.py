from bson import ObjectId
import datetime
from pymongo.collection import Collection
from datetime import datetime

from pymongo.database import Database
from datetime import datetime, timezone
import json

async def store_wellness_data(
    user_id: str,
    company_id: str,
    wellness_predictions: dict,
    user_response: dict,
    db: Database
) -> dict:
    """
    Store or update wellness prediction data for a user in the wellness collection.

    If a document with the given userId and companyId already exists in the wellness collection,
    it will be overwritten. Otherwise, a new document will be created.

    Parameters:
    - user_id (str): The user's ID (matches _id in users collection).
    - company_id (str): The company ID (matches companyId in users collection).
    - wellness_data (dict): The wellness prediction data to store.
    - db (Database): MongoDB database object (e.g., client['prod']).

    Returns:
    - dict: Status, document ID, and message indicating whether the data was updated or inserted.

    Raises:
    - ValueError: If user_id or company_id doesn't match a user in the users collection.
    """
    
    try:
        print(user_response)
        # Access collections from the database
        users_collection = db["users"]
        wellness_collection = db["wellness"]

        # Step 1: Validate user exists in users collection
        user = users_collection.find_one({"_id": ObjectId(user_id), "companyId": company_id})
        if not user:
            raise ValueError(f"No user found with userId {user_id} and companyId {company_id}")

        # Step 3: Prepare the document for the wellness collection
        current_time = datetime.now(timezone.utc).isoformat().replace("+00:00", "Z")


        wellness_document = {
            "userId": user_id,
            "companyId": company_id,
            "predictions": wellness_predictions,
            "user_response": user_response,
            "createdAt": current_time,
            "updatedAt": current_time
        }

        # Step 4: Update or insert the document in the wellness collection
        result = wellness_collection.update_one(
            {"userId": user_id, "companyId": company_id},
            {"$set": wellness_document},
            upsert=True
        )

        # Step 5: Determine if it was an update or insert
        if result.matched_count > 0:
            # Document existed and was updated
            message = f"Wellness data updated for user {user_id}"
            doc_id = str(wellness_collection.find_one({"userId": user_id, "companyId": company_id})["_id"])
            status = "updated"
        else:
            # Document was inserted
            message = f"Wellness data stored for user {user_id}"
            doc_id = str(result.upserted_id)
            status = "inserted"

        return {
            "status": status,
            "document_id": doc_id,
            "message": message
        }
    
    except Exception as e:
        print(e)
        raise e