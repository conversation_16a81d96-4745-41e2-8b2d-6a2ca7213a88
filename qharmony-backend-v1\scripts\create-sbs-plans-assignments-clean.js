const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Schema definitions
const planSchema = new mongoose.Schema({
  planName: String, planCode: String, brokerId: mongoose.Schema.Types.ObjectId, 
  brokerageId: mongoose.Schema.Types.ObjectId, isTemplate: Boolean, coverageType: String, 
  coverageSubTypes: [String], planType: String, metalTier: String, description: String,
  highlights: [String], informativeLinks: [String], benefitDetails: Object, documentIds: [String],
  carrierId: mongoose.Schema.Types.ObjectId, status: String, isActivated: Boolean
}, { timestamps: true });

const planAssignmentSchema = new mongoose.Schema({
  planId: mongoose.Schema.Types.ObjectId, companyId: mongoose.Schema.Types.ObjectId, 
  groupNumber: String, assignmentYear: Number, assignmentExpiry: Date,
  employerContribution: Object, employeeContribution: Object, rateStructure: String, 
  coverageTiers: [Object], planCustomizations: Object, waitingPeriod: Object, 
  eligibleEmployeeClasses: [String], enrollmentType: String, planEffectiveDate: Date, 
  planEndDate: Date, enrollmentStartDate: Date, enrollmentEndDate: Date,
  qualifyingLifeEventWindow: Object, assignedBy: mongoose.Schema.Types.ObjectId, 
  isActive: Boolean, status: String
}, { timestamps: true });

// IDs as ObjectIds
const CARRIER_ID = new mongoose.Types.ObjectId("6851ca25709a91cb2132ad55");
const COMPANY_ID = new mongoose.Types.ObjectId("67bf65bf50bad0a4b3d805ba");
const BROKER_ID = new mongoose.Types.ObjectId("67bf65bf50bad0a4b3d805bc");

async function createSBSPlansAndAssignments() {
  try {
    console.log('🚀 CREATING SBS PLANS & ASSIGNMENTS WITH CORRECT OBJECTIDS');
    console.log('='.repeat(60));
    
    // Connect to production database
    const prodUri = process.env.MONGO_URI.replace('/?retryWrites', '/prod?retryWrites');
    await mongoose.connect(prodUri);
    console.log('✅ Connected to MongoDB PRODUCTION database');
    
    const dbName = mongoose.connection.db.databaseName;
    console.log(`📍 Database: ${dbName}`);
    console.log(`📍 Carrier ID: ${CARRIER_ID}`);
    console.log(`📍 Company ID: ${COMPANY_ID}`);
    console.log(`📍 Broker ID: ${BROKER_ID}`);
    
    // Create models
    const Plan = mongoose.model('Plan', planSchema);
    const PlanAssignment = mongoose.model('PlanAssignment', planAssignmentSchema);
    
    // STEP 1: Create Plans with correct ObjectId references
    console.log('\n📋 STEP 1: Creating Plans...');
    
    const plansData = [
      {
        planName: "SBS Group Term Life Insurance",
        planCode: "P005-Q0008-LIFE",
        brokerId: BROKER_ID,
        brokerageId: COMPANY_ID,
        isTemplate: false,
        coverageType: "Life & Disability Insurance",
        coverageSubTypes: ["Term Life"],
        planType: "Term Life",
        metalTier: null,
        description: "Employer-paid group term life insurance plan offering financial protection for employees' beneficiaries in case of death. Includes waiver of premium, conversion, and accelerated death benefit features.",
        highlights: [
          "$1,700,000 total coverage volume",
          "Rate: $0.190 per $1,000 coverage", 
          "Monthly Premium: $323.00 (fully employer-paid)",
          "Accelerated Death Benefit included",
          "Waiver of Premium and Conversion option"
        ],
        informativeLinks: [],
        benefitDetails: {
          preventiveCareCoinsurance: "0%",
          additionalBenefits: []
        },
        documentIds: [],
        carrierId: CARRIER_ID, // ObjectId reference
        status: "Active",
        isActivated: true
      },
      {
        planName: "SBS Accidental Death & Dismemberment (AD&D)",
        planCode: "P005-Q0008-AD&D",
        brokerId: BROKER_ID,
        brokerageId: COMPANY_ID,
        isTemplate: false,
        coverageType: "Life & Disability Insurance",
        coverageSubTypes: ["Accidental Death & Dismemberment (AD&D)"],
        planType: "Term Life",
        metalTier: null,
        description: "AD&D insurance plan covering accidental death and serious injury, fully paid by employer. Covers the same volume as life insurance with additional AD&D-specific benefits.",
        highlights: [
          "$1,700,000 coverage volume",
          "Rate: $0.023 per $1,000 coverage",
          "Monthly Premium: $39.10",
          "Fully employer-paid",
          "Covers accidental death, dismemberment, paralysis, and other losses"
        ],
        informativeLinks: [],
        benefitDetails: {
          preventiveCareCoinsurance: "0%",
          additionalBenefits: []
        },
        documentIds: [],
        carrierId: CARRIER_ID, // ObjectId reference
        status: "Active",
        isActivated: true
      },
      {
        planName: "SBS Dental PPO Plan",
        planCode: "P005-Q0007",
        brokerId: BROKER_ID,
        brokerageId: COMPANY_ID,
        isTemplate: false,
        coverageType: "Ancillary Benefits",
        coverageSubTypes: ["Dental"],
        planType: "PPO",
        metalTier: null,
        description: "Comprehensive dental PPO plan with 100/80/50 coinsurance, orthodontia, $2,250 annual max, and $50 individual deductible. Fully employee-paid.",
        highlights: [
          "100/80/50 coinsurance (Preventive/Basic/Major)",
          "$2,250 Annual Max Benefit",
          "$50 Individual Deductible / $150 Family",
          "Orthodontia: $2,000 child / $750 adult (cleft only)",
          "12-month waiting period for late entrants"
        ],
        informativeLinks: [],
        benefitDetails: {
          preventiveCareCoinsurance: "0%",
          additionalBenefits: []
        },
        documentIds: [],
        carrierId: CARRIER_ID, // ObjectId reference
        status: "Active",
        isActivated: true
      },
      {
        planName: "SBS Vision PPO Plan",
        planCode: "P005-Q0009",
        brokerId: BROKER_ID,
        brokerageId: COMPANY_ID,
        isTemplate: false,
        coverageType: "Ancillary Benefits",
        coverageSubTypes: ["Vision"],
        planType: "PPO",
        metalTier: null,
        description: "VSP-administered vision PPO plan with $10 eye exam copay, $25 lens copay, and $150 allowance for frames or contacts. Fully employee-paid.",
        highlights: [
          "$10 Eye Exam Copay",
          "$25 Eyewear Copay", 
          "$150 Frame / Contact Lens Allowance",
          "Covered Every 12 Months",
          "Laser and eyewear discounts included"
        ],
        informativeLinks: [],
        benefitDetails: {
          preventiveCareCoinsurance: "0%",
          additionalBenefits: []
        },
        documentIds: [],
        carrierId: CARRIER_ID, // ObjectId reference
        status: "Active",
        isActivated: true
      }
    ];

    const createdPlans = [];
    for (const planData of plansData) {
      // Check if plan already exists
      let plan = await Plan.findOne({ planCode: planData.planCode });
      if (plan) {
        console.log(`⚠️  Plan ${planData.planCode} already exists: ${plan._id}`);
        createdPlans.push(plan);
      } else {
        plan = await Plan.create(planData);
        console.log(`✅ Plan created: ${plan.planName} - ${plan._id}`);
        createdPlans.push(plan);
      }
    }

    // STEP 2: Create Plan Assignments with correct ObjectId references
    console.log('\n📋 STEP 2: Creating Plan Assignments...');
    
    const assignmentsData = [
      {
        planCode: "P005-Q0008-LIFE",
        groupNumber: "GRP-SBS-LIFE",
        employerContribution: { contributionType: "Percentage", contributionAmount: 100 },
        employeeContribution: { contributionType: "Percentage", contributionAmount: 0 },
        coverageTiers: [{
          tierName: "Employee Only",
          totalCost: 323.00,
          employerCost: 323.00,
          employeeCost: 0
        }]
      },
      {
        planCode: "P005-Q0008-AD&D",
        groupNumber: "GRP-SBS-AD&D",
        employerContribution: { contributionType: "Percentage", contributionAmount: 100 },
        employeeContribution: { contributionType: "Percentage", contributionAmount: 0 },
        coverageTiers: [{
          tierName: "Employee Only",
          totalCost: 39.10,
          employerCost: 39.10,
          employeeCost: 0
        }]
      },
      {
        planCode: "P005-Q0007",
        groupNumber: "GRP-SBS-DENTAL",
        employerContribution: { contributionType: "Percentage", contributionAmount: 0 },
        employeeContribution: { contributionType: "Percentage", contributionAmount: 100 },
        coverageTiers: [
          { tierName: "Employee Only", totalCost: 33.16, employerCost: 0, employeeCost: 33.16 },
          { tierName: "Employee & Spouse", totalCost: 67.73, employerCost: 0, employeeCost: 67.73 },
          { tierName: "Employee & Child(ren)", totalCost: 95.73, employerCost: 0, employeeCost: 95.73 },
          { tierName: "Employee & Family", totalCost: 133.81, employerCost: 0, employeeCost: 133.81 }
        ]
      },
      {
        planCode: "P005-Q0009",
        groupNumber: "GRP-SBS-VISION",
        employerContribution: { contributionType: "Percentage", contributionAmount: 0 },
        employeeContribution: { contributionType: "Percentage", contributionAmount: 100 },
        coverageTiers: [
          { tierName: "Employee Only", totalCost: 7.68, employerCost: 0, employeeCost: 7.68 },
          { tierName: "Employee & Spouse", totalCost: 14.99, employerCost: 0, employeeCost: 14.99 },
          { tierName: "Employee & Child(ren)", totalCost: 16.06, employerCost: 0, employeeCost: 16.06 },
          { tierName: "Employee & Family", totalCost: 25.04, employerCost: 0, employeeCost: 25.04 }
        ]
      }
    ];

    const createdAssignments = [];
    for (const assignmentData of assignmentsData) {
      const plan = createdPlans.find(p => p.planCode === assignmentData.planCode);
      if (!plan) {
        console.error(`❌ Plan not found for code: ${assignmentData.planCode}`);
        continue;
      }

      // Check if assignment already exists
      let assignment = await PlanAssignment.findOne({ 
        groupNumber: assignmentData.groupNumber, 
        companyId: COMPANY_ID 
      });

      if (assignment) {
        console.log(`⚠️  Assignment ${assignmentData.groupNumber} already exists: ${assignment._id}`);
        createdAssignments.push(assignment);
      } else {
        const fullAssignmentData = {
          planId: plan._id, // ObjectId reference
          companyId: COMPANY_ID, // ObjectId reference
          groupNumber: assignmentData.groupNumber,
          assignmentYear: 2026,
          assignmentExpiry: new Date("2026-06-30T23:59:59.999Z"),
          employerContribution: assignmentData.employerContribution,
          employeeContribution: assignmentData.employeeContribution,
          rateStructure: "Composite",
          coverageTiers: assignmentData.coverageTiers,
          planCustomizations: { additionalDocuments: [] },
          waitingPeriod: { enabled: false, days: 0, rule: "Immediate" },
          eligibleEmployeeClasses: ["Full-Time"],
          enrollmentType: "Active",
          planEffectiveDate: new Date("2025-07-01T00:00:00.000Z"),
          planEndDate: new Date("2026-06-30T23:59:59.999Z"),
          enrollmentStartDate: new Date("2025-06-17T00:00:00.000Z"),
          enrollmentEndDate: new Date("2025-06-16T23:59:59.999Z"),
          qualifyingLifeEventWindow: {
            enabled: true,
            windowDays: 30,
            allowedEvents: ["Marriage", "Divorce", "Birth", "Adoption", "Loss of Coverage", "Job Change", "Death", "Relocation", "Other"],
            description: "Qualifying life event enrollment window"
          },
          assignedBy: BROKER_ID, // ObjectId reference
          isActive: true,
          status: "Active"
        };
        
        assignment = await PlanAssignment.create(fullAssignmentData);
        console.log(`✅ Assignment created: ${assignment.groupNumber} - ${assignment._id}`);
        createdAssignments.push(assignment);
      }
    }

    // SUMMARY
    console.log('\n🎯 SBS PLANS & ASSIGNMENTS CREATED SUCCESSFULLY!');
    console.log('='.repeat(60));
    console.log(`Database: ${dbName}`);
    console.log(`Carrier ID: ${CARRIER_ID}`);
    console.log(`Company ID: ${COMPANY_ID}`);
    console.log(`Broker ID: ${BROKER_ID}`);
    
    console.log('\n📋 Created Plans:');
    createdPlans.forEach(p => {
      console.log(`  ${p.planCode}: ${p.planName} (${p._id})`);
      console.log(`    Carrier ID: ${p.carrierId} (ObjectId)`);
      console.log(`    Broker ID: ${p.brokerId} (ObjectId)`);
    });
    
    console.log('\n📋 Created Plan Assignments:');
    createdAssignments.forEach(a => {
      console.log(`  ${a.groupNumber}: ${a._id}`);
      console.log(`    Plan ID: ${a.planId} (ObjectId)`);
      console.log(`    Company ID: ${a.companyId} (ObjectId)`);
      console.log(`    Assigned By: ${a.assignedBy} (ObjectId)`);
    });
    
    console.log('\n✅ READY FOR ENROLLMENT:');
    console.log('- Enrollment Period: June 17, 2025 - June 16, 2025 (OPEN NOW!)');
    console.log('- Coverage Period: July 1, 2025 - June 30, 2026');
    console.log('- Life & AD&D: 100% employer paid');
    console.log('- Dental & Vision: 100% employee paid');
    
    console.log('\n🧪 TEST API:');
    console.log(`curl -X GET "http://localhost:8080/api/pre-enrollment/plan-assignments/company/${COMPANY_ID}" -H "user-id: ${BROKER_ID}"`);

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');

  } catch (error) {
    console.error('❌ Script failed:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
}

createSBSPlansAndAssignments();
