'use client';

import { useEffect, useState } from 'react';

export default function TestInfiniteLoopPage() {
  const [requestCount, setRequestCount] = useState(0);
  const [lastRequestTime, setLastRequestTime] = useState<string>('');

  useEffect(() => {
    // Simulate the previous problematic pattern
    const makeRequest = () => {
      setRequestCount(prev => prev + 1);
      setLastRequestTime(new Date().toLocaleTimeString());
      console.log('Simulated API request #', requestCount + 1);
    };

    // This would cause infinite loop (commented out for safety)
    // makeRequest();
  }, [requestCount]); // This dependency would cause infinite loop

  const simulateInfiniteLoop = () => {
    setRequestCount(prev => prev + 1);
  };

  const resetCounter = () => {
    setRequestCount(0);
    setLastRequestTime('');
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🔄 Infinite Loop Test Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>Request Counter</h2>
        <div style={{ 
          background: requestCount > 10 ? '#f8d7da' : '#d4edda', 
          padding: '15px', 
          borderRadius: '5px',
          border: `1px solid ${requestCount > 10 ? '#f5c6cb' : '#c3e6cb'}`
        }}>
          <div><strong>Total Requests:</strong> {requestCount}</div>
          <div><strong>Last Request:</strong> {lastRequestTime || 'None'}</div>
          <div style={{ marginTop: '10px', fontSize: '14px' }}>
            {requestCount > 10 ? (
              <span style={{ color: '#721c24' }}>⚠️ High request count detected!</span>
            ) : (
              <span style={{ color: '#155724' }}>✅ Normal request count</span>
            )}
          </div>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Test Actions</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={simulateInfiniteLoop}
            style={{ 
              padding: '10px 20px', 
              background: '#ffc107', 
              color: 'black', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Simulate Request
          </button>
          
          <button 
            onClick={resetCounter}
            style={{ 
              padding: '10px 20px', 
              background: '#6c757d', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Reset Counter
          </button>
        </div>
      </div>

      <div style={{ marginTop: '30px', padding: '20px', background: '#e9ecef', borderRadius: '5px' }}>
        <h3>🐛 Common Infinite Loop Causes</h3>
        <ul>
          <li><strong>useEffect dependency issues:</strong> Function recreated on every render</li>
          <li><strong>Missing useCallback:</strong> Functions in dependency arrays</li>
          <li><strong>State updates in useEffect:</strong> Causing re-renders</li>
          <li><strong>API calls without proper memoization</strong></li>
        </ul>
        
        <h4>✅ Solutions Applied:</h4>
        <ul>
          <li>Added <code>useCallback</code> to <code>fetchCompanies</code> function</li>
          <li>Added proper dependencies to useEffect</li>
          <li>Added conditional checks to prevent unnecessary API calls</li>
          <li>Added error handling to prevent failed requests from retrying</li>
        </ul>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', background: '#d1ecf1', borderRadius: '5px', border: '1px solid #bee5eb' }}>
        <h4>🔍 How to Debug:</h4>
        <ol>
          <li>Open browser DevTools → Network tab</li>
          <li>Look for repeated requests to same endpoints</li>
          <li>Check React DevTools for component re-renders</li>
          <li>Add console.logs to useEffect hooks</li>
          <li>Check useEffect dependency arrays</li>
        </ol>
      </div>
    </div>
  );
}
