version: '3.7'
services:
  redis:
    image: redis:latest
    command: redis-server --requirepass qharmony --protected-mode no
    ports:
      - target: 6379
        published: 6379
        protocol: tcp
        mode: host
    volumes:
      - redis_data_container:/data

  mongo:
    image: mongo:latest
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=examplepassword
    ports:
      - target: 27017
        published: 27017
        protocol: tcp
        mode: host
    volumes:
      - mongo_data_container:/data/db

volumes:
  redis_data_container:
  mongo_data_container:
