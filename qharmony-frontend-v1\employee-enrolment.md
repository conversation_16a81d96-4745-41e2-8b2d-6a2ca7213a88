# 🧠 AI Assistant Prompt: Employee Enrollment Plan Assignment & Flow Handling

This document outlines the context, responsibilities, and logic expected from the AI assistant to help manage the employee enrollment workflow in the QHarmony platform.

---

## 🔧 System Context

### 📦 Existing Functionality

- Brokers can **create plan templates** (e.g., Medical, Dental, Vision)
- Brokers assign these templates to **companies** (either broker-owned or other companies)
- The **assigned plans** appear under various **coverage subtypes**
- The system UI uses a **step-based enrollment navigation**:


---

## 🎯 Goal of the AI Assistant

The assistant will dynamically:

1. **Display plan assignment pages** based on available `coverageSubTypes`
2. **Filter and show only relevant plans** on each page (e.g., only Medical plans on Medical page)
3. **Validate enrollment eligibility** and show inline guidance/errors
4. **Create enrollments** for employees using available plan assignments
5. **Interact with the backend APIs** in a structured and limited manner (see below)
6. Handle **navigation logic**, **step-skipping**, and **smart fallbacks**

---

## 🛠️ API Access Info

For now, use the following controller:

📁 `qharmony-backend-v1/src/controllers/employeeEnrollment.controller.ts`  
✅ Only **limited APIs are currently available**. As of now, only the **Create Enrollment API** is active and should be used.  
📌 Other endpoints like eligibility checks, updates, or termination will be implemented later.

---

## 🗂️ Plan Assignment Page Logic

### 1. 🔍 Fetch Plan Assignments
- Pull assigned plans for the company via internal API or service
- Structure by `coverageSubType` (e.g., `Medical`, `Dental`, etc.)

### 2. 🧭 Navigation Rendering
- Dynamically render plan pages based on available subtypes
- If no plans under a subtype, skip that step
- Example:
- If only `Medical` and `Dental` are assigned → skip `Vision` step

### 3. ✅ Eligibility Checks (manual for now or mocked)
- Only allow showing plans where:
- `status === 'Active'`
- `current date` is within `enrollmentStartDate` and `enrollmentEndDate`
- (More validations will be enforced once `check-eligibility` API is available)

### 4. 💰 Cost Display
- Use plan metadata like:
- `rateStructure`, `coverageTiers`, `ageBandedRates`, `salaryBasedRates`
- Display premium cost breakdown
- Do not calculate final cost yet — this will be added once cost APIs are connected

### 5. 🧒 Dependents Reference Handling
- Use new architecture:
- Store dependent references using `dependentIds[]`
- Enrollment holds metadata and snapshot
- Validate required dependent fields (age, status, relationship)

---

## 💡 Smart UI/UX Guidelines

- Show fallback message if no plans exist under a subtype (e.g., “No dental plans assigned to your company.”)
- Disable "Next" if no plan is selected and the step is required
- Auto-skip steps without eligible plans
- Pre-fill dependent data from `User.details.dependents[]`

---

## 📌 Summary

| Feature                     | Current Status | Future Scope                        |
|----------------------------|----------------|-------------------------------------|
| Plan Assignment Pages      | ✅ Implement    | Enhance with eligibility filtering  |
| Create Enrollment API      | ✅ Available    | Continue using                      |
| Eligibility Validation API | ❌ Not Yet      | To be added                         |
| Cost Calculation API       | ❌ Not Yet      | To be added                         |
| Dependent Snapshot Support | ✅ Implemented  | Enhance with dynamic snapshoting    |
| Step Navigation            | ✅ Dynamic      | Add conditions based on API results |

---

## 🧩 Next Steps

- Start with dynamic plan rendering using assigned subtypes
- Implement enrollment creation with:
- Selected `planAssignmentId`
- Referenced `dependentIds[]`
- Relevant user metadata
- Gradually add `check-eligibility` and `calculate-cost` integrations once ready

---

> ⚠️ **Note:** Treat current functionality as read-only except for `Create Enrollment`. Avoid assumptions based on future API availability until explicitly integrated.

