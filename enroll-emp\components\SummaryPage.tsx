import React from 'react';
import { BotQuestion } from './BotQuestion';
import { UserProfile } from '../page';

interface SummaryPageProps {
  userProfile: UserProfile;
  onConfirm: () => void;
  onEdit: () => void;
}

export const SummaryPage = ({ userProfile, onConfirm, onEdit }: SummaryPageProps) => {
  const calculateTotal = () => {
    let total = 0;
    if (userProfile.selectedMedical) total += userProfile.selectedMedical.cost || 0;
    if (userProfile.selectedDental) total += userProfile.selectedDental.cost || 0;
    if (userProfile.selectedVision) total += userProfile.selectedVision.cost || 0;
    if (userProfile.selectedPetInsurance) total += userProfile.selectedPetInsurance.cost || 0;
    if (userProfile.selectedHospitalIndemnity) total += userProfile.selectedHospitalIndemnity.cost || 0;
    return total.toFixed(2);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <BotQuestion 
        question="Perfect! Here's your complete benefits package."
        context="Review your selections and confirm when you're ready to enroll."
      />

      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-6">Your Benefits Summary</h3>
        
        <div className="space-y-4">
          {userProfile.selectedMedical && (
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-semibold">Medical: {userProfile.selectedMedical.name}</h4>
                  <p className="text-sm text-gray-600">Deductible: ${userProfile.selectedMedical.deductible}</p>
                </div>
                <p className="text-lg font-bold text-green-600">${userProfile.selectedMedical.cost}/paycheck</p>
              </div>
            </div>
          )}

          {userProfile.selectedDental && (
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-semibold">Dental: {userProfile.selectedDental.name}</h4>
                  <p className="text-sm text-gray-600">{userProfile.selectedDental.coverage}</p>
                </div>
                <p className="text-lg font-bold text-green-600">${userProfile.selectedDental.cost}/paycheck</p>
              </div>
            </div>
          )}

          {userProfile.selectedVision && (
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-semibold">Vision: {userProfile.selectedVision.name}</h4>
                  <p className="text-sm text-gray-600">{userProfile.selectedVision.coverage}</p>
                </div>
                <p className="text-lg font-bold text-green-600">${userProfile.selectedVision.cost}/paycheck</p>
              </div>
            </div>
          )}

          {userProfile.selectedPetInsurance && (
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-semibold">Pet Insurance: {userProfile.selectedPetInsurance.name}</h4>
                  <p className="text-sm text-gray-600">{userProfile.selectedPetInsurance.description}</p>
                </div>
                <p className="text-lg font-bold text-green-600">${userProfile.selectedPetInsurance.cost}/paycheck</p>
              </div>
            </div>
          )}

          {userProfile.selectedHospitalIndemnity && (
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-semibold">Hospital Indemnity: {userProfile.selectedHospitalIndemnity.name}</h4>
                  <p className="text-sm text-gray-600">{userProfile.selectedHospitalIndemnity.description}</p>
                </div>
                <p className="text-lg font-bold text-green-600">${userProfile.selectedHospitalIndemnity.cost}/paycheck</p>
              </div>
            </div>
          )}
        </div>

        <div className="mt-6 pt-4 border-t">
          <div className="flex justify-between items-center mb-6">
            <h4 className="text-xl font-semibold">Total Cost Per Paycheck:</h4>
            <p className="text-2xl font-bold text-green-600">${calculateTotal()}</p>
          </div>

          <div className="flex gap-4">
            <button
              onClick={onEdit}
              className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Make Changes
            </button>
            <button
              onClick={onConfirm}
              className="flex-1 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
            >
              Confirm Enrollment
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
