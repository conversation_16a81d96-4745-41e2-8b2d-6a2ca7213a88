'use client';

import { useState } from 'react';
import { bulkWaiveEnrollments } from '../ai-enroller/employee-enrol/services/bulkWaiveApi';

export default function TestBulkWaivePage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const getUserId = () => {
    const primaryKey = process.env.NEXT_PUBLIC_USER_ID_KEY || "userid1";
    const altKey = process.env.NEXT_PUBLIC_USER_ID_ALT_KEY || "userId";
    return localStorage.getItem(primaryKey) || localStorage.getItem(altKey);
  };

  const testBulkWaive = async () => {
    setLoading(true);
    const userId = getUserId();
    
    console.log('🧪 Testing bulk waive with user ID:', userId);
    
    if (!userId) {
      alert('No user ID found. Please log in first.');
      setLoading(false);
      return;
    }

    // Test with dummy plan assignment IDs
    const testPlanAssignmentIds = ['test-assignment-1', 'test-assignment-2'];
    
    try {
      const result = await bulkWaiveEnrollments({
        employeeId: userId,
        planAssignmentIds: testPlanAssignmentIds,
        waiveReason: 'Test waive reason',
        enrollmentType: 'Open Enrollment'
      });

      console.log('🧪 Test bulk waive result:', result);
      
      setTestResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        userId,
        planAssignmentIds: testPlanAssignmentIds,
        result,
        success: result.success
      }]);
    } catch (error) {
      console.error('🧪 Test bulk waive error:', error);
      setTestResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        userId,
        planAssignmentIds: testPlanAssignmentIds,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      }]);
    }
    
    setLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const checkLocalStorage = () => {
    const keys = ['userid1', 'userId', 'dentalWaived', 'visionWaived', 'lifeWaived', 'dentalWaiveReason', 'visionWaiveReason', 'lifeWaiveReason'];
    const values: any = {};
    
    keys.forEach(key => {
      values[key] = localStorage.getItem(key);
    });
    
    console.log('🔍 LocalStorage values:', values);
    alert(`LocalStorage values:\n${JSON.stringify(values, null, 2)}`);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '1000px', margin: '0 auto' }}>
      <h1>🧪 Bulk Waive API Test Page</h1>
      
      <div style={{ marginBottom: '20px', padding: '15px', background: '#f0f8ff', borderRadius: '5px', border: '1px solid #b0d4f1' }}>
        <h2>Test Controls</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={testBulkWaive}
            disabled={loading}
            style={{ 
              padding: '10px 20px', 
              background: loading ? '#ccc' : '#007bff', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Testing...' : 'Test Bulk Waive API'}
          </button>
          
          <button 
            onClick={checkLocalStorage}
            style={{ 
              padding: '10px 20px', 
              background: '#28a745', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Check LocalStorage
          </button>
          
          <button 
            onClick={clearResults}
            style={{ 
              padding: '10px 20px', 
              background: '#dc3545', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Clear Results
          </button>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Current User Info</h2>
        <div style={{ padding: '10px', background: '#f8f9fa', borderRadius: '5px', border: '1px solid #dee2e6' }}>
          <div><strong>User ID:</strong> {getUserId() || 'Not found'}</div>
          <div><strong>API Base URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}</div>
        </div>
      </div>

      <div>
        <h2>Test Results ({testResults.length})</h2>
        {testResults.length === 0 ? (
          <p style={{ color: '#666', fontStyle: 'italic' }}>No test results yet. Click "Test Bulk Waive API" to run a test.</p>
        ) : (
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {testResults.map((result, index) => (
              <div 
                key={index} 
                style={{ 
                  marginBottom: '15px', 
                  padding: '15px', 
                  background: result.success ? '#d4edda' : '#f8d7da',
                  border: `1px solid ${result.success ? '#c3e6cb' : '#f5c6cb'}`,
                  borderRadius: '5px'
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                  <strong>Test #{index + 1}</strong>
                  <span style={{ fontSize: '12px', color: '#666' }}>{result.timestamp}</span>
                </div>
                
                <div style={{ marginBottom: '10px' }}>
                  <div><strong>User ID:</strong> {result.userId}</div>
                  <div><strong>Plan Assignment IDs:</strong> {result.planAssignmentIds.join(', ')}</div>
                  <div><strong>Success:</strong> {result.success ? '✅ Yes' : '❌ No'}</div>
                </div>
                
                {result.result && (
                  <div style={{ marginBottom: '10px' }}>
                    <strong>API Response:</strong>
                    <pre style={{ 
                      background: '#f8f9fa', 
                      padding: '10px', 
                      borderRadius: '3px', 
                      fontSize: '12px',
                      overflow: 'auto',
                      maxHeight: '200px'
                    }}>
                      {JSON.stringify(result.result, null, 2)}
                    </pre>
                  </div>
                )}
                
                {result.error && (
                  <div style={{ color: '#721c24' }}>
                    <strong>Error:</strong> {result.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      <div style={{ marginTop: '30px', padding: '20px', background: '#e9ecef', borderRadius: '5px' }}>
        <h3>🔍 Debugging Steps</h3>
        <ol>
          <li><strong>Check User Authentication:</strong> Make sure you're logged in and have a valid user ID</li>
          <li><strong>Check Plan Assignments:</strong> Verify that plan assignments exist for the company</li>
          <li><strong>Check API Endpoint:</strong> Ensure the bulk waive API endpoint is working</li>
          <li><strong>Check Request Format:</strong> Verify the request body matches backend expectations</li>
          <li><strong>Check Backend Logs:</strong> Look at server logs for detailed error information</li>
        </ol>
        
        <h4>Expected API Request Format:</h4>
        <pre style={{ 
          background: '#f8f9fa', 
          padding: '10px', 
          borderRadius: '3px', 
          fontSize: '12px',
          overflow: 'auto'
        }}>
{`POST /api/pre-enrollment/employee-enrollments/bulk-waive
{
  "employeeId": "user123",
  "planAssignmentIds": ["assignment1", "assignment2"],
  "waiveReason": "Coverage not needed",
  "waiveDate": "2024-01-01T00:00:00.000Z",
  "enrollmentType": "Open Enrollment"
}`}
        </pre>
      </div>
    </div>
  );
}
