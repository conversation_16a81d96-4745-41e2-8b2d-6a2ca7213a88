import express from 'express';
import logger from '../utils/logger';
import GroupModelClass from '../nosql/group.model';
import UserModelClass from '../nosql/user.model';
import CompanyModelClass from '../nosql/company.model';
import axios from 'axios';
import NotificationAnalyticsModelClass from '../nosql/notification-analytics.model';
import NotificationModelClass from '../nosql/notification.model';
import { countUsersByCompany } from '../utils/utils';
import mongoose from 'mongoose';
import { parse } from 'json2csv'; // Import the json2csv module
import getCurrentGitBranch from '../utils/getCurrentGitBranch';

export interface User {
  email: string;
  companyName: string;
  tenantId: string;
}

class GroupController {
  public router = express.Router();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post('/group', this.createGroup);
    this.router.get('/group-detail/:groupId', this.getGroupById);
    this.router.get('/groups/:companyId', this.getGroupsByCompanyId);
    this.router.post('/group/update-group', this.updateGroupDetails);
    this.router.get('/company/get-all-companies', this.getAllCompanies);
    this.router.get('/groups-by-company-ids', this.getAllGroups);
    this.router.post('/send-group-notifications', this.sendNotificationsByGroups);
    this.router.post('/notification-actions', this.notificationsActions);
    this.router.get('/notifications', this.getAllNotifications); // Added route for getAllNotifications
    this.router.get('/notifications-analytics', this.getNotificationAnalytics); // Added route for analytics
    this.router.get('/download-notifications-analytics', this.getNotificationAnalyticsCSVDownloader); // Added route for analytics
  }

  /**
   * Creates a new group for a company.
   */
  private createGroup = async (req: express.Request, res: express.Response) => {
    try {
      const { companyId, name } = req.body;
      if (!companyId || !name) {
        return res
          .status(400)
          .json({ error: 'Company ID and Name are required' });
      }

      const groupId = await GroupModelClass.createGroup(companyId, name);
      if (!groupId) {
        return res.status(500).json({ error: 'Failed to create group' });
      }

      res.status(201).json({ message: 'Group created successfully', groupId });
      return;
    } catch (error) {
      logger.error('Error creating group:', error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  /**
   * Retrieves a single group by its ID.
   */
  private getGroupById = async (
    req: express.Request,
    res: express.Response
  ) => {
    try {
      const { groupId } = req.params;

      const group = await GroupModelClass.getGroupById(groupId);
      if (!group) {
        return res.status(404).json({ error: 'Group not found' });
      }

      res.status(200).json({ group });
      return;
    } catch (error) {
      logger.error(
        `Error retrieving group with ID ${req.params.groupId}:`,
        error
      );
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  /**
   * Retrieves all groups for a given company ID.
   */
  private getGroupsByCompanyId = async (
    req: express.Request,
    res: express.Response
  ) => {
    try {
      const { companyId } = req.params;

      const groups = await GroupModelClass.getGroupsByCompanyId(companyId);
      if (!groups.length) {
        return res
          .status(404)
          .json({ error: 'No groups found for this company' });
      }

      res.status(200).json({ groups });
      return;
    } catch (error) {
      logger.error(
        `Error retrieving groups for company ID ${req.params.companyId}:`,
        error
      );
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private updateGroupDetails = async (
    req: express.Request,
    res: express.Response
  ) => {
    try {
      const { groupId, documentIds, employeeIds } = req.body;

      if (!Array.isArray(documentIds) || !Array.isArray(employeeIds)) {
        return res
          .status(400)
          .json({ error: 'documentIds and employeeIds must be arrays' });
      }

      // Step 1: Get current group data to determine employees being removed
      const currentGroup = await GroupModelClass.getGroupById(groupId);
      if (!currentGroup) {
        return res.status(404).json({ error: 'Group not found' });
      }

      const previousEmployeeIds: string[] = currentGroup.employee_ids || [];

      // Step 2: Determine removed employees (users no longer part of the group)
      const removedEmployeeIds = previousEmployeeIds.filter(
        (id) => !employeeIds.includes(id)
      );

      // Step 3: Update the group details with new employee and document lists
      const updateResult = await GroupModelClass.updateGroupDetails(
        groupId,
        documentIds,
        employeeIds
      );

      if (!updateResult?.modifiedCount) {
        return res.status(400).json({ error: 'No changes made to the group' });
      }

      // Step 4: Update Users Collection
      // 4A: Add groupId to new employees while ensuring `groupIds` is an array
      await UserModelClass.addGroupToUsers(employeeIds, groupId);

      // 4B: Remove groupId from employees no longer part of the group
      if (removedEmployeeIds.length > 0) {
        await UserModelClass.removeGroupFromUsers(removedEmployeeIds, groupId);
      }

      res.status(200).json({ message: 'Group updated successfully' });
      return;
    } catch (error) {
      logger.error(`Error updating group with ID ${req.body.groupId}:`, error);
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  // =========================================================================
  //  THIS CONTROLLER IS RESPONSIBLE FOR FETCHING ALL OF THE COMPANIES WHERE 
  //  TENANT ID IS PRESENT.
  // =========================================================================


  private async getAllCompanies(req: express.Request,
    res: express.Response): Promise<void> {
    console.log(req.body)
    try {
      const companies = await CompanyModelClass.getCompaniesWithTenantIdAndUserCount();
      res.status(200).json({ success: true, data: companies });
    } catch (error) {
      logger.error('Error fetching companies:', error);
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  // ============================================================================
  // THIS CONTROLLER IS RESPONSIBLE FOR FETCHING ALL OF THE GROUPS BY COMPANY IDS
  // ============================================================================

  private getAllGroups = async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      console.log("body", req.body);

      const companies = (await CompanyModelClass.getCompaniesWithTenantId()).map(company => company._id!.toString())

      // Fetch groups using the model method
      const groups = (await GroupModelClass.getAllGroupsByCompanyId(companies))
        .filter(group => group.employee_ids.length !== 0)
        .map(group => ({ _id: group._id, name: group.name, companyId: group.companyId, userCount: group.employee_ids.length }))

      res.status(200).json({ success: true, data: { groups } });
    } catch (error) {
      logger.error('Error fetching groups by company IDs:', error);
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  };

  // =========================================================================
  //  THIS CONTROLLER IS RESPONSIBLE FOR SENDING NOTIFICATIONS TO THE SELECTED
  //  GROUPS AND COMPANIES. THE NOTIFICATIONS WILL BE SENT TO THE USERS OF THOSE
  //  GROUPS AND COMPANIES WHICH ARE SELECTED BY THE ADMIN.
  // =========================================================================

  private sendNotificationsByGroups = async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      let { selectedGroups, body_type, message, selectedCompanies, messageText } = req.body;

      // console.log("body >>>", JSON.stringify(req.body, null, 2))

      if (!body_type || !message) {
        res.status(400).json({ success: false, message: "required data is missing" });
      }

      // =========================================================================
      //  THIS IS THE MAIN USERS ARRAY WHERE I AM STORING THE USERS WHO WILL
      //  RECEIVE THE NOTIFICATIONS.
      // =========================================================================

      let users: User[] = []

      // =========================================================================
      // CASE 1: IF NO GROUPS SELECTED, THEN WE SEND NOTIFICATIONS TO ALL OF THE
      // USERSOF THE SELECTED COMPANIES
      // =========================================================================

      if (selectedGroups.length === 0) {
        console.log("sending notifications to all of the users of the selected companies")
        const companyIds =
          selectedCompanies.map((company: any) => company._id)

        users.push(...await UserModelClass.getUsersByCompanyIdsWithTenantId(companyIds))

        // console.log("all users of the selected companies >>", users)

      } else {
        // =========================================================================
        // CASE 2: IF GROUPS AND COMPANIES ARE SELECTED THEN SEND NOTIFICATIONS TO 
        // ALL OF THE USERS ASSOCIATED WITH THOSE GROUPS.
        // =========================================================================


        // =========================================================================
        // CASE 2A: IF ANY OF THE SELECTED COMPANIES HAVE NO GROUPS THEN WE SEND
        // NOTIFICATIONS TO ALL OF THE USERS OF THAT ORGANIZATION
        // =========================================================================

        const companiesWithoutGroup =
          (selectedCompanies.filter((company: any) => company.groupIds.length === 0))
            .map((company: any) => company._id)

        console.log("companies without group >>", JSON.stringify(companiesWithoutGroup, null, 2))

        if (companiesWithoutGroup.length > 0) {
          users.push(...await UserModelClass.getUsersByCompanyIdsWithTenantId(companiesWithoutGroup))
        }

        // =========================================================================
        // CASE 2B: IF COMPANY AND ITS ASSOCAITED GROUPS ARE SELECTED THEN WE SEND
        // NOTIFICATIONS TO ALL OF THE USERS ASSOCIATED WITH THOSE GROUPS ONLY.
        // =========================================================================


        // console.log("users of the companies without groups >>", users)

        const groupIds = selectedGroups.map((group: any) => group._id)

        // console.log("groupIDS >>>", groupIds)

        const employeeIds = (await GroupModelClass.getGroupsByIds(groupIds))
          .map((group: any) => group.employee_ids).flat()

        // console.log("employeeIds >>", employeeIds)

        users.push(...await UserModelClass.getUsersByIdsWithTenantId(employeeIds))

      }

      // // Split users into batches of 50
      // const batchSize = 50;
      // const userBatches = [];
      // for (let i = 0; i < users.length; i += batchSize) {
      //   userBatches.push(users.slice(i, i + batchSize));
      // }

      // let adaptiveCardApiEndpoint =
      //   process.env.ADAPTIVE_CARD_API_ENDPOINT ||
      //   "https://qharmony-teams-bot-dev.azurewebsites.net/api/notification"

      // let adaptiveCardApiKey =
      //   process.env.ADAPTIVE_CARD_API_KEY || "QmVub3NwaGVyZVRlYW1zQm90OkltcG9ydGFudFBhc3N3b3JkOjFbNHhCXTloQ2l8JA"

      // // Send each batch asynchronously
      // await Promise.all(userBatches.map(async (batch) => {
      //   const adaptiveCardApiPayload = { users: batch, body_type, message };
      //   console.log("Sending batch:", JSON.stringify(adaptiveCardApiPayload, null, 2));

      //   await axios.post(adaptiveCardApiEndpoint, adaptiveCardApiPayload, {
      //     headers: {
      //       API_KEY: adaptiveCardApiKey,
      //       'Content-Type': 'application/json'
      //     }
      //   });
      // }));

      // Extract button titles from the message (assuming it's an adaptive card)
      let buttons: string[] = [];
      try {
        // No need to parse, message is already an object
        const messageObj = typeof message === 'string' ? JSON.parse(message) : message;

        // Find the ActionSet in the body
        const actionSet = messageObj.body?.find((item: any) => item.type === 'ActionSet');

        // Extract titles from actions
        buttons = actionSet?.actions?.map((action: any) => action.data.messageType) || [];
      } catch (error) {
        logger.error("Error extracting button titles:", error);
        buttons = [];
      }

      // Store notification in MongoDB
      const notificationId = await NotificationModelClass.createNotification({
        message: messageText,
        sentToOrgs: selectedCompanies.map((company: any) => company.name),
        sentToGroups: selectedGroups.map((group: any) => group.name),
        noOfUsersSentTo: countUsersByCompany(users),
        totalUserCount: users.length,
        buttons
      });

      if (!notificationId) {
        logger.error("Failed to store notification in database");
        res.status(500).json({ success: false, message: 'Failed to store notification in database' });
        return;
      }


      message = JSON.parse(
        JSON.stringify(message).replace(/{{MESSAGE_ID}}/g, notificationId)
      );

      const branch = await getCurrentGitBranch();

      console.log("branch >>>", branch)

      let adaptiveCardApiEndpoint =
        branch === "main" ?
          "https://benosphere.azurewebsites.net/api/notification"
          : "https://qharmony-teams-bot-dev.azurewebsites.net/api/notification"

      console.log("adaptiveCardApiEndpoint >>>", adaptiveCardApiEndpoint)

      let adaptiveCardApiKey =
        process.env.ADAPTIVE_CARD_API_KEY || "QmVub3NwaGVyZVRlYW1zQm90OkltcG9ydGFudFBhc3N3b3JkOjFbNHhCXTloQ2l8JA"

      const adaptiveCardApiPayload = { users, body_type, message };

      console.log("Sending batch:", JSON.stringify(adaptiveCardApiPayload, null, 2));

      // Send notification via Teams bot
      await axios.post(adaptiveCardApiEndpoint, adaptiveCardApiPayload, {
        headers: {
          API_KEY: adaptiveCardApiKey,
          'Content-Type': 'application/json'
        }
      });

      res
        .status(200)
        .json({
          success: true,
          message: "notifications sent successfully.",
          notificationId
        });
    } catch (error) {
      console.error("Error sending notifications:", error);
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  };

  /**
   * Handles the button click analytics from adaptive cards
   * @param req - Express request object
   * @param res - Express response object
   */
  private notificationsActions = async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { messageId, messageType, useremail, tenantId } = req.body;

      // Validate required fields
      if (!messageId || !messageType || !useremail || !tenantId) {
        res.status(400).json({
          success: false,
          message: "Missing required fields. Required: messageId, messageType, useremail, tenantId"
        });
        return;
      }

      // Fetch the company data using tenantId
      const companyData = await CompanyModelClass.getCompanyByTenantId(tenantId);

      if (!companyData) {
        res.status(404).json({
          success: false,
          message: "Company not found for the provided tenantId"
        });
        return;
      }

      // Create analytics entry
      const analyticsId = await NotificationAnalyticsModelClass.createAnalytics({
        messageId,
        messageType,
        useremail,
        tenantId,
        companyName: companyData.name, // Add companyName from the fetched company data
        // @ts-ignore
        companyId: companyData._id.toString() // Add companyId from the fetched company data
      });

      if (!analyticsId) {
        res.status(500).json({
          success: false,
          message: "Failed to save button click analytics"
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: "Button click analytics saved successfully",
        analyticsId
      });
    } catch (error) {
      logger.error("Error saving button click analytics:", error);
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  };

  /**
   * Retrieves all notifications sorted by the createdAt date in descending order.
   */
  private getAllNotifications = async (req: express.Request, res: express.Response): Promise<void> => {
    console.log("body", req.body)
    try {
      const notifications = await NotificationModelClass.getAllNotifications();

      if (!notifications || notifications.length === 0) {
        res.status(404).json({ success: false, message: 'Notifications not found' });
        return;
      }

      res.status(200).json({ success: true, data: notifications });
      return;
    } catch (error) {
      logger.error("Error fetching all notifications:", error);
      res.status(500).json({ success: false, message: 'Internal server error' });
      return;
    }
  };

  /**
   * Fetches analytics for a notification cycle.
   * @param req - Express request object
   * @param res - Express response object
   */
  private getNotificationAnalytics = async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { messageId } = req.query;

      // Validate required fields
      if (!messageId) {
        res.status(400).json({
          success: false,
          message: "Missing required field: messageId"
        });
        return;
      }

      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(messageId as string)) {
        res.status(400).json({
          success: false,
          message: "Invalid messageId format. Must be a 24-character hex string."
        });
        return;
      }

      // Query the notifications collection using the messageId
      const notificationId = new mongoose.Types.ObjectId(messageId as string);
      const notification = await NotificationModelClass.getNotificationById(notificationId);

      if (!notification) {
        res.status(404).json({
          success: false,
          message: "Notification not found"
        });
        return;
      }

      // Fetch company details based on company names
      const companyNames = notification.noOfUsersSentTo.map((company) => company.companyName);
      const companies = await CompanyModelClass.getCompaniesByNames(companyNames);

      if (!companies || companies.length === 0) {
        res.status(404).json({
          success: false,
          message: "No companies found for the notification"
        });
        return;
      }

      // Map tenantId to companies
      const companiesWithTenantId = companies.map((company) => ({
        companyName: company.name,
        tenantId: company.tenantId,
        userCount: notification.noOfUsersSentTo.find((c) => c.companyName === company.name)?.userCount || 0
      }));

      // Fetch analytics for each company based on messageId and tenantId
      const analyticsResults = await Promise.all(
        companiesWithTenantId.map(async (company) => {
          const analytics = await NotificationAnalyticsModelClass.getAnalyticsByMessageIdAndTenantId(
            messageId as string,
            company.tenantId
          );
          return { company, analytics };
        })
      );

      // Build the analytics response
      const analytics = analyticsResults.map(({ company, analytics }) => {
        // Aggregate click analytics data for the company
        const clickAnalytics = analytics.reduce((acc, entry) => {
          const { messageType } = entry;
          acc[messageType] = (acc[messageType] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        // Add missing buttons from the notification's buttons array with a value of 0
        const buttons = notification.buttons || [];
        buttons.forEach((button) => {
          if (!clickAnalytics[button]) {
            clickAnalytics[button] = 0;
          }
        });

        // Build the response dynamically based on messageType keys
        const dynamicKeys = Object.keys(clickAnalytics).reduce((acc, key) => {
          acc[key] = clickAnalytics[key];
          return acc;
        }, {} as Record<string, number>);

        // Calculate the total as the sum of all dynamic keys
        const total = Object.values(clickAnalytics).reduce((sum, count) => sum + count, 0);

        return {
          date: notification.createdAt,
          employer: company.companyName,
          message: notification.message,
          messageDelivered: company.userCount,
          ...dynamicKeys, // Add dynamic keys for message types
          noActionTaken: company.userCount - total,
          total // Use the calculated total
        };
      });

      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error("Error fetching notification analytics:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error"
      });
    }
  };

  private getNotificationAnalyticsCSVDownloader = async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { messageId } = req.query;

      // Validate required fields
      if (!messageId) {
        res.status(400).json({
          success: false,
          message: "Missing required field: messageId"
        });
        return;
      }

      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(messageId as string)) {
        res.status(400).json({
          success: false,
          message: "Invalid messageId format. Must be a 24-character hex string."
        });
        return;
      }

      // Query the notifications collection using the messageId
      const notificationId = new mongoose.Types.ObjectId(messageId as string);
      const notification = await NotificationModelClass.getNotificationById(notificationId);

      if (!notification) {
        res.status(404).json({
          success: false,
          message: "Notification not found"
        });
        return;
      }

      // Fetch company details based on company names
      const companyNames = notification.noOfUsersSentTo.map((company) => company.companyName);
      const companies = await CompanyModelClass.getCompaniesByNames(companyNames);

      if (!companies || companies.length === 0) {
        res.status(404).json({
          success: false,
          message: "No companies found for the notification"
        });
        return;
      }

      // Map tenantId to companies
      const companiesWithTenantId = companies.map((company) => ({
        companyName: company.name,
        tenantId: company.tenantId,
        userCount: notification.noOfUsersSentTo.find((c) => c.companyName === company.name)?.userCount || 0
      }));

      // Fetch analytics for each company based on messageId and tenantId
      const analyticsResults = await Promise.all(
        companiesWithTenantId.map(async (company) => {
          const analytics = await NotificationAnalyticsModelClass.getAnalyticsByMessageIdAndTenantId(
            messageId as string,
            company.tenantId
          );
          return { company, analytics };
        })
      );

      // Build the analytics response
      const analytics = analyticsResults.map(({ company, analytics }) => {
        // Aggregate click analytics data for the company
        const clickAnalytics = analytics.reduce((acc, entry) => {
          const { messageType } = entry;
          acc[messageType] = (acc[messageType] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        // Add missing buttons from the notification's buttons array with a value of 0
        const buttons = notification.buttons || [];
        buttons.forEach((button) => {
          if (!clickAnalytics[button]) {
            clickAnalytics[button] = 0;
          }
        });

        // Build the response dynamically based on messageType keys
        const dynamicKeys = Object.keys(clickAnalytics).reduce((acc, key) => {
          acc[key] = clickAnalytics[key];
          return acc;
        }, {} as Record<string, number>);

        // Calculate the total as the sum of all dynamic keys
        const total = Object.values(clickAnalytics).reduce((sum, count) => sum + count, 0);

        return {
          Date: notification.createdAt,
          Employer: company.companyName,
          Message: notification.message,
          "Message Delivered": company.userCount,
          ...dynamicKeys, // Add dynamic keys for message types
          "No Action Taken": company.userCount - total,
          Total: total // Use the calculated total
        };
      });

      // Convert the analytics data into CSV format
      const csv = parse(analytics);

      // Set the response headers for CSV file download
      res.header('Content-Type', 'text/csv');
      res.attachment('notification_analytics.csv'); // Specify the filename
      res.send(csv); // Send the CSV file as the response

    } catch (error) {
      logger.error("Error fetching notification analytics:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error"
      });
    }
  };

}

export default GroupController;
