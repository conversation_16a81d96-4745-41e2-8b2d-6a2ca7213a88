# 📋 ENDPOINT REFERENCE GUIDE

## 🏢 COMPANY/EMPLOYER/EMPLOYEE CREATION ENDPOINTS

### **1. 🏢 COMPANY SELF-ONBOARDING**
```http
POST /admin/onboard
```
**Purpose**: New company registers itself with first admin user
**Used by**: Independent companies signing up
**Creates**: Company + Admin User
**Request**:
```json
{
  "company": {
    "name": "Company Name",
    "adminEmail": "<EMAIL>",
    "adminRole": "CEO",
    "industry": "Technology",
    "companySize": 50,
    "location": "San Francisco",
    "website": "https://company.com",
    "isBrokerage": false
  },
  "user": {
    "name": "Admin Name",
    "email": "<EMAIL>",
    "isAdmin": true,
    "isBroker": false
  }
}
```

### **2. 🏢 BROKER CREATES CLIENT COMPANY (EMPLOYER)**
```http
POST /admin/add/employer
```
**Purpose**: <PERSON>roker creates a client company
**Used by**: Brokers adding employer clients
**Creates**: Client Company + Admin User (via magic link)
**Request**:
```json
{
  "brokerId": "broker_user_id",
  "companyName": "Client Company Name",
  "companyAdminEmail": "<EMAIL>",
  "companyAdminName": "Client Admin Name"
}
```

### **3. 👥 ADMIN ADDS EMPLOYEES**
```http
POST /admin/add/employees
```
**Purpose**: Company admin adds employees to their company
**Used by**: Company admins
**Creates**: Employee users
**Request**:
```json
{
  "employeeList": [
    {
      "name": "Employee Name",
      "email": "<EMAIL>",
      "phoneNumber": "555-0123",
      "department": "Engineering",
      "title": "Software Engineer"
    }
  ]
}
```

### **4. 👤 EMPLOYEE SELF-ONBOARDING**
```http
POST /user/self-onboard
```
**Purpose**: Employee attempts to register themselves
**Used by**: Employees
**Creates**: Registration attempt (may redirect to admin)
**Request**:
```json
{
  "userEmail": "<EMAIL>"
}
```

## 🚛 CARRIER CREATION

### **BROKER CREATES CARRIER**
```http
POST /api/pre-enrollment/carriers/create
```
**Purpose**: Broker creates their own carrier
**Request**:
```json
{
  "carrierName": "Carrier Name",
  "carrierCode": "CARRIER_001",
  "displayName": "Display Name",
  "contactInfo": {
    "phone": "555-0123",
    "email": "<EMAIL>",
    "website": "https://carrier.com"
  },
  "supportedPlanTypes": ["PPO", "HMO"],
  "supportedCoverageTypes": ["Your Health"],
  "supportedCoverageSubTypes": ["Medical", "Dental"],
  "integration": {
    "apiEndpoint": "https://api.carrier.com",
    "authMethod": "API_KEY"
  },
  "licenseStates": ["CA", "NY"],
  "amRating": "A+",
  "networkName": "Network Name"
}
```

## 📋 PLAN CREATION

### **BROKER CREATES PLAN**
```http
POST /api/pre-enrollment/plans
```
**Purpose**: Broker creates plan with their carrier
**Request**:
```json
{
  "planName": "Plan Name",
  "planCode": "PLAN_001",
  "coverageType": "Your Health",
  "coverageSubTypes": ["Medical"],
  "planType": "PPO",
  "metalTier": "Gold",
  "description": "Plan description",
  "carrierId": "carrier_object_id"
}
```

## ⚙️ COMPANY BENEFITS SETTINGS

### **CREATE COMPANY SETTINGS**
```http
POST /api/pre-enrollment/company-benefits-settings
```
**Purpose**: Create benefits settings for a company
**Request**:
```json
{
  "companyId": "company_object_id",
  "globalEligibility": {
    "minimumHoursPerWeek": 30,
    "waitingPeriodDays": 90
  },
  "enrollmentPeriods": {
    "openEnrollmentStart": "2024-11-01",
    "openEnrollmentEnd": "2024-11-30"
  }
}
```

## 🎯 KEY DIFFERENCES

| Endpoint | Who Uses | Creates What | Relationship |
|----------|----------|--------------|--------------|
| `/admin/onboard` | **New Companies** | Company + Admin | Independent |
| `/admin/add/employer` | **Brokers** | Client Company | Broker → Client |
| `/admin/add/employees` | **Admins** | Employees | Admin → Employees |
| `/user/self-onboard` | **Employees** | Registration attempt | Employee → Company |

## ⚠️ IMPORTANT FIELD NAMES

### **Carrier Model Fields:**
- ✅ `supportedCoverageTypes` (array)
- ✅ `supportedCoverageSubTypes` (array)
- ❌ NOT `coverageType` or `coverageSubTypes`

### **Plan Model Fields:**
- ✅ `coverageType` (string)
- ✅ `coverageSubTypes` (array)
