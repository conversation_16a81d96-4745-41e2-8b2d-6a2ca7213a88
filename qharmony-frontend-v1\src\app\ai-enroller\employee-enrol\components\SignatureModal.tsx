'use client';

import React, { useRef, useState, useEffect } from 'react';
import { HiOutlinePencil, HiOutlineX } from 'react-icons/hi';
import SignaturePad from 'signature_pad';
import { saveSignatureWithRetry } from '../services/signatureApi';

interface SignatureModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSignatureComplete: (signatureData: string) => void;
  employeeName?: string;
}

const SignatureModal: React.FC<SignatureModalProps> = ({
  isOpen,
  onClose,
  onSignatureComplete,
  employeeName = 'Employee'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const signaturePadRef = useRef<SignaturePad | null>(null);
  const [hasSignature, setHasSignature] = useState(false);
  const [isAgreed, setIsAgreed] = useState(false);

  useEffect(() => {
    if (isOpen && canvasRef.current && !signaturePadRef.current) {
      const canvas = canvasRef.current;

      // Set canvas size
      canvas.width = 600;
      canvas.height = 200;

      // Initialize SignaturePad
      const signaturePad = new SignaturePad(canvas, {
        backgroundColor: '#ffffff',
        penColor: '#000000',
        minWidth: 1,
        maxWidth: 3,
        throttle: 16,
        minDistance: 5,
        dotSize: 0,
        velocityFilterWeight: 0.7
      });

      // Add event listeners
      signaturePad.addEventListener('beginStroke', () => {
        setHasSignature(true);
      });

      signaturePad.addEventListener('endStroke', () => {
        setHasSignature(!signaturePad.isEmpty());
      });

      signaturePadRef.current = signaturePad;

      // Draw signature line
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.strokeStyle = '#d1d5db';
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(50, canvas.height - 30);
        ctx.lineTo(canvas.width - 50, canvas.height - 30);
        ctx.stroke();
        ctx.setLineDash([]);
      }
    }

    // Cleanup on unmount or close
    return () => {
      if (signaturePadRef.current && !isOpen) {
        signaturePadRef.current.off();
        signaturePadRef.current = null;
      }
    };
  }, [isOpen]);

  const clearSignature = () => {
    if (signaturePadRef.current) {
      signaturePadRef.current.clear();
      setHasSignature(false);

      // Redraw signature line after clearing
      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.strokeStyle = '#d1d5db';
          ctx.lineWidth = 1;
          ctx.setLineDash([5, 5]);
          ctx.beginPath();
          ctx.moveTo(50, canvas.height - 30);
          ctx.lineTo(canvas.width - 50, canvas.height - 30);
          ctx.stroke();
          ctx.setLineDash([]);
        }
      }
    }
  };

  const handleNext = async () => {
    if (!hasSignature || !isAgreed || !signaturePadRef.current) return;

    try {
      // Get signature as base64 data URL using SignaturePad
      const signatureDataURL = signaturePadRef.current.toDataURL('image/png', 1.0);

      // Create signature object with metadata for local storage
      const signatureObject = {
        signature: signatureDataURL,
        timestamp: new Date().toISOString(),
        employeeName: employeeName,
        userAgent: navigator.userAgent,
        ipAddress: 'client-side',
        signatureHash: btoa(signatureDataURL).substring(0, 32),
        signaturePadData: signaturePadRef.current.toData(),
        quality: 1.0,
        format: 'PNG'
      };

      // Encrypt and store in localStorage for immediate use
      const encryptedSignature = btoa(
        Array.from(new TextEncoder().encode(JSON.stringify(signatureObject)))
          .map(byte => String.fromCharCode(byte))
          .join('')
      );
      localStorage.setItem('enrollmentSignature', encryptedSignature);

      // Send to backend API with retry mechanism
      const apiResult = await saveSignatureWithRetry(encryptedSignature);

      if (!apiResult.success) {
        console.warn('⚠️ Failed to save signature to database:', apiResult.error);
        // Still proceed with enrollment flow using local storage
      }

      console.log('📝 Signature captured, stored locally and sent to database:', {
        timestamp: signatureObject.timestamp,
        employeeName: signatureObject.employeeName,
        signatureHash: signatureObject.signatureHash,
        dataSize: signatureDataURL.length,
        vectorPoints: signatureObject.signaturePadData.length
      });

      onSignatureComplete(encryptedSignature);
    } catch (error) {
      console.error('❌ Error saving signature:', error);
      // Still proceed with local storage for enrollment flow
      alert('Signature saved locally but failed to save to database. Please contact support if this persists.');
    }
  };



  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 10000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '700px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
        position: 'relative'
      }}>
        {/* Close Button */}
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '16px',
            right: '16px',
            background: 'none',
            border: 'none',
            fontSize: '24px',
            cursor: 'pointer',
            color: '#6b7280',
            padding: '4px'
          }}
        >
          <HiOutlineX />
        </button>

        {/* Header */}
        <div style={{ marginBottom: '24px' }}>
          <h2 style={{
            fontSize: '24px',
            fontWeight: '600',
            color: '#111827',
            margin: '0 0 8px 0'
          }}>
            Create your signature
          </h2>
          <p style={{
            color: '#6b7280',
            fontSize: '14px',
            margin: 0,
            lineHeight: '21px'
          }}>
            Some carriers require a hand-drawn signature. Please draw your signature in the box below.
          </p>
        </div>

        {/* Signature Canvas */}
        <div style={{
          border: '2px solid #e5e7eb',
          borderRadius: '8px',
          marginBottom: '16px',
          position: 'relative',
          backgroundColor: '#ffffff'
        }}>
          <canvas
            ref={canvasRef}
            style={{
              width: '100%',
              height: '200px',
              cursor: 'crosshair',
              display: 'block',
              touchAction: 'none' // Prevent scrolling on touch devices
            }}
          />
          
          {/* Clear Button */}
          <button
            onClick={clearSignature}
            style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              background: 'none',
              border: 'none',
              color: '#6b7280',
              fontSize: '14px',
              cursor: 'pointer',
              padding: '4px 8px',
              borderRadius: '4px',
              backgroundColor: 'rgba(255, 255, 255, 0.9)'
            }}
          >
            Clear
          </button>

          {/* Signature Line Label */}
          <div style={{
            position: 'absolute',
            bottom: '8px',
            left: '50px',
            fontSize: '12px',
            color: '#9ca3af'
          }}>
            <HiOutlinePencil style={{ display: 'inline', marginRight: '4px' }} />
            Sign above this line
          </div>
        </div>

        {/* Agreement Checkbox */}
        <div style={{
          backgroundColor: '#f0fdf4',
          border: '1px solid #bbf7d0',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '24px'
        }}>
          <label style={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: '12px',
            cursor: 'pointer'
          }}>
            <input
              type="checkbox"
              checked={isAgreed}
              onChange={(e) => setIsAgreed(e.target.checked)}
              style={{
                marginTop: '2px',
                width: '16px',
                height: '16px'
              }}
            />
            <div>
              <div style={{
                fontSize: '14px',
                fontWeight: '600',
                color: '#065f46',
                marginBottom: '4px'
              }}>
                🔒 SHA-256 with RSA Encryption
              </div>
              <div style={{
                fontSize: '14px',
                color: '#047857',
                lineHeight: '20px'
              }}>
                I understand this is a legal representation of my signature and confirms my enrollment selections.
              </div>
            </div>
          </label>
        </div>

        {/* Action Buttons */}
        <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
          <button
            onClick={onClose}
            style={{
              padding: '12px 24px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontWeight: '500',
              fontSize: '14px'
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleNext}
            disabled={!hasSignature || !isAgreed}
            style={{
              padding: '12px 24px',
              backgroundColor: hasSignature && isAgreed ? '#2563eb' : '#9ca3af',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: hasSignature && isAgreed ? 'pointer' : 'not-allowed',
              fontWeight: '500',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            Next
            <span style={{ fontSize: '14px' }}>→</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SignatureModal;
