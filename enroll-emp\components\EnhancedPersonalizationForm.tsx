
import React, { useState } from 'react';
import { UserProfile } from './BenefitsEnrollmentBot';
import { Users, DollarSign, Heart, Stethoscope } from 'lucide-react';

interface EnhancedPersonalizationFormProps {
  onComplete: (profile: Partial<UserProfile> & {
    expectedMedicalUsage: string;
    budgetPreference: string;
    chronicConditions: boolean;
    prescriptionNeeds: boolean;
    preferredProviders: string[];
    budgetRange: number[];
  }) => void;
}

export const EnhancedPersonalizationForm = ({ onComplete }: EnhancedPersonalizationFormProps) => {
  const [familyMembers, setFamilyMembers] = useState('');
  const [wearGlasses, setWearGlasses] = useState(false);
  const [needsDentalCare, setNeedsDentalCare] = useState(false);
  const [hasPreferredDoctors, setHasPreferredDoctors] = useState(false);
  const [expectedMedicalUsage, setExpectedMedicalUsage] = useState('');
  const [budgetPreference, setBudgetPreference] = useState('');
  const [chronicConditions, setChronicConditions] = useState(false);
  const [prescriptionNeeds, setPrescriptionNeeds] = useState(false);
  const [preferredProviders, setPreferredProviders] = useState<string[]>([]);
  const [budgetRange, setBudgetRange] = useState([100]);

  const handleProviderToggle = (provider: string) => {
    setPreferredProviders(prev => 
      prev.includes(provider) 
        ? prev.filter(p => p !== provider)
        : [...prev, provider]
    );
  };

  const handleSubmit = () => {
    onComplete({
      familyMembers,
      wearGlasses,
      needsDentalCare,
      hasPreferredDoctors,
      expectedMedicalUsage,
      budgetPreference,
      chronicConditions,
      prescriptionNeeds,
      preferredProviders,
      budgetRange,
    });
  };

  const isComplete = familyMembers !== '' && expectedMedicalUsage !== '' && budgetPreference !== '';

  return (
    <div className="w-full bg-white border rounded-lg shadow-sm">
      <div className="p-6 border-b">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Heart className="w-5 h-5 text-red-500" />
          Enhanced Health Profile
        </h3>
      </div>
      <div className="p-6 space-y-6">
        {/* Family Coverage */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            <label className="text-base font-medium">Family Coverage Needed</label>
          </div>
          <div className="space-y-2">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="familyMembers"
                value="self"
                checked={familyMembers === 'self'}
                onChange={(e) => setFamilyMembers(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>Just me (Employee only)</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="familyMembers"
                value="spouse"
                checked={familyMembers === 'spouse'}
                onChange={(e) => setFamilyMembers(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>Me + Spouse/Partner</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="familyMembers"
                value="family"
                checked={familyMembers === 'family'}
                onChange={(e) => setFamilyMembers(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>Me + Family (includes children)</span>
            </label>
          </div>
        </div>

        {/* Expected Medical Usage */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Stethoscope className="w-4 h-4" />
            <label className="text-base font-medium">Expected Healthcare Usage</label>
          </div>
          <div className="space-y-2">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="expectedMedicalUsage"
                value="low"
                checked={expectedMedicalUsage === 'low'}
                onChange={(e) => setExpectedMedicalUsage(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>Low - Just preventive care & checkups</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="expectedMedicalUsage"
                value="moderate"
                checked={expectedMedicalUsage === 'moderate'}
                onChange={(e) => setExpectedMedicalUsage(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>Moderate - Occasional visits & some prescriptions</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="expectedMedicalUsage"
                value="high"
                checked={expectedMedicalUsage === 'high'}
                onChange={(e) => setExpectedMedicalUsage(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>High - Regular specialists, procedures, or chronic conditions</span>
            </label>
          </div>
        </div>

        {/* Budget Preference */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            <label className="text-base font-medium">Budget Preference</label>
          </div>
          <div className="space-y-2">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="budgetPreference"
                value="low-premium"
                checked={budgetPreference === 'low-premium'}
                onChange={(e) => setBudgetPreference(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>Lower monthly cost, higher deductible</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="budgetPreference"
                value="balanced"
                checked={budgetPreference === 'balanced'}
                onChange={(e) => setBudgetPreference(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>Balanced monthly cost and deductible</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="budgetPreference"
                value="low-deductible"
                checked={budgetPreference === 'low-deductible'}
                onChange={(e) => setBudgetPreference(e.target.value)}
                className="w-4 h-4 text-blue-600"
              />
              <span>Higher monthly cost, lower deductible</span>
            </label>
          </div>
        </div>

        {/* Budget Range Slider */}
        <div className="space-y-3">
          <label className="text-base font-medium">
            Maximum monthly budget: ${budgetRange[0]}/paycheck
          </label>
          <input
            type="range"
            min="50"
            max="200"
            step="10"
            value={budgetRange[0]}
            onChange={(e) => setBudgetRange([parseInt(e.target.value)])}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>

        {/* Health Conditions */}
        <div className="space-y-3">
          <label className="text-base font-medium">Health Considerations</label>

          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={wearGlasses}
              onChange={(e) => setWearGlasses(e.target.checked)}
              className="w-4 h-4 text-blue-600 rounded"
            />
            <span>I wear glasses or contacts</span>
          </label>

          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={needsDentalCare}
              onChange={(e) => setNeedsDentalCare(e.target.checked)}
              className="w-4 h-4 text-blue-600 rounded"
            />
            <span>I need regular dental care or have dental work planned</span>
          </label>

          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={chronicConditions}
              onChange={(e) => setChronicConditions(e.target.checked)}
              className="w-4 h-4 text-blue-600 rounded"
            />
            <span>I have ongoing health conditions requiring regular care</span>
          </label>

          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={prescriptionNeeds}
              onChange={(e) => setPrescriptionNeeds(e.target.checked)}
              className="w-4 h-4 text-blue-600 rounded"
            />
            <span>I take regular prescription medications</span>
          </label>

          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={hasPreferredDoctors}
              onChange={(e) => setHasPreferredDoctors(e.target.checked)}
              className="w-4 h-4 text-blue-600 rounded"
            />
            <span>I have preferred doctors I want to keep seeing</span>
          </label>
        </div>

        <button
          onClick={handleSubmit}
          disabled={!isComplete}
          className={`w-full px-6 py-3 rounded-lg font-medium transition-colors ${
            isComplete
              ? 'bg-blue-500 text-white hover:bg-blue-600'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          Get My Personalized Recommendations
        </button>
      </div>
    </div>
  );
};
