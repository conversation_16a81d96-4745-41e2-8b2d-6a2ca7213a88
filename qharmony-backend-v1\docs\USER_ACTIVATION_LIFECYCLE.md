# User Activation & Disabling Lifecycle

## 🎯 **Overview**
This document shows exactly where and when the `isActivated` and `isDisabled` flags are used throughout the QHarmony application.

## 📋 **Flag Definitions**

### **`isActivated: boolean`**
- **Purpose**: Indicates if a user has completed their onboarding process
- **Default**: Varies by user type
- **Schema**: Required field in user model

### **`isDisabled: boolean`** 
- **Purpose**: Indicates if a user has been disabled by an admin (soft delete)
- **Default**: `false` for new users
- **Schema**: Required field in user model

## 🔄 **Complete User Lifecycle**

### **1. User Creation (Initial State)**

#### **Admin Users** (Company Owners)
```typescript
// File: src/controllers/admin.controller.ts
// Method: onboardAdmin (Line 86)
const userEntity = await UserModelClass.addData({
  ...user,
  companyId: companyEntity._id.toString(),
  isActivated: true,    // ✅ Admins are immediately activated
  isDisabled: false,    // ✅ Admins start enabled
  // ...
});
```

#### **Employee Users** (Added by Admins)
```typescript
// File: src/controllers/admin.controller.ts  
// Method: addEmployees (Line 369)
{
  email: employee.email,
  companyId: company._id.toString(),
  isAdmin: false,
  isBroker: false,
  isActivated: false,   // ❌ Employees start NOT activated
  isDisabled: false,    // ✅ Employees start enabled
  role: 'employee',
  // ...
}
```

### **2. User Activation Process**

#### **Employee Onboarding** (Magic Link Click)
```typescript
// File: src/controllers/employee.controller.ts
// Method: onboardEmployee (Line 60)
await UserModelClass.updateData(userId, { 
  isActivated: true     // ✅ Employee becomes activated
});
```

**Trigger**: When employee clicks magic link from email

### **3. User Disabling Process**

#### **Employee Offboarding** (Admin Action)
```typescript
// File: src/controllers/employee.controller.ts
// Method: offboardEmployee (Lines 97-99)
await UserModelClass.updateData(userId, {
  isActivated: false,   // ❌ Remove activation
  isDisabled: true,     // ❌ Disable user
});
```

**Trigger**: When admin offboards an employee

### **4. User Re-enabling Process**

#### **Employee Re-enabling** (Admin Action)
```typescript
// File: src/controllers/employee.controller.ts
// Method: enableEmployee (Lines 418-420)
await UserModelClass.updateData(userId, {
  isDisabled: false,    // ✅ Re-enable user
  isActivated: false,   // ❌ Still needs to activate via magic link
});

// Then sends magic link for re-activation
await this.authService.sendLoginMagicLink(user.email, {
  email: user.email,
  companyId: user.companyId,
  userId: user._id,
});
```

**Trigger**: When admin re-enables a disabled employee

## 🔍 **Flag Usage in Authentication Flow**

### **Self-Onboarding Check**
```typescript
// File: src/controllers/employee.controller.ts
// Method: selfOnboardEmployee (Line 154)
if (Boolean(user?.isDisabled)) {
  response.status(200).json({ data: 'ask_admin_to_add' });
  return;
}
```

**Logic**: Disabled users cannot self-onboard

### **Login Check**
```typescript
// File: src/controllers/employee.controller.ts  
// Method: selfOnboardEmployeeViaTeams (Line 249)
if (user && !Boolean(user?.isDisabled)) {
  // User exists and is not disabled - proceed with login
  const company = await CompanyModelClass.getDataById(user.companyId);
  // ...
}
```

**Logic**: Only non-disabled users can login

## 📊 **User State Matrix**

| `isActivated` | `isDisabled` | User State | Can Login? | Can Self-Onboard? | Action Needed |
|---------------|--------------|------------|------------|-------------------|---------------|
| `true` | `false` | ✅ **Active** | ✅ Yes | N/A | None |
| `false` | `false` | 🟡 **Pending** | ❌ No | ✅ Yes | Click magic link |
| `false` | `true` | ❌ **Disabled** | ❌ No | ❌ No | Admin must re-enable |
| `true` | `true` | ⚠️ **Invalid** | ❌ No | ❌ No | Should not occur |

## 🎭 **User Types & Default States**

### **Admin/Company Owner**
- **Created**: `isActivated: true`, `isDisabled: false`
- **Reason**: Admins complete onboarding immediately during company creation

### **Broker Admin**
- **Created**: `isActivated: true`, `isDisabled: false`  
- **Reason**: Brokers are immediately active when added by other brokers

### **Employee (Added by Admin)**
- **Created**: `isActivated: false`, `isDisabled: false`
- **Reason**: Employees must click magic link to complete onboarding

### **Self-Onboarding User**
- **Created**: `isActivated: true`, `isDisabled: false`
- **Reason**: Self-onboarding completes activation immediately

## 🔧 **API Endpoints That Modify Flags**

### **Activation Endpoints**
1. **`POST /employee/onboard`** - Sets `isActivated: true`
2. **`POST /admin/onboard`** - Creates user with `isActivated: true`
3. **`POST /user/self-onboard`** - Creates user with `isActivated: true`

### **Disabling Endpoints**
1. **`POST /employee/offboard`** - Sets `isActivated: false, isDisabled: true`
2. **`POST /employee/enable`** - Sets `isDisabled: false, isActivated: false`

## 🚨 **Critical Business Rules**

### **1. Disabled Users Cannot Login**
```typescript
// Authentication checks must verify !isDisabled
if (Boolean(user?.isDisabled)) {
  // Block access
}
```

### **2. Non-Activated Users Need Onboarding**
```typescript
// Check activation status for feature access
if (!Boolean(user?.isActivated)) {
  // Redirect to onboarding
}
```

### **3. Re-enabled Users Must Re-activate**
```typescript
// Re-enabling sets isActivated: false
// User must click new magic link to reactivate
```

## 🔍 **Where Flags Are Checked**

### **Controllers That Check `isDisabled`**
1. **`employee.controller.ts`** - Lines 154, 249
2. **`admin.controller.ts`** - Line 458 (commented out)

### **Controllers That Check `isActivated`**
- Currently no explicit checks in controllers
- Mainly used for state management during user lifecycle

## ⚠️ **Potential Issues**

### **1. Missing Validation**
- No middleware to block disabled users from API access
- Controllers don't consistently check activation status

### **2. State Inconsistency**
- Possible to have `isActivated: true` and `isDisabled: true`
- No validation to prevent invalid state combinations

### **3. Boolean Safety**
- Some checks use unsafe patterns: `user.isDisabled` instead of `Boolean(user?.isDisabled)`
- Could fail if fields are undefined in old records

## ✅ **Recommended Improvements**

### **1. Add Middleware**
```typescript
// Check user status before API access
const userStatusMiddleware = (req, res, next) => {
  const user = req.user;
  if (Boolean(user?.isDisabled)) {
    return res.status(403).json({ error: 'Account disabled' });
  }
  next();
};
```

### **2. Add State Validation**
```typescript
// Prevent invalid state combinations
const validateUserState = (isActivated, isDisabled) => {
  if (isActivated && isDisabled) {
    throw new Error('User cannot be both activated and disabled');
  }
};
```

### **3. Consistent Boolean Checks**
```typescript
// Always use safe boolean checks
Boolean(user?.isDisabled)
Boolean(user?.isActivated)
```

**The flags control the complete user lifecycle from creation → activation → disabling → re-enabling.**
