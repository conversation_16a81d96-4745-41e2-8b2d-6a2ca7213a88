# Frontend URL Usage in Backend Repository

## Overview
This document identifies all places in the backend repository where frontend URLs (app.benosphere.com, benosphere.com) are hardcoded and need to be replaced with the new `FRONTEND_BASE_URL` environment variable.

## 🎯 Critical Findings - Backend Repository

### **1. Authentication Service** ⚠️ **HIGH PRIORITY**
**File**: `qharmony-backend-v1/src/services/auth.service.ts`

#### **Magic Link URLs (3 instances)**
```typescript
// Line 28-30: Onboarding Magic Link
const actionCodeSettings = {
  url: `https://app.benosphere.com/onboard?params=${encodeURIComponent(
    encryptedParams
  )}`,
  handleCodeInApp: true,
};

// Line 56-58: Login Magic Link  
const actionCodeSettings = {
  url: `https://app.benosphere.com/login?params=${encodeURIComponent(
    encryptedParams
  )}`,
  handleCodeInApp: true,
};

// Line 132-134: Generate Sign-In Magic Link
const actionCodeSettings = {
  url: `https://app.benosphere.com/onboard?params=${encodeURIComponent(
    encryptedParams
  )}`,
  handleCodeInApp: true,
};
```

#### **Email Configuration**
```typescript
// Line 151-157: SMTP Configuration
auth: {
  user: '<EMAIL>',
  pass: 'e5zz_Mka', // ⚠️ SECURITY RISK: Password exposed
},

// Line 157: From Email
from: '<EMAIL>',
```

**Impact**: All magic links point to hardcoded production URL
**Priority**: 🔴 **CRITICAL** - Breaks authentication in non-production environments

### **2. Documentation References** ⚠️ **LOW PRIORITY**
**File**: `qharmony-backend-v1/docs/QHarmony-Authentication-Flow.md`

```markdown
// Line 309: Documentation reference
When a user returns to app.benosphere.com, the application automatically logs them in...
```

**Impact**: Documentation reference only
**Priority**: 🟢 **LOW** - Documentation update needed

## 📋 Complete Inventory

### **Backend Files with Frontend URLs:**

| File | Line(s) | URL | Usage | Priority |
|------|---------|-----|-------|----------|
| `src/services/auth.service.ts` | 28 | `https://app.benosphere.com/onboard` | Onboarding magic link | 🔴 Critical |
| `src/services/auth.service.ts` | 56 | `https://app.benosphere.com/login` | Login magic link | 🔴 Critical |
| `src/services/auth.service.ts` | 132 | `https://app.benosphere.com/onboard` | Sign-in magic link | 🔴 Critical |
| `src/services/auth.service.ts` | 151, 157 | `<EMAIL>` | SMTP email config | 🟡 Medium |
| `docs/QHarmony-Authentication-Flow.md` | 309 | `app.benosphere.com` | Documentation | 🟢 Low |

### **Related Frontend References (for context):**
- `qharmony-frontend-v1/src/APILayer/axios_helper.ts`: `https://api.benosphere.com`
- `qharmony-frontend-v1/src/app/teamsauth/authconfig.ts`: `https://app.benosphere.com/teams-landing`
- `qharmony-frontend-v1/src/components/ShareButtons.tsx`: `https://app.benosphere.com`
- `qharmony-frontend-v1/src/components/Navbar.tsx`: `https://app.benosphere.com/onboard`

## 🛠️ Required Fixes

### **1. Update Authentication Service**
**File**: `src/services/auth.service.ts`

#### **Replace Magic Link URLs:**
```typescript
// BEFORE (3 instances):
url: `https://app.benosphere.com/onboard?params=${encodeURIComponent(encryptedParams)}`,
url: `https://app.benosphere.com/login?params=${encodeURIComponent(encryptedParams)}`,

// AFTER:
import EnvService from './env.service';

url: `${EnvService.env().FRONTEND_BASE_URL}/onboard?params=${encodeURIComponent(encryptedParams)}`,
url: `${EnvService.env().FRONTEND_BASE_URL}/login?params=${encodeURIComponent(encryptedParams)}`,
```

#### **Email Configuration (Optional):**
```typescript
// Consider moving to environment variables:
SMTP_USER=<EMAIL>
SMTP_PASSWORD=secure_password_here
SMTP_FROM_EMAIL=<EMAIL>
```

### **2. Update Documentation**
**File**: `docs/QHarmony-Authentication-Flow.md`

```markdown
// BEFORE:
When a user returns to app.benosphere.com, the application automatically logs them in...

// AFTER:
When a user returns to the frontend application, the application automatically logs them in...
```

## 🚀 Implementation Steps

### **Step 1: Update auth.service.ts**
1. Import EnvService
2. Replace all 3 hardcoded URLs with `EnvService.env().FRONTEND_BASE_URL`
3. Test magic link generation

### **Step 2: Environment Configuration**
```bash
# Production
FRONTEND_BASE_URL=https://app.benosphere.com

# Staging  
FRONTEND_BASE_URL=https://staging.benosphere.com

# Development
FRONTEND_BASE_URL=http://localhost:3000
```

### **Step 3: Testing**
1. Test onboarding magic links
2. Test login magic links  
3. Verify email delivery
4. Test across environments

## 🔒 Security Considerations

### **1. Exposed SMTP Password**
```typescript
// SECURITY RISK in auth.service.ts line 152:
pass: 'e5zz_Mka', // ⚠️ Password exposed in source code
```

**Recommendation**: Move to environment variables:
```bash
SMTP_PASSWORD=secure_password_here
```

### **2. Hardcoded Secret Key**
```typescript
// Line 9 & 53:
const SECRET_KEY="my-secret-key"; // ⚠️ Weak secret in source code
```

**Recommendation**: Use strong environment variable:
```bash
ENCRYPTION_SECRET_KEY=strong_random_secret_here
```

## ✅ Benefits After Implementation

### **1. Environment Flexibility**
- ✅ Easy switching between dev/staging/prod
- ✅ Local development with localhost:3000
- ✅ Custom domain support

### **2. Deployment Safety**
- ✅ No hardcoded production URLs in code
- ✅ Environment-specific magic links
- ✅ Proper configuration management

### **3. Testing Improvements**
- ✅ Test magic links work in all environments
- ✅ Staging environment testing
- ✅ Local development testing

## 📊 Impact Assessment

### **Before Fix:**
- ❌ All magic links point to production (app.benosphere.com)
- ❌ Cannot test authentication in dev/staging
- ❌ Local development broken for auth flows
- ❌ SMTP credentials exposed in code

### **After Fix:**
- ✅ Environment-specific magic links
- ✅ Proper local development support
- ✅ Staging environment testing possible
- ✅ Secure credential management

## ✅ Implementation Status

### **🎯 Completed**
- ✅ **CRITICAL**: Updated `auth.service.ts` to use `FRONTEND_BASE_URL`
  - ✅ Added `EnvService` import
  - ✅ Replaced all 3 hardcoded `https://app.benosphere.com` URLs
  - ✅ `sendOnboardingMagicLink` now uses `${EnvService.env().FRONTEND_BASE_URL}/onboard`
  - ✅ `sendLoginMagicLink` now uses `${EnvService.env().FRONTEND_BASE_URL}/login`
  - ✅ `generateSignInMagicLink` now uses `${EnvService.env().FRONTEND_BASE_URL}/onboard`

### **Environment Configuration**
```bash
# Production
FRONTEND_BASE_URL=https://app.benosphere.com

# Test Environment (as requested)
FRONTEND_BASE_URL=https://test.benosphere.com

# Development
FRONTEND_BASE_URL=http://localhost:3000
```

## 🎯 Remaining Steps

1. **🟡 MEDIUM**: Move SMTP credentials to environment variables
2. **🟡 MEDIUM**: Update encryption secret key
3. **🟢 LOW**: Update documentation references
4. **🟢 LOW**: Test across all environments

**Estimated Time**: 30 minutes for remaining fixes.
