import express from 'express';
import Controller from '../interfaces/controller.interface';
import logger from '../utils/logger';
import { AuthService } from '../services/auth.service';

class AuthController implements Controller {
  public router = express.Router();
  public authService = new AuthService();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post(
      '/auth/onboard-with-magic-link',
      this.sendOnboardingMagicLink
    );
    this.router.post('/auth/login-with-magic-link', this.sendLoginMagicLink);
    this.router.post('/auth/parse-params', this.parseParams);
    // this.router.post('/auth/sign-in-with-magic-link', this.signInWithMagicLink);
  }

  private sendOnboardingMagicLink = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { email, additionalParams, companyDetails, userDetails } =
        request.body as {
          email: string;
          additionalParams: Record<string, string>;
          companyDetails: Record<string, any>; // Use 'any' to allow nested objects
          userDetails: Record<string, any>; // Use 'any' to allow nested objects
        };
      console.log('Encrypted params:', additionalParams);
      await this.authService.sendOnboardingMagicLink(
        email,
        companyDetails,
        userDetails,
        additionalParams
      );
      response.status(200).json({ message: 'Magic link sent successfully' });
    } catch (error) {
      logger.error('Error sending magic link.', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private sendLoginMagicLink = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { email, additionalParams } = request.body as {
        email: string;
        additionalParams: Record<string, string>;
      };
      console.log('Encrypted params:', additionalParams);
      await this.authService.sendLoginMagicLink(email, additionalParams);
      response.status(200).json({ message: 'Magic link sent successfully' });
    } catch (error) {
      logger.error('Error sending magic link.', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private parseParams = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { link } = request.body;
      const decryptedParams = this.authService.parseAdditionalParams(link);
      response.status(200).json(decryptedParams); // Directly return the decryptedParams object
    } catch (error) {
      logger.error('Error parsing params.', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  // private signInWithMagicLink = async (
  //   request: express.Request,
  //   response: express.Response
  // ) => {
  //   try {
  //     const { email, link } = request.body;
  //     const params = await this.authService.signInWithMagicLink(email, link);
  //     response
  //       .status(200)
  //       .json({ message: 'Signed in successfully', params: params });
  //   } catch (error) {
  //     logger.error('Error signing in with magic link.', error);
  //     response.status(500).json({ error: 'Internal server error' });
  //   }
  // };
}

export default AuthController;
