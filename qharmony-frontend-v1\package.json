{"name": "qharmony-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 8080", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@azure/msal-browser": "^3.28.0", "@azure/msal-react": "^2.1.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/poppins": "^5.0.15", "@microsoft/teams-js": "^2.31.0", "@mui/icons-material": "^6.0.2", "@mui/material": "^6.0.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.2.7", "@svgr/webpack": "^8.1.0", "@types/signature_pad": "^2.3.6", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^10.13.1", "lucide-react": "^0.475.0", "next": "^14.2.13", "react": "^18", "react-dom": "^18", "react-icons": "^5.5.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "signature_pad": "^5.0.9", "tailwind-merge": "^3.3.0", "turndown": "^7.2.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/turndown": "^5.0.5", "eslint": "^8", "eslint-config-next": "14.2.7", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.1", "typescript": "^5"}}