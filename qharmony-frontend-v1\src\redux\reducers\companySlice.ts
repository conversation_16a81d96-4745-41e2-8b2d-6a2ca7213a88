// lib/features/counterSlice.ts

import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface CompanyState {
  companyBenefitTypes: string[];
  companyTeamMembers: string[];
  companyDetails: {
    _id: string;
    name: string;
    companySize: number;
    industry: string;
    location: string;
    website: string;
    adminEmail: string;
    adminRole: string;
    brokerId: string;
    brokerageId: string;
    isBrokerage: boolean;
    isActivated: boolean;
    howHeard: string;
    details: {
      logo: string;
    };
    __v: number;
  };
}

const initialState: CompanyState = {
  companyBenefitTypes: [],
  companyTeamMembers: [],
  companyDetails: {
    _id: "",
    name: "",
    companySize: 0,
    industry: "",
    location: "",
    website: "",
    adminEmail: "",
    adminRole: "",
    brokerId: "",
    brokerageId: "",
    isBrokerage: false,
    isActivated: false,
    howHeard: "",
    details: {
      logo: "",
    },
    __v: 0,
  },
};

export const companySlice = createSlice({
  name: "company",
  initialState,
  reducers: {
    setCompanyBenefitTypes: (state, action: PayloadAction<string[]>) => {
      state.companyBenefitTypes = action.payload;
    },
    setCompanyTeamMembers: (state, action: PayloadAction<string[]>) => {
      state.companyTeamMembers = action.payload;
    },
    setCompanyDetails: (state, action: PayloadAction<CompanyState["companyDetails"]>) => {
      console.log("COMPANY DETAILS PAYLOAD: ", action.payload);
      state.companyDetails = action.payload;
    },
  },
});

export const {
  setCompanyBenefitTypes,
  setCompanyTeamMembers,
  setCompanyDetails,
} = companySlice.actions;

export default companySlice.reducer;
