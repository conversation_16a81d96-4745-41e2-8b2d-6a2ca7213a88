
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus } from 'lucide-react';
import { AdditionalRecommendations } from '@/components/AdditionalRecommendations';
import { UserProfile } from '@/components/BenefitsEnrollmentBot';
import { BotQuestion } from '@/components/BotQuestion';

interface AdditionalBenefitsPageProps {
  userProfile: UserProfile;
  onComplete: (additionalPlans: any) => void;
}

export const AdditionalBenefitsPage = ({ userProfile, onComplete }: AdditionalBenefitsPageProps) => {
  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <BotQuestion 
        question="🌟 Want extra protection? Here are some bonus benefits!"
        context="Optional coverage for added peace of mind and savings."
      />
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="w-6 h-6 text-purple-500" />
            <h2 className="text-xl">Optional Benefits</h2>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <AdditionalRecommendations 
            userProfile={userProfile}
            onComplete={onComplete}
          />
        </CardContent>
      </Card>
    </div>
  );
};
