
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { HelpCircle, FileText, Calculator, Users, Clock } from 'lucide-react';

export const FloatingHelp = () => {
  const [isOpen, setIsOpen] = useState(false);

  const helpTopics = {
    deadlines: [
      { q: "When is the enrollment deadline?", a: "December 15, 2024 at 11:59 PM" },
      { q: "What happens if I miss the deadline?", a: "You'll have no coverage for 2025 unless you have a qualifying life event" },
      { q: "Can I change my mind after enrolling?", a: "Only during next year's open enrollment or with a qualifying life event" }
    ],
    costs: [
      { q: "How much will plans cost me?", a: "Costs vary by plan and family size. Use our calculator above to see your specific costs." },
      { q: "When do deductions start?", a: "Payroll deductions begin with your first paycheck in January 2025" },
      { q: "Are there any hidden fees?", a: "No hidden fees. The costs shown include all plan premiums." }
    ],
    coverage: [
      { q: "When does coverage start?", a: "January 1, 2025 for all plans" },
      { q: "What if I need care before January 1?", a: "Your current 2024 benefits remain active through December 31, 2024" },
      { q: "Do I need to re-enroll every year?", a: "Yes, you must actively enroll each year during open enrollment" }
    ],
    plans: [
      { q: "What's the difference between HMO and PPO?", a: "PPO offers more flexibility but costs more. HMO requires referrals but has lower costs." },
      { q: "Can I have different plan types for different benefits?", a: "Yes! You can mix and match (e.g., HMO medical with PPO dental)" },
      { q: "Which plan is best for me?", a: "Use our personalization tool above to get recommendations based on your needs" }
    ]
  };

  return (
    <>
      {/* Floating Help Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button 
              size="lg"
              className="rounded-full shadow-lg hover:shadow-xl transition-all duration-200 bg-blue-600 hover:bg-blue-700"
            >
              <HelpCircle className="w-5 h-5 mr-2" />
              Need Help?
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <HelpCircle className="w-5 h-5 text-blue-500" />
                Benefits Enrollment Help Center
              </DialogTitle>
            </DialogHeader>
            
            <Tabs defaultValue="deadlines" className="w-full">
              <TabsList className="grid grid-cols-4 w-full">
                <TabsTrigger value="deadlines" className="gap-1">
                  <Clock className="w-3 h-3" />
                  Deadlines
                </TabsTrigger>
                <TabsTrigger value="costs" className="gap-1">
                  <Calculator className="w-3 h-3" />
                  Costs
                </TabsTrigger>
                <TabsTrigger value="coverage" className="gap-1">
                  <FileText className="w-3 h-3" />
                  Coverage
                </TabsTrigger>
                <TabsTrigger value="plans" className="gap-1">
                  <Users className="w-3 h-3" />
                  Plans
                </TabsTrigger>
              </TabsList>

              {Object.entries(helpTopics).map(([key, topics]) => (
                <TabsContent key={key} value={key} className="mt-4">
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {topics.map((topic, index) => (
                      <Card key={index}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">{topic.q}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground">{topic.a}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>

            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <h4 className="font-medium mb-2">Still need help?</h4>
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline">📞 Call HR: (555) 123-4567</Badge>
                <Badge variant="outline">📧 Email: <EMAIL></Badge>
                <Badge variant="outline">💬 Live Chat: Mon-Fri 9AM-5PM</Badge>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};
