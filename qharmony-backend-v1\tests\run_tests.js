#!/usr/bin/env node

/**
 * SIMPLE TEST RUNNER
 * Execute the comprehensive pre-enrollment system tests
 */

const { runComprehensiveEnrollmentTests } = require('./comprehensive_pre_enrollment_test');

console.log('🎯 PRE-ENROLLMENT SYSTEM TEST RUNNER');
console.log('=====================================\n');

console.log('📋 WHAT THIS SCRIPT WILL TEST:');
console.log('✅ Carrier Operations (Create, Validate, Activate/Deactivate)');
console.log('✅ Plan Operations (Create, Validate, Activate)');
console.log('✅ Company Creation & User Management');
console.log('✅ Company Benefits Settings (Broker & Employer)');
console.log('✅ Plan Assignments (Self & Cross-Company)');
console.log('✅ Access Control Validation');
console.log('✅ Data Integrity & Uniqueness Validation\n');

console.log('🚀 STARTING COMPREHENSIVE TESTS...\n');

// Run the comprehensive test suite
runComprehensiveEnrollmentTests()
  .then(() => {
    console.log('\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ TESTS FAILED:', error);
    process.exit(1);
  });
