import pandas as pd
import numpy as np
from scipy.stats import norm
import shap
import pickle
import joblib
import xgboost as xgb
import os
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Union, Any, Tuple
from langchain_openai.chat_models import ChatOpenAI
import json
from scipy.stats import norm
from .dataModels import Question,QuestionsResponse,UserAnswer,LifeExpectancyPrediction
from .WellnessQuestions import get_life_expectancy_questions, get_life_expectancy_questions_json
import datetime, math
from bson import ObjectId
from pymongo import MongoClient




    
def get_mongo_db(mongo_client: MongoClient, env:str):
    """Get MongoDB database."""
    return mongo_client[env]

def get_mongo_collection(collection_name: str, mongo_client: MongoClient, env):
    """Get MongoDB collection."""
    db = get_mongo_db(mongo_client=mongo_client, env=env)
    collection = db[collection_name]
    return collection






def calculate_bmi(height,weight):
    return (weight)/(height/100)**2




def preprocess_dataframe(df, column_types=None, ordinal_categories=None, fitted_preprocessors=None, columns_to_drop=None):
    df = df.copy()  # avoid modifying original

    if columns_to_drop is not None:
        columns_to_drop = [col for col in columns_to_drop if col in df.columns]
        if columns_to_drop:
            print(f"Dropping columns: {columns_to_drop}")
            df = df.drop(columns=columns_to_drop)

    if column_types is None:
        column_types = {'ordinal': [], 'nominal': [], 'numeric': [], 'yes_or_no': []}
        
    if fitted_preprocessors is None:
        fitted_preprocessors = {'ordinal': {}, 'numeric': None, 'nominal': None}
        fit_mode = True
    else:
        fit_mode = False

    # Process yes/no columns.
    for col in column_types.get('yes_or_no', []):
        if col in df.columns:
            df[col] = df[col].replace({'Yes': 1, 'No': 0}).astype(int)

    # Process ordinal columns.
    for col in column_types.get('ordinal', []):
        if col in df.columns:
            if fit_mode:
                from sklearn.preprocessing import OrdinalEncoder
                if ordinal_categories and col in ordinal_categories:
                    ord_enc = OrdinalEncoder(categories=[ordinal_categories[col]])
                else:
                    ord_enc = OrdinalEncoder()
                df[col] = ord_enc.fit_transform(df[[col]])
                fitted_preprocessors['ordinal'][col] = ord_enc
            else:
                ord_enc = fitted_preprocessors['ordinal'][col]
                df[col] = ord_enc.transform(df[[col]])
    if column_types.get('ordinal', []):
        ordinal_cols = [col for col in column_types['ordinal'] if col in df.columns]
        if ordinal_cols:
            df[ordinal_cols] = df[ordinal_cols].astype(int)

    # Process numeric columns.
    numeric_cols = [col for col in column_types.get('numeric', []) if col in df.columns]
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    if numeric_cols:
        from sklearn.preprocessing import StandardScaler
        if fit_mode:
            scaler = StandardScaler()
            df[numeric_cols] = scaler.fit_transform(df[numeric_cols])
            fitted_preprocessors['numeric'] = scaler
        else:
            scaler = fitted_preprocessors['numeric']
            df[numeric_cols] = scaler.transform(df[numeric_cols])
            
    # Process nominal columns.
    nominal_cols = [col for col in column_types.get('nominal', []) if col in df.columns]
    if nominal_cols:
        from sklearn.preprocessing import OneHotEncoder
        if fit_mode:
            ohe = OneHotEncoder(drop='first', sparse_output=False, handle_unknown='ignore')
            nominal_encoded = ohe.fit_transform(df[nominal_cols])
            fitted_preprocessors['nominal'] = ohe
        else:
            ohe = fitted_preprocessors['nominal']
            nominal_encoded = ohe.transform(df[nominal_cols])
        nominal_encoded_df = pd.DataFrame(nominal_encoded,
                                          columns=ohe.get_feature_names_out(nominal_cols),
                                          index=df.index)
        df = df.drop(nominal_cols, axis=1)
        df = pd.concat([df, nominal_encoded_df], axis=1)
    
    if fit_mode:
        return df, fitted_preprocessors
    return df