'use client';

import React, { useState } from 'react';
import { BarChart3, X, Check, Minus } from 'lucide-react';

interface Plan {
  id: string;
  name: string;
  type: 'medical' | 'dental' | 'vision';
  tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  monthlyPremium: number;
  deductible: number;
  outOfPocketMax: number;
  features: string[];
  network: string;
}

interface CompareModalProps {
  plans: Plan[];
  selectedPlans?: string[];
  onClose?: () => void;
}

export const CompareModal = ({ plans, selectedPlans = [], onClose }: CompareModalProps) => {
  const [isOpen, setIsOpen] = useState(true); // Start open when component is rendered
  const [plansToCompare, setPlansToCompare] = useState<string[]>(selectedPlans.slice(0, 3));

  const handleClose = () => {
    setIsOpen(false);
    if (onClose) {
      onClose();
    }
  };

  // Mock plans data for demo
  const mockPlans: Plan[] = [
    {
      id: '1',
      name: 'HealthFirst PPO Gold',
      type: 'medical',
      tier: 'Gold',
      monthlyPremium: 450,
      deductible: 1000,
      outOfPocketMax: 5000,
      features: ['Nationwide network', 'No referrals needed', 'Prescription coverage', 'Mental health coverage'],
      network: 'PPO Network'
    },
    {
      id: '2', 
      name: 'HealthFirst HMO Silver',
      type: 'medical',
      tier: 'Silver',
      monthlyPremium: 320,
      deductible: 2000,
      outOfPocketMax: 7000,
      features: ['Local network', 'Primary care referrals', 'Prescription coverage', 'Preventive care'],
      network: 'HMO Network'
    },
    {
      id: '3',
      name: 'DentalCare Plus',
      type: 'dental',
      tier: 'Gold',
      monthlyPremium: 45,
      deductible: 50,
      outOfPocketMax: 1500,
      features: ['Cleanings covered 100%', 'Major work 50% coverage', 'Orthodontics included', 'No waiting period'],
      network: 'Dental Network'
    }
  ];

  const availablePlans = plans.length > 0 ? plans : mockPlans;
  const selectedPlanData = availablePlans.filter(plan => plansToCompare.includes(plan.id));

  const togglePlanSelection = (planId: string) => {
    if (plansToCompare.includes(planId)) {
      setPlansToCompare(prev => prev.filter(id => id !== planId));
    } else if (plansToCompare.length < 3) {
      setPlansToCompare(prev => [...prev, planId]);
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '8px 16px',
          color: '#374151',
          border: '1px solid #d1d5db',
          borderRadius: '8px',
          backgroundColor: 'white',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease',
          fontSize: '14px',
          fontWeight: '500',
          fontFamily: 'sans-serif'
        }}
        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
        onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
      >
        <BarChart3 size={16} style={{ color: '#6b7280' }} />
        Compare Plans
      </button>
    );
  }

  return (
    <>
      {/* Backdrop */}
      <div
        style={{
          position: 'fixed',
          inset: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 50
        }}
        onClick={handleClose}
      />

      {/* Modal */}
      <div style={{
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '95vw',
        maxWidth: '1200px',
        maxHeight: '90vh',
        backgroundColor: 'white',
        borderRadius: '16px',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
        zIndex: 51,
        overflow: 'hidden'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '20px 24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <BarChart3 size={20} style={{ color: '#2563eb' }} />
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#111827',
              margin: 0,
              fontFamily: 'sans-serif'
            }}>
              Compare Plans
            </h2>
          </div>
          <button
            onClick={handleClose}
            style={{
              padding: '8px',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* Plan Selection */}
        <div style={{
          padding: '16px 24px',
          backgroundColor: '#f9fafb',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <p style={{
            fontSize: '14px',
            color: '#6b7280',
            margin: '0 0 12px 0',
            fontFamily: 'sans-serif'
          }}>
            Select up to 3 plans to compare ({plansToCompare.length}/3 selected):
          </p>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            {availablePlans.map((plan) => (
              <button
                key={plan.id}
                onClick={() => togglePlanSelection(plan.id)}
                disabled={!plansToCompare.includes(plan.id) && plansToCompare.length >= 3}
                style={{
                  padding: '6px 12px',
                  backgroundColor: plansToCompare.includes(plan.id) ? '#2563eb' : 'white',
                  color: plansToCompare.includes(plan.id) ? 'white' : '#374151',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '12px',
                  fontWeight: '500',
                  cursor: plansToCompare.includes(plan.id) || plansToCompare.length < 3 ? 'pointer' : 'not-allowed',
                  opacity: !plansToCompare.includes(plan.id) && plansToCompare.length >= 3 ? 0.5 : 1,
                  fontFamily: 'sans-serif'
                }}
              >
                {plan.name}
              </button>
            ))}
          </div>
        </div>

        {/* Comparison Table */}
        <div style={{
          padding: '24px',
          overflowX: 'auto',
          maxHeight: '500px',
          overflowY: 'auto'
        }}>
          {selectedPlanData.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '40px',
              color: '#6b7280'
            }}>
              <BarChart3 size={48} style={{ margin: '0 auto 16px', opacity: 0.5 }} />
              <p style={{
                fontSize: '16px',
                fontWeight: '500',
                margin: '0 0 8px 0',
                fontFamily: 'sans-serif'
              }}>
                Select plans to compare
              </p>
              <p style={{
                fontSize: '14px',
                margin: 0,
                fontFamily: 'sans-serif'
              }}>
                Choose up to 3 plans from the list above to see a detailed comparison.
              </p>
            </div>
          ) : (
            <div style={{ minWidth: '600px' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr>
                    <th style={{
                      padding: '12px',
                      textAlign: 'left',
                      borderBottom: '2px solid #e5e7eb',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#111827',
                      fontFamily: 'sans-serif'
                    }}>
                      Feature
                    </th>
                    {selectedPlanData.map((plan) => (
                      <th key={plan.id} style={{
                        padding: '12px',
                        textAlign: 'center',
                        borderBottom: '2px solid #e5e7eb',
                        fontSize: '14px',
                        fontWeight: '600',
                        color: '#111827',
                        fontFamily: 'sans-serif'
                      }}>
                        {plan.name}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td style={{
                      padding: '12px',
                      borderBottom: '1px solid #e5e7eb',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      fontFamily: 'sans-serif'
                    }}>
                      Monthly Premium
                    </td>
                    {selectedPlanData.map((plan) => (
                      <td key={plan.id} style={{
                        padding: '12px',
                        textAlign: 'center',
                        borderBottom: '1px solid #e5e7eb',
                        fontSize: '14px',
                        color: '#111827',
                        fontFamily: 'sans-serif'
                      }}>
                        ${plan.monthlyPremium}
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td style={{
                      padding: '12px',
                      borderBottom: '1px solid #e5e7eb',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      fontFamily: 'sans-serif'
                    }}>
                      Deductible
                    </td>
                    {selectedPlanData.map((plan) => (
                      <td key={plan.id} style={{
                        padding: '12px',
                        textAlign: 'center',
                        borderBottom: '1px solid #e5e7eb',
                        fontSize: '14px',
                        color: '#111827',
                        fontFamily: 'sans-serif'
                      }}>
                        ${plan.deductible.toLocaleString()}
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td style={{
                      padding: '12px',
                      borderBottom: '1px solid #e5e7eb',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      fontFamily: 'sans-serif'
                    }}>
                      Out-of-Pocket Max
                    </td>
                    {selectedPlanData.map((plan) => (
                      <td key={plan.id} style={{
                        padding: '12px',
                        textAlign: 'center',
                        borderBottom: '1px solid #e5e7eb',
                        fontSize: '14px',
                        color: '#111827',
                        fontFamily: 'sans-serif'
                      }}>
                        ${plan.outOfPocketMax.toLocaleString()}
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td style={{
                      padding: '12px',
                      borderBottom: '1px solid #e5e7eb',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      fontFamily: 'sans-serif'
                    }}>
                      Network
                    </td>
                    {selectedPlanData.map((plan) => (
                      <td key={plan.id} style={{
                        padding: '12px',
                        textAlign: 'center',
                        borderBottom: '1px solid #e5e7eb',
                        fontSize: '14px',
                        color: '#111827',
                        fontFamily: 'sans-serif'
                      }}>
                        {plan.network}
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
