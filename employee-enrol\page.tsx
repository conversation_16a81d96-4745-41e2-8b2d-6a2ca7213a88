'use client';

import React, { useState, useRef } from 'react';
import './employee-enrol.css';
import { <PERSON>Lef<PERSON>, ArrowRight, User, Bell, Brain, Stethoscope, Smile, Eye, Plus, FileText, CheckCircle } from 'lucide-react';
import { WelcomePage } from './components/WelcomePage';
import { PersonalizationPage } from './components/PersonalizationPage';
import { MedicalPlanPage } from './components/MedicalPlanPage';
import DentalPlanPage from './components/DentalPlanPage';
import VisionPlanPage from './components/VisionPlanPage';
import AdditionalBenefitsPage from './components/AdditionalBenefitsPage';
import SummaryPage from './components/SummaryPage';
import ConfirmationPage from './components/ConfirmationPage';

export interface UserProfile {
  familyMembers: string;
  expectedMedicalUsage: string;
  budgetPreference: string;
  chronicConditions: boolean;
  prescriptionNeeds: boolean;
  hasPreferredDoctors: boolean;
  selectedMedical?: any;
  selectedDental?: any;
  selectedVision?: any;
  selectedAdditionalBenefits?: any[];
}

export interface ChatStep {
  id: string;
  title: string;
  icon: any;
  completed: boolean;
}

export default function EmployeeEnrollmentPage() {
  const [currentStep, setCurrentStep] = useState(0);
  const [userProfile, setUserProfile] = useState<UserProfile>({
    familyMembers: '',
    expectedMedicalUsage: '',
    budgetPreference: '',
    chronicConditions: false,
    prescriptionNeeds: false,
    hasPreferredDoctors: false,
    selectedAdditionalBenefits: [],
  });
  const [recommendation, setRecommendation] = useState<any>(null);
  const [enrollmentComplete, setEnrollmentComplete] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  const steps: ChatStep[] = [
    { id: 'kickoff', title: 'Welcome & Kickoff', icon: Bell, completed: false },
    { id: 'personalization', title: 'Smart Personalization', icon: Brain, completed: false },
    { id: 'medical', title: 'Medical Plan', icon: Stethoscope, completed: false },
    { id: 'dental', title: 'Dental Plan', icon: Smile, completed: false },
    { id: 'vision', title: 'Vision Plan', icon: Eye, completed: false },
    { id: 'additional', title: 'Additional Benefits', icon: Plus, completed: false },
    { id: 'summary', title: 'Summary Review', icon: FileText, completed: false },
    { id: 'confirmation', title: 'Confirmation', icon: CheckCircle, completed: false },
  ];

  const getProgressPercentage = () => {
    return (currentStep / (steps.length - 1)) * 100;
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePersonalizationComplete = (profile: Partial<UserProfile>) => {
    setUserProfile(prev => ({ ...prev, ...profile }));

    // Generate recommendation based on profile
    const rec = getSmartRecommendation(profile);
    setRecommendation(rec);

    handleNext();
  };

  const getSmartRecommendation = (profile: any) => {
    const { expectedMedicalUsage, budgetPreference } = profile;

    if (budgetPreference === 'balanced' || expectedMedicalUsage === 'moderate') {
      return {
        plan: {
          name: "Anthem PPO 035",
          cost: 82.90,
          deductible: 2000,
          features: [
            "Balanced cost and coverage",
            "Deductible: $2,000",
            "Covers your PCP visits at $25",
            "Good for moderate usage & predictable costs"
          ]
        },
        reason: "This plan offers the best balance of monthly cost and coverage for someone with moderate healthcare needs."
      };
    }

    return {
      plan: {
        name: "Anthem PPO 035",
        cost: 82.90,
        deductible: 2000,
        features: [
          "Balanced cost and coverage",
          "Deductible: $2,000",
          "Covers your PCP visits at $25",
          "Good for moderate usage & predictable costs"
        ]
      },
      reason: "This plan offers the best balance of monthly cost and coverage for someone with moderate healthcare needs."
    };
  };

  const handlePlanSelection = (planType: 'medical' | 'dental' | 'vision', planData: any) => {
    setUserProfile(prev => ({
      ...prev,
      [`selected${planType.charAt(0).toUpperCase() + planType.slice(1)}`]: planData
    }));
    handleNext();
  };

  const handleAdditionalBenefitsSelection = (benefits: any[]) => {
    setUserProfile(prev => ({
      ...prev,
      selectedAdditionalBenefits: benefits
    }));
    handleNext();
  };

  const handleMakeChanges = () => {
    setCurrentStep(0); // Go back to beginning to make changes
  };

  const handleConfirmEnrollment = () => {
    setEnrollmentComplete(true);
    handleNext();
  };

  const renderCurrentPage = () => {
    switch (currentStep) {
      case 0:
        return <WelcomePage onNext={handleNext} />;
      case 1:
        return <PersonalizationPage onComplete={handlePersonalizationComplete} />;
      case 2:
        return recommendation ? (
          <MedicalPlanPage
            userProfile={userProfile}
            onPlanSelect={(planData: any) => handlePlanSelection('medical', planData)}
            recommendation={recommendation}
          />
        ) : null;
      case 3:
        return (
          <DentalPlanPage
            onPlanSelect={(planData: any) => handlePlanSelection('dental', planData)}
          />
        );
      case 4:
        return (
          <VisionPlanPage
            onPlanSelect={(planData: any) => handlePlanSelection('vision', planData)}
          />
        );
      case 5:
        return (
          <AdditionalBenefitsPage
            onBenefitsSelect={handleAdditionalBenefitsSelection}
          />
        );
      case 6:
        return (
          <SummaryPage
            enrollmentData={{
              medicalPlan: userProfile.selectedMedical,
              dentalPlan: userProfile.selectedDental,
              visionPlan: userProfile.selectedVision,
              additionalBenefits: userProfile.selectedAdditionalBenefits
            }}
            onMakeChanges={handleMakeChanges}
            onConfirmEnrollment={handleConfirmEnrollment}
          />
        );
      case 7:
        return (
          <ConfirmationPage
            enrollmentData={userProfile}
          />
        );
      default:
        return <WelcomePage onNext={handleNext} />;
    }
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#f3f4f6' }}>
      {/* Header */}
      <div style={{ backgroundColor: 'white', borderBottom: '1px solid #e5e7eb', padding: '16px 24px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '32px',
              height: '32px',
              backgroundColor: '#dbeafe',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <User style={{ width: '16px', height: '16px', color: '#2563eb' }} />
            </div>
            <div>
              <h1 style={{
                fontSize: '18px',
                fontWeight: '600',
                color: '#2563eb',
                margin: 0,
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                🏥 AI Benefits Assistant ✨
              </h1>
              <p style={{
                fontSize: '12px',
                color: '#6b7280',
                margin: 0
              }}>
                2025 Medical, Dental & Vision • Powered by Smart Recommendations
              </p>
            </div>
          </div>
          <button style={{
            width: '32px',
            height: '32px',
            backgroundColor: '#f3f4f6',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: 'none',
            cursor: 'pointer'
          }}>
            <span style={{ color: '#6b7280' }}>☀️</span>
          </button>
        </div>
      </div>

      {/* Progress Section */}
      <div style={{ backgroundColor: 'white', borderBottom: '1px solid #e5e7eb', padding: '16px 24px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
            <h2 style={{ fontSize: '18px', fontWeight: '500', color: '#111827', margin: 0 }}>Enrollment Progress</h2>
            <span style={{ fontSize: '14px', color: '#6b7280' }}>{currentStep + 1} of {steps.length}</span>
          </div>

          {/* Progress Bar */}
          <div style={{
            width: '100%',
            backgroundColor: '#e5e7eb',
            borderRadius: '9999px',
            height: '8px',
            marginBottom: '16px'
          }}>
            <div
              style={{
                backgroundColor: '#111827',
                height: '8px',
                borderRadius: '9999px',
                transition: 'all 0.3s ease',
                width: `${getProgressPercentage()}%`
              }}
            />
          </div>

          {/* Step Navigation Pills */}
          <div style={{ display: 'flex', gap: '12px', overflowX: 'auto', paddingBottom: '8px' }}>
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;

              return (
                <div
                  key={step.id}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '8px 16px',
                    borderRadius: '9999px',
                    fontSize: '14px',
                    fontWeight: '500',
                    whiteSpace: 'nowrap',
                    transition: 'all 0.3s ease',
                    backgroundColor: isActive ? '#2563eb' : isCompleted ? '#10b981' : '#f3f4f6',
                    color: isActive || isCompleted ? 'white' : '#4b5563',
                    border: !isActive && !isCompleted ? '1px solid #d1d5db' : 'none'
                  }}
                >
                  <Icon size={16} />
                  <span>{step.title}</span>
                  {isCompleted && <CheckCircle size={16} />}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div style={{ padding: '32px 24px' }}>
        <div style={{ maxWidth: '1024px', margin: '0 auto' }}>
          <div ref={scrollRef}>
            {renderCurrentPage()}
          </div>
        </div>
      </div>

      {/* Navigation Footer */}
      {currentStep > 0 && (
        <div style={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          borderTop: '1px solid #e5e7eb',
          padding: '16px 24px'
        }}>
          <div style={{ maxWidth: '1024px', margin: '0 auto', display: 'flex', justifyContent: 'space-between' }}>
            <button
              onClick={handleBack}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                color: '#6b7280',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer',
                transition: 'color 0.2s ease'
              }}
              onMouseOver={(e) => e.currentTarget.style.color = '#374151'}
              onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}
            >
              <ArrowLeft size={16} />
              Back
            </button>

            {currentStep < steps.length - 1 && (
              <button
                onClick={handleNext}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px 24px',
                  backgroundColor: '#111827',
                  color: 'white',
                  borderRadius: '8px',
                  border: 'none',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s ease'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#374151'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#111827'}
              >
                Next
                <ArrowRight size={16} />
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
