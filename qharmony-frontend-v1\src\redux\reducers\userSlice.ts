// lib/features/counterSlice.ts

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store"; // Adjust the import path as necessary

// Address interface
interface AddressInterface {
  street1: string;
  street2?: string;
  city: string;
  state: string;
  zipCode: string;
  country?: string;
}

// Dependent interface
interface DependentInterface {
  _id?: string;
  name: string;
  gender: string;
  dateOfBirth: Date;
  relationship: string;
  ssn?: string;
  isStudent?: boolean;
  isDisabled?: boolean;
  coverageEndAge?: number;
  address?: AddressInterface;
  phoneNumber?: string;
  email?: string;
  isActive?: boolean;
}

// Emergency contact interface
interface EmergencyContactInterface {
  name?: string;
  relationship?: string;
  phoneNumber?: string;
  email?: string;
  address?: AddressInterface;
}

interface UserState {
  _id: string;
  userProfile: {
    name: string;
    email: string;
    companyId: string;
    role: string;
    isAdmin: boolean;
    isBroker: boolean;
    details?: {
      // Basic Contact Information
      phoneNumber?: string;
      department?: string;
      title?: string;
      role?: string;

      // Employee demographic data
      dateOfBirth?: Date;
      hireDate?: Date;
      annualSalary?: number;
      employeeClassType?: string;
      customPayrollFrequency?: string;

      // Personal Identification
      ssn?: string;
      address?: AddressInterface;
      mailingAddress?: AddressInterface;

      // Family Information
      dependents?: DependentInterface[];

      // Emergency Contact
      emergencyContact?: EmergencyContactInterface;

      // Employment Details
      employeeId?: string;
      managerId?: string;
      workLocation?: string;
      workSchedule?: string;

      // Employer Information
      ein?: string;
    };
  };
  selectedBenefitType: string;
  selectedBenefitId: string;
  selectedFAQQuestion: string;
  managedCompanies: any[];
}

const initialState: UserState = {
  _id: "",
  userProfile: {
    name: "",
    email: "",
    companyId: "",
    role: "",
    isAdmin: false,
    isBroker: false,
    details: {
      // Basic Contact Information
      phoneNumber: undefined,
      department: undefined,
      title: undefined,
      role: undefined,

      // Employee demographic data
      dateOfBirth: undefined,
      hireDate: undefined,
      annualSalary: undefined,
      employeeClassType: "",
      customPayrollFrequency: "",

      // Personal Identification
      ssn: "",
      address: undefined,
      mailingAddress: undefined,

      // Family Information
      dependents: [],

      // Emergency Contact
      emergencyContact: undefined,

      // Employment Details
      employeeId: "",
      managerId: "",
      workLocation: "",
      workSchedule: "",

      // Employer Information
      ein: "",
    },
  },
  selectedBenefitType: "",
  selectedBenefitId: "",
  selectedFAQQuestion: "",
  managedCompanies: [],
};

export const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUserId: (state, action: PayloadAction<string>) => {
      state._id = action.payload;
    },
    setUserProfile: (
      state,
      action: PayloadAction<{
        name: string;
        email: string;
        companyId: string;
        role: string;
        isAdmin: boolean;
        isBroker: boolean;
        details?: {
          // Basic Contact Information
          phoneNumber?: string;
          department?: string;
          title?: string;
          role?: string;

          // Employee demographic data
          dateOfBirth?: Date;
          hireDate?: Date;
          annualSalary?: number;
          employeeClassType?: string;
          customPayrollFrequency?: string;

          // Personal Identification
          ssn?: string;
          address?: AddressInterface;
          mailingAddress?: AddressInterface;

          // Family Information
          dependents?: DependentInterface[];

          // Emergency Contact
          emergencyContact?: EmergencyContactInterface;

          // Employment Details
          employeeId?: string;
          managerId?: string;
          workLocation?: string;
          workSchedule?: string;

          // Employer Information
          ein?: string;
        };
      }>,
    ) => {
      const { name, email, companyId, role, isAdmin, isBroker, details } =
        action.payload;
      state.userProfile = {
        name,
        email,
        companyId,
        role,
        isAdmin,
        isBroker,
        details: details || state.userProfile.details,
      };
    },
    setSelectedBenefitType: (state, action: PayloadAction<string>) => {
      console.log("action.payload", action.payload);
      state.selectedBenefitType = action.payload;
    },
    setSelectedBenefitId: (state, action: PayloadAction<string>) => {
      state.selectedBenefitId = action.payload;
    },
    setSelectedFAQQuestion: (state, action: PayloadAction<string>) => {
      state.selectedFAQQuestion = action.payload;
    },
    clearSelectedFAQQuestion: (state) => {
      state.selectedFAQQuestion = "";
    },
    setManagedCompanies: (state, action: PayloadAction<any[]>) => {
      state.managedCompanies = action.payload;
    },
  },
});

export const {
  setUserId,
  setUserProfile,
  setSelectedBenefitType,
  setSelectedBenefitId,
  setSelectedFAQQuestion,
  clearSelectedFAQQuestion,
  setManagedCompanies,
} = userSlice.actions;

export const getUsersCompanyId = (state: RootState) =>
  state.user.userProfile.companyId;

export default userSlice.reducer;
