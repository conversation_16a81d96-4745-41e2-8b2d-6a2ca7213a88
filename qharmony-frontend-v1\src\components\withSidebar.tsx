import Sidebar from "./sidebar";
import Box from "@mui/material/Box"; // Import Box from MUI
import TopBar from "./topbar";

const withSidebar = (WrappedComponent: React.ComponentType) => {
  const WithSidebar = (props: any) => {
    return (
      <Box sx={{ display: "flex", flexGrow: 1 }}>
        <Sidebar />
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            width: `calc(100% - 240px)`, // Adjust width based on sidebar
          }}
        >
          <TopBar />
          <WrappedComponent {...props} />
        </Box>
      </Box>
    );
  };

  WithSidebar.displayName = `WithSidebar(${WrappedComponent.displayName || WrappedComponent.name || "Component"})`;

  return WithSidebar;
};

export default withSidebar;
