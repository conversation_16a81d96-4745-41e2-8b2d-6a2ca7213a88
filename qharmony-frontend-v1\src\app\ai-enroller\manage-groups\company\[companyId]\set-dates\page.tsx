'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { HiOutlineCalendar, HiOutlineInformationCircle, HiOutlineHome, HiOutlineOfficeBuilding, HiOutlineClipboardList, HiOutlineCheckCircle } from 'react-icons/hi';
import { getPlanAssignmentsByCompany, updatePlanAssignment, PlanAssignment } from '../../../services/planAssignmentApi';

interface Company {
  _id: string;
  companyName: string;
  employeeCount?: number;
}

// Extended interface for enhanced plan assignments with additional details
interface EnhancedPlanAssignment extends PlanAssignment {
  planDetails?: {
    planName: string;
    planCode: string;
    planType: string;
    coverageType: string;
    coverageSubTypes: string[];
    metalTier: string;
    carrierName: string;
  } | null;
  assignmentDetails?: any;
}

export default function SetDatesPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const companyId = params.companyId as string;

  // Memoize selectedAssignmentIds to prevent infinite re-renders
  // Note: URL parameter is called 'plans' but contains assignment IDs
  const selectedAssignmentIds = useMemo(() => {
    return searchParams.get('plans')?.split(',') || [];
  }, [searchParams]);
  
  const [company, setCompany] = useState<Company | null>(null);
  const [planAssignments, setPlanAssignments] = useState<EnhancedPlanAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [useGlobalDates, setUseGlobalDates] = useState(false);
  const [enrollmentStartDate, setEnrollmentStartDate] = useState('2024-11-01');
  const [enrollmentEndDate, setEnrollmentEndDate] = useState('2024-11-30');
  const [planStartDate, setPlanStartDate] = useState('2025-01-01');
  const [planEndDate, setPlanEndDate] = useState('2025-12-31');
  const [waitingPeriod, setWaitingPeriod] = useState('0');
  const [gracePeriod, setGracePeriod] = useState('30');

  // Function to fetch plan details by ID (same as plans page)
  const fetchPlanDetails = async (planId: string) => {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
    const getUserId = () => {
      const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found. Please authenticate first.');
      }
      return userId;
    };

    try {
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': getUserId(),
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          planName: data.plan?.planName || 'Unknown Plan',
          planCode: data.plan?.planCode || 'N/A',
          planType: data.plan?.planType || 'N/A',
          coverageType: data.plan?.coverageType || 'Unknown',
          coverageSubTypes: data.plan?.coverageSubTypes || [],
          metalTier: data.plan?.metalTier || '',
          carrierName: data.carrier?.carrierName || 'Unknown Carrier'
        };
      }
    } catch (error) {
      console.error('Error fetching plan details for planId:', planId, error);
    }

    return {
      planName: 'Unknown Plan',
      planCode: 'N/A',
      planType: 'N/A',
      coverageType: 'Unknown',
      coverageSubTypes: [],
      metalTier: '',
      carrierName: 'Unknown Carrier'
    };
  };

  // Function to fetch plan assignment details including coverage tiers (same as plans page)
  const fetchPlanAssignmentDetails = async (assignmentId: string) => {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
    const getUserId = () => localStorage.getItem('userid1') || localStorage.getItem('userId') || '6838677aef6db0212bcfdacd';

    try {
      console.log('Fetching plan assignment details for ID:', assignmentId);
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': getUserId(),
        },
      });

      console.log('Plan assignment fetch response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Plan assignment fetch response data:', data);
        console.log('Assignment object:', data.assignment);

        // Handle Mongoose document structure - data might be in _doc
        const assignment = data.assignment._doc || data.assignment;
        console.log('Processed assignment:', assignment);
        console.log('Coverage tiers in assignment:', assignment?.coverageTiers);

        return assignment;
      } else {
        console.error('Failed to fetch plan assignment details. Status:', response.status);
        const errorText = await response.text();
        console.error('Error response:', errorText);
      }
    } catch (error) {
      console.error('Error fetching plan assignment details:', error);
    }
    return null;
  };

  const fetchCompanyAndPlans = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch company details using the correct endpoint
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const getUserId = () => {
        const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
        if (!userId) {
          throw new Error('User ID not found. Please authenticate first.');
        }
        return userId;
      };

      const companyResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/company-benefits-settings/company/${companyId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': getUserId(),
        },
      });

      if (companyResponse.ok) {
        const companyData = await companyResponse.json();
        // Extract company info from the benefits settings response
        setCompany({
          _id: companyId,
          companyName: companyData.companyName || 'Company Name',
          employeeCount: companyData.employeeCount || 250
        });
      } else {
        console.error('Failed to fetch company details:', companyResponse.status);
        // Set fallback company data
        setCompany({
          _id: companyId,
          companyName: 'Company Name',
          employeeCount: 250
        });
      }

      // Fetch real plan assignments for the company
      const planAssignmentsResult = await getPlanAssignmentsByCompany(companyId);

      if (planAssignmentsResult.success && planAssignmentsResult.data) {
        const assignments = planAssignmentsResult.data.assignments;

        console.log('All assignments:', assignments.length);
        console.log('Selected assignment IDs from URL:', selectedAssignmentIds);
        console.log('Assignment IDs in data:', assignments.map(a => a._id));
        console.log('Sample assignment structure:', assignments[0]);

        // Filter assignments based on selected assignment IDs if provided
        let filteredAssignments = assignments;
        if (selectedAssignmentIds.length > 0) {
          // The URL contains assignment IDs, not plan IDs
          filteredAssignments = assignments.filter(assignment => {
            return selectedAssignmentIds.includes(assignment._id);
          });
        }

        console.log('Filtered assignments:', filteredAssignments.length);

        // Enhance assignments with plan details and assignment details
        const enhancedAssignments = await Promise.all(
          filteredAssignments.map(async (assignment) => {
            // Get planId as string
            const planIdString = typeof assignment.planId === 'string' ? assignment.planId : assignment.planId?._id || '';
            console.log('Processing assignment:', assignment._id, 'with planId:', planIdString);

            // Fetch plan details using the planId
            let planDetails = null;
            if (planIdString) {
              planDetails = await fetchPlanDetails(planIdString);
              console.log('Fetched plan details:', planDetails);
            }

            // Fetch assignment details to get coverage tiers
            const assignmentDetails = await fetchPlanAssignmentDetails(assignment._id);
            console.log('Fetched assignment details:', assignmentDetails);

            // Combine the data
            return {
              ...assignment,
              planDetails,
              assignmentDetails
            };
          })
        );

        setPlanAssignments(enhancedAssignments);

        // Set initial dates from the first assignment if available
        if (enhancedAssignments.length > 0) {
          const firstAssignment = enhancedAssignments[0];
          setEnrollmentStartDate(firstAssignment.enrollmentStartDate?.split('T')[0] || '2024-11-01');
          setEnrollmentEndDate(firstAssignment.enrollmentEndDate?.split('T')[0] || '2024-11-30');
          setPlanStartDate(firstAssignment.planEffectiveDate?.split('T')[0] || '2025-01-01');
          setPlanEndDate(firstAssignment.planEndDate?.split('T')[0] || '2025-12-31');
        }
      } else {
        console.error('Failed to fetch plan assignments:', planAssignmentsResult.error);
        setPlanAssignments([]);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      // Set fallback data even on error
      setCompany({
        _id: companyId,
        companyName: 'Company Name',
        employeeCount: 250
      });
      setPlanAssignments([]);
    } finally {
      setLoading(false);
    }
  }, [companyId, selectedAssignmentIds]);

  useEffect(() => {
    fetchCompanyAndPlans();
  }, [fetchCompanyAndPlans]);

  const saveDateChanges = async () => {
    if (planAssignments.length === 0) {
      return true; // No need to save if no assignments
    }

    setSaving(true);
    try {
      // Update all plan assignments with new dates
      const updatePromises = planAssignments.map(async (assignment) => {
        const updateData = {
          enrollmentStartDate: enrollmentStartDate,
          enrollmentEndDate: enrollmentEndDate,
          planEffectiveDate: planStartDate,
          planEndDate: planEndDate,
        };

        const result = await updatePlanAssignment(assignment._id, updateData);
        if (!result.success) {
          console.error(`Failed to update assignment ${assignment._id}:`, result.error);
          throw new Error(`Failed to update ${assignment.plan?.planName || 'plan'}: ${result.error}`);
        }
        return result.data;
      });

      await Promise.all(updatePromises);
      console.log('All plan assignments updated successfully');
      return true;
    } catch (error) {
      console.error('Error saving date changes:', error);
      alert(`Error saving changes: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    } finally {
      setSaving(false);
    }
  };

  const handleContinue = async () => {
    const saved = await saveDateChanges();
    if (saved) {
      // Navigate to review page with assignment IDs
      const assignmentIds = planAssignments.map(assignment => assignment._id);
      router.push(`/ai-enroller/manage-groups/company/${companyId}/review?assignments=${assignmentIds.join(',')}`);
    }
  };

  const handleSaveAndReturn = async () => {
    const saved = await saveDateChanges();
    if (saved) {
      router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Step Navigation */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex gap-3 overflow-x-auto">
            <button
              onClick={() => router.push('/ai-enroller')}
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineHome className="w-4 h-4" />
              Home
              <span className="flex items-center justify-center w-4 h-4 bg-purple-600 rounded-full ml-2">
                <HiOutlineCheckCircle className="w-5 h-5 text-white" />
              </span>
            </button>

            <button
              onClick={() => router.push('/ai-enroller/manage-groups/select-company')}
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineOfficeBuilding className="w-4 h-4" />
              Select Company
              <span className="flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2">
                <HiOutlineCheckCircle className="w-5 h-5" />
              </span>
            </button>

            <button
              onClick={() => router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`)}
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineClipboardList className="w-4 h-4" />
              View Plans
              <span className="flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2">
                <HiOutlineCheckCircle className="w-5 h-5" />
              </span>
            </button>

            <div
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-purple-50 text-purple-600 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineCalendar className="w-4 h-4" />
              Set Dates
            </div>

            <div
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineCheckCircle className="w-4 h-4" />
              Review
            </div>

            <div
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineCheckCircle className="w-4 h-4" />
              Confirmation
            </div>
          </div>
          <div className="mt-4">
            <div className="text-right text-sm text-gray-500">
              Step 4 of 6<br />
              <span className="text-xs text-purple-600 font-medium">Set plan dates</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8 bg-white">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Set Enrollment & Active Dates</h1>
          <p className="text-gray-600">
            Configure dates for {planAssignments.length} plan{planAssignments.length !== 1 ? 's' : ''}
            {planAssignments.length > 0 && (
              <span className="block text-sm text-gray-500 mt-1">
                {planAssignments.map(assignment => {
                  const planName = assignment.planDetails?.planName || 'Unknown Plan';
                  return planName;
                }).join(', ')}
              </span>
            )}
          </p>
        </div>

        {/* No Plans Message */}
        {planAssignments.length === 0 && !loading && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-yellow-100 rounded-xl flex items-center justify-center">
                <span className="text-yellow-600 text-lg">⚠️</span>
              </div>
              <div>
                <h3 className="font-semibold text-yellow-800">No Plan Assignments Found</h3>
                <p className="text-sm text-yellow-700">
                  No plan assignments were found for this company. Please create plan assignments first.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Plan Details */}
        {planAssignments.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-xl p-6 mb-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-blue-100 rounded-xl flex items-center justify-center">
                <span className="text-blue-600 text-lg">📋</span>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Selected Plans</h2>
                <p className="text-sm text-gray-600">Plans that will be updated with new dates</p>
              </div>
            </div>

            <div className="space-y-4">
              {planAssignments.map((assignment) => {
                // Use enhanced plan details
                const planName = assignment.planDetails?.planName || 'Unknown Plan';
                const planCode = assignment.planDetails?.planCode || 'N/A';
                const coverageType = assignment.planDetails?.coverageType || 'Unknown Coverage';
                const carrier = assignment.planDetails?.carrierName || 'Unknown Carrier';
                const metalTier = assignment.planDetails?.metalTier || '';

                // Get pricing data from assignment details (coverage tiers)
                const coverageTiers = assignment.assignmentDetails?.coverageTiers || assignment.coverageTiers || [];
                const firstTier = coverageTiers[0] || {};
                const totalCost = firstTier.totalCost || 0;
                const employerCost = firstTier.employerCost || 0;
                const employeeCost = firstTier.employeeCost || (totalCost - employerCost);

                return (
                  <div key={assignment._id} className="border-l-4 border-blue-500 pl-4 py-3 bg-gray-50 rounded-r-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{planName}</h3>
                        <p className="text-sm text-gray-600">
                          {carrier} • {coverageType} {metalTier && `• ${metalTier}`} • {planCode}
                        </p>
                        <p className="text-xs text-gray-500">
                          Current: {assignment.enrollmentStartDate?.split('T')[0] || 'N/A'} to {assignment.enrollmentEndDate?.split('T')[0] || 'N/A'}
                        </p>
                        {coverageTiers.length > 1 && (
                          <p className="text-xs text-blue-600 mt-1">
                            {coverageTiers.length} coverage tiers available
                          </p>
                        )}
                      </div>
                      <div className="text-right ml-4">
                        <p className="text-sm font-medium text-gray-900">
                          ${totalCost.toFixed(2)}/month
                        </p>
                        <p className="text-xs text-gray-500">
                          Employer: ${employerCost.toFixed(2)}
                        </p>
                        {employeeCost > 0 && (
                          <p className="text-xs text-gray-500">
                            Employee: ${employeeCost.toFixed(2)}
                          </p>
                        )}
                        {coverageTiers.length > 0 && (
                          <p className="text-xs text-gray-400 mt-1">
                            {firstTier.tierName || 'Employee Only'}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Date Configuration */}
        <div className="bg-white border border-gray-200 rounded-xl p-6 mb-6">
          <div className="flex items-center gap-3 mb-6">
            <HiOutlineCalendar className="text-gray-600 w-6 h-6" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Date Configuration</h2>
              <p className="text-sm text-gray-600">Set enrollment and plan active dates for these plans</p>
            </div>
          </div>



          {/* Date Inputs */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Enrollment Dates */}
            <div className="border-l-4 border-blue-500 pl-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Open Enrollment Dates</h3>
              <p className="text-sm text-gray-600 mb-4">When employees can enroll in this plan</p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Enrollment Start Date</label>
                  <input
                    type="date"
                    value={enrollmentStartDate}
                    onChange={(e) => setEnrollmentStartDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white"
                    style={{ color: '#111827', backgroundColor: 'white' }}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Enrollment End Date</label>
                  <input
                    type="date"
                    value={enrollmentEndDate}
                    onChange={(e) => setEnrollmentEndDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white"
                    style={{ color: '#111827', backgroundColor: 'white' }}
                  />
                </div>
              </div>
            </div>

            {/* Plan Active Dates */}
            <div className="border-l-4 border-green-500 pl-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Plan Active Dates</h3>
              <p className="text-sm text-gray-600 mb-4">When this plan coverage is active</p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Plan Start Date</label>
                  <input
                    type="date"
                    value={planStartDate}
                    onChange={(e) => setPlanStartDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white"
                    style={{ color: '#111827', backgroundColor: 'white' }}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Plan End Date</label>
                  <input
                    type="date"
                    value={planEndDate}
                    onChange={(e) => setPlanEndDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white"
                    style={{ color: '#111827', backgroundColor: 'white' }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Settings */}
        <div className="bg-white border border-gray-200 rounded-xl p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Additional Settings</h2>
          <p className="text-sm text-blue-600 mb-6">Optional configuration for this plan</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Waiting Period (days)</label>
              <input
                type="number"
                value={waitingPeriod}
                onChange={(e) => setWaitingPeriod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white"
                style={{ color: '#111827', backgroundColor: 'white' }}
                min="0"
                max="365"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Grace Period (days)</label>
              <input
                type="number"
                value={gracePeriod}
                onChange={(e) => setGracePeriod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white"
                style={{ color: '#111827', backgroundColor: 'white' }}
                min="0"
                max="90"
              />
            </div>
          </div>
        </div>

        {/* Success Message */}
        <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-8">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs">✓</span>
            </div>
            <div>
              <span className="font-medium text-green-800">Plan Configuration Complete</span>
              <p className="text-green-700 text-sm">You can now return to configure other plans or continue the workflow</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <button
            onClick={handleSaveAndReturn}
            disabled={saving}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? 'Saving...' : 'Save & Return to Plans'}
          </button>
          <button
            onClick={handleContinue}
            disabled={saving}
            className="flex-1 px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center justify-center gap-2 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            type="button"
          >
            {saving ? 'Saving...' : 'Continue to Review'}
            {!saving && <span>→</span>}
          </button>
        </div>
      </div>
    </div>
  );
}
