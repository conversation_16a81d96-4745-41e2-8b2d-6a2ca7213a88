import express from 'express';
import Controller from '../interfaces/controller.interface';
import CommonService from '../services/common.service';
import { ChatService } from '../services/chat.service';
import EventPayloadI from '../interfaces/eventPayload.interface';
import logger, { prettyJSON } from '../utils/logger';
import { homeUnverifiedLayout } from '../layouts/homeUnverified.layout';
import { homeVerifiedLayout } from '../layouts/homeVerified.layout';
import InteractionPayloadI from '../interfaces/interactionPayload.interface';
import axios from 'axios';
import EnvService from '../services/env.service';
import SlackOauthModelClass from '../nosql/slackOauth.model';
import FinchConnectionModelClass from '../nosql/finchConnection.model';
import FinchIndividualModelClass from '../nosql/finchIndividualData.model';
import CompanyDocumentModelClass from '../nosql/companyDocument.model';

class SlackController implements Controller {
  public router = express.Router();
  public commonService = new CommonService();
  public chatService = new ChatService();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post(`/slack/event`, this.handleEvent);
    this.router.post(`/slack/interaction`, this.handleInteraction);
    this.router.get(`/slack/oauth/access-token`, this.handleOAuthAccessToken);
  }

  private handleEvent = async (
    request: express.Request,
    response: express.Response
  ) => {
    if (request.body.type === 'url_verification') {
      response.send(request.body.challenge);
      return;
    }
    response.send();

    const payload = request.body as EventPayloadI;
    const { user, type, tab, bot_id, channel_type } = payload.event;
    logger.info(`Received event payload: ${prettyJSON(payload)}`);
    const slackOauthModel = await SlackOauthModelClass.getDataBySlackTeamId(
      payload.team_id
    );
    const companyDocumentModel =
      await CompanyDocumentModelClass.getDataBySlackTeamId(payload.team_id);
    const slackBotToken = slackOauthModel?.slackBotOauthToken ?? '';
    logger.info(`slackBotToken ${slackBotToken}`);
    if (type === 'app_home_opened' && tab === 'home') {
      // TODO(ankit): Check if the user has connected their Finch account and fetch the access token from DB.
      const finchAccessToken =
        (await FinchConnectionModelClass.getDataBySlackTeamId(payload.team_id))
          ?.finchAccessToken ?? '';
      logger.info(`finchAccessToken ${finchAccessToken}`);
      if (!finchAccessToken || finchAccessToken === '') {
        await this.commonService.publishHomeTab({
          slackBotToken,
          userId: user,
          view: homeUnverifiedLayout({ slackTeamId: payload.team_id }),
        });
      } else {
        const userEntity = await this.commonService.getUser({
          slackBotToken,
          userId: user,
        });
        const email = userEntity?.profile?.email ?? '';
        const finchIndividualEntity =
          await FinchIndividualModelClass.getDataByEmail(email);
        if (finchIndividualEntity) {
          await this.commonService.publishHomeTab({
            slackBotToken,
            userId: user,
            view: homeVerifiedLayout({
              showDisconnectButton:
                slackOauthModel?.slackInstallerUserId === user,
              pageNum: 1,
              firstName: finchIndividualEntity.finchFirstName,
              lastName: finchIndividualEntity.finchLastName,
              department: finchIndividualEntity.department,
              residence: finchIndividualEntity.finchAddress,
              emails: email,
              title: finchIndividualEntity.title,
              employmentType: finchIndividualEntity.employmentSubtype,
              startDate: finchIndividualEntity.startDate,
              endDate: finchIndividualEntity.endDate,
              dob: finchIndividualEntity.dob,
              incomeUnit: finchIndividualEntity.incomeUnit,
              incomeAmount: finchIndividualEntity.incomeAmount,
              incomeCurrency: finchIndividualEntity.incomeCurrency,
              incomeEffectiveDate: finchIndividualEntity.incomeEffectiveDate,
              benefits: finchIndividualEntity.benefits,
              timeoffs: finchIndividualEntity.timeoffs,
              taxes: finchIndividualEntity.payments[0].taxes, // TODO(ankit): Ask Shan if we only need to show the recent payment.
              payStub: {
                payPeriod: finchIndividualEntity.payments[0].pay_period,
                payDate: finchIndividualEntity.payments[0].pay_date,
                debitDate: finchIndividualEntity.payments[0].debit_date,
                netPay: finchIndividualEntity.payments[0].net_pay,
                grossPay: finchIndividualEntity.payments[0].gross_pay,
              },
              notificationPaused: false,
              companyDocument: companyDocumentModel,
            }),
          });
        }
      }
    }

    if (type === 'message' && !bot_id && channel_type === 'im') {
      this.handleMessage(payload, slackBotToken);
    }
  };

  private handleInteraction = async (
    request: express.Request,
    response: express.Response
  ) => {
    // Send acknowledgement response to Slack immediately.
    response.send();
    const payload = JSON.parse(request.body.payload) as InteractionPayloadI;
    logger.info(
      `Received interaction payload: ${prettyJSON(request.body.payload)}`
    );
    const slackOauthModel = await SlackOauthModelClass.getDataBySlackTeamId(
      payload.team.id
    );
    const companyDocumentModel =
      await CompanyDocumentModelClass.getDataBySlackTeamId(payload.team.id);
    const slackBotToken = slackOauthModel?.slackBotOauthToken ?? '';
    if (payload.type === 'block_actions') {
      console.log(
        '🚀 ~ SlackController ~ payload.actions[0].action_id',
        payload.actions[0].action_id
      );
      if (payload.actions[0].action_id === 'disconnect') {
        await FinchConnectionModelClass.deleteDataBySlackTeamId(
          payload.team.id
        );
        await this.commonService.publishHomeTab({
          slackBotToken,
          userId: payload.user.id,
          view: homeUnverifiedLayout({ slackTeamId: payload.team.id }),
        });
        return;
      }
      if (payload.actions[0].action_id === 'view_pay_history') {
        await this.commonService.openViewPayHistoryModal({
          slackBotToken,
          userId: payload.user.id,
          triggerId: payload.trigger_id,
        });
        return;
      }
      if (payload.actions[0].action_id === 'view_help_center') {
        await this.commonService.openViewHelpCenterModal({
          slackBotToken,
          userId: payload.user.id,
          triggerId: payload.trigger_id,
        });
        return;
      }
      if (payload.actions[0].action_id === 'pauseNotification') {
        console.log('pausing notification');
        const userEntity = await this.commonService.getUser({
          slackBotToken,
          userId: payload.user.id,
        });
        const email = userEntity?.profile?.email ?? '';
        const finchIndividualEntity =
          await FinchIndividualModelClass.getDataByEmail(email);
        if (finchIndividualEntity) {
          await this.commonService.publishHomeTab({
            slackBotToken,
            userId: payload.user.id,
            view: homeVerifiedLayout({
              showDisconnectButton:
                slackOauthModel?.slackInstallerUserId === payload.user.id,
              pageNum: 1,
              firstName: finchIndividualEntity.finchFirstName,
              lastName: finchIndividualEntity.finchLastName,
              department: finchIndividualEntity.department,
              residence: finchIndividualEntity.finchAddress,
              emails: email,
              title: finchIndividualEntity.title,
              employmentType: finchIndividualEntity.employmentSubtype,
              startDate: finchIndividualEntity.startDate,
              endDate: finchIndividualEntity.endDate,
              dob: finchIndividualEntity.dob,
              incomeUnit: finchIndividualEntity.incomeUnit,
              incomeAmount: finchIndividualEntity.incomeAmount,
              incomeCurrency: finchIndividualEntity.incomeCurrency,
              incomeEffectiveDate: finchIndividualEntity.incomeEffectiveDate,
              benefits: finchIndividualEntity.benefits,
              timeoffs: finchIndividualEntity.timeoffs,
              taxes: finchIndividualEntity.payments[0].taxes, // TODO(ankit): Ask Shan if we only need to show the recent payment.
              payStub: {
                payPeriod: finchIndividualEntity.payments[0].pay_period,
                payDate: finchIndividualEntity.payments[0].pay_date,
                debitDate: finchIndividualEntity.payments[0].debit_date,
                netPay: finchIndividualEntity.payments[0].net_pay,
                grossPay: finchIndividualEntity.payments[0].gross_pay,
              },
              notificationPaused: true,
              companyDocument: companyDocumentModel,
            }),
          });
        }
        return;
      }
      if (payload.actions[0].action_id === 'resumeNotification') {
        console.log('resuming notification');
        const userEntity = await this.commonService.getUser({
          slackBotToken,
          userId: payload.user.id,
        });
        const email = userEntity?.profile?.email ?? '';
        const finchIndividualEntity =
          await FinchIndividualModelClass.getDataByEmail(email);
        if (finchIndividualEntity) {
          await this.commonService.publishHomeTab({
            slackBotToken,
            userId: payload.user.id,
            view: homeVerifiedLayout({
              showDisconnectButton:
                slackOauthModel?.slackInstallerUserId === payload.user.id,
              pageNum: 1,
              firstName: finchIndividualEntity.finchFirstName,
              lastName: finchIndividualEntity.finchLastName,
              department: finchIndividualEntity.department,
              residence: finchIndividualEntity.finchAddress,
              emails: email,
              title: finchIndividualEntity.title,
              employmentType: finchIndividualEntity.employmentSubtype,
              startDate: finchIndividualEntity.startDate,
              endDate: finchIndividualEntity.endDate,
              dob: finchIndividualEntity.dob,
              incomeUnit: finchIndividualEntity.incomeUnit,
              incomeAmount: finchIndividualEntity.incomeAmount,
              incomeCurrency: finchIndividualEntity.incomeCurrency,
              incomeEffectiveDate: finchIndividualEntity.incomeEffectiveDate,
              benefits: finchIndividualEntity.benefits,
              timeoffs: finchIndividualEntity.timeoffs,
              taxes: finchIndividualEntity.payments[0].taxes, // TODO(ankit): Ask Shan if we only need to show the recent payment.
              payStub: {
                payPeriod: finchIndividualEntity.payments[0].pay_period,
                payDate: finchIndividualEntity.payments[0].pay_date,
                debitDate: finchIndividualEntity.payments[0].debit_date,
                netPay: finchIndividualEntity.payments[0].net_pay,
                grossPay: finchIndividualEntity.payments[0].gross_pay,
              },
              notificationPaused: false,
              companyDocument: companyDocumentModel,
            }),
          });
        }
        return;
      }
      if (payload.actions[0].action_id === '🤓 Training') {
        console.log('🤓 Training');
        // post a message to the user
        await this.commonService.postMessageInDm({
          slackBotToken,
          sink: payload.user.id,
          text: 'How can I help you today in your query related to Training?',
        });
      }
      if (payload.actions[0].action_id === 'tab1dasfiehnedsv') {
        console.log('tab 1 clicked');
        const userEntity = await this.commonService.getUser({
          slackBotToken,
          userId: payload.user.id,
        });
        const email = userEntity?.profile?.email ?? '';
        const finchIndividualEntity =
          await FinchIndividualModelClass.getDataByEmail(email);
        if (finchIndividualEntity) {
          await this.commonService.publishHomeTab({
            slackBotToken,
            userId: payload.user.id,
            view: homeVerifiedLayout({
              showDisconnectButton:
                slackOauthModel?.slackInstallerUserId === payload.user.id,
              pageNum: 1,
              firstName: finchIndividualEntity.finchFirstName,
              lastName: finchIndividualEntity.finchLastName,
              department: finchIndividualEntity.department,
              residence: finchIndividualEntity.finchAddress,
              emails: email,
              title: finchIndividualEntity.title,
              employmentType: finchIndividualEntity.employmentSubtype,
              startDate: finchIndividualEntity.startDate,
              endDate: finchIndividualEntity.endDate,
              dob: finchIndividualEntity.dob,
              incomeUnit: finchIndividualEntity.incomeUnit,
              incomeAmount: finchIndividualEntity.incomeAmount,
              incomeCurrency: finchIndividualEntity.incomeCurrency,
              incomeEffectiveDate: finchIndividualEntity.incomeEffectiveDate,
              benefits: finchIndividualEntity.benefits,
              timeoffs: finchIndividualEntity.timeoffs,
              taxes: finchIndividualEntity.payments[0].taxes, // TODO(ankit): Ask Shan if we only need to show the recent payment.
              payStub: {
                payPeriod: finchIndividualEntity.payments[0].pay_period,
                payDate: finchIndividualEntity.payments[0].pay_date,
                debitDate: finchIndividualEntity.payments[0].debit_date,
                netPay: finchIndividualEntity.payments[0].net_pay,
                grossPay: finchIndividualEntity.payments[0].gross_pay,
              },
              notificationPaused: false,
              companyDocument: companyDocumentModel,
            }),
          });
        }
        return;
      }
      if (payload.actions[0].action_id === 'tab2dasfiehnedsv') {
        console.log('tab 2 clicked');
        const userEntity = await this.commonService.getUser({
          slackBotToken,
          userId: payload.user.id,
        });
        const email = userEntity?.profile?.email ?? '';
        const finchIndividualEntity =
          await FinchIndividualModelClass.getDataByEmail(email);
        if (finchIndividualEntity) {
          await this.commonService.publishHomeTab({
            slackBotToken,
            userId: payload.user.id,
            view: homeVerifiedLayout({
              showDisconnectButton:
                slackOauthModel?.slackInstallerUserId === payload.user.id,
              pageNum: 2,
              firstName: finchIndividualEntity.finchFirstName,
              lastName: finchIndividualEntity.finchLastName,
              department: finchIndividualEntity.department,
              residence: finchIndividualEntity.finchAddress,
              emails: email,
              title: finchIndividualEntity.title,
              employmentType: finchIndividualEntity.employmentSubtype,
              startDate: finchIndividualEntity.startDate,
              endDate: finchIndividualEntity.endDate,
              dob: finchIndividualEntity.dob,
              incomeUnit: finchIndividualEntity.incomeUnit,
              incomeAmount: finchIndividualEntity.incomeAmount,
              incomeCurrency: finchIndividualEntity.incomeCurrency,
              incomeEffectiveDate: finchIndividualEntity.incomeEffectiveDate,
              benefits: finchIndividualEntity.benefits,
              timeoffs: finchIndividualEntity.timeoffs,
              taxes: finchIndividualEntity.payments[0].taxes, // TODO(ankit): Ask Shan if we only need to show the recent payment.
              payStub: {
                payPeriod: finchIndividualEntity.payments[0].pay_period,
                payDate: finchIndividualEntity.payments[0].pay_date,
                debitDate: finchIndividualEntity.payments[0].debit_date,
                netPay: finchIndividualEntity.payments[0].net_pay,
                grossPay: finchIndividualEntity.payments[0].gross_pay,
              },
              notificationPaused: false,
              companyDocument: companyDocumentModel,
            }),
          });
        }
        return;
      }
      if (payload.actions[0].action_id === 'tab3dasfiehnedsv') {
        console.log('tab 3 clicked');
        const userEntity = await this.commonService.getUser({
          slackBotToken,
          userId: payload.user.id,
        });
        const email = userEntity?.profile?.email ?? '';
        const finchIndividualEntity =
          await FinchIndividualModelClass.getDataByEmail(email);
        if (finchIndividualEntity) {
          await this.commonService.publishHomeTab({
            slackBotToken,
            userId: payload.user.id,
            view: homeVerifiedLayout({
              showDisconnectButton:
                slackOauthModel?.slackInstallerUserId === payload.user.id,
              pageNum: 3,
              firstName: finchIndividualEntity.finchFirstName,
              lastName: finchIndividualEntity.finchLastName,
              department: finchIndividualEntity.department,
              residence: finchIndividualEntity.finchAddress,
              emails: email,
              title: finchIndividualEntity.title,
              employmentType: finchIndividualEntity.employmentSubtype,
              startDate: finchIndividualEntity.startDate,
              endDate: finchIndividualEntity.endDate,
              dob: finchIndividualEntity.dob,
              incomeUnit: finchIndividualEntity.incomeUnit,
              incomeAmount: finchIndividualEntity.incomeAmount,
              incomeCurrency: finchIndividualEntity.incomeCurrency,
              incomeEffectiveDate: finchIndividualEntity.incomeEffectiveDate,
              benefits: finchIndividualEntity.benefits,
              timeoffs: finchIndividualEntity.timeoffs,
              taxes: finchIndividualEntity.payments[0].taxes, // TODO(ankit): Ask Shan if we only need to show the recent payment.
              payStub: {
                payPeriod: finchIndividualEntity.payments[0].pay_period,
                payDate: finchIndividualEntity.payments[0].pay_date,
                debitDate: finchIndividualEntity.payments[0].debit_date,
                netPay: finchIndividualEntity.payments[0].net_pay,
                grossPay: finchIndividualEntity.payments[0].gross_pay,
              },
              notificationPaused: false,
              companyDocument: companyDocumentModel,
            }),
          });
        }
        return;
      }
      if (payload.actions[0].action_id === 'tab4dasfiehnedsv') {
        console.log('tab 4 clicked');
        const userEntity = await this.commonService.getUser({
          slackBotToken,
          userId: payload.user.id,
        });
        const email = userEntity?.profile?.email ?? '';
        const finchIndividualEntity =
          await FinchIndividualModelClass.getDataByEmail(email);
        if (finchIndividualEntity) {
          await this.commonService.publishHomeTab({
            slackBotToken,
            userId: payload.user.id,
            view: homeVerifiedLayout({
              showDisconnectButton:
                slackOauthModel?.slackInstallerUserId === payload.user.id,
              pageNum: 4,
              firstName: finchIndividualEntity.finchFirstName,
              lastName: finchIndividualEntity.finchLastName,
              department: finchIndividualEntity.department,
              residence: finchIndividualEntity.finchAddress,
              emails: email,
              title: finchIndividualEntity.title,
              employmentType: finchIndividualEntity.employmentSubtype,
              startDate: finchIndividualEntity.startDate,
              endDate: finchIndividualEntity.endDate,
              dob: finchIndividualEntity.dob,
              incomeUnit: finchIndividualEntity.incomeUnit,
              incomeAmount: finchIndividualEntity.incomeAmount,
              incomeCurrency: finchIndividualEntity.incomeCurrency,
              incomeEffectiveDate: finchIndividualEntity.incomeEffectiveDate,
              benefits: finchIndividualEntity.benefits,
              timeoffs: finchIndividualEntity.timeoffs,
              taxes: finchIndividualEntity.payments[0].taxes, // TODO(ankit): Ask Shan if we only need to show the recent payment.
              payStub: {
                payPeriod: finchIndividualEntity.payments[0].pay_period,
                payDate: finchIndividualEntity.payments[0].pay_date,
                debitDate: finchIndividualEntity.payments[0].debit_date,
                netPay: finchIndividualEntity.payments[0].net_pay,
                grossPay: finchIndividualEntity.payments[0].gross_pay,
              },
              notificationPaused: false,
              companyDocument: companyDocumentModel,
            }),
          });
        }
        return;
      }
    }
  };

  private handleOAuthAccessToken = async (
    request: express.Request,
    response: express.Response
  ) => {
    const { code } = request.query;
    const oauthResponse = await axios.get(
      `https://slack.com/api/oauth.v2.access?client_id=${
        EnvService.env().SLACK_CLIENT_ID
      }&client_secret=${EnvService.env().SLACK_CLIENT_SECRET}&code=${code}`
    );

    if (!oauthResponse.data.ok) {
      response.send(oauthResponse.data);
      return;
    }
    logger.info(`oauthResponse.data: ${prettyJSON(oauthResponse.data)}`);

    try {
      await SlackOauthModelClass.addData({
        slackBotOauthToken: oauthResponse.data.access_token,
        slackTeamId: oauthResponse.data.team.id,
        slackTeamName: oauthResponse.data.team.name,
        slackInstallerUserId: oauthResponse.data.authed_user.id,
        slackInstalledAtTs: Date.now(),
      });
    } catch (error) {
      response.send(`Error while creating installation: ${error}`);
    }
    response.send('OK');
  };

  private handleMessage = async (
    payloadEvent: EventPayloadI,
    slackBotToken: string
    // response: express.Response
  ) => {
    try {
      const userMessage = payloadEvent.event.text;
      const userId = payloadEvent.event.user;
      const teamId = payloadEvent.team_id;

      // Get the response from the chat service
      const responseForChat = await this.chatService.processMessage(
        userId,
        userMessage,
        teamId
      );
      // console.log("🚀 ~ SlackController ~ responseForChat:", responseForChat.message)
      // Post the response to slack
      try {
        const slackResponse = await axios.post(
          'https://slack.com/api/chat.postMessage',
          {
            channel: userId,
            text: responseForChat.message,
            team: teamId,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${slackBotToken}`,
            },
          }
        );
        console.log(
          '🚀 ~ SlackController ~ slackResponse.data:',
          slackResponse.data
        );
        console.log('Message sent successfully');
      } catch (error) {
        console.error('Error sending message to slack:', error);
      }
    } catch (error) {
      console.error('Error processing message through chat service:', error);
    }
    // response.send('Message received and processing...');
  };
}

export default SlackController;
