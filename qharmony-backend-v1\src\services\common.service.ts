import axios from 'axios';
import logger, { prettyJSO<PERSON> } from '../utils/logger';
import SlackUserI from '../interfaces/slackUser.interface';
import { viewPayHistoryModalLayout } from '../layouts/viewPayHistoryModal.layout';
import { viewHelpCenterModalLayout } from '../layouts/viewHelpCenterModal.layout';

class CommonService {
  public async publishHomeTab({
    slackBotToken,
    userId,
    view,
  }: {
    slackBotToken: string;
    userId: string;
    view: any;
  }): Promise<void> {
    try {
      logger.info('Publishing home tab, slackBotToken:' + slackBotToken)
      const response = await axios.post(
        `https://slack.com/api/views.publish`,
        {
          user_id: userId,
          view,
        },
        {
          headers: {
            Authorization: `Bearer ${slackBotToken}`,
          },
        }
      );
      if (!response || response.status != 200 || response.data.ok === false) {
        throw Error(
          `Failed to publish home tab, request body: ${prettyJSON(
            response.data
          )} response status: ${response.status} response data: ${prettyJSON(
            response.data
          )}`
        );
      }
    } catch (e) {
      logger.error(`Error publishing home tab for user ${userId}`);
      logger.error(e);
    }
  }

  public async openViewPayHistoryModal({
    slackBotToken,
    userId,
    triggerId,
  }: {
    slackBotToken: string;
    userId: string;
    triggerId: string;
  }): Promise<void> {
    try {
      logger.info(
        `Opening create alarm modal for user ${userId} with triggerId ${triggerId} and slackBotToken ${slackBotToken}`
      );
      await axios.post(
        `https://slack.com/api/views.open`,
        {
          trigger_id: triggerId,
          view: viewPayHistoryModalLayout(),
        },
        {
          headers: {
            Authorization: `Bearer ${slackBotToken}`,
          },
        }
      );
    } catch (e) {
      logger.error(`Error publishing home tab for user ${userId}`);
      logger.error(prettyJSON(e));
    }
  }

  public async openViewHelpCenterModal({
    slackBotToken,
    userId,
    triggerId,
  }: {
    slackBotToken: string;
    userId: string;
    triggerId: string;
  }): Promise<void> {
    try {
      logger.info(
        `Opening view help center modal for user ${userId} with triggerId ${triggerId} and slackBotToken ${slackBotToken}`
      );
      await axios.post(
        `https://slack.com/api/views.open`,
        {
          trigger_id: triggerId,
          view: viewHelpCenterModalLayout()
        },
        {
          headers: {
            Authorization: `Bearer ${slackBotToken}`,
          },
        }
      );
    } catch (e) {
      logger.error(`Error publishing home tab for user ${userId}`);
      logger.error(prettyJSON(e));
    }
  }

  public async getAllUsers({
    slackBotToken,
  }: {
    slackBotToken: string;
  }): Promise<SlackUserI[] | undefined> {
    try {
      const response = await axios.get(`https://slack.com/api/users.list`, {
        headers: {
          Authorization: `Bearer ${slackBotToken}`,
        },
      });
      if (!response || response.status != 200 || response.data.ok === false) {
        throw Error(
          `Failed to get users, response status: ${
            response.status
          } response data: ${prettyJSON(response.data)}`
        );
      }
      return response.data.members as SlackUserI[];
    } catch (e: any) {
      logger.error(`Error getting users`);
      logger.error(e.message);
      logger.error(prettyJSON(e));
    }
    logger.error(`Failed getting users`);
    return undefined;
  }

  public async getUser({
    slackBotToken,
    userId,
  }: {
    slackBotToken: string;
    userId: string;
  }): Promise<SlackUserI | undefined> {
    try {
      const response = await axios.get(`https://slack.com/api/users.info`, {
        headers: {
          Authorization: `Bearer ${slackBotToken}`,
        },
        params: {
          user: userId,
        },
      });
      if (!response || response.status != 200 || response.data.ok === false) {
        throw Error(
          `Failed to get user, response status: ${
            response.status
          } response data: ${prettyJSON(response.data)}`
        );
      }
      return response.data.user as SlackUserI;
    } catch (e: any) {
      logger.error(`Error getting user`);
      logger.error(e.message);
      logger.error(prettyJSON(e));
    }
    logger.error(`Failed getting user`);
    return undefined;
  }

  public async postMessageInDm({
    slackBotToken,
    sink,
    text,
    ts,
    blocks,
    metadata,
  }: {
    slackBotToken: string;
    sink: string;
    text: string;
    ts?: string;
    blocks?: string;
    metadata?: string;
  }): Promise<
    | {
        message: any;
        channel: string;
        ts: string;
        ok: boolean;
      }
    | undefined
  > {
    let body = {
      channel: sink,
      text,
      metadata,
    } as any;
    if (blocks) {
      body = {
        ...body,
        blocks,
      };
    }
    if (ts) {
      body = {
        ...body,
        thread_ts: ts,
      };
    }
    try {
      const response = await axios.post(
        `https://slack.com/api/chat.postMessage`,
        body,
        {
          headers: {
            Authorization: `Bearer ${slackBotToken}`,
          },
        }
      );
      if (!response || response.status != 200 || response.data.ok === false) {
        throw Error(
          `Failed to post chat, request body: ${prettyJSON(
            body
          )} response status: ${response.status} response data: ${prettyJSON(
            response.data
          )}`
        );
      }
      return response.data as {
        message: any;
        channel: string;
        ts: string;
        ok: boolean;
      };
    } catch (e: any) {
      logger.error(`Error posting message to ${sink}`);
      logger.error(e.message);
      logger.error(prettyJSON(e));
    }
    return undefined;
  }
}

export default CommonService;
