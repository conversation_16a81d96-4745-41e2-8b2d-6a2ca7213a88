"use client";

import { useEffect, useState } from "react";
import {
  Typo<PERSON>,
  Box,
  Grid,
  Paper,
  Button,
  Tabs,
  Tab,
  Checkbox,
} from "@mui/material";
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { getUsersCompanyId } from "@/redux/reducers/userSlice";
import { getRequest, postRequest } from "@/APILayer/axios_helper";
import { ArrowLeft, ArrowRight, MoveRight } from "lucide-react";
import { maskBenefitCategory, maskedSubCategory } from "@/middleware/company_middleware";
// import { renameBenefits } from "@/middleware/company_middleware";

interface Benefit {
  _id: string;
  companyId: string;
  type: string;
  subType: string;
  heading: string;
  description: string;
  imageS3Urls: string[];
  links: string[];
  isActivated: boolean;
}

interface BenefitCategory {
  benefitType: string;
  benefits: Benefit[];
}

interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  companyId: string;
  isAdmin: boolean;
  isBroker: boolean;
  isActivated: boolean;
  isDisabled: boolean;
  details: {
    phoneNumber: string;
    department: string;
    title: string;
    role: string;
  };
}

interface GroupType {
  _id: string;
  companyId: string;
  name: string;
  document_ids: string[];
  employee_ids: string[];
}

const extractDocumentName = (url: string): string => {
  const parts = url.split("_____");
  return parts.length > 1 ? parts[1] : url;
};

const ManageGroupsWithTabs: React.FC = () => {
  const params = useParams<{ groupId: string }>();
  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));
  const [tabIndex, setTabIndex] = useState<number>(0);
  const [group, setGroup] = useState<GroupType | null>(null);
  const [benefitsData, setBenefitsData] = useState<BenefitCategory[]>([]);
  const [teamMembers, setTeamMembers] = useState<User[]>([]);
  const [isUpdated, setIsUpdated] = useState<boolean>(true);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const router = useRouter()

  useEffect(() => {
    const fetchData = async () => {
      await fetchGroupDetails();
      await fetchTeamMembers();
      // await fetchBenefitsData();
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (tabIndex === 0) {
      fetchTeamMembers();
    } else if (tabIndex === 1) {
      fetchBenefitsData();
    }
  }, [tabIndex]);

  useEffect(() => {
    if (group && teamMembers.length > 0) {
      const allSelected = group.employee_ids.length === teamMembers.length;
      setSelectAll(allSelected);
    }
  }, [group, teamMembers]);

  const fetchGroupDetails = async () => {
    try {
      const data: { group: GroupType } = await getRequest(
        `/group-detail/${params.groupId}`,
      );
      setGroup(data.group);
    } catch (error) {
      console.error("Error fetching group details:", error);
    }
  };

  const fetchBenefitsData = async () => {
    try {
      const response: { benefitsPerType: BenefitCategory[] } = await getRequest(
        `/benefits/all-benefits`,
        { companyId },
      );
      const filteredData: BenefitCategory[] = response.benefitsPerType.reduce<
        BenefitCategory[]
      >((acc, category) => {
        const benefits = category.benefits.filter(
          (benefit) => benefit.isActivated,
        );

        if (benefits.length > 0) {
          acc.push({ ...category, benefits });
        }

        return acc;
      }, []);

      setBenefitsData(filteredData);
    } catch (error) {
      console.error("Error fetching benefits data:", error);
    }
  };

  const fetchTeamMembers = async () => {
    try {
      const data: { employees: User[] } = await getRequest(
        "/admin/all-employees",
      );
      setTeamMembers(data.employees);

      if (group && data.employees.length > 0) {
        const allSelected = data.employees.length === group.employee_ids.length;
        setSelectAll(allSelected);
      }
    } catch (error) {
      console.error("Error fetching team members:", error);
    }
  };

  const handleCheckboxChange = (id: string, type: "employee" | "document") => {
    if (!group) return;

    setIsUpdated(false);
    setGroup((prev) => {
      if (!prev) return prev;
      const updatedIds = prev[
        type === "employee" ? "employee_ids" : "document_ids"
      ].includes(id)
        ? prev[type === "employee" ? "employee_ids" : "document_ids"].filter(
          (existingId) => existingId !== id,
        )
        : [...prev[type === "employee" ? "employee_ids" : "document_ids"], id];

      return {
        ...prev,
        [type === "employee" ? "employee_ids" : "document_ids"]: updatedIds,
      };
    });
  };

  const handleSelectAllChange = () => {
    setSelectAll((prev) => !prev);
    setIsUpdated(false);
    setGroup((prev) => {
      if (!prev) return prev;
      const updatedEmployeeIds = !selectAll ? teamMembers.map(member => member._id) : [];
      return {
        ...prev,
        employee_ids: updatedEmployeeIds,
      };
    });
  };

  const handleUpdateClick = async () => {
    let data = {
      groupId: params.groupId,
      documentIds: group?.document_ids,
      employeeIds: group?.employee_ids,
    };
    const response = await postRequest("/group/update-group", data);
    if (response.status === 200) {
      setIsUpdated(true);
    }
  };

  const handleSubCategoryCheckboxChange = (subCategory: Benefit) => {
    if (!group) return;

    setIsUpdated(false);
    setGroup((prev) => {
      if (!prev) return prev;

      const allSelected = subCategory.imageS3Urls.every(url => prev.document_ids.includes(url));

      const updatedDocumentIds = allSelected
        ? prev.document_ids.filter((id) => !subCategory.imageS3Urls.includes(id))
        : [...new Set([...prev.document_ids, ...subCategory.imageS3Urls])];

      return {
        ...prev,
        document_ids: updatedDocumentIds,
      };
    });
  };

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6FA",
          // px: 4,
          py: 2,
          width: "100%",
          height: "95vh",
          overflow: "auto",
        }}
      >
        <Box
          sx={{
            px: 4,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            sx={{
              fontWeight: "500",
              fontSize: "28px",
              color: "black",
              textAlign: "left",
            }}
          >
            {group?.name}
          </Typography>
          {tabIndex === 0 ? (<Button
            variant="contained"
            sx={{
              textTransform: "none",
              borderRadius: "8px",
              bgcolor: "rgba(0, 0, 0, 0.06)",
              color: "black",
              boxShadow: "none",
              width: "15%",
              paddingY: "10px",
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.1)",
                boxShadow: "none",
              },
            }}
            disabled={isUpdated}
            onClick={handleUpdateClick}
          >
            Confirm Changes
          </Button>) : ""}
        </Box>
        {/* <Tabs
          value={tabIndex}
          onChange={(e, newIndex) => setTabIndex(newIndex)}
          centered
        >
          <Tab label="1. Select Team Members" />
          <Tab label="2. Select Documents" />
        </Tabs> */}
        <Box >
          {tabIndex === 0 && (
            <>
              <Typography
                sx={{
                  px: 4,
                  fontWeight: "500",
                  fontSize: "17px",
                  color: "black",
                  textAlign: "left",
                  mb: "40px"
                }}
              >
                Select Users
              </Typography>
              <Paper
                sx={{
                  bgcolor: "#ffffff",
                  borderRadius: "12px",
                  marginBottom: 9,
                  boxShadow: "none",
                  mx: 4,
                }}
              >
                <Grid container sx={{ borderBottom: "1px solid #E0E0E0", background: "#F0F0F0", px: 3 }}>
                  <Grid item xs={1}>
                    <Checkbox
                      checked={selectAll}
                      onChange={handleSelectAllChange}
                      sx={{ color: "#B0B0B0", "&.Mui-checked": { color: "#B0B0B0" } }}
                    />
                  </Grid>
                  <Grid item xs={5}>
                    <Typography
                      variant="body2"
                      sx={{ fontWeight: 500, color: "#B0B0B0", py: 1 }}
                    >
                      NAME
                    </Typography>
                  </Grid>
                  <Grid item xs={6} borderLeft={"1px solid #E0E0E0"} paddingLeft={"5px"}>
                    <Typography
                      variant="body2"
                      sx={{ fontWeight: 500, color: "#B0B0B0", py: 1 }}
                    >
                      EMAIL
                    </Typography>
                  </Grid>
                </Grid>

                {/* Team Member Rows */}
                {teamMembers.map((member) => (
                  <Box
                    key={member._id}
                    sx={{
                      transition: "background-color 0.3s ease",
                      "&:hover": { bgcolor: "#f0f0f0", cursor: "pointer" },
                      borderBottom: "1px solid #E0E0E0",
                      px: 3,
                    }}
                  >
                    <Grid container alignItems="center" >
                      <Grid item xs={1}>
                        <Checkbox
                          checked={selectAll || group?.employee_ids.includes(member._id)}
                          onChange={() => handleCheckboxChange(member._id, "employee")}
                          sx={{ color: "#B0B0B0", "&.Mui-checked": { color: "#B0B0B0" } }}
                        />
                      </Grid>
                      <Grid item xs={5}>
                        <Typography sx={{ fontWeight: "500", color: "black", py: 2 }}>
                          {member.name}
                        </Typography>
                      </Grid>
                      <Grid item xs={6} borderLeft={"1px solid #E0E0E0"} paddingLeft={"5px"}>
                        <Typography sx={{ fontWeight: "500", color: "black", py: 2 }}>
                          {member.email}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                ))}
              </Paper>
              <Box sx={{ display: "flex", width: "100%", justifyContent: "center", gap: "10px" }}>

                <Button
                  variant="contained"
                  onClick={() => router.push('/manage-groups')}
                  sx={{
                    alignSelf: "center",
                    display: "flex",
                    gap: "10px",
                    textTransform: "none",
                    borderRadius: "8px",
                    bgcolor: "black",
                    color: "white",
                    boxShadow: "none",
                    // width: "20%",
                    // paddingX: 0,
                    // paddingY: "10px",
                    // "&:hover": {
                    //   backgroundColor: "rgba(0, 0, 0, 0.1)",
                    //   boxShadow: "none",
                    // },
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <ArrowLeft color="white" size={16} />
                  </Box>
                  Back{" "}
                </Button>
                <Button
                  variant="contained"
                  onClick={() => setTabIndex(1)}
                  sx={{
                    alignSelf: "center",
                    display: "flex",
                    gap: "10px",
                    textTransform: "none",
                    borderRadius: "8px",
                    bgcolor: "black",
                    color: "white",
                    boxShadow: "none",
                    // width: "20%",
                    // paddingX: 0,
                    // paddingY: "10px",
                    // "&:hover": {
                    //   backgroundColor: "rgba(0, 0, 0, 0.1)",
                    //   boxShadow: "none",
                    // },
                  }}
                >
                  Continue{" "}
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <ArrowRight color="white" size={16} />
                  </Box>
                </Button>
              </Box>
            </>
          )}
          {tabIndex === 1 && (
            <>
              <Typography
                sx={{
                  px: 4,
                  fontWeight: "500",
                  fontSize: "17px",
                  color: "black",
                  textAlign: "left",
                  mb: "40px"
                }}
              >
                Select Documents
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Box>
                  {benefitsData.map((category, index) => (
                    <Box key={index} sx={{ mb: 3 }}>
                      <Typography
                        sx={{ fontWeight: "500", fontSize: "22px", mb: 1, px: 4, }}
                      >
                        {maskBenefitCategory(category.benefitType)}
                      </Typography>
                      {category.benefits.map((subCategory, subIndex) => (
                        <Paper
                          key={subIndex}
                          sx={{
                            bgcolor: "#ffffff",
                            // borderRadius: "12px",
                            // padding: 2,
                            mb: 2,
                            mx: 4,
                          }}
                        >
                          <Typography
                            sx={{ fontWeight: "500", fontSize: "20px", mb: 1, px: 4, pt: 2 }}
                          >
                            {maskedSubCategory(subCategory.subType)}
                          </Typography>
                          {subCategory.imageS3Urls.length > 0 ? (
                            <Box>
                              <Grid container sx={{ borderBottom: "1px solid #E0E0E0", background: "#F0F0F0", px: 3, }}>
                                <Grid item xs={1}>
                                  <Checkbox
                                    checked={subCategory.imageS3Urls.every(url => group?.document_ids.includes(url))}
                                    onChange={() => handleSubCategoryCheckboxChange(subCategory)}
                                    sx={{ color: "#B0B0B0", "&.Mui-checked": { color: "#B0B0B0" } }}
                                  />
                                </Grid>
                                <Grid item xs={5}>
                                  <Typography
                                    variant="body2"
                                    sx={{ fontWeight: 500, color: "#B0B0B0", py: 1 }}
                                  >
                                    DOCUMENT NAME
                                  </Typography>
                                </Grid>
                              </Grid>
                              {subCategory.imageS3Urls.map((url, imgIndex) => (
                                <Grid
                                  key={imgIndex}
                                  container
                                  sx={{ px: 3, py: 1 }}
                                  alignItems="center"
                                >
                                  <Grid item xs={1}>
                                    <Checkbox
                                      checked={
                                        group?.document_ids.includes(url) || false
                                      }
                                      onChange={() =>
                                        handleCheckboxChange(url, "document")
                                      }
                                      sx={{
                                        color: "green",
                                        "&.Mui-checked": { color: "green" },
                                      }}
                                    />
                                  </Grid>
                                  <Grid item xs={11}>
                                    <Typography>
                                      {extractDocumentName(url)}
                                    </Typography>
                                  </Grid>
                                </Grid>
                              ))}
                            </Box>
                          ) : (
                            <Box>
                              <Typography
                                sx={{
                                  fontSize: "16px",
                                  fontWeight: "400",
                                  color: "gray",
                                  ml: 2,
                                  pb: 3,
                                  textAlign: "center",
                                }}
                              >
                                There are no documents in this category.
                              </Typography>
                            </Box>
                          )}
                        </Paper>
                      ))}
                    </Box>
                  ))}
                  <Box sx={{ alignSelf: "center", display: "flex", gap: "10px", justifyContent: "center" }}>
                    <Button
                      variant="contained"
                      onClick={() => setTabIndex(0)}
                      sx={{
                        alignSelf: "center",
                        display: "flex",
                        gap: "10px",
                        textTransform: "none",
                        borderRadius: "8px",
                        bgcolor: "black",
                        color: "white",
                        boxShadow: "none",
                        // width: "20%",
                        // paddingX: 0,
                        // paddingY: "10px",
                        // "&:hover": {
                        //   backgroundColor: "rgba(0, 0, 0, 0.1)",
                        //   boxShadow: "none",
                        // },
                      }}
                    >
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <ArrowLeft color="white" size={16} />
                      </Box>
                      Back{" "}
                    </Button>
                    <Button
                      variant="contained"
                      sx={{
                        textTransform: "none",
                        borderRadius: "8px",
                        bgcolor: "black",
                        color: "white",
                        boxShadow: "none",
                        // width: "100%",
                        height: "36px",
                        paddingY: "10px",
                        // paddingX: "5px",
                        "&:hover": {
                          backgroundColor: "rgba(0, 0, 0, 0.1)",
                          boxShadow: "none",
                        },
                      }}
                      disabled={isUpdated}
                      onClick={handleUpdateClick}
                    >
                      Confirm Changes
                    </Button>

                    {/* <Button
                  variant="contained"
                  onClick={() => setTabIndex(1)}
                  sx={{
                    alignSelf: "center",
                    display: "flex",
                    gap: "10px",
                    textTransform: "none",
                    borderRadius: "8px",
                    bgcolor: "black",
                    color: "white",
                    boxShadow: "none",
                    // width: "20%",
                    // paddingX: 0,
                    // paddingY: "10px",
                    // "&:hover": {
                    //   backgroundColor: "rgba(0, 0, 0, 0.1)",
                    //   boxShadow: "none",
                    // },
                  }}
                >
                  Continue{" "}
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <ArrowRight color="white" size={16} />
                  </Box>
                </Button> */}
                  </Box>
                </Box>
              </Box>
            </>
          )}
        </Box>
      </Box>
    </ProtectedRoute>
  );
};

export default withSidebar(ManageGroupsWithTabs);
