"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er,
  Icon<PERSON>utton,
  Toolbar,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import Box from "@mui/material/Box"; // Import Box from MUI
import MobileSidebar from "@/app/mobile/dashboard/mobile_sidebar";
import { useState } from "react";
import { usePathname } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useAppSelector } from "@/redux/hooks";
import { closeDrawer, openDrawer } from "@/redux/reducers/mobileSidebarSlice";

const withMobileEdgeFill = (WrappedComponent: React.ComponentType) => {
  const WithMobileEdgeFill = (props: any) => {
    const dispatch = useDispatch();
    const drawerOpen = useAppSelector(
      (state: RootState) => state.mobileSidebarToggle.isOpen,
    );
    const pathname = usePathname(); // Get the current path

    // Check if the current path is "/" or "/onboard"
    const shouldHideAppBarAndDrawer =
      pathname === "/" || pathname === "/onboard";

    return (
      <Box>
        <CssBaseline />
        {!shouldHideAppBarAndDrawer && (
          <>
            <AppBar position="static" sx={{ backgroundColor: "black" }}>
              <Toolbar sx={{ mb: pathname === "/mobile/dashboard" ? 6 : 0 }}>
                <IconButton
                  edge="start"
                  color="inherit"
                  aria-label="menu"
                  onClick={() => dispatch(openDrawer())}
                >
                  <MenuIcon fontSize="large" />
                </IconButton>
              </Toolbar>
            </AppBar>
            <Drawer
              anchor="left"
              open={drawerOpen}
              onClose={() => dispatch(closeDrawer())}
            >
              <MobileSidebar />
            </Drawer>
          </>
        )}
        <WrappedComponent {...props} />
      </Box>
    );
  };

  WithMobileEdgeFill.displayName = `WithMobileEdgeFill(${WrappedComponent.displayName || WrappedComponent.name || "Component"})`;

  return WithMobileEdgeFill;
};

export default withMobileEdgeFill;
