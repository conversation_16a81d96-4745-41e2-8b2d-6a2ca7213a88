export const PROD = 'PROD';
export const ASSIGNEE = 'ASSIGNEE';
export const ASSIGNER = 'ASSIGNER';
export const CREATOR = 'CREATOR';

export const INTERNAL_SERVER_ERROR = 'Internal server error';

// Plan Types Constants
export const PLAN_TYPES = [
  'PPO', 'HMO', 'HDHP', 'MEC', 'EPO', 'POS', 'Indemnity',
  'DHMO', 'Vision', 'Hospital Indemnity', 'Accident', 'Critical Illness',
  'Term Life', 'Whole Life', 'STD', 'LTD'
] as const;


// Rate Structure Constants
export const RATE_STRUCTURES = [
  'Composite', 'Age-Banded', 'Salary-Based', 'Four-Tier', 'Age-Banded-Four-Tier'
] as const;

// Coverage Tier Constants
export const COVERAGE_TIERS = [
  'Employee Only', 'Employee + Spouse', 'Employee + Child(ren)',
  'Family', 'Employee + Domestic Partner', 'Employee + Domestic Partner + Child(ren)'
] as const;

// Coverage Tier Dependent Requirements
export const COVERAGE_TIER_REQUIREMENTS = {
  'Employee Only': { min: 0, max: 0 },
  'Employee + Spouse': { min: 1, max: 1 },
  'Employee + Child(ren)': { min: 1, max: 10 },
  'Employee + Children': { min: 1, max: 10 }, // Alternative naming
  'Family': { min: 1, max: 10 },
  'Employee + Domestic Partner': { min: 1, max: 1 },
  'Employee + Domestic Partner + Child(ren)': { min: 2, max: 10 }
} as const;

// Plan Status Constants
export const PLAN_STATUSES = ['Draft', 'Active', 'Archived', 'Template'] as const;

// Carrier Status Constants (for consistency with plan statuses)
export const CARRIER_STATUSES = ['Active', 'Inactive', 'Archived'] as const;

// Plan Assignment Status Constants
export const PLAN_ASSIGNMENT_STATUSES = ['Active', 'Expired', 'Deactivated'] as const;

// Payroll Frequency Constants
export const PAYROLL_FREQUENCIES = ['Weekly', 'Biweekly', 'Semi-Monthly', 'Monthly'] as const;

// Contribution Type Constants
export const CONTRIBUTION_TYPES = ['Percentage', 'Fixed'] as const;

// Enrollment Period Type Constants
export const ENROLLMENT_PERIOD_TYPES = ['Open Enrollment', 'New Hire', 'Qualifying Life Event'] as const;

// 🎯 NEW: Qualifying Life Event Types
export const QUALIFYING_LIFE_EVENT_TYPES = [
  'Marriage',
  'Divorce',
  'Birth',
  'Adoption',
  'Loss of Coverage',
  'Job Change',
  'Death',
  'Relocation',
  'Other'
] as const;

// Employee Enrollment Status Constants
export const ENROLLMENT_STATUSES = ['Enrolled', 'Waived', 'Pending', 'Terminated', 'Expired'] as const;

// Employee Class Type Constants
export const EMPLOYEE_CLASS_TYPES = ['Full-Time', 'Part-Time', 'Contractor', 'Temporary', 'Seasonal'] as const;

// A.M. Best Rating Constants (Financial Strength Ratings for Insurance Carriers)
export const AM_BEST_RATING_CODES = [
  'A++', 'A+', 'A', 'A-',           // Superior & Excellent
  'B++', 'B+', 'B', 'B-',           // Good & Fair
  'C++', 'C+', 'C', 'C-',           // Marginal & Weak
  'D', 'E', 'F', 'NR'               // Poor, Under Supervision, In Liquidation, Not Rated
] as const;

// Waiting Period Rule Constants
export const WAITING_PERIOD_RULES = [
  'Immediate',
  'Days from hire date',
  'First of month after X days',
  'Next enrollment period'
] as const;

// Enrollment Type Constants - Controls user experience during enrollment
export const ENROLLMENT_TYPES = ['Active', 'Passive'] as const;

// Metal Tier Constants (for ACA compliance)
export const METAL_TIERS = ['Bronze', 'Silver', 'Gold', 'Platinum', 'Catastrophic'] as const;

// Coinsurance Options for Benefit Details (informational only - doesn't affect cost calculation)
export const COINSURANCE_OPTIONS = [
  '0%',    // Preventive care (covered 100%)
  '10%',   // High coverage
  '15%',   // Good coverage
  '20%',   // Standard coverage
  '25%',   // Moderate coverage
  '30%',   // Lower coverage
  '40%',   // Basic coverage
  '50%'    // Minimal coverage
] as const;

export const BENEFIT_TYPE_SUBTYPE_MAP = [
  {
    type: 'Your Health',
    subTypes: [
      'Medical',
      'Dental',
      'Vision',
      'Wellness',
      'Employee Assistance Program',
      'Gym Membership',
    ],
  },
  {
    type: 'Income Security',
    subTypes: ['Life', 'Short Term Disability', 'Long Term Disability'],
  },
  {
    type: 'Your Money',
    subTypes: [
      'Pay & Bonus',
      'Stock Options',
      'Health Savings Account',
      'Flexible Savings Accounts',
      'Technology Stipend',
      'Commuter Benefits',
    ],
  },
  {
    type: 'Your Time Away',
    subTypes: [
      'Paid Time Off (PTO)',
      'Parental Leave',
      'Family and Medical Leave',
      'Paid Volunteer Time',
    ],
  },
  {
    type: 'Your Family',
    subTypes: [
      'On-site Child Care',
      'Student Loan Assistance',
      'Pet Insurance',
    ],
  },
  {
    type: 'Your Career',
    subTypes: [
      'Employee Training & Development',
      'Tuition Reimbursement',
      'Employee Recognition',
      'Performance Goals & Process',
    ],
  },
  {
    type: 'Work Policies',
    subTypes: [
      'Pet-friendly Workplace',
      'Ergonomic Workplace',
      'Company Handbook',
    ],
  },
  {
    type: 'Life Events',
    subTypes: [
      'Marriage or Divorce',
      'New Baby or Adoption',
      'Loss of Insurance',
    ],
  },
];

// ===== PRE-ENROLLMENT COVERAGE MAP (New Insurance-Focused Categories) =====
export const PRE_ENROLLMENT_COVERAGE_MAP = [
  {
    "type": "Health Insurance",
    "subTypes": ["Medical"]
  },
  {
    "type": "Ancillary Benefits",
    "subTypes": ["Dental", "Vision"]
  },
  {
    "type": "Life & Disability Insurance",
    "subTypes": [
      "Term Life",
      "Supplemental Life Insurance",
      "Short-Term Disability",
      "Long-Term Disability",
      "Whole Life",
      "Group (Employer) Life",
      "Accidental Death & Dismemberment (AD&D)"
    ]
  },
  {
    "type": "Voluntary Benefits",
    "subTypes": [
      "Hospital Indemnity",
      "Accident Insurance",
      "Critical Illness Insurance",
      "Cancer Insurance",
      "Gap Insurance",
      "Legal Insurance",
      "Identity Theft Protection",
      "Accident & Illness (Pets)",
      "Nursing Care / Custodial Care"
    ]
  },
  {
    "type": "Wellness & Mental Health",
    "subTypes": [
      "Wellness Programs",
      "Employee Assistance Program",
      "Gym Membership"
    ]
  },
  {
    "type": "Spending & Savings Accounts",
    "subTypes": [
      "Health Savings Account",
      "Flexible Savings Accounts",
      "Commuter Benefits",
      "Technology Stipend"
    ]
  },
  {
    "type": "Financial Benefits",
    "subTypes": [
      "Pay & Bonus",
      "Stock Options",
      "Student Loan Assistance"
    ]
  },
  {
    "type": "Retirement Benefits",
    "subTypes": [
      "401(k)",
      "403(b)",
      "Pension Plan"
    ]
  },
  {
    "type": "Time Off & Leave",
    "subTypes": [
      "Paid Time Off (PTO)",
      "Parental Leave",
      "Family and Medical Leave",
      "Paid Volunteer Time"
    ]
  },
  {
    "type": "Family & Caregiver Support",
    "subTypes": ["On-site Child Care"]
  },
  {
    "type": "Career & Development",
    "subTypes": [
      "Employee Training & Development",
      "Tuition Reimbursement",
      "Employee Recognition",
      "Performance Goals & Process"
    ]
  },
  {
    "type": "Workplace Environment",
    "subTypes": [
      "Pet-friendly Workplace",
      "Ergonomic Workplace",
      "Company Handbook"
    ]
  },
  {
    "type": "Life Events",
    "subTypes": [
      "Marriage or Divorce",
      "New Baby or Adoption",
      "Loss of Insurance"
    ]
  }
];

// Extract pre-enrollment coverage types and subtypes
export const PRE_ENROLLMENT_COVERAGE_TYPES = PRE_ENROLLMENT_COVERAGE_MAP.map(item => item.type);
export const PRE_ENROLLMENT_COVERAGE_SUBTYPES = PRE_ENROLLMENT_COVERAGE_MAP.flatMap(item => item.subTypes);

// ===== LEGACY BENEFIT MAP (Keep for existing benefit system) =====
// Extract coverage types and subtypes from BENEFIT_TYPE_SUBTYPE_MAP (Legacy)
export const COVERAGE_TYPES = BENEFIT_TYPE_SUBTYPE_MAP.map(item => item.type);
export const COVERAGE_SUBTYPES = BENEFIT_TYPE_SUBTYPE_MAP.flatMap(item => item.subTypes);

// ===== HELPER FUNCTIONS =====

// Legacy helper functions (for existing benefit system)
export const getSubTypesForCoverageType = (coverageType: string): string[] => {
  const found = BENEFIT_TYPE_SUBTYPE_MAP.find(item => item.type === coverageType);
  return found ? found.subTypes : [];
};

export const isValidCoverageTypeSubTypeCombination = (coverageType: string, coverageSubType: string): boolean => {
  const validSubTypes = getSubTypesForCoverageType(coverageType);
  return validSubTypes.includes(coverageSubType);
};

// Pre-enrollment helper functions (for new insurance system)
export const getPreEnrollmentSubTypesForCoverageType = (coverageType: string): string[] => {
  const found = PRE_ENROLLMENT_COVERAGE_MAP.find(item => item.type === coverageType);
  return found ? found.subTypes : [];
};

export const isValidPreEnrollmentCombination = (coverageType: string, coverageSubType: string): boolean => {
  const validSubTypes = getPreEnrollmentSubTypesForCoverageType(coverageType);
  return validSubTypes.includes(coverageSubType);
};


// Check if all the apis for the three models cover all fundamental things, and it should be updated in documentation. Then understand the requirements and side effects and all validation needs, assign reassign needs and come up with the apis for plan assignment.

// Note these rules:
// 1) Plan assigned can not be enrolled unless it is active. There should be api to deactivate it, by default on creation, it will be active.
// 2) Plan assignment is editable if there are no or one dependencies below i.e, no /one employee enrollment is referencing it. Can be deleted if no referencing is below.
// 3) Plan assignment expires after plan end date or plan expiry date that we have after which it deactivates. There should be an inbuilt check for active plan assignments for a company while fetching them to expire them when the expiry date passes, in fetch plan assignments by companyId or maybe somewhere more fundamental. Or maybe as a component that can be resued.
// 4) We should be able to reuse plan assignments that are already made in next year, an api that takes planassignment id and extra details (if given thenoverrides exisitng plan assignment details for that plan assignment id). Generally this is for companies who want to use last year's plan assignment  with little changed plan assignment details but keeping plan reference same and companyId same. They can provide the dates in fields or we can use the previous plan assignment's dates but year will change to current.
// 5)  There should be apis to activate deactivate plan assignments. Once the expiry date passes it becomes expired always but within the plan active year we can activate and deactivate it, once deactivated employee can not enroll in this plan assignment. Then when again activated it can be used to enroll. But once expired, it can not be activated.

// Think about all the fundamental apis we need for this and update the document in the plan assignment api section. Also update the doc with all these rules and if there is something redundant in the document you can remove it. Plan everything in the documentation and well maintain it and document should be clean.