import dotenv from 'dotenv';
dotenv.config();

import App from './app';
import InitService from './services/init.service';
import MergeController from './controllers/merge.controller';
import FinchController from './controllers/finch.controller';
import SlackController from './controllers/slack.controller';
import AuthController from './controllers/auth.controller';
import AdminController from './controllers/admin.controller';
import BenfitController from './controllers/benefit.controller';
import EmployeeController from './controllers/employee.controller';
import GroupController from './controllers/group.controller';
import WaitlistController from './controllers/waitlist.controller';
import PlanController from './controllers/plan.controller';
import CarrierController from './controllers/carrier.controller';
import { CompanyBenefitsSettingsController } from './controllers/companyBenefitsSettings.controller';
import PlanAssignmentController from './controllers/planAssignment.controller';
import EmployeeEnrollmentController from './controllers/employeeEnrollment.controller';

InitService.init()
  .then(async () => {
    console.log('🚀 Starting application with all services initialized...');

    const app = new App([
      new MergeController(),
      new FinchController(),
      new SlackController(),
      new AuthController(),
      new AdminController(),
      new BenfitController(),
      new EmployeeController(),
      new GroupController(),
      new WaitlistController(),
      new PlanController(),
      new CarrierController(),
      new CompanyBenefitsSettingsController(),
      new PlanAssignmentController(),
      new EmployeeEnrollmentController(),
    ]);

    app.listen();
    console.log('🎉 Application started successfully!');
  })
  .catch((error) => {
    console.error('💥 Failed to start application:', error);
    console.error('🔄 Please check your configuration and try again');
    process.exit(1); // Exit with error code
  });