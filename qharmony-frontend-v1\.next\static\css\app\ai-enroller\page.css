/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./public/font.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
@font-face {
    font-family: 'SF Pro';
    src: url('/fonts/SF-Pro-Text-Regular.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'SF Pro';
    src: url('/fonts/SF-Pro-Text-Bold.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'SF Pro';
    src: url('/fonts/SF-Pro-Text-Medium.otf') format('opentype');
    font-weight: 500;
    font-style: normal;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/ai-enroller/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* SF Pro Font for AI Enroller Directory */
/* Font family and weights are handled by design-system.css */

/* Smooth font rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Custom font sizes for better hierarchy */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }

/* Better letter spacing for SF Pro */
h1, h2, h3, h4, h5, h6 {
  letter-spacing: -0.025em;
}

/* Improved readability for small text */
.text-xs, .text-sm {
  letter-spacing: 0.025em;
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}


