'use client';

import React, { useState, useEffect } from 'react';
import { HiOutlineEye, HiOutlineDownload, HiOutlineTrash, HiOutlineX } from 'react-icons/hi';
import { getStoredSignature, getSignatureReference, clearStoredSignature, formatSignatureTimestamp, verifySignature, debugSignature } from '../utils/signatureUtils';

interface SignatureViewerProps {
  isOpen: boolean;
  onClose: () => void;
}

const SignatureViewer: React.FC<SignatureViewerProps> = ({ isOpen, onClose }) => {
  const [signatureData, setSignatureData] = useState<any>(null);
  const [signatureRef, setSignatureRef] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadSignatureData();
    }
  }, [isOpen]);

  const loadSignatureData = () => {
    setLoading(true);
    try {
      const signature = getStoredSignature();
      const reference = getSignatureReference();
      
      setSignatureData(signature);
      setSignatureRef(reference);
      
      console.log('📝 Loaded signature data:', { signature: !!signature, reference });
    } catch (error) {
      console.error('Error loading signature data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadSignature = () => {
    if (!signatureData?.signature) return;

    try {
      // Create download link
      const link = document.createElement('a');
      link.href = signatureData.signature;
      link.download = `enrollment-signature-${signatureData.employeeName}-${new Date(signatureData.timestamp).toISOString().split('T')[0]}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      console.log('📥 Signature downloaded');
    } catch (error) {
      console.error('Error downloading signature:', error);
    }
  };

  const handleClearSignature = () => {
    if (window.confirm('Are you sure you want to clear the stored signature? This action cannot be undone.')) {
      clearStoredSignature();
      setSignatureData(null);
      setSignatureRef(null);
      console.log('🗑️ Signature cleared');
    }
  };

  const handleDebugSignature = () => {
    debugSignature();
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 10000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '800px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
        position: 'relative'
      }}>
        {/* Close Button */}
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '16px',
            right: '16px',
            background: 'none',
            border: 'none',
            fontSize: '24px',
            cursor: 'pointer',
            color: '#6b7280',
            padding: '4px'
          }}
        >
          <HiOutlineX />
        </button>

        {/* Header */}
        <div style={{ marginBottom: '24px' }}>
          <h2 style={{
            fontSize: '24px',
            fontWeight: '600',
            color: '#111827',
            margin: '0 0 8px 0'
          }}>
            📝 Enrollment Signature
          </h2>
          <p style={{
            color: '#6b7280',
            fontSize: '14px',
            margin: 0,
            lineHeight: '21px'
          }}>
            View and manage your stored enrollment signature
          </p>
        </div>

        {loading ? (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#6b7280'
          }}>
            Loading signature data...
          </div>
        ) : signatureData ? (
          <div>
            {/* Signature Metadata */}
            <div style={{
              backgroundColor: '#f9fafb',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '24px'
            }}>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#111827',
                margin: '0 0 12px 0'
              }}>
                Signature Details
              </h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', fontSize: '14px' }}>
                <div>
                  <strong>Employee:</strong> {signatureData.employeeName}
                </div>
                <div>
                  <strong>Signed:</strong> {formatSignatureTimestamp(signatureData.timestamp)}
                </div>
                <div>
                  <strong>Signature Hash:</strong> {signatureData.signatureHash}
                </div>
                <div>
                  <strong>Valid:</strong>
                  <span style={{
                    color: verifySignature(signatureData) ? '#10b981' : '#dc2626',
                    fontWeight: '600',
                    marginLeft: '4px'
                  }}>
                    {verifySignature(signatureData) ? '✅ Valid' : '❌ Invalid'}
                  </span>
                </div>
                <div>
                  <strong>Format:</strong> {signatureData.format || 'PNG'}
                </div>
                <div>
                  <strong>Quality:</strong> {signatureData.quality || 'Standard'}
                </div>
                <div>
                  <strong>Vector Data:</strong> {signatureData.signaturePadData ? `${signatureData.signaturePadData.length} strokes` : 'Not available'}
                </div>
                <div>
                  <strong>Data Size:</strong> {(signatureData.signature.length / 1024).toFixed(1)} KB
                </div>
              </div>
            </div>

            {/* Signature Image */}
            <div style={{
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '24px',
              backgroundColor: '#ffffff',
              textAlign: 'center'
            }}>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#111827',
                margin: '0 0 16px 0'
              }}>
                Digital Signature
              </h3>
              <img
                src={signatureData.signature}
                alt="Digital Signature"
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px'
                }}
              />
            </div>

            {/* Action Buttons */}
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
              <button
                onClick={handleDownloadSignature}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#2563eb',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  cursor: 'pointer',
                  fontWeight: '500',
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                <HiOutlineDownload />
                Download
              </button>
              
              <button
                onClick={handleDebugSignature}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#6b7280',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  cursor: 'pointer',
                  fontWeight: '500',
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                <HiOutlineEye />
                Debug Info
              </button>
              
              <button
                onClick={handleClearSignature}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#dc2626',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  cursor: 'pointer',
                  fontWeight: '500',
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                <HiOutlineTrash />
                Clear
              </button>
            </div>
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#6b7280'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', marginBottom: '8px' }}>
              No Signature Found
            </h3>
            <p style={{ margin: 0, fontSize: '14px' }}>
              Complete your enrollment to create a digital signature.
            </p>
          </div>
        )}

        {/* Close Button */}
        <div style={{ marginTop: '24px', textAlign: 'center' }}>
          <button
            onClick={onClose}
            style={{
              padding: '12px 24px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontWeight: '500',
              fontSize: '14px'
            }}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default SignatureViewer;
