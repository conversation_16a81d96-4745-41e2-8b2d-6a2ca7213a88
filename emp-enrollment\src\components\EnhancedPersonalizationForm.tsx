
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { UserProfile } from './BenefitsEnrollmentBot';
import { Users, DollarSign, Heart, Stethoscope } from 'lucide-react';

interface EnhancedPersonalizationFormProps {
  onComplete: (profile: Partial<UserProfile> & {
    expectedMedicalUsage: string;
    budgetPreference: string;
    chronicConditions: boolean;
    prescriptionNeeds: boolean;
    preferredProviders: string[];
    budgetRange: number[];
  }) => void;
}

export const EnhancedPersonalizationForm = ({ onComplete }: EnhancedPersonalizationFormProps) => {
  const [familyMembers, setFamilyMembers] = useState('');
  const [wearGlasses, setWearGlasses] = useState(false);
  const [needsDentalCare, setNeedsDentalCare] = useState(false);
  const [hasPreferredDoctors, setHasPreferredDoctors] = useState(false);
  const [expectedMedicalUsage, setExpectedMedicalUsage] = useState('');
  const [budgetPreference, setBudgetPreference] = useState('');
  const [chronicConditions, setChronicConditions] = useState(false);
  const [prescriptionNeeds, setPrescriptionNeeds] = useState(false);
  const [preferredProviders, setPreferredProviders] = useState<string[]>([]);
  const [budgetRange, setBudgetRange] = useState([100]);

  const handleProviderToggle = (provider: string) => {
    setPreferredProviders(prev => 
      prev.includes(provider) 
        ? prev.filter(p => p !== provider)
        : [...prev, provider]
    );
  };

  const handleSubmit = () => {
    onComplete({
      familyMembers,
      wearGlasses,
      needsDentalCare,
      hasPreferredDoctors,
      expectedMedicalUsage,
      budgetPreference,
      chronicConditions,
      prescriptionNeeds,
      preferredProviders,
      budgetRange,
    });
  };

  const isComplete = familyMembers !== '' && expectedMedicalUsage !== '' && budgetPreference !== '';

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Heart className="w-5 h-5 text-red-500" />
          Enhanced Health Profile
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Family Coverage */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            <Label className="text-base font-medium">Family Coverage Needed</Label>
          </div>
          <RadioGroup value={familyMembers} onValueChange={setFamilyMembers}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="self" id="self" />
              <Label htmlFor="self">Just me (Employee only)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="spouse" id="spouse" />
              <Label htmlFor="spouse">Me + Spouse/Partner</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="family" id="family" />
              <Label htmlFor="family">Me + Family (includes children)</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Expected Medical Usage */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Stethoscope className="w-4 h-4" />
            <Label className="text-base font-medium">Expected Healthcare Usage</Label>
          </div>
          <RadioGroup value={expectedMedicalUsage} onValueChange={setExpectedMedicalUsage}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="low" id="low" />
              <Label htmlFor="low">Low - Just preventive care & checkups</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="moderate" id="moderate" />
              <Label htmlFor="moderate">Moderate - Occasional visits & some prescriptions</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="high" id="high" />
              <Label htmlFor="high">High - Regular specialists, procedures, or chronic conditions</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Budget Preference */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            <Label className="text-base font-medium">Budget Preference</Label>
          </div>
          <RadioGroup value={budgetPreference} onValueChange={setBudgetPreference}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="low-premium" id="low-premium" />
              <Label htmlFor="low-premium">Lower monthly cost, higher deductible</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="balanced" id="balanced" />
              <Label htmlFor="balanced">Balanced monthly cost and deductible</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="low-deductible" id="low-deductible" />
              <Label htmlFor="low-deductible">Higher monthly cost, lower deductible</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Budget Range Slider */}
        <div className="space-y-3">
          <Label className="text-base font-medium">
            Maximum monthly budget: ${budgetRange[0]}/paycheck
          </Label>
          <Slider
            value={budgetRange}
            onValueChange={setBudgetRange}
            max={200}
            min={50}
            step={10}
            className="w-full"
          />
        </div>

        {/* Health Conditions */}
        <div className="space-y-3">
          <Label className="text-base font-medium">Health Considerations</Label>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="glasses" 
              checked={wearGlasses}
              onCheckedChange={(checked) => setWearGlasses(checked as boolean)}
            />
            <Label htmlFor="glasses">I wear glasses or contacts</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox 
              id="dental" 
              checked={needsDentalCare}
              onCheckedChange={(checked) => setNeedsDentalCare(checked as boolean)}
            />
            <Label htmlFor="dental">I need regular dental care or have dental work planned</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox 
              id="chronic" 
              checked={chronicConditions}
              onCheckedChange={(checked) => setChronicConditions(checked as boolean)}
            />
            <Label htmlFor="chronic">I have ongoing health conditions requiring regular care</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox 
              id="prescriptions" 
              checked={prescriptionNeeds}
              onCheckedChange={(checked) => setPrescriptionNeeds(checked as boolean)}
            />
            <Label htmlFor="prescriptions">I take regular prescription medications</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox 
              id="doctors" 
              checked={hasPreferredDoctors}
              onCheckedChange={(checked) => setHasPreferredDoctors(checked as boolean)}
            />
            <Label htmlFor="doctors">I have preferred doctors I want to keep seeing</Label>
          </div>
        </div>

        <Button 
          onClick={handleSubmit}
          disabled={!isComplete}
          className="w-full"
          size="lg"
        >
          Get My Personalized Recommendations
        </Button>
      </CardContent>
    </Card>
  );
};
