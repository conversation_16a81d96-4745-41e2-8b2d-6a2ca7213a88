import express from 'express';
import Controller from '../interfaces/controller.interface';
import { ChatService } from '../services/chat.service';
import logger from '../utils/logger';

class ChatController implements Controller {
  public router = express.Router();
  public chatService = new ChatService();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post('/slack/webhook', this.handleSlackWebhook);
  }

  private handleSlackWebhook = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, message, teamId } = request.body; // Extract user ID and message from the request body

      // Process the user message using the ChatService
      const chatResponse = await this.chatService.processMessage(
        userId,
        message,
        teamId
      );

      // Send the response back to Slack
      response.status(200).json({ message: chatResponse.message });
    } catch (error) {
      logger.error('Error processing Slack webhook:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };
}

export default ChatController;
