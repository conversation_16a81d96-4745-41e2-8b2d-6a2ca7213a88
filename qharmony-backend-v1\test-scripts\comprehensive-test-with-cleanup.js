/**
 * 🎯 COMPREHENSIVE ENROLLMENT APIS TEST WITH DATABASE ACCESS & CLEANUP
 *
 * This script:
 * 1. Uses direct database access to create base data (users, companies, carriers, plans)
 * 2. Uses API endpoints to test plan assignments and employee enrollments
 * 3. Covers all edge cases including multi-year behavior and business rules
 * 4. Cleans up all created data at the end
 *
 * Server: http://localhost:8080 (test database)
 * APIs tested:
 * - GET /api/pre-enrollment/employee-enrollments/enrollment-periods/:planAssignmentId
 * - POST /api/pre-enrollment/employee-enrollments/estimate-plan-costs
 * - GET /api/pre-enrollment/employee-enrollments/expired
 * - POST /api/pre-enrollment/employee-enrollments/check-expired
 * - Plan Assignment APIs (creation, management)
 * - Employee Enrollment APIs (creation, management, expiry)
 */

const axios = require('axios');
const mongoose = require('mongoose');

// Import database models for direct access
let UserModel, CompanyModel, CarrierModel, PlanModel, PlanAssignmentModel, EmployeeEnrollmentModel;

// 🔧 TEST CONFIGURATION
const TEST_CONFIG = {
  BASE_URL: 'http://127.0.0.1:8080', // Use IPv4 instead of localhost
  API_BASE: '/api/pre-enrollment/employee-enrollments',
  PLAN_ASSIGNMENT_API: '/api/pre-enrollment/plan-assignments',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  DELAY_BETWEEN_TESTS: 500,
  CLEANUP_DELAY: 1000,
  DB_CONNECTION_STRING: process.env.MONGODB_URI || 'mongodb://localhost:27017/qharmony-test'
};

// 📊 TEST RESULTS TRACKING
const testResults = {
  passed: 0,
  failed: 0,
  errors: [],
  createdData: {
    companies: [],
    users: [],
    carriers: [],
    plans: [],
    planAssignments: [],
    enrollments: []
  }
};

// 🛠️ UTILITY FUNCTIONS
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    cleanup: '🧹',
    create: '🏗️'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
};

const logSection = (title) => {
  console.log('\n' + '='.repeat(80));
  console.log(`🎯 ${title}`);
  console.log('='.repeat(80));
};

const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const makeRequest = async (method, endpoint, data = null, userId = null, retries = TEST_CONFIG.RETRY_ATTEMPTS) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${TEST_CONFIG.BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: TEST_CONFIG.TIMEOUT
      };
      
      if (userId) {
        config.headers['user-id'] = userId;
      }
      
      if (data) {
        config.data = data;
      }
      
      const response = await axios(config);
      return { 
        success: true, 
        data: response.data, 
        status: response.status,
        headers: response.headers
      };
    } catch (error) {
      if (attempt === retries) {
        return { 
          success: false, 
          error: error.response?.data || error.message, 
          status: error.response?.status || 500,
          code: error.code
        };
      }
      
      await wait(1000 * attempt); // Exponential backoff
    }
  }
};

const assert = (condition, message, testName = 'unknown') => {
  if (condition) {
    testResults.passed++;
    log(`✅ PASS: ${message}`, 'success');
    return true;
  } else {
    testResults.failed++;
    testResults.errors.push({ test: testName, message });
    log(`❌ FAIL: ${message}`, 'error');
    return false;
  }
};

// 🗄️ DATABASE CONNECTION AND MODEL INITIALIZATION

/**
 * Connect to test database and initialize models
 */
async function initializeDatabase() {
  try {
    log('Connecting to test database...', 'create');

    // Connect to MongoDB
    await mongoose.connect(TEST_CONFIG.DB_CONNECTION_STRING, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    log('✅ Connected to test database', 'success');

    // Define schemas (simplified for testing)
    const userSchema = new mongoose.Schema({
      email: { type: String, required: true, unique: true },
      firstName: String,
      lastName: String,
      password: String,
      isSuperAdmin: { type: Boolean, default: false },
      isAdmin: { type: Boolean, default: false },
      isBroker: { type: Boolean, default: false },
      companyId: mongoose.Schema.Types.ObjectId,
      role: String,
      isActive: { type: Boolean, default: true },
      details: mongoose.Schema.Types.Mixed
    }, { timestamps: true });

    const companySchema = new mongoose.Schema({
      companyName: { type: String, required: true },
      ein: String,
      isBrokerage: { type: Boolean, default: false },
      isActive: { type: Boolean, default: true },
      brokerId: mongoose.Schema.Types.ObjectId,
      brokerageId: mongoose.Schema.Types.ObjectId,
      address: mongoose.Schema.Types.Mixed,
      contactInfo: mongoose.Schema.Types.Mixed,
      employeeCount: Number,
      industry: String
    }, { timestamps: true });

    const carrierSchema = new mongoose.Schema({
      carrierName: { type: String, required: true },
      carrierCode: String,
      amBestRating: String,
      isActive: { type: Boolean, default: true },
      supportedCoverageTypes: [String],
      integrationCapabilities: mongoose.Schema.Types.Mixed,
      contactInfo: mongoose.Schema.Types.Mixed,
      address: mongoose.Schema.Types.Mixed
    }, { timestamps: true });

    const planSchema = new mongoose.Schema({
      planName: { type: String, required: true },
      planCode: String,
      carrierId: { type: mongoose.Schema.Types.ObjectId, required: true },
      coverageType: String,
      planType: String,
      isActive: { type: Boolean, default: true },
      rateStructure: String,
      benefitDetails: mongoose.Schema.Types.Mixed,
      networkDetails: mongoose.Schema.Types.Mixed
    }, { timestamps: true });

    // Initialize models
    UserModel = mongoose.model('TestUser', userSchema);
    CompanyModel = mongoose.model('TestCompany', companySchema);
    CarrierModel = mongoose.model('TestCarrier', carrierSchema);
    PlanModel = mongoose.model('TestPlan', planSchema);

    log('✅ Database models initialized', 'success');
    return true;

  } catch (error) {
    log(`❌ Database initialization failed: ${error.message}`, 'error');
    return false;
  }
}

/**
 * Close database connection
 */
async function closeDatabase() {
  try {
    await mongoose.connection.close();
    log('✅ Database connection closed', 'cleanup');
  } catch (error) {
    log(`❌ Error closing database: ${error.message}`, 'error');
  }
}

// 🏗️ DATABASE DATA CREATION FUNCTIONS

/**
 * Create test companies using direct database access
 */
async function createTestCompanies() {
  log('Creating test companies via database...', 'create');

  try {
    // Create brokerage company
    const brokerage = new CompanyModel({
      companyName: 'Test Brokerage Alpha',
      ein: '12-3456789',
      isBrokerage: true,
      isActive: true,
      address: {
        street: '123 Test Broker St',
        city: 'Test City',
        state: 'NY',
        zipCode: '10001'
      },
      contactInfo: {
        phone: '555-TEST-001',
        email: '<EMAIL>'
      }
    });

    const savedBrokerage = await brokerage.save();
    testResults.createdData.companies.push(savedBrokerage._id);
    log(`Created brokerage: ${savedBrokerage.companyName}`, 'create');

    // Create client companies
    const clientCompanies = [
      {
        companyName: 'Test TechCorp Inc',
        ein: '11-1111111',
        isBrokerage: false,
        isActive: true,
        brokerId: savedBrokerage._id, // Will be updated with actual broker user ID
        brokerageId: savedBrokerage._id,
        address: {
          street: '789 Test Tech Blvd',
          city: 'Test Francisco',
          state: 'CA',
          zipCode: '94105'
        },
        contactInfo: {
          phone: '555-TEST-002',
          email: '<EMAIL>'
        },
        employeeCount: 50,
        industry: 'Technology'
      },
      {
        companyName: 'Test HealthPlus LLC',
        ein: '22-2222222',
        isBrokerage: false,
        isActive: true,
        brokerId: savedBrokerage._id, // Will be updated with actual broker user ID
        brokerageId: savedBrokerage._id,
        address: {
          street: '321 Test Medical Dr',
          city: 'Test Houston',
          state: 'TX',
          zipCode: '77030'
        },
        contactInfo: {
          phone: '555-TEST-003',
          email: '<EMAIL>'
        },
        employeeCount: 30,
        industry: 'Healthcare'
      }
    ];

    for (const companyData of clientCompanies) {
      const company = new CompanyModel(companyData);
      const savedCompany = await company.save();
      testResults.createdData.companies.push(savedCompany._id);
      log(`Created client company: ${savedCompany.companyName}`, 'create');
    }

    return testResults.createdData.companies;

  } catch (error) {
    log(`❌ Error creating companies: ${error.message}`, 'error');
    return [];
  }
}

/**
 * Create test users using direct database access
 */
async function createTestUsers(companyIds) {
  log('Creating test users via database...', 'create');

  try {
    const users = [
      {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'SuperAdmin',
        password: 'TestPassword123!',
        isSuperAdmin: true,
        isAdmin: true,
        isBroker: false,
        companyId: companyIds[0], // Brokerage
        role: 'SuperAdmin'
      },
      {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Broker',
        password: 'TestPassword123!',
        isSuperAdmin: false,
        isAdmin: true,
        isBroker: true,
        companyId: companyIds[0], // Brokerage
        role: 'Broker'
      },
      {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Admin',
        password: 'TestPassword123!',
        isSuperAdmin: false,
        isAdmin: true,
        isBroker: false,
        companyId: companyIds[1], // TechCorp
        role: 'CompanyAdmin'
      },
      {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Employee1',
        password: 'TestPassword123!',
        isSuperAdmin: false,
        isAdmin: false,
        isBroker: false,
        companyId: companyIds[1], // TechCorp
        role: 'Employee',
        details: {
          phoneNumber: '555-TEST-100',
          dateOfBirth: '1990-01-01',
          hireDate: '2024-01-15', // New hire
          employeeClassType: 'Full-Time',
          department: 'Engineering',
          title: 'Software Developer',
          annualSalary: 75000
        }
      },
      {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Employee2',
        password: 'TestPassword123!',
        isSuperAdmin: false,
        isAdmin: false,
        isBroker: false,
        companyId: companyIds[1], // TechCorp
        role: 'Employee',
        details: {
          phoneNumber: '555-TEST-101',
          dateOfBirth: '1985-05-15',
          hireDate: '2023-01-01', // Established employee
          employeeClassType: 'Full-Time',
          department: 'Marketing',
          title: 'Marketing Manager',
          annualSalary: 85000
        }
      },
      {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Employee3',
        password: 'TestPassword123!',
        isSuperAdmin: false,
        isAdmin: false,
        isBroker: false,
        companyId: companyIds[2], // HealthPlus
        role: 'Employee',
        details: {
          phoneNumber: '555-TEST-102',
          dateOfBirth: '1992-12-10',
          hireDate: '2023-06-01',
          employeeClassType: 'Full-Time',
          department: 'Medical',
          title: 'Nurse',
          annualSalary: 65000
        }
      }
    ];

    for (const userData of users) {
      const user = new UserModel(userData);
      const savedUser = await user.save();
      testResults.createdData.users.push(savedUser._id);
      log(`Created user: ${savedUser.email} (${savedUser.role})`, 'create');
    }

    // Update company brokerId with actual broker user ID
    const brokerUser = await UserModel.findOne({ email: '<EMAIL>' });
    if (brokerUser) {
      await CompanyModel.updateMany(
        { _id: { $in: [companyIds[1], companyIds[2]] } }, // Client companies
        { brokerId: brokerUser._id }
      );
      log('Updated client companies with broker user ID', 'create');
    }

    return testResults.createdData.users;

  } catch (error) {
    log(`❌ Error creating users: ${error.message}`, 'error');
    return [];
  }
}

/**
 * Create test carriers using direct database access
 */
async function createTestCarriers() {
  log('Creating test carriers via database...', 'create');

  try {
    const carriers = [
      {
        carrierName: 'Test Blue Shield Health',
        carrierCode: 'TEST-BSH',
        amBestRating: 'A+',
        isActive: true,
        supportedCoverageTypes: ['Medical', 'Dental', 'Vision'],
        integrationCapabilities: {
          eligibilityVerification: true,
          claimsProcessing: true,
          realTimeUpdates: true
        },
        contactInfo: {
          phone: '1-800-TEST-123',
          email: '<EMAIL>',
          website: 'https://test.blueshield.com'
        },
        address: {
          street: '100 Test Insurance Plaza',
          city: 'Test City',
          state: 'CA',
          zipCode: '94105'
        }
      },
      {
        carrierName: 'Test Aetna Health',
        carrierCode: 'TEST-AET',
        amBestRating: 'A',
        isActive: true,
        supportedCoverageTypes: ['Medical', 'Dental'],
        integrationCapabilities: {
          eligibilityVerification: true,
          claimsProcessing: true,
          realTimeUpdates: false
        },
        contactInfo: {
          phone: '1-800-TEST-456',
          email: '<EMAIL>',
          website: 'https://test.aetna.com'
        },
        address: {
          street: '200 Test Health Ave',
          city: 'Test Hartford',
          state: 'CT',
          zipCode: '06156'
        }
      }
    ];

    for (const carrierData of carriers) {
      const carrier = new CarrierModel(carrierData);
      const savedCarrier = await carrier.save();
      testResults.createdData.carriers.push(savedCarrier._id);
      log(`Created carrier: ${savedCarrier.carrierName}`, 'create');
    }

    return testResults.createdData.carriers;

  } catch (error) {
    log(`❌ Error creating carriers: ${error.message}`, 'error');
    return [];
  }
}

/**
 * Create test plans using direct database access
 */
async function createTestPlans(carrierIds) {
  log('Creating test plans via database...', 'create');

  try {
    const plans = [
      {
        planName: 'Test Blue Shield PPO Gold',
        planCode: 'TEST-BSH-PPO-GOLD',
        carrierId: carrierIds[0],
        coverageType: 'Medical',
        planType: 'PPO',
        isActive: true,
        rateStructure: 'Four-Tier',
        benefitDetails: {
          deductible: {
            individual: 1000,
            family: 2000
          },
          outOfPocketMax: {
            individual: 5000,
            family: 10000
          },
          copays: {
            primaryCare: 25,
            specialist: 50,
            urgentCare: 75,
            emergencyRoom: 300
          },
          coinsurance: {
            inNetwork: 80,
            outOfNetwork: 60
          }
        },
        networkDetails: {
          networkName: 'Test Blue Shield PPO Network',
          providerCount: 50000,
          hospitalCount: 1200
        }
      },
      {
        planName: 'Test Aetna Dental Plus',
        planCode: 'TEST-AET-DENTAL-PLUS',
        carrierId: carrierIds[1],
        coverageType: 'Dental',
        planType: 'DHMO',
        isActive: true,
        rateStructure: 'Composite',
        benefitDetails: {
          annualMaximum: 1500,
          deductible: {
            individual: 50,
            family: 150
          },
          coveragePercentages: {
            preventive: 100,
            basic: 80,
            major: 50,
            orthodontics: 50
          },
          waitingPeriods: {
            basic: 6,
            major: 12,
            orthodontics: 12
          }
        },
        networkDetails: {
          networkName: 'Test Aetna Dental Network',
          providerCount: 15000,
          specialistCount: 3000
        }
      },
      {
        planName: 'Test Blue Shield HSA High Deductible',
        planCode: 'TEST-BSH-HSA-HD',
        carrierId: carrierIds[0],
        coverageType: 'Medical',
        planType: 'HDHP',
        isActive: true,
        rateStructure: 'Age-Banded-Four-Tier',
        benefitDetails: {
          deductible: {
            individual: 3000,
            family: 6000
          },
          outOfPocketMax: {
            individual: 7000,
            family: 14000
          },
          hsaContribution: {
            employerMax: 1000,
            individualLimit: 4150,
            familyLimit: 8300
          },
          coinsurance: {
            inNetwork: 90,
            outOfNetwork: 70
          }
        }
      }
    ];

    for (const planData of plans) {
      const plan = new PlanModel(planData);
      const savedPlan = await plan.save();
      testResults.createdData.plans.push(savedPlan._id);
      log(`Created plan: ${savedPlan.planName} (${savedPlan.rateStructure})`, 'create');
    }

    return testResults.createdData.plans;

  } catch (error) {
    log(`❌ Error creating plans: ${error.message}`, 'error');
    return [];
  }
}

/**
 * Create test plan assignments using API endpoints (this is what we're testing)
 */
async function createTestPlanAssignments(companyIds, planIds, carrierIds, userIds) {
  log('Creating test plan assignments via API...', 'create');

  try {
    const currentYear = new Date().getFullYear();
    const planAssignments = [
      // Current year active plan assignment
      {
        companyId: companyIds[1], // TechCorp
        planId: planIds[0], // Blue Shield PPO
        carrierId: carrierIds[0],
        planYear: currentYear,
        isActive: true,
        enrollmentStartDate: new Date(`${currentYear - 1}-11-01`),
        enrollmentEndDate: new Date(`${currentYear - 1}-11-30`),
        planStartDate: new Date(`${currentYear}-01-01`),
        planEndDate: new Date(`${currentYear}-12-31`),
        rateStructure: 'Four-Tier',
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 450.00,
            employeeCost: 135.00,
            employerCost: 315.00
          },
          {
            tierName: 'Employee + Spouse',
            totalCost: 900.00,
            employeeCost: 270.00,
            employerCost: 630.00
          },
          {
            tierName: 'Employee + Children',
            totalCost: 810.00,
            employeeCost: 243.00,
            employerCost: 567.00
          },
          {
            tierName: 'Family',
            totalCost: 1350.00,
            employeeCost: 405.00,
            employerCost: 945.00
          }
        ],
        contributionPolicy: {
          type: 'Percentage',
          employeeContribution: 30,
          employerContribution: 70
        },
        eligibilityRules: {
          employeeClasses: ['Full-Time', 'Part-Time'],
          waitingPeriod: {
            enabled: true,
            days: 30
          },
          minimumHours: 20
        },
        qualifyingLifeEventWindow: {
          enabled: true,
          windowDays: 30,
          allowedEvents: ['Marriage', 'Birth', 'Adoption', 'Divorce', 'Death', 'Loss of Coverage'],
          description: 'Standard QLE window for plan changes'
        }
      },
      // Dental plan assignment
      {
        companyId: companyIds[1], // TechCorp
        planId: planIds[1], // Aetna Dental
        carrierId: carrierIds[1],
        planYear: currentYear,
        isActive: true,
        enrollmentStartDate: new Date(`${currentYear - 1}-11-01`),
        enrollmentEndDate: new Date(`${currentYear - 1}-11-30`),
        planStartDate: new Date(`${currentYear}-01-01`),
        planEndDate: new Date(`${currentYear}-12-31`),
        rateStructure: 'Composite',
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 35.00,
            employeeCost: 10.50,
            employerCost: 24.50
          },
          {
            tierName: 'Employee + Family',
            totalCost: 85.00,
            employeeCost: 25.50,
            employerCost: 59.50
          }
        ],
        contributionPolicy: {
          type: 'Percentage',
          employeeContribution: 30,
          employerContribution: 70
        },
        eligibilityRules: {
          employeeClasses: ['Full-Time'],
          waitingPeriod: {
            enabled: true,
            days: 60
          },
          minimumHours: 30
        }
      },
      // Previous year expired plan assignment (for testing expired enrollments)
      {
        companyId: companyIds[1], // TechCorp
        planId: planIds[2], // HSA Plan
        carrierId: carrierIds[0],
        planYear: currentYear - 1,
        isActive: false,
        enrollmentStartDate: new Date(`${currentYear - 2}-11-01`),
        enrollmentEndDate: new Date(`${currentYear - 2}-11-30`),
        planStartDate: new Date(`${currentYear - 1}-01-01`),
        planEndDate: new Date(`${currentYear - 1}-12-31`),
        rateStructure: 'Age-Banded-Four-Tier',
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 380.00,
            employeeCost: 114.00,
            employerCost: 266.00
          }
        ],
        contributionPolicy: {
          type: 'Percentage',
          employeeContribution: 30,
          employerContribution: 70
        },
        eligibilityRules: {
          employeeClasses: ['Full-Time'],
          waitingPeriod: {
            enabled: true,
            days: 30
          }
        }
      },
      // HealthPlus company plan assignment
      {
        companyId: companyIds[2], // HealthPlus
        planId: planIds[0], // Blue Shield PPO
        carrierId: carrierIds[0],
        planYear: currentYear,
        isActive: true,
        enrollmentStartDate: new Date(`${currentYear - 1}-11-01`),
        enrollmentEndDate: new Date(`${currentYear - 1}-11-30`),
        planStartDate: new Date(`${currentYear}-01-01`),
        planEndDate: new Date(`${currentYear}-12-31`),
        rateStructure: 'Four-Tier',
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 420.00,
            employeeCost: 126.00,
            employerCost: 294.00
          },
          {
            tierName: 'Family',
            totalCost: 1260.00,
            employeeCost: 378.00,
            employerCost: 882.00
          }
        ],
        contributionPolicy: {
          type: 'Percentage',
          employeeContribution: 30,
          employerContribution: 70
        },
        eligibilityRules: {
          employeeClasses: ['Full-Time'],
          waitingPeriod: {
            enabled: true,
            days: 30
          }
        }
      }
    ];

    for (const planAssignmentData of planAssignments) {
      const result = await makeRequest(
        'POST',
        `${TEST_CONFIG.PLAN_ASSIGNMENT_API}`,
        planAssignmentData,
        userIds[0] // SuperAdmin
      );

      if (result.success) {
        testResults.createdData.planAssignments.push(result.data._id || result.data.id);
        log(`Created plan assignment: ${planAssignmentData.planYear} ${planAssignmentData.rateStructure}`, 'create');
      } else {
        log(`Failed to create plan assignment: ${result.error}`, 'error');
      }
    }

    return testResults.createdData.planAssignments;

  } catch (error) {
    log(`❌ Error creating plan assignments: ${error.message}`, 'error');
    return [];
  }
}

// 🧪 TEST FUNCTIONS

/**
 * Test enrollment periods API with multiple plan assignments
 */
async function testEnrollmentPeriods(planAssignmentIds, userIds) {
  logSection('TESTING: GET ENROLLMENT PERIODS');

  // Test with SuperAdmin on first plan assignment
  const result1 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/enrollment-periods/${planAssignmentIds[0]}`,
    null,
    userIds[0] // SuperAdmin
  );

  assert(result1.success, 'SuperAdmin should access enrollment periods', 'enrollmentPeriods');

  if (result1.success) {
    assert(result1.data.success === true, 'Response should have success: true', 'enrollmentPeriods');
    assert(result1.data.enrollmentPeriods !== undefined, 'Should include enrollment periods', 'enrollmentPeriods');
    assert(result1.data.enrollmentPeriods.openEnrollment !== undefined, 'Should include open enrollment', 'enrollmentPeriods');
    assert(result1.data.enrollmentPeriods.newHire !== undefined, 'Should include new hire period', 'enrollmentPeriods');
    assert(result1.data.enrollmentPeriods.qualifyingLifeEvent !== undefined, 'Should include QLE period', 'enrollmentPeriods');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test with Broker on their company's plan assignment
  const result2 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/enrollment-periods/${planAssignmentIds[0]}`,
    null,
    userIds[1] // Broker
  );

  assert(result2.success, 'Broker should access own company enrollment periods', 'enrollmentPeriods');

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test with invalid plan assignment ID
  const result3 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/enrollment-periods/invalid-id`,
    null,
    userIds[0]
  );

  assert(!result3.success, 'Invalid plan assignment ID should fail', 'enrollmentPeriods');
  assert(result3.status === 404 || result3.status === 400, 'Should return 404 or 400 for invalid ID', 'enrollmentPeriods');

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test cross-company access (should fail)
  if (planAssignmentIds.length > 3) {
    const result4 = await makeRequest(
      'GET',
      `${TEST_CONFIG.API_BASE}/enrollment-periods/${planAssignmentIds[3]}`, // HealthPlus plan
      null,
      userIds[2] // TechCorp Admin
    );

    assert(!result4.success, 'Cross-company access should fail', 'enrollmentPeriods');
    assert(result4.status === 403, 'Should return 403 for cross-company access', 'enrollmentPeriods');
  }
}

/**
 * Test estimate plan costs API with multiple scenarios
 */
async function testEstimatePlanCosts(planAssignmentIds, userIds) {
  logSection('TESTING: ESTIMATE PLAN COSTS');

  // Test 1: Default scenarios
  const requestData1 = {
    planAssignmentId: planAssignmentIds[0]
  };

  const result1 = await makeRequest(
    'POST',
    `${TEST_CONFIG.API_BASE}/estimate-plan-costs`,
    requestData1,
    userIds[0] // SuperAdmin
  );

  assert(result1.success, 'SuperAdmin should estimate plan costs with default scenarios', 'estimatePlanCosts');

  if (result1.success) {
    assert(result1.data.success === true, 'Response should have success: true', 'estimatePlanCosts');
    assert(result1.data.costEstimations !== undefined, 'Should include cost estimations', 'estimatePlanCosts');
    assert(result1.data.metadata !== undefined, 'Should include metadata', 'estimatePlanCosts');
    assert(Array.isArray(result1.data.costEstimations), 'Cost estimations should be array', 'estimatePlanCosts');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 2: Custom scenarios with different rate structures
  const requestData2 = {
    planAssignmentId: planAssignmentIds[1], // Dental plan with Composite rate structure
    scenarios: [
      { employeeAge: 25, employeeSalary: 45000, description: 'Young employee' },
      { employeeAge: 35, employeeSalary: 65000, description: 'Mid-career employee' },
      { employeeAge: 45, employeeSalary: 85000, description: 'Senior employee' },
      { employeeAge: 55, employeeSalary: 105000, description: 'Executive employee' }
    ]
  };

  const result2 = await makeRequest(
    'POST',
    `${TEST_CONFIG.API_BASE}/estimate-plan-costs`,
    requestData2,
    userIds[0]
  );

  assert(result2.success, 'Custom scenarios should work', 'estimatePlanCosts');

  if (result2.success) {
    assert(result2.data.metadata.scenarioCount === 4, 'Should use custom scenario count', 'estimatePlanCosts');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 3: Edge case scenarios
  const requestData3 = {
    planAssignmentId: planAssignmentIds[0],
    scenarios: [
      { employeeAge: 18, employeeSalary: 15000, description: 'Minimum age/salary' },
      { employeeAge: 70, employeeSalary: 500000, description: 'Maximum age/salary' },
      { employeeAge: 30, employeeSalary: 0, description: 'Zero salary edge case' }
    ]
  };

  const result3 = await makeRequest(
    'POST',
    `${TEST_CONFIG.API_BASE}/estimate-plan-costs`,
    requestData3,
    userIds[0]
  );

  // Should handle edge cases gracefully
  if (result3.success) {
    assert(true, 'Edge case scenarios handled gracefully', 'estimatePlanCosts');
  } else {
    assert(result3.status === 400, 'Invalid scenarios should return 400', 'estimatePlanCosts');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 4: Access control - Broker should access own company plans
  const result4 = await makeRequest(
    'POST',
    `${TEST_CONFIG.API_BASE}/estimate-plan-costs`,
    requestData1,
    userIds[1] // Broker
  );

  assert(result4.success, 'Broker should access own company plan costs', 'estimatePlanCosts');

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 5: Missing planAssignmentId
  const result5 = await makeRequest(
    'POST',
    `${TEST_CONFIG.API_BASE}/estimate-plan-costs`,
    {},
    userIds[0]
  );

  assert(!result5.success, 'Missing planAssignmentId should fail', 'estimatePlanCosts');
  assert(result5.status === 400, 'Should return 400 for missing planAssignmentId', 'estimatePlanCosts');

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 6: Invalid planAssignmentId
  const result6 = await makeRequest(
    'POST',
    `${TEST_CONFIG.API_BASE}/estimate-plan-costs`,
    { planAssignmentId: 'invalid-id' },
    userIds[0]
  );

  assert(!result6.success, 'Invalid planAssignmentId should fail', 'estimatePlanCosts');
  assert(result6.status === 404 || result6.status === 400, 'Should return 404 or 400 for invalid planAssignmentId', 'estimatePlanCosts');
}

/**
 * Test get expired enrollments API with plan assignments
 */
async function testGetExpiredEnrollments(userIds, planAssignmentIds) {
  logSection('TESTING: GET EXPIRED ENROLLMENTS');

  // Test 1: User mode (default)
  const result1 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/expired`,
    null,
    userIds[0] // SuperAdmin
  );

  assert(result1.success, 'SuperAdmin should get expired enrollments in user mode', 'getExpiredEnrollments');

  if (result1.success) {
    assert(result1.data.success === true, 'Response should have success: true', 'getExpiredEnrollments');
    assert(result1.data.mode === 'user', 'Default mode should be user', 'getExpiredEnrollments');
    assert(result1.data.expiredEnrollments !== undefined, 'Should include expired enrollments', 'getExpiredEnrollments');
    assert(result1.data.count !== undefined, 'Should include count', 'getExpiredEnrollments');
    assert(result1.data.expiryCheckPerformed === true, 'Should indicate expiry check performed', 'getExpiredEnrollments');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 2: Plan assignments mode
  const result2 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/expired?mode=planAssignments&planAssignmentIds=${planAssignmentIds[0]}`,
    null,
    userIds[0]
  );

  assert(result2.success, 'SuperAdmin should get expired enrollments by plan assignments', 'getExpiredEnrollments');

  if (result2.success) {
    assert(result2.data.mode === 'planAssignments', 'Mode should be planAssignments', 'getExpiredEnrollments');
    assert(result2.data.planAssignmentIds !== undefined, 'Should include planAssignmentIds', 'getExpiredEnrollments');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 3: Multiple plan assignments
  if (planAssignmentIds.length > 1) {
    const result3 = await makeRequest(
      'GET',
      `${TEST_CONFIG.API_BASE}/expired?mode=planAssignments&planAssignmentIds=${planAssignmentIds[0]},${planAssignmentIds[1]}`,
      null,
      userIds[0]
    );

    assert(result3.success, 'Multiple plan assignments should work', 'getExpiredEnrollments');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 4: Broker access control
  const result4 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/expired`,
    null,
    userIds[1] // Broker
  );

  // Broker should be able to see expired enrollments for their companies
  if (result4.success) {
    assert(result4.data.mode === 'user', 'Broker should get user mode', 'getExpiredEnrollments');
  } else {
    assert(result4.status === 403, 'Broker access failure should be 403', 'getExpiredEnrollments');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 5: Employee access control
  const result5 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/expired`,
    null,
    userIds[3] // Employee
  );

  // Employee should be able to see their own expired enrollments
  if (result5.success) {
    assert(result5.data.mode === 'user', 'Employee should get user mode', 'getExpiredEnrollments');
  } else {
    assert(result5.status === 403, 'Employee access failure should be 403', 'getExpiredEnrollments');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 6: Invalid mode
  const result6 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/expired?mode=invalid`,
    null,
    userIds[0]
  );

  assert(!result6.success, 'Invalid mode should fail', 'getExpiredEnrollments');
  assert(result6.status === 400, 'Should return 400 for invalid mode', 'getExpiredEnrollments');

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test 7: Plan assignments mode without planAssignmentIds
  const result7 = await makeRequest(
    'GET',
    `${TEST_CONFIG.API_BASE}/expired?mode=planAssignments`,
    null,
    userIds[0]
  );

  assert(!result7.success, 'Plan assignments mode without IDs should fail', 'getExpiredEnrollments');
  assert(result7.status === 400, 'Should return 400 for missing planAssignmentIds', 'getExpiredEnrollments');
}

/**
 * Test check expired enrollments API
 */
async function testCheckExpiredEnrollments(userIds) {
  logSection('TESTING: CHECK EXPIRED ENROLLMENTS');

  // Test with SuperAdmin (should succeed)
  const result1 = await makeRequest(
    'POST',
    `${TEST_CONFIG.API_BASE}/check-expired`,
    {},
    userIds[0] // SuperAdmin
  );

  assert(result1.success, 'SuperAdmin should check expired enrollments', 'checkExpiredEnrollments');

  if (result1.success) {
    assert(result1.data.success === true, 'Response should have success: true', 'checkExpiredEnrollments');
    assert(typeof result1.data.expiredCount === 'number', 'Should include expiredCount', 'checkExpiredEnrollments');
  }

  await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

  // Test with non-SuperAdmin (should fail)
  const result2 = await makeRequest(
    'POST',
    `${TEST_CONFIG.API_BASE}/check-expired`,
    {},
    userIds[1] // Broker
  );

  assert(!result2.success, 'Non-SuperAdmin should not check expired enrollments', 'checkExpiredEnrollments');
  assert(result2.status === 403, 'Should return 403 for non-SuperAdmin', 'checkExpiredEnrollments');
}

/**
 * Comprehensive employee enrollment testing with multi-year scenarios and business rules
 */
async function testEmployeeEnrollmentComprehensive(planAssignmentIds, userIds) {
  logSection('COMPREHENSIVE EMPLOYEE ENROLLMENT TESTING');

  try {
    // Test 1: Create enrollments for different scenarios
    logSection('Test 1: Creating Employee Enrollments');

    const currentYear = new Date().getFullYear();
    const enrollmentScenarios = [
      {
        name: 'New Hire Enrollment',
        employeeId: userIds[3], // Employee 1
        planAssignmentId: planAssignmentIds[0],
        enrollmentType: 'New Hire',
        selectedTier: 'Employee Only',
        enrollmentDate: new Date(),
        effectiveDate: new Date(`${currentYear}-02-01`)
      },
      {
        name: 'Open Enrollment',
        employeeId: userIds[4], // Employee 2
        planAssignmentId: planAssignmentIds[0],
        enrollmentType: 'Open Enrollment',
        selectedTier: 'Family',
        enrollmentDate: new Date(`${currentYear - 1}-11-15`),
        effectiveDate: new Date(`${currentYear}-01-01`)
      },
      {
        name: 'Qualifying Life Event',
        employeeId: userIds[5], // Employee 3
        planAssignmentId: planAssignmentIds[3], // HealthPlus plan
        enrollmentType: 'Qualifying Life Event',
        selectedTier: 'Employee + Spouse',
        enrollmentDate: new Date(),
        effectiveDate: new Date(),
        qualifyingLifeEvent: {
          eventType: 'Marriage',
          eventDate: new Date(),
          documentation: 'Marriage certificate uploaded'
        }
      }
    ];

    for (const scenario of enrollmentScenarios) {
      const result = await makeRequest(
        'POST',
        `${TEST_CONFIG.API_BASE}`,
        scenario,
        userIds[0] // SuperAdmin
      );

      if (result.success) {
        testResults.createdData.enrollments.push(result.data._id || result.data.id);
        log(`✅ Created enrollment: ${scenario.name}`, 'success');
        assert(true, `${scenario.name} enrollment created successfully`, 'employeeEnrollment');
      } else {
        log(`❌ Failed to create enrollment: ${scenario.name} - ${result.error}`, 'error');
        assert(false, `${scenario.name} enrollment should be created`, 'employeeEnrollment');
      }

      await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);
    }

    // Test 2: Test enrollment period validation
    logSection('Test 2: Enrollment Period Validation');

    // Test enrollment outside of enrollment period (should fail)
    const outsidePeriodEnrollment = {
      employeeId: userIds[4],
      planAssignmentId: planAssignmentIds[0],
      enrollmentType: 'Open Enrollment',
      selectedTier: 'Employee Only',
      enrollmentDate: new Date(`${currentYear}-06-01`), // Outside enrollment period
      effectiveDate: new Date(`${currentYear}-07-01`)
    };

    const result2 = await makeRequest(
      'POST',
      `${TEST_CONFIG.API_BASE}`,
      outsidePeriodEnrollment,
      userIds[0]
    );

    assert(!result2.success, 'Enrollment outside period should fail', 'enrollmentPeriodValidation');

    await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

    // Test 3: Test waiting period validation for new hires
    logSection('Test 3: Waiting Period Validation');

    const newHireBeforeWaitingPeriod = {
      employeeId: userIds[3],
      planAssignmentId: planAssignmentIds[1], // Dental plan with 60-day waiting period
      enrollmentType: 'New Hire',
      selectedTier: 'Employee Only',
      enrollmentDate: new Date(),
      effectiveDate: new Date() // Immediate effective date
    };

    const result3 = await makeRequest(
      'POST',
      `${TEST_CONFIG.API_BASE}`,
      newHireBeforeWaitingPeriod,
      userIds[0]
    );

    // Should either succeed with adjusted effective date or fail with waiting period message
    if (result3.success) {
      assert(true, 'New hire enrollment handled waiting period correctly', 'waitingPeriodValidation');
    } else {
      assert(result3.error.includes('waiting period') || result3.error.includes('eligibility'),
             'Should mention waiting period or eligibility', 'waitingPeriodValidation');
    }

    await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

    // Test 4: Test multi-year enrollment expiry behavior
    logSection('Test 4: Multi-Year Enrollment Expiry Behavior');

    // Create an enrollment for previous year that should be expired
    const previousYearEnrollment = {
      employeeId: userIds[4],
      planAssignmentId: planAssignmentIds[2], // Previous year plan assignment
      enrollmentType: 'Open Enrollment',
      selectedTier: 'Employee Only',
      enrollmentDate: new Date(`${currentYear - 2}-11-15`),
      effectiveDate: new Date(`${currentYear - 1}-01-01`),
      planYear: currentYear - 1,
      planEndDate: new Date(`${currentYear - 1}-12-31`)
    };

    const result4 = await makeRequest(
      'POST',
      `${TEST_CONFIG.API_BASE}`,
      previousYearEnrollment,
      userIds[0]
    );

    if (result4.success) {
      testResults.createdData.enrollments.push(result4.data._id || result4.data.id);
      log(`Created previous year enrollment for expiry testing`, 'create');

      // Now test the expired enrollments API
      const expiredResult = await makeRequest(
        'GET',
        `${TEST_CONFIG.API_BASE}/expired`,
        null,
        userIds[0]
      );

      if (expiredResult.success) {
        assert(expiredResult.data.expiredEnrollments.length >= 0,
               'Should return expired enrollments array', 'multiYearExpiry');
      }
    }

    await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

    // Test 5: Test access control for different user roles
    logSection('Test 5: Access Control Testing');

    const accessControlTests = [
      {
        name: 'Broker accessing own company enrollments',
        userId: userIds[1], // Broker
        shouldSucceed: true
      },
      {
        name: 'Company Admin accessing own company enrollments',
        userId: userIds[2], // Company Admin
        shouldSucceed: true
      },
      {
        name: 'Employee accessing own enrollments',
        userId: userIds[3], // Employee
        shouldSucceed: true
      }
    ];

    for (const test of accessControlTests) {
      const result = await makeRequest(
        'GET',
        `${TEST_CONFIG.API_BASE}`,
        null,
        test.userId
      );

      if (test.shouldSucceed) {
        assert(result.success, `${test.name} should succeed`, 'accessControl');
      } else {
        assert(!result.success, `${test.name} should fail`, 'accessControl');
      }

      await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);
    }

    // Test 6: Test business rule validations
    logSection('Test 6: Business Rule Validations');

    // Test duplicate enrollment prevention
    const duplicateEnrollment = {
      employeeId: userIds[3],
      planAssignmentId: planAssignmentIds[0],
      enrollmentType: 'New Hire',
      selectedTier: 'Employee Only',
      enrollmentDate: new Date(),
      effectiveDate: new Date()
    };

    const duplicateResult = await makeRequest(
      'POST',
      `${TEST_CONFIG.API_BASE}`,
      duplicateEnrollment,
      userIds[0]
    );

    assert(!duplicateResult.success, 'Duplicate enrollment should be prevented', 'businessRules');

    await wait(TEST_CONFIG.DELAY_BETWEEN_TESTS);

    log('✅ Comprehensive employee enrollment testing completed', 'success');

  } catch (error) {
    log(`❌ Error in comprehensive enrollment testing: ${error.message}`, 'error');
    assert(false, `Comprehensive enrollment testing failed: ${error.message}`, 'employeeEnrollment');
  }
}

// 🧹 CLEANUP FUNCTIONS

/**
 * Clean up all created test data using database access and APIs
 */
async function cleanupTestData() {
  logSection('CLEANING UP TEST DATA');

  let cleanupErrors = 0;

  try {
    // Clean up enrollments (via API if any were created)
    for (const enrollmentId of testResults.createdData.enrollments) {
      const result = await makeRequest('DELETE', `${TEST_CONFIG.API_BASE}/${enrollmentId}`);
      if (result.success) {
        log(`Deleted enrollment: ${enrollmentId}`, 'cleanup');
      } else {
        log(`Failed to delete enrollment: ${enrollmentId}`, 'error');
        cleanupErrors++;
      }
      await wait(100);
    }

    // Clean up plan assignments (via API)
    for (const planAssignmentId of testResults.createdData.planAssignments) {
      const result = await makeRequest('DELETE', `${TEST_CONFIG.PLAN_ASSIGNMENT_API}/${planAssignmentId}`);
      if (result.success) {
        log(`Deleted plan assignment: ${planAssignmentId}`, 'cleanup');
      } else {
        log(`Failed to delete plan assignment: ${planAssignmentId}`, 'error');
        cleanupErrors++;
      }
      await wait(100);
    }

    // Clean up plans (via database)
    if (PlanModel) {
      const deletedPlans = await PlanModel.deleteMany({ _id: { $in: testResults.createdData.plans } });
      log(`Deleted ${deletedPlans.deletedCount} plans via database`, 'cleanup');
    }

    // Clean up carriers (via database)
    if (CarrierModel) {
      const deletedCarriers = await CarrierModel.deleteMany({ _id: { $in: testResults.createdData.carriers } });
      log(`Deleted ${deletedCarriers.deletedCount} carriers via database`, 'cleanup');
    }

    // Clean up users (via database)
    if (UserModel) {
      const deletedUsers = await UserModel.deleteMany({ _id: { $in: testResults.createdData.users } });
      log(`Deleted ${deletedUsers.deletedCount} users via database`, 'cleanup');
    }

    // Clean up companies (via database)
    if (CompanyModel) {
      const deletedCompanies = await CompanyModel.deleteMany({ _id: { $in: testResults.createdData.companies } });
      log(`Deleted ${deletedCompanies.deletedCount} companies via database`, 'cleanup');
    }

    // Close database connection
    await closeDatabase();

    if (cleanupErrors === 0) {
      log('✅ All test data cleaned up successfully!', 'success');
    } else {
      log(`⚠️ Cleanup completed with ${cleanupErrors} errors`, 'warning');
    }

  } catch (error) {
    log(`❌ Error during cleanup: ${error.message}`, 'error');
    cleanupErrors++;
  }

  return cleanupErrors;
}

// 🎯 MAIN TEST RUNNER

/**
 * Main comprehensive test function with database access and API testing
 */
async function runComprehensiveTestsWithCleanup() {
  try {
    logSection('🎯 COMPREHENSIVE ENROLLMENT APIS TEST WITH DATABASE ACCESS & CLEANUP');
    log(`📅 Test Started: ${new Date().toISOString()}`);
    log(`🌐 Server URL: ${TEST_CONFIG.BASE_URL}`);
    log(`🗄️ Using test database with automatic cleanup\n`);

    // Step 0: Initialize database connection
    logSection('STEP 0: INITIALIZING DATABASE CONNECTION');
    const dbInitialized = await initializeDatabase();
    if (!dbInitialized) {
      throw new Error('Failed to initialize database connection');
    }

    // Step 1: Create base test data via database
    logSection('STEP 1: CREATING BASE TEST DATA VIA DATABASE');

    const companyIds = await createTestCompanies();
    await wait(TEST_CONFIG.CLEANUP_DELAY);

    const userIds = await createTestUsers(companyIds);
    await wait(TEST_CONFIG.CLEANUP_DELAY);

    const carrierIds = await createTestCarriers();
    await wait(TEST_CONFIG.CLEANUP_DELAY);

    const planIds = await createTestPlans(carrierIds);
    await wait(TEST_CONFIG.CLEANUP_DELAY);

    // Verify we have the minimum required base data
    if (companyIds.length === 0 || userIds.length === 0 || carrierIds.length === 0 || planIds.length === 0) {
      throw new Error('Failed to create minimum required base data');
    }

    log(`✅ Base data creation completed!`, 'success');
    log(`   Companies: ${companyIds.length}`, 'info');
    log(`   Users: ${userIds.length}`, 'info');
    log(`   Carriers: ${carrierIds.length}`, 'info');
    log(`   Plans: ${planIds.length}`, 'info');

    // Step 2: Create plan assignments via API (this is what we're testing)
    logSection('STEP 2: CREATING PLAN ASSIGNMENTS VIA API');

    const planAssignmentIds = await createTestPlanAssignments(companyIds, planIds, carrierIds, userIds);
    await wait(TEST_CONFIG.CLEANUP_DELAY);

    if (planAssignmentIds.length === 0) {
      throw new Error('Failed to create plan assignments via API');
    }

    log(`✅ Plan assignments created via API: ${planAssignmentIds.length}`, 'success');

    // Step 3: Run comprehensive enrollment API tests
    logSection('STEP 3: RUNNING COMPREHENSIVE ENROLLMENT API TESTS');

    await testEnrollmentPeriods(planAssignmentIds, userIds);
    await testEstimatePlanCosts(planAssignmentIds, userIds);
    await testGetExpiredEnrollments(userIds, planAssignmentIds);
    await testCheckExpiredEnrollments(userIds);

    // Step 4: Test employee enrollment APIs with multi-year scenarios
    logSection('STEP 4: TESTING EMPLOYEE ENROLLMENT APIS WITH MULTI-YEAR SCENARIOS');

    await testEmployeeEnrollmentComprehensive(planAssignmentIds, userIds);

    // Step 5: Generate test results
    logSection('STEP 5: TEST RESULTS SUMMARY');

    const totalTests = testResults.passed + testResults.failed;
    const successRate = totalTests > 0 ? ((testResults.passed / totalTests) * 100).toFixed(1) : 0;

    log(`📊 Total Tests: ${totalTests}`);
    log(`✅ Passed: ${testResults.passed}`);
    log(`❌ Failed: ${testResults.failed}`);
    log(`📈 Success Rate: ${successRate}%`);

    if (testResults.failed > 0) {
      log('\n❌ Failed Tests:', 'error');
      testResults.errors.forEach((error, index) => {
        log(`${index + 1}. [${error.test}] ${error.message}`, 'error');
      });
    }

    return testResults.failed === 0;

  } catch (error) {
    log(`❌ Test suite failed: ${error.message}`, 'error');
    return false;
  } finally {
    // Step 6: Always clean up test data
    logSection('STEP 6: CLEANING UP TEST DATA');
    await wait(TEST_CONFIG.CLEANUP_DELAY);
    await cleanupTestData();
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runComprehensiveTestsWithCleanup()
    .then(success => {
      if (success) {
        log('\n🎉 ALL TESTS PASSED! Refactored APIs are working correctly.', 'success');
        process.exit(0);
      } else {
        log('\n❌ Some tests failed. Please review the results above.', 'error');
        process.exit(1);
      }
    })
    .catch(error => {
      log(`💥 Test suite crashed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = {
  runComprehensiveTestsWithCleanup,
  cleanupTestData,
  TEST_CONFIG
};
