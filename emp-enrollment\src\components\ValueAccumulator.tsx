
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, DollarSign } from 'lucide-react';
import { UserProfile } from './BenefitsEnrollmentBot';

interface ValueAccumulatorProps {
  userProfile: UserProfile;
  currentStep: number;
}

export const ValueAccumulator = ({ userProfile, currentStep }: ValueAccumulatorProps) => {
  const calculateAccumulatedValue = () => {
    let totalEmployeeCost = 0;
    let totalEmployerContribution = 0;
    let totalValue = 0;

    if (userProfile.selectedMedical) {
      const medicalAnnual = userProfile.selectedMedical.cost * 26;
      totalEmployeeCost += medicalAnnual * 0.2; // Employee pays 20%
      totalEmployerContribution += medicalAnnual * 0.8; // Employer pays 80%
      totalValue += medicalAnnual * 4; // Total insurance value
    }

    if (userProfile.selectedDental) {
      const dentalAnnual = userProfile.selectedDental.cost * 26;
      totalEmployeeCost += dentalAnnual * 0.5;
      totalEmployerContribution += dentalAnnual * 0.5;
      totalValue += dentalAnnual + 800; // Additional value from prevention
    }

    if (userProfile.selectedVision) {
      const visionAnnual = userProfile.selectedVision.cost * 26;
      totalEmployeeCost += visionAnnual * 0.5;
      totalEmployerContribution += visionAnnual * 0.5;
      totalValue += visionAnnual + 300; // Additional value
    }

    if (userProfile.selectedPetInsurance && userProfile.selectedPetInsurance.name !== 'No Pet Insurance') {
      const petAnnual = userProfile.selectedPetInsurance.cost * 26;
      totalEmployeeCost += petAnnual;
      totalValue += petAnnual * 3; // Potential savings
    }

    if (userProfile.selectedHospitalIndemnity && userProfile.selectedHospitalIndemnity.name !== 'No Hospital Indemnity') {
      const hospitalAnnual = userProfile.selectedHospitalIndemnity.cost * 26;
      totalEmployeeCost += hospitalAnnual;
      totalValue += hospitalAnnual * 5; // Potential protection value
    }

    const netValue = totalValue - totalEmployeeCost;
    const roi = totalEmployeeCost > 0 ? Math.round((netValue / totalEmployeeCost) * 100) : 0;

    return {
      totalEmployeeCost: Math.round(totalEmployeeCost),
      totalEmployerContribution: Math.round(totalEmployerContribution),
      totalValue: Math.round(totalValue),
      netValue: Math.round(netValue),
      roi
    };
  };

  const values = calculateAccumulatedValue();
  const progressPercentage = (currentStep / 7) * 100;

  // Only show if we have at least one plan selected
  if (!userProfile.selectedMedical && !userProfile.selectedDental && !userProfile.selectedVision) {
    return null;
  }

  return (
    <Card className="bg-gradient-to-r from-emerald-50 to-teal-100 dark:from-emerald-950 dark:to-teal-900 border-emerald-200 dark:border-emerald-800">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-emerald-800 dark:text-emerald-200">
          <TrendingUp className="w-5 h-5" />
          📈 Your Benefits Value So Far
        </CardTitle>
        <Progress value={progressPercentage} className="w-full" />
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-3">
          <div className="text-center p-2 bg-white dark:bg-gray-800 rounded">
            <p className="text-xs text-muted-foreground">You Pay</p>
            <p className="text-lg font-bold text-blue-600">${values.totalEmployeeCost.toLocaleString()}</p>
          </div>
          <div className="text-center p-2 bg-white dark:bg-gray-800 rounded">
            <p className="text-xs text-muted-foreground">Total Value</p>
            <p className="text-lg font-bold text-green-600">${values.totalValue.toLocaleString()}</p>
          </div>
        </div>

        <div className="text-center p-3 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-lg">
          <div className="flex items-center justify-center gap-1 mb-1">
            <DollarSign className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium">Your Net Benefit</span>
          </div>
          <p className="text-xl font-bold text-green-700 dark:text-green-300">
            ${values.netValue.toLocaleString()}
          </p>
          <Badge className="mt-1 bg-green-600 text-white">
            {values.roi}% ROI
          </Badge>
        </div>

        <div className="text-xs text-center text-muted-foreground">
          💡 Value increases with each plan you add!
        </div>
      </CardContent>
    </Card>
  );
};
