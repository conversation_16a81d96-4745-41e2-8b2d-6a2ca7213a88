"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@microsoft";
exports.ids = ["vendor-chunks/@microsoft"];
exports.modules = {

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/_polyfill-node.buffer.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/_virtual/_polyfill-node.buffer.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Buffer: () => (/* binding */ y),\n/* harmony export */   INSPECT_MAX_BYTES: () => (/* binding */ l),\n/* harmony export */   isBuffer: () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _polyfill_node_global_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_polyfill-node.global.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/_polyfill-node.global.js\");\nvar r=[],e=[],n=\"undefined\"!=typeof Uint8Array?Uint8Array:Array,i=!1;function o(){i=!0;for(var t=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",n=0;n<64;++n)r[n]=t[n],e[t.charCodeAt(n)]=n;e[\"-\".charCodeAt(0)]=62,e[\"_\".charCodeAt(0)]=63}function u(t,e,n){for(var i,o,u=[],f=e;f<n;f+=3)i=(t[f]<<16)+(t[f+1]<<8)+t[f+2],u.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return u.join(\"\")}function f(t){var e;i||o();for(var n=t.length,f=n%3,s=\"\",h=[],a=16383,c=0,l=n-f;c<l;c+=a)h.push(u(t,c,c+a>l?l:c+a));return 1===f?(e=t[n-1],s+=r[e>>2],s+=r[e<<4&63],s+=\"==\"):2===f&&(e=(t[n-2]<<8)+t[n-1],s+=r[e>>10],s+=r[e>>4&63],s+=r[e<<2&63],s+=\"=\"),h.push(s),h.join(\"\")}function s(t,r,e,n,i){var o,u,f=8*i-n-1,s=(1<<f)-1,h=s>>1,a=-7,c=e?i-1:0,l=e?-1:1,p=t[r+c];for(c+=l,o=p&(1<<-a)-1,p>>=-a,a+=f;a>0;o=256*o+t[r+c],c+=l,a-=8);for(u=o&(1<<-a)-1,o>>=-a,a+=n;a>0;u=256*u+t[r+c],c+=l,a-=8);if(0===o)o=1-h;else{if(o===s)return u?NaN:1/0*(p?-1:1);u+=Math.pow(2,n),o-=h}return(p?-1:1)*u*Math.pow(2,o-n)}function h(t,r,e,n,i,o){var u,f,s,h=8*o-i-1,a=(1<<h)-1,c=a>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,g=n?1:-1,y=r<0||0===r&&1/r<0?1:0;for(r=Math.abs(r),isNaN(r)||r===1/0?(f=isNaN(r)?1:0,u=a):(u=Math.floor(Math.log(r)/Math.LN2),r*(s=Math.pow(2,-u))<1&&(u--,s*=2),(r+=u+c>=1?l/s:l*Math.pow(2,1-c))*s>=2&&(u++,s/=2),u+c>=a?(f=0,u=a):u+c>=1?(f=(r*s-1)*Math.pow(2,i),u+=c):(f=r*Math.pow(2,c-1)*Math.pow(2,i),u=0));i>=8;t[e+p]=255&f,p+=g,f/=256,i-=8);for(u=u<<i|f,h+=i;h>0;t[e+p]=255&u,p+=g,u/=256,h-=8);t[e+p-g]|=128*y}var a={}.toString,c=Array.isArray||function(t){return\"[object Array]\"==a.call(t)},l=50;function p(){return y.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function g(t,r){if(p()<r)throw new RangeError(\"Invalid typed array length\");return y.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(r)).__proto__=y.prototype:(null===t&&(t=new y(r)),t.length=r),t}function y(t,r,e){if(!(y.TYPED_ARRAY_SUPPORT||this instanceof y))return new y(t,r,e);if(\"number\"==typeof t){if(\"string\"==typeof r)throw new Error(\"If encoding is specified then the first argument must be a string\");return v(this,t)}return w(this,t,r,e)}function w(t,r,e,n){if(\"number\"==typeof r)throw new TypeError('\"value\" argument must not be a number');return\"undefined\"!=typeof ArrayBuffer&&r instanceof ArrayBuffer?function(t,r,e,n){if(r.byteLength,e<0||r.byteLength<e)throw new RangeError(\"'offset' is out of bounds\");if(r.byteLength<e+(n||0))throw new RangeError(\"'length' is out of bounds\");r=void 0===e&&void 0===n?new Uint8Array(r):void 0===n?new Uint8Array(r,e):new Uint8Array(r,e,n);y.TYPED_ARRAY_SUPPORT?(t=r).__proto__=y.prototype:t=E(t,r);return t}(t,r,e,n):\"string\"==typeof r?function(t,r,e){\"string\"==typeof e&&\"\"!==e||(e=\"utf8\");if(!y.isEncoding(e))throw new TypeError('\"encoding\" must be a valid string encoding');var n=0|b(r,e);t=g(t,n);var i=t.write(r,e);i!==n&&(t=t.slice(0,i));return t}(t,r,e):function(t,r){if(R(r)){var e=0|A(r.length);return 0===(t=g(t,e)).length||r.copy(t,0,0,e),t}if(r){if(\"undefined\"!=typeof ArrayBuffer&&r.buffer instanceof ArrayBuffer||\"length\"in r)return\"number\"!=typeof r.length||(n=r.length)!=n?g(t,0):E(t,r);if(\"Buffer\"===r.type&&c(r.data))return E(t,r.data)}var n;throw new TypeError(\"First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.\")}(t,r)}function d(t){if(\"number\"!=typeof t)throw new TypeError('\"size\" argument must be a number');if(t<0)throw new RangeError('\"size\" argument must not be negative')}function v(t,r){if(d(r),t=g(t,r<0?0:0|A(r)),!y.TYPED_ARRAY_SUPPORT)for(var e=0;e<r;++e)t[e]=0;return t}function E(t,r){var e=r.length<0?0:0|A(r.length);t=g(t,e);for(var n=0;n<e;n+=1)t[n]=255&r[n];return t}function A(t){if(t>=p())throw new RangeError(\"Attempt to allocate Buffer larger than maximum size: 0x\"+p().toString(16)+\" bytes\");return 0|t}function R(t){return!(null==t||!t._isBuffer)}function b(t,r){if(R(t))return t.length;if(\"undefined\"!=typeof ArrayBuffer&&\"function\"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;\"string\"!=typeof t&&(t=\"\"+t);var e=t.length;if(0===e)return 0;for(var n=!1;;)switch(r){case\"ascii\":case\"latin1\":case\"binary\":return e;case\"utf8\":case\"utf-8\":case void 0:return K(t).length;case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return 2*e;case\"hex\":return e>>>1;case\"base64\":return Q(t).length;default:if(n)return K(t).length;r=(\"\"+r).toLowerCase(),n=!0}}function m(t,r,e){var n=!1;if((void 0===r||r<0)&&(r=0),r>this.length)return\"\";if((void 0===e||e>this.length)&&(e=this.length),e<=0)return\"\";if((e>>>=0)<=(r>>>=0))return\"\";for(t||(t=\"utf8\");;)switch(t){case\"hex\":return k(this,r,e);case\"utf8\":case\"utf-8\":return L(this,r,e);case\"ascii\":return D(this,r,e);case\"latin1\":case\"binary\":return x(this,r,e);case\"base64\":return O(this,r,e);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return N(this,r,e);default:if(n)throw new TypeError(\"Unknown encoding: \"+t);t=(t+\"\").toLowerCase(),n=!0}}function _(t,r,e){var n=t[r];t[r]=t[e],t[e]=n}function P(t,r,e,n,i){if(0===t.length)return-1;if(\"string\"==typeof e?(n=e,e=0):e>2147483647?e=2147483647:e<-2147483648&&(e=-2147483648),e=+e,isNaN(e)&&(e=i?0:t.length-1),e<0&&(e=t.length+e),e>=t.length){if(i)return-1;e=t.length-1}else if(e<0){if(!i)return-1;e=0}if(\"string\"==typeof r&&(r=y.from(r,n)),R(r))return 0===r.length?-1:T(t,r,e,n,i);if(\"number\"==typeof r)return r&=255,y.TYPED_ARRAY_SUPPORT&&\"function\"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,r,e):Uint8Array.prototype.lastIndexOf.call(t,r,e):T(t,[r],e,n,i);throw new TypeError(\"val must be string, number or Buffer\")}function T(t,r,e,n,i){var o,u=1,f=t.length,s=r.length;if(void 0!==n&&(\"ucs2\"===(n=String(n).toLowerCase())||\"ucs-2\"===n||\"utf16le\"===n||\"utf-16le\"===n)){if(t.length<2||r.length<2)return-1;u=2,f/=2,s/=2,e/=2}function h(t,r){return 1===u?t[r]:t.readUInt16BE(r*u)}if(i){var a=-1;for(o=e;o<f;o++)if(h(t,o)===h(r,-1===a?0:o-a)){if(-1===a&&(a=o),o-a+1===s)return a*u}else-1!==a&&(o-=o-a),a=-1}else for(e+s>f&&(e=f-s),o=e;o>=0;o--){for(var c=!0,l=0;l<s;l++)if(h(t,o+l)!==h(r,l)){c=!1;break}if(c)return o}return-1}function U(t,r,e,n){e=Number(e)||0;var i=t.length-e;n?(n=Number(n))>i&&(n=i):n=i;var o=r.length;if(o%2!=0)throw new TypeError(\"Invalid hex string\");n>o/2&&(n=o/2);for(var u=0;u<n;++u){var f=parseInt(r.substr(2*u,2),16);if(isNaN(f))return u;t[e+u]=f}return u}function B(t,r,e,n){return W(K(r,t.length-e),t,e,n)}function S(t,r,e,n){return W(function(t){for(var r=[],e=0;e<t.length;++e)r.push(255&t.charCodeAt(e));return r}(r),t,e,n)}function Y(t,r,e,n){return S(t,r,e,n)}function I(t,r,e,n){return W(Q(r),t,e,n)}function C(t,r,e,n){return W(function(t,r){for(var e,n,i,o=[],u=0;u<t.length&&!((r-=2)<0);++u)n=(e=t.charCodeAt(u))>>8,i=e%256,o.push(i),o.push(n);return o}(r,t.length-e),t,e,n)}function O(t,r,e){return 0===r&&e===t.length?f(t):f(t.slice(r,e))}function L(t,r,e){e=Math.min(t.length,e);for(var n=[],i=r;i<e;){var o,u,f,s,h=t[i],a=null,c=h>239?4:h>223?3:h>191?2:1;if(i+c<=e)switch(c){case 1:h<128&&(a=h);break;case 2:128==(192&(o=t[i+1]))&&(s=(31&h)<<6|63&o)>127&&(a=s);break;case 3:o=t[i+1],u=t[i+2],128==(192&o)&&128==(192&u)&&(s=(15&h)<<12|(63&o)<<6|63&u)>2047&&(s<55296||s>57343)&&(a=s);break;case 4:o=t[i+1],u=t[i+2],f=t[i+3],128==(192&o)&&128==(192&u)&&128==(192&f)&&(s=(15&h)<<18|(63&o)<<12|(63&u)<<6|63&f)>65535&&s<1114112&&(a=s)}null===a?(a=65533,c=1):a>65535&&(a-=65536,n.push(a>>>10&1023|55296),a=56320|1023&a),n.push(a),i+=c}return function(t){var r=t.length;if(r<=M)return String.fromCharCode.apply(String,t);var e=\"\",n=0;for(;n<r;)e+=String.fromCharCode.apply(String,t.slice(n,n+=M));return e}(n)}y.TYPED_ARRAY_SUPPORT=void 0===_polyfill_node_global_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].TYPED_ARRAY_SUPPORT||_polyfill_node_global_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].TYPED_ARRAY_SUPPORT,p(),y.poolSize=8192,y._augment=function(t){return t.__proto__=y.prototype,t},y.from=function(t,r,e){return w(null,t,r,e)},y.TYPED_ARRAY_SUPPORT&&(y.prototype.__proto__=Uint8Array.prototype,y.__proto__=Uint8Array,\"undefined\"!=typeof Symbol&&Symbol.species&&y[Symbol.species]),y.alloc=function(t,r,e){return function(t,r,e,n){return d(r),r<=0?g(t,r):void 0!==e?\"string\"==typeof n?g(t,r).fill(e,n):g(t,r).fill(e):g(t,r)}(null,t,r,e)},y.allocUnsafe=function(t){return v(null,t)},y.allocUnsafeSlow=function(t){return v(null,t)},y.isBuffer=X,y.compare=function(t,r){if(!R(t)||!R(r))throw new TypeError(\"Arguments must be Buffers\");if(t===r)return 0;for(var e=t.length,n=r.length,i=0,o=Math.min(e,n);i<o;++i)if(t[i]!==r[i]){e=t[i],n=r[i];break}return e<n?-1:n<e?1:0},y.isEncoding=function(t){switch(String(t).toLowerCase()){case\"hex\":case\"utf8\":case\"utf-8\":case\"ascii\":case\"latin1\":case\"binary\":case\"base64\":case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return!0;default:return!1}},y.concat=function(t,r){if(!c(t))throw new TypeError('\"list\" argument must be an Array of Buffers');if(0===t.length)return y.alloc(0);var e;if(void 0===r)for(r=0,e=0;e<t.length;++e)r+=t[e].length;var n=y.allocUnsafe(r),i=0;for(e=0;e<t.length;++e){var o=t[e];if(!R(o))throw new TypeError('\"list\" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},y.byteLength=b,y.prototype._isBuffer=!0,y.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError(\"Buffer size must be a multiple of 16-bits\");for(var r=0;r<t;r+=2)_(this,r,r+1);return this},y.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError(\"Buffer size must be a multiple of 32-bits\");for(var r=0;r<t;r+=4)_(this,r,r+3),_(this,r+1,r+2);return this},y.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError(\"Buffer size must be a multiple of 64-bits\");for(var r=0;r<t;r+=8)_(this,r,r+7),_(this,r+1,r+6),_(this,r+2,r+5),_(this,r+3,r+4);return this},y.prototype.toString=function(){var t=0|this.length;return 0===t?\"\":0===arguments.length?L(this,0,t):m.apply(this,arguments)},y.prototype.equals=function(t){if(!R(t))throw new TypeError(\"Argument must be a Buffer\");return this===t||0===y.compare(this,t)},y.prototype.inspect=function(){var t=\"\";return this.length>0&&(t=this.toString(\"hex\",0,50).match(/.{2}/g).join(\" \"),this.length>50&&(t+=\" ... \")),\"<Buffer \"+t+\">\"},y.prototype.compare=function(t,r,e,n,i){if(!R(t))throw new TypeError(\"Argument must be a Buffer\");if(void 0===r&&(r=0),void 0===e&&(e=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),r<0||e>t.length||n<0||i>this.length)throw new RangeError(\"out of range index\");if(n>=i&&r>=e)return 0;if(n>=i)return-1;if(r>=e)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),u=(e>>>=0)-(r>>>=0),f=Math.min(o,u),s=this.slice(n,i),h=t.slice(r,e),a=0;a<f;++a)if(s[a]!==h[a]){o=s[a],u=h[a];break}return o<u?-1:u<o?1:0},y.prototype.includes=function(t,r,e){return-1!==this.indexOf(t,r,e)},y.prototype.indexOf=function(t,r,e){return P(this,t,r,e,!0)},y.prototype.lastIndexOf=function(t,r,e){return P(this,t,r,e,!1)},y.prototype.write=function(t,r,e,n){if(void 0===r)n=\"utf8\",e=this.length,r=0;else if(void 0===e&&\"string\"==typeof r)n=r,e=this.length,r=0;else{if(!isFinite(r))throw new Error(\"Buffer.write(string, encoding, offset[, length]) is no longer supported\");r|=0,isFinite(e)?(e|=0,void 0===n&&(n=\"utf8\")):(n=e,e=void 0)}var i=this.length-r;if((void 0===e||e>i)&&(e=i),t.length>0&&(e<0||r<0)||r>this.length)throw new RangeError(\"Attempt to write outside buffer bounds\");n||(n=\"utf8\");for(var o=!1;;)switch(n){case\"hex\":return U(this,t,r,e);case\"utf8\":case\"utf-8\":return B(this,t,r,e);case\"ascii\":return S(this,t,r,e);case\"latin1\":case\"binary\":return Y(this,t,r,e);case\"base64\":return I(this,t,r,e);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return C(this,t,r,e);default:if(o)throw new TypeError(\"Unknown encoding: \"+n);n=(\"\"+n).toLowerCase(),o=!0}},y.prototype.toJSON=function(){return{type:\"Buffer\",data:Array.prototype.slice.call(this._arr||this,0)}};var M=4096;function D(t,r,e){var n=\"\";e=Math.min(t.length,e);for(var i=r;i<e;++i)n+=String.fromCharCode(127&t[i]);return n}function x(t,r,e){var n=\"\";e=Math.min(t.length,e);for(var i=r;i<e;++i)n+=String.fromCharCode(t[i]);return n}function k(t,r,e){var n=t.length;(!r||r<0)&&(r=0),(!e||e<0||e>n)&&(e=n);for(var i=\"\",o=r;o<e;++o)i+=H(t[o]);return i}function N(t,r,e){for(var n=t.slice(r,e),i=\"\",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function z(t,r,e){if(t%1!=0||t<0)throw new RangeError(\"offset is not uint\");if(t+r>e)throw new RangeError(\"Trying to access beyond buffer length\")}function F(t,r,e,n,i,o){if(!R(t))throw new TypeError('\"buffer\" argument must be a Buffer instance');if(r>i||r<o)throw new RangeError('\"value\" argument is out of bounds');if(e+n>t.length)throw new RangeError(\"Index out of range\")}function j(t,r,e,n){r<0&&(r=65535+r+1);for(var i=0,o=Math.min(t.length-e,2);i<o;++i)t[e+i]=(r&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function V(t,r,e,n){r<0&&(r=4294967295+r+1);for(var i=0,o=Math.min(t.length-e,4);i<o;++i)t[e+i]=r>>>8*(n?i:3-i)&255}function q(t,r,e,n,i,o){if(e+n>t.length)throw new RangeError(\"Index out of range\");if(e<0)throw new RangeError(\"Index out of range\")}function J(t,r,e,n,i){return i||q(t,0,e,4),h(t,r,e,n,23,4),e+4}function Z(t,r,e,n,i){return i||q(t,0,e,8),h(t,r,e,n,52,8),e+8}y.prototype.slice=function(t,r){var e,n=this.length;if((t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(r=void 0===r?n:~~r)<0?(r+=n)<0&&(r=0):r>n&&(r=n),r<t&&(r=t),y.TYPED_ARRAY_SUPPORT)(e=this.subarray(t,r)).__proto__=y.prototype;else{var i=r-t;e=new y(i,void 0);for(var o=0;o<i;++o)e[o]=this[o+t]}return e},y.prototype.readUIntLE=function(t,r,e){t|=0,r|=0,e||z(t,r,this.length);for(var n=this[t],i=1,o=0;++o<r&&(i*=256);)n+=this[t+o]*i;return n},y.prototype.readUIntBE=function(t,r,e){t|=0,r|=0,e||z(t,r,this.length);for(var n=this[t+--r],i=1;r>0&&(i*=256);)n+=this[t+--r]*i;return n},y.prototype.readUInt8=function(t,r){return r||z(t,1,this.length),this[t]},y.prototype.readUInt16LE=function(t,r){return r||z(t,2,this.length),this[t]|this[t+1]<<8},y.prototype.readUInt16BE=function(t,r){return r||z(t,2,this.length),this[t]<<8|this[t+1]},y.prototype.readUInt32LE=function(t,r){return r||z(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},y.prototype.readUInt32BE=function(t,r){return r||z(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},y.prototype.readIntLE=function(t,r,e){t|=0,r|=0,e||z(t,r,this.length);for(var n=this[t],i=1,o=0;++o<r&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*r)),n},y.prototype.readIntBE=function(t,r,e){t|=0,r|=0,e||z(t,r,this.length);for(var n=r,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*r)),o},y.prototype.readInt8=function(t,r){return r||z(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},y.prototype.readInt16LE=function(t,r){r||z(t,2,this.length);var e=this[t]|this[t+1]<<8;return 32768&e?4294901760|e:e},y.prototype.readInt16BE=function(t,r){r||z(t,2,this.length);var e=this[t+1]|this[t]<<8;return 32768&e?4294901760|e:e},y.prototype.readInt32LE=function(t,r){return r||z(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},y.prototype.readInt32BE=function(t,r){return r||z(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},y.prototype.readFloatLE=function(t,r){return r||z(t,4,this.length),s(this,t,!0,23,4)},y.prototype.readFloatBE=function(t,r){return r||z(t,4,this.length),s(this,t,!1,23,4)},y.prototype.readDoubleLE=function(t,r){return r||z(t,8,this.length),s(this,t,!0,52,8)},y.prototype.readDoubleBE=function(t,r){return r||z(t,8,this.length),s(this,t,!1,52,8)},y.prototype.writeUIntLE=function(t,r,e,n){(t=+t,r|=0,e|=0,n)||F(this,t,r,e,Math.pow(2,8*e)-1,0);var i=1,o=0;for(this[r]=255&t;++o<e&&(i*=256);)this[r+o]=t/i&255;return r+e},y.prototype.writeUIntBE=function(t,r,e,n){(t=+t,r|=0,e|=0,n)||F(this,t,r,e,Math.pow(2,8*e)-1,0);var i=e-1,o=1;for(this[r+i]=255&t;--i>=0&&(o*=256);)this[r+i]=t/o&255;return r+e},y.prototype.writeUInt8=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,1,255,0),y.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[r]=255&t,r+1},y.prototype.writeUInt16LE=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,2,65535,0),y.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):j(this,t,r,!0),r+2},y.prototype.writeUInt16BE=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,2,65535,0),y.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):j(this,t,r,!1),r+2},y.prototype.writeUInt32LE=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,4,4294967295,0),y.TYPED_ARRAY_SUPPORT?(this[r+3]=t>>>24,this[r+2]=t>>>16,this[r+1]=t>>>8,this[r]=255&t):V(this,t,r,!0),r+4},y.prototype.writeUInt32BE=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,4,4294967295,0),y.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):V(this,t,r,!1),r+4},y.prototype.writeIntLE=function(t,r,e,n){if(t=+t,r|=0,!n){var i=Math.pow(2,8*e-1);F(this,t,r,e,i-1,-i)}var o=0,u=1,f=0;for(this[r]=255&t;++o<e&&(u*=256);)t<0&&0===f&&0!==this[r+o-1]&&(f=1),this[r+o]=(t/u|0)-f&255;return r+e},y.prototype.writeIntBE=function(t,r,e,n){if(t=+t,r|=0,!n){var i=Math.pow(2,8*e-1);F(this,t,r,e,i-1,-i)}var o=e-1,u=1,f=0;for(this[r+o]=255&t;--o>=0&&(u*=256);)t<0&&0===f&&0!==this[r+o+1]&&(f=1),this[r+o]=(t/u|0)-f&255;return r+e},y.prototype.writeInt8=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,1,127,-128),y.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[r]=255&t,r+1},y.prototype.writeInt16LE=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,2,32767,-32768),y.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):j(this,t,r,!0),r+2},y.prototype.writeInt16BE=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,2,32767,-32768),y.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):j(this,t,r,!1),r+2},y.prototype.writeInt32LE=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,4,2147483647,-2147483648),y.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8,this[r+2]=t>>>16,this[r+3]=t>>>24):V(this,t,r,!0),r+4},y.prototype.writeInt32BE=function(t,r,e){return t=+t,r|=0,e||F(this,t,r,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),y.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):V(this,t,r,!1),r+4},y.prototype.writeFloatLE=function(t,r,e){return J(this,t,r,!0,e)},y.prototype.writeFloatBE=function(t,r,e){return J(this,t,r,!1,e)},y.prototype.writeDoubleLE=function(t,r,e){return Z(this,t,r,!0,e)},y.prototype.writeDoubleBE=function(t,r,e){return Z(this,t,r,!1,e)},y.prototype.copy=function(t,r,e,n){if(e||(e=0),n||0===n||(n=this.length),r>=t.length&&(r=t.length),r||(r=0),n>0&&n<e&&(n=e),n===e)return 0;if(0===t.length||0===this.length)return 0;if(r<0)throw new RangeError(\"targetStart out of bounds\");if(e<0||e>=this.length)throw new RangeError(\"sourceStart out of bounds\");if(n<0)throw new RangeError(\"sourceEnd out of bounds\");n>this.length&&(n=this.length),t.length-r<n-e&&(n=t.length-r+e);var i,o=n-e;if(this===t&&e<r&&r<n)for(i=o-1;i>=0;--i)t[i+r]=this[i+e];else if(o<1e3||!y.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+r]=this[i+e];else Uint8Array.prototype.set.call(t,this.subarray(e,e+o),r);return o},y.prototype.fill=function(t,r,e,n){if(\"string\"==typeof t){if(\"string\"==typeof r?(n=r,r=0,e=this.length):\"string\"==typeof e&&(n=e,e=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==n&&\"string\"!=typeof n)throw new TypeError(\"encoding must be a string\");if(\"string\"==typeof n&&!y.isEncoding(n))throw new TypeError(\"Unknown encoding: \"+n)}else\"number\"==typeof t&&(t&=255);if(r<0||this.length<r||this.length<e)throw new RangeError(\"Out of range index\");if(e<=r)return this;var o;if(r>>>=0,e=void 0===e?this.length:e>>>0,t||(t=0),\"number\"==typeof t)for(o=r;o<e;++o)this[o]=t;else{var u=R(t)?t:K(new y(t,n).toString()),f=u.length;for(o=0;o<e-r;++o)this[o+r]=u[o%f]}return this};var G=/[^+\\/0-9A-Za-z-_]/g;function H(t){return t<16?\"0\"+t.toString(16):t.toString(16)}function K(t,r){var e;r=r||1/0;for(var n=t.length,i=null,o=[],u=0;u<n;++u){if((e=t.charCodeAt(u))>55295&&e<57344){if(!i){if(e>56319){(r-=3)>-1&&o.push(239,191,189);continue}if(u+1===n){(r-=3)>-1&&o.push(239,191,189);continue}i=e;continue}if(e<56320){(r-=3)>-1&&o.push(239,191,189),i=e;continue}e=65536+(i-55296<<10|e-56320)}else i&&(r-=3)>-1&&o.push(239,191,189);if(i=null,e<128){if((r-=1)<0)break;o.push(e)}else if(e<2048){if((r-=2)<0)break;o.push(e>>6|192,63&e|128)}else if(e<65536){if((r-=3)<0)break;o.push(e>>12|224,e>>6&63|128,63&e|128)}else{if(!(e<1114112))throw new Error(\"Invalid code point\");if((r-=4)<0)break;o.push(e>>18|240,e>>12&63|128,e>>6&63|128,63&e|128)}}return o}function Q(t){return function(t){var r,u,f,s,h,a;i||o();var c=t.length;if(c%4>0)throw new Error(\"Invalid string. Length must be a multiple of 4\");h=\"=\"===t[c-2]?2:\"=\"===t[c-1]?1:0,a=new n(3*c/4-h),f=h>0?c-4:c;var l=0;for(r=0,u=0;r<f;r+=4,u+=3)s=e[t.charCodeAt(r)]<<18|e[t.charCodeAt(r+1)]<<12|e[t.charCodeAt(r+2)]<<6|e[t.charCodeAt(r+3)],a[l++]=s>>16&255,a[l++]=s>>8&255,a[l++]=255&s;return 2===h?(s=e[t.charCodeAt(r)]<<2|e[t.charCodeAt(r+1)]>>4,a[l++]=255&s):1===h&&(s=e[t.charCodeAt(r)]<<10|e[t.charCodeAt(r+1)]<<4|e[t.charCodeAt(r+2)]>>2,a[l++]=s>>8&255,a[l++]=255&s),a}(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\\s+|\\s+$/g,\"\")}(t).replace(G,\"\")).length<2)return\"\";for(;t.length%4!=0;)t+=\"=\";return t}(t))}function W(t,r,e,n){for(var i=0;i<n&&!(i+e>=r.length||i>=t.length);++i)r[i+e]=t[i];return i}function X(t){return null!=t&&(!!t._isBuffer||$(t)||function(t){return\"function\"==typeof t.readFloatLE&&\"function\"==typeof t.slice&&$(t.slice(0,0))}(t))}function $(t){return!!t.constructor&&\"function\"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/_polyfill-node.buffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/_polyfill-node.global.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/_virtual/_polyfill-node.global.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ e)\n/* harmony export */ });\nvar e=\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9fdmlydHVhbC9fcG9seWZpbGwtbm9kZS5nbG9iYWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDJHQUFnSSIsInNvdXJjZXMiOlsid2VicGFjazovL3FoYXJtb255LXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9fdmlydHVhbC9fcG9seWZpbGwtbm9kZS5nbG9iYWwuanM/YWExNSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZT1cInVuZGVmaW5lZFwiIT10eXBlb2YgZ2xvYmFsP2dsb2JhbDpcInVuZGVmaW5lZFwiIT10eXBlb2Ygc2VsZj9zZWxmOlwidW5kZWZpbmVkXCIhPXR5cGVvZiB3aW5kb3c/d2luZG93Ont9O2V4cG9ydHtlIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/_polyfill-node.global.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/browser.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/_virtual/browser.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __module: () => (/* binding */ e)\n/* harmony export */ });\nvar e={exports:{}};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9fdmlydHVhbC9icm93c2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxPQUFPLFlBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL192aXJ0dWFsL2Jyb3dzZXIuanM/ZjE5MSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZT17ZXhwb3J0czp7fX07ZXhwb3J0e2UgYXMgX19tb2R1bGV9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __awaiter: () => (/* binding */ n),\n/* harmony export */   __rest: () => (/* binding */ t)\n/* harmony export */ });\nfunction t(t,n){var e={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.indexOf(r)<0&&(e[r]=t[r]);if(null!=t&&\"function\"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(e[r[o]]=t[r[o]])}return e}function n(t,n,e,r){return new(e||(e=Promise))((function(o,c){function p(t){try{u(r.next(t))}catch(t){c(t)}}function f(t){try{u(r.throw(t))}catch(t){c(t)}}function u(t){var n;t.done?o(t.value):(n=t.value,n instanceof e?n:new e((function(t){t(n)}))).then(p,f)}u((r=r.apply(t,n||[])).next())}))}\"function\"==typeof SuppressedError&&SuppressedError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vQHJvbGx1cF9wbHVnaW4tdHlwZXNjcmlwdEAxMS4xLjZfcm9sbHVwQDQuMjQuNF90c2xpYkAyLjYuM190eXBlc2NyaXB0QDQuOS41L25vZGVfbW9kdWxlcy90c2xpYi90c2xpYi5lczYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxnQkFBZ0IsU0FBUyxzRkFBc0YsNkRBQTZELFFBQVEsc0NBQXNDLFdBQVcsNkZBQTZGLFNBQVMsb0JBQW9CLDBDQUEwQyxjQUFjLElBQUksYUFBYSxTQUFTLE1BQU0sY0FBYyxJQUFJLGNBQWMsU0FBUyxNQUFNLGNBQWMsTUFBTSxpRUFBaUUsS0FBSyxjQUFjLCtCQUErQixHQUFHLG9EQUF1RiIsInNvdXJjZXMiOlsid2VicGFjazovL3FoYXJtb255LXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vQHJvbGx1cF9wbHVnaW4tdHlwZXNjcmlwdEAxMS4xLjZfcm9sbHVwQDQuMjQuNF90c2xpYkAyLjYuM190eXBlc2NyaXB0QDQuOS41L25vZGVfbW9kdWxlcy90c2xpYi90c2xpYi5lczYuanM/NGZhZSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiB0KHQsbil7dmFyIGU9e307Zm9yKHZhciByIGluIHQpT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHQscikmJm4uaW5kZXhPZihyKTwwJiYoZVtyXT10W3JdKTtpZihudWxsIT10JiZcImZ1bmN0aW9uXCI9PXR5cGVvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKXt2YXIgbz0wO2ZvcihyPU9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHModCk7bzxyLmxlbmd0aDtvKyspbi5pbmRleE9mKHJbb10pPDAmJk9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbCh0LHJbb10pJiYoZVtyW29dXT10W3Jbb11dKX1yZXR1cm4gZX1mdW5jdGlvbiBuKHQsbixlLHIpe3JldHVybiBuZXcoZXx8KGU9UHJvbWlzZSkpKChmdW5jdGlvbihvLGMpe2Z1bmN0aW9uIHAodCl7dHJ5e3Uoci5uZXh0KHQpKX1jYXRjaCh0KXtjKHQpfX1mdW5jdGlvbiBmKHQpe3RyeXt1KHIudGhyb3codCkpfWNhdGNoKHQpe2ModCl9fWZ1bmN0aW9uIHUodCl7dmFyIG47dC5kb25lP28odC52YWx1ZSk6KG49dC52YWx1ZSxuIGluc3RhbmNlb2YgZT9uOm5ldyBlKChmdW5jdGlvbih0KXt0KG4pfSkpKS50aGVuKHAsZil9dSgocj1yLmFwcGx5KHQsbnx8W10pKS5uZXh0KCkpfSkpfVwiZnVuY3Rpb25cIj09dHlwZW9mIFN1cHByZXNzZWRFcnJvciYmU3VwcHJlc3NlZEVycm9yO2V4cG9ydHtuIGFzIF9fYXdhaXRlcix0IGFzIF9fcmVzdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/browser.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/browser.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   b: () => (/* binding */ C)\n/* harmony export */ });\n/* harmony import */ var _virtual_browser_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../../_virtual/browser.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/browser.js\");\n/* harmony import */ var _common_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./common.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/common.js\");\n!function(e,C){C.formatArgs=function(o){if(o[0]=(this.useColors?\"%c\":\"\")+this.namespace+(this.useColors?\" %c\":\" \")+o[0]+(this.useColors?\"%c \":\" \")+\"+\"+e.exports.humanize(this.diff),!this.useColors)return;const C=\"color: \"+this.color;o.splice(1,0,C,\"color: inherit\");let t=0,n=0;o[0].replace(/%[a-zA-Z%]/g,(e=>{\"%%\"!==e&&(t++,\"%c\"===e&&(n=t))})),o.splice(n,0,C)},C.save=function(e){try{e?C.storage.setItem(\"debug\",e):C.storage.removeItem(\"debug\")}catch(e){}},C.load=function(){let e;try{e=C.storage.getItem(\"debug\")}catch(e){}!e&&\"undefined\"!=typeof process&&\"env\"in process&&(e=process.env.DEBUG);return e},C.useColors=function(){if(\"undefined\"!=typeof window&&window.process&&(\"renderer\"===window.process.type||window.process.__nwjs))return!0;if(\"undefined\"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/))return!1;return\"undefined\"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||\"undefined\"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||\"undefined\"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)&&parseInt(RegExp.$1,10)>=31||\"undefined\"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/)},C.storage=function(){try{return localStorage}catch(e){}}(),C.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\"))}})(),C.colors=[\"#0000CC\",\"#0000FF\",\"#0033CC\",\"#0033FF\",\"#0066CC\",\"#0066FF\",\"#0099CC\",\"#0099FF\",\"#00CC00\",\"#00CC33\",\"#00CC66\",\"#00CC99\",\"#00CCCC\",\"#00CCFF\",\"#3300CC\",\"#3300FF\",\"#3333CC\",\"#3333FF\",\"#3366CC\",\"#3366FF\",\"#3399CC\",\"#3399FF\",\"#33CC00\",\"#33CC33\",\"#33CC66\",\"#33CC99\",\"#33CCCC\",\"#33CCFF\",\"#6600CC\",\"#6600FF\",\"#6633CC\",\"#6633FF\",\"#66CC00\",\"#66CC33\",\"#9900CC\",\"#9900FF\",\"#9933CC\",\"#9933FF\",\"#99CC00\",\"#99CC33\",\"#CC0000\",\"#CC0033\",\"#CC0066\",\"#CC0099\",\"#CC00CC\",\"#CC00FF\",\"#CC3300\",\"#CC3333\",\"#CC3366\",\"#CC3399\",\"#CC33CC\",\"#CC33FF\",\"#CC6600\",\"#CC6633\",\"#CC9900\",\"#CC9933\",\"#CCCC00\",\"#CCCC33\",\"#FF0000\",\"#FF0033\",\"#FF0066\",\"#FF0099\",\"#FF00CC\",\"#FF00FF\",\"#FF3300\",\"#FF3333\",\"#FF3366\",\"#FF3399\",\"#FF33CC\",\"#FF33FF\",\"#FF6600\",\"#FF6633\",\"#FF9900\",\"#FF9933\",\"#FFCC00\",\"#FFCC33\"],C.log=console.debug||console.log||(()=>{}),e.exports=(0,_common_js__WEBPACK_IMPORTED_MODULE_0__.c)(C);const{formatters:t}=e.exports;t.j=function(e){try{return JSON.stringify(e)}catch(e){return\"[UnexpectedJSONParseError]: \"+e.message}}}(_virtual_browser_js__WEBPACK_IMPORTED_MODULE_1__.__module,_virtual_browser_js__WEBPACK_IMPORTED_MODULE_1__.__module.exports);var C=_virtual_browser_js__WEBPACK_IMPORTED_MODULE_1__.__module.exports;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/common.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/common.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   c: () => (/* binding */ n)\n/* harmony export */ });\n/* harmony import */ var _ms_2_1_2_node_modules_ms_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../ms@2.1.2/node_modules/ms/index.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/ms@2.1.2/node_modules/ms/index.js\");\nvar n=function(n){function t(e){let n,s,o,a=null;function l(...e){if(!l.enabled)return;const r=l,s=Number(new Date),o=s-(n||s);r.diff=o,r.prev=n,r.curr=s,n=s,e[0]=t.coerce(e[0]),\"string\"!=typeof e[0]&&e.unshift(\"%O\");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,s)=>{if(\"%%\"===n)return\"%\";a++;const o=t.formatters[s];if(\"function\"==typeof o){const t=e[a];n=o.call(r,t),e.splice(a,1),a--}return n})),t.formatArgs.call(r,e);(r.log||t.log).apply(r,e)}return l.namespace=e,l.useColors=t.useColors(),l.color=t.selectColor(e),l.extend=r,l.destroy=t.destroy,Object.defineProperty(l,\"enabled\",{enumerable:!0,configurable:!1,get:()=>null!==a?a:(s!==t.namespaces&&(s=t.namespaces,o=t.enabled(e)),o),set:e=>{a=e}}),\"function\"==typeof t.init&&t.init(l),l}function r(e,n){const r=t(this.namespace+(void 0===n?\":\":n)+e);return r.log=this.log,r}function s(e){return e.toString().substring(2,e.toString().length-2).replace(/\\.\\*\\?$/,\"*\")}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names.map(s),...t.skips.map(s).map((e=>\"-\"+e))].join(\",\");return t.enable(\"\"),e},t.enable=function(e){let n;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=(\"string\"==typeof e?e:\"\").split(/[\\s,]+/),s=r.length;for(n=0;n<s;n++)r[n]&&(\"-\"===(e=r[n].replace(/\\*/g,\".*?\"))[0]?t.skips.push(new RegExp(\"^\"+e.slice(1)+\"$\")):t.names.push(new RegExp(\"^\"+e+\"$\")))},t.enabled=function(e){if(\"*\"===e[e.length-1])return!0;let n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=(0,_ms_2_1_2_node_modules_ms_index_js__WEBPACK_IMPORTED_MODULE_0__.__require)(),t.destroy=function(){console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\")},Object.keys(n).forEach((e=>{t[e]=n[e]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vZGVidWdANC4zLjUvbm9kZV9tb2R1bGVzL2RlYnVnL3NyYy9jb21tb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEUsa0JBQWtCLGNBQWMsaUJBQWlCLGlCQUFpQixxQkFBcUIsd0NBQXdDLDBGQUEwRixRQUFRLDJDQUEyQyxzQkFBc0IsSUFBSSx3QkFBd0IseUJBQXlCLGFBQWEsZ0NBQWdDLFNBQVMsMEJBQTBCLDBCQUEwQiwwSUFBMEksK0dBQStHLEtBQUsseUNBQXlDLGdCQUFnQiwrQ0FBK0Msd0JBQXdCLGNBQWMsOEVBQThFLGtEQUFrRCxnREFBZ0QsU0FBUyxzQkFBc0Isd0VBQXdFLHNCQUFzQixzQkFBc0IsTUFBTSwrQ0FBK0MsNkRBQTZELFFBQVEsSUFBSSxvSUFBb0ksdUJBQXVCLGdDQUFnQyxRQUFRLHlCQUF5QixJQUFJLG1DQUFtQyx5QkFBeUIsSUFBSSxtQ0FBbUMsU0FBUyxZQUFZLDZFQUFDLHdCQUF3QixzSkFBc0osNkJBQTZCLFVBQVUsd0NBQXdDLDJCQUEyQixRQUFRLFlBQVksV0FBVyxvQ0FBb0MsNkNBQTZDLHVCQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL3FoYXJtb255LXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vZGVidWdANC4zLjUvbm9kZV9tb2R1bGVzL2RlYnVnL3NyYy9jb21tb24uanM/ZmU3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7X19yZXF1aXJlIGFzIGV9ZnJvbVwiLi4vLi4vLi4vLi4vbXNAMi4xLjIvbm9kZV9tb2R1bGVzL21zL2luZGV4LmpzXCI7dmFyIG49ZnVuY3Rpb24obil7ZnVuY3Rpb24gdChlKXtsZXQgbixzLG8sYT1udWxsO2Z1bmN0aW9uIGwoLi4uZSl7aWYoIWwuZW5hYmxlZClyZXR1cm47Y29uc3Qgcj1sLHM9TnVtYmVyKG5ldyBEYXRlKSxvPXMtKG58fHMpO3IuZGlmZj1vLHIucHJldj1uLHIuY3Vycj1zLG49cyxlWzBdPXQuY29lcmNlKGVbMF0pLFwic3RyaW5nXCIhPXR5cGVvZiBlWzBdJiZlLnVuc2hpZnQoXCIlT1wiKTtsZXQgYT0wO2VbMF09ZVswXS5yZXBsYWNlKC8lKFthLXpBLVolXSkvZywoKG4scyk9PntpZihcIiUlXCI9PT1uKXJldHVyblwiJVwiO2ErKztjb25zdCBvPXQuZm9ybWF0dGVyc1tzXTtpZihcImZ1bmN0aW9uXCI9PXR5cGVvZiBvKXtjb25zdCB0PWVbYV07bj1vLmNhbGwocix0KSxlLnNwbGljZShhLDEpLGEtLX1yZXR1cm4gbn0pKSx0LmZvcm1hdEFyZ3MuY2FsbChyLGUpOyhyLmxvZ3x8dC5sb2cpLmFwcGx5KHIsZSl9cmV0dXJuIGwubmFtZXNwYWNlPWUsbC51c2VDb2xvcnM9dC51c2VDb2xvcnMoKSxsLmNvbG9yPXQuc2VsZWN0Q29sb3IoZSksbC5leHRlbmQ9cixsLmRlc3Ryb3k9dC5kZXN0cm95LE9iamVjdC5kZWZpbmVQcm9wZXJ0eShsLFwiZW5hYmxlZFwiLHtlbnVtZXJhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMSxnZXQ6KCk9Pm51bGwhPT1hP2E6KHMhPT10Lm5hbWVzcGFjZXMmJihzPXQubmFtZXNwYWNlcyxvPXQuZW5hYmxlZChlKSksbyksc2V0OmU9PnthPWV9fSksXCJmdW5jdGlvblwiPT10eXBlb2YgdC5pbml0JiZ0LmluaXQobCksbH1mdW5jdGlvbiByKGUsbil7Y29uc3Qgcj10KHRoaXMubmFtZXNwYWNlKyh2b2lkIDA9PT1uP1wiOlwiOm4pK2UpO3JldHVybiByLmxvZz10aGlzLmxvZyxyfWZ1bmN0aW9uIHMoZSl7cmV0dXJuIGUudG9TdHJpbmcoKS5zdWJzdHJpbmcoMixlLnRvU3RyaW5nKCkubGVuZ3RoLTIpLnJlcGxhY2UoL1xcLlxcKlxcPyQvLFwiKlwiKX1yZXR1cm4gdC5kZWJ1Zz10LHQuZGVmYXVsdD10LHQuY29lcmNlPWZ1bmN0aW9uKGUpe2lmKGUgaW5zdGFuY2VvZiBFcnJvcilyZXR1cm4gZS5zdGFja3x8ZS5tZXNzYWdlO3JldHVybiBlfSx0LmRpc2FibGU9ZnVuY3Rpb24oKXtjb25zdCBlPVsuLi50Lm5hbWVzLm1hcChzKSwuLi50LnNraXBzLm1hcChzKS5tYXAoKGU9PlwiLVwiK2UpKV0uam9pbihcIixcIik7cmV0dXJuIHQuZW5hYmxlKFwiXCIpLGV9LHQuZW5hYmxlPWZ1bmN0aW9uKGUpe2xldCBuO3Quc2F2ZShlKSx0Lm5hbWVzcGFjZXM9ZSx0Lm5hbWVzPVtdLHQuc2tpcHM9W107Y29uc3Qgcj0oXCJzdHJpbmdcIj09dHlwZW9mIGU/ZTpcIlwiKS5zcGxpdCgvW1xccyxdKy8pLHM9ci5sZW5ndGg7Zm9yKG49MDtuPHM7bisrKXJbbl0mJihcIi1cIj09PShlPXJbbl0ucmVwbGFjZSgvXFwqL2csXCIuKj9cIikpWzBdP3Quc2tpcHMucHVzaChuZXcgUmVnRXhwKFwiXlwiK2Uuc2xpY2UoMSkrXCIkXCIpKTp0Lm5hbWVzLnB1c2gobmV3IFJlZ0V4cChcIl5cIitlK1wiJFwiKSkpfSx0LmVuYWJsZWQ9ZnVuY3Rpb24oZSl7aWYoXCIqXCI9PT1lW2UubGVuZ3RoLTFdKXJldHVybiEwO2xldCBuLHI7Zm9yKG49MCxyPXQuc2tpcHMubGVuZ3RoO248cjtuKyspaWYodC5za2lwc1tuXS50ZXN0KGUpKXJldHVybiExO2ZvcihuPTAscj10Lm5hbWVzLmxlbmd0aDtuPHI7bisrKWlmKHQubmFtZXNbbl0udGVzdChlKSlyZXR1cm4hMDtyZXR1cm4hMX0sdC5odW1hbml6ZT1lKCksdC5kZXN0cm95PWZ1bmN0aW9uKCl7Y29uc29sZS53YXJuKFwiSW5zdGFuY2UgbWV0aG9kIGBkZWJ1Zy5kZXN0cm95KClgIGlzIGRlcHJlY2F0ZWQgYW5kIG5vIGxvbmdlciBkb2VzIGFueXRoaW5nLiBJdCB3aWxsIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbiBvZiBgZGVidWdgLlwiKX0sT2JqZWN0LmtleXMobikuZm9yRWFjaCgoZT0+e3RbZV09bltlXX0pKSx0Lm5hbWVzPVtdLHQuc2tpcHM9W10sdC5mb3JtYXR0ZXJzPXt9LHQuc2VsZWN0Q29sb3I9ZnVuY3Rpb24oZSl7bGV0IG49MDtmb3IobGV0IHQ9MDt0PGUubGVuZ3RoO3QrKyluPShuPDw1KS1uK2UuY2hhckNvZGVBdCh0KSxufD0wO3JldHVybiB0LmNvbG9yc1tNYXRoLmFicyhuKSV0LmNvbG9ycy5sZW5ndGhdfSx0LmVuYWJsZSh0LmxvYWQoKSksdH07ZXhwb3J0e24gYXMgY307XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/ms@2.1.2/node_modules/ms/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/ms@2.1.2/node_modules/ms/index.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __require: () => (/* binding */ s)\n/* harmony export */ });\nvar e,r;function s(){if(r)return e;r=1;var s=1e3,n=60*s,a=60*n,t=24*a,c=7*t,u=365.25*t;function i(e,r,s,n){var a=r>=1.5*s;return Math.round(e/s)+\" \"+n+(a?\"s\":\"\")}return e=function(e,r){r=r||{};var o=typeof e;if(\"string\"===o&&e.length>0)return function(e){if((e=String(e)).length>100)return;var r=/^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!r)return;var i=parseFloat(r[1]);switch((r[2]||\"ms\").toLowerCase()){case\"years\":case\"year\":case\"yrs\":case\"yr\":case\"y\":return i*u;case\"weeks\":case\"week\":case\"w\":return i*c;case\"days\":case\"day\":case\"d\":return i*t;case\"hours\":case\"hour\":case\"hrs\":case\"hr\":case\"h\":return i*a;case\"minutes\":case\"minute\":case\"mins\":case\"min\":case\"m\":return i*n;case\"seconds\":case\"second\":case\"secs\":case\"sec\":case\"s\":return i*s;case\"milliseconds\":case\"millisecond\":case\"msecs\":case\"msec\":case\"ms\":return i;default:return}}(e);if(\"number\"===o&&isFinite(e))return r.long?function(e){var r=Math.abs(e);if(r>=t)return i(e,r,t,\"day\");if(r>=a)return i(e,r,a,\"hour\");if(r>=n)return i(e,r,n,\"minute\");if(r>=s)return i(e,r,s,\"second\");return e+\" ms\"}(e):function(e){var r=Math.abs(e);if(r>=t)return Math.round(e/t)+\"d\";if(r>=a)return Math.round(e/a)+\"h\";if(r>=n)return Math.round(e/n)+\"m\";if(r>=s)return Math.round(e/s)+\"s\";return e+\"ms\"}(e);throw new Error(\"val is not a non-empty string or a valid number. val=\"+JSON.stringify(e))}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/ms@2.1.2/node_modules/ms/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/native.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/native.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\nvar o={randomUUID:\"undefined\"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vdXVpZEA5LjAuMS9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL25hdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTywwRkFBK0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vbm9kZV9tb2R1bGVzLy5wbnBtL3V1aWRAOS4wLjEvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tYnJvd3Nlci9uYXRpdmUuanM/MjMyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbz17cmFuZG9tVVVJRDpcInVuZGVmaW5lZFwiIT10eXBlb2YgY3J5cHRvJiZjcnlwdG8ucmFuZG9tVVVJRCYmY3J5cHRvLnJhbmRvbVVVSUQuYmluZChjcnlwdG8pfTtleHBvcnR7byBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/native.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/regex.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/regex.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ a)\n/* harmony export */ });\nvar a=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vdXVpZEA5LjAuMS9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JlZ2V4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxvQkFBb0IsRUFBRSxVQUFVLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixFQUFFLFVBQVUsR0FBRywwQ0FBK0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vbm9kZV9tb2R1bGVzLy5wbnBtL3V1aWRAOS4wLjEvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tYnJvd3Nlci9yZWdleC5qcz9jZTE4Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBhPS9eKD86WzAtOWEtZl17OH0tWzAtOWEtZl17NH0tWzEtNV1bMC05YS1mXXszfS1bODlhYl1bMC05YS1mXXszfS1bMC05YS1mXXsxMn18MDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAwKSQvaTtleHBvcnR7YSBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/rng.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/rng.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\nlet t;const e=new Uint8Array(16);function o(){if(!t&&(t=\"undefined\"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!t))throw new Error(\"crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported\");return t(e)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vdXVpZEA5LjAuMS9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JuZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTSwyQkFBMkIsYUFBYSxrT0FBa08sWUFBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vbm9kZV9tb2R1bGVzLy5wbnBtL3V1aWRAOS4wLjEvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tYnJvd3Nlci9ybmcuanM/ZDY5NyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgdDtjb25zdCBlPW5ldyBVaW50OEFycmF5KDE2KTtmdW5jdGlvbiBvKCl7aWYoIXQmJih0PVwidW5kZWZpbmVkXCIhPXR5cGVvZiBjcnlwdG8mJmNyeXB0by5nZXRSYW5kb21WYWx1ZXMmJmNyeXB0by5nZXRSYW5kb21WYWx1ZXMuYmluZChjcnlwdG8pLCF0KSl0aHJvdyBuZXcgRXJyb3IoXCJjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKCkgbm90IHN1cHBvcnRlZC4gU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS91dWlkanMvdXVpZCNnZXRyYW5kb212YWx1ZXMtbm90LXN1cHBvcnRlZFwiKTtyZXR1cm4gdChlKX1leHBvcnR7byBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/rng.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/stringify.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/stringify.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unsafeStringify: () => (/* binding */ n)\n/* harmony export */ });\nconst t=[];for(let n=0;n<256;++n)t.push((n+256).toString(16).slice(1));function n(n,o=0){return t[n[o+0]]+t[n[o+1]]+t[n[o+2]]+t[n[o+3]]+\"-\"+t[n[o+4]]+t[n[o+5]]+\"-\"+t[n[o+6]]+t[n[o+7]]+\"-\"+t[n[o+8]]+t[n[o+9]]+\"-\"+t[n[o+10]]+t[n[o+11]]+t[n[o+12]]+t[n[o+13]]+t[n[o+14]]+t[n[o+15]]}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vdXVpZEA5LjAuMS9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3N0cmluZ2lmeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsV0FBVyxZQUFZLE1BQU0sMENBQTBDLGtCQUFrQiw2TEFBME4iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vbm9kZV9tb2R1bGVzLy5wbnBtL3V1aWRAOS4wLjEvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tYnJvd3Nlci9zdHJpbmdpZnkuanM/Yzc1MSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB0PVtdO2ZvcihsZXQgbj0wO248MjU2Oysrbil0LnB1c2goKG4rMjU2KS50b1N0cmluZygxNikuc2xpY2UoMSkpO2Z1bmN0aW9uIG4obixvPTApe3JldHVybiB0W25bbyswXV0rdFtuW28rMV1dK3RbbltvKzJdXSt0W25bbyszXV0rXCItXCIrdFtuW28rNF1dK3RbbltvKzVdXStcIi1cIit0W25bbys2XV0rdFtuW28rN11dK1wiLVwiK3RbbltvKzhdXSt0W25bbys5XV0rXCItXCIrdFtuW28rMTBdXSt0W25bbysxMV1dK3RbbltvKzEyXV0rdFtuW28rMTNdXSt0W25bbysxNF1dK3RbbltvKzE1XV19ZXhwb3J0e24gYXMgdW5zYWZlU3RyaW5naWZ5fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/stringify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/v4.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/v4.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/stringify.js\");\nfunction t(t,m,i){if(_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID&&!m&&!t)return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();const f=(t=t||{}).random||(t.rng||_rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();return f[6]=15&f[6]|64,f[8]=63&f[8]|128,(0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(f)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vdXVpZEA5LjAuMS9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0csa0JBQWtCLEdBQUcsa0RBQUMsMkJBQTJCLGtEQUFDLGNBQWMsZ0JBQWdCLGtCQUFrQiwrQ0FBQyxJQUFJLHdDQUF3Qyw4REFBQyxJQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3FoYXJtb255LXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vdXVpZEA5LjAuMS9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzP2Y2MmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHIgZnJvbVwiLi9uYXRpdmUuanNcIjtpbXBvcnQgbiBmcm9tXCIuL3JuZy5qc1wiO2ltcG9ydHt1bnNhZmVTdHJpbmdpZnkgYXMgb31mcm9tXCIuL3N0cmluZ2lmeS5qc1wiO2Z1bmN0aW9uIHQodCxtLGkpe2lmKHIucmFuZG9tVVVJRCYmIW0mJiF0KXJldHVybiByLnJhbmRvbVVVSUQoKTtjb25zdCBmPSh0PXR8fHt9KS5yYW5kb218fCh0LnJuZ3x8bikoKTtyZXR1cm4gZls2XT0xNSZmWzZdfDY0LGZbOF09NjMmZls4XXwxMjgsbyhmKX1leHBvcnR7dCBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/v4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/validate.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/validate.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ e)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/regex.js\");\nfunction e(e){return\"string\"==typeof e&&_regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(e)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9ub2RlX21vZHVsZXMvLnBucG0vdXVpZEA5LjAuMS9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3ZhbGlkYXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBCLGNBQWMsMEJBQTBCLGlEQUFDLFNBQThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL25vZGVfbW9kdWxlcy8ucG5wbS91dWlkQDkuMC4xL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanM/NzBiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdCBmcm9tXCIuL3JlZ2V4LmpzXCI7ZnVuY3Rpb24gZShlKXtyZXR1cm5cInN0cmluZ1wiPT10eXBlb2YgZSYmdC50ZXN0KGUpfWV4cG9ydHtlIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/validate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/artifactsForCDN/validDomains.json.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/artifactsForCDN/validDomains.json.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ c),\n/* harmony export */   validOrigins: () => (/* binding */ o)\n/* harmony export */ });\nvar o=[\"teams.microsoft.com\",\"teams.microsoft.us\",\"gov.teams.microsoft.us\",\"dod.teams.microsoft.us\",\"int.teams.microsoft.com\",\"outlook.office.com\",\"outlook-sdf.office.com\",\"outlook.office365.com\",\"outlook-sdf.office365.com\",\"outlook.live.com\",\"outlook-sdf.live.com\",\"teams.live.com\",\"local.teams.live.com\",\"local.teams.live.com:8080\",\"local.teams.office.com\",\"local.teams.office.com:8080\",\"devspaces.skype.com\",\"*.www.office.com\",\"www.office.com\",\"word.office.com\",\"excel.office.com\",\"powerpoint.office.com\",\"www.officeppe.com\",\"*.www.microsoft365.com\",\"www.microsoft365.com\",\"bing.com\",\"edgeservices.bing.com\",\"work.bing.com\",\"www.bing.com\",\"www.staging-bing-int.com\",\"*.cloud.microsoft\",\"*.m365.cloud.microsoft\",\"chatuxmanager.svc.cloud.microsoft\",\"copilot.microsoft.com\",\"windows.msn.com\",\"fa000000125.resources.office.net\",\"fa000000129.resources.office.net\",\"fa000000124.resources.office.net\",\"fa000000128.resources.office.net\",\"fa000000136.resources.office.net\"],c={validOrigins:o};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvYXJ0aWZhY3RzRm9yQ0ROL3ZhbGlkRG9tYWlucy5qc29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsMjhCQUEyOEIsZ0JBQXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9hcnRpZmFjdHNGb3JDRE4vdmFsaWREb21haW5zLmpzb24uanM/ZTg2NCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbz1bXCJ0ZWFtcy5taWNyb3NvZnQuY29tXCIsXCJ0ZWFtcy5taWNyb3NvZnQudXNcIixcImdvdi50ZWFtcy5taWNyb3NvZnQudXNcIixcImRvZC50ZWFtcy5taWNyb3NvZnQudXNcIixcImludC50ZWFtcy5taWNyb3NvZnQuY29tXCIsXCJvdXRsb29rLm9mZmljZS5jb21cIixcIm91dGxvb2stc2RmLm9mZmljZS5jb21cIixcIm91dGxvb2sub2ZmaWNlMzY1LmNvbVwiLFwib3V0bG9vay1zZGYub2ZmaWNlMzY1LmNvbVwiLFwib3V0bG9vay5saXZlLmNvbVwiLFwib3V0bG9vay1zZGYubGl2ZS5jb21cIixcInRlYW1zLmxpdmUuY29tXCIsXCJsb2NhbC50ZWFtcy5saXZlLmNvbVwiLFwibG9jYWwudGVhbXMubGl2ZS5jb206ODA4MFwiLFwibG9jYWwudGVhbXMub2ZmaWNlLmNvbVwiLFwibG9jYWwudGVhbXMub2ZmaWNlLmNvbTo4MDgwXCIsXCJkZXZzcGFjZXMuc2t5cGUuY29tXCIsXCIqLnd3dy5vZmZpY2UuY29tXCIsXCJ3d3cub2ZmaWNlLmNvbVwiLFwid29yZC5vZmZpY2UuY29tXCIsXCJleGNlbC5vZmZpY2UuY29tXCIsXCJwb3dlcnBvaW50Lm9mZmljZS5jb21cIixcInd3dy5vZmZpY2VwcGUuY29tXCIsXCIqLnd3dy5taWNyb3NvZnQzNjUuY29tXCIsXCJ3d3cubWljcm9zb2Z0MzY1LmNvbVwiLFwiYmluZy5jb21cIixcImVkZ2VzZXJ2aWNlcy5iaW5nLmNvbVwiLFwid29yay5iaW5nLmNvbVwiLFwid3d3LmJpbmcuY29tXCIsXCJ3d3cuc3RhZ2luZy1iaW5nLWludC5jb21cIixcIiouY2xvdWQubWljcm9zb2Z0XCIsXCIqLm0zNjUuY2xvdWQubWljcm9zb2Z0XCIsXCJjaGF0dXhtYW5hZ2VyLnN2Yy5jbG91ZC5taWNyb3NvZnRcIixcImNvcGlsb3QubWljcm9zb2Z0LmNvbVwiLFwid2luZG93cy5tc24uY29tXCIsXCJmYTAwMDAwMDEyNS5yZXNvdXJjZXMub2ZmaWNlLm5ldFwiLFwiZmEwMDAwMDAxMjkucmVzb3VyY2VzLm9mZmljZS5uZXRcIixcImZhMDAwMDAwMTI0LnJlc291cmNlcy5vZmZpY2UubmV0XCIsXCJmYTAwMDAwMDEyOC5yZXNvdXJjZXMub2ZmaWNlLm5ldFwiLFwiZmEwMDAwMDAxMzYucmVzb3VyY2VzLm9mZmljZS5uZXRcIl0sYz17dmFsaWRPcmlnaW5zOm99O2V4cG9ydHtjIGFzIGRlZmF1bHQsbyBhcyB2YWxpZE9yaWdpbnN9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/artifactsForCDN/validDomains.json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/appHelpers.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/appHelpers.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appInitializeHelper: () => (/* binding */ _),\n/* harmony export */   callNotifySuccessInHost: () => (/* binding */ R),\n/* harmony export */   notifyAppLoadedHelper: () => (/* binding */ k),\n/* harmony export */   notifyExpectedFailureHelper: () => (/* binding */ A),\n/* harmony export */   notifyFailureHelper: () => (/* binding */ F),\n/* harmony export */   notifySuccessHelper: () => (/* binding */ N),\n/* harmony export */   openLinkHelper: () => (/* binding */ L),\n/* harmony export */   registerOnThemeChangeHandlerHelper: () => (/* binding */ J)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _communication_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/constants.js\");\n/* harmony import */ var _globalVars_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globalVars.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\");\n/* harmony import */ var _handlers_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internalAPIs_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./typeCheckUtilities.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/typeCheckUtilities.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\n/* harmony import */ var _public_app_app_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../public/app/app.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/app/app.js\");\n/* harmony import */ var _public_constants_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../public/constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _public_dialog_dialog_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../public/dialog/dialog.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/dialog.js\");\n/* harmony import */ var _public_menus_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/menus.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/menus.js\");\n/* harmony import */ var _public_runtime_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _public_version_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../public/version.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/version.js\");\n/* harmony import */ var _responseHandler_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./responseHandler.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/responseHandler.js\");\n/* harmony import */ var _public_pages_config_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/pages/config.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/config.js\");\nconst K=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"app\");function _(i,e){if((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.inServerSideRenderingEnvironment)()){return K.extend(\"initialize\")(\"window object undefined at initialization\"),Promise.resolve()}return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.runWithTimeout)((()=>function(i,e){return new Promise((n=>{_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializeCalled||(_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializeCalled=!0,(0,_handlers_js__WEBPACK_IMPORTED_MODULE_3__.initializeHandlers)(),_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializePromise=(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.initializeCommunication)(e,i).then((({context:i,clientType:e,runtimeConfig:n,clientSupportedSDKVersion:t=_constants_js__WEBPACK_IMPORTED_MODULE_5__.defaultSDKVersionForCompatCheck})=>{_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.frameContext=i,_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.hostClientType=e,_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.clientSupportedSDKVersion=t;try{H(\"Parsing %s\",n);const i=JSON.parse(n);if(H(\"Checking if %o is a valid runtime object\",null!=i?i:\"null\"),!i||!i.apiVersion)throw new Error(\"Received runtime config is invalid\");n&&(0,_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.applyRuntimeConfig)(i)}catch(i){if(!(i instanceof SyntaxError))throw i;try{H(\"Attempting to parse %s as an SDK version\",n),isNaN((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.compareSDKVersions)(n,_constants_js__WEBPACK_IMPORTED_MODULE_5__.defaultSDKVersionForCompatCheck))||(_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.clientSupportedSDKVersion=n);const i=JSON.parse(t);if(H(\"givenRuntimeConfig parsed to %o\",null!=i?i:\"null\"),!i)throw new Error(\"givenRuntimeConfig string was successfully parsed. However, it parsed to value of null\");(0,_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.applyRuntimeConfig)(i)}catch(i){if(!(i instanceof SyntaxError))throw i;(0,_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.applyRuntimeConfig)((0,_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.generateVersionBasedTeamsRuntimeConfig)(_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.clientSupportedSDKVersion,_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.versionAndPlatformAgnosticTeamsRuntimeConfig,_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.mapTeamsVersionToSupportedCapabilities))}}_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializeCompleted=!0})),(0,_public_menus_js__WEBPACK_IMPORTED_MODULE_7__.initialize)(),(0,_public_pages_config_js__WEBPACK_IMPORTED_MODULE_8__.initialize)(),(0,_public_dialog_dialog_js__WEBPACK_IMPORTED_MODULE_9__.initialize)()),Array.isArray(e)&&(0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_10__.processAdditionalValidOrigins)(e),void 0!==_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializePromise?n(_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializePromise):H(\"GlobalVars.initializePromise is unexpectedly undefined\")}))}(i,e)),6e4,new Error(\"SDK initialization timed out.\"))}function k(i){(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(i,_public_app_app_js__WEBPACK_IMPORTED_MODULE_11__.Messages.AppLoaded,[_public_version_js__WEBPACK_IMPORTED_MODULE_12__.version])}function A(i,n){(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(i,_public_app_app_js__WEBPACK_IMPORTED_MODULE_11__.Messages.ExpectedFailure,[n.reason,n.message])}function F(i,n){(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(i,_public_app_app_js__WEBPACK_IMPORTED_MODULE_11__.Messages.Failure,[n.reason,n.message])}function N(e){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_13__.__awaiter)(this,void 0,void 0,(function*(){if(_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializeCompleted)return R(e);if(!_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializePromise)throw new Error(_constants_js__WEBPACK_IMPORTED_MODULE_5__.errorLibraryNotInitialized);return _globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializePromise.then((()=>R(e)))}))}function R(n){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_13__.__awaiter)(this,void 0,void 0,(function*(){return (0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_10__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.runtime)&&(null===(i=_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.runtime.supports.app)||void 0===i?void 0:i.notifySuccessResponse)?(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.callFunctionInHostAndHandleResponse)(_public_app_app_js__WEBPACK_IMPORTED_MODULE_11__.Messages.Success,[_public_version_js__WEBPACK_IMPORTED_MODULE_12__.version],new _responseHandler_js__WEBPACK_IMPORTED_MODULE_14__.SimpleTypeResponseHandler,n).then((()=>({hasFinishedSuccessfully:!0}))):((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(n,_public_app_app_js__WEBPACK_IMPORTED_MODULE_11__.Messages.Success,[_public_version_js__WEBPACK_IMPORTED_MODULE_12__.version]),{hasFinishedSuccessfully:\"unknown\"});var i}))}const H=K.extend(\"initializeHelper\");function J(i,e){!(0,_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_15__.isNullOrUndefined)(e)&&(0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_10__.ensureInitializeCalled)(),(0,_handlers_js__WEBPACK_IMPORTED_MODULE_3__.registerOnThemeChangeHandler)(i,e)}function L(i,e){return new Promise((t=>{(0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_10__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_6__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_16__.FrameContexts.content,_public_constants_js__WEBPACK_IMPORTED_MODULE_16__.FrameContexts.sidePanel,_public_constants_js__WEBPACK_IMPORTED_MODULE_16__.FrameContexts.settings,_public_constants_js__WEBPACK_IMPORTED_MODULE_16__.FrameContexts.task,_public_constants_js__WEBPACK_IMPORTED_MODULE_16__.FrameContexts.stage,_public_constants_js__WEBPACK_IMPORTED_MODULE_16__.FrameContexts.meetingStage),t((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndHandleStatusAndReason)(i,\"executeDeepLink\",e))}))}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/appHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/childCommunication.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/childCommunication.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleIncomingMessageFromChild: () => (/* binding */ w),\n/* harmony export */   sendMessageEventToChild: () => (/* binding */ v),\n/* harmony export */   shouldEventBeRelayedToChild: () => (/* binding */ m),\n/* harmony export */   shouldProcessChildMessage: () => (/* binding */ f),\n/* harmony export */   uninitializeChildCommunication: () => (/* binding */ g)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _public_featureFlags_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../public/featureFlags.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/featureFlags.js\");\n/* harmony import */ var _communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communicationUtils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communicationUtils.js\");\n/* harmony import */ var _handlers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _messageObjects_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./messageObjects.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/messageObjects.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\nconst c=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"childProxyingCommunication\");class l{}function g(){l.window=null,l.origin=null,l.messageQueue=[]}function m(){return!!(0,_public_featureFlags_js__WEBPACK_IMPORTED_MODULE_1__.isChildProxyingEnabled)()&&!!l.window}function f(n,o){return!!(0,_public_featureFlags_js__WEBPACK_IMPORTED_MODULE_1__.isChildProxyingEnabled)()&&(l.window&&!l.window.closed&&n!==l.window||(l.window=n,l.origin=o),l.window&&l.window.closed?(l.window=null,l.origin=null,!1):l.window===n)}function w(i,d,a,c){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_2__.__awaiter)(this,void 0,void 0,(function*(){l.window===d&&((0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.flushMessageQueue)(l.window,l.origin,l.messageQueue,\"child\"),function(n,i,s){if(void 0===n.data.id||void 0===n.data.func)return;const d=(0,_messageObjects_js__WEBPACK_IMPORTED_MODULE_4__.deserializeMessageRequest)(n.data),[a,c]=(0,_handlers_js__WEBPACK_IMPORTED_MODULE_5__.callHandler)(d.func,d.args);if(a&&void 0!==c)return p(\"Handler called in response to message %s from child. Returning response from handler to child, action: %s.\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(d),d.func),void h(d.id,d.uuid,Array.isArray(c)?c:[c]);p(\"No handler for message %s from child found; relaying message on to parent, action: %s. Relayed message will have a new id.\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(d),d.func),function(n,i,s){const r=i((0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getApiVersionTag)(\"v2\",\"tasks.startTask\"),n.func,n.args,!0,n.teamsJsInstanceId),t=l.origin;s(r.uuid,((...i)=>{if(!l.window)return;if(!(0,_public_featureFlags_js__WEBPACK_IMPORTED_MODULE_1__.getCurrentFeatureFlagsState)().disableEnforceOriginMatchForChildResponses&&t!==l.origin)return void p(\"Origin of child window has changed, not sending response back to child window\");const s=i.pop();p(\"Message from parent being relayed to child, id: %s\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(n)),h(n.id,n.uuid,i,s)}))}(d,i,s)}(i,a,c))}))}l.messageQueue=[];const p=c.extend(\"handleIncomingMessageFromChild\");function h(n,i,o,s){const r=l.window,t=function(n,i,o,s){return{id:n,uuid:i,args:o||[],isPartialResponse:s}}(n,i,o,s),a=(0,_messageObjects_js__WEBPACK_IMPORTED_MODULE_4__.serializeMessageResponse)(t),u=l.origin;r&&u&&(p(\"Sending message %s to %s via postMessage, args = %o\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(a),\"child\",a.args),r.postMessage(a,u))}function v(n,i){const o=l.window,s=function(n,i){return{func:n,args:i||[]}}(n,i),e=l.origin;o&&e?o.postMessage(s,e):l.messageQueue.push(s)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/childCommunication.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Communication: () => (/* binding */ I),\n/* harmony export */   callFunctionInHost: () => (/* binding */ A),\n/* harmony export */   callFunctionInHostAndHandleResponse: () => (/* binding */ N),\n/* harmony export */   initializeCommunication: () => (/* binding */ E),\n/* harmony export */   requestPortFromParentWithVersion: () => (/* binding */ U),\n/* harmony export */   sendAndHandleSdkError: () => (/* binding */ P),\n/* harmony export */   sendAndHandleStatusAndReason: () => (/* binding */ R),\n/* harmony export */   sendAndHandleStatusAndReasonWithDefaultError: () => (/* binding */ C),\n/* harmony export */   sendAndUnwrap: () => (/* binding */ O),\n/* harmony export */   sendMessageToParent: () => (/* binding */ $),\n/* harmony export */   sendMessageToParentAsync: () => (/* binding */ S),\n/* harmony export */   sendNestedAuthRequestToTopWindow: () => (/* binding */ L),\n/* harmony export */   uninitializeCommunication: () => (/* binding */ j),\n/* harmony export */   waitForMessageQueue: () => (/* binding */ ne)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _public_interfaces_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../public/interfaces.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/interfaces.js\");\n/* harmony import */ var _public_runtime_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _public_serializable_interface_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../public/serializable.interface.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/serializable.interface.js\");\n/* harmony import */ var _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../public/uuidObject.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/uuidObject.js\");\n/* harmony import */ var _public_version_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/version.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/version.js\");\n/* harmony import */ var _childCommunication_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./childCommunication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/childCommunication.js\");\n/* harmony import */ var _communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communicationUtils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communicationUtils.js\");\n/* harmony import */ var _globalVars_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globalVars.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\");\n/* harmony import */ var _handlers_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _hostToAppTelemetry_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hostToAppTelemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/hostToAppTelemetry.js\");\n/* harmony import */ var _messageObjects_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./messageObjects.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/messageObjects.js\");\n/* harmony import */ var _nestedAppAuthUtils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./nestedAppAuthUtils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/nestedAppAuthUtils.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\n/* harmony import */ var _validOrigins_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./validOrigins.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/validOrigins.js\");\nconst k=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"communication\");class I{}class T{}function E(n,t){if(T.messageListener=n=>function(n){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this,void 0,void 0,(function*(){if(!n||!n.data||\"object\"!=typeof n.data)return void J(\"Unrecognized message format received by app, message being ignored. Message: %o\",n);const e=n.source||n.originalEvent&&n.originalEvent.source,t=n.origin||n.originalEvent&&n.originalEvent.origin;return B(e,t).then((o=>{o?(!function(e,n){_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.isFramelessWindow||I.parentWindow&&!I.parentWindow.closed&&e!==I.parentWindow||(I.parentWindow=e,I.parentOrigin=n);I.parentWindow&&I.parentWindow.closed&&(I.parentWindow=null,I.parentOrigin=null);(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.flushMessageQueue)(I.parentWindow,I.parentOrigin,T.parentMessageQueue,\"parent\")}(e,t),e!==I.parentWindow?(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_4__.shouldProcessChildMessage)(e,t)&&(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_4__.handleIncomingMessageFromChild)(n,e,V,((e,n)=>T.callbacks.set(e,n))):Y(n)):J(\"Message being ignored by app because it is either coming from the current window or a different window with an invalid origin, message: %o, source: %o, origin: %o\",n,e,t)}))}))}(n),I.currentWindow=I.currentWindow||(0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.ssrSafeWindow)(),I.parentWindow=I.currentWindow.parent!==I.currentWindow.self?I.currentWindow.parent:I.currentWindow.opener,I.topWindow=I.currentWindow.top,(I.parentWindow||n)&&I.currentWindow.addEventListener(\"message\",T.messageListener,!1),!I.parentWindow){const e=I.currentWindow;if(!e.nativeInterface)return Promise.reject(new Error(\"Initialization Failed. No Parent window found.\"));_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.isFramelessWindow=!0,e.onNativeMessage=Y}try{return I.parentOrigin=\"*\",S(t,\"initialize\",[_public_version_js__WEBPACK_IMPORTED_MODULE_6__.version,_public_runtime_js__WEBPACK_IMPORTED_MODULE_7__.latestRuntimeApiVersion,n]).then((([e,n,t,o])=>((0,_nestedAppAuthUtils_js__WEBPACK_IMPORTED_MODULE_8__.tryPolyfillWithNestedAppAuthBridge)(o,I.currentWindow,{onMessage:D,sendPostMessage:L}),{context:e,clientType:n,runtimeConfig:t,clientSupportedSDKVersion:o})))}finally{I.parentOrigin=null}}function j(){I.currentWindow&&I.currentWindow.removeEventListener(\"message\",T.messageListener,!1),I.currentWindow=null,I.parentWindow=null,I.parentOrigin=null,T.parentMessageQueue=[],T.nextMessageId=0,T.callbacks.clear(),T.promiseCallbacks.clear(),T.portCallbacks.clear(),T.legacyMessageIdsToUuidMap={},_hostToAppTelemetry_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"].clearMessages(),(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_4__.uninitializeChildCommunication)()}function O(e,n,...t){return S(e,n,t).then((([e])=>e))}function R(e,n,...t){return S(e,n,t).then((([e,n])=>{if(!e)throw new Error(n)}))}function C(e,n,t,...o){return S(e,n,o).then((([e,n])=>{if(!e)throw new Error(n||t)}))}function P(e,n,...t){return S(e,n,t).then((([e,n])=>{if(e)throw e;return n}))}function S(e,n,t=void 0){if(!(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.isFollowingApiVersionTagFormat)(e))throw Error(`apiVersionTag: ${e} passed in doesn't follow the pattern starting with 'v' followed by digits, then underscore with words, please check.`);return new Promise((o=>{const r=V(e,n,t);var i;o((i=r.uuid,new Promise((e=>{T.promiseCallbacks.set(i,e)}))))}))}function x(e){return e.map((e=>(0,_public_serializable_interface_js__WEBPACK_IMPORTED_MODULE_10__.isSerializable)(e)?e.serialize():e))}function N(o,r,i,s,a){var c;return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this,void 0,void 0,(function*(){const e=x(r),[d]=yield S(s,o,e);if(a&&a(d)||!a&&(0,_public_interfaces_js__WEBPACK_IMPORTED_MODULE_11__.isSdkError)(d))throw new Error(`${d.errorCode}, message: ${null!==(c=d.message)&&void 0!==c?c:\"None\"}`);if(i.validate(d))return i.deserialize(d);throw new Error(`${_public_interfaces_js__WEBPACK_IMPORTED_MODULE_11__.ErrorCode.INTERNAL_ERROR}, message: Invalid response from host - ${JSON.stringify(d)}`)}))}function A(o,r,i,s){var a;return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this,void 0,void 0,(function*(){const e=x(r),[c]=yield S(i,o,e);if(s&&s(c)||!s&&(0,_public_interfaces_js__WEBPACK_IMPORTED_MODULE_11__.isSdkError)(c))throw new Error(`${c.errorCode}, message: ${null!==(a=c.message)&&void 0!==a?a:\"None\"}`);if(void 0!==c)throw new Error(`${_public_interfaces_js__WEBPACK_IMPORTED_MODULE_11__.ErrorCode.INTERNAL_ERROR}, message: Invalid response from host`)}))}function U(e,n,t=void 0){if(!(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.isFollowingApiVersionTagFormat)(e))throw Error(`apiVersionTag: ${e} passed in doesn't follow the pattern starting with 'v' followed by digits, then underscore with words, please check.`);const o=V(e,n,t);return r=o.uuid,new Promise(((e,n)=>{T.portCallbacks.set(r,((t,o)=>{t instanceof MessagePort?e(t):n(o&&o.length>0?o[0]:new Error(\"Host responded without port or error details.\"))}))}));var r}function $(e,n,t,o){let r;if(t instanceof Function?o=t:t instanceof Array&&(r=t),!(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.isFollowingApiVersionTagFormat)(e))throw Error(`apiVersionTag: ${e} passed in doesn't follow the pattern starting with 'v' followed by digits, then underscore with words, please check.`);const i=V(e,n,r);o&&T.callbacks.set(i.uuid,o)}T.parentMessageQueue=[],T.topMessageQueue=[],T.nextMessageId=0,T.callbacks=new Map,T.promiseCallbacks=new Map,T.portCallbacks=new Map,T.legacyMessageIdsToUuidMap={};const z=k.extend(\"sendNestedAuthRequestToTopWindow\");function L(e,n){const t=z,o=I.topWindow,r=function(e,n){const t=T.nextMessageId++,o=new _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_12__.UUID;return T.legacyMessageIdsToUuidMap[t]=o,{id:t,uuid:o,func:\"nestedAppAuth.execute\",timestamp:Date.now(),monotonicTimestamp:(0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getCurrentTimestamp)(),apiVersionTag:n,args:[],data:e}}(e,n);return t(\"Message %s information: %o\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(r),{actionName:r.func}),F(o,r)}const _=k.extend(\"sendRequestToTargetWindowHelper\");function F(e,n){const t=_,o=function(e){return e===I.topWindow&&Z()?\"top\":e===I.parentWindow?\"parent\":null}(e),r=(0,_messageObjects_js__WEBPACK_IMPORTED_MODULE_13__.serializeMessageRequest)(n);if(_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.isFramelessWindow)I.currentWindow&&I.currentWindow.nativeInterface&&(t(\"Sending message %s to %s via framelessPostMessage interface\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(r),o),I.currentWindow.nativeInterface.framelessPostMessage(JSON.stringify(r)));else{const i=function(e){return e===I.topWindow&&Z()?I.topOrigin:e===I.parentWindow?I.parentOrigin:null}(e);e&&i?(t(\"Sending message %s to %s via postMessage\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(r),o),e.postMessage(r,i)):(t(\"Adding message %s to %s message queue\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(r),o),ee(e).push(n))}return n}const Q=k.extend(\"sendMessageToParentHelper\");function V(e,n,t,o,r){const s=Q,a=I.parentWindow,c=function(e,n,t,o,r){const s=T.nextMessageId++,a=new _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_12__.UUID;T.legacyMessageIdsToUuidMap[s]=a;const c=!0===o?r:_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.teamsJsInstanceId;return{id:s,uuid:a,func:n,timestamp:Date.now(),monotonicTimestamp:(0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getCurrentTimestamp)(),args:t||[],apiVersionTag:e,isProxiedFromChild:null!=o&&o,teamsJsInstanceId:c}}(e,n,t,o,r);return _hostToAppTelemetry_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"].storeCallbackInformation(c.uuid,{name:n,calledAt:c.timestamp}),s(\"Message %s information: %o\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(c),{actionName:n,args:t}),F(a,c)}const J=k.extend(\"processIncomingMessage\");const q=k.extend(\"processAuthBridgeMessage\");function D(e,n){var t,o;const r=q;if(!e||!e.data||\"object\"!=typeof e.data)return void r(\"Unrecognized message format received by app, message being ignored. Message: %o\",e);const{args:i}=e.data,[,s]=null!=i?i:[],a=(()=>{try{return JSON.parse(s)}catch(e){return null}})();if(!a||\"object\"!=typeof a||\"NestedAppAuthResponse\"!==a.messageType)return void r(\"Unrecognized data format received by app, message being ignored. Message: %o\",e);const c=e.source||(null===(t=null==e?void 0:e.originalEvent)||void 0===t?void 0:t.source),d=e.origin||(null===(o=null==e?void 0:e.originalEvent)||void 0===o?void 0:o.origin);c?B(c,d)?(I.topWindow&&!I.topWindow.closed&&c!==I.topWindow||(I.topWindow=c,I.topOrigin=d),I.topWindow&&I.topWindow.closed&&(I.topWindow=null,I.topOrigin=null),(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.flushMessageQueue)(I.topWindow,I.topOrigin,T.topMessageQueue,\"top\"),n(s)):r(\"Message being ignored by app because it is either coming from the current window or a different window with an invalid origin\"):r(\"Message being ignored by app because it is coming for a target that is null\")}const H=k.extend(\"shouldProcessIncomingMessage\");function B(n,t){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this,void 0,void 0,(function*(){if(I.currentWindow&&n===I.currentWindow)return H(\"Should not process message because it is coming from the current window\"),!1;if(I.currentWindow&&I.currentWindow.location&&t&&t===I.currentWindow.location.origin)return!0;{let e;try{e=new URL(t)}catch(e){return H(\"Message has an invalid origin of %s\",t),!1}const n=yield (0,_validOrigins_js__WEBPACK_IMPORTED_MODULE_14__.validateOrigin)(e);return n||H(\"Message has an invalid origin of %s\",t),n}}))}const K=k.extend(\"handleIncomingMessageFromParent\");function G(e,n){if(n){const t=[...e].find((([e,t])=>e.toString()===n.toString()));if(t)return t[0]}}function X(e,n){const t=G(n,e.uuid);t&&n.delete(t),e.uuid?T.legacyMessageIdsToUuidMap={}:delete T.legacyMessageIdsToUuidMap[e.id]}function Y(e){const n=K,t=(0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getCurrentTimestamp)();if(\"id\"in e.data&&\"number\"==typeof e.data.id){const o=e.data,r=(0,_messageObjects_js__WEBPACK_IMPORTED_MODULE_13__.deserializeMessageResponse)(o),i=function(e){const n=K;if(!e.uuid)return T.legacyMessageIdsToUuidMap[e.id];{const n=e.uuid,t=G(T.callbacks,n);if(t)return t;const o=G(T.promiseCallbacks,n);if(o)return o;const r=G(T.portCallbacks,n);if(r)return r}n(\"Received message %s that failed to produce a callbackId\",(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(e))}(r);if(i){const o=T.callbacks.get(i);n(\"Received a response from parent for message %s\",i.toString()),_hostToAppTelemetry_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"].handlePerformanceMetrics(i,r,n,t),o&&(n(\"Invoking the registered callback for message %s with arguments %o\",i.toString(),r.args),o.apply(null,[...r.args,r.isPartialResponse]),function(e){return!0===e.data.isPartialResponse}(e)||(n(\"Removing registered callback for message %s\",i.toString()),X(r,T.callbacks)));const s=T.promiseCallbacks.get(i);s&&(n(\"Invoking the registered promise callback for message %s with arguments %o\",i.toString(),r.args),s(r.args),n(\"Removing registered promise callback for message %s\",i.toString()),X(r,T.promiseCallbacks));const a=T.portCallbacks.get(i);if(a){let t;n(\"Invoking the registered port callback for message %s with arguments %o\",i.toString(),r.args),e.ports&&e.ports[0]instanceof MessagePort&&(t=e.ports[0]),a(t,r.args),n(\"Removing registered port callback for message %s\",i.toString()),X(r,T.portCallbacks)}r.uuid&&(T.legacyMessageIdsToUuidMap={})}}else if(\"func\"in e.data&&\"string\"==typeof e.data.func){const o=e.data;_hostToAppTelemetry_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"].handleOneWayPerformanceMetrics(o,n,t),n('Received a message from parent %s, action: \"%s\"',(0,_communicationUtils_js__WEBPACK_IMPORTED_MODULE_3__.getMessageIdsAsLogString)(o),o.func),(0,_handlers_js__WEBPACK_IMPORTED_MODULE_15__.callHandler)(o.func,o.args)}else n(\"Received an unknown message: %O\",e)}function Z(){return I.topWindow!==I.parentWindow}function ee(e){return e===I.topWindow&&Z()?T.topMessageQueue:e===I.parentWindow?T.parentMessageQueue:[]}function ne(e,n){let t;t=I.currentWindow.setInterval((()=>{0===ee(e).length&&(clearInterval(t),n())}),100)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communicationUtils.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communicationUtils.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flushMessageQueue: () => (/* binding */ u),\n/* harmony export */   getMessageIdsAsLogString: () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _messageObjects_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./messageObjects.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/messageObjects.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\nfunction i(e){return void 0!==e.uuidAsString?`${e.uuidAsString} (legacy id: ${e.id})`:void 0!==e.uuid?`${e.uuid.toString()} (legacy id: ${e.id})`:`legacy id: ${e.id} (no uuid)`}const t=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"flushMessageQueue\");function u(s,u,o,g){if(s&&u&&0!==o.length)for(;o.length>0;){const n=o.shift();if(n){const o=(0,_messageObjects_js__WEBPACK_IMPORTED_MODULE_1__.serializeMessageRequest)(n);t(\"Flushing message %s from %s message queue via postMessage.\",i(o),g),s.postMessage(o,u)}}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvY29tbXVuaWNhdGlvblV0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBeUcsY0FBYyxrQ0FBa0MsZ0JBQWdCLGNBQWMsS0FBSyxzQkFBc0IsbUJBQW1CLGNBQWMsS0FBSyxpQkFBaUIsTUFBTSxXQUFXLFFBQVEsd0RBQUMsc0JBQXNCLG9CQUFvQiwyQkFBMkIsV0FBVyxFQUFFLGtCQUFrQixNQUFNLFFBQVEsMkVBQUMsSUFBSSw0RkFBeUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vcGFja2FnZXMvdGVhbXMtanMvc3JjL2ludGVybmFsL2NvbW11bmljYXRpb25VdGlscy5qcz9kZDAwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtzZXJpYWxpemVNZXNzYWdlUmVxdWVzdCBhcyBlfWZyb21cIi4vbWVzc2FnZU9iamVjdHMuanNcIjtpbXBvcnR7Z2V0TG9nZ2VyIGFzIHN9ZnJvbVwiLi90ZWxlbWV0cnkuanNcIjtmdW5jdGlvbiBpKGUpe3JldHVybiB2b2lkIDAhPT1lLnV1aWRBc1N0cmluZz9gJHtlLnV1aWRBc1N0cmluZ30gKGxlZ2FjeSBpZDogJHtlLmlkfSlgOnZvaWQgMCE9PWUudXVpZD9gJHtlLnV1aWQudG9TdHJpbmcoKX0gKGxlZ2FjeSBpZDogJHtlLmlkfSlgOmBsZWdhY3kgaWQ6ICR7ZS5pZH0gKG5vIHV1aWQpYH1jb25zdCB0PXMoXCJmbHVzaE1lc3NhZ2VRdWV1ZVwiKTtmdW5jdGlvbiB1KHMsdSxvLGcpe2lmKHMmJnUmJjAhPT1vLmxlbmd0aClmb3IoO28ubGVuZ3RoPjA7KXtjb25zdCBuPW8uc2hpZnQoKTtpZihuKXtjb25zdCBvPWUobik7dChcIkZsdXNoaW5nIG1lc3NhZ2UgJXMgZnJvbSAlcyBtZXNzYWdlIHF1ZXVlIHZpYSBwb3N0TWVzc2FnZS5cIixpKG8pLGcpLHMucG9zdE1lc3NhZ2Uobyx1KX19fWV4cG9ydHt1IGFzIGZsdXNoTWVzc2FnZVF1ZXVlLGkgYXMgZ2V0TWVzc2FnZUlkc0FzTG9nU3RyaW5nfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communicationUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/constants.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/constants.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ORIGIN_LIST_FETCH_TIMEOUT_IN_MS: () => (/* binding */ c),\n/* harmony export */   captureImageMobileSupportVersion: () => (/* binding */ m),\n/* harmony export */   defaultSDKVersionForCompatCheck: () => (/* binding */ i),\n/* harmony export */   errorCallNotStarted: () => (/* binding */ u),\n/* harmony export */   errorLibraryNotInitialized: () => (/* binding */ D),\n/* harmony export */   errorRuntimeNotInitialized: () => (/* binding */ T),\n/* harmony export */   errorRuntimeNotSupported: () => (/* binding */ b),\n/* harmony export */   getMediaCallbackSupportVersion: () => (/* binding */ d),\n/* harmony export */   getUserJoinedTeamsSupportedAndroidClientVersion: () => (/* binding */ o),\n/* harmony export */   imageOutputFormatsAPISupportVersion: () => (/* binding */ n),\n/* harmony export */   locationAPIsRequiredVersion: () => (/* binding */ a),\n/* harmony export */   mediaAPISupportVersion: () => (/* binding */ l),\n/* harmony export */   nonFullScreenVideoModeAPISupportVersion: () => (/* binding */ e),\n/* harmony export */   peoplePickerRequiredVersion: () => (/* binding */ r),\n/* harmony export */   scanBarCodeAPIMobileSupportVersion: () => (/* binding */ h),\n/* harmony export */   teamsDeepLinkHost: () => (/* binding */ y),\n/* harmony export */   teamsDeepLinkProtocol: () => (/* binding */ v),\n/* harmony export */   userOriginUrlValidationRegExp: () => (/* binding */ j),\n/* harmony export */   validOriginsCdnEndpoint: () => (/* binding */ f),\n/* harmony export */   validOriginsFallback: () => (/* binding */ p),\n/* harmony export */   videoAndImageMediaAPISupportVersion: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var _artifactsForCDN_validDomains_json_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../artifactsForCDN/validDomains.json.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/artifactsForCDN/validDomains.json.js\");\nconst i=\"2.0.1\",s=\"2.0.2\",e=\"2.0.3\",n=\"2.0.4\",o=\"2.0.1\",a=\"1.9.0\",r=\"2.0.0\",m=\"1.7.0\",l=\"1.8.0\",d=\"2.0.0\",h=\"1.9.0\",p=_artifactsForCDN_validDomains_json_js__WEBPACK_IMPORTED_MODULE_0__.validOrigins,c=1500,f=new URL(\"https://res.cdn.office.net/teams-js/validDomains/json/validDomains.json\"),j=/^https:\\/\\//,v=\"https\",y=\"teams.microsoft.com\",D=\"The library has not yet been initialized\",T=\"The runtime has not yet been initialized\",b=\"The runtime version is not supported\",u=\"The call was not properly started\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEQsc0hBQXNILCtFQUFjLHdUQUFpaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vcGFja2FnZXMvdGVhbXMtanMvc3JjL2ludGVybmFsL2NvbnN0YW50cy5qcz8zYTZiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCphcyB0IGZyb21cIi4uL2FydGlmYWN0c0ZvckNETi92YWxpZERvbWFpbnMuanNvbi5qc1wiO2NvbnN0IGk9XCIyLjAuMVwiLHM9XCIyLjAuMlwiLGU9XCIyLjAuM1wiLG49XCIyLjAuNFwiLG89XCIyLjAuMVwiLGE9XCIxLjkuMFwiLHI9XCIyLjAuMFwiLG09XCIxLjcuMFwiLGw9XCIxLjguMFwiLGQ9XCIyLjAuMFwiLGg9XCIxLjkuMFwiLHA9dC52YWxpZE9yaWdpbnMsYz0xNTAwLGY9bmV3IFVSTChcImh0dHBzOi8vcmVzLmNkbi5vZmZpY2UubmV0L3RlYW1zLWpzL3ZhbGlkRG9tYWlucy9qc29uL3ZhbGlkRG9tYWlucy5qc29uXCIpLGo9L15odHRwczpcXC9cXC8vLHY9XCJodHRwc1wiLHk9XCJ0ZWFtcy5taWNyb3NvZnQuY29tXCIsRD1cIlRoZSBsaWJyYXJ5IGhhcyBub3QgeWV0IGJlZW4gaW5pdGlhbGl6ZWRcIixUPVwiVGhlIHJ1bnRpbWUgaGFzIG5vdCB5ZXQgYmVlbiBpbml0aWFsaXplZFwiLGI9XCJUaGUgcnVudGltZSB2ZXJzaW9uIGlzIG5vdCBzdXBwb3J0ZWRcIix1PVwiVGhlIGNhbGwgd2FzIG5vdCBwcm9wZXJseSBzdGFydGVkXCI7ZXhwb3J0e2MgYXMgT1JJR0lOX0xJU1RfRkVUQ0hfVElNRU9VVF9JTl9NUyxtIGFzIGNhcHR1cmVJbWFnZU1vYmlsZVN1cHBvcnRWZXJzaW9uLGkgYXMgZGVmYXVsdFNES1ZlcnNpb25Gb3JDb21wYXRDaGVjayx1IGFzIGVycm9yQ2FsbE5vdFN0YXJ0ZWQsRCBhcyBlcnJvckxpYnJhcnlOb3RJbml0aWFsaXplZCxUIGFzIGVycm9yUnVudGltZU5vdEluaXRpYWxpemVkLGIgYXMgZXJyb3JSdW50aW1lTm90U3VwcG9ydGVkLGQgYXMgZ2V0TWVkaWFDYWxsYmFja1N1cHBvcnRWZXJzaW9uLG8gYXMgZ2V0VXNlckpvaW5lZFRlYW1zU3VwcG9ydGVkQW5kcm9pZENsaWVudFZlcnNpb24sbiBhcyBpbWFnZU91dHB1dEZvcm1hdHNBUElTdXBwb3J0VmVyc2lvbixhIGFzIGxvY2F0aW9uQVBJc1JlcXVpcmVkVmVyc2lvbixsIGFzIG1lZGlhQVBJU3VwcG9ydFZlcnNpb24sZSBhcyBub25GdWxsU2NyZWVuVmlkZW9Nb2RlQVBJU3VwcG9ydFZlcnNpb24sciBhcyBwZW9wbGVQaWNrZXJSZXF1aXJlZFZlcnNpb24saCBhcyBzY2FuQmFyQ29kZUFQSU1vYmlsZVN1cHBvcnRWZXJzaW9uLHkgYXMgdGVhbXNEZWVwTGlua0hvc3QsdiBhcyB0ZWFtc0RlZXBMaW5rUHJvdG9jb2wsaiBhcyB1c2VyT3JpZ2luVXJsVmFsaWRhdGlvblJlZ0V4cCxmIGFzIHZhbGlkT3JpZ2luc0NkbkVuZHBvaW50LHAgYXMgdmFsaWRPcmlnaW5zRmFsbGJhY2sscyBhcyB2aWRlb0FuZEltYWdlTWVkaWFBUElTdXBwb3J0VmVyc2lvbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   botUrlOpenHelper: () => (/* binding */ j),\n/* harmony export */   dialogTelemetryVersionNumber: () => (/* binding */ d),\n/* harmony export */   getDialogInfoFromAdaptiveCardDialogInfo: () => (/* binding */ b),\n/* harmony export */   getDialogInfoFromBotAdaptiveCardDialogInfo: () => (/* binding */ F),\n/* harmony export */   handleDialogMessage: () => (/* binding */ w),\n/* harmony export */   storedMessages: () => (/* binding */ S),\n/* harmony export */   updateResizeHelper: () => (/* binding */ h),\n/* harmony export */   urlOpenHelper: () => (/* binding */ k),\n/* harmony export */   urlSubmitHelper: () => (/* binding */ P)\n/* harmony export */ });\n/* harmony import */ var _internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _public_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../public/constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _public_runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../public/runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _communication_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _globalVars_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./globalVars.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\");\n/* harmony import */ var _handlers_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _public_dialog_update_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/dialog/update.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/update.js\");\n/* harmony import */ var _public_dialog_url_url_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/dialog/url/url.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/url.js\");\n/* harmony import */ var _public_dialog_url_bot_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/dialog/url/bot.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/bot.js\");\nconst d=\"v2\";function h(o,a){if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.task,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!(0,_public_dialog_update_js__WEBPACK_IMPORTED_MODULE_3__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(o,\"tasks.updateTask\",[a])}function k(o,a,u,f){if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!(0,_public_dialog_url_url_js__WEBPACK_IMPORTED_MODULE_5__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;f&&(0,_handlers_js__WEBPACK_IMPORTED_MODULE_6__.registerHandler)((0,_telemetry_js__WEBPACK_IMPORTED_MODULE_7__.getApiVersionTag)(d,\"dialog.url.registerMessageForParentHandler\"),\"messageForParent\",f);const g=(0,_public_dialog_url_url_js__WEBPACK_IMPORTED_MODULE_5__.getDialogInfoFromUrlDialogInfo)(a);(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(o,\"tasks.startTask\",[g],((t,e)=>{null==u||u({err:t,result:e}),(0,_handlers_js__WEBPACK_IMPORTED_MODULE_6__.removeHandler)(\"messageForParent\")}))}function j(o,a,u,c){if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!(0,_public_dialog_url_bot_js__WEBPACK_IMPORTED_MODULE_8__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;c&&(0,_handlers_js__WEBPACK_IMPORTED_MODULE_6__.registerHandler)((0,_telemetry_js__WEBPACK_IMPORTED_MODULE_7__.getApiVersionTag)(d,\"dialog.url.bot.registerMessageForParentHandler\"),\"messageForParent\",c);const p=(0,_public_dialog_url_url_js__WEBPACK_IMPORTED_MODULE_5__.getDialogInfoFromBotUrlDialogInfo)(a);(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(o,\"tasks.startTask\",[p],((t,e)=>{null==u||u({err:t,result:e}),(0,_handlers_js__WEBPACK_IMPORTED_MODULE_6__.removeHandler)(\"messageForParent\")}))}function P(o,a,n){if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.task),!(0,_public_dialog_url_url_js__WEBPACK_IMPORTED_MODULE_5__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(o,\"tasks.completeTask\",[a,n?Array.isArray(n)?n:[n]:[]])}function b(t){return{card:t.card,height:t.size?t.size.height:_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.DialogDimension.Small,width:t.size?t.size.width:_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.DialogDimension.Small,title:t.title}}function F(t){const e=b(t);return e.completionBotId=t.completionBotId,e}const S=[];function w(t){_globalVars_js__WEBPACK_IMPORTED_MODULE_9__.GlobalVars.frameContext&&(_globalVars_js__WEBPACK_IMPORTED_MODULE_9__.GlobalVars.frameContext===_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.task?S.push(t):(0,_handlers_js__WEBPACK_IMPORTED_MODULE_6__.removeHandler)(\"messageForChild\"))}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalVars: () => (/* binding */ e)\n/* harmony export */ });\n/* harmony import */ var _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../public/uuidObject.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/uuidObject.js\");\nclass e{}e.initializeCalled=!1,e.initializeCompleted=!1,e.additionalValidOrigins=[],e.initializePromise=void 0,e.isFramelessWindow=!1,e.frameContext=void 0,e.hostClientType=void 0,e.printCapabilityEnabled=!1,e.teamsJsInstanceId=(new _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_0__.UUID).toString();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvZ2xvYmFsVmFycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQyxTQUFTLGdPQUFnTyx1REFBQyxhQUFxQyIsInNvdXJjZXMiOlsid2VicGFjazovL3FoYXJtb255LXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvZ2xvYmFsVmFycy5qcz9mYjIzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtVVUlEIGFzIGl9ZnJvbVwiLi4vcHVibGljL3V1aWRPYmplY3QuanNcIjtjbGFzcyBle31lLmluaXRpYWxpemVDYWxsZWQ9ITEsZS5pbml0aWFsaXplQ29tcGxldGVkPSExLGUuYWRkaXRpb25hbFZhbGlkT3JpZ2lucz1bXSxlLmluaXRpYWxpemVQcm9taXNlPXZvaWQgMCxlLmlzRnJhbWVsZXNzV2luZG93PSExLGUuZnJhbWVDb250ZXh0PXZvaWQgMCxlLmhvc3RDbGllbnRUeXBlPXZvaWQgMCxlLnByaW50Q2FwYWJpbGl0eUVuYWJsZWQ9ITEsZS50ZWFtc0pzSW5zdGFuY2VJZD0obmV3IGkpLnRvU3RyaW5nKCk7ZXhwb3J0e2UgYXMgR2xvYmFsVmFyc307XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   callHandler: () => (/* binding */ h),\n/* harmony export */   doesHandlerExist: () => (/* binding */ b),\n/* harmony export */   handleHostToAppPerformanceMetrics: () => (/* binding */ y),\n/* harmony export */   handleThemeChange: () => (/* binding */ C),\n/* harmony export */   initializeHandlers: () => (/* binding */ f),\n/* harmony export */   registerBeforeSuspendOrTerminateHandler: () => (/* binding */ A),\n/* harmony export */   registerBeforeUnloadHandler: () => (/* binding */ S),\n/* harmony export */   registerHandler: () => (/* binding */ p),\n/* harmony export */   registerHandlerHelper: () => (/* binding */ U),\n/* harmony export */   registerHostToAppPerformanceMetricsHandler: () => (/* binding */ v),\n/* harmony export */   registerOnLoadHandler: () => (/* binding */ j),\n/* harmony export */   registerOnResumeHandler: () => (/* binding */ P),\n/* harmony export */   registerOnThemeChangeHandler: () => (/* binding */ T),\n/* harmony export */   removeHandler: () => (/* binding */ g),\n/* harmony export */   uninitializeHandlers: () => (/* binding */ c)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _public_runtime_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _childCommunication_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./childCommunication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/childCommunication.js\");\n/* harmony import */ var _communication_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _pagesHelpers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pagesHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\");\n/* harmony import */ var _typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./typeCheckUtilities.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/typeCheckUtilities.js\");\nconst u=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"handlers\");class m{static initializeHandlers(){m.handlers.themeChange=C,m.handlers.load=O,m.handlers.beforeUnload=_,(0,_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_1__.initializeBackStackHelper)()}static uninitializeHandlers(){m.handlers={},m.themeChangeHandler=null,m.loadHandler=null,m.beforeUnloadHandler=null,m.beforeSuspendOrTerminateHandler=null,m.resumeHandler=null}}function f(){m.initializeHandlers()}function c(){m.uninitializeHandlers()}m.handlers={},m.themeChangeHandler=null,m.loadHandler=null,m.beforeUnloadHandler=null,m.beforeSuspendOrTerminateHandler=null,m.resumeHandler=null,m.hostToAppPerformanceMetricsHandler=null;const H=u.extend(\"callHandler\");function h(e,n){const r=m.handlers[e];if(r){H(\"Invoking the registered handler for message %s with arguments %o\",e,n);return[!0,r.apply(this,n)]}return (0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.shouldEventBeRelayedToChild)()?((0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.sendMessageEventToChild)(e,n),[!1,void 0]):(H(\"Handler for action message %s not found.\",e),[!1,void 0])}function p(e,n,r,l=!0,a=[]){r?(m.handlers[n]=r,l&&(0,_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)(e,\"registerHandler\",[n,...a])):delete m.handlers[n]}function g(e){delete m.handlers[e]}function b(e){return null!=m.handlers[e]}function U(e,n,r,a,o){r&&(0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime,...a),o&&o(),p(e,n,r)}function T(e,n){m.themeChangeHandler=n,!(0,_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_6__.isNullOrUndefined)(n)&&(0,_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)(e,\"registerHandler\",[\"themeChange\"])}function C(e){m.themeChangeHandler&&m.themeChangeHandler(e),(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.shouldEventBeRelayedToChild)()&&(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.sendMessageEventToChild)(\"themeChange\",[e])}function v(e){m.hostToAppPerformanceMetricsHandler=e}function y(e){m.hostToAppPerformanceMetricsHandler&&m.hostToAppPerformanceMetricsHandler(e)}function j(e,n){m.loadHandler=n,!(0,_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_6__.isNullOrUndefined)(n)&&(0,_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)(e,\"registerHandler\",[\"load\"])}function O(e){const n={entityId:(r=e).entityId,contentUrl:new URL(r.contentUrl)};var r;m.resumeHandler?(m.resumeHandler(n),(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.shouldEventBeRelayedToChild)()&&(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.sendMessageEventToChild)(\"load\",[n])):m.loadHandler&&(m.loadHandler(e),(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.shouldEventBeRelayedToChild)()&&(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.sendMessageEventToChild)(\"load\",[e]))}function S(e,n){m.beforeUnloadHandler=n,!(0,_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_6__.isNullOrUndefined)(n)&&(0,_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)(e,\"registerHandler\",[\"beforeUnload\"])}function _(){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_7__.__awaiter)(this,void 0,void 0,(function*(){const e=()=>{(0,_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)((0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getApiVersionTag)(\"v2\",\"handleBeforeUnload\"),\"readyToUnload\",[])};m.beforeSuspendOrTerminateHandler?(yield m.beforeSuspendOrTerminateHandler(),(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.shouldEventBeRelayedToChild)()?(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.sendMessageEventToChild)(\"beforeUnload\"):e()):m.beforeUnloadHandler&&m.beforeUnloadHandler(e)||((0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.shouldEventBeRelayedToChild)()?(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_2__.sendMessageEventToChild)(\"beforeUnload\"):e())}))}function A(e){m.beforeSuspendOrTerminateHandler=e,!(0,_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_6__.isNullOrUndefined)(e)&&(0,_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)((0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getApiVersionTag)(\"v2\",\"registerBeforeSuspendOrTerminateHandler\"),\"registerHandler\",[\"beforeUnload\"])}function P(e){m.resumeHandler=e,!(0,_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_6__.isNullOrUndefined)(e)&&(0,_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)((0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getApiVersionTag)(\"v2\",\"registerOnResumeHandler\"),\"registerHandler\",[\"load\"])}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/hostToAppTelemetry.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/hostToAppTelemetry.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ a)\n/* harmony export */ });\n/* harmony import */ var _handlers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\nclass a{static storeCallbackInformation(e,t){a.callbackInformation.set(e,t)}static clearMessages(){a.callbackInformation.clear()}static deleteMessageInformation(e){a.callbackInformation.delete(e)}static handleOneWayPerformanceMetrics(a,t,n){const o=a.monotonicTimestamp;o&&n?(0,_handlers_js__WEBPACK_IMPORTED_MODULE_0__.handleHostToAppPerformanceMetrics)({actionName:a.func,messageDelay:n-o,requestStartedAt:o}):t(\"Unable to send performance metrics for event %s\",a.func)}static handlePerformanceMetrics(t,n,o,s){const c=a.callbackInformation.get(t);c&&n.monotonicTimestamp&&s?((0,_handlers_js__WEBPACK_IMPORTED_MODULE_0__.handleHostToAppPerformanceMetrics)({actionName:c.name,messageDelay:s-n.monotonicTimestamp,requestStartedAt:c.calledAt}),a.deleteMessageInformation(t)):o(\"Unable to send performance metrics for callback %s with arguments %o\",t.toString(),n.args)}}a.callbackInformation=new Map;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvaG9zdFRvQXBwVGVsZW1ldHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtFLFFBQVEscUNBQXFDLCtCQUErQix1QkFBdUIsOEJBQThCLG1DQUFtQyxnQ0FBZ0MsNkNBQTZDLDZCQUE2QixLQUFLLCtFQUFDLEVBQUUsc0RBQXNELDhEQUE4RCx5Q0FBeUMscUNBQXFDLDRCQUE0QiwrRUFBQyxFQUFFLGtGQUFrRixnSUFBZ0ksOEJBQW1EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9pbnRlcm5hbC9ob3N0VG9BcHBUZWxlbWV0cnkuanM/ZmVhMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7aGFuZGxlSG9zdFRvQXBwUGVyZm9ybWFuY2VNZXRyaWNzIGFzIGV9ZnJvbVwiLi9oYW5kbGVycy5qc1wiO2NsYXNzIGF7c3RhdGljIHN0b3JlQ2FsbGJhY2tJbmZvcm1hdGlvbihlLHQpe2EuY2FsbGJhY2tJbmZvcm1hdGlvbi5zZXQoZSx0KX1zdGF0aWMgY2xlYXJNZXNzYWdlcygpe2EuY2FsbGJhY2tJbmZvcm1hdGlvbi5jbGVhcigpfXN0YXRpYyBkZWxldGVNZXNzYWdlSW5mb3JtYXRpb24oZSl7YS5jYWxsYmFja0luZm9ybWF0aW9uLmRlbGV0ZShlKX1zdGF0aWMgaGFuZGxlT25lV2F5UGVyZm9ybWFuY2VNZXRyaWNzKGEsdCxuKXtjb25zdCBvPWEubW9ub3RvbmljVGltZXN0YW1wO28mJm4/ZSh7YWN0aW9uTmFtZTphLmZ1bmMsbWVzc2FnZURlbGF5Om4tbyxyZXF1ZXN0U3RhcnRlZEF0Om99KTp0KFwiVW5hYmxlIHRvIHNlbmQgcGVyZm9ybWFuY2UgbWV0cmljcyBmb3IgZXZlbnQgJXNcIixhLmZ1bmMpfXN0YXRpYyBoYW5kbGVQZXJmb3JtYW5jZU1ldHJpY3ModCxuLG8scyl7Y29uc3QgYz1hLmNhbGxiYWNrSW5mb3JtYXRpb24uZ2V0KHQpO2MmJm4ubW9ub3RvbmljVGltZXN0YW1wJiZzPyhlKHthY3Rpb25OYW1lOmMubmFtZSxtZXNzYWdlRGVsYXk6cy1uLm1vbm90b25pY1RpbWVzdGFtcCxyZXF1ZXN0U3RhcnRlZEF0OmMuY2FsbGVkQXR9KSxhLmRlbGV0ZU1lc3NhZ2VJbmZvcm1hdGlvbih0KSk6byhcIlVuYWJsZSB0byBzZW5kIHBlcmZvcm1hbmNlIG1ldHJpY3MgZm9yIGNhbGxiYWNrICVzIHdpdGggYXJndW1lbnRzICVvXCIsdC50b1N0cmluZygpLG4uYXJncyl9fWEuY2FsbGJhY2tJbmZvcm1hdGlvbj1uZXcgTWFwO2V4cG9ydHthIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/hostToAppTelemetry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/idValidation.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/idValidation.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   doesStringContainNonPrintableCharacters: () => (/* binding */ s),\n/* harmony export */   isStringWithinAppIdLengthLimits: () => (/* binding */ e),\n/* harmony export */   maximumValidAppIdLength: () => (/* binding */ a),\n/* harmony export */   minimumValidAppIdLength: () => (/* binding */ r),\n/* harmony export */   validateAppIdInstance: () => (/* binding */ l),\n/* harmony export */   validateSafeContent: () => (/* binding */ o),\n/* harmony export */   validateStringLength: () => (/* binding */ n)\n/* harmony export */ });\n/* harmony import */ var _public_appId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../public/appId.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/appId.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\nfunction n(i){if(!e(i))throw new Error(`Potential app id (${i}) is invalid; its length ${i.length} is not within the length limits (${r}-${a}).`)}function o(i){if((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hasScriptTags)(i))throw new Error(`Potential app id (${i}) is invalid; it contains script tags.`);if(s(i))throw new Error(`Potential app id (${i}) is invalid; it contains non-printable characters.`)}const r=4,a=256;function e(i){return i.length<a&&i.length>r}function s(i){return[...i].some((i=>{const t=i.charCodeAt(0);return t<32||t>126}))}function l(t){if(!(t instanceof _public_appId_js__WEBPACK_IMPORTED_MODULE_1__.AppId))throw new Error(`Potential app id (${t}) is invalid; it is not an instance of AppId class.`)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvaWRWYWxpZGF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFzRixjQUFjLDhDQUE4QyxFQUFFLGNBQWMsYUFBYSxVQUFVLG1DQUFtQyxFQUFFLEdBQUcsRUFBRSxLQUFLLGNBQWMsR0FBRyx3REFBQyx5Q0FBeUMsRUFBRSxjQUFjLDJCQUEyQiw2Q0FBNkMsRUFBRSxjQUFjLHdDQUF3QyxnQkFBZ0IsY0FBYyw4QkFBOEIsY0FBYyx1QkFBdUIsd0JBQXdCLG1CQUFtQixHQUFHLGNBQWMsa0JBQWtCLG1EQUFDLHVDQUF1QyxFQUFFLGNBQWMsd0NBQTBRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9pbnRlcm5hbC9pZFZhbGlkYXRpb24uanM/M2RiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7QXBwSWQgYXMgaX1mcm9tXCIuLi9wdWJsaWMvYXBwSWQuanNcIjtpbXBvcnR7aGFzU2NyaXB0VGFncyBhcyB0fWZyb21cIi4vdXRpbHMuanNcIjtmdW5jdGlvbiBuKGkpe2lmKCFlKGkpKXRocm93IG5ldyBFcnJvcihgUG90ZW50aWFsIGFwcCBpZCAoJHtpfSkgaXMgaW52YWxpZDsgaXRzIGxlbmd0aCAke2kubGVuZ3RofSBpcyBub3Qgd2l0aGluIHRoZSBsZW5ndGggbGltaXRzICgke3J9LSR7YX0pLmApfWZ1bmN0aW9uIG8oaSl7aWYodChpKSl0aHJvdyBuZXcgRXJyb3IoYFBvdGVudGlhbCBhcHAgaWQgKCR7aX0pIGlzIGludmFsaWQ7IGl0IGNvbnRhaW5zIHNjcmlwdCB0YWdzLmApO2lmKHMoaSkpdGhyb3cgbmV3IEVycm9yKGBQb3RlbnRpYWwgYXBwIGlkICgke2l9KSBpcyBpbnZhbGlkOyBpdCBjb250YWlucyBub24tcHJpbnRhYmxlIGNoYXJhY3RlcnMuYCl9Y29uc3Qgcj00LGE9MjU2O2Z1bmN0aW9uIGUoaSl7cmV0dXJuIGkubGVuZ3RoPGEmJmkubGVuZ3RoPnJ9ZnVuY3Rpb24gcyhpKXtyZXR1cm5bLi4uaV0uc29tZSgoaT0+e2NvbnN0IHQ9aS5jaGFyQ29kZUF0KDApO3JldHVybiB0PDMyfHx0PjEyNn0pKX1mdW5jdGlvbiBsKHQpe2lmKCEodCBpbnN0YW5jZW9mIGkpKXRocm93IG5ldyBFcnJvcihgUG90ZW50aWFsIGFwcCBpZCAoJHt0fSkgaXMgaW52YWxpZDsgaXQgaXMgbm90IGFuIGluc3RhbmNlIG9mIEFwcElkIGNsYXNzLmApfWV4cG9ydHtzIGFzIGRvZXNTdHJpbmdDb250YWluTm9uUHJpbnRhYmxlQ2hhcmFjdGVycyxlIGFzIGlzU3RyaW5nV2l0aGluQXBwSWRMZW5ndGhMaW1pdHMsYSBhcyBtYXhpbXVtVmFsaWRBcHBJZExlbmd0aCxyIGFzIG1pbmltdW1WYWxpZEFwcElkTGVuZ3RoLGwgYXMgdmFsaWRhdGVBcHBJZEluc3RhbmNlLG8gYXMgdmFsaWRhdGVTYWZlQ29udGVudCxuIGFzIHZhbGlkYXRlU3RyaW5nTGVuZ3RofTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/idValidation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureInitializeCalled: () => (/* binding */ p),\n/* harmony export */   ensureInitialized: () => (/* binding */ m),\n/* harmony export */   isCurrentSDKVersionAtLeast: () => (/* binding */ u),\n/* harmony export */   isHostClientMobile: () => (/* binding */ C),\n/* harmony export */   processAdditionalValidOrigins: () => (/* binding */ w),\n/* harmony export */   throwExceptionIfMobileApiIsNotSupported: () => (/* binding */ h)\n/* harmony export */ });\n/* harmony import */ var _public_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _public_interfaces_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/interfaces.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/interfaces.js\");\n/* harmony import */ var _public_runtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/constants.js\");\n/* harmony import */ var _globalVars_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globalVars.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\nconst f=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"internal\"),c=f.extend(\"ensureInitializeCalled\"),d=f.extend(\"ensureInitialized\");function p(){if(!_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.initializeCalled)throw c(_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorLibraryNotInitialized),new Error(_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorLibraryNotInitialized)}function m(i,...t){if(!_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.initializeCompleted)throw d(\"%s. initializeCalled: %s\",_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorLibraryNotInitialized,_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.initializeCalled.toString()),new Error(_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorLibraryNotInitialized);if(t&&t.length>0){let i=!1;for(let e=0;e<t.length;e++)if(t[e]===_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.frameContext){i=!0;break}if(!i)throw new Error(`This call is only allowed in following contexts: ${JSON.stringify(t)}. Current context: \"${_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.frameContext}\".`)}return (0,_public_runtime_js__WEBPACK_IMPORTED_MODULE_3__.isRuntimeInitialized)(i)}function u(i=_constants_js__WEBPACK_IMPORTED_MODULE_2__.defaultSDKVersionForCompatCheck){const t=(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.compareSDKVersions)(_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.clientSupportedSDKVersion,i);return!isNaN(t)&&t>=0}function C(){return _globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.hostClientType==_public_constants_js__WEBPACK_IMPORTED_MODULE_5__.HostClientType.android||_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.hostClientType==_public_constants_js__WEBPACK_IMPORTED_MODULE_5__.HostClientType.ios||_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.hostClientType==_public_constants_js__WEBPACK_IMPORTED_MODULE_5__.HostClientType.ipados||_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.hostClientType==_public_constants_js__WEBPACK_IMPORTED_MODULE_5__.HostClientType.visionOS}function h(i=_constants_js__WEBPACK_IMPORTED_MODULE_2__.defaultSDKVersionForCompatCheck){if(!C()){throw{errorCode:_public_interfaces_js__WEBPACK_IMPORTED_MODULE_6__.ErrorCode.NOT_SUPPORTED_ON_PLATFORM}}if(!u(i)){throw{errorCode:_public_interfaces_js__WEBPACK_IMPORTED_MODULE_6__.ErrorCode.OLD_PLATFORM}}}function w(i){let t=_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.additionalValidOrigins.concat(i.filter((i=>\"string\"==typeof i&&_constants_js__WEBPACK_IMPORTED_MODULE_2__.userOriginUrlValidationRegExp.test(i))));const e={};t=t.filter((i=>!e[i]&&(e[i]=!0,!0))),_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.additionalValidOrigins=t}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/messageObjects.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/messageObjects.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializeMessageRequest: () => (/* binding */ s),\n/* harmony export */   deserializeMessageResponse: () => (/* binding */ n),\n/* harmony export */   serializeMessageRequest: () => (/* binding */ u),\n/* harmony export */   serializeMessageResponse: () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../public/uuidObject.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/uuidObject.js\");\nconst u=t=>{const{uuid:u}=t,s=(0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__rest)(t,[\"uuid\"]),n=null==u?void 0:u.toString();return Object.assign(Object.assign({},s),{uuidAsString:n})},s=u=>{const{uuidAsString:s}=u,n=(0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__rest)(u,[\"uuidAsString\"]);return Object.assign(Object.assign({},n),{uuid:s?new _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_1__.UUID(s):void 0})},n=u=>{const{uuidAsString:s}=u,n=(0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__rest)(u,[\"uuidAsString\"]);return Object.assign(Object.assign({},n),{uuid:s?new _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_1__.UUID(s):void 0})},r=t=>{const{uuid:u}=t,s=(0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_0__.__rest)(t,[\"uuid\"]),n=null==u?void 0:u.toString();return Object.assign(Object.assign({},s),{uuidAsString:n})};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvbWVzc2FnZU9iamVjdHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW1OLFlBQVksTUFBTSxPQUFPLEtBQUsscUtBQUMsMkNBQTJDLHFDQUFxQyxLQUFLLGVBQWUsRUFBRSxPQUFPLE1BQU0sZUFBZSxLQUFLLHFLQUFDLHFCQUFxQixxQ0FBcUMsS0FBSyxXQUFXLHVEQUFDLFdBQVcsRUFBRSxPQUFPLE1BQU0sZUFBZSxLQUFLLHFLQUFDLHFCQUFxQixxQ0FBcUMsS0FBSyxXQUFXLHVEQUFDLFdBQVcsRUFBRSxPQUFPLE1BQU0sT0FBTyxLQUFLLHFLQUFDLDJDQUEyQyxxQ0FBcUMsS0FBSyxlQUFlLEdBQXFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9pbnRlcm5hbC9tZXNzYWdlT2JqZWN0cy5qcz85NDI4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtfX3Jlc3QgYXMgaX1mcm9tXCIuLi8uLi8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vQHJvbGx1cF9wbHVnaW4tdHlwZXNjcmlwdEAxMS4xLjZfcm9sbHVwQDQuMjQuNF90c2xpYkAyLjYuM190eXBlc2NyaXB0QDQuOS41L25vZGVfbW9kdWxlcy90c2xpYi90c2xpYi5lczYuanNcIjtpbXBvcnR7VVVJRCBhcyB0fWZyb21cIi4uL3B1YmxpYy91dWlkT2JqZWN0LmpzXCI7Y29uc3QgdT10PT57Y29uc3R7dXVpZDp1fT10LHM9aSh0LFtcInV1aWRcIl0pLG49bnVsbD09dT92b2lkIDA6dS50b1N0cmluZygpO3JldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30scykse3V1aWRBc1N0cmluZzpufSl9LHM9dT0+e2NvbnN0e3V1aWRBc1N0cmluZzpzfT11LG49aSh1LFtcInV1aWRBc1N0cmluZ1wiXSk7cmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSxuKSx7dXVpZDpzP25ldyB0KHMpOnZvaWQgMH0pfSxuPXU9Pntjb25zdHt1dWlkQXNTdHJpbmc6c309dSxuPWkodSxbXCJ1dWlkQXNTdHJpbmdcIl0pO3JldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sbikse3V1aWQ6cz9uZXcgdChzKTp2b2lkIDB9KX0scj10PT57Y29uc3R7dXVpZDp1fT10LHM9aSh0LFtcInV1aWRcIl0pLG49bnVsbD09dT92b2lkIDA6dS50b1N0cmluZygpO3JldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30scykse3V1aWRBc1N0cmluZzpufSl9O2V4cG9ydHtzIGFzIGRlc2VyaWFsaXplTWVzc2FnZVJlcXVlc3QsbiBhcyBkZXNlcmlhbGl6ZU1lc3NhZ2VSZXNwb25zZSx1IGFzIHNlcmlhbGl6ZU1lc3NhZ2VSZXF1ZXN0LHIgYXMgc2VyaWFsaXplTWVzc2FnZVJlc3BvbnNlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/messageObjects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/nestedAppAuthUtils.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/nestedAppAuthUtils.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nestedAppAuthTelemetryVersionNumber: () => (/* binding */ o),\n/* harmony export */   tryPolyfillWithNestedAppAuthBridge: () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _globalVars_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globalVars.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\nconst s=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"nestedAppAuthUtils\"),r=s.extend(\"tryPolyfillWithNestedAppAuthBridge\"),o=\"v2\";function i(t,s,i){var p;const u=r;if(_globalVars_js__WEBPACK_IMPORTED_MODULE_1__.GlobalVars.isFramelessWindow)return void u(\"Cannot polyfill nestedAppAuthBridge as current window is frameless\");if(!s)return void u(\"Cannot polyfill nestedAppAuthBridge as current window does not exist\");if(s.parent!==s.top)return void u(\"Default NAA bridge injection not supported in nested iframe. Use standalone NAA bridge instead.\");const a=(()=>{try{return JSON.parse(t)}catch(e){return null}})();if(!a||!(null===(p=a.supports)||void 0===p?void 0:p.nestedAppAuth))return void u(\"Cannot polyfill nestedAppAuthBridge as current hub does not support nested app auth\");const l=s;if(l.nestedAppAuthBridge)return void u(\"nestedAppAuthBridge already exists on current window, skipping polyfill\");const A=function(e,t){const s=d;if(!e)return s(\"nestedAppAuthBridge cannot be created as current window does not exist\"),null;const{onMessage:r,sendPostMessage:i}=t,p=e=>t=>r(t,e);return{addEventListener:(t,n)=>{\"message\"===t?e.addEventListener(t,p(n)):s(`Event ${t} is not supported by nestedAppAuthBridge`)},postMessage:e=>{const t=(()=>{try{return JSON.parse(e)}catch(e){return null}})();if(!t||\"object\"!=typeof t||\"NestedAppAuthRequest\"!==t.messageType)return void s(\"Unrecognized data format received by app, message being ignored. Message: %o\",e);const r=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getApiVersionTag)(o,\"nestedAppAuth.execute\");i(e,r)},removeEventListener:(t,n)=>{e.removeEventListener(t,p(n))}}}(l,i);A&&(l.nestedAppAuthBridge=A)}const d=s.extend(\"createNestedAppAuthBridge\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/nestedAppAuthUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backStackNavigateBackHelper: () => (/* binding */ j),\n/* harmony export */   configSetConfigHelper: () => (/* binding */ L),\n/* harmony export */   configSetValidityStateHelper: () => (/* binding */ T),\n/* harmony export */   convertAppNavigationParametersToNavigateToAppParams: () => (/* binding */ y),\n/* harmony export */   convertNavigateToAppParamsToAppNavigationParameters: () => (/* binding */ x),\n/* harmony export */   getConfigHelper: () => (/* binding */ C),\n/* harmony export */   getMruTabInstancesHelper: () => (/* binding */ k),\n/* harmony export */   getTabInstancesHelper: () => (/* binding */ S),\n/* harmony export */   initializeBackStackHelper: () => (/* binding */ R),\n/* harmony export */   isAppNavigationParametersObject: () => (/* binding */ O),\n/* harmony export */   navigateCrossDomainHelper: () => (/* binding */ P),\n/* harmony export */   pagesTelemetryVersionNumber: () => (/* binding */ h),\n/* harmony export */   returnFocusHelper: () => (/* binding */ I),\n/* harmony export */   setBackButtonPressHandler: () => (/* binding */ F),\n/* harmony export */   setCurrentFrameHelper: () => (/* binding */ B),\n/* harmony export */   shareDeepLinkHelper: () => (/* binding */ U),\n/* harmony export */   tabsNavigateToTabHelper: () => (/* binding */ v)\n/* harmony export */ });\n/* harmony import */ var _public_appId_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/appId.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/appId.js\");\n/* harmony import */ var _public_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../public/constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _public_pages_pages_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/pages/pages.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/pages.js\");\n/* harmony import */ var _public_runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../public/runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _childCommunication_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./childCommunication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/childCommunication.js\");\n/* harmony import */ var _communication_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _handlers_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _public_pages_backStack_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/pages/backStack.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/backStack.js\");\n/* harmony import */ var _public_pages_tabs_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/pages/tabs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/tabs.js\");\n/* harmony import */ var _public_pages_config_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/pages/config.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/config.js\");\nconst h=\"v2\";function P(t,o){return new Promise((s=>{if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.settings,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.remove,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.task,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.stage,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!(0,_public_pages_pages_js__WEBPACK_IMPORTED_MODULE_3__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;s((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndHandleStatusAndReasonWithDefaultError)(t,\"navigateCrossDomain\",\"Cross-origin navigation is only supported for URLs matching the pattern registered in the manifest.\",o))}))}function j(t){return new Promise((e=>{if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime),!(0,_public_pages_backStack_js__WEBPACK_IMPORTED_MODULE_5__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;e((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndHandleStatusAndReasonWithDefaultError)(t,\"navigateBack\",\"Back navigation is not supported in the current client or context.\"))}))}function v(t,e){return new Promise((i=>{if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime),!(0,_public_pages_tabs_js__WEBPACK_IMPORTED_MODULE_6__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;i((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndHandleStatusAndReasonWithDefaultError)(t,\"navigateToTab\",\"Invalid internalTabInstanceId and/or channelId were/was provided\",e))}))}function I(t,e){if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime),!(0,_public_pages_pages_js__WEBPACK_IMPORTED_MODULE_3__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(t,\"returnFocus\",[e])}function S(t,e){return new Promise((i=>{if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime),!(0,_public_pages_tabs_js__WEBPACK_IMPORTED_MODULE_6__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;i((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndUnwrap)(t,\"getTabInstances\",e))}))}function k(t,e){return new Promise((i=>{if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime),!(0,_public_pages_tabs_js__WEBPACK_IMPORTED_MODULE_6__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;i((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndUnwrap)(t,\"getMruTabInstances\",e))}))}function U(t,o){if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!(0,_public_pages_pages_js__WEBPACK_IMPORTED_MODULE_3__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(t,\"shareDeepLink\",[o.subPageId,o.subPageLabel,o.subPageWebUrl])}function B(t,o){if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content),!(0,_public_pages_pages_js__WEBPACK_IMPORTED_MODULE_3__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(t,\"setFrameContext\",[o])}function T(t,i){if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.settings,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.remove),!(0,_public_pages_config_js__WEBPACK_IMPORTED_MODULE_7__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)(t,\"settings.setValidityState\",[i])}function C(t){return new Promise((o=>{if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.settings,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.remove,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel),!(0,_public_pages_pages_js__WEBPACK_IMPORTED_MODULE_3__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;o((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndUnwrap)(t,\"settings.getSettings\"))}))}function L(t,i){return new Promise((o=>{if((0,_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.settings,_public_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel),!(0,_public_pages_config_js__WEBPACK_IMPORTED_MODULE_7__.isSupported)())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;o((0,_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndHandleStatusAndReason)(t,\"settings.setSettings\",i))}))}function O(e){return e.appId instanceof _public_appId_js__WEBPACK_IMPORTED_MODULE_8__.AppId}function x(e){return Object.assign(Object.assign({},e),{appId:new _public_appId_js__WEBPACK_IMPORTED_MODULE_8__.AppId(e.appId),webUrl:e.webUrl?new URL(e.webUrl):void 0})}function y(t){return Object.assign(Object.assign({},t),{appId:t.appId.toString(),webUrl:t.webUrl?t.webUrl.toString():void 0})}let D;function F(t){D=t}function R(){(0,_handlers_js__WEBPACK_IMPORTED_MODULE_9__.registerHandler)((0,_telemetry_js__WEBPACK_IMPORTED_MODULE_10__.getApiVersionTag)(\"v2\",\"pages.backStack.registerBackButtonPressHandler\"),\"backButtonPress\",A,!1)}function A(){D&&D()||((0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_11__.shouldEventBeRelayedToChild)()?(0,_childCommunication_js__WEBPACK_IMPORTED_MODULE_11__.sendMessageEventToChild)(\"backButtonPress\",[]):(0,_public_pages_backStack_js__WEBPACK_IMPORTED_MODULE_5__.navigateBack)())}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/responseHandler.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/responseHandler.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponseHandler: () => (/* binding */ e),\n/* harmony export */   SimpleTypeResponseHandler: () => (/* binding */ r)\n/* harmony export */ });\nclass e{}class r extends e{validate(e){return!0}deserialize(e){return e}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvcmVzcG9uc2VIYW5kbGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsU0FBUyxrQkFBa0IsWUFBWSxTQUFTLGVBQWUsVUFBc0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vcGFja2FnZXMvdGVhbXMtanMvc3JjL2ludGVybmFsL3Jlc3BvbnNlSGFuZGxlci5qcz8zYWFmIl0sInNvdXJjZXNDb250ZW50IjpbImNsYXNzIGV7fWNsYXNzIHIgZXh0ZW5kcyBle3ZhbGlkYXRlKGUpe3JldHVybiEwfWRlc2VyaWFsaXplKGUpe3JldHVybiBlfX1leHBvcnR7ZSBhcyBSZXNwb25zZUhhbmRsZXIsciBhcyBTaW1wbGVUeXBlUmVzcG9uc2VIYW5kbGVyfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/responseHandler.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getApiVersionTag: () => (/* binding */ s),\n/* harmony export */   getLogger: () => (/* binding */ u),\n/* harmony export */   isFollowingApiVersionTagFormat: () => (/* binding */ d),\n/* harmony export */   teamsJsInstanceIdentifier: () => (/* binding */ n)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_debug_4_3_5_node_modules_debug_src_browser_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/browser.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/debug@4.3.5/node_modules/debug/src/browser.js\");\n/* harmony import */ var _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../public/uuidObject.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/uuidObject.js\");\nconst n=new _public_uuidObject_js__WEBPACK_IMPORTED_MODULE_0__.UUID,o=_node_modules_pnpm_debug_4_3_5_node_modules_debug_src_browser_js__WEBPACK_IMPORTED_MODULE_1__.b.debug.formatArgs;_node_modules_pnpm_debug_4_3_5_node_modules_debug_src_browser_js__WEBPACK_IMPORTED_MODULE_1__.b.debug.formatArgs=function(t){t[0]=`(${(new Date).toISOString()}): ${t[0]} [${n.toString()}]`,o.call(this,t)};const r=_node_modules_pnpm_debug_4_3_5_node_modules_debug_src_browser_js__WEBPACK_IMPORTED_MODULE_1__.b.debug(\"teamsJs\");function u(t){return r.extend(t)}function s(t,e){return`${t}_${e}`}function d(t){return/^v\\d+_[\\w.]+$/.test(t)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvdGVsZW1ldHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFnSixZQUFZLHVEQUFDLEdBQUcsK0ZBQUMsa0JBQWtCLCtGQUFDLDhCQUE4QixTQUFTLHlCQUF5QixLQUFLLE1BQU0sR0FBRyxhQUFhLG1CQUFtQixRQUFRLCtGQUFDLGtCQUFrQixjQUFjLG1CQUFtQixnQkFBZ0IsU0FBUyxFQUFFLEdBQUcsRUFBRSxFQUFFLGNBQWMsOEJBQThJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9pbnRlcm5hbC90ZWxlbWV0cnkuanM/NDMyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7YiBhcyB0fWZyb21cIi4uLy4uLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9kZWJ1Z0A0LjMuNS9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2Jyb3dzZXIuanNcIjtpbXBvcnR7VVVJRCBhcyBlfWZyb21cIi4uL3B1YmxpYy91dWlkT2JqZWN0LmpzXCI7Y29uc3Qgbj1uZXcgZSxvPXQuZGVidWcuZm9ybWF0QXJnczt0LmRlYnVnLmZvcm1hdEFyZ3M9ZnVuY3Rpb24odCl7dFswXT1gKCR7KG5ldyBEYXRlKS50b0lTT1N0cmluZygpfSk6ICR7dFswXX0gWyR7bi50b1N0cmluZygpfV1gLG8uY2FsbCh0aGlzLHQpfTtjb25zdCByPXQuZGVidWcoXCJ0ZWFtc0pzXCIpO2Z1bmN0aW9uIHUodCl7cmV0dXJuIHIuZXh0ZW5kKHQpfWZ1bmN0aW9uIHModCxlKXtyZXR1cm5gJHt0fV8ke2V9YH1mdW5jdGlvbiBkKHQpe3JldHVybi9edlxcZCtfW1xcdy5dKyQvLnRlc3QodCl9ZXhwb3J0e3MgYXMgZ2V0QXBpVmVyc2lvblRhZyx1IGFzIGdldExvZ2dlcixkIGFzIGlzRm9sbG93aW5nQXBpVmVyc2lvblRhZ0Zvcm1hdCxuIGFzIHRlYW1zSnNJbnN0YW5jZUlkZW50aWZpZXJ9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/typeCheckUtilities.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/typeCheckUtilities.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNullOrUndefined: () => (/* binding */ n)\n/* harmony export */ });\nfunction n(n){return null==n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvaW50ZXJuYWwvdHlwZUNoZWNrVXRpbGl0aWVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxjQUFjLGVBQThDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9pbnRlcm5hbC90eXBlQ2hlY2tVdGlsaXRpZXMuanM/YjFlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBuKG4pe3JldHVybiBudWxsPT1ufWV4cG9ydHtuIGFzIGlzTnVsbE9yVW5kZWZpbmVkfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/typeCheckUtilities.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64ToBlob: () => (/* binding */ h),\n/* harmony export */   callCallbackWithErrorOrResultFromPromiseAndReturnPromise: () => (/* binding */ s),\n/* harmony export */   callCallbackWithErrorOrResultOrNullFromPromiseAndReturnPromise: () => (/* binding */ l),\n/* harmony export */   callCallbackWithSdkErrorFromPromiseAndReturnPromise: () => (/* binding */ f),\n/* harmony export */   compareSDKVersions: () => (/* binding */ i),\n/* harmony export */   createTeamsAppLink: () => (/* binding */ p),\n/* harmony export */   deepFreeze: () => (/* binding */ u),\n/* harmony export */   fullyQualifyUrlString: () => (/* binding */ E),\n/* harmony export */   generateGUID: () => (/* binding */ c),\n/* harmony export */   getBase64StringFromBlob: () => (/* binding */ b),\n/* harmony export */   getCurrentTimestamp: () => (/* binding */ O),\n/* harmony export */   getGenericOnCompleteHandler: () => (/* binding */ o),\n/* harmony export */   hasScriptTags: () => (/* binding */ v),\n/* harmony export */   inServerSideRenderingEnvironment: () => (/* binding */ g),\n/* harmony export */   isHostAdaptiveCardSchemaVersionUnsupported: () => (/* binding */ d),\n/* harmony export */   isPrimitiveOrPlainObject: () => (/* binding */ S),\n/* harmony export */   isValidHttpsURL: () => (/* binding */ m),\n/* harmony export */   runWithTimeout: () => (/* binding */ a),\n/* harmony export */   ssrSafeWindow: () => (/* binding */ w),\n/* harmony export */   validateId: () => (/* binding */ y),\n/* harmony export */   validateUrl: () => (/* binding */ j),\n/* harmony export */   validateUuid: () => (/* binding */ I)\n/* harmony export */ });\n/* harmony import */ var _virtual_polyfill_node_buffer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../_virtual/_polyfill-node.buffer.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/_virtual/_polyfill-node.buffer.js\");\n/* harmony import */ var _public_constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../public/constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _node_modules_pnpm_uuid_9_0_1_node_modules_uuid_dist_esm_browser_v4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/v4.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _node_modules_pnpm_uuid_9_0_1_node_modules_uuid_dist_esm_browser_validate_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/validate.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/validate.js\");\nfunction o(t){return(t,e)=>{if(!t)throw new Error(e)}}function i(t,e){if(\"string\"!=typeof t||\"string\"!=typeof e)return NaN;const r=t.split(\".\"),n=e.split(\".\");function o(t){return/^\\d+$/.test(t)}if(!r.every(o)||!n.every(o))return NaN;for(;r.length<n.length;)r.push(\"0\");for(;n.length<r.length;)n.push(\"0\");for(let t=0;t<r.length;++t)if(Number(r[t])!=Number(n[t]))return Number(r[t])>Number(n[t])?1:-1;return 0}function c(){return (0,_node_modules_pnpm_uuid_9_0_1_node_modules_uuid_dist_esm_browser_v4_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])()}function u(t){return Object.keys(t).forEach((e=>{null!==t[e]&&void 0!==t[e]&&\"object\"==typeof t[e]&&u(t[e])})),Object.freeze(t)}function s(t,e,...r){const n=t(...r);return n.then((t=>{e&&e(void 0,t)})).catch((t=>{e&&e(t)})),n}function f(t,e,...r){const n=t(...r);return n.then((()=>{e&&e(null)})).catch((t=>{e&&e(t)})),n}function l(t,e,...r){const n=t(...r);return n.then((t=>{e&&e(null,t)})).catch((t=>{e&&e(t,null)})),n}function a(t,e,r){return new Promise(((n,o)=>{const i=setTimeout(o,e,r);t().then((t=>{clearTimeout(i),n(t)})).catch((t=>{clearTimeout(i),o(t)}))}))}function p(t){const e=new URL(\"https://teams.microsoft.com/l/entity/\"+encodeURIComponent(t.appId.toString())+\"/\"+encodeURIComponent(t.pageId));return t.webUrl&&e.searchParams.append(\"webUrl\",t.webUrl.toString()),(t.chatId||t.channelId||t.subPageId)&&e.searchParams.append(\"context\",JSON.stringify({chatId:t.chatId,channelId:t.channelId,subEntityId:t.subPageId})),e.toString()}function d(t){return!(i(`${t.majorVersion}.${t.minorVersion}`,`${_public_constants_js__WEBPACK_IMPORTED_MODULE_1__.minAdaptiveCardVersion.majorVersion}.${_public_constants_js__WEBPACK_IMPORTED_MODULE_1__.minAdaptiveCardVersion.minorVersion}`)>=0)}function m(t){return\"https:\"===t.protocol}function h(e,r){return new Promise(((n,o)=>{if(e||o(\"MimeType cannot be null or empty.\"),r||o(\"Base64 string cannot be null or empty.\"),e.startsWith(\"image/\")){const t=atob(r),o=new Uint8Array(t.length);for(let e=0;e<t.length;e++)o[e]=t.charCodeAt(e);n(new Blob([o],{type:e}))}const i=_virtual_polyfill_node_buffer_js__WEBPACK_IMPORTED_MODULE_2__.Buffer.from(r,\"base64\").toString();n(new Blob([i],{type:e}))}))}function b(t){return new Promise(((e,r)=>{0===t.size&&r(new Error(\"Blob cannot be empty.\"));const n=new FileReader;n.onloadend=()=>{n.result?e(n.result.toString().split(\",\")[1]):r(new Error(\"Failed to read the blob\"))},n.onerror=()=>{r(n.error)},n.readAsDataURL(t)}))}function w(){if(g())throw new Error(\"window object undefined at SSR check\");return window}function g(){return\"undefined\"==typeof window}function y(t,e){if(v(t)||!function(t){return t.length<256&&t.length>4}(t)||!function(t){for(let e=0;e<t.length;e++){const r=t.charCodeAt(e);if(r<32||r>126)return!1}return!0}(t))throw e||new Error(\"id is not valid.\")}function j(t,e){const r=t.toString().toLocaleLowerCase();if(v(r))throw new Error(\"Invalid Url\");if(r.length>2048)throw new Error(\"Url exceeds the maximum size of 2048 characters\");if(!m(t))throw new Error(\"Url should be a valid https url\")}function E(t){const e=document.createElement(\"a\");return e.href=t,new URL(e.href)}function v(t){return new RegExp(`${/<script[^>]*>|&lt;script[^&]*&gt;|%3Cscript[^%]*%3E/gi.source}|${/<\\/script[^>]*>|&lt;\\/script[^&]*&gt;|%3C\\/script[^%]*%3E/gi.source}`,\"gi\").test(t)}function I(t){if(!t)throw new Error(\"id must not be empty\");if(!1===(0,_node_modules_pnpm_uuid_9_0_1_node_modules_uuid_dist_esm_browser_validate_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(t))throw new Error(\"id must be a valid UUID\")}const U=!!performance&&\"now\"in performance;function O(){return U?performance.now()+performance.timeOrigin:void 0}function S(t,e=0){if(e>1e3)return!1;if(void 0===t||\"boolean\"==typeof t||\"number\"==typeof t||\"bigint\"==typeof t||\"string\"==typeof t||null===t)return!0;if(Array.isArray(t))return t.every((t=>S(t,e+1)));return!(\"object\"!=typeof t||\"[object Object]\"!==Object.prototype.toString.call(t)||Object.getPrototypeOf(t)!==Object.prototype&&null!==Object.getPrototypeOf(t))&&Object.keys(t).every((r=>S(t[r],e+1)))}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/validOrigins.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/validOrigins.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prefetchOriginsFromCDN: () => (/* binding */ c),\n/* harmony export */   validateOrigin: () => (/* binding */ g)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/constants.js\");\n/* harmony import */ var _globalVars_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globalVars.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\");\n/* harmony import */ var _telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\nlet a=[];const u=(0,_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"validateOrigin\");function c(){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this,void 0,void 0,(function*(){yield d()}))}function d(e=!1){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this,void 0,void 0,(function*(){if(0!==a.length&&!e)return a;if((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.inServerSideRenderingEnvironment)())return a=_constants_js__WEBPACK_IMPORTED_MODULE_3__.validOriginsFallback,_constants_js__WEBPACK_IMPORTED_MODULE_3__.validOriginsFallback;{u(\"Initiating fetch call to acquire valid origins list from CDN\");const i=new AbortController,e=setTimeout((()=>i.abort()),_constants_js__WEBPACK_IMPORTED_MODULE_3__.ORIGIN_LIST_FETCH_TIMEOUT_IN_MS);return fetch(_constants_js__WEBPACK_IMPORTED_MODULE_3__.validOriginsCdnEndpoint,{signal:i.signal}).then((i=>{if(clearTimeout(e),!i.ok)throw new Error(\"Invalid Response from Fetch Call\");return u(\"Fetch call completed and retrieved valid origins list from CDN\"),i.json().then((i=>{if(function(i){let t=JSON.parse(i);try{t=JSON.parse(i)}catch(i){return!1}if(!t.validOrigins)return!1;for(const i of t.validOrigins)try{new URL(\"https://\"+i)}catch(t){return u(\"isValidOriginsFromCDN call failed to validate origin: %s\",i),!1}return!0}(JSON.stringify(i)))return a=i.validOrigins,a;throw new Error(\"Valid origins list retrieved from CDN is invalid\")}))})).catch((i=>(\"AbortError\"===i.name?u(`validOrigins fetch call to CDN failed due to Timeout of ${_constants_js__WEBPACK_IMPORTED_MODULE_3__.ORIGIN_LIST_FETCH_TIMEOUT_IN_MS} ms. Defaulting to fallback list`):u(\"validOrigins fetch call to CDN failed with error: %s. Defaulting to fallback list\",i),a=_constants_js__WEBPACK_IMPORTED_MODULE_3__.validOriginsFallback,a)))}}))}function f(i,t){if(\"*.\"===i.substring(0,2)){const r=i.substring(1);if(t.length>r.length&&t.split(\".\").length===r.split(\".\").length&&t.substring(t.length-r.length)===r)return!0}else if(i===t)return!0;return!1}function g(i,t){return d(t).then((t=>{if(!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidHttpsURL)(i))return u(\"Origin %s is invalid because it is not using https protocol. Protocol being used: %s\",i,i.protocol),!1;const r=i.host;if(t.some((i=>f(i,r))))return!0;for(const i of _globalVars_js__WEBPACK_IMPORTED_MODULE_4__.GlobalVars.additionalValidOrigins){if(f(\"https://\"===i.substring(0,8)?i.substring(8):i,r))return!0}return u(\"Origin %s is invalid because it is not an origin approved by this library or included in the call to app.initialize.\\nOrigins approved by this library: %o\\nOrigins included in app.initialize: %o\",i,t,_globalVars_js__WEBPACK_IMPORTED_MODULE_4__.GlobalVars.additionalValidOrigins),!1}))}c();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/validOrigins.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/private/messageChannels/dataLayer.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/private/messageChannels/dataLayer.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _clearDataLayerPort: () => (/* binding */ u),\n/* harmony export */   getDataLayerPort: () => (/* binding */ m),\n/* harmony export */   isSupported: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _public_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _public_runtime_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../public/runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nlet s;const l=(0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"messageChannels.dataLayer\");function m(){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this,void 0,void 0,(function*(){if(s)return l(\"Returning dataLayer port from cache\"),s;if(!p())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;return s=yield (0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_3__.requestPortFromParentWithVersion)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getApiVersionTag)(\"v1\",\"messageChannels.dataLayer.getDataLayerPort\"),\"messageChannels.dataLayer.getDataLayerPort\"),s}))}function p(){var t;return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime)||!(null===(t=_public_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime.supports.messageChannels)||void 0===t?void 0:t.dataLayer))}function u(){s=void 0}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/private/messageChannels/dataLayer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/private/messageChannels/telemetry.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/private/messageChannels/telemetry.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _clearTelemetryPort: () => (/* binding */ a),\n/* harmony export */   getTelemetryPort: () => (/* binding */ p),\n/* harmony export */   isSupported: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _public_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _public_runtime_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../public/runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nlet l;const m=(0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"messageChannels.telemetry\");function p(){return (0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this,void 0,void 0,(function*(){if(l)return m(\"Returning telemetry port from cache\"),l;if(!u())throw _public_constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;return l=yield (0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_3__.requestPortFromParentWithVersion)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getApiVersionTag)(\"v1\",\"messageChannels.telemetry.getTelemetryPort\"),\"messageChannels.telemetry.getTelemetryPort\"),l}))}function u(){var e;return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__.ensureInitialized)(_public_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime)||!(null===(e=_public_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime.supports.messageChannels)||void 0===e?void 0:e.telemetry))}function a(){l=void 0}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/private/messageChannels/telemetry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/appId.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/appId.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppId: () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _internal_idValidation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../internal/idValidation.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/idValidation.js\");\n/* harmony import */ var _validatedSafeString_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validatedSafeString.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/validatedSafeString.js\");\nclass i extends _validatedSafeString_js__WEBPACK_IMPORTED_MODULE_0__.ValidatedSafeString{constructor(r){super(r),(0,_internal_idValidation_js__WEBPACK_IMPORTED_MODULE_1__.validateStringLength)(r)}toJSON(){return{appIdAsString:this.toString()}}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL2FwcElkLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrSSxnQkFBZ0Isd0VBQUMsQ0FBQyxlQUFlLFNBQVMsK0VBQUMsSUFBSSxTQUFTLE9BQU8sZ0NBQW1EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9wdWJsaWMvYXBwSWQuanM/ZjA0ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7dmFsaWRhdGVTdHJpbmdMZW5ndGggYXMgdH1mcm9tXCIuLi9pbnRlcm5hbC9pZFZhbGlkYXRpb24uanNcIjtpbXBvcnR7VmFsaWRhdGVkU2FmZVN0cmluZyBhcyByfWZyb21cIi4vdmFsaWRhdGVkU2FmZVN0cmluZy5qc1wiO2NsYXNzIGkgZXh0ZW5kcyBye2NvbnN0cnVjdG9yKHIpe3N1cGVyKHIpLHQocil9dG9KU09OKCl7cmV0dXJue2FwcElkQXNTdHJpbmc6dGhpcy50b1N0cmluZygpfX19ZXhwb3J0e2kgYXMgQXBwSWR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/appId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/app/app.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/app/app.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExpectedFailureReason: () => (/* binding */ b),\n/* harmony export */   FailedReason: () => (/* binding */ O),\n/* harmony export */   Messages: () => (/* binding */ N),\n/* harmony export */   _initialize: () => (/* binding */ A),\n/* harmony export */   _uninitialize: () => (/* binding */ z),\n/* harmony export */   getContext: () => (/* binding */ D),\n/* harmony export */   getFrameContext: () => (/* binding */ x),\n/* harmony export */   initialize: () => (/* binding */ L),\n/* harmony export */   isInitialized: () => (/* binding */ F),\n/* harmony export */   lifecycle: () => (/* reexport module object */ _lifecycle_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   notifyAppLoaded: () => (/* binding */ k),\n/* harmony export */   notifyExpectedFailure: () => (/* binding */ E),\n/* harmony export */   notifyFailure: () => (/* binding */ V),\n/* harmony export */   notifySuccess: () => (/* binding */ U),\n/* harmony export */   openLink: () => (/* binding */ W),\n/* harmony export */   registerHostToAppPerformanceMetricsHandler: () => (/* binding */ M),\n/* harmony export */   registerOnThemeChangeHandler: () => (/* binding */ H)\n/* harmony export */ });\n/* harmony import */ var _internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../internal/appHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/appHelpers.js\");\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/globalVars.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\");\n/* harmony import */ var _internal_handlers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../internal/handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _internal_utils_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../internal/utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\n/* harmony import */ var _appId_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../appId.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/appId.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/version.js\");\n/* harmony import */ var _lifecycle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lifecycle.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/app/lifecycle.js\");\n/* harmony import */ var _private_messageChannels_telemetry_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../private/messageChannels/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/private/messageChannels/telemetry.js\");\n/* harmony import */ var _private_messageChannels_dataLayer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../private/messageChannels/dataLayer.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/private/messageChannels/dataLayer.js\");\nconst j=\"v2\",w=(0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getLogger)(\"app\"),N={AppLoaded:\"appInitialization.appLoaded\",Success:\"appInitialization.success\",Failure:\"appInitialization.failure\",ExpectedFailure:\"appInitialization.expectedFailure\"};var O,b;function F(){return _internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializeCompleted}function x(){return _internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.frameContext}function L(i){return (0,_internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_3__.appInitializeHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(j,\"app.initialize\"),i)}function A(e){_internal_communication_js__WEBPACK_IMPORTED_MODULE_4__.Communication.currentWindow=e}function z(){_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializeCalled&&((0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_5__.uninitializeHandlers)(),_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializeCalled=!1,_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializeCompleted=!1,_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.initializePromise=void 0,_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.additionalValidOrigins=[],_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.frameContext=void 0,_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.hostClientType=void 0,_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.isFramelessWindow=!1,(0,_private_messageChannels_telemetry_js__WEBPACK_IMPORTED_MODULE_6__._clearTelemetryPort)(),(0,_private_messageChannels_dataLayer_js__WEBPACK_IMPORTED_MODULE_7__._clearDataLayerPort)(),(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_4__.uninitializeCommunication)())}function D(){return new Promise((e=>{(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__.ensureInitializeCalled)(),e((0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendAndUnwrap)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(j,\"app.getContext\"),\"getContext\"))})).then((e=>function(e){var i;const t={actionInfo:e.actionInfo,app:{locale:e.locale,sessionId:e.appSessionId?e.appSessionId:\"\",theme:e.theme?e.theme:\"default\",iconPositionVertical:e.appIconPosition,osLocaleInfo:e.osLocaleInfo,parentMessageId:e.parentMessageId,userClickTime:e.userClickTime,userClickTimeV2:e.userClickTimeV2,userFileOpenPreference:e.userFileOpenPreference,host:{name:e.hostName?e.hostName:_constants_js__WEBPACK_IMPORTED_MODULE_9__.HostName.teams,clientType:e.hostClientType?e.hostClientType:_constants_js__WEBPACK_IMPORTED_MODULE_9__.HostClientType.web,sessionId:e.sessionId?e.sessionId:\"\",ringId:e.ringId},appLaunchId:e.appLaunchId,appId:e.appId?new _appId_js__WEBPACK_IMPORTED_MODULE_10__.AppId(e.appId):void 0,manifestVersion:e.manifestVersion},page:{id:e.entityId,frameContext:e.frameContext?e.frameContext:_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_2__.GlobalVars.frameContext,subPageId:e.subEntityId,isFullScreen:e.isFullScreen,isMultiWindow:e.isMultiWindow,isBackgroundLoad:e.isBackgroundLoad,sourceOrigin:e.sourceOrigin},user:{id:null!==(i=e.userObjectId)&&void 0!==i?i:\"\",displayName:e.userDisplayName,isCallingAllowed:e.isCallingAllowed,isPSTNCallingAllowed:e.isPSTNCallingAllowed,licenseType:e.userLicenseType,loginHint:e.loginHint,userPrincipalName:e.userPrincipalName,tenant:e.tid?{id:e.tid,teamsSku:e.tenantSKU}:void 0},channel:e.channelId?{id:e.channelId,displayName:e.channelName,relativeUrl:e.channelRelativeUrl,membershipType:e.channelType,defaultOneNoteSectionId:e.defaultOneNoteSectionId,ownerGroupId:e.hostTeamGroupId,ownerTenantId:e.hostTeamTenantId}:void 0,chat:e.chatId?{id:e.chatId}:void 0,meeting:e.meetingId?{id:e.meetingId}:void 0,sharepoint:e.sharepoint,team:e.teamId?{internalId:e.teamId,displayName:e.teamName,type:e.teamType,groupId:e.groupId,templateId:e.teamTemplateId,isArchived:e.isTeamArchived,userRole:e.userTeamRole}:void 0,sharePointSite:e.teamSiteUrl||e.teamSiteDomain||e.teamSitePath||e.mySitePath||e.mySiteDomain?{teamSiteUrl:e.teamSiteUrl,teamSiteDomain:e.teamSiteDomain,teamSitePath:e.teamSitePath,teamSiteId:e.teamSiteId,mySitePath:e.mySitePath,mySiteDomain:e.mySiteDomain}:void 0,dialogParameters:e.dialogParameters||{}};return t}(e)))}function k(){(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__.ensureInitializeCalled)(),(0,_internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_3__.notifyAppLoadedHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(j,\"app.notifyAppLoaded\"))}function U(){return (0,_internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_3__.notifySuccessHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(j,\"app.notifySuccess\"))}function V(e){(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__.ensureInitializeCalled)(),(0,_internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_3__.notifyFailureHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(j,\"app.notifyFailure\"),e)}function E(e){(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__.ensureInitializeCalled)(),(0,_internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_3__.notifyExpectedFailureHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(j,\"app.notifyExpectedFailure\"),e)}function H(e){(0,_internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_3__.registerOnThemeChangeHandlerHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(j,\"app.registerOnThemeChangeHandler\"),e)}function M(e){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_5__.registerHostToAppPerformanceMetricsHandler)(e)}function W(e){return (0,_internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_3__.openLinkHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(j,\"app.openLink\"),e)}!function(e){e.AuthFailed=\"AuthFailed\",e.Timeout=\"Timeout\",e.Other=\"Other\"}(O||(O={})),function(e){e.PermissionError=\"PermissionError\",e.NotFound=\"NotFound\",e.Throttling=\"Throttling\",e.Offline=\"Offline\",e.Other=\"Other\"}(b||(b={})),w(\"teamsjs instance is version %s, starting at %s UTC (%s local)\",_version_js__WEBPACK_IMPORTED_MODULE_11__.version,(new Date).toISOString(),(new Date).toLocaleString()),function(){if((0,_internal_utils_js__WEBPACK_IMPORTED_MODULE_12__.inServerSideRenderingEnvironment)())return;const e=document.getElementsByTagName(\"script\"),i=e&&e[e.length-1]&&e[e.length-1].src,t=\"Today, teamsjs can only be used from a single script or you may see undefined behavior. This log line is used to help detect cases where teamsjs is loaded multiple times -- it is always written. The presence of the log itself does not indicate a multi-load situation, but multiples of these log lines will. If you would like to use teamjs from more than one script at the same time, please open an issue at https://github.com/OfficeDev/microsoft-teams-library-js/issues\";i&&0!==i.length?w(\"teamsjs is being used from %s. %s\",i,t):w(\"teamsjs is being used from a script tag embedded directly in your html. %s\",t)}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/app/app.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/app/lifecycle.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/app/lifecycle.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerBeforeSuspendOrTerminateHandler: () => (/* binding */ i),\n/* harmony export */   registerOnResumeHandler: () => (/* binding */ a)\n/* harmony export */ });\n/* harmony import */ var _internal_handlers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction i(r){if(!r)throw new Error(\"[app.lifecycle.registerBeforeSuspendOrTerminateHandler] Handler cannot be null\");(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime),(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_2__.registerBeforeSuspendOrTerminateHandler)(r)}function a(e){if(!e)throw new Error(\"[app.lifecycle.registerOnResumeHandler] Handler cannot be null\");(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime),(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_2__.registerOnResumeHandler)(e)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL2FwcC9saWZlY3ljbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNk4sY0FBYyx3R0FBd0csNEVBQUMsQ0FBQyxnREFBQyxFQUFFLDhGQUFDLElBQUksY0FBYyx3RkFBd0YsNEVBQUMsQ0FBQyxnREFBQyxFQUFFLDhFQUFDLElBQXNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9wdWJsaWMvYXBwL2xpZmVjeWNsZS5qcz8xOGE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtyZWdpc3RlckJlZm9yZVN1c3BlbmRPclRlcm1pbmF0ZUhhbmRsZXIgYXMgZSxyZWdpc3Rlck9uUmVzdW1lSGFuZGxlciBhcyByfWZyb21cIi4uLy4uL2ludGVybmFsL2hhbmRsZXJzLmpzXCI7aW1wb3J0e2Vuc3VyZUluaXRpYWxpemVkIGFzIG59ZnJvbVwiLi4vLi4vaW50ZXJuYWwvaW50ZXJuYWxBUElzLmpzXCI7aW1wb3J0e3J1bnRpbWUgYXMgdH1mcm9tXCIuLi9ydW50aW1lLmpzXCI7ZnVuY3Rpb24gaShyKXtpZighcil0aHJvdyBuZXcgRXJyb3IoXCJbYXBwLmxpZmVjeWNsZS5yZWdpc3RlckJlZm9yZVN1c3BlbmRPclRlcm1pbmF0ZUhhbmRsZXJdIEhhbmRsZXIgY2Fubm90IGJlIG51bGxcIik7bih0KSxlKHIpfWZ1bmN0aW9uIGEoZSl7aWYoIWUpdGhyb3cgbmV3IEVycm9yKFwiW2FwcC5saWZlY3ljbGUucmVnaXN0ZXJPblJlc3VtZUhhbmRsZXJdIEhhbmRsZXIgY2Fubm90IGJlIG51bGxcIik7bih0KSxyKGUpfWV4cG9ydHtpIGFzIHJlZ2lzdGVyQmVmb3JlU3VzcGVuZE9yVGVybWluYXRlSGFuZGxlcixhIGFzIHJlZ2lzdGVyT25SZXN1bWVIYW5kbGVyfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/app/lifecycle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChannelType: () => (/* binding */ r),\n/* harmony export */   DialogDimension: () => (/* binding */ t),\n/* harmony export */   FrameContexts: () => (/* binding */ a),\n/* harmony export */   HostClientType: () => (/* binding */ o),\n/* harmony export */   HostName: () => (/* binding */ s),\n/* harmony export */   TaskModuleDimension: () => (/* binding */ t),\n/* harmony export */   TeamType: () => (/* binding */ i),\n/* harmony export */   UserTeamRole: () => (/* binding */ n),\n/* harmony export */   errorInvalidCount: () => (/* binding */ l),\n/* harmony export */   errorInvalidResponse: () => (/* binding */ c),\n/* harmony export */   errorNotSupportedOnPlatform: () => (/* binding */ m),\n/* harmony export */   minAdaptiveCardVersion: () => (/* binding */ d),\n/* harmony export */   teamsMinAdaptiveCardVersion: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _interfaces_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interfaces.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/interfaces.js\");\nvar o,s,a,i,n,t,r;!function(e){e.desktop=\"desktop\",e.web=\"web\",e.android=\"android\",e.ios=\"ios\",e.ipados=\"ipados\",e.macos=\"macos\",e.visionOS=\"visionOS\",e.rigel=\"rigel\",e.surfaceHub=\"surfaceHub\",e.teamsRoomsWindows=\"teamsRoomsWindows\",e.teamsRoomsAndroid=\"teamsRoomsAndroid\",e.teamsPhones=\"teamsPhones\",e.teamsDisplays=\"teamsDisplays\"}(o||(o={})),function(e){e.office=\"Office\",e.outlook=\"Outlook\",e.outlookWin32=\"OutlookWin32\",e.orange=\"Orange\",e.places=\"Places\",e.teams=\"Teams\",e.teamsModern=\"TeamsModern\"}(s||(s={})),function(e){e.settings=\"settings\",e.content=\"content\",e.authentication=\"authentication\",e.remove=\"remove\",e.task=\"task\",e.sidePanel=\"sidePanel\",e.stage=\"stage\",e.meetingStage=\"meetingStage\"}(a||(a={})),function(e){e[e.Standard=0]=\"Standard\",e[e.Edu=1]=\"Edu\",e[e.Class=2]=\"Class\",e[e.Plc=3]=\"Plc\",e[e.Staff=4]=\"Staff\"}(i||(i={})),function(e){e[e.Admin=0]=\"Admin\",e[e.User=1]=\"User\",e[e.Guest=2]=\"Guest\"}(n||(n={})),function(e){e.Large=\"large\",e.Medium=\"medium\",e.Small=\"small\"}(t||(t={})),function(e){e.Regular=\"Regular\",e.Private=\"Private\",e.Shared=\"Shared\"}(r||(r={}));const m={errorCode:_interfaces_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.NOT_SUPPORTED_ON_PLATFORM},d={majorVersion:1,minorVersion:5},u={adaptiveCardSchemaVersion:{majorVersion:1,minorVersion:5}},l=new Error(\"Invalid input count: Must supply a valid image count (limit of 10).\"),c=new Error(\"Invalid response: Received more images than the specified max limit in the response.\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/adaptiveCard/adaptiveCard.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/adaptiveCard/adaptiveCard.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bot: () => (/* reexport module object */ _bot_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   isSupported: () => (/* binding */ f),\n/* harmony export */   open: () => (/* binding */ l)\n/* harmony export */ });\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../internal/dialogHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _internal_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../internal/utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _bot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bot.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/adaptiveCard/bot.js\");\nfunction l(i,p){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_1__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_2__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_3__.FrameContexts.content,_constants_js__WEBPACK_IMPORTED_MODULE_3__.FrameContexts.sidePanel,_constants_js__WEBPACK_IMPORTED_MODULE_3__.FrameContexts.meetingStage),!f())throw _constants_js__WEBPACK_IMPORTED_MODULE_3__.errorNotSupportedOnPlatform;const l=(0,_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_4__.getDialogInfoFromAdaptiveCardDialogInfo)(i);(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_5__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_4__.dialogTelemetryVersionNumber,\"dialog.adaptiveCard.open\"),\"tasks.startTask\",[l],((o,t)=>{null==p||p({err:o,result:t})}))}function f(){const o=_runtime_js__WEBPACK_IMPORTED_MODULE_2__.runtime.hostVersionsInfo&&_runtime_js__WEBPACK_IMPORTED_MODULE_2__.runtime.hostVersionsInfo.adaptiveCardSchemaVersion&&!(0,_internal_utils_js__WEBPACK_IMPORTED_MODULE_7__.isHostAdaptiveCardSchemaVersionUnsupported)(_runtime_js__WEBPACK_IMPORTED_MODULE_2__.runtime.hostVersionsInfo.adaptiveCardSchemaVersion);return (0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_1__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_2__.runtime)&&void 0!==(o&&_runtime_js__WEBPACK_IMPORTED_MODULE_2__.runtime.supports.dialog&&_runtime_js__WEBPACK_IMPORTED_MODULE_2__.runtime.supports.dialog.card)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/adaptiveCard/adaptiveCard.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/adaptiveCard/bot.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/adaptiveCard/bot.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupported: () => (/* binding */ l),\n/* harmony export */   open: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../internal/dialogHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _internal_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../internal/utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction p(i,p){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!l())throw _constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;const d=(0,_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_3__.getDialogInfoFromBotAdaptiveCardDialogInfo)(i);(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_4__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_5__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_3__.dialogTelemetryVersionNumber,\"dialog.adaptiveCard.bot.open\"),\"tasks.startTask\",[d],((o,t)=>{null==p||p({err:o,result:t})}))}function l(){const o=_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.hostVersionsInfo&&_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.hostVersionsInfo.adaptiveCardSchemaVersion&&!(0,_internal_utils_js__WEBPACK_IMPORTED_MODULE_6__.isHostAdaptiveCardSchemaVersionUnsupported)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.hostVersionsInfo.adaptiveCardSchemaVersion);return (0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime)&&void 0!==(o&&_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.supports.dialog&&_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.supports.dialog.card&&_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.supports.dialog.card.bot)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/adaptiveCard/bot.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/dialog.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/dialog.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adaptiveCard: () => (/* reexport module object */ _adaptiveCard_adaptiveCard_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   initialize: () => (/* binding */ p),\n/* harmony export */   isSupported: () => (/* binding */ l),\n/* harmony export */   update: () => (/* reexport module object */ _update_js__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   url: () => (/* reexport module object */ _url_url_js__WEBPACK_IMPORTED_MODULE_2__)\n/* harmony export */ });\n/* harmony import */ var _internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../internal/dialogHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js\");\n/* harmony import */ var _internal_handlers_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../internal/handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _adaptiveCard_adaptiveCard_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./adaptiveCard/adaptiveCard.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/adaptiveCard/adaptiveCard.js\");\n/* harmony import */ var _update_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./update.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/update.js\");\n/* harmony import */ var _url_url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./url/url.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/url.js\");\nfunction p(){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_3__.registerHandler)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__.dialogTelemetryVersionNumber,\"dialog.registerMessageForChildHandler\"),\"messageForChild\",_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__.handleDialogMessage,!1)}function l(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_6__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_7__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_7__.runtime.supports.dialog)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL2RpYWxvZy9kaWFsb2cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUE0ZixhQUFhLHNFQUFDLENBQUMsd0VBQUMsQ0FBQyxvRkFBQyw0REFBNEQsMkVBQUMsS0FBSyxhQUFhLFNBQVMsNEVBQUMsQ0FBQyxnREFBQyxJQUFJLGdEQUFDLGtCQUEyRCIsInNvdXJjZXMiOlsid2VicGFjazovL3FoYXJtb255LXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL2RpYWxvZy9kaWFsb2cuanM/MTBlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7aGFuZGxlRGlhbG9nTWVzc2FnZSBhcyByLGRpYWxvZ1RlbGVtZXRyeVZlcnNpb25OdW1iZXIgYXMgb31mcm9tXCIuLi8uLi9pbnRlcm5hbC9kaWFsb2dIZWxwZXJzLmpzXCI7aW1wb3J0e3JlZ2lzdGVySGFuZGxlciBhcyB0fWZyb21cIi4uLy4uL2ludGVybmFsL2hhbmRsZXJzLmpzXCI7aW1wb3J0e2Vuc3VyZUluaXRpYWxpemVkIGFzIGV9ZnJvbVwiLi4vLi4vaW50ZXJuYWwvaW50ZXJuYWxBUElzLmpzXCI7aW1wb3J0e2dldEFwaVZlcnNpb25UYWcgYXMgaX1mcm9tXCIuLi8uLi9pbnRlcm5hbC90ZWxlbWV0cnkuanNcIjtpbXBvcnR7cnVudGltZSBhcyBhfWZyb21cIi4uL3J1bnRpbWUuanNcIjtpbXBvcnQqYXMgcyBmcm9tXCIuL2FkYXB0aXZlQ2FyZC9hZGFwdGl2ZUNhcmQuanNcIjtleHBvcnR7cyBhcyBhZGFwdGl2ZUNhcmR9O2ltcG9ydCphcyBtIGZyb21cIi4vdXBkYXRlLmpzXCI7ZXhwb3J0e20gYXMgdXBkYXRlfTtpbXBvcnQqYXMgbiBmcm9tXCIuL3VybC91cmwuanNcIjtleHBvcnR7biBhcyB1cmx9O2Z1bmN0aW9uIHAoKXt0KGkobyxcImRpYWxvZy5yZWdpc3Rlck1lc3NhZ2VGb3JDaGlsZEhhbmRsZXJcIiksXCJtZXNzYWdlRm9yQ2hpbGRcIixyLCExKX1mdW5jdGlvbiBsKCl7cmV0dXJuISghZShhKXx8IWEuc3VwcG9ydHMuZGlhbG9nKX1leHBvcnR7cCBhcyBpbml0aWFsaXplLGwgYXMgaXNTdXBwb3J0ZWR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/dialog.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/update.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/update.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupported: () => (/* binding */ p),\n/* harmony export */   resize: () => (/* binding */ n)\n/* harmony export */ });\n/* harmony import */ var _internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/dialogHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction n(o){(0,_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_0__.updateResizeHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_0__.dialogTelemetryVersionNumber,\"dialog.update.resize\"),o)}function p(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime.supports.dialog)&&!!_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime.supports.dialog.update}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL2RpYWxvZy91cGRhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWlSLGNBQWMsOEVBQUMsQ0FBQyx3RUFBQyxDQUFDLG9GQUFDLDRCQUE0QixhQUFhLFNBQVMsNEVBQUMsQ0FBQyxnREFBQyxJQUFJLGdEQUFDLHFCQUFxQixnREFBQyx3QkFBNkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vcGFja2FnZXMvdGVhbXMtanMvc3JjL3B1YmxpYy9kaWFsb2cvdXBkYXRlLmpzP2FlOWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e3VwZGF0ZVJlc2l6ZUhlbHBlciBhcyByLGRpYWxvZ1RlbGVtZXRyeVZlcnNpb25OdW1iZXIgYXMgdH1mcm9tXCIuLi8uLi9pbnRlcm5hbC9kaWFsb2dIZWxwZXJzLmpzXCI7aW1wb3J0e2Vuc3VyZUluaXRpYWxpemVkIGFzIG99ZnJvbVwiLi4vLi4vaW50ZXJuYWwvaW50ZXJuYWxBUElzLmpzXCI7aW1wb3J0e2dldEFwaVZlcnNpb25UYWcgYXMgZX1mcm9tXCIuLi8uLi9pbnRlcm5hbC90ZWxlbWV0cnkuanNcIjtpbXBvcnR7cnVudGltZSBhcyBpfWZyb21cIi4uL3J1bnRpbWUuanNcIjtmdW5jdGlvbiBuKG8pe3IoZSh0LFwiZGlhbG9nLnVwZGF0ZS5yZXNpemVcIiksbyl9ZnVuY3Rpb24gcCgpe3JldHVybiEoIW8oaSl8fCFpLnN1cHBvcnRzLmRpYWxvZykmJiEhaS5zdXBwb3J0cy5kaWFsb2cudXBkYXRlfWV4cG9ydHtwIGFzIGlzU3VwcG9ydGVkLG4gYXMgcmVzaXplfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/update.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/bot.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/bot.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupported: () => (/* binding */ e),\n/* harmony export */   open: () => (/* binding */ l)\n/* harmony export */ });\n/* harmony import */ var _internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../internal/dialogHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction l(t,n,l){(0,_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_0__.botUrlOpenHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_0__.dialogTelemetryVersionNumber,\"dialog.url.bot.open\"),t,n,l)}function e(){return (0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime)&&void 0!==(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime.supports.dialog&&_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime.supports.dialog.url&&_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime.supports.dialog.url.bot)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL2RpYWxvZy91cmwvYm90LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEyUixrQkFBa0IsNEVBQUMsQ0FBQyx3RUFBQyxDQUFDLG9GQUFDLCtCQUErQixhQUFhLE9BQU8sNEVBQUMsQ0FBQyxnREFBQyxhQUFhLGdEQUFDLGtCQUFrQixnREFBQyxzQkFBc0IsZ0RBQUMsMEJBQTZEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9wdWJsaWMvZGlhbG9nL3VybC9ib3QuanM/NmM4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Ym90VXJsT3BlbkhlbHBlciBhcyByLGRpYWxvZ1RlbGVtZXRyeVZlcnNpb25OdW1iZXIgYXMgb31mcm9tXCIuLi8uLi8uLi9pbnRlcm5hbC9kaWFsb2dIZWxwZXJzLmpzXCI7aW1wb3J0e2Vuc3VyZUluaXRpYWxpemVkIGFzIHR9ZnJvbVwiLi4vLi4vLi4vaW50ZXJuYWwvaW50ZXJuYWxBUElzLmpzXCI7aW1wb3J0e2dldEFwaVZlcnNpb25UYWcgYXMgaX1mcm9tXCIuLi8uLi8uLi9pbnRlcm5hbC90ZWxlbWV0cnkuanNcIjtpbXBvcnR7cnVudGltZSBhcyBufWZyb21cIi4uLy4uL3J1bnRpbWUuanNcIjtmdW5jdGlvbiBsKHQsbixsKXtyKGkobyxcImRpYWxvZy51cmwuYm90Lm9wZW5cIiksdCxuLGwpfWZ1bmN0aW9uIGUoKXtyZXR1cm4gdChuKSYmdm9pZCAwIT09KG4uc3VwcG9ydHMuZGlhbG9nJiZuLnN1cHBvcnRzLmRpYWxvZy51cmwmJm4uc3VwcG9ydHMuZGlhbG9nLnVybC5ib3QpfWV4cG9ydHtlIGFzIGlzU3VwcG9ydGVkLGwgYXMgb3Blbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/bot.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/parentCommunication.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/parentCommunication.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupported: () => (/* binding */ p),\n/* harmony export */   registerOnMessageFromParent: () => (/* binding */ u),\n/* harmony export */   sendMessageToDialog: () => (/* binding */ g),\n/* harmony export */   sendMessageToParentFromDialog: () => (/* binding */ d)\n/* harmony export */ });\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../internal/dialogHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js\");\n/* harmony import */ var _internal_handlers_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../internal/handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction d(r){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.task),!p())throw _constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__.dialogTelemetryVersionNumber,\"dialog.url.parentCommunication.sendMessageToParentFromDialog\"),\"messageForParent\",[r])}function g(r){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!p())throw _constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__.dialogTelemetryVersionNumber,\"dialog.url.parentCommunication.sendMessageToDialog\"),\"messageForChild\",[r])}function u(o){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.task),!p())throw _constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;for((0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_6__.removeHandler)(\"messageForChild\"),(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_6__.registerHandler)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__.dialogTelemetryVersionNumber,\"dialog.url.parentCommunication.registerMessageForChildHandler\"),\"messageForChild\",o),_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__.storedMessages.reverse();_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__.storedMessages.length>0;){o(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_5__.storedMessages.pop())}}function p(){var o,r;return (0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime)&&!!(null===(r=null===(o=_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.supports.dialog)||void 0===o?void 0:o.url)||void 0===r?void 0:r.parentCommunication)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/parentCommunication.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/url.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/url.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bot: () => (/* reexport module object */ _bot_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   getDialogInfoFromBotUrlDialogInfo: () => (/* binding */ c),\n/* harmony export */   getDialogInfoFromUrlDialogInfo: () => (/* binding */ f),\n/* harmony export */   isSupported: () => (/* binding */ u),\n/* harmony export */   open: () => (/* binding */ a),\n/* harmony export */   parentCommunication: () => (/* reexport module object */ _parentCommunication_js__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   submit: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../internal/dialogHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/dialogHelpers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _bot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bot.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/bot.js\");\n/* harmony import */ var _parentCommunication_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parentCommunication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/parentCommunication.js\");\nfunction a(o,i,l){(0,_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_2__.urlOpenHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_3__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_2__.dialogTelemetryVersionNumber,\"dialog.url.open\"),o,i,l)}function p(t,i){(0,_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_2__.urlSubmitHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_3__.getApiVersionTag)(_internal_dialogHelpers_js__WEBPACK_IMPORTED_MODULE_2__.dialogTelemetryVersionNumber,\"dialog.url.submit\"),t,i)}function u(){return (0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime)&&void 0!==(_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime.supports.dialog&&_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime.supports.dialog.url)}function f(t){return{url:t.url,height:t.size?t.size.height:_constants_js__WEBPACK_IMPORTED_MODULE_6__.DialogDimension.Small,width:t.size?t.size.width:_constants_js__WEBPACK_IMPORTED_MODULE_6__.DialogDimension.Small,title:t.title,fallbackUrl:t.fallbackUrl}}function c(t){const o=f(t);return o.completionBotId=t.completionBotId,o}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/dialog/url/url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/featureFlags.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/featureFlags.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activateChildProxyingCommunication: () => (/* binding */ i),\n/* harmony export */   getCurrentFeatureFlagsState: () => (/* binding */ c),\n/* harmony export */   isChildProxyingEnabled: () => (/* binding */ o),\n/* harmony export */   overwriteFeatureFlagsState: () => (/* binding */ e),\n/* harmony export */   setFeatureFlagsState: () => (/* binding */ r)\n/* harmony export */ });\nconst n={childProxyingCommunication:!1};function i(){n.childProxyingCommunication=!0}function o(){return n.childProxyingCommunication}let t={disableEnforceOriginMatchForChildResponses:!1};function c(){return t}function r(n){t=n}function e(n){return r(Object.assign(Object.assign({},t),n)),c()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL2ZlYXR1cmVGbGFncy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLFNBQVMsK0JBQStCLGFBQWEsZ0NBQWdDLGFBQWEsb0NBQW9DLE9BQU8sK0NBQStDLGFBQWEsU0FBUyxjQUFjLElBQUksY0FBYyx1Q0FBdUMsWUFBbUwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vcGFja2FnZXMvdGVhbXMtanMvc3JjL3B1YmxpYy9mZWF0dXJlRmxhZ3MuanM/ZGNkNSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBuPXtjaGlsZFByb3h5aW5nQ29tbXVuaWNhdGlvbjohMX07ZnVuY3Rpb24gaSgpe24uY2hpbGRQcm94eWluZ0NvbW11bmljYXRpb249ITB9ZnVuY3Rpb24gbygpe3JldHVybiBuLmNoaWxkUHJveHlpbmdDb21tdW5pY2F0aW9ufWxldCB0PXtkaXNhYmxlRW5mb3JjZU9yaWdpbk1hdGNoRm9yQ2hpbGRSZXNwb25zZXM6ITF9O2Z1bmN0aW9uIGMoKXtyZXR1cm4gdH1mdW5jdGlvbiByKG4pe3Q9bn1mdW5jdGlvbiBlKG4pe3JldHVybiByKE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSx0KSxuKSksYygpfWV4cG9ydHtpIGFzIGFjdGl2YXRlQ2hpbGRQcm94eWluZ0NvbW11bmljYXRpb24sYyBhcyBnZXRDdXJyZW50RmVhdHVyZUZsYWdzU3RhdGUsbyBhcyBpc0NoaWxkUHJveHlpbmdFbmFibGVkLGUgYXMgb3ZlcndyaXRlRmVhdHVyZUZsYWdzU3RhdGUsciBhcyBzZXRGZWF0dXJlRmxhZ3NTdGF0ZX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/featureFlags.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/interfaces.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/interfaces.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActionObjectType: () => (/* binding */ e),\n/* harmony export */   ClipboardSupportedMimeType: () => (/* binding */ _),\n/* harmony export */   Cohort: () => (/* binding */ R),\n/* harmony export */   DevicePermission: () => (/* binding */ O),\n/* harmony export */   EduType: () => (/* binding */ N),\n/* harmony export */   ErrorCode: () => (/* binding */ E),\n/* harmony export */   FileOpenPreference: () => (/* binding */ n),\n/* harmony export */   LegalAgeGroupClassification: () => (/* binding */ T),\n/* harmony export */   Persona: () => (/* binding */ o),\n/* harmony export */   SecondaryM365ContentIdName: () => (/* binding */ t),\n/* harmony export */   isSdkError: () => (/* binding */ i)\n/* harmony export */ });\nvar n,e,t,E,O,R,o,T,N,_;function i(n){return void 0!==(null==n?void 0:n.errorCode)}!function(n){n.Inline=\"inline\",n.Desktop=\"desktop\",n.Web=\"web\"}(n||(n={})),function(n){n.M365Content=\"m365content\"}(e||(e={})),function(n){n.DriveId=\"driveId\",n.GroupId=\"groupId\",n.SiteId=\"siteId\",n.UserId=\"userId\"}(t||(t={})),function(n){n[n.NOT_SUPPORTED_ON_PLATFORM=100]=\"NOT_SUPPORTED_ON_PLATFORM\",n[n.INTERNAL_ERROR=500]=\"INTERNAL_ERROR\",n[n.NOT_SUPPORTED_IN_CURRENT_CONTEXT=501]=\"NOT_SUPPORTED_IN_CURRENT_CONTEXT\",n[n.PERMISSION_DENIED=1e3]=\"PERMISSION_DENIED\",n[n.NETWORK_ERROR=2e3]=\"NETWORK_ERROR\",n[n.NO_HW_SUPPORT=3e3]=\"NO_HW_SUPPORT\",n[n.INVALID_ARGUMENTS=4e3]=\"INVALID_ARGUMENTS\",n[n.UNAUTHORIZED_USER_OPERATION=5e3]=\"UNAUTHORIZED_USER_OPERATION\",n[n.INSUFFICIENT_RESOURCES=6e3]=\"INSUFFICIENT_RESOURCES\",n[n.THROTTLE=7e3]=\"THROTTLE\",n[n.USER_ABORT=8e3]=\"USER_ABORT\",n[n.OPERATION_TIMED_OUT=8001]=\"OPERATION_TIMED_OUT\",n[n.OLD_PLATFORM=9e3]=\"OLD_PLATFORM\",n[n.FILE_NOT_FOUND=404]=\"FILE_NOT_FOUND\",n[n.SIZE_EXCEEDED=1e4]=\"SIZE_EXCEEDED\"}(E||(E={})),function(n){n.GeoLocation=\"geolocation\",n.Media=\"media\"}(O||(O={})),function(n){n.BCAIS=\"bcais\",n.BCWAF=\"bcwaf\",n.BCWBF=\"bcwbf\"}(R||(R={})),function(n){n.Faculty=\"faculty\",n.Student=\"student\",n.Other=\"other\"}(o||(o={})),function(n){n.Adult=\"adult\",n.MinorNoParentalConsentRequired=\"minorNoParentalConsentRequired\",n.MinorWithoutParentalConsent=\"minorWithoutParentalConsent\",n.MinorWithParentalConsent=\"minorWithParentalConsent\",n.NonAdult=\"nonAdult\"}(T||(T={})),function(n){n.HigherEducation=\"higherEducation\",n.K12=\"k12\",n.Other=\"other\"}(N||(N={})),function(n){n.TextPlain=\"text/plain\",n.TextHtml=\"text/html\",n.ImagePNG=\"image/png\",n.ImageJPEG=\"image/jpeg\"}(_||(_={}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/interfaces.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/menus.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/menus.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DisplayMode: () => (/* binding */ u),\n/* harmony export */   MenuItem: () => (/* binding */ a),\n/* harmony export */   MenuListType: () => (/* binding */ m),\n/* harmony export */   initialize: () => (/* binding */ p),\n/* harmony export */   isSupported: () => (/* binding */ P),\n/* harmony export */   setNavBarMenu: () => (/* binding */ M),\n/* harmony export */   setUpViews: () => (/* binding */ d),\n/* harmony export */   showActionMenu: () => (/* binding */ v)\n/* harmony export */ });\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\nconst i=\"v2\";var u,m;!function(e){e[e.ifRoom=0]=\"ifRoom\",e[e.overflowOnly=1]=\"overflowOnly\"}(u||(u={}));class a{constructor(){this.enabled=!0,this.selected=!1}}let f,l,c;function p(){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandler)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.registerNavBarMenuItemPressHandler\"),\"navBarMenuItemPress\",h,!1),(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandler)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.registerActionMenuItemPressHandler\"),\"actionMenuItemPress\",I,!1),(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandler)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.registerSetModuleViewHandler\"),\"setModuleView\",w,!1)}function d(n,u){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime),!P())throw _constants_js__WEBPACK_IMPORTED_MODULE_4__.errorNotSupportedOnPlatform;c=u,(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_5__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.setUpViews\"),\"setUpViews\",[n])}function w(n){c&&c(n)||((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime),(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_5__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.handleViewConfigItemPress\"),\"viewConfigItemPress\",[n]))}function M(n,u){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime),!P())throw _constants_js__WEBPACK_IMPORTED_MODULE_4__.errorNotSupportedOnPlatform;f=u,(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_5__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.setNavBarMenu\"),\"setNavBarMenu\",[n])}function h(n){f&&f(n)||((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime),(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_5__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.handleNavBarMenuItemPress\"),\"handleNavBarMenuItemPress\",[n]))}function v(n,u){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime),!P())throw _constants_js__WEBPACK_IMPORTED_MODULE_4__.errorNotSupportedOnPlatform;l=u,(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_5__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.showActionMenu\"),\"showActionMenu\",[n])}function I(n){l&&l(n)||((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime),(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_5__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(i,\"menus.handleActionMenuItemPress\"),\"handleActionMenuItemPress\",[n]))}function P(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime.supports.menus)}!function(e){e.dropDown=\"dropDown\",e.popOver=\"popOver\"}(m||(m={}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/menus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/appButton.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/appButton.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupported: () => (/* binding */ m),\n/* harmony export */   onClick: () => (/* binding */ i),\n/* harmony export */   onHoverEnter: () => (/* binding */ s),\n/* harmony export */   onHoverLeave: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/pagesHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction i(n){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandlerHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.appButton.onClick\"),\"appButtonClick\",n,[_constants_js__WEBPACK_IMPORTED_MODULE_3__.FrameContexts.content],(()=>{if(!m())throw _constants_js__WEBPACK_IMPORTED_MODULE_3__.errorNotSupportedOnPlatform}))}function s(n){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandlerHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.appButton.onHoverEnter\"),\"appButtonHoverEnter\",n,[_constants_js__WEBPACK_IMPORTED_MODULE_3__.FrameContexts.content],(()=>{if(!m())throw _constants_js__WEBPACK_IMPORTED_MODULE_3__.errorNotSupportedOnPlatform}))}function u(n){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandlerHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.appButton.onHoverLeave\"),\"appButtonHoverLeave\",n,[_constants_js__WEBPACK_IMPORTED_MODULE_3__.FrameContexts.content],(()=>{if(!m())throw _constants_js__WEBPACK_IMPORTED_MODULE_3__.errorNotSupportedOnPlatform}))}function m(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime.supports.pages)&&!!_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime.supports.pages.appButton}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/appButton.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/backStack.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/backStack.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _initialize: () => (/* binding */ m),\n/* harmony export */   isSupported: () => (/* binding */ k),\n/* harmony export */   navigateBack: () => (/* binding */ f),\n/* harmony export */   registerBackButtonHandler: () => (/* binding */ u),\n/* harmony export */   registerBackButtonHandlerHelper: () => (/* binding */ l)\n/* harmony export */ });\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/pagesHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/typeCheckUtilities.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/typeCheckUtilities.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction m(){(0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.initializeBackStackHelper)()}function f(){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.backStackNavigateBackHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.pagesTelemetryVersionNumber,\"pages.backStack.navigateBack\"))}function u(t){l((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.pagesTelemetryVersionNumber,\"pages.backStack.registerBackButtonHandler\"),t,(()=>{if(!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_2__.isNullOrUndefined)(t)&&!k())throw _constants_js__WEBPACK_IMPORTED_MODULE_3__.errorNotSupportedOnPlatform}))}function l(n,e,i){!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_2__.isNullOrUndefined)(e)&&(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime),i&&i(),(0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.setBackButtonPressHandler)(e),!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_2__.isNullOrUndefined)(e)&&(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_6__.sendMessageToParent)(n,\"registerHandler\",[\"backButton\"])}function k(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_4__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime.supports.pages)&&!!_runtime_js__WEBPACK_IMPORTED_MODULE_5__.runtime.supports.pages.backStack}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/backStack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/config.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/config.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initialize: () => (/* binding */ h),\n/* harmony export */   isSupported: () => (/* binding */ O),\n/* harmony export */   registerChangeConfigHandler: () => (/* binding */ H),\n/* harmony export */   registerOnRemoveHandler: () => (/* binding */ j),\n/* harmony export */   registerOnRemoveHandlerHelper: () => (/* binding */ w),\n/* harmony export */   registerOnSaveHandler: () => (/* binding */ S),\n/* harmony export */   registerOnSaveHandlerHelper: () => (/* binding */ N),\n/* harmony export */   setConfig: () => (/* binding */ y),\n/* harmony export */   setValidityState: () => (/* binding */ d)\n/* harmony export */ });\n/* harmony import */ var _internal_childCommunication_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../internal/childCommunication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/childCommunication.js\");\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/pagesHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../internal/typeCheckUtilities.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/typeCheckUtilities.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nlet p,v;function h(){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandler)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.config.registerSettingsSaveHandler\"),\"settings.save\",E,!1),(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandler)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.config.registerSettingsRemoveHandler\"),\"settings.remove\",F,!1)}function d(e){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.configSetValidityStateHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.config.setValidityState\"),e)}function y(e){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.configSetConfigHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.config.setConfig\"),e)}function S(e){N((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.config.registerOnSaveHandler\"),e,(()=>{if(!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_3__.isNullOrUndefined)(e)&&!O())throw _constants_js__WEBPACK_IMPORTED_MODULE_4__.errorNotSupportedOnPlatform}))}function N(e,t,n){!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_3__.isNullOrUndefined)(t)&&(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_5__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_6__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_4__.FrameContexts.settings),n&&n(),p=t,!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_3__.isNullOrUndefined)(t)&&(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_7__.sendMessageToParent)(e,\"registerHandler\",[\"save\"])}function j(e){w((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.config.registerOnRemoveHandler\"),e,(()=>{if(!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_3__.isNullOrUndefined)(e)&&!O())throw _constants_js__WEBPACK_IMPORTED_MODULE_4__.errorNotSupportedOnPlatform}))}function w(e,t,n){!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_3__.isNullOrUndefined)(t)&&(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_5__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_6__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_4__.FrameContexts.remove,_constants_js__WEBPACK_IMPORTED_MODULE_4__.FrameContexts.settings),n&&n(),v=t,!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_3__.isNullOrUndefined)(t)&&(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_7__.sendMessageToParent)(e,\"registerHandler\",[\"remove\"])}function E(i){const n=new C(i);p?p(n):(0,_internal_childCommunication_js__WEBPACK_IMPORTED_MODULE_8__.shouldEventBeRelayedToChild)()?(0,_internal_childCommunication_js__WEBPACK_IMPORTED_MODULE_8__.sendMessageEventToChild)(\"settings.save\",[i]):n.notifySuccess()}function H(e){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_0__.registerHandlerHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.config.registerChangeConfigHandler\"),\"changeSettings\",e,[_constants_js__WEBPACK_IMPORTED_MODULE_4__.FrameContexts.content],(()=>{if(!O())throw _constants_js__WEBPACK_IMPORTED_MODULE_4__.errorNotSupportedOnPlatform}))}class C{constructor(e){this.notified=!1,this.result=e||{}}notifySuccess(){this.ensureNotNotified(),(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_7__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.saveEvent.notifySuccess\"),\"settings.save.success\"),this.notified=!0}notifyFailure(e){this.ensureNotNotified(),(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_7__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.saveEvent.notifyFailure\"),\"settings.save.failure\",[e]),this.notified=!0}ensureNotNotified(){if(this.notified)throw new Error(\"The SaveEvent may only notify success or failure once.\")}}function F(){const i=new T;v?v(i):(0,_internal_childCommunication_js__WEBPACK_IMPORTED_MODULE_8__.shouldEventBeRelayedToChild)()?(0,_internal_childCommunication_js__WEBPACK_IMPORTED_MODULE_8__.sendMessageEventToChild)(\"settings.remove\",[]):i.notifySuccess()}class T{constructor(){this.notified=!1}notifySuccess(){this.ensureNotNotified(),(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_7__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.removeEvent.notifySuccess\"),\"settings.remove.success\"),this.notified=!0}notifyFailure(e){this.ensureNotNotified(),(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_7__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_2__.pagesTelemetryVersionNumber,\"pages.removeEvent.notifyFailure\"),\"settings.remove.failure\",[e]),this.notified=!0}ensureNotNotified(){if(this.notified)throw new Error(\"The removeEventType may only notify success or failure once.\")}}function O(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_5__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_6__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_6__.runtime.supports.pages)&&!!_runtime_js__WEBPACK_IMPORTED_MODULE_6__.runtime.supports.pages.config}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/currentApp.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/currentApp.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupported: () => (/* binding */ g),\n/* harmony export */   navigateTo: () => (/* binding */ o),\n/* harmony export */   navigateToDefaultPage: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../internal/pagesHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction o(o){return new Promise((p=>{if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.settings,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.task,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.stage,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!g())throw _constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;p((0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendAndHandleSdkError)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_5__.pagesTelemetryVersionNumber,\"pages.currentApp.navigateTo\"),\"pages.currentApp.navigateTo\",o))}))}function p(){return new Promise((o=>{if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.sidePanel,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.settings,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.task,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.stage,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.meetingStage),!g())throw _constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;o((0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendAndHandleSdkError)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_5__.pagesTelemetryVersionNumber,\"pages.currentApp.navigateToDefaultPage\"),\"pages.currentApp.navigateToDefaultPage\"))}))}function g(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.supports.pages)&&!!_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.supports.pages.currentApp}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/currentApp.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/fullTrust.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/fullTrust.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enterFullscreen: () => (/* binding */ l),\n/* harmony export */   exitFullscreen: () => (/* binding */ u),\n/* harmony export */   isSupported: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../internal/pagesHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction l(){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content),!p())throw _constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_5__.pagesTelemetryVersionNumber,\"pages.fullTrust.enterFullscreen\"),\"enterFullscreen\",[])}function u(){if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_2__.FrameContexts.content),!p())throw _constants_js__WEBPACK_IMPORTED_MODULE_2__.errorNotSupportedOnPlatform;(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_3__.sendMessageToParent)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_4__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_5__.pagesTelemetryVersionNumber,\"pages.fullTrust.exitFullscreen\"),\"exitFullscreen\",[])}function p(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_0__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.supports.pages)&&!!_runtime_js__WEBPACK_IMPORTED_MODULE_1__.runtime.supports.pages.fullTrust}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL3BhZ2VzL2Z1bGxUcnVzdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBOFksYUFBYSxHQUFHLDRFQUFDLENBQUMsZ0RBQUMsQ0FBQyx3REFBQyxxQkFBcUIsc0VBQUMsQ0FBQywrRUFBQyxDQUFDLHdFQUFDLENBQUMsa0ZBQUMsMERBQTBELGFBQWEsR0FBRyw0RUFBQyxDQUFDLGdEQUFDLENBQUMsd0RBQUMscUJBQXFCLHNFQUFDLENBQUMsK0VBQUMsQ0FBQyx3RUFBQyxDQUFDLGtGQUFDLHdEQUF3RCxhQUFhLFNBQVMsNEVBQUMsQ0FBQyxnREFBQyxJQUFJLGdEQUFDLG9CQUFvQixnREFBQywwQkFBNEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vcGFja2FnZXMvdGVhbXMtanMvc3JjL3B1YmxpYy9wYWdlcy9mdWxsVHJ1c3QuanM/MzhhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7c2VuZE1lc3NhZ2VUb1BhcmVudCBhcyB0fWZyb21cIi4uLy4uL2ludGVybmFsL2NvbW11bmljYXRpb24uanNcIjtpbXBvcnR7ZW5zdXJlSW5pdGlhbGl6ZWQgYXMgcn1mcm9tXCIuLi8uLi9pbnRlcm5hbC9pbnRlcm5hbEFQSXMuanNcIjtpbXBvcnR7cGFnZXNUZWxlbWV0cnlWZXJzaW9uTnVtYmVyIGFzIGV9ZnJvbVwiLi4vLi4vaW50ZXJuYWwvcGFnZXNIZWxwZXJzLmpzXCI7aW1wb3J0e2dldEFwaVZlcnNpb25UYWcgYXMgbn1mcm9tXCIuLi8uLi9pbnRlcm5hbC90ZWxlbWV0cnkuanNcIjtpbXBvcnR7RnJhbWVDb250ZXh0cyBhcyBzLGVycm9yTm90U3VwcG9ydGVkT25QbGF0Zm9ybSBhcyBvfWZyb21cIi4uL2NvbnN0YW50cy5qc1wiO2ltcG9ydHtydW50aW1lIGFzIGl9ZnJvbVwiLi4vcnVudGltZS5qc1wiO2Z1bmN0aW9uIGwoKXtpZihyKGkscy5jb250ZW50KSwhcCgpKXRocm93IG87dChuKGUsXCJwYWdlcy5mdWxsVHJ1c3QuZW50ZXJGdWxsc2NyZWVuXCIpLFwiZW50ZXJGdWxsc2NyZWVuXCIsW10pfWZ1bmN0aW9uIHUoKXtpZihyKGkscy5jb250ZW50KSwhcCgpKXRocm93IG87dChuKGUsXCJwYWdlcy5mdWxsVHJ1c3QuZXhpdEZ1bGxzY3JlZW5cIiksXCJleGl0RnVsbHNjcmVlblwiLFtdKX1mdW5jdGlvbiBwKCl7cmV0dXJuISghcihpKXx8IWkuc3VwcG9ydHMucGFnZXMpJiYhIWkuc3VwcG9ydHMucGFnZXMuZnVsbFRydXN0fWV4cG9ydHtsIGFzIGVudGVyRnVsbHNjcmVlbix1IGFzIGV4aXRGdWxsc2NyZWVuLHAgYXMgaXNTdXBwb3J0ZWR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/fullTrust.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/pages.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/pages.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnterFocusType: () => (/* binding */ A),\n/* harmony export */   ReturnFocusType: () => (/* binding */ T),\n/* harmony export */   appButton: () => (/* reexport module object */ _appButton_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   backStack: () => (/* reexport module object */ _backStack_js__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   config: () => (/* reexport module object */ _config_js__WEBPACK_IMPORTED_MODULE_2__),\n/* harmony export */   currentApp: () => (/* reexport module object */ _currentApp_js__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   fullTrust: () => (/* reexport module object */ _fullTrust_js__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   getConfig: () => (/* binding */ S),\n/* harmony export */   initializeWithFrameContext: () => (/* binding */ H),\n/* harmony export */   isSupported: () => (/* binding */ z),\n/* harmony export */   navigateCrossDomain: () => (/* binding */ D),\n/* harmony export */   navigateToApp: () => (/* binding */ G),\n/* harmony export */   registerFocusEnterHandler: () => (/* binding */ N),\n/* harmony export */   registerFullScreenHandler: () => (/* binding */ R),\n/* harmony export */   returnFocus: () => (/* binding */ w),\n/* harmony export */   setCurrentFrame: () => (/* binding */ b),\n/* harmony export */   shareDeepLink: () => (/* binding */ E),\n/* harmony export */   tabs: () => (/* reexport module object */ _tabs_js__WEBPACK_IMPORTED_MODULE_5__)\n/* harmony export */ });\n/* harmony import */ var _internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../internal/appHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/appHelpers.js\");\n/* harmony import */ var _internal_communication_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../internal/communication.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/communication.js\");\n/* harmony import */ var _internal_handlers_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../internal/handlers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/handlers.js\");\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../internal/pagesHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../internal/typeCheckUtilities.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/typeCheckUtilities.js\");\n/* harmony import */ var _internal_utils_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../internal/utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\n/* harmony import */ var _internal_validOrigins_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../internal/validOrigins.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/validOrigins.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\n/* harmony import */ var _appButton_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./appButton.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/appButton.js\");\n/* harmony import */ var _backStack_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./backStack.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/backStack.js\");\n/* harmony import */ var _config_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./config.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/config.js\");\n/* harmony import */ var _currentApp_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./currentApp.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/currentApp.js\");\n/* harmony import */ var _fullTrust_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./fullTrust.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/fullTrust.js\");\n/* harmony import */ var _tabs_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tabs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/tabs.js\");\nvar A,T;function w(e){const t=(0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.returnFocus\");if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_9__.runtime),!z())throw _constants_js__WEBPACK_IMPORTED_MODULE_10__.errorNotSupportedOnPlatform;if(void 0===e&&(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_11__.sendMessageToParent)(t,\"returnFocus\",[!1]),\"boolean\"==typeof e)(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_11__.sendMessageToParent)(t,\"returnFocus\",[e]);else switch(e){case T.PreviousLandmark:case T.GoToActivityFeed:(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_11__.sendMessageToParent)(t,\"returnFocus\",[!1,e]);break;case T.NextLandmark:(0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_11__.sendMessageToParent)(t,\"returnFocus\",[!0,e])}}function N(e){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_12__.registerHandlerHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.registerFocusEnterHandler\"),\"focusEnter\",e,[],(()=>{if(!z())throw _constants_js__WEBPACK_IMPORTED_MODULE_10__.errorNotSupportedOnPlatform}))}function b(e){(0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.setCurrentFrameHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.setCurrentFrame\"),e)}function H(r,t,n){(0,_internal_validOrigins_js__WEBPACK_IMPORTED_MODULE_13__.prefetchOriginsFromCDN)(),(0,_internal_appHelpers_js__WEBPACK_IMPORTED_MODULE_14__.appInitializeHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.initializeWithFrameContext\"),n).then((()=>t&&t())),b(r)}function S(){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.getConfigHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.getConfig.\"))}function D(e){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.navigateCrossDomainHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.navigateCrossDomain\"),e)}function G(e){return new Promise((r=>{if((0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_9__.runtime,_constants_js__WEBPACK_IMPORTED_MODULE_10__.FrameContexts.content,_constants_js__WEBPACK_IMPORTED_MODULE_10__.FrameContexts.sidePanel,_constants_js__WEBPACK_IMPORTED_MODULE_10__.FrameContexts.settings,_constants_js__WEBPACK_IMPORTED_MODULE_10__.FrameContexts.task,_constants_js__WEBPACK_IMPORTED_MODULE_10__.FrameContexts.stage,_constants_js__WEBPACK_IMPORTED_MODULE_10__.FrameContexts.meetingStage),!z())throw _constants_js__WEBPACK_IMPORTED_MODULE_10__.errorNotSupportedOnPlatform;const n=(0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.navigateToApp\");if(_runtime_js__WEBPACK_IMPORTED_MODULE_9__.runtime.isLegacyTeams){const o=(0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.isAppNavigationParametersObject)(e)?e:(0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.convertNavigateToAppParamsToAppNavigationParameters)(e);r((0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_11__.sendAndHandleStatusAndReason)(n,\"executeDeepLink\",(0,_internal_utils_js__WEBPACK_IMPORTED_MODULE_15__.createTeamsAppLink)(o)))}else{const o=(0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.isAppNavigationParametersObject)(e)?(0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.convertAppNavigationParametersToNavigateToAppParams)(e):e;r((0,_internal_communication_js__WEBPACK_IMPORTED_MODULE_11__.sendAndHandleStatusAndReason)(n,\"pages.navigateToApp\",o))}}))}function E(e){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.shareDeepLinkHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.shareDeepLink\"),e)}function R(e){(0,_internal_handlers_js__WEBPACK_IMPORTED_MODULE_12__.registerHandlerHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_6__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_7__.pagesTelemetryVersionNumber,\"pages.registerFullScreenHandler\"),\"fullScreenChange\",e,[],(()=>{if(!(0,_internal_typeCheckUtilities_js__WEBPACK_IMPORTED_MODULE_16__.isNullOrUndefined)(e)&&!z())throw _constants_js__WEBPACK_IMPORTED_MODULE_10__.errorNotSupportedOnPlatform}))}function z(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_8__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_9__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_9__.runtime.supports.pages)}!function(e){e[e.PreviousLandmark=0]=\"PreviousLandmark\",e[e.NextLandmark=1]=\"NextLandmark\",e[e.Read=2]=\"Read\",e[e.Compose=3]=\"Compose\"}(A||(A={})),function(e){e[e.PreviousLandmark=0]=\"PreviousLandmark\",e[e.NextLandmark=1]=\"NextLandmark\",e[e.GoToActivityFeed=2]=\"GoToActivityFeed\"}(T||(T={}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/pages.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/tabs.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/tabs.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMruTabInstances: () => (/* binding */ u),\n/* harmony export */   getTabInstances: () => (/* binding */ i),\n/* harmony export */   isSupported: () => (/* binding */ m),\n/* harmony export */   navigateToTab: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/internalAPIs.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/internalAPIs.js\");\n/* harmony import */ var _internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internal/pagesHelpers.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/pagesHelpers.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../runtime.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\");\nfunction p(t){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.tabsNavigateToTabHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.pagesTelemetryVersionNumber,\"pages.tabs.navigateToTab\"),t)}function i(t){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.getTabInstancesHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.pagesTelemetryVersionNumber,\"pages.tabs.getTabInstances\"),t)}function u(t){return (0,_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.getMruTabInstancesHelper)((0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_1__.getApiVersionTag)(_internal_pagesHelpers_js__WEBPACK_IMPORTED_MODULE_0__.pagesTelemetryVersionNumber,\"pages.tabs.getMruTabInstances\"),t)}function m(){return!(!(0,_internal_internalAPIs_js__WEBPACK_IMPORTED_MODULE_2__.ensureInitialized)(_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime)||!_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime.supports.pages)&&!!_runtime_js__WEBPACK_IMPORTED_MODULE_3__.runtime.supports.pages.tabs}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL3BhZ2VzL3RhYnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBNlUsY0FBYyxPQUFPLGtGQUFDLENBQUMsd0VBQUMsQ0FBQyxrRkFBQyxnQ0FBZ0MsY0FBYyxPQUFPLGdGQUFDLENBQUMsd0VBQUMsQ0FBQyxrRkFBQyxrQ0FBa0MsY0FBYyxPQUFPLG1GQUFDLENBQUMsd0VBQUMsQ0FBQyxrRkFBQyxxQ0FBcUMsYUFBYSxTQUFTLDRFQUFDLENBQUMsZ0RBQUMsSUFBSSxnREFBQyxvQkFBb0IsZ0RBQUMscUJBQThHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9wdWJsaWMvcGFnZXMvdGFicy5qcz9jMjUwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtlbnN1cmVJbml0aWFsaXplZCBhcyB0fWZyb21cIi4uLy4uL2ludGVybmFsL2ludGVybmFsQVBJcy5qc1wiO2ltcG9ydHt0YWJzTmF2aWdhdGVUb1RhYkhlbHBlciBhcyByLGdldFRhYkluc3RhbmNlc0hlbHBlciBhcyBlLGdldE1ydVRhYkluc3RhbmNlc0hlbHBlciBhcyBuLHBhZ2VzVGVsZW1ldHJ5VmVyc2lvbk51bWJlciBhcyBzfWZyb21cIi4uLy4uL2ludGVybmFsL3BhZ2VzSGVscGVycy5qc1wiO2ltcG9ydHtnZXRBcGlWZXJzaW9uVGFnIGFzIGF9ZnJvbVwiLi4vLi4vaW50ZXJuYWwvdGVsZW1ldHJ5LmpzXCI7aW1wb3J0e3J1bnRpbWUgYXMgb31mcm9tXCIuLi9ydW50aW1lLmpzXCI7ZnVuY3Rpb24gcCh0KXtyZXR1cm4gcihhKHMsXCJwYWdlcy50YWJzLm5hdmlnYXRlVG9UYWJcIiksdCl9ZnVuY3Rpb24gaSh0KXtyZXR1cm4gZShhKHMsXCJwYWdlcy50YWJzLmdldFRhYkluc3RhbmNlc1wiKSx0KX1mdW5jdGlvbiB1KHQpe3JldHVybiBuKGEocyxcInBhZ2VzLnRhYnMuZ2V0TXJ1VGFiSW5zdGFuY2VzXCIpLHQpfWZ1bmN0aW9uIG0oKXtyZXR1cm4hKCF0KG8pfHwhby5zdXBwb3J0cy5wYWdlcykmJiEhby5zdXBwb3J0cy5wYWdlcy50YWJzfWV4cG9ydHt1IGFzIGdldE1ydVRhYkluc3RhbmNlcyxpIGFzIGdldFRhYkluc3RhbmNlcyxtIGFzIGlzU3VwcG9ydGVkLHAgYXMgbmF2aWdhdGVUb1RhYn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/pages/tabs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyRuntimeConfig: () => (/* binding */ O),\n/* harmony export */   fastForwardRuntime: () => (/* binding */ h),\n/* harmony export */   generateVersionBasedTeamsRuntimeConfig: () => (/* binding */ j),\n/* harmony export */   isRuntimeInitialized: () => (/* binding */ c),\n/* harmony export */   latestRuntimeApiVersion: () => (/* binding */ d),\n/* harmony export */   mapTeamsVersionToSupportedCapabilities: () => (/* binding */ T),\n/* harmony export */   runtime: () => (/* binding */ g),\n/* harmony export */   upgradeChain: () => (/* binding */ v),\n/* harmony export */   v1HostClientTypes: () => (/* binding */ f),\n/* harmony export */   v1MobileHostClientTypes: () => (/* binding */ b),\n/* harmony export */   versionAndPlatformAgnosticTeamsRuntimeConfig: () => (/* binding */ m)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/node_modules/.pnpm/@rollup_plugin-typescript@11.1.6_rollup@4.24.4_tslib@2.6.3_typescript@4.9.5/node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _internal_constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../internal/constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/constants.js\");\n/* harmony import */ var _internal_globalVars_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../internal/globalVars.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/globalVars.js\");\n/* harmony import */ var _internal_telemetry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/telemetry.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/telemetry.js\");\n/* harmony import */ var _internal_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../internal/utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/constants.js\");\nconst l=(0,_internal_telemetry_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)(\"runtime\"),d=4;function u(o){return o.apiVersion===d}function c(o){if(u(o))return!0;throw-1===o.apiVersion?new Error(_internal_constants_js__WEBPACK_IMPORTED_MODULE_1__.errorRuntimeNotInitialized):new Error(_internal_constants_js__WEBPACK_IMPORTED_MODULE_1__.errorRuntimeNotSupported)}let g={apiVersion:-1,supports:{}};const m={apiVersion:4,isNAAChannelRecommended:!1,isDeeplyNestedAuthSupported:!1,hostVersionsInfo:_constants_js__WEBPACK_IMPORTED_MODULE_2__.teamsMinAdaptiveCardVersion,isLegacyTeams:!0,supports:{appInstallDialog:{},appEntity:{},call:{},chat:{},conversations:{},dialog:{card:{bot:{}},url:{bot:{},parentCommunication:{}},update:{}},interactive:{},logs:{},meetingRoom:{},menus:{},monetization:{},notifications:{},pages:{config:{},backStack:{},fullTrust:{}},remoteCamera:{},teams:{fullTrust:{}},teamsCore:{},video:{sharedFrame:{}}}},y=[_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.desktop,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.web,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.rigel,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.surfaceHub,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.teamsRoomsWindows,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.teamsRoomsAndroid,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.teamsPhones,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.teamsDisplays],b=[_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.android,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.ios,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.ipados,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.visionOS],f=[...y,...b];function h(o){let e=o;if(e.apiVersion<d&&v.forEach((o=>{e.apiVersion===o.versionToUpgradeFrom&&(e=o.upgradeToNextVersion(e))})),u(e))return e;throw new Error(\"Received a runtime that could not be upgraded to the latest version\")}const v=[{versionToUpgradeFrom:1,upgradeToNextVersion:o=>{var e;return{apiVersion:2,hostVersionsInfo:void 0,isLegacyTeams:o.isLegacyTeams,supports:Object.assign(Object.assign({},o.supports),{dialog:o.supports.dialog?{card:void 0,url:o.supports.dialog,update:null===(e=o.supports.dialog)||void 0===e?void 0:e.update}:void 0})}}},{versionToUpgradeFrom:2,upgradeToNextVersion:e=>{const s=e.supports,i=(0,_node_modules_pnpm_rollup_plugin_typescript_11_1_6_rollup_4_24_4_tslib_2_6_3_typescript_4_9_5_node_modules_tslib_tslib_es6_js__WEBPACK_IMPORTED_MODULE_3__.__rest)(s,[\"appNotification\"]);return Object.assign(Object.assign({},e),{apiVersion:3,supports:i})}},{versionToUpgradeFrom:3,upgradeToNextVersion:o=>{var e,s,i,t,n;return{apiVersion:4,hostVersionsInfo:o.hostVersionsInfo,isNAAChannelRecommended:o.isNAAChannelRecommended,isLegacyTeams:o.isLegacyTeams,supports:Object.assign(Object.assign({},o.supports),{dialog:o.supports.dialog?{card:null===(e=o.supports.dialog)||void 0===e?void 0:e.card,url:{bot:null===(i=null===(s=o.supports.dialog)||void 0===s?void 0:s.url)||void 0===i?void 0:i.bot,parentCommunication:(null===(t=o.supports.dialog)||void 0===t?void 0:t.url)?{}:void 0},update:null===(n=o.supports.dialog)||void 0===n?void 0:n.update}:void 0})}}}],T={\"1.0.0\":[{capability:{pages:{appButton:{},tabs:{}},stageView:{}},hostClientTypes:y}],\"1.9.0\":[{capability:{location:{}},hostClientTypes:f}],\"2.0.0\":[{capability:{people:{}},hostClientTypes:f},{capability:{sharing:{}},hostClientTypes:[_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.desktop,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.web]}],\"2.0.1\":[{capability:{teams:{fullTrust:{joinedTeams:{}}}},hostClientTypes:[_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.android,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.desktop,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.ios,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.teamsRoomsAndroid,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.teamsPhones,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.teamsDisplays,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.web]},{capability:{webStorage:{}},hostClientTypes:[_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.desktop]}],\"2.0.5\":[{capability:{webStorage:{}},hostClientTypes:[_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.android,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.ios]}],\"2.0.8\":[{capability:{sharing:{}},hostClientTypes:[_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.android,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.ios]}],\"2.1.1\":[{capability:{nestedAppAuth:{}},hostClientTypes:[_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.android,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.ios,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.ipados,_constants_js__WEBPACK_IMPORTED_MODULE_2__.HostClientType.visionOS]}]},V=l.extend(\"generateBackCompatRuntimeConfig\");function C(o,e){const s=Object.assign({},o);for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&(\"object\"!=typeof e[i]||Array.isArray(e[i])?i in o||(s[i]=e[i]):s[i]=C(o[i]||{},e[i]));return s}function j(o,e,s){V(\"generating back compat runtime config for %s\",o);let t=Object.assign({},e.supports);V(\"Supported capabilities in config before updating based on highestSupportedVersion: %o\",t),Object.keys(s).forEach((e=>{(0,_internal_utils_js__WEBPACK_IMPORTED_MODULE_4__.compareSDKVersions)(o,e)>=0&&s[e].forEach((o=>{void 0!==_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_5__.GlobalVars.hostClientType&&o.hostClientTypes.includes(_internal_globalVars_js__WEBPACK_IMPORTED_MODULE_5__.GlobalVars.hostClientType)&&(t=C(t,o.capability))}))}));const a={apiVersion:d,hostVersionsInfo:_constants_js__WEBPACK_IMPORTED_MODULE_2__.teamsMinAdaptiveCardVersion,isLegacyTeams:!0,supports:t};return V(\"Runtime config after updating based on highestSupportedVersion: %o\",a),a}const w=l.extend(\"applyRuntimeConfig\");function O(o){\"string\"==typeof o.apiVersion&&(w(\"Trying to apply runtime with string apiVersion, processing as v1: %o\",o),o=Object.assign(Object.assign({},o),{apiVersion:1})),w(\"Fast-forwarding runtime %o\",o);const e=h(o);w(\"Applying runtime %o\",e),g=(0,_internal_utils_js__WEBPACK_IMPORTED_MODULE_4__.deepFreeze)(e)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/runtime.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/serializable.interface.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/serializable.interface.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSerializable: () => (/* binding */ e)\n/* harmony export */ });\nfunction e(e){return null!=e&&void 0!==e.serialize&&\"function\"==typeof e.serialize}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL3NlcmlhbGl6YWJsZS5pbnRlcmZhY2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGNBQWMscUVBQWlHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9wdWJsaWMvc2VyaWFsaXphYmxlLmludGVyZmFjZS5qcz9iMzc0Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGUoZSl7cmV0dXJuIG51bGwhPWUmJnZvaWQgMCE9PWUuc2VyaWFsaXplJiZcImZ1bmN0aW9uXCI9PXR5cGVvZiBlLnNlcmlhbGl6ZX1leHBvcnR7ZSBhcyBpc1NlcmlhbGl6YWJsZX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/serializable.interface.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/uuidObject.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/uuidObject.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UUID: () => (/* binding */ n),\n/* harmony export */   validateUuidInstance: () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _internal_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/utils.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/utils.js\");\nclass n{constructor(n=(0,_internal_utils_js__WEBPACK_IMPORTED_MODULE_0__.generateGUID)()){this.uuid=n,(0,_internal_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateUuid)(n)}toString(){return this.uuid}serialize(){return this.toString()}}function r(i){if(!(i instanceof n))throw new Error(`Potential id (${JSON.stringify(i)}) is invalid; it is not an instance of UUID class.`)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL3V1aWRPYmplY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNFLFFBQVEsY0FBYyxnRUFBQyxJQUFJLFlBQVksZ0VBQUMsSUFBSSxXQUFXLGlCQUFpQixZQUFZLHdCQUF3QixjQUFjLHNEQUFzRCxrQkFBa0IsY0FBYyx1Q0FBbUYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xaGFybW9ueS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0BtaWNyb3NvZnQvdGVhbXMtanMvZGlzdC9lc20vcGFja2FnZXMvdGVhbXMtanMvc3JjL3B1YmxpYy91dWlkT2JqZWN0LmpzPzcyYWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e3ZhbGlkYXRlVXVpZCBhcyBpLGdlbmVyYXRlR1VJRCBhcyB0fWZyb21cIi4uL2ludGVybmFsL3V0aWxzLmpzXCI7Y2xhc3Mgbntjb25zdHJ1Y3RvcihuPXQoKSl7dGhpcy51dWlkPW4saShuKX10b1N0cmluZygpe3JldHVybiB0aGlzLnV1aWR9c2VyaWFsaXplKCl7cmV0dXJuIHRoaXMudG9TdHJpbmcoKX19ZnVuY3Rpb24gcihpKXtpZighKGkgaW5zdGFuY2VvZiBuKSl0aHJvdyBuZXcgRXJyb3IoYFBvdGVudGlhbCBpZCAoJHtKU09OLnN0cmluZ2lmeShpKX0pIGlzIGludmFsaWQ7IGl0IGlzIG5vdCBhbiBpbnN0YW5jZSBvZiBVVUlEIGNsYXNzLmApfWV4cG9ydHtuIGFzIFVVSUQsciBhcyB2YWxpZGF0ZVV1aWRJbnN0YW5jZX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/uuidObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/validatedSafeString.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/validatedSafeString.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValidatedSafeString: () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _internal_idValidation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/idValidation.js */ \"(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/internal/idValidation.js\");\nclass i{constructor(i){this.idAsString=i,(0,_internal_idValidation_js__WEBPACK_IMPORTED_MODULE_0__.validateSafeContent)(i)}serialize(){return this.toString()}toString(){return this.idAsString}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL3ZhbGlkYXRlZFNhZmVTdHJpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0UsUUFBUSxlQUFlLGtCQUFrQiw4RUFBQyxJQUFJLFlBQVksdUJBQXVCLFdBQVcsd0JBQXlEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcWhhcm1vbnktd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AbWljcm9zb2Z0L3RlYW1zLWpzL2Rpc3QvZXNtL3BhY2thZ2VzL3RlYW1zLWpzL3NyYy9wdWJsaWMvdmFsaWRhdGVkU2FmZVN0cmluZy5qcz80ZDRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt2YWxpZGF0ZVNhZmVDb250ZW50IGFzIHR9ZnJvbVwiLi4vaW50ZXJuYWwvaWRWYWxpZGF0aW9uLmpzXCI7Y2xhc3MgaXtjb25zdHJ1Y3RvcihpKXt0aGlzLmlkQXNTdHJpbmc9aSx0KGkpfXNlcmlhbGl6ZSgpe3JldHVybiB0aGlzLnRvU3RyaW5nKCl9dG9TdHJpbmcoKXtyZXR1cm4gdGhpcy5pZEFzU3RyaW5nfX1leHBvcnR7aSBhcyBWYWxpZGF0ZWRTYWZlU3RyaW5nfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/validatedSafeString.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/version.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/version.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ o)\n/* harmony export */ });\nconst o=\"2.36.0\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlCQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL3FoYXJtb255LXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC90ZWFtcy1qcy9kaXN0L2VzbS9wYWNrYWdlcy90ZWFtcy1qcy9zcmMvcHVibGljL3ZlcnNpb24uanM/MDc4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBvPVwiMi4zNi4wXCI7ZXhwb3J0e28gYXMgdmVyc2lvbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/teams-js/dist/esm/packages/teams-js/src/public/version.js\n");

/***/ })

};
;