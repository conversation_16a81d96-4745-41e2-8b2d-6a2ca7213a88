import { postRequest } from "@/APILayer/axios_helper";
import { AdminOnboardObj } from "@/models/admin";
import { CompanyOnboardObj } from "@/models/company";

export async function onboardAdmin(
  admin: AdminOnboardObj,
  company: CompanyOnboardObj,
) {
  console.log("onboardAdmin called");
  const data = {
    company,
    user: admin,
  };

  try {
    const response = await postRequest("/admin/onboard", data);
    console.log("Response from onboardAdmin:", response);
    return response.data;
  } catch (error) {
    console.error("Error in onboardAdmin:", error);
    throw error; // Re-throw to let the caller handle it
  }
}

export async function parseParamsFromUrl(url: string) {
  const response = await postRequest("/auth/parse-params", { link: url });
  return response.data;
}

export async function selfOnboard(email: string) {
  const response = await postRequest("/user/self-onboard", {
    userEmail: email,
  });
  return response.data.data;
}

export async function teamsSelfOnboard(email: string, tenantId?: string) {
  const response = await postRequest("/teams/user/self-onboard", {
    userEmail: email,
    tenantId
  });
  return response.data;
}

export async function onboardEmployee(companyId: string, userId: string) {
  const response = await postRequest("/employee/onboard", {
    companyId: companyId,
    userId: userId,
  });
  console.log("Response from onboardEmployee:", response);
  return response.data;
}
