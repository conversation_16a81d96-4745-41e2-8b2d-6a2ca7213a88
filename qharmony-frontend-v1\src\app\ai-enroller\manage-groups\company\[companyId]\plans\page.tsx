'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { HiOutlineCheckCircle, HiOutlinePlus, HiOutlinePencil, HiOutlineCalendar, HiOutlineHome, HiOutlineOfficeBuilding, HiOutlineClipboardList, HiOutlineCurrencyDollar, HiOutlineTrash } from 'react-icons/hi';
import '../../../../../globals.css';
import PlanSelectionModal from './components/PlanSelectionModal';
import {
  getPlanAssignmentsByCompany,
  PlanAssignment,
  createPlanAssignment,
  clonePlanAssignment,
  updatePlanAssignment,
  canEditPlanAssignment
} from '../../../services/planAssignmentApi';
import { getApiBaseUrl, getUserId } from '../../../../../../utils/env';

interface PlanAssignmentDisplay {
  _id: string;
  planName: string;
  planCode: string;
  carrier: string;
  type: 'Medical' | 'Dental' | 'Vision' | 'Ancillary';
  metalTier?: string;
  period: string;
  status: 'Active' | 'Draft' | 'Expired' | 'Inactive';
  assignmentId: string;
  planId: string;
  canEdit: boolean;
  canDelete: boolean;
}

interface Company {
  _id: string;
  companyName: string;
  employeeCount?: number;
}

function CompanyPlansPage() {
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;

  const [company, setCompany] = useState<Company | null>(null);
  const [plans, setPlans] = useState<PlanAssignmentDisplay[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlans, setSelectedPlans] = useState<string[]>([]);
  const [editingPlan, setEditingPlan] = useState<PlanAssignmentDisplay | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [carrierFilter, setCarrierFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [plansPerPage] = useState<number>(10);
  const [showPlanSelectionModal, setShowPlanSelectionModal] = useState(false);
  const [contributionType, setContributionType] = useState<'percentage' | 'fixed'>('percentage');
  const [coverageTiers, setCoverageTiers] = useState<any[]>([]);

  // Calculate current plan year in 2025-2026 format
  const getCurrentPlanYear = () => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const nextYear = currentYear + 1;
    return `${currentYear}-${nextYear}`;
  };

  // State to store plan assignment details for cost calculation
  const [planAssignmentDetails, setPlanAssignmentDetails] = useState<{[key: string]: any}>({});
  const [companyEmployeeCount, setCompanyEmployeeCount] = useState<number>(0);
  const [estimatedMonthlyCost, setEstimatedMonthlyCost] = useState<number>(0);

  // Calculate estimated monthly cost from plan assignments
  const calculateEstimatedMonthlyCost = () => {
    if (!companyEmployeeCount || Object.keys(planAssignmentDetails).length === 0) {
      return 0;
    }

    console.log('Calculating estimated monthly cost...');
    console.log('Company employee count:', companyEmployeeCount);
    console.log('Plan assignment details:', planAssignmentDetails);

    // Sum all employee-only coverage tier employer costs from all plan assignments
    let totalEmployeeOnlyEmployerCost = 0;

    Object.values(planAssignmentDetails).forEach((assignmentDetails: any) => {
      if (assignmentDetails && assignmentDetails.coverageTiers) {
        // Find the "Employee Only" tier
        const employeeOnlyTier = assignmentDetails.coverageTiers.find((tier: any) =>
          tier.tierName?.toLowerCase().includes('employee only') ||
          tier.tierName?.toLowerCase() === 'employee'
        );

        if (employeeOnlyTier) {
          console.log('Found employee-only tier:', employeeOnlyTier);
          // Add the employer cost (what employer pays) for this plan
          totalEmployeeOnlyEmployerCost += employeeOnlyTier.employerCost || 0;
        }
      }
    });

    console.log('Total employee-only employer cost per employee:', totalEmployeeOnlyEmployerCost);

    // Calculate total monthly employer cost: employee count × sum of all employee-only employer costs
    const totalMonthlyEmployerCost = companyEmployeeCount * totalEmployeeOnlyEmployerCost;

    console.log('Total monthly employer cost:', totalMonthlyEmployerCost);

    return Math.round(totalMonthlyEmployerCost);
  };

  // Get the estimated monthly cost to display
  const getEstimatedMonthlyCost = () => {
    return estimatedMonthlyCost;
  };

  // Fetch plan assignment details for cost calculation
  const fetchAllPlanAssignmentDetails = async () => {
    const details: {[key: string]: any} = {};

    for (const plan of plans) {
      try {
        const assignmentDetails = await fetchPlanAssignmentDetails(plan.assignmentId);
        if (assignmentDetails) {
          details[plan.assignmentId] = assignmentDetails;
        }
      } catch (error) {
        console.warn(`Failed to fetch details for assignment ${plan.assignmentId}:`, error);
      }
    }

    setPlanAssignmentDetails(details);
  };

  // Note: fetchPlanDetails function removed - now using enriched data from plan assignments API

  // Function to fetch company details with employee count (handles broker's own company)
  const fetchCompanyDetails = async (companyId: string): Promise<Company> => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      // First, check if this is the broker's own company using /employee/company-details
      const ownCompanyResponse = await fetch(`${API_BASE_URL}/employee/company-details`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (ownCompanyResponse.ok) {
        const ownCompanyData = await ownCompanyResponse.json();
        if (ownCompanyData.company &&
            ownCompanyData.company.isBrokerage &&
            ownCompanyData.company._id === companyId) {
          console.log('Found broker\'s own company with employee count:', ownCompanyData.company.companySize);
          return {
            _id: ownCompanyData.company._id,
            companyName: ownCompanyData.company.name || 'Unknown Company',
            employeeCount: ownCompanyData.company.companySize || 250
          };
        }
      }

      // If not broker's own company, try to get from /admin/all-companies (client companies)
      const companiesResponse = await fetch(`${API_BASE_URL}/admin/all-companies`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        const targetCompany = companiesData.companies?.find((company: any) => company._id === companyId);

        if (targetCompany) {
          console.log('Found client company with employee count:', targetCompany.companySize);
          return {
            _id: targetCompany._id,
            companyName: targetCompany.name || 'Unknown Company',
            employeeCount: targetCompany.companySize || 250
          };
        }
      }

      // Fallback: try the pre-enrollment companies API
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/companies/${companyId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          _id: data.company?._id || companyId,
          companyName: data.company?.companyName || 'Unknown Company',
          employeeCount: data.company?.companySize || data.company?.employeeCount || 250
        };
      }
    } catch (error) {
      console.error('Error fetching company details:', error);
    }

    // Final fallback
    return {
      _id: companyId,
      companyName: 'Unknown Company',
      employeeCount: 250
    };
  };

  const fetchCompanyAndPlans = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch actual company details with real employee count
      const companyDetails = await fetchCompanyDetails(companyId);
      setCompanyEmployeeCount(companyDetails.employeeCount || 250);
      console.log('Company employee count set to:', companyDetails.employeeCount || 250);

      // Fetch plan assignments for the company with enriched data
      const result = await getPlanAssignmentsByCompany(companyId, {
        includePlanData: true
      });

      if (result.success && result.data) {
        const planAssignments = result.data.assignments;
        console.log('Fetched plan assignments:', planAssignments);

        // Handle special case where broker has no existing assignments but can create new ones
        if (result.data.accessDeniedToExisting && result.data.canCreateAssignments) {
          console.log('🔧 Broker can create new plan assignments for this company');
          // Show empty state but allow plan creation
          setCompany(companyDetails);
          setPlans([]);
          return;
        }

        // Transform plan assignments to display format using enriched data
        const displayPlans: PlanAssignmentDisplay[] = await Promise.all(
          planAssignments.map(async (assignment: PlanAssignment) => {
            console.log('Processing assignment with enriched data:', assignment._id, assignment);

            // Use enriched plan data from the API response
            const planData = assignment.planData || assignment.plan;
            const carrierData = assignment.carrierData;

            // Get planId as string
            const planIdString = typeof assignment.planId === 'string' ? assignment.planId : assignment.planId?._id || '';

            // Use enriched data instead of making additional API calls
            const planName = planData?.planName || 'Unknown Plan';
            const planCode = planData?.planCode || 'N/A';
            const carrierName = carrierData?.carrierName || planData?.carrierName || 'Unknown Carrier';
            const coverageType = planData?.coverageType || '';
            const coverageSubTypes = planData?.coverageSubTypes || [];
            const metalTier = planData?.metalTier || '';

            console.log('Using enriched plan data:', { planName, planCode, carrierName, coverageType, coverageSubTypes, metalTier });

            // Determine display type based on coverage subtypes or coverage type
            let displayType: 'Medical' | 'Dental' | 'Vision' | 'Ancillary' = 'Medical';
            if (coverageSubTypes && coverageSubTypes.length > 0) {
              const primarySubtype = coverageSubTypes[0].toLowerCase();
              if (primarySubtype.includes('dental')) {
                displayType = 'Dental';
              } else if (primarySubtype.includes('vision')) {
                displayType = 'Vision';
              } else if (primarySubtype.includes('medical') || primarySubtype.includes('health')) {
                displayType = 'Medical';
              } else {
                displayType = 'Ancillary';
              }
            } else if (coverageType) {
              const coverageTypeLower = coverageType.toLowerCase();
              if (coverageTypeLower.includes('dental')) {
                displayType = 'Dental';
              } else if (coverageTypeLower.includes('vision')) {
                displayType = 'Vision';
              } else if (coverageTypeLower.includes('medical') || coverageTypeLower.includes('health')) {
                displayType = 'Medical';
              } else {
                displayType = 'Ancillary';
              }
            }

            // Check if assignment can be edited
            let canEdit = false;
            let canDelete = false;

            try {
              const editCheckResult = await canEditPlanAssignment(assignment._id);
              canEdit = editCheckResult.success && editCheckResult.data ? editCheckResult.data.canEdit : false;
              // For simplicity, assume canDelete is same as canEdit for now
              canDelete = canEdit;
            } catch (error) {
              console.warn('Failed to check edit permissions for assignment:', assignment._id);
            }

            return {
              _id: assignment._id,
              planName: planName,
              planCode: planCode,
              carrier: carrierName,
              type: displayType,
              metalTier: metalTier,
              period: `${new Date(assignment.planEffectiveDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })} - ${new Date(assignment.planEndDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}`,
              status: assignment.status as 'Active' | 'Draft' | 'Expired' | 'Inactive',
              assignmentId: assignment._id,
              planId: planIdString,
              canEdit,
              canDelete
            };
          })
        );

        console.log('Final display plans:', displayPlans);
        setCompany(companyDetails);
        setPlans(displayPlans);
      } else {
        setError(result.error || 'Failed to fetch plan assignments');
        setPlans([]);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to fetch plan assignments');
      setPlans([]);
    } finally {
      setLoading(false);
    }
  }, [companyId]);

  useEffect(() => {
    fetchCompanyAndPlans();
  }, [fetchCompanyAndPlans]);

  // Fetch plan assignment details when plans are loaded
  useEffect(() => {
    if (plans.length > 0) {
      fetchAllPlanAssignmentDetails();
    }
  }, [plans]);

  // Calculate estimated monthly cost when plan assignment details and employee count are available
  useEffect(() => {
    if (Object.keys(planAssignmentDetails).length > 0 && companyEmployeeCount > 0) {
      const calculatedCost = calculateEstimatedMonthlyCost();
      setEstimatedMonthlyCost(calculatedCost);
    }
  }, [planAssignmentDetails, companyEmployeeCount]);

  const handleSelectAll = () => {
    // Check if all filtered plans are selected (across all pages)
    const filteredPlanIds = filteredPlans.map(plan => plan._id);
    const allFilteredSelected = filteredPlanIds.every(id => selectedPlans.includes(id));

    if (allFilteredSelected) {
      // Deselect all filtered plans
      setSelectedPlans(prev => prev.filter(id => !filteredPlanIds.includes(id)));
    } else {
      // Select all filtered plans (add to existing selection)
      setSelectedPlans(prev => [...new Set([...prev, ...filteredPlanIds])]);
    }
  };

  const handleSelectCurrentPage = () => {
    // Check if all plans on current page are selected
    const currentPagePlanIds = paginatedPlans.map(plan => plan._id);
    const allCurrentPageSelected = currentPagePlanIds.every(id => selectedPlans.includes(id));

    if (allCurrentPageSelected) {
      // Deselect all plans on current page
      setSelectedPlans(prev => prev.filter(id => !currentPagePlanIds.includes(id)));
    } else {
      // Select all plans on current page
      setSelectedPlans(prev => [...new Set([...prev, ...currentPagePlanIds])]);
    }
  };

  const handlePlanSelect = (planId: string) => {
    setSelectedPlans(prev => 
      prev.includes(planId) 
        ? prev.filter(id => id !== planId)
        : [...prev, planId]
    );
  };

  const handleAddNewPlan = () => {
    setShowPlanSelectionModal(true);
  };

  const handlePlanSelected = async (selectedPlan: any) => {
    // Workflow 2: Create Fresh Plan Assignment from Template
    console.log('🔄 Workflow 2: Creating fresh plan assignment from template:', selectedPlan);

    try {
      // Create plan assignment with all required fields
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      // Set dates for next year enrollment
      const planEffectiveDate = `${nextYear}-01-01`;
      const planEndDate = `${nextYear}-12-31`;
      const enrollmentStartDate = `${currentYear}-11-01`; // Current year November
      const enrollmentEndDate = `${currentYear}-11-30`; // Current year November end

      const assignmentData = {
        planId: selectedPlan._id,
        companyId: companyId,
        // Required fields with defaults
        rateStructure: 'Composite', // Default rate structure
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 500,
            employerCost: 400,
            employeeCost: 100
          },
          {
            tierName: 'Employee + Spouse',
            totalCost: 1000,
            employerCost: 800,
            employeeCost: 200
          },
          {
            tierName: 'Employee + Child(ren)',
            totalCost: 800,
            employerCost: 640,
            employeeCost: 160
          },
          {
            tierName: 'Family',
            totalCost: 1500,
            employerCost: 1200,
            employeeCost: 300
          }
        ],
        planEffectiveDate,
        planEndDate,
        enrollmentStartDate,
        enrollmentEndDate,
        // Optional fields with defaults
        groupNumber: `GRP-${companyId}-${selectedPlan._id.slice(-6)}`,
        waitingPeriod: { enabled: false, days: 0, rule: 'Immediate' },
        enrollmentType: 'Active',
        employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
        employeeContribution: { contributionType: 'Percentage', contributionAmount: 20 },
        ageBandedRates: [],
        salaryBasedRates: [],
        planCustomizations: {},
        status: 'Draft' // Start as Draft for new assignments
      };

      const result = await createPlanAssignment(assignmentData);

      if (result.success && result.data) {
        console.log('✅ Fresh plan assignment created successfully:', result.data);
        // Immediately refresh the plans list to show the new assignment
        await fetchCompanyAndPlans();
      } else {
        console.error('❌ Failed to create plan assignment:', result.error);
        alert('Failed to assign plan to company: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error creating plan assignment:', error);
      alert('Failed to assign plan to company. Please try again.');
    }
  };

  const handleModalClose = () => {
    // No need to refresh here since we refresh immediately after each plan selection
    setShowPlanSelectionModal(false);
  };

  const handlePlanCreated = async (newPlan: any) => {
    // Workflow 2: Create Fresh Plan Assignment for newly created plan
    console.log('🔄 Workflow 2: Creating fresh plan assignment for new plan:', newPlan);

    try {
      // Create plan assignment with all required fields
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      // Set dates for next year enrollment
      const planEffectiveDate = `${nextYear}-01-01`;
      const planEndDate = `${nextYear}-12-31`;
      const enrollmentStartDate = `${currentYear}-11-01`; // Current year November
      const enrollmentEndDate = `${currentYear}-11-30`; // Current year November end

      const assignmentData = {
        planId: newPlan._id,
        companyId: companyId,
        // Required fields with defaults
        rateStructure: 'Composite', // Default rate structure
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 500,
            employerCost: 400,
            employeeCost: 100
          },
          {
            tierName: 'Employee + Spouse',
            totalCost: 1000,
            employerCost: 800,
            employeeCost: 200
          },
          {
            tierName: 'Employee + Child(ren)',
            totalCost: 800,
            employerCost: 640,
            employeeCost: 160
          },
          {
            tierName: 'Family',
            totalCost: 1500,
            employerCost: 1200,
            employeeCost: 300
          }
        ],
        planEffectiveDate,
        planEndDate,
        enrollmentStartDate,
        enrollmentEndDate,
        // Optional fields with defaults
        groupNumber: `GRP-${companyId}-${newPlan._id.slice(-6)}`,
        waitingPeriod: { enabled: false, days: 0, rule: 'Immediate' },
        enrollmentType: 'Active',
        employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
        employeeContribution: { contributionType: 'Percentage', contributionAmount: 20 },
        ageBandedRates: [],
        salaryBasedRates: [],
        planCustomizations: {},
        status: 'Draft' // Start as Draft for new assignments
      };

      const result = await createPlanAssignment(assignmentData);

      if (result.success && result.data) {
        // Refresh the plans list to include the new assignment
        await fetchCompanyAndPlans();
        alert('✅ Plan created and assigned to company successfully!');
      } else {
        console.error('❌ Failed to create plan assignment:', result.error);
        alert('Plan created but failed to assign to company: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error creating plan assignment:', error);
      alert('Plan created but failed to assign to company. Please try again.');
    }

    setShowPlanSelectionModal(false);
  };

  // Function to fetch plan assignment details including coverage tiers
  const fetchPlanAssignmentDetails = async (assignmentId: string) => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      console.log('Fetching plan assignment details for ID:', assignmentId);
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      console.log('Plan assignment fetch response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Plan assignment fetch response data:', data);
        console.log('Assignment object:', data.assignment);

        // Handle Mongoose document structure - data might be in _doc
        const assignment = data.assignment._doc || data.assignment;
        console.log('Processed assignment:', assignment);
        console.log('Coverage tiers in assignment:', assignment?.coverageTiers);

        return assignment;
      } else {
        console.error('Failed to fetch plan assignment details. Status:', response.status);
        const errorText = await response.text();
        console.error('Error response:', errorText);
      }
    } catch (error) {
      console.error('Error fetching plan assignment details:', error);
    }
    return null;
  };

  const handleEditPlan = async (planId: string) => {
    const plan = plans.find(p => p._id === planId);
    if (plan) {
      // Check backend can-edit functionality
      const editCheckResult = await canEditPlanAssignment(plan.assignmentId);

      if (!editCheckResult.success) {
        alert('Failed to check edit permissions. Please try again.');
        return;
      }

      // Note: Workflow type is determined automatically based on edit permissions
      // Workflow 2: Direct update (no enrollments) - editCheckResult.data?.canEdit === true
      // Workflow 1: Clone for rollover (has enrollments) - editCheckResult.data?.canEdit === false

      // Fetch actual plan assignment details to get current coverage tiers
      console.log('Fetching assignment details for:', plan.assignmentId);
      const assignmentDetails = await fetchPlanAssignmentDetails(plan.assignmentId);
      console.log('Assignment details received:', assignmentDetails);

      if (assignmentDetails && assignmentDetails.coverageTiers && assignmentDetails.coverageTiers.length > 0) {
        // Load actual coverage tiers from the assignment
        console.log('Raw coverage tiers from assignment:', assignmentDetails.coverageTiers);

        const actualTiers = assignmentDetails.coverageTiers.map((tier: any, index: number) => {
          const employerPercent = tier.totalCost > 0 ? Math.round((tier.employerCost / tier.totalCost) * 100) : 80;
          const tierData = {
            id: (index + 1).toString(),
            tier: tier.tierName,
            premium: tier.totalCost,
            employeePercent: employerPercent,
            employerPays: tier.employerCost,
            employeePays: tier.employeeCost
          };
          console.log(`Mapped tier ${index + 1}:`, tierData);
          return tierData;
        });

        console.log('Setting actual coverage tiers:', actualTiers);
        setCoverageTiers(actualTiers);
      } else {
        console.warn('No coverage tiers found in assignment, using defaults. Assignment details:', assignmentDetails);
        // Reset to default tiers if no data found
        const defaultTiers = [
          { id: '1', tier: 'Employee Only', premium: 450.00, employeePercent: 80, employerPays: 360.00, employeePays: 90.00 },
          { id: '2', tier: 'Employee + Spouse', premium: 880.00, employeePercent: 80, employerPays: 712.00, employeePays: 178.00 },
          { id: '3', tier: 'Employee + Children', premium: 720.00, employeePercent: 80, employerPays: 576.00, employeePays: 144.00 },
          { id: '4', tier: 'Employee + Family', premium: 1250.00, employeePercent: 80, employerPays: 1000.00, employeePays: 250.00 }
        ];
        console.log('Setting default tiers:', defaultTiers);
        setCoverageTiers(defaultTiers);
      }

      setEditingPlan(plan);
      setShowEditModal(true);
    }
  };

  const handleSaveEdit = async () => {
    if (!editingPlan) return;

    try {
      // Check if the plan can be edited first
      const editCheckResult = await canEditPlanAssignment(editingPlan.assignmentId);

      if (!editCheckResult.success) {
        alert('Failed to check edit permissions. Please try again.');
        return;
      }

      let result;
      const updateData = {
        rateStructure: 'Composite', // Required field for validation
        coverageTiers: coverageTiers.map(tier => {
          // Ensure costs add up correctly to avoid validation errors
          const totalCost = parseFloat(tier.premium.toFixed(2));
          const employerCost = parseFloat(tier.employerPays.toFixed(2));
          const employeeCost = parseFloat(tier.employeePays.toFixed(2));

          // Verify the math adds up (backend validation requirement)
          const calculatedTotal = employerCost + employeeCost;
          const finalTotalCost = Math.abs(totalCost - calculatedTotal) > 0.01 ? calculatedTotal : totalCost;

          console.log(`Tier ${tier.tier}: total=${finalTotalCost}, employer=${employerCost}, employee=${employeeCost}`);

          return {
            tierName: tier.tier,
            totalCost: finalTotalCost,
            employerCost: employerCost,
            employeeCost: employeeCost
          };
        }),
        ageBandedRates: [] // Required to be empty for non-age-banded structures
      };

      if (editCheckResult.data?.canEdit) {
        // Workflow 2: Direct Update (no enrollments assigned)
        console.log('🔄 Workflow 2: Updating plan assignment directly (no enrollments)');

        result = await updatePlanAssignment(editingPlan.assignmentId, updateData);

        if (result.success) {
          alert('✅ Plan assignment updated successfully.');
        } else {
          console.error('Update failed with error:', result.error);
          alert('❌ Failed to update plan assignment: ' + result.error);
        }
      } else {
        // Workflow 1: Clone for Rollover (has enrollments)
        console.log('🔄 Workflow 1: Cloning plan assignment for rollover (has enrollments)');

        // Prepare clone data with next year dates
        const currentYear = new Date().getFullYear();
        const nextYear = currentYear + 1;

        const cloneData = {
          rateStructure: updateData.rateStructure,
          coverageTiers: updateData.coverageTiers,
          ageBandedRates: updateData.ageBandedRates,
          // Update dates for next year
          planEffectiveDate: `${nextYear}-01-01`,
          planEndDate: `${nextYear}-12-31`,
          enrollmentStartDate: `${currentYear}-11-01`,
          enrollmentEndDate: `${currentYear}-11-30`,
          // Reset status for new assignment
          status: 'Draft'
        };

        result = await clonePlanAssignment(editingPlan.assignmentId, cloneData);

        if (result.success) {
          alert(`✅ Plan has active enrollments. Created a new ${nextYear} version with your changes.`);
        } else {
          console.error('Clone failed with error:', result.error);
          alert('❌ Failed to create new version: ' + result.error);
        }
      }

      if (result.success) {
        // Refresh the plans list
        await fetchCompanyAndPlans();
        setShowEditModal(false);
        setEditingPlan(null);
      }
    } catch (error) {
      console.error('Error updating plan assignment:', error);
      alert('Failed to update plan assignment. Please try again.');
    }
  };

  const handleCloseModal = () => {
    setShowEditModal(false);
    setEditingPlan(null);
  };

  const updateTier = (id: string, field: string, value: number) => {
    setCoverageTiers(prev => prev.map(tier => {
      if (tier.id === id) {
        const updated = { ...tier, [field]: value };

        // Recalculate employer/employee pays based on contribution type
        if (contributionType === 'percentage') {
          // In percentage mode: employer % is editable, employer pays is calculated
          if (field === 'premium' || field === 'employeePercent') {
            updated.employerPays = (updated.premium * updated.employeePercent) / 100;
            updated.employeePays = updated.premium - updated.employerPays;
          }
        } else if (contributionType === 'fixed') {
          // In fixed mode: employer pays is editable, employer % is calculated
          if (field === 'premium' || field === 'employerPays') {
            updated.employeePays = updated.premium - updated.employerPays;
            updated.employeePercent = updated.premium > 0 ? (updated.employerPays / updated.premium) * 100 : 0;
          }
        }

        return updated;
      }
      return tier;
    }));
  };

  const deleteTier = (id: string) => {
    setCoverageTiers(prev => prev.filter(tier => tier.id !== id));
  };

  const addNewTier = () => {
    const newId = (coverageTiers.length + 1).toString();
    const newTier = {
      id: newId,
      tier: 'New Coverage Tier',
      premium: 500.00,
      employeePercent: 80,
      employerPays: 400.00,
      employeePays: 100.00
    };
    setCoverageTiers(prev => [...prev, newTier]);
  };

  const handleContinueWithSelected = () => {
    if (selectedPlans.length === 0) {
      alert('Please select at least one plan to continue.');
      return;
    }

    // Navigate directly to set dates page (skip enrollment-dates step)
    const selectedPlanIds = selectedPlans.join(',');
    router.push(`/ai-enroller/manage-groups/company/${companyId}/set-dates?plans=${selectedPlanIds}`);
  };

  // Filter plans based on selected filters
  const filteredPlans = plans.filter(plan => {
    const statusMatch = statusFilter === 'all' || plan.status === statusFilter;
    const carrierMatch = carrierFilter === 'all' || plan.carrier === carrierFilter;
    return statusMatch && carrierMatch;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredPlans.length / plansPerPage);
  const startIndex = (currentPage - 1) * plansPerPage;
  const endIndex = startIndex + plansPerPage;
  const paginatedPlans = filteredPlans.slice(startIndex, endIndex);

  // Reset to page 1 when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, carrierFilter]);

  const groupedPlans = paginatedPlans.reduce((acc, plan) => {
    if (!acc[plan.type]) {
      acc[plan.type] = [];
    }
    acc[plan.type].push(plan);
    return acc;
  }, {} as Record<string, PlanAssignmentDisplay[]>);

  // Function to get sorted category order
  const getSortedCategories = () => {
    const preferredOrder = ['Medical', 'Dental', 'Vision', 'Ancillary'];
    const availableCategories = Object.keys(groupedPlans);

    // First add categories in preferred order
    const sortedCategories = [];
    for (const preferred of preferredOrder) {
      if (availableCategories.includes(preferred)) {
        sortedCategories.push(preferred);
      }
    }

    // Then add any remaining categories not in preferred order
    for (const category of availableCategories) {
      if (!sortedCategories.includes(category)) {
        sortedCategories.push(category);
      }
    }

    return sortedCategories;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading plan assignments...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl">
            <h3 className="font-bold">Error Loading Plans</h3>
            <p>{error}</p>
            <button
              onClick={fetchCompanyAndPlans}
              className="mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <button
            onClick={() => router.push('/ai-enroller/manage-groups')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            ← Back to Groups
          </button>
          <button
            onClick={handleAddNewPlan}
            className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            + Add Plan
          </button>
        </div>
      </div>

      {/* Company Info & Stats */}
      <div className="bg-white px-6 py-6 border-b border-gray-200">
        <div className="flex items-center justify-center mb-6">
          <div className="flex items-center gap-3 bg-blue-50 px-6 py-3 rounded-lg">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <HiOutlineOfficeBuilding className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">{company?.companyName || 'Loading...'}</h1>
              <p className="text-sm text-gray-600">{company?.employeeCount || 0} employees • San Francisco, CA</p>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-4 gap-6">
          <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">Active Plans</p>
                <p className="text-2xl font-bold text-blue-900">{filteredPlans.filter(p => p.status === 'Active').length}</p>
                <p className="text-xs text-blue-600">Currently enrolled</p>
              </div>
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <HiOutlineClipboardList className="w-5 h-5 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-xl p-4 border border-green-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 font-medium">Total Premium</p>
                <p className="text-2xl font-bold text-green-900">${estimatedMonthlyCost.toLocaleString()}</p>
                <p className="text-xs text-green-600">Monthly cost</p>
              </div>
              <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                <HiOutlineCurrencyDollar className="w-5 h-5 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-purple-50 rounded-xl p-4 border border-purple-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 font-medium">Employees</p>
                <p className="text-2xl font-bold text-purple-900">{company?.employeeCount || 0}</p>
                <p className="text-xs text-purple-600">Total covered</p>
              </div>
              <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                <HiOutlineOfficeBuilding className="w-5 h-5 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-orange-50 rounded-xl p-4 border border-orange-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-orange-600 font-medium">Per Employee</p>
                <p className="text-2xl font-bold text-orange-900">${company?.employeeCount ? Math.round(estimatedMonthlyCost / company.employeeCount) : 0}</p>
                <p className="text-xs text-orange-600">Average cost</p>
              </div>
              <div className="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center">
                <HiOutlineCurrencyDollar className="w-5 h-5 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="bg-white px-6 py-4 border-b border-gray-200">
        <div className="relative max-w-md mx-auto">
          <input
            type="text"
            placeholder="Search plans by name, carrier, or type..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>


      {/* Plan Cards */}
      <div className="px-6 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPlans.map((plan) => {
            const getCategoryColor = (coverageType: string) => {
              switch (coverageType?.toLowerCase()) {
                case 'medical':
                case 'health':
                  return {
                    bg: 'bg-blue-50',
                    border: 'border-blue-200',
                    icon: 'bg-blue-600',
                    text: 'text-blue-900',
                    badge: 'bg-blue-100 text-blue-800'
                  };
                case 'dental':
                  return {
                    bg: 'bg-green-50',
                    border: 'border-green-200',
                    icon: 'bg-green-600',
                    text: 'text-green-900',
                    badge: 'bg-green-100 text-green-800'
                  };
                case 'vision':
                  return {
                    bg: 'bg-purple-50',
                    border: 'border-purple-200',
                    icon: 'bg-purple-600',
                    text: 'text-purple-900',
                    badge: 'bg-purple-100 text-purple-800'
                  };
                default:
                  return {
                    bg: 'bg-gray-50',
                    border: 'border-gray-200',
                    icon: 'bg-gray-600',
                    text: 'text-gray-900',
                    badge: 'bg-gray-100 text-gray-800'
                  };
              }
            };

            const colors = getCategoryColor(plan.coverageType);
            const monthlyPremium = plan.coverageTiers?.[0]?.totalCost || 450;

            return (
              <div key={plan._id} className={`${colors.bg} ${colors.border} border-2 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow`}>
                {/* Card Header */}
                <div className="p-4 border-b border-gray-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`w-10 h-10 ${colors.icon} rounded-lg flex items-center justify-center`}>
                      {plan.coverageType?.toLowerCase() === 'medical' || plan.coverageType?.toLowerCase() === 'health' ? (
                        <HiOutlineClipboardList className="w-5 h-5 text-white" />
                      ) : plan.coverageType?.toLowerCase() === 'dental' ? (
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 2C6.686 2 4 4.686 4 8c0 1.5.5 3 1.5 4.5L10 18l4.5-5.5C15.5 11 16 9.5 16 8c0-3.314-2.686-6-6-6z"/>
                        </svg>
                      ) : (
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                        </svg>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className={`font-semibold ${colors.text}`}>{plan.planName}</h3>
                      <p className="text-sm text-gray-600">{plan.carrier}</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-md text-xs font-medium ${colors.badge}`}>
                      {plan.coverageType}
                    </span>
                    <span className={`px-2 py-1 rounded-md text-xs font-medium ${
                      plan.metalTier === 'Gold' ? 'bg-yellow-100 text-yellow-800' :
                      plan.metalTier === 'Silver' ? 'bg-gray-100 text-gray-800' :
                      plan.metalTier === 'Bronze' ? 'bg-orange-100 text-orange-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {plan.metalTier || 'Standard'}
                    </span>
                  </div>
                </div>

                {/* Card Body */}
                <div className="p-4">
                  <div className="flex items-center gap-1 mb-2">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-xs text-gray-500">Since {plan.planEffectiveDate ? new Date(plan.planEffectiveDate).toLocaleDateString() : '1/1/2024'}</span>
                  </div>

                  <div className="flex items-baseline gap-1">
                    <span className={`text-2xl font-bold ${colors.text}`}>${monthlyPremium}</span>
                    <span className="text-sm text-gray-500">per month</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Plan Selection Modal */}
      {showPlanSelectionModal && (
        <PlanSelectionModal
          isOpen={showPlanSelectionModal}
          onClose={handleModalClose}
          onSelectPlan={handlePlanSelected}
          onCreatePlan={handlePlanCreated}
          companyId={companyId}
        />
      )}
    </div>
  );
}

export default CompanyPlansPage;
