'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { HiOutlineCheckCircle, HiOutlinePlus, HiOutlinePencil, HiOutlineCalendar, HiOutlineHome, HiOutlineOfficeBuilding, HiOutlineClipboardList, HiOutlineCurrencyDollar, HiOutlineTrash, HiOutlineToggleLeft, HiOutlineToggleRight } from 'react-icons/hi';
import '../../../../../globals.css';
import PlanSelectionModal from './components/PlanSelectionModal';
import EnrollmentHeader from '../../../../employee-enrol/components/EnrollmentHeader';
import {
  getPlanAssignmentsByCompany,
  PlanAssignment,
  createPlanAssignment,
  clonePlanAssignment,
  updatePlanAssignment,
  canEditPlanAssignment,
  activatePlanAssignment,
  deactivatePlanAssignment,
  deletePlanAssignment
} from '../../../services/planAssignmentApi';
import { getApiBaseUrl, getUserId } from '../../../../../../utils/env';

interface PlanAssignmentDisplay {
  _id: string;
  planName: string;
  planCode: string;
  carrier: string;
  type: 'Medical' | 'Dental' | 'Vision' | 'Ancillary';
  metalTier?: string;
  period: string;
  status: 'Active' | 'Draft' | 'Expired' | 'Inactive';
  assignmentId: string;
  planId: string;
  canEdit: boolean;
  canDelete: boolean;
}

interface Company {
  _id: string;
  companyName: string;
  employeeCount?: number;
}

function CompanyPlansPage() {
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;

  const [company, setCompany] = useState<Company | null>(null);
  const [plans, setPlans] = useState<PlanAssignmentDisplay[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlans, setSelectedPlans] = useState<string[]>([]);
  const [editingPlan, setEditingPlan] = useState<PlanAssignmentDisplay | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [carrierFilter, setCarrierFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [plansPerPage] = useState<number>(10);
  const [showPlanSelectionModal, setShowPlanSelectionModal] = useState(false);
  const [contributionType, setContributionType] = useState<'percentage' | 'fixed'>('percentage');
  const [coverageTiers, setCoverageTiers] = useState<any[]>([]);

  // Calculate current plan year in 2025-2026 format
  const getCurrentPlanYear = () => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const nextYear = currentYear + 1;
    return `${currentYear}-${nextYear}`;
  };

  // State to store plan assignment details for cost calculation
  const [planAssignmentDetails, setPlanAssignmentDetails] = useState<{[key: string]: any}>({});
  const [companyEmployeeCount, setCompanyEmployeeCount] = useState<number>(0);
  const [estimatedMonthlyCost, setEstimatedMonthlyCost] = useState<number>(0);

  // Calculate estimated monthly cost from plan assignments
  const calculateEstimatedMonthlyCost = () => {
    if (!companyEmployeeCount || Object.keys(planAssignmentDetails).length === 0) {
      return 0;
    }

    console.log('Calculating estimated monthly cost...');
    console.log('Company employee count:', companyEmployeeCount);
    console.log('Plan assignment details:', planAssignmentDetails);

    // Sum all employee-only coverage tier employer costs from all plan assignments
    let totalEmployeeOnlyEmployerCost = 0;

    Object.values(planAssignmentDetails).forEach((assignmentDetails: any) => {
      if (assignmentDetails && assignmentDetails.coverageTiers) {
        // Find the "Employee Only" tier
        const employeeOnlyTier = assignmentDetails.coverageTiers.find((tier: any) =>
          tier.tierName?.toLowerCase().includes('employee only') ||
          tier.tierName?.toLowerCase() === 'employee'
        );

        if (employeeOnlyTier) {
          console.log('Found employee-only tier:', employeeOnlyTier);
          // Add the employer cost (what employer pays) for this plan
          totalEmployeeOnlyEmployerCost += employeeOnlyTier.employerCost || 0;
        }
      }
    });

    console.log('Total employee-only employer cost per employee:', totalEmployeeOnlyEmployerCost);

    // Calculate total monthly employer cost: employee count × sum of all employee-only employer costs
    const totalMonthlyEmployerCost = companyEmployeeCount * totalEmployeeOnlyEmployerCost;

    console.log('Total monthly employer cost:', totalMonthlyEmployerCost);

    return Math.round(totalMonthlyEmployerCost);
  };

  // Get the estimated monthly cost to display
  const getEstimatedMonthlyCost = () => {
    return estimatedMonthlyCost;
  };

  // Fetch plan assignment details for cost calculation
  const fetchAllPlanAssignmentDetails = async () => {
    const details: {[key: string]: any} = {};

    for (const plan of plans) {
      try {
        const assignmentDetails = await fetchPlanAssignmentDetails(plan.assignmentId);
        if (assignmentDetails) {
          details[plan.assignmentId] = assignmentDetails;
        }
      } catch (error) {
        console.warn(`Failed to fetch details for assignment ${plan.assignmentId}:`, error);
      }
    }

    setPlanAssignmentDetails(details);
  };

  // Function to fetch plan details by ID
  const fetchPlanDetails = async (planId: string) => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          planName: data.plan?.planName || 'Unknown Plan',
          planCode: data.plan?.planCode || 'N/A',
          planType: data.plan?.planType || 'N/A',
          coverageType: data.plan?.coverageType || 'Unknown',
          coverageSubTypes: data.plan?.coverageSubTypes || [],
          metalTier: data.plan?.metalTier || '',
          carrierName: data.carrier?.carrierName || 'Unknown Carrier'
        };
      }
    } catch (error) {
      console.error('Error fetching plan details for planId:', planId, error);
    }

    return {
      planName: 'Unknown Plan',
      planCode: 'N/A',
      planType: 'N/A',
      coverageType: 'Unknown',
      coverageSubTypes: [],
      metalTier: '',
      carrierName: 'Unknown Carrier'
    };
  };

  // Function to fetch company details with employee count (handles broker's own company)
  const fetchCompanyDetails = async (companyId: string): Promise<Company> => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      // First, check if this is the broker's own company using /employee/company-details
      const ownCompanyResponse = await fetch(`${API_BASE_URL}/employee/company-details`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (ownCompanyResponse.ok) {
        const ownCompanyData = await ownCompanyResponse.json();
        if (ownCompanyData.company &&
            ownCompanyData.company.isBrokerage &&
            ownCompanyData.company._id === companyId) {
          console.log('Found broker\'s own company with employee count:', ownCompanyData.company.companySize);
          return {
            _id: ownCompanyData.company._id,
            companyName: ownCompanyData.company.name || 'Unknown Company',
            employeeCount: ownCompanyData.company.companySize || 250
          };
        }
      }

      // If not broker's own company, try to get from /admin/all-companies (client companies)
      const companiesResponse = await fetch(`${API_BASE_URL}/admin/all-companies`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        const targetCompany = companiesData.companies?.find((company: any) => company._id === companyId);

        if (targetCompany) {
          console.log('Found client company with employee count:', targetCompany.companySize);
          return {
            _id: targetCompany._id,
            companyName: targetCompany.name || 'Unknown Company',
            employeeCount: targetCompany.companySize || 250
          };
        }
      }

      // Fallback: try the pre-enrollment companies API
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/companies/${companyId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          _id: data.company?._id || companyId,
          companyName: data.company?.companyName || 'Unknown Company',
          employeeCount: data.company?.companySize || data.company?.employeeCount || 250
        };
      }
    } catch (error) {
      console.error('Error fetching company details:', error);
    }

    // Final fallback
    return {
      _id: companyId,
      companyName: 'Unknown Company',
      employeeCount: 250
    };
  };

  const fetchCompanyAndPlans = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Debug API configuration
      const API_BASE_URL = getApiBaseUrl();
      const userId = getUserId();
      console.log('🔧 API Debug Info:', {
        API_BASE_URL,
        userId,
        companyId,
        timestamp: new Date().toISOString()
      });

      // Fetch actual company details with real employee count
      const companyDetails = await fetchCompanyDetails(companyId);
      setCompanyEmployeeCount(companyDetails.employeeCount || 250);
      console.log('Company employee count set to:', companyDetails.employeeCount || 250);

      // Fetch plan assignments for the company using optimized API
      console.log('🚀 Calling getPlanAssignmentsByCompany with params:', {
        companyId,
        filters: { includeExpired: true, includePlanData: true }
      });

      const result = await getPlanAssignmentsByCompany(companyId, {
        includeExpired: true,
        includePlanData: true
      });

      console.log('📊 API Response:', {
        success: result.success,
        hasData: !!result.data,
        error: result.error,
        dataKeys: result.data ? Object.keys(result.data) : null
      });

      if (result.success && result.data) {
        const planAssignments = result.data.assignments;
        console.log('Fetched plan assignments:', planAssignments);
        console.log('📈 Assignment count:', planAssignments?.length || 0);

        // Handle special case where broker has no existing assignments but can create new ones
        if (result.data.accessDeniedToExisting && result.data.canCreateAssignments) {
          console.log('🔧 Broker can create new plan assignments for this company');
          // Show empty state but allow plan creation
          setCompany(companyDetails);
          setPlans([]);
          return;
        }

        // Check if we have valid plan assignments
        if (!planAssignments || !Array.isArray(planAssignments)) {
          console.warn('⚠️ Invalid plan assignments data structure:', planAssignments);
          setError('Invalid data received from server. Please try again.');
          setPlans([]);
          return;
        }

        if (planAssignments.length === 0) {
          console.log('📭 No plan assignments found for company');
          setCompany(companyDetails);
          setPlans([]);
          return;
        }

        // Transform plan assignments to display format
        const displayPlans: PlanAssignmentDisplay[] = await Promise.all(
          planAssignments.map(async (assignment: PlanAssignment) => {
            // Get planId as string
            const planIdString = typeof assignment.planId === 'string' ? assignment.planId : assignment.planId?._id || '';
            console.log('Processing assignment:', assignment._id, 'with planId:', planIdString);

            // Fetch plan details using the planId
            let planDetails = null;
            if (planIdString) {
              planDetails = await fetchPlanDetails(planIdString);
              console.log('Fetched plan details:', planDetails);
            }

            // Determine display type based on coverage subtypes or coverage type
            let displayType: 'Medical' | 'Dental' | 'Vision' | 'Ancillary' = 'Medical';
            if (planDetails?.coverageSubTypes && planDetails.coverageSubTypes.length > 0) {
              const primarySubtype = planDetails.coverageSubTypes[0].toLowerCase();
              if (primarySubtype.includes('dental')) {
                displayType = 'Dental';
              } else if (primarySubtype.includes('vision')) {
                displayType = 'Vision';
              } else if (primarySubtype.includes('medical') || primarySubtype.includes('health')) {
                displayType = 'Medical';
              } else {
                displayType = 'Ancillary';
              }
            } else if (planDetails?.coverageType) {
              const coverageType = planDetails.coverageType.toLowerCase();
              if (coverageType.includes('dental')) {
                displayType = 'Dental';
              } else if (coverageType.includes('vision')) {
                displayType = 'Vision';
              } else if (coverageType.includes('medical') || coverageType.includes('health')) {
                displayType = 'Medical';
              } else {
                displayType = 'Ancillary';
              }
            }

            // Check if assignment can be edited
            let canEdit = false;
            let canDelete = false;

            try {
              const editCheckResult = await canEditPlanAssignment(assignment._id);
              canEdit = editCheckResult.success && editCheckResult.data ? editCheckResult.data.canEdit : false;
              // For simplicity, assume canDelete is same as canEdit for now
              canDelete = canEdit;
            } catch (error) {
              console.warn('Failed to check edit permissions for assignment:', assignment._id);
            }

            return {
              _id: assignment._id,
              planName: planDetails?.planName || 'Unknown Plan',
              planCode: planDetails?.planCode || 'N/A',
              carrier: planDetails?.carrierName || 'Unknown Carrier',
              type: displayType,
              metalTier: planDetails?.metalTier || '',
              period: `${new Date(assignment.planEffectiveDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })} - ${new Date(assignment.planEndDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}`,
              status: assignment.status as 'Active' | 'Draft' | 'Expired' | 'Inactive',
              assignmentId: assignment._id,
              planId: planIdString,
              canEdit,
              canDelete
            };
          })
        );

        console.log('Final display plans:', displayPlans);
        setCompany(companyDetails);
        setPlans(displayPlans);
      } else {
        console.error('❌ API call failed:', {
          success: result.success,
          error: result.error,
          data: result.data
        });
        setError(result.error || 'Failed to fetch plan assignments. Please check your permissions and try again.');
        setPlans([]);
      }
    } catch (error) {
      console.error('💥 Exception in fetchCompanyAndPlans:', error);
      setError(`Failed to fetch plan assignments: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setPlans([]);
    } finally {
      setLoading(false);
    }
  }, [companyId]);

  useEffect(() => {
    fetchCompanyAndPlans();
  }, [fetchCompanyAndPlans]);

  // Fetch plan assignment details when plans are loaded
  useEffect(() => {
    if (plans.length > 0) {
      fetchAllPlanAssignmentDetails();
    }
  }, [plans]);

  // Calculate estimated monthly cost when plan assignment details and employee count are available
  useEffect(() => {
    if (Object.keys(planAssignmentDetails).length > 0 && companyEmployeeCount > 0) {
      const calculatedCost = calculateEstimatedMonthlyCost();
      setEstimatedMonthlyCost(calculatedCost);
    }
  }, [planAssignmentDetails, companyEmployeeCount]);

  const handleSelectAll = () => {
    // Check if all filtered plans are selected (across all pages)
    const filteredPlanIds = filteredPlans.map(plan => plan._id);
    const allFilteredSelected = filteredPlanIds.every(id => selectedPlans.includes(id));

    if (allFilteredSelected) {
      // Deselect all filtered plans
      setSelectedPlans(prev => prev.filter(id => !filteredPlanIds.includes(id)));
    } else {
      // Select all filtered plans (add to existing selection)
      setSelectedPlans(prev => [...new Set([...prev, ...filteredPlanIds])]);
    }
  };

  const handleSelectCurrentPage = () => {
    // Check if all plans on current page are selected
    const currentPagePlanIds = paginatedPlans.map(plan => plan._id);
    const allCurrentPageSelected = currentPagePlanIds.every(id => selectedPlans.includes(id));

    if (allCurrentPageSelected) {
      // Deselect all plans on current page
      setSelectedPlans(prev => prev.filter(id => !currentPagePlanIds.includes(id)));
    } else {
      // Select all plans on current page
      setSelectedPlans(prev => [...new Set([...prev, ...currentPagePlanIds])]);
    }
  };

  const handlePlanSelect = (planId: string) => {
    setSelectedPlans(prev => 
      prev.includes(planId) 
        ? prev.filter(id => id !== planId)
        : [...prev, planId]
    );
  };

  const handleAddNewPlan = () => {
    setShowPlanSelectionModal(true);
  };

  const handlePlanSelected = async (selectedPlan: any) => {
    // Workflow 2: Create Fresh Plan Assignment from Template
    console.log('🔄 Workflow 2: Creating fresh plan assignment from template:', selectedPlan);

    try {
      // Create plan assignment with all required fields
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      // Set dates for next year enrollment
      const planEffectiveDate = `${nextYear}-01-01`;
      const planEndDate = `${nextYear}-12-31`;
      const enrollmentStartDate = `${currentYear}-11-01`; // Current year November
      const enrollmentEndDate = `${currentYear}-11-30`; // Current year November end

      const assignmentData = {
        planId: selectedPlan._id,
        companyId: companyId,
        // Required fields with defaults
        rateStructure: 'Composite', // Default rate structure
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 500,
            employerCost: 400,
            employeeCost: 100
          },
          {
            tierName: 'Employee + Spouse',
            totalCost: 1000,
            employerCost: 800,
            employeeCost: 200
          },
          {
            tierName: 'Employee + Child(ren)',
            totalCost: 800,
            employerCost: 640,
            employeeCost: 160
          },
          {
            tierName: 'Family',
            totalCost: 1500,
            employerCost: 1200,
            employeeCost: 300
          }
        ],
        planEffectiveDate,
        planEndDate,
        enrollmentStartDate,
        enrollmentEndDate,
        // Optional fields with defaults
        groupNumber: `GRP-${companyId}-${selectedPlan._id.slice(-6)}`,
        waitingPeriod: { enabled: false, days: 0, rule: 'Immediate' },
        enrollmentType: 'Active',
        employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
        employeeContribution: { contributionType: 'Percentage', contributionAmount: 20 },
        ageBandedRates: [],
        salaryBasedRates: [],
        planCustomizations: {},
        status: 'Draft' // Start as Draft for new assignments
      };

      const result = await createPlanAssignment(assignmentData);

      if (result.success && result.data) {
        console.log('✅ Fresh plan assignment created successfully:', result.data);
        // Immediately refresh the plans list to show the new assignment
        await fetchCompanyAndPlans();
      } else {
        console.error('❌ Failed to create plan assignment:', result.error);
        alert('Failed to assign plan to company: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error creating plan assignment:', error);
      alert('Failed to assign plan to company. Please try again.');
    }
  };

  const handleModalClose = () => {
    // No need to refresh here since we refresh immediately after each plan selection
    setShowPlanSelectionModal(false);
  };

  const handlePlanCreated = async (newPlan: any) => {
    // Workflow 2: Create Fresh Plan Assignment for newly created plan
    console.log('🔄 Workflow 2: Creating fresh plan assignment for new plan:', newPlan);

    try {
      // Create plan assignment with all required fields
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      // Set dates for next year enrollment
      const planEffectiveDate = `${nextYear}-01-01`;
      const planEndDate = `${nextYear}-12-31`;
      const enrollmentStartDate = `${currentYear}-11-01`; // Current year November
      const enrollmentEndDate = `${currentYear}-11-30`; // Current year November end

      const assignmentData = {
        planId: newPlan._id,
        companyId: companyId,
        // Required fields with defaults
        rateStructure: 'Composite', // Default rate structure
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 500,
            employerCost: 400,
            employeeCost: 100
          },
          {
            tierName: 'Employee + Spouse',
            totalCost: 1000,
            employerCost: 800,
            employeeCost: 200
          },
          {
            tierName: 'Employee + Child(ren)',
            totalCost: 800,
            employerCost: 640,
            employeeCost: 160
          },
          {
            tierName: 'Family',
            totalCost: 1500,
            employerCost: 1200,
            employeeCost: 300
          }
        ],
        planEffectiveDate,
        planEndDate,
        enrollmentStartDate,
        enrollmentEndDate,
        // Optional fields with defaults
        groupNumber: `GRP-${companyId}-${newPlan._id.slice(-6)}`,
        waitingPeriod: { enabled: false, days: 0, rule: 'Immediate' },
        enrollmentType: 'Active',
        employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
        employeeContribution: { contributionType: 'Percentage', contributionAmount: 20 },
        ageBandedRates: [],
        salaryBasedRates: [],
        planCustomizations: {},
        status: 'Draft' // Start as Draft for new assignments
      };

      const result = await createPlanAssignment(assignmentData);

      if (result.success && result.data) {
        // Refresh the plans list to include the new assignment
        await fetchCompanyAndPlans();
        alert('✅ Plan created and assigned to company successfully!');
      } else {
        console.error('❌ Failed to create plan assignment:', result.error);
        alert('Plan created but failed to assign to company: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error creating plan assignment:', error);
      alert('Plan created but failed to assign to company. Please try again.');
    }

    setShowPlanSelectionModal(false);
  };

  // Function to fetch plan assignment details including coverage tiers
  const fetchPlanAssignmentDetails = async (assignmentId: string) => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      console.log('Fetching plan assignment details for ID:', assignmentId);
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      console.log('Plan assignment fetch response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Plan assignment fetch response data:', data);
        console.log('Assignment object:', data.assignment);

        // Handle Mongoose document structure - data might be in _doc
        const assignment = data.assignment._doc || data.assignment;
        console.log('Processed assignment:', assignment);
        console.log('Coverage tiers in assignment:', assignment?.coverageTiers);

        return assignment;
      } else {
        console.error('Failed to fetch plan assignment details. Status:', response.status);
        const errorText = await response.text();
        console.error('Error response:', errorText);
      }
    } catch (error) {
      console.error('Error fetching plan assignment details:', error);
    }
    return null;
  };

  const handleEditPlan = async (planId: string) => {
    const plan = plans.find(p => p._id === planId);
    if (plan) {
      // Check backend can-edit functionality
      const editCheckResult = await canEditPlanAssignment(plan.assignmentId);

      if (!editCheckResult.success) {
        alert('Failed to check edit permissions. Please try again.');
        return;
      }

      // Note: Workflow type is determined automatically based on edit permissions
      // Workflow 2: Direct update (no enrollments) - editCheckResult.data?.canEdit === true
      // Workflow 1: Clone for rollover (has enrollments) - editCheckResult.data?.canEdit === false

      // Fetch actual plan assignment details to get current coverage tiers
      console.log('Fetching assignment details for:', plan.assignmentId);
      const assignmentDetails = await fetchPlanAssignmentDetails(plan.assignmentId);
      console.log('Assignment details received:', assignmentDetails);

      if (assignmentDetails && assignmentDetails.coverageTiers && assignmentDetails.coverageTiers.length > 0) {
        // Load actual coverage tiers from the assignment
        console.log('Raw coverage tiers from assignment:', assignmentDetails.coverageTiers);

        const actualTiers = assignmentDetails.coverageTiers.map((tier: any, index: number) => {
          const employerPercent = tier.totalCost > 0 ? Math.round((tier.employerCost / tier.totalCost) * 100) : 80;
          const tierData = {
            id: (index + 1).toString(),
            tier: tier.tierName,
            premium: tier.totalCost,
            employeePercent: employerPercent,
            employerPays: tier.employerCost,
            employeePays: tier.employeeCost
          };
          console.log(`Mapped tier ${index + 1}:`, tierData);
          return tierData;
        });

        console.log('Setting actual coverage tiers:', actualTiers);
        setCoverageTiers(actualTiers);
      } else {
        console.warn('No coverage tiers found in assignment, using defaults. Assignment details:', assignmentDetails);
        // Reset to default tiers if no data found
        const defaultTiers = [
          { id: '1', tier: 'Employee Only', premium: 450.00, employeePercent: 80, employerPays: 360.00, employeePays: 90.00 },
          { id: '2', tier: 'Employee + Spouse', premium: 880.00, employeePercent: 80, employerPays: 712.00, employeePays: 178.00 },
          { id: '3', tier: 'Employee + Children', premium: 720.00, employeePercent: 80, employerPays: 576.00, employeePays: 144.00 },
          { id: '4', tier: 'Employee + Family', premium: 1250.00, employeePercent: 80, employerPays: 1000.00, employeePays: 250.00 }
        ];
        console.log('Setting default tiers:', defaultTiers);
        setCoverageTiers(defaultTiers);
      }

      setEditingPlan(plan);
      setShowEditModal(true);
    }
  };

  const handleSaveEdit = async () => {
    if (!editingPlan) return;

    try {
      // Check if the plan can be edited first
      const editCheckResult = await canEditPlanAssignment(editingPlan.assignmentId);

      if (!editCheckResult.success) {
        alert('Failed to check edit permissions. Please try again.');
        return;
      }

      let result;
      const updateData = {
        rateStructure: 'Composite', // Required field for validation
        coverageTiers: coverageTiers.map(tier => {
          // Ensure costs add up correctly to avoid validation errors
          const totalCost = parseFloat(tier.premium.toFixed(2));
          const employerCost = parseFloat(tier.employerPays.toFixed(2));
          const employeeCost = parseFloat(tier.employeePays.toFixed(2));

          // Verify the math adds up (backend validation requirement)
          const calculatedTotal = employerCost + employeeCost;
          const finalTotalCost = Math.abs(totalCost - calculatedTotal) > 0.01 ? calculatedTotal : totalCost;

          console.log(`Tier ${tier.tier}: total=${finalTotalCost}, employer=${employerCost}, employee=${employeeCost}`);

          return {
            tierName: tier.tier,
            totalCost: finalTotalCost,
            employerCost: employerCost,
            employeeCost: employeeCost
          };
        }),
        ageBandedRates: [] // Required to be empty for non-age-banded structures
      };

      if (editCheckResult.data?.canEdit) {
        // Workflow 2: Direct Update (no enrollments assigned)
        console.log('🔄 Workflow 2: Updating plan assignment directly (no enrollments)');

        result = await updatePlanAssignment(editingPlan.assignmentId, updateData);

        if (result.success) {
          alert('✅ Plan assignment updated successfully.');
        } else {
          console.error('Update failed with error:', result.error);
          alert('❌ Failed to update plan assignment: ' + result.error);
        }
      } else {
        // Workflow 1: Clone for Rollover (has enrollments)
        console.log('🔄 Workflow 1: Cloning plan assignment for rollover (has enrollments)');

        // Prepare clone data with next year dates
        const currentYear = new Date().getFullYear();
        const nextYear = currentYear + 1;

        const cloneData = {
          rateStructure: updateData.rateStructure,
          coverageTiers: updateData.coverageTiers,
          ageBandedRates: updateData.ageBandedRates,
          // Update dates for next year
          planEffectiveDate: `${nextYear}-01-01`,
          planEndDate: `${nextYear}-12-31`,
          enrollmentStartDate: `${currentYear}-11-01`,
          enrollmentEndDate: `${currentYear}-11-30`,
          // Reset status for new assignment
          status: 'Draft'
        };

        result = await clonePlanAssignment(editingPlan.assignmentId, cloneData);

        if (result.success) {
          alert(`✅ Plan has active enrollments. Created a new ${nextYear} version with your changes.`);
        } else {
          console.error('Clone failed with error:', result.error);
          alert('❌ Failed to create new version: ' + result.error);
        }
      }

      if (result.success) {
        // Refresh the plans list
        await fetchCompanyAndPlans();
        setShowEditModal(false);
        setEditingPlan(null);
      }
    } catch (error) {
      console.error('Error updating plan assignment:', error);
      alert('Failed to update plan assignment. Please try again.');
    }
  };

  const handleCloseModal = () => {
    setShowEditModal(false);
    setEditingPlan(null);
  };

  const handleStatusToggle = async (plan: PlanAssignmentDisplay) => {
    try {
      setLoading(true);

      let result;
      if (plan.status === 'Active') {
        result = await deactivatePlanAssignment(plan.assignmentId);
      } else if (plan.status === 'Draft' || plan.status === 'Inactive') {
        result = await activatePlanAssignment(plan.assignmentId);
      } else {
        console.error('Cannot toggle status for plan with status:', plan.status);
        return;
      }

      if (result.success) {
        // Refresh the plans list to show updated status
        await fetchCompanyAndPlans();
        console.log('Plan status updated successfully:', result.data?.message);
      } else {
        console.error('Failed to update plan status:', result.error);
        setError(result.error || 'Failed to update plan status');
      }
    } catch (error) {
      console.error('Error toggling plan status:', error);
      setError('Failed to update plan status');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePlan = async (plan: PlanAssignmentDisplay) => {
    if (!confirm(`Are you sure you want to delete the plan assignment "${plan.planName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setLoading(true);

      const result = await deletePlanAssignment(plan.assignmentId);

      if (result.success) {
        // Refresh the plans list to remove the deleted plan
        await fetchCompanyAndPlans();
        console.log('Plan assignment deleted successfully');
      } else {
        console.error('Failed to delete plan assignment:', result.error);
        setError(result.error || 'Failed to delete plan assignment');
      }
    } catch (error) {
      console.error('Error deleting plan assignment:', error);
      setError('Failed to delete plan assignment');
    } finally {
      setLoading(false);
    }
  };

  const handleClonePlan = async (plan: PlanAssignmentDisplay) => {
    try {
      setLoading(true);

      // Clone the plan assignment with updated dates for next year
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      const cloneData = {
        planEffectiveDate: `${nextYear}-01-01`,
        planEndDate: `${nextYear}-12-31`,
        enrollmentStartDate: `${currentYear}-11-01`,
        enrollmentEndDate: `${currentYear}-11-30`,
        status: 'Draft' // Start cloned assignments as Draft
      };

      const result = await clonePlanAssignment(plan.assignmentId, cloneData);

      if (result.success) {
        // Refresh the plans list to show the cloned plan
        await fetchCompanyAndPlans();
        console.log('Plan assignment cloned successfully');
      } else {
        console.error('Failed to clone plan assignment:', result.error);
        setError(result.error || 'Failed to clone plan assignment');
      }
    } catch (error) {
      console.error('Error cloning plan assignment:', error);
      setError('Failed to clone plan assignment');
    } finally {
      setLoading(false);
    }
  };

  const updateTier = (id: string, field: string, value: number) => {
    setCoverageTiers(prev => prev.map(tier => {
      if (tier.id === id) {
        const updated = { ...tier, [field]: value };

        // Recalculate employer/employee pays based on contribution type
        if (contributionType === 'percentage') {
          // In percentage mode: employer % is editable, employer pays is calculated
          if (field === 'premium' || field === 'employeePercent') {
            updated.employerPays = (updated.premium * updated.employeePercent) / 100;
            updated.employeePays = updated.premium - updated.employerPays;
          }
        } else if (contributionType === 'fixed') {
          // In fixed mode: employer pays is editable, employer % is calculated
          if (field === 'premium' || field === 'employerPays') {
            updated.employeePays = updated.premium - updated.employerPays;
            updated.employeePercent = updated.premium > 0 ? (updated.employerPays / updated.premium) * 100 : 0;
          }
        }

        return updated;
      }
      return tier;
    }));
  };

  const deleteTier = (id: string) => {
    setCoverageTiers(prev => prev.filter(tier => tier.id !== id));
  };

  const addNewTier = () => {
    const newId = (coverageTiers.length + 1).toString();
    const newTier = {
      id: newId,
      tier: 'New Coverage Tier',
      premium: 500.00,
      employeePercent: 80,
      employerPays: 400.00,
      employeePays: 100.00
    };
    setCoverageTiers(prev => [...prev, newTier]);
  };

  const handleContinueWithSelected = () => {
    if (selectedPlans.length === 0) {
      alert('Please select at least one plan to continue.');
      return;
    }

    // Navigate directly to set dates page (skip enrollment-dates step)
    const selectedPlanIds = selectedPlans.join(',');
    router.push(`/ai-enroller/manage-groups/company/${companyId}/set-dates?plans=${selectedPlanIds}`);
  };

  // Filter plans based on selected filters
  const filteredPlans = plans.filter(plan => {
    const statusMatch = statusFilter === 'all' || plan.status === statusFilter;
    const carrierMatch = carrierFilter === 'all' || plan.carrier === carrierFilter;
    return statusMatch && carrierMatch;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredPlans.length / plansPerPage);
  const startIndex = (currentPage - 1) * plansPerPage;
  const endIndex = startIndex + plansPerPage;
  const paginatedPlans = filteredPlans.slice(startIndex, endIndex);

  // Reset to page 1 when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, carrierFilter]);

  const groupedPlans = paginatedPlans.reduce((acc, plan) => {
    if (!acc[plan.type]) {
      acc[plan.type] = [];
    }
    acc[plan.type].push(plan);
    return acc;
  }, {} as Record<string, PlanAssignmentDisplay[]>);

  // Function to get sorted category order
  const getSortedCategories = () => {
    const preferredOrder = ['Medical', 'Dental', 'Vision', 'Ancillary'];
    const availableCategories = Object.keys(groupedPlans);

    // First add categories in preferred order
    const sortedCategories = [];
    for (const preferred of preferredOrder) {
      if (availableCategories.includes(preferred)) {
        sortedCategories.push(preferred);
      }
    }

    // Then add any remaining categories not in preferred order
    for (const category of availableCategories) {
      if (!sortedCategories.includes(category)) {
        sortedCategories.push(category);
      }
    }

    return sortedCategories;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading plan assignments...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl">
            <h3 className="font-bold">Error Loading Plans</h3>
            <p>{error}</p>
            <button
              onClick={fetchCompanyAndPlans}
              className="mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  const headerActions = (
    <button
      onClick={handleAddNewPlan}
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: '10px 16px',
        background: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        cursor: 'pointer',
        transition: 'all 0.2s'
      }}
    >
      <HiOutlinePlus size={16} />
      Create New Plan
    </button>
  );

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <EnrollmentHeader />

        <div style={{ background: 'white', minHeight: '100vh' }}>
          {/* Plan Management Header */}
          <div style={{
            background: 'white',
            padding: '24px 0',
            borderBottom: '1px solid #E5E7EB'
          }}>
            <div style={{
              maxWidth: '95%',
              margin: '0 auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '0 2%'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  background: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlineClipboardList style={{ width: '18px', height: '18px', color: 'white' }} />
                </div>
                <div>
                  <h1 style={{
                    fontSize: '24px',
                    fontWeight: '600',
                    color: '#111827',
                    margin: 0
                  }}>
                    {company?.companyName || 'Loading...'} - Plan Management
                  </h1>
                  <p style={{
                    fontSize: '14px',
                    color: '#6B7280',
                    margin: 0
                  }}>
                    Manage plan assignments for {company?.employeeCount || 0} employees
                  </p>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '12px' }}>
                <button style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '10px 16px',
                  background: 'white',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  color: '#374151',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  <HiOutlineQuestionMarkCircle size={16} />
                  Ask Questions
                </button>
                <button style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '10px 16px',
                  background: 'white',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  color: '#374151',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  <HiOutlineViewGrid size={16} />
                  Dashboard
                </button>
                {headerActions}
              </div>
            </div>
          </div>

          {/* Statistics Cards */}
          {stats && (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
              gap: '16px',
              maxWidth: '95%',
              margin: '24px auto',
              padding: '0 2%'
            }}>
              <div style={{
                background: '#EFF6FF',
                border: '1px solid #DBEAFE',
                borderRadius: '12px',
                padding: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <div>
                  <div style={{ fontSize: '14px', color: '#2563EB', fontWeight: '500', marginBottom: '4px' }}>
                    Total Plans
                  </div>
                  <div style={{ fontSize: '32px', fontWeight: '700', color: '#1E40AF' }}>
                    {stats.totalPlans}
                  </div>
                </div>
                <div style={{
                  width: '48px',
                  height: '48px',
                  background: '#3B82F6',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlineClipboardList style={{ width: '24px', height: '24px', color: 'white' }} />
                </div>
              </div>

              <div style={{
                background: '#F0FDF4',
                border: '1px solid #BBF7D0',
                borderRadius: '12px',
                padding: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <div>
                  <div style={{ fontSize: '14px', color: '#16A34A', fontWeight: '500', marginBottom: '4px' }}>
                    Active Plans
                  </div>
                  <div style={{ fontSize: '32px', fontWeight: '700', color: '#15803D' }}>
                    {stats.activePlans}
                  </div>
                </div>
                <div style={{
                  width: '48px',
                  height: '48px',
                  background: '#22C55E',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlineCheckCircle style={{ width: '24px', height: '24px', color: 'white' }} />
                </div>
              </div>

              <div style={{
                background: '#FEF3C7',
                border: '1px solid #FDE68A',
                borderRadius: '12px',
                padding: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <div>
                  <div style={{ fontSize: '14px', color: '#D97706', fontWeight: '500', marginBottom: '4px' }}>
                    Recent Plans
                  </div>
                  <div style={{ fontSize: '32px', fontWeight: '700', color: '#B45309' }}>
                    {stats.recentPlans}
                  </div>
                </div>
                <div style={{
                  width: '48px',
                  height: '48px',
                  background: '#F59E0B',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlineClock style={{ width: '24px', height: '24px', color: 'white' }} />
                </div>
              </div>
            </div>
          )}

          {/* Search and Filter */}
          <div style={{
            background: 'white',
            border: '1px solid #E5E7EB',
            borderRadius: '12px',
            padding: '24px',
            margin: '0 auto 24px',
            maxWidth: '95%',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              marginBottom: '16px'
            }}>
              <HiOutlineSearch style={{ width: '16px', height: '16px', color: '#6B7280' }} />
              <span style={{ fontSize: '16px', fontWeight: '500', color: '#374151' }}>Search & Filter</span>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '12px',
              marginBottom: '16px'
            }}>
              <input
                type="text"
                placeholder="Search by plan name, code, or carrier type..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                style={{
                  padding: '10px 12px',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  fontSize: '14px',
                  outline: 'none',
                  transition: 'border-color 0.2s',
                  gridColumn: 'span 2'
                }}
              />

              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                style={{
                  padding: '10px 12px',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  fontSize: '14px',
                  outline: 'none',
                  background: 'white'
                }}
              >
                <option value="all">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="draft">Draft</option>
                <option value="template">Template</option>
                <option value="archived">Archived</option>
              </select>

              <select
                value={carrierFilter}
                onChange={(e) => setCarrierFilter(e.target.value)}
                style={{
                  padding: '10px 12px',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  fontSize: '14px',
                  outline: 'none',
                  background: 'white'
                }}
              >
                <option value="all">All Carriers</option>
                {/* Add carrier options here */}
              </select>

              <button
                onClick={() => {
                  setSearchQuery('');
                  setFilterType('all');
                  setCarrierFilter('all');
                }}
                style={{
                  padding: '10px 16px',
                  background: 'white',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  cursor: 'pointer'
                }}
              >
                Clear Filters
              </button>
            </div>

            <div style={{
              fontSize: '14px',
              color: '#6B7280'
            }}>
              Showing {filteredPlans.length} of {plans.length} plans
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '48px 24px',
              background: 'white',
              borderRadius: '12px',
              margin: '0 auto',
              maxWidth: '95%',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #F3F4F6',
                borderTop: '4px solid #6366F1',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                marginBottom: '16px'
              }}></div>
              <p style={{ fontSize: '16px', color: '#6B7280', margin: 0 }}>Loading plans...</p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div style={{
              textAlign: 'center',
              padding: '48px 24px',
              background: 'white',
              borderRadius: '12px',
              margin: '0 auto',
              maxWidth: '95%',
              border: '1px solid #FEE2E2',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}>
              <div style={{
                width: '48px',
                height: '48px',
                background: '#FEE2E2',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 16px'
              }}>
                <HiOutlineExclamationTriangle style={{ width: '24px', height: '24px', color: '#DC2626' }} />
              </div>
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: '0 0 8px' }}>Error Loading Plans</h3>
              <p style={{ fontSize: '14px', color: '#6B7280', margin: '0 0 24px' }}>{error}</p>
              <button
                onClick={fetchCompanyAndPlans}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '10px 16px',
                  background: '#DC2626',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Try Again
              </button>
            </div>
          )}

          {/* Plans Table */}
          {!loading && !error && (
            <div style={{ maxWidth: '95%', margin: '0 auto', padding: '0 2%' }}>
              {filteredPlans.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '48px 24px',
                  background: 'white',
                  borderRadius: '12px',
                  border: '1px solid #E5E7EB',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                }}>
                  <HiOutlineClipboardList size={48} style={{ color: '#9CA3AF', margin: '0 auto 16px' }} />
                  <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: '0 0 8px' }}>No Plans Found</h3>
                  <p style={{ fontSize: '14px', color: '#6B7280', margin: '0 0 24px' }}>
                    {plans.length === 0
                      ? "No plan assignments found for this company. Create your first plan assignment to get started."
                      : "No plans match your search criteria. Try adjusting your filters."
                    }
                  </p>
                  <button
                    onClick={handleAddNewPlan}
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '10px 16px',
                      background: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer'
                    }}
                  >
                    <HiOutlinePlus size={16} />
                    Create Your First Plan Assignment
                  </button>
                </div>
              ) : (
                <>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '24px'
                  }}>
                    <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>Plans List</h3>
                    <span style={{ fontSize: '14px', color: '#6B7280' }}>
                      {filteredPlans.length} plan{filteredPlans.length !== 1 ? 's' : ''}
                    </span>
                  </div>

                  {/* Plans Cards Grid */}
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
                    gap: '20px',
                    marginBottom: '24px'
                  }}>
                    {filteredPlans.map((plan) => {
                      const getCoverageTypeColor = (coverageType: string) => {
                        switch (coverageType?.toLowerCase()) {
                          case 'medical':
                          case 'health':
                            return { bg: '#EFF6FF', text: '#1E40AF', border: '#DBEAFE' };
                          case 'dental':
                            return { bg: '#F0FDF4', text: '#166534', border: '#BBF7D0' };
                          case 'vision':
                            return { bg: '#FEF3C7', text: '#92400E', border: '#FDE68A' };
                          case 'life':
                          case 'term life':
                            return { bg: '#F3E8FF', text: '#7C3AED', border: '#DDD6FE' };
                          default:
                            return { bg: '#F9FAFB', text: '#374151', border: '#E5E7EB' };
                        }
                      };

                      const getStatusColor = (status: string) => {
                        switch (status?.toLowerCase()) {
                          case 'active':
                            return { bg: '#D1FAE5', text: '#065F46' };
                          case 'template':
                            return { bg: '#DBEAFE', text: '#1E40AF' };
                          case 'draft':
                            return { bg: '#FEF3C7', text: '#92400E' };
                          case 'inactive':
                          case 'archived':
                            return { bg: '#F3F4F6', text: '#6B7280' };
                          default:
                            return { bg: '#F3F4F6', text: '#6B7280' };
                        }
                      };

                      const coverageColors = getCoverageTypeColor(plan.coverageType);
                      const statusColors = getStatusColor(plan.status);

                      return (
                        <div key={plan._id} style={{
                          background: 'white',
                          border: `1px solid ${coverageColors.border}`,
                          borderRadius: '12px',
                          padding: '20px',
                          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                          transition: 'all 0.2s',
                          position: 'relative'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                        }}>
                          {/* Status Badge */}
                          <div style={{
                            position: 'absolute',
                            top: '12px',
                            right: '12px'
                          }}>
                            <span style={{
                              display: 'inline-block',
                              padding: '4px 8px',
                              borderRadius: '12px',
                              fontSize: '12px',
                              fontWeight: '500',
                              background: statusColors.bg,
                              color: statusColors.text
                            }}>
                              {plan.status || 'Unknown'}
                            </span>
                          </div>

                          {/* Plan Header */}
                          <div style={{ marginBottom: '16px', paddingRight: '80px' }}>
                            <h4 style={{
                              fontSize: '16px',
                              fontWeight: '600',
                              color: '#111827',
                              margin: '0 0 4px 0'
                            }}>
                              {plan.planName}
                            </h4>
                            <p style={{
                              fontSize: '14px',
                              color: '#6B7280',
                              margin: 0
                            }}>
                              {plan.carrier || 'Unknown Carrier'}
                            </p>
                          </div>

                          {/* Plan Details */}
                          <div style={{
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr',
                            gap: '12px',
                            marginBottom: '16px'
                          }}>
                            <div>
                              <label style={{
                                fontSize: '12px',
                                fontWeight: '500',
                                color: '#6B7280',
                                textTransform: 'uppercase',
                                letterSpacing: '0.05em'
                              }}>
                                Plan Code
                              </label>
                              <p style={{
                                fontSize: '14px',
                                fontWeight: '500',
                                color: '#374151',
                                margin: '4px 0 0 0'
                              }}>
                                {plan.planCode || 'N/A'}
                              </p>
                            </div>
                            <div>
                              <label style={{
                                fontSize: '12px',
                                fontWeight: '500',
                                color: '#6B7280',
                                textTransform: 'uppercase',
                                letterSpacing: '0.05em'
                              }}>
                                Coverage Type
                              </label>
                              <div style={{ marginTop: '4px' }}>
                                <span style={{
                                  display: 'inline-block',
                                  padding: '4px 8px',
                                  borderRadius: '6px',
                                  fontSize: '12px',
                                  fontWeight: '500',
                                  background: coverageColors.bg,
                                  color: coverageColors.text,
                                  border: `1px solid ${coverageColors.border}`
                                }}>
                                  {plan.coverageType || 'Unknown'}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Groups Count */}
                          <div style={{ marginBottom: '16px' }}>
                            <label style={{
                              fontSize: '12px',
                              fontWeight: '500',
                              color: '#6B7280',
                              textTransform: 'uppercase',
                              letterSpacing: '0.05em'
                            }}>
                              Assigned Groups
                            </label>
                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                              marginTop: '4px'
                            }}>
                              <span style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '24px',
                                height: '24px',
                                borderRadius: '50%',
                                background: '#EFF6FF',
                                color: '#2563EB',
                                fontSize: '12px',
                                fontWeight: '600'
                              }}>
                                {plan.assignedGroups || 0}
                              </span>
                              <span style={{
                                fontSize: '14px',
                                color: '#6B7280'
                              }}>
                                group{(plan.assignedGroups || 0) !== 1 ? 's' : ''}
                              </span>
                            </div>
                          </div>

                          {/* Actions */}
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingTop: '16px',
                            borderTop: '1px solid #F3F4F6'
                          }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <button
                                onClick={() => handleEditPlan(plan)}
                                style={{
                                  padding: '6px 12px',
                                  background: 'transparent',
                                  border: '1px solid #D1D5DB',
                                  borderRadius: '6px',
                                  color: '#374151',
                                  fontSize: '12px',
                                  fontWeight: '500',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '4px'
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.background = '#F3F4F6';
                                  e.currentTarget.style.borderColor = '#9CA3AF';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.background = 'transparent';
                                  e.currentTarget.style.borderColor = '#D1D5DB';
                                }}
                                title="Edit Plan"
                              >
                                <HiOutlinePencil size={14} />
                                Edit
                              </button>

                              {plan.status === 'Active' ? (
                                <button
                                  onClick={() => handleDeactivatePlan(plan._id)}
                                  style={{
                                    padding: '6px 12px',
                                    background: 'transparent',
                                    border: '1px solid #FDE68A',
                                    borderRadius: '6px',
                                    color: '#D97706',
                                    fontSize: '12px',
                                    fontWeight: '500',
                                    cursor: 'pointer',
                                    transition: 'all 0.2s',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '4px'
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.background = '#FEF3C7';
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.background = 'transparent';
                                  }}
                                  title="Deactivate Plan"
                                >
                                  <HiOutlinePause size={14} />
                                  Deactivate
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleActivatePlan(plan._id)}
                                  style={{
                                    padding: '6px 12px',
                                    background: 'transparent',
                                    border: '1px solid #BBF7D0',
                                    borderRadius: '6px',
                                    color: '#059669',
                                    fontSize: '12px',
                                    fontWeight: '500',
                                    cursor: 'pointer',
                                    transition: 'all 0.2s',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '4px'
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.background = '#D1FAE5';
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.background = 'transparent';
                                  }}
                                  title="Activate Plan"
                                >
                                  <HiOutlinePlay size={14} />
                                  Activate
                                </button>
                              )}
                            </div>

                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <button
                                onClick={() => handleViewPlan(plan._id)}
                                style={{
                                  padding: '6px',
                                  background: 'transparent',
                                  border: 'none',
                                  borderRadius: '4px',
                                  color: '#6B7280',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s'
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.background = '#F3F4F6';
                                  e.currentTarget.style.color = '#374151';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.background = 'transparent';
                                  e.currentTarget.style.color = '#6B7280';
                                }}
                                title="View Plan Details"
                              >
                                <HiOutlineEye size={16} />
                              </button>

                              <button
                                onClick={() => handleDeletePlan(plan._id)}
                                style={{
                                  padding: '6px',
                                  background: 'transparent',
                                  border: 'none',
                                  borderRadius: '4px',
                                  color: '#6B7280',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s'
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.background = '#FEE2E2';
                                  e.currentTarget.style.color = '#DC2626';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.background = 'transparent';
                                  e.currentTarget.style.color = '#6B7280';
                                }}
                                title="Delete Plan"
                              >
                                <HiOutlineTrash size={16} />
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default CompanyPlansPage;
