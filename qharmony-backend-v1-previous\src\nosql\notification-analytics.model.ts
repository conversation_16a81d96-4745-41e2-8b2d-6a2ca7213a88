import mongoose, { Document, Model } from 'mongoose';
import logger from '../utils/logger';

const { Schema } = mongoose;

/**
 * Interface representing notification analytics.
 */
export interface NotificationAnalyticsDataInterface {
    _id?: mongoose.Types.ObjectId;
    messageId: string;      // Adaptive card id
    messageType: string;    // Button clicked
    useremail: string;      // User email
    tenantId: string;      // Tenant id
    companyName: string;   // Company name
    companyId: string;     // Company id
    createdAt?: Date;      // Will be automatically handled by Mongoose
    updatedAt?: Date;      // Will be automatically handled by Mongoose
}

/**
 * Mongoose document interface (excluding _id conflict).
 */
interface NotificationAnalyticsDocument extends Document, Omit<NotificationAnalyticsDataInterface, '_id'> { }

class NotificationAnalyticsModelClass {
    private static notificationAnalyticsModel: Model<NotificationAnalyticsDocument>;

    /**
     * Initializes the Mongoose model.
     */
    public static initializeModel() {
        const schema = new Schema({
            messageId: { type: String, required: true },
            messageType: { type: String, required: true },
            useremail: { type: String, required: true },
            tenantId: { type: String, required: true },
            companyName: { type: String, required: true }, // Added companyName field
            companyId: { type: String, required: true }   // Added companyId field
        }, {
            timestamps: true  // This will automatically add createdAt and updatedAt fields
        });

        // Add indexes for better query performance
        schema.index({ messageId: 1 });
        schema.index({ tenantId: 1 });
        schema.index({ companyId: 1 }); // Added index for companyId
        schema.index({ createdAt: -1 });

        this.notificationAnalyticsModel = mongoose.model<NotificationAnalyticsDocument>('NotificationAnalytics', schema);
    }

    /**
     * Creates a new notification analytics entry.
     * @param analyticsData - The notification analytics data.
     * @returns The created analytics entry's ID as a string, or null if an error occurs.
     */
    public static async createAnalytics(analyticsData: Omit<NotificationAnalyticsDataInterface, '_id' | 'createdAt' | 'updatedAt'>): Promise<string | null> {
        try {
            const analytics = await this.notificationAnalyticsModel.create(analyticsData);
            return analytics._id.toString();
        } catch (error) {
            logger.error("Error creating notification analytics:", error);
            return null;
        }
    }

    /**
     * Retrieves analytics by message ID.
     * @param messageId - The ID of the message.
     * @returns Array of analytics entries for the message.
     */
    public static async getAnalyticsByMessageId(messageId: string): Promise<NotificationAnalyticsDataInterface[]> {
        try {
            const analytics = await this.notificationAnalyticsModel.find({ messageId })
                .sort({ createdAt: -1 })
                .lean();
            // Use unknown as an intermediate type to fix the TypeScript error
            return analytics as unknown as NotificationAnalyticsDataInterface[];
        } catch (error) {
            logger.error(`Error fetching analytics for messageId: ${messageId}`, error);
            return [];
        }
    }

    /**
     * Retrieves analytics by tenant ID.
     * @param tenantId - The tenant ID.
     * @returns Array of analytics entries for the tenant.
     */
    public static async getAnalyticsByTenantId(tenantId: string): Promise<NotificationAnalyticsDataInterface[]> {
        try {
            const analytics = await this.notificationAnalyticsModel.find({ tenantId })
                .sort({ createdAt: -1 })
                .lean();
            // Use unknown as an intermediate type to fix the TypeScript error
            return analytics as unknown as NotificationAnalyticsDataInterface[];
        } catch (error) {
            logger.error(`Error fetching analytics for tenantId: ${tenantId}`, error);
            return [];
        }
    }

    /**
     * Retrieves analytics by message ID and tenant ID.
     * @param messageId - The ID of the message.
     * @param tenantId - The tenant ID.
     * @returns Array of analytics entries for the message and tenant.
     */
    public static async getAnalyticsByMessageIdAndTenantId(messageId: string, tenantId: string): Promise<NotificationAnalyticsDataInterface[]> {
        try {
            const analytics = await this.notificationAnalyticsModel.find({ messageId, tenantId }).lean();
            // Use unknown as an intermediate type to fix the TypeScript error
            return analytics as unknown as NotificationAnalyticsDataInterface[];
        } catch (error) {
            logger.error(`Error fetching analytics for messageId: ${messageId} and tenantId: ${tenantId}`, error);
            return [];
        }
    }
}

// Initialize the model
NotificationAnalyticsModelClass.initializeModel();

export default NotificationAnalyticsModelClass;
