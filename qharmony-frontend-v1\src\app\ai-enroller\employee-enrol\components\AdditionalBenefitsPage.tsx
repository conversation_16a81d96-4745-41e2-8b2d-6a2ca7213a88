'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { User, CheckCircle, X, Heart, Shield } from 'lucide-react';

interface AdditionalBenefit {
  id: string;
  name: string;
  cost: number;
  features: string[];
  category: 'pet' | 'hospital';
}

interface AdditionalBenefitsPageProps {
  onBenefitsSelect: (benefits: AdditionalBenefit[]) => void;
}

const AdditionalBenefitsPage: React.FC<AdditionalBenefitsPageProps> = ({ onBenefitsSelect }) => {
  const [selectedBenefits, setSelectedBenefits] = useState<string[]>([]);

  const additionalBenefits: AdditionalBenefit[] = [
    {
      id: 'metlife-pet',
      name: 'MetLife Pet Plan',
      cost: 18.50,
      category: 'pet',
      features: [
        'Up to 90% reimbursement for vet bills',
        'No age limits for enrollment',
        'Coverage for accidents & illness',
        'Optional wellness add-on available'
      ]
    },
    {
      id: 'aspca-pet',
      name: 'ASPCA Pet Health Plan',
      cost: 22.00,
      category: 'pet',
      features: [
        'Comprehensive accident & illness coverage',
        'Hereditary condition coverage',
        '24/7 pet helpline',
        'No breed restrictions'
      ]
    },
    {
      id: 'hospital-cash',
      name: 'Hospital Cash Plan',
      cost: 8.75,
      category: 'hospital',
      features: [
        'Cash benefit for hospital stays',
        'No network restrictions',
        'Use funds however you need',
        'Covers unexpected medical expenses'
      ]
    },
    {
      id: 'critical-illness',
      name: 'Critical Illness Plan',
      cost: 12.50,
      category: 'hospital',
      features: [
        'Lump sum for critical illness diagnosis',
        'Cancer, heart attack, stroke coverage',
        'Help with out-of-pocket expenses',
        'Peace of mind protection'
      ]
    }
  ];

  const handleBenefitToggle = (benefitId: string) => {
    const newSelected = selectedBenefits.includes(benefitId)
      ? selectedBenefits.filter(id => id !== benefitId)
      : [...selectedBenefits, benefitId];
    
    setSelectedBenefits(newSelected);
    
    const selectedBenefitObjects = additionalBenefits.filter(benefit => 
      newSelected.includes(benefit.id)
    );
    onBenefitsSelect(selectedBenefitObjects);
  };

  const petBenefits = additionalBenefits.filter(b => b.category === 'pet');
  const hospitalBenefits = additionalBenefits.filter(b => b.category === 'hospital');

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0
          }}>
            ⭐ Looking for extra coverage?
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0'
          }}>
            Check out personalized add-ons that offer more protection and savings.
          </p>
        </div>
      </div>

      {/* Additional Benefits */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        border: '1px solid #e5e7eb', 
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <span style={{ fontSize: '18px' }}>🌟</span>
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            Personalized Benefit Recommendations
          </h2>
        </div>

        <p style={{
          color: '#6b7280',
          marginBottom: '24px',
          margin: 0
        }}>
          We&apos;ve reviewed your profile and selected optional benefits you might find valuable — all designed to offer extra support where it matters most.
        </p>

        {/* Pet Insurance Section */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '24px',
          marginBottom: '24px',
          border: '1px solid #e5e7eb',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <Heart style={{ width: '20px', height: '20px', color: '#ea580c' }} />
            <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#111827', margin: 0 }}>
              Pet Insurance Plans 🐾
            </h3>
          </div>

          <p style={{ color: '#6b7280', fontSize: '14px', marginBottom: '24px', margin: 0 }}>
            Protect your furry family members with comprehensive pet insurance coverage:
          </p>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {petBenefits.map((benefit) => (
              <div
                key={benefit.id}
                style={{
                  backgroundColor: 'white',
                  border: selectedBenefits.includes(benefit.id) ? '2px solid #ea580c' : '2px solid #fed7aa',
                  borderRadius: '8px',
                  padding: '20px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={() => handleBenefitToggle(benefit.id)}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                  <div>
                    <h4 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
                      {benefit.name}
                    </h4>
                    <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '4px' }}>
                      <span style={{
                        fontSize: '24px',
                        fontWeight: '700',
                        color: '#111827'
                      }}>
                        ${benefit.cost.toFixed(2)}
                      </span>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                    </div>
                  </div>
                  <div style={{
                    backgroundColor: '#ea580c',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}>
                    Pet Insurance
                  </div>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                  {benefit.features.map((feature, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <CheckCircle style={{ width: '16px', height: '16px', color: '#10b981', flexShrink: 0 }} />
                      <span style={{ color: '#374151', fontSize: '14px' }}>{feature}</span>
                    </div>
                  ))}
                </div>

                <button
                  style={{
                    width: '100%',
                    backgroundColor: selectedBenefits.includes(benefit.id) ? '#000000' : '#f3f4f6',
                    color: selectedBenefits.includes(benefit.id) ? 'white' : '#6b7280',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    fontWeight: '500',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleBenefitToggle(benefit.id);
                  }}
                >
                  {selectedBenefits.includes(benefit.id) ? '✓ Selected' : 'Select This Plan'}
                </button>
              </div>
            ))}
            
            <button
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '8px 16px',
                backgroundColor: 'white',
                border: '1px solid #fed7aa',
                borderRadius: '8px',
                color: '#92400e',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              <X size={16} />
              Skip Pet Insurance
            </button>
          </div>
        </div>

        {/* Hospital Indemnity Section */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '24px',
          border: '1px solid #e5e7eb',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <Shield style={{ width: '20px', height: '20px', color: '#2563eb' }} />
            <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#111827', margin: 0 }}>
              Hospital Indemnity Plans 🏥
            </h3>
          </div>

          <p style={{ color: '#6b7280', fontSize: '14px', marginBottom: '24px', margin: 0 }}>
            Get extra financial protection with cash benefits for hospital stays and medical expenses:
          </p>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {hospitalBenefits.map((benefit) => (
              <div
                key={benefit.id}
                style={{
                  backgroundColor: 'white',
                  border: selectedBenefits.includes(benefit.id) ? '2px solid #2563eb' : '2px solid #bfdbfe',
                  borderRadius: '8px',
                  padding: '20px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={() => handleBenefitToggle(benefit.id)}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                  <div>
                    <h4 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
                      {benefit.name}
                    </h4>
                    <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '4px' }}>
                      <span style={{
                        fontSize: '24px',
                        fontWeight: '700',
                        color: '#111827'
                      }}>
                        ${benefit.cost.toFixed(2)}
                      </span>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                    </div>
                  </div>
                  <div style={{
                    backgroundColor: '#2563eb',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}>
                    Hospital Coverage
                  </div>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                  {benefit.features.map((feature, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <CheckCircle style={{ width: '16px', height: '16px', color: '#10b981', flexShrink: 0 }} />
                      <span style={{ color: '#374151', fontSize: '14px' }}>{feature}</span>
                    </div>
                  ))}
                </div>

                <button
                  style={{
                    width: '100%',
                    backgroundColor: selectedBenefits.includes(benefit.id) ? '#000000' : '#f3f4f6',
                    color: selectedBenefits.includes(benefit.id) ? 'white' : '#6b7280',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    fontWeight: '500',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleBenefitToggle(benefit.id);
                  }}
                >
                  {selectedBenefits.includes(benefit.id) ? '✓ Selected' : 'Select This Plan'}
                </button>
              </div>
            ))}
            
            <button
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '8px 16px',
                backgroundColor: 'white',
                border: '1px solid #bfdbfe',
                borderRadius: '8px',
                color: '#1e40af',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              <X size={16} />
              Skip Hospital Indemnity
            </button>
          </div>
        </div>

        {/* Selection Summary */}
        {selectedBenefits.length > 0 && (
          <div style={{
            backgroundColor: '#f0f9ff',
            border: '1px solid #bae6fd',
            borderRadius: '8px',
            padding: '20px',
            marginTop: '24px'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#0c4a6e',
              margin: '0 0 16px 0'
            }}>
              Selected Additional Benefits ({selectedBenefits.length})
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {selectedBenefits.map(benefitId => {
                const benefit = additionalBenefits.find(b => b.id === benefitId);
                return benefit ? (
                  <div key={benefitId} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '1px solid #e0f2fe'
                  }}>
                    <div>
                      <span style={{ fontWeight: '600', color: '#111827', fontSize: '16px' }}>
                        {benefit.name}
                      </span>
                      <div style={{
                        fontSize: '12px',
                        color: benefit.category === 'pet' ? '#ea580c' : '#2563eb',
                        fontWeight: '500',
                        marginTop: '4px'
                      }}>
                        {benefit.category === 'pet' ? '🐾 Pet Insurance' : '🏥 Hospital Coverage'}
                      </div>
                    </div>
                    <span style={{
                      color: '#0c4a6e',
                      fontWeight: '700',
                      fontSize: '16px'
                    }}>
                      ${benefit.cost.toFixed(2)}/paycheck
                    </span>
                  </div>
                ) : null;
              })}
              <div style={{
                borderTop: '1px solid #bae6fd',
                paddingTop: '12px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '600', color: '#0c4a6e', fontSize: '16px' }}>
                  Total Additional Benefits:
                </span>
                <span style={{
                  fontWeight: '700',
                  color: '#0c4a6e',
                  fontSize: '18px'
                }}>
                  ${selectedBenefits.reduce((total, benefitId) => {
                    const benefit = additionalBenefits.find(b => b.id === benefitId);
                    return total + (benefit ? benefit.cost : 0);
                  }, 0).toFixed(2)}/paycheck
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdditionalBenefitsPage;
