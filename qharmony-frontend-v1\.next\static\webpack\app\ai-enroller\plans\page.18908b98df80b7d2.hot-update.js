"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/plans/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/plans/page.tsx":
/*!********************************************!*\
  !*** ./src/app/ai-enroller/plans/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineClipboardList,HiOutlineDuplicate,HiOutlinePause,HiOutlinePencil,HiOutlinePlay,HiOutlinePlus,HiOutlineQuestionMarkCircle,HiOutlineSearch,HiOutlineTrash,HiOutlineViewGrid,HiOutlineX!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=RiCalendarLine,RiHealthBookLine,RiMoneyDollarCircleLine,RiShieldCheckLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../create-plan/services/planApi */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/services/planApi.ts\");\n/* harmony import */ var _manage_groups_company_companyId_plans_components_CreatePlanForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../manage-groups/company/[companyId]/plans/components/CreatePlanForm */ \"(app-pages-browser)/./src/app/ai-enroller/manage-groups/company/[companyId]/plans/components/CreatePlanForm.tsx\");\n/* harmony import */ var _employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../employee-enrol/components/EnrollmentHeader */ \"(app-pages-browser)/./src/app/ai-enroller/employee-enrol/components/EnrollmentHeader.tsx\");\n/* harmony import */ var _plans_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plans.css */ \"(app-pages-browser)/./src/app/ai-enroller/plans/plans.css\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../utils/env */ \"(app-pages-browser)/./src/utils/env.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// API configuration\nconst API_BASE_URL = (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getApiBaseUrl)();\nconst PlansPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [carrierFilter, setCarrierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [carriers, setCarriers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPlanModal, setShowPlanModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPlan, setEditingPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [planAssignmentCounts, setPlanAssignmentCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Custom modal states\n    const [showConfirmModal, setShowConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmModalData, setConfirmModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAlertModal, setShowAlertModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [alertModalData, setAlertModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInputModal, setShowInputModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inputModalData, setInputModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadPlans();\n    }, []);\n    // Function to fetch assignment counts for all plans\n    const loadPlanAssignmentCounts = async (planIds)=>{\n        try {\n            const counts = {};\n            // Fetch assignment counts for each plan\n            await Promise.all(planIds.map(async (planId)=>{\n                try {\n                    const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments?planId=\").concat(planId), {\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                        }\n                    });\n                    if (response.ok) {\n                        const result = await response.json();\n                        counts[planId] = result.count || 0;\n                    } else {\n                        counts[planId] = 0;\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching assignment count for plan \".concat(planId, \":\"), error);\n                    counts[planId] = 0;\n                }\n            }));\n            setPlanAssignmentCounts(counts);\n        } catch (error) {\n            console.error(\"Error loading plan assignment counts:\", error);\n        }\n    };\n    const loadPlans = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load both plans and carriers\n            const [plansResult, carriersResult] = await Promise.all([\n                (0,_create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__.getPlans)(),\n                (0,_create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__.getCarriers)()\n            ]);\n            if (plansResult.success && plansResult.data) {\n                const plans = plansResult.data.plans;\n                setPlans(plans);\n                // Calculate statistics\n                const totalPlans = plans.length;\n                const activePlans = plans.filter((p)=>p.status === \"Active\").length;\n                const recentPlans = plans.filter((p)=>{\n                    if (!p.createdAt) return false;\n                    const createdDate = new Date(p.createdAt);\n                    const weekAgo = new Date();\n                    weekAgo.setDate(weekAgo.getDate() - 7);\n                    return createdDate > weekAgo;\n                });\n                const plansByStatus = plans.reduce((acc, plan)=>{\n                    const status = plan.status || \"Unknown\";\n                    acc[status] = (acc[status] || 0) + 1;\n                    return acc;\n                }, {});\n                setStats({\n                    totalPlans,\n                    plansByStatus,\n                    recentPlans\n                });\n                // Load assignment counts for all plans\n                const planIds = plans.map((plan)=>plan._id);\n                loadPlanAssignmentCounts(planIds);\n            } else {\n                setError(plansResult.error || \"Failed to load plans\");\n            }\n            // Load carriers for display purposes\n            if (carriersResult.success && carriersResult.data) {\n                setCarriers(carriersResult.data);\n            }\n        } catch (err) {\n            setError(\"Failed to load plans\");\n            console.error(\"Error loading plans:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredPlans = plans.filter((plan)=>{\n        var _plan_planType;\n        const matchesSearch = (plan.planName || \"\").toLowerCase().includes(searchQuery.toLowerCase()) || (plan.description || \"\").toLowerCase().includes(searchQuery.toLowerCase()) || (plan.planCode || \"\").toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesFilter = filterType === \"all\" || ((_plan_planType = plan.planType) === null || _plan_planType === void 0 ? void 0 : _plan_planType.toLowerCase()) === filterType.toLowerCase() || (plan.status || \"\").toLowerCase() === filterType.toLowerCase();\n        const matchesCarrier = carrierFilter === \"all\" || plan.carrierId === carrierFilter;\n        return matchesSearch && matchesFilter && matchesCarrier;\n    });\n    // Pagination logic\n    const totalPages = Math.ceil(filteredPlans.length / itemsPerPage);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    const paginatedPlans = filteredPlans.slice(startIndex, endIndex);\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    const handleClearFilters = ()=>{\n        setSearchQuery(\"\");\n        setFilterType(\"all\");\n        setCarrierFilter(\"all\");\n        setCurrentPage(1);\n    };\n    // Custom modal helpers\n    const showCustomAlert = (title, message, onClose)=>{\n        setAlertModalData({\n            title,\n            message,\n            onClose\n        });\n        setShowAlertModal(true);\n    };\n    const showCustomConfirm = (title, message, onConfirm, onCancel)=>{\n        setConfirmModalData({\n            title,\n            message,\n            onConfirm,\n            onCancel\n        });\n        setShowConfirmModal(true);\n    };\n    const closeAlertModal = ()=>{\n        setShowAlertModal(false);\n        if (alertModalData === null || alertModalData === void 0 ? void 0 : alertModalData.onClose) {\n            alertModalData.onClose();\n        }\n        setAlertModalData(null);\n    };\n    const closeConfirmModal = ()=>{\n        setShowConfirmModal(false);\n        if (confirmModalData === null || confirmModalData === void 0 ? void 0 : confirmModalData.onCancel) {\n            confirmModalData.onCancel();\n        }\n        setConfirmModalData(null);\n    };\n    const confirmAction = ()=>{\n        if (confirmModalData === null || confirmModalData === void 0 ? void 0 : confirmModalData.onConfirm) {\n            confirmModalData.onConfirm();\n        }\n        closeConfirmModal();\n    };\n    const showCustomInput = (title, fields, onSubmit, onCancel)=>{\n        setInputModalData({\n            title,\n            fields,\n            onSubmit,\n            onCancel\n        });\n        setShowInputModal(true);\n    };\n    const closeInputModal = ()=>{\n        setShowInputModal(false);\n        if (inputModalData === null || inputModalData === void 0 ? void 0 : inputModalData.onCancel) {\n            inputModalData.onCancel();\n        }\n        setInputModalData(null);\n    };\n    const handleEditPlan = async (planId)=>{\n        try {\n            // Check if plan can be edited\n            const canEditResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/can-edit\"), {\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (canEditResponse.ok) {\n                const canEditResult = await canEditResponse.json();\n                if (canEditResult.canEdit) {\n                    // Find the plan and open edit modal\n                    const plan = plans.find((p)=>p._id === planId);\n                    if (plan) {\n                        setEditingPlan(plan);\n                        setShowPlanModal(true);\n                    } else {\n                        showCustomAlert(\"Error\", \"Plan not found\");\n                    }\n                } else {\n                    showCustomAlert(\"Cannot Edit Plan\", canEditResult.message);\n                }\n            } else {\n                showCustomAlert(\"Error\", \"Error checking plan editability\");\n            }\n        } catch (error) {\n            console.error(\"Error checking plan editability:\", error);\n            showCustomAlert(\"Error\", \"Error checking plan editability\");\n        }\n    };\n    const handleCopyPlan = async (planId)=>{\n        try {\n            const plan = plans.find((p)=>p._id === planId);\n            if (!plan) {\n                showCustomAlert(\"Error\", \"Plan not found\");\n                return;\n            }\n            // Show custom input modal for plan details\n            showCustomInput(\"Copy Plan\", [\n                {\n                    name: \"planName\",\n                    label: \"Plan Name\",\n                    placeholder: \"Enter name for the copied plan\",\n                    defaultValue: \"\".concat(plan.planName, \" (Copy)\"),\n                    required: true\n                },\n                {\n                    name: \"planCode\",\n                    label: \"Plan Code (Optional)\",\n                    placeholder: \"Enter plan code for the copied plan\",\n                    defaultValue: \"\".concat(plan.planCode || \"\", \"-COPY\"),\n                    required: false\n                }\n            ], async (values)=>{\n                const newPlanName = values.planName;\n                const newPlanCode = values.planCode;\n                try {\n                    // Call duplicate API\n                    const duplicateResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/duplicate\"), {\n                        method: \"POST\",\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)(),\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            planName: newPlanName,\n                            planCode: newPlanCode || undefined\n                        })\n                    });\n                    if (duplicateResponse.ok) {\n                        const result = await duplicateResponse.json();\n                        showCustomAlert(\"Success\", \"Plan copied successfully!\");\n                        loadPlans(); // Reload the plans list\n                    } else {\n                        const errorData = await duplicateResponse.json();\n                        showCustomAlert(\"Error\", \"Error copying plan: \".concat(errorData.error));\n                    }\n                } catch (error) {\n                    console.error(\"Error copying plan:\", error);\n                    showCustomAlert(\"Error\", \"Error copying plan\");\n                }\n            });\n        } catch (error) {\n            console.error(\"Error copying plan:\", error);\n            showCustomAlert(\"Error\", \"Error copying plan\");\n        }\n    };\n    const handleDeletePlan = async (planId)=>{\n        try {\n            // Check if plan can be deleted\n            const canDeleteResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/can-delete\"), {\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (canDeleteResponse.ok) {\n                const canDeleteResult = await canDeleteResponse.json();\n                if (canDeleteResult.canDelete) {\n                    showCustomConfirm(\"Delete Plan\", \"Are you sure you want to delete this plan? This action cannot be undone.\", async ()=>{\n                        try {\n                            const deleteResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId), {\n                                method: \"DELETE\",\n                                headers: {\n                                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                                }\n                            });\n                            if (deleteResponse.ok) {\n                                showCustomAlert(\"Success\", \"Plan deleted successfully!\");\n                                loadPlans(); // Reload the plans list\n                            } else {\n                                const errorData = await deleteResponse.json();\n                                showCustomAlert(\"Error\", \"Error deleting plan: \".concat(errorData.error || \"Unknown error\"));\n                            }\n                        } catch (deleteError) {\n                            console.error(\"Error deleting plan:\", deleteError);\n                            showCustomAlert(\"Error\", \"Error deleting plan. Please try again.\");\n                        }\n                    });\n                } else {\n                    // Show dependencies using correct endpoint\n                    const dependenciesResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/dependent-assignments\"), {\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                        }\n                    });\n                    if (dependenciesResponse.ok) {\n                        var _dependencies_dependentAssignments;\n                        const dependencies = await dependenciesResponse.json();\n                        const assignmentsList = ((_dependencies_dependentAssignments = dependencies.dependentAssignments) === null || _dependencies_dependentAssignments === void 0 ? void 0 : _dependencies_dependentAssignments.map((assignment)=>\"Assignment \".concat(assignment._id)).join(\", \")) || \"Unknown assignments\";\n                        showCustomAlert(\"Cannot Delete Plan\", \"\".concat(canDeleteResult.message, \"\\n\\nThis plan is referenced by \").concat(dependencies.count, \" assignment(s):\\n\").concat(assignmentsList));\n                    } else {\n                        showCustomAlert(\"Cannot Delete Plan\", canDeleteResult.message);\n                    }\n                }\n            } else {\n                showCustomAlert(\"Error\", \"Error checking plan dependencies\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting plan:\", error);\n            showCustomAlert(\"Error\", \"Error deleting plan\");\n        }\n    };\n    const handleActivatePlan = async (planId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/activate\"), {\n                method: \"POST\",\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (response.ok) {\n                showCustomAlert(\"Success\", \"Plan activated successfully!\");\n                loadPlans(); // Reload the plans list\n            } else {\n                const errorData = await response.json();\n                showCustomAlert(\"Error\", \"Error activating plan: \".concat(errorData.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error activating plan:\", error);\n            showCustomAlert(\"Error\", \"Error activating plan. Please try again.\");\n        }\n    };\n    const handleDeactivatePlan = async (planId)=>{\n        try {\n            showCustomConfirm(\"Convert to Draft\", \"Are you sure you want to convert this plan to draft? It will no longer be available for new assignments.\", async ()=>{\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/convert-to-draft\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                    }\n                });\n                if (response.ok) {\n                    showCustomAlert(\"Success\", \"Plan converted to draft successfully!\");\n                    loadPlans(); // Reload the plans list\n                } else {\n                    const errorData = await response.json();\n                    showCustomAlert(\"Error\", \"Error converting plan to draft: \".concat(errorData.error || \"Unknown error\"));\n                }\n            });\n        } catch (error) {\n            console.error(\"Error converting plan to draft:\", error);\n            showCustomAlert(\"Error\", \"Error converting plan to draft. Please try again.\");\n        }\n    };\n    // Helper function to get carrier name by ID\n    const getCarrierName = (carrierId)=>{\n        const carrier = carriers.find((c)=>c._id === carrierId);\n        return carrier ? carrier.carrierName : \"Unknown Carrier\";\n    };\n    // Handle plan modal submission\n    const handlePlanSubmit = (plan)=>{\n        setShowPlanModal(false);\n        setEditingPlan(null);\n        loadPlans(); // Reload plans list (this will also reload assignment counts)\n    };\n    // Handle plan modal cancel\n    const handlePlanCancel = ()=>{\n        setShowPlanModal(false);\n        setEditingPlan(null);\n    };\n    const headerActions = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"create-btn\",\n        onClick: ()=>{\n            setEditingPlan(null);\n            setShowPlanModal(true);\n        },\n        style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"8px\",\n            padding: \"10px 16px\",\n            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"8px\",\n            fontSize: \"14px\",\n            fontWeight: \"500\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                size: 16\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                lineNumber: 509,\n                columnNumber: 7\n            }, undefined),\n            \"Create Plan\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n        lineNumber: 492,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"plans-wrapper\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 517,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: \"white\",\n                        padding: \"24px 0\",\n                        borderBottom: \"1px solid #E5E7EB\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            maxWidth: \"95%\",\n                            margin: \"0 auto\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\",\n                            padding: \"0 2%\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n                                            borderRadius: \"8px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineClipboardList, {\n                                            style: {\n                                                width: \"18px\",\n                                                height: \"18px\",\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"600\",\n                                                    color: \"#111827\",\n                                                    margin: 0\n                                                },\n                                                children: \"Plan Management\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: \"#6B7280\",\n                                                    margin: 0\n                                                },\n                                                children: \"Manage and view all insurance plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"white\",\n                                            border: \"1px solid #D1D5DB\",\n                                            borderRadius: \"8px\",\n                                            color: \"#374151\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineQuestionMarkCircle, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Ask Questions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"white\",\n                                            border: \"1px solid #D1D5DB\",\n                                            borderRadius: \"8px\",\n                                            color: \"#374151\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineViewGrid, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPlanModal(true),\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"8px\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create New Plan\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 520,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"plans-page\",\n                    children: [\n                        stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"grid\",\n                                gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n                                gap: \"16px\",\n                                maxWidth: \"95%\",\n                                margin: \"24px auto\",\n                                padding: \"0 2%\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#EFF6FF\",\n                                        border: \"1px solid #DBEAFE\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#2563EB\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Total Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#1E40AF\"\n                                                    },\n                                                    children: stats.totalPlans\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#2563EB\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiHealthBookLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#F0FDF4\",\n                                        border: \"1px solid #BBF7D0\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#16A34A\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Active Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#15803D\"\n                                                    },\n                                                    children: stats.plansByStatus.Active || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#16A34A\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiCalendarLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#FEF3C7\",\n                                        border: \"1px solid #FDE68A\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#D97706\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Recent Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#B45309\"\n                                                    },\n                                                    children: stats.recentPlans.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#D97706\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiMoneyDollarCircleLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"white\",\n                                border: \"1px solid #E5E7EB\",\n                                borderRadius: \"12px\",\n                                padding: \"24px\",\n                                margin: \"0 auto 24px\",\n                                maxWidth: \"95%\",\n                                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"8px\",\n                                        marginBottom: \"16px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineSearch, {\n                                            style: {\n                                                width: \"16px\",\n                                                height: \"16px\",\n                                                color: \"#6B7280\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\",\n                                                fontWeight: \"500\",\n                                                color: \"#374151\"\n                                            },\n                                            children: \"Search & Filter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 11\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 9\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                                        gap: \"12px\",\n                                        marginBottom: \"16px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search by plan name, code, or carrier type...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            style: {\n                                                padding: \"10px 12px\",\n                                                border: \"1px solid #D1D5DB\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                outline: \"none\",\n                                                transition: \"border-color 0.2s\",\n                                                gridColumn: \"span 2\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterType,\n                                            onChange: (e)=>setFilterType(e.target.value),\n                                            style: {\n                                                padding: \"10px 12px\",\n                                                border: \"1px solid #D1D5DB\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                outline: \"none\",\n                                                background: \"white\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"active\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"inactive\",\n                                                    children: \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"draft\",\n                                                    children: \"Draft\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"template\",\n                                                    children: \"Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"archived\",\n                                                    children: \"Archived\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 13\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: carrierFilter,\n                                            onChange: (e)=>setCarrierFilter(e.target.value),\n                                            style: {\n                                                padding: \"10px 12px\",\n                                                border: \"1px solid #D1D5DB\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                outline: \"none\",\n                                                background: \"white\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Carriers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                carriers.map((carrier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: carrier._id,\n                                                        children: carrier.carrierName\n                                                    }, carrier._id, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 15\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleClearFilters,\n                                            style: {\n                                                padding: \"10px 16px\",\n                                                background: \"white\",\n                                                border: \"1px solid #D1D5DB\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                color: \"#374151\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: \"Clear Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 11\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 9\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        color: \"#6B7280\"\n                                    },\n                                    children: [\n                                        \"Showing \",\n                                        filteredPlans.length,\n                                        \" of \",\n                                        plans.length,\n                                        \" plans\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 9\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 7\n                        }, undefined),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-state\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-spinner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 836,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading plans...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 835,\n                            columnNumber: 9\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"error-state\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Error: \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadPlans,\n                                    className: \"retry-btn\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 845,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 9\n                        }, undefined),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"plans-table-container\",\n                            children: filteredPlans.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"empty-state\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiShieldCheckLine, {\n                                        size: 48\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"No Plans Found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: plans.length === 0 ? \"You haven't created any plans yet. Create your first plan to get started.\" : \"No plans match your search criteria. Try adjusting your filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"create-first-plan-btn\",\n                                        onClick: ()=>router.push(\"/ai-enroller/create-plan\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create Your First Plan\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 855,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"table-header\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"Plans List\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"table-wrapper\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"plans-table\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Plan Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Plan Code\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 883,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Coverage Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Groups\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 887,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: paginatedPlans.map((plan)=>{\n                                                        var _this, _plan_coverageSubTypes, _plan_coverageSubTypes1;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"plan-name-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"plan-name\",\n                                                                        children: plan.planName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 894,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"plan-code-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"plan-code-badge\",\n                                                                        children: plan.planCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"carrier-type-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"carrier-type-badge \".concat((_this = ((_plan_coverageSubTypes = plan.coverageSubTypes) === null || _plan_coverageSubTypes === void 0 ? void 0 : _plan_coverageSubTypes[0]) || plan.coverageType) === null || _this === void 0 ? void 0 : _this.toLowerCase().replace(\" \", \"-\")),\n                                                                        children: ((_plan_coverageSubTypes1 = plan.coverageSubTypes) === null || _plan_coverageSubTypes1 === void 0 ? void 0 : _plan_coverageSubTypes1[0]) || plan.coverageType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 900,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"status-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"status-badge \".concat((plan.status || \"unknown\").toLowerCase()),\n                                                                        children: plan.status || \"Unknown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 905,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"groups-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"groups-count\",\n                                                                        children: planAssignmentCounts[plan._id] !== undefined ? planAssignmentCounts[plan._id] : \"...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 910,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"actions-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"action-buttons\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn edit\",\n                                                                                onClick: ()=>handleEditPlan(plan._id),\n                                                                                title: \"Edit Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePencil, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 921,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn copy\",\n                                                                                onClick: ()=>handleCopyPlan(plan._id),\n                                                                                title: \"Copy Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineDuplicate, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 928,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 923,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            plan.status === \"Active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn deactivate\",\n                                                                                onClick: ()=>handleDeactivatePlan(plan._id),\n                                                                                title: \"Convert to Draft\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePause, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 936,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 931,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn activate\",\n                                                                                onClick: ()=>handleActivatePlan(plan._id),\n                                                                                title: \"Activate Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlay, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 944,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 939,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn delete\",\n                                                                                onClick: ()=>handleDeletePlan(plan._id),\n                                                                                title: \"Delete Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineTrash, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 952,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 947,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 915,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, plan._id, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pagination-container\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pagination-info\",\n                                                children: [\n                                                    \"Showing \",\n                                                    startIndex + 1,\n                                                    \"-\",\n                                                    Math.min(endIndex, filteredPlans.length),\n                                                    \" of \",\n                                                    filteredPlans.length,\n                                                    \" plans\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 965,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pagination-controls\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"pagination-btn\",\n                                                        onClick: ()=>handlePageChange(currentPage - 1),\n                                                        disabled: currentPage === 1,\n                                                        children: \"Previous\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 969,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    Array.from({\n                                                        length: totalPages\n                                                    }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"pagination-btn \".concat(page === currentPage ? \"active\" : \"\"),\n                                                            onClick: ()=>handlePageChange(page),\n                                                            children: page\n                                                        }, page, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 23\n                                                        }, undefined)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"pagination-btn\",\n                                                        onClick: ()=>handlePageChange(currentPage + 1),\n                                                        disabled: currentPage === totalPages,\n                                                        children: \"Next\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 853,\n                            columnNumber: 9\n                        }, undefined),\n                        showPlanModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: handlePlanCancel,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content plan-modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: editingPlan ? \"Edit Plan\" : \"Create New Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1005,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: handlePlanCancel,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1006,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1004,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_manage_groups_company_companyId_plans_components_CreatePlanForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            initialData: editingPlan,\n                                            onSubmit: handlePlanSubmit,\n                                            onCancel: handlePlanCancel,\n                                            isModal: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1003,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1002,\n                            columnNumber: 9\n                        }, undefined),\n                        showAlertModal && alertModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeAlertModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: alertModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeAlertModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1028,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1026,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                whiteSpace: \"pre-line\"\n                                            },\n                                            children: alertModalData.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"modal-btn primary\",\n                                            onClick: closeAlertModal,\n                                            children: \"OK\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1025,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1024,\n                            columnNumber: 9\n                        }, undefined),\n                        showConfirmModal && confirmModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeConfirmModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: confirmModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1049,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeConfirmModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1051,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1050,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                whiteSpace: \"pre-line\"\n                                            },\n                                            children: confirmModalData.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1055,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1054,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-btn secondary\",\n                                                onClick: closeConfirmModal,\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1058,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-btn primary\",\n                                                onClick: confirmAction,\n                                                children: \"Confirm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1057,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1046,\n                            columnNumber: 9\n                        }, undefined),\n                        showInputModal && inputModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeInputModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: inputModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1074,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeInputModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineClipboardList_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1076,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: (e)=>{\n                                            e.preventDefault();\n                                            const formData = new FormData(e.target);\n                                            const values = {};\n                                            inputModalData.fields.forEach((field)=>{\n                                                values[field.name] = formData.get(field.name) || \"\";\n                                            });\n                                            inputModalData.onSubmit(values);\n                                            closeInputModal();\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-body\",\n                                                children: inputModalData.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-group\",\n                                                        style: {\n                                                            marginBottom: \"1rem\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: field.name,\n                                                                style: {\n                                                                    display: \"block\",\n                                                                    marginBottom: \"0.5rem\",\n                                                                    fontSize: \"14px\",\n                                                                    lineHeight: \"21px\",\n                                                                    fontWeight: \"500\",\n                                                                    color: \"#374151\"\n                                                                },\n                                                                children: [\n                                                                    field.label,\n                                                                    field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: \"#dc2626\"\n                                                                        },\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 42\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1092,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: field.name,\n                                                                name: field.name,\n                                                                placeholder: field.placeholder,\n                                                                defaultValue: field.defaultValue,\n                                                                required: field.required,\n                                                                style: {\n                                                                    width: \"100%\",\n                                                                    padding: \"0.75rem\",\n                                                                    border: \"1px solid #d1d5db\",\n                                                                    borderRadius: \"0.5rem\",\n                                                                    fontSize: \"14px\",\n                                                                    lineHeight: \"21px\",\n                                                                    fontFamily: \"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1103,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, field.name, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1089,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-footer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"modal-btn secondary\",\n                                                        onClick: closeInputModal,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1124,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        className: \"modal-btn primary\",\n                                                        children: \"Submit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1123,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1079,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1072,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1071,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 619,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n            lineNumber: 516,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"rj+iLjGTAPCU1ONVJi170qf1XYk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = PlansPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/plans/page.tsx\n"));

/***/ })

});