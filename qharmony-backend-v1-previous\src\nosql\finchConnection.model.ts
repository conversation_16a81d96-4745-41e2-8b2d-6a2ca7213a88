import mongoose, { Document, Model } from 'mongoose';

const { Schema } = mongoose;

export interface FinchConnectionDataItemInterface {
  slackTeamId: string;
  finchAccessToken: string;
  finchCompanyId: string;
}

export interface FinchConnectionDataInterface {
  data: FinchConnectionDataItemInterface;
}

interface FinchConnectionDocument extends Document {
  data: FinchConnectionDataInterface[];
}

class FinchConnectionModelClass {
  private static finchConnectionModel: Model<FinchConnectionDocument>;

  public static initializeModel() {
    const schema = new Schema({
      slackTeamId: String,
      finchAccessToken: String,
      finchCompanyId: String,
    });

    this.finchConnectionModel = mongoose.model<FinchConnectionDocument>(
      'FinchConnection',
      schema
    );
  }

  public static async addData(
    data: FinchConnectionDataItemInterface
  ): Promise<void> {
    try {
      // Extract the data. If slackTeamId exists, update the data. Otherwise, insert the data.
      const { slackTeamId } = data;
      const existingData = await this.finchConnectionModel.findOne({
        slackTeamId,
      });
      if (existingData) {
        await this.finchConnectionModel.updateOne({ slackTeamId }, data);
      } else {
        await this.finchConnectionModel.create(data);
      }
    } catch (error) {
      console.error(error);
    }
  }

  public static async deleteDataBySlackTeamId(
    slackTeamId: string
  ): Promise<void> {
    try {
      await this.finchConnectionModel.deleteOne({ slackTeamId });
    } catch (error) {
      console.error(error);
    }
  }

  public static async getAllData(): Promise<
    FinchConnectionDataItemInterface[] | null
  > {
    try {
      const result =
        (await this.finchConnectionModel.find()) as unknown as FinchConnectionDataItemInterface[];
      return result;
    } catch (error) {
      console.error(error);
    }
    return null;
  }

  public static async getDataBySlackTeamId(
    slackTeamId: string
  ): Promise<FinchConnectionDataItemInterface | null> {
    try {
      const result = (await this.finchConnectionModel.findOne({
        slackTeamId,
      })) as FinchConnectionDataItemInterface;
      return result;
    } catch (error) {
      console.error(error);
    }
    return null;
  }
}

FinchConnectionModelClass.initializeModel();

export default FinchConnectionModelClass;
