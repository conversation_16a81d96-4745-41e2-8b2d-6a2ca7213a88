'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  HiOutlineArrowLeft,
  HiOutlineUsers,
  HiOutlinePlus,
  HiOutlineHeart,
  HiOutlineClipboard,
  HiOutlineEye
} from 'react-icons/hi';
// import { getCompanyTeamMembers } from '@/middleware/company_middleware';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import '../company-detail.css';

interface Employee {
  _id: string;
  name: string;
  email: string;
  role: string;
  isActivated: boolean;
}

interface Plan {
  id: string;
  name: string;
  type: 'medical' | 'dental' | 'vision';
  cost: string;
}

const CompanyDetailPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const dispatch = useDispatch();
  const companyId = params.companyId as string;

  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'employees'>('overview');

  const managedCompanies = useSelector((state: RootState) => state.user.managedCompanies);

  const company = managedCompanies?.find(c => c._id === companyId);

  // Mock current plans data
  const currentPlans: Plan[] = [
    { id: '1', name: 'Blue Cross Blue Shield', type: 'medical', cost: '$450/mo' },
    { id: '2', name: 'Delta Dental', type: 'dental', cost: '$85/mo' },
    { id: '3', name: 'VSP', type: 'vision', cost: '$25/mo' }
  ];

  // Mock statistics
  const stats = {
    totalEmployees: company?.companySize || 127,
    avgAge: 35,
    highRisk: '40%',
    withDependents: '60%'
  };

  useEffect(() => {
    if (company) {
      fetchEmployees();
    }
  }, [company]);

  useEffect(() => {
    // Mock employees data for demo
    const mockEmployees = [
      { _id: '1', name: 'John Doe', email: '<EMAIL>', role: 'Manager', isActivated: true },
      { _id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'Developer', isActivated: true },
      { _id: '3', name: 'Bob Johnson', email: '<EMAIL>', role: 'Designer', isActivated: false }
    ];
    setEmployees(mockEmployees);
    setLoading(false);
  }, []);

  const fetchEmployees = async () => {
    try {
      // await getCompanyTeamMembers(dispatch);
      // Mock employees for demo
      setLoading(false);
    } catch (error) {
      console.error('Error fetching employees:', error);
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/ai-enroller/manage-groups');
  };

  const handleAddPlans = () => {
    router.push(`/ai-enroller/manage-groups/${companyId}/add-plans`);
  };

  const handleManagePlans = () => {
    router.push(`/ai-enroller/manage-groups/${companyId}/manage-plans`);
  };

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'medical':
        return <HiOutlineHeart className="plan-icon medical" />;
      case 'dental':
        return <HiOutlineClipboard className="plan-icon dental" />;
      case 'vision':
        return <HiOutlineEye className="plan-icon vision" />;
      default:
        return <HiOutlineClipboard className="plan-icon" />;
    }
  };

  if (!company) {
    return (
      <div className="company-detail-page">
        <div className="loading-container">
          <p>Company not found</p>
          <button onClick={handleBack} className="back-button">
            <HiOutlineArrowLeft size={20} />
            Back to Companies
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="company-detail-page">
      {/* Header */}
      <div className="page-header">
        <button onClick={handleBack} className="back-button">
          <HiOutlineArrowLeft size={20} />
          Back
        </button>
        <button className="edit-group-btn">
          Edit Group
        </button>
      </div>

      {/* Company Overview */}
      <div className="company-overview">
        <h1>Company Overview</h1>
        <div className="overview-grid">
          <div className="overview-item">
            <span className="label">Industry</span>
            <span className="value">{company.industry}</span>
          </div>
          <div className="overview-item">
            <span className="label">Employees</span>
            <span className="value">{stats.totalEmployees}</span>
          </div>
          <div className="overview-item">
            <span className="label">Location</span>
            <span className="value">{company.location || 'San Francisco, CA'}</span>
          </div>
          <div className="overview-item">
            <span className="label">Revenue</span>
            <span className="value">$50M - $100M</span>
          </div>
        </div>
      </div>

      {/* Current Plans */}
      <div className="current-plans">
        <h2>Current Plans</h2>
        <div className="plans-grid">
          {currentPlans.map((plan) => (
            <div key={plan.id} className="plan-card">
              {getPlanIcon(plan.type)}
              <div className="plan-info">
                <h3>{plan.name}</h3>
                <span className="plan-cost">{plan.cost}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Statistics */}
      <div className="statistics-grid">
        <div className="stat-card">
          <div className="stat-number">{stats.totalEmployees}</div>
          <div className="stat-label">Total Employees</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.avgAge}</div>
          <div className="stat-label">Avg Age</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.highRisk}</div>
          <div className="stat-label">High Risk</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.withDependents}</div>
          <div className="stat-label">With Dependents</div>
        </div>
      </div>

      {/* Feature Cards */}
      <div className="feature-cards">
        <div className="feature-card">
          <div className="feature-icon">
            <HiOutlinePlus size={24} />
          </div>
          <div className="feature-content">
            <h3>Enrichment</h3>
            <p>Coming Soon</p>
          </div>
        </div>
        <div className="feature-card">
          <div className="feature-icon">
            <HiOutlinePlus size={24} />
          </div>
          <div className="feature-content">
            <h3>Optimization</h3>
            <p>Coming Soon</p>
          </div>
        </div>
        <div className="feature-card">
          <div className="feature-icon">
            <HiOutlinePlus size={24} />
          </div>
          <div className="feature-content">
            <h3>Insights</h3>
            <p>Coming Soon</p>
          </div>
        </div>
        <div className="feature-card clickable" onClick={() => setActiveTab('employees')}>
          <div className="feature-icon">
            <HiOutlineUsers size={24} />
          </div>
          <div className="feature-content">
            <h3>Employees</h3>
            <p>View employee list</p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="action-buttons-section">
        <button className="manage-plans-btn" onClick={handleManagePlans}>
          <HiOutlineEye size={20} />
          Manage Plans
        </button>
        <button className="add-plans-btn" onClick={handleAddPlans}>
          <HiOutlinePlus size={20} />
          Add Plans
        </button>
      </div>

      {/* Employees Modal/Section */}
      {activeTab === 'employees' && (
        <div className="employees-section">
          <div className="employees-header">
            <h2>Employees ({employees.length})</h2>
            <button
              className="close-employees"
              onClick={() => setActiveTab('overview')}
            >
              ×
            </button>
          </div>
          <div className="employees-list">
            {loading ? (
              <div className="loading-spinner"></div>
            ) : employees.length === 0 ? (
              <p>No employees found</p>
            ) : (
              employees.map((employee) => (
                <div key={employee._id} className="employee-card">
                  <div className="employee-info">
                    <h4>{employee.name}</h4>
                    <p>{employee.email}</p>
                    <span className="employee-role">{employee.role}</span>
                  </div>
                  <div className={`employee-status ${employee.isActivated ? 'active' : 'inactive'}`}>
                    {employee.isActivated ? 'Active' : 'Inactive'}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyDetailPage;
