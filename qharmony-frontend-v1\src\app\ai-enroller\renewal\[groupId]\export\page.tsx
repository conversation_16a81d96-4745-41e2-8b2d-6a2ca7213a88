'use client';

import React, { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { 
  HiOutlineArrowLeft,
  HiOutlineDownload,
  HiOutlineCheckCircle,
  HiOutlineDocumentText,
  HiOutlineCode,
  HiOutlineClipboardCopy,
  HiOutlineMail
} from 'react-icons/hi';
import '../../renewal.css';
import '../plan-detail.css';
import './export.css';

interface ExportOption {
  id: string;
  title: string;
  description: string;
  filename: string;
  bestFor: string;
  icon: React.ReactNode;
}

const ExportPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(7);

  const groupName = 'Green Valley Manufacturing';

  const steps = [
    { number: 1, title: 'Review Current Plans', subtitle: 'View existing benefit plans', active: false, completed: true },
    { number: 2, title: 'Renewal Options', subtitle: 'Choose renewal type', active: false, completed: true },
    { number: 3, title: 'Plan Configuration', subtitle: 'Set dates and modifications', active: false, completed: true },
    { number: 4, title: 'Document Upload', subtitle: 'Upload plan documents', active: false, completed: true },
    { number: 5, title: 'Validation', subtitle: 'Review and validate setup', active: false, completed: true },
    { number: 6, title: 'Finalize', subtitle: 'Complete renewal process', active: false, completed: true },
    { number: 7, title: 'Export', subtitle: 'Download and share data', active: currentStep === 7 }
  ];

  const exportOptions: ExportOption[] = [
    {
      id: 'csv',
      title: 'CSV Export',
      description: 'Spreadsheet format with plan details, rates, and employee data',
      filename: 'Green_Valley_Manufacturing_renewal_2025.csv',
      bestFor: 'Import into payroll systems, Excel analysis',
      icon: <HiOutlineDocumentText size={24} />
    },
    {
      id: 'json',
      title: 'JSON Export',
      description: 'Structured data format for API integration and system sync',
      filename: 'Green_Valley_Manufacturing_renewal_2025.json',
      bestFor: 'API integration, automated system sync',
      icon: <HiOutlineCode size={24} />
    },
    {
      id: 'summary',
      title: 'Summary Report',
      description: 'PDF report with renewal overview and plan comparisons',
      filename: 'Green_Valley_Manufacturing_renewal_summary.pdf',
      bestFor: 'Client presentations, internal reporting',
      icon: <HiOutlineDocumentText size={24} />
    }
  ];

  const renewalSummary = {
    group: 'Green Valley Manufacturing',
    effectiveDate: '1/1/2025',
    plansRenewed: 2,
    employeesAffected: 89,
    documentsUploaded: 0,
    completed: '26/5/2025'
  };

  const handleDownload = (exportType: string) => {
    console.log(`Downloading ${exportType} export...`);
    // Simulate download
  };

  const handleCopyToClipboard = () => {
    const summaryText = `Renewal Process Complete\n\nGroup: ${renewalSummary.group}\nEffective Date: ${renewalSummary.effectiveDate}\nPlans Renewed: ${renewalSummary.plansRenewed}\nEmployees Affected: ${renewalSummary.employeesAffected}\nCompleted: ${renewalSummary.completed}`;
    navigator.clipboard.writeText(summaryText);
    alert('Summary copied to clipboard!');
  };

  const handleSendEmail = () => {
    console.log('Sending email notification...');
    // Simulate email sending
  };

  const handleReturnToDashboard = () => {
    router.push('/ai-enroller/renewal');
  };

  return (
    <div className="plan-renewal-detail">
      {/* Header */}
      <div className="detail-header">
        <button 
          className="back-btn"
          onClick={() => router.push('/ai-enroller/renewal')}
        >
          <HiOutlineArrowLeft size={20} />
          Back to Dashboard
        </button>
        
        <div className="header-info">
          <h1>Plan Renewal</h1>
          <h2>{groupName}</h2>
          <div className="step-indicator">Step {currentStep} of 7</div>
        </div>

        <div className="completion-status">
          100% Complete
        </div>
      </div>

      {/* Progress Steps */}
      <div className="renewal-steps">
        {steps.map((step, index) => (
          <div key={step.number} className={`renewal-step ${step.active ? 'active' : ''} ${step.completed ? 'completed' : ''}`}>
            <div className="step-number">
              {step.completed ? '✓' : step.number}
            </div>
            <div className="step-content">
              <div className="step-title">{step.title}</div>
              <div className="step-subtitle">{step.subtitle}</div>
            </div>
            {index < steps.length - 1 && <div className="step-connector"></div>}
          </div>
        ))}
      </div>

      {/* Export Section */}
      <div className="export-section">
        <div className="export-header">
          <div className="export-title">
            <HiOutlineDownload size={20} />
            <h3>Export & Share Renewal Data</h3>
          </div>
          <p>Download renewal data in various formats or copy summary information for sharing with your team.</p>
        </div>

        <div className="export-content">
          {/* Success Message */}
          <div className="success-message">
            <HiOutlineCheckCircle size={24} />
            <div>
              <h4>Renewal Successfully Completed!</h4>
              <p>All plans for {groupName} have been renewed and are now active. Use the options below to export data or share the renewal summary.</p>
            </div>
          </div>

          {/* Export Options */}
          <div className="export-options">
            {exportOptions.map((option) => (
              <div key={option.id} className="export-card">
                <div className="export-card-header">
                  <div className="export-icon">
                    {option.icon}
                  </div>
                  <div className="export-info">
                    <h4>{option.title}</h4>
                    <p>{option.description}</p>
                  </div>
                </div>
                
                <div className="export-details">
                  <div className="filename">
                    <strong>Filename:</strong> {option.filename}
                  </div>
                  <div className="best-for">
                    <strong>Best for:</strong> {option.bestFor}
                  </div>
                </div>
                
                <button 
                  className="download-btn"
                  onClick={() => handleDownload(option.id)}
                >
                  <HiOutlineDownload size={16} />
                  Download {option.title}
                </button>
              </div>
            ))}
          </div>

          {/* Quick Share Options */}
          <div className="quick-share">
            <h4>Quick Share Options</h4>
            <p>Quickly share renewal information with your team via common communication tools.</p>
            
            <div className="share-buttons">
              <button className="share-btn" onClick={handleCopyToClipboard}>
                <HiOutlineClipboardCopy size={16} />
                Copy to Clipboard
                <span className="share-desc">Summary text for Slack, Teams, or email</span>
              </button>
              
              <button className="share-btn" onClick={handleSendEmail}>
                <HiOutlineMail size={16} />
                Send Email Notification
                <span className="share-desc">Quick email to stakeholders</span>
              </button>
            </div>
          </div>

          {/* Renewal Process Summary */}
          <div className="process-summary">
            <h4>Renewal Process Complete</h4>
            <div className="summary-grid">
              <div className="summary-item">
                <strong>Group:</strong> {renewalSummary.group}
              </div>
              <div className="summary-item">
                <strong>Effective Date:</strong> {renewalSummary.effectiveDate}
              </div>
              <div className="summary-item">
                <strong>Plans Renewed:</strong> {renewalSummary.plansRenewed}
              </div>
              <div className="summary-item">
                <strong>Employees Affected:</strong> {renewalSummary.employeesAffected}
              </div>
              <div className="summary-item">
                <strong>Documents Uploaded:</strong> {renewalSummary.documentsUploaded}
              </div>
              <div className="summary-item">
                <strong>Completed:</strong> {renewalSummary.completed}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="navigation-section">
          <button 
            className="nav-btn secondary"
            onClick={() => router.back()}
          >
            <HiOutlineArrowLeft size={16} />
            Previous
          </button>
          
          <button 
            className="nav-btn primary enabled"
            onClick={handleReturnToDashboard}
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportPage;
