/* Plan Configuration Styles */
.plan-configuration-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
  overflow: hidden;
}

.config-header {
  padding: 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.config-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.config-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.config-header p {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Configuration Content */
.config-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.config-card {
  background: #fafbfc;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #1e293b;
}

.card-header h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

/* Date Inputs */
.date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.date-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-field label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.date-input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
}

.date-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.date-help {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Summary Card */
.summary-card {
  background: #f0f9ff;
  border-color: #bfdbfe;
}

.summary-content p {
  font-size: 0.875rem;
  color: #1e40af;
  margin: 0;
  line-height: 1.6;
}

/* Modifications */
.modifications-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modification-notice {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 0.5rem;
  color: #92400e;
}

.modification-notice p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Navigation */
.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-top: 1px solid #f1f5f9;
  background: #f8fafc;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.nav-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.nav-btn.secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.nav-btn.primary {
  background: #7c3aed; /* Purple matching gradient */
  color: white;
}

.nav-btn.primary.enabled:hover {
  background: #6d28d9; /* Darker purple on hover */
  transform: translateY(-1px);
}

/* Step Progress Updates */
.renewal-step.completed .step-number {
  background: #7c3aed; /* Purple matching gradient */
  color: white;
}

.renewal-step.completed .step-title {
  color: #7c3aed; /* Purple matching gradient */
}

/* Responsive Design */
@media (max-width: 768px) {
  .config-content {
    padding: 1rem;
  }
  
  .date-inputs {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .navigation-section {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-btn {
    width: 100%;
    justify-content: center;
  }
  
  .modification-notice {
    flex-direction: column;
    gap: 0.5rem;
  }
}
