'use client';

import React, { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  HiOutlineArrowLeft,
  HiOutlineSparkles,
  HiOutlineDocumentText,
  HiOutlineUsers,
  HiOutlineCalendar,
  HiOutlineUpload,
  HiOutlineCheckCircle
} from 'react-icons/hi';
import '../../renewal.css';
import '../plan-detail.css';
import './finalize.css';

const FinalizePage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(6);
  const [isProcessing, setIsProcessing] = useState(false);

  const groupName = 'Green Valley Manufacturing';

  const steps = [
    { number: 1, title: 'Review Current Plans', subtitle: 'View existing benefit plans', active: false, completed: true },
    { number: 2, title: 'Renewal Options', subtitle: 'Choose renewal type', active: false, completed: true },
    { number: 3, title: 'Plan Configuration', subtitle: 'Set dates and modifications', active: false, completed: true },
    { number: 4, title: 'Document Upload', subtitle: 'Upload plan documents', active: false, completed: true },
    { number: 5, title: 'Validation', subtitle: 'Review and validate setup', active: false, completed: true },
    { number: 6, title: 'Finalize', subtitle: 'Complete renewal process', active: currentStep === 6 },
    { number: 7, title: 'Export', subtitle: 'Download and share data', active: false }
  ];

  const renewalSummary = {
    plansRenewed: 2,
    employeesAffected: 89,
    effectiveDate: '1/1/2025',
    documentsUploaded: 0
  };

  const finalizeSteps = [
    {
      number: 1,
      title: 'Archive Current Plans',
      description: 'Existing plans will be marked as "Expired" and archived',
      completed: false
    },
    {
      number: 2,
      title: 'Activate Renewed Plans',
      description: 'New plans become active on the effective date',
      completed: false
    },
    {
      number: 3,
      title: 'Notify HR Team',
      description: 'HR administrators will receive renewal confirmation',
      completed: false
    },
    {
      number: 4,
      title: 'Update Employee Portal',
      description: 'Employees will see updated plan information',
      completed: false
    }
  ];

  const handleFinalize = async () => {
    setIsProcessing(true);

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Navigate to export page
    router.push(`/ai-enroller/renewal/${params.groupId}/export`);
  };

  const handlePrevious = () => {
    router.back();
  };

  return (
    <div className="plan-renewal-detail">
      {/* Header */}
      <div className="detail-header">
        <button
          className="back-btn"
          onClick={() => router.push('/ai-enroller/renewal')}
        >
          <HiOutlineArrowLeft size={20} />
          Back to Dashboard
        </button>

        <div className="header-info">
          <h1>Plan Renewal</h1>
          <h2>{groupName}</h2>
          <div className="step-indicator">Step {currentStep} of 7</div>
        </div>

        <div className="completion-status">
          86% Complete
        </div>
      </div>

      {/* Progress Steps */}
      <div className="renewal-steps">
        {steps.map((step, index) => (
          <div key={step.number} className={`renewal-step ${step.active ? 'active' : ''} ${step.completed ? 'completed' : ''}`}>
            <div className="step-number">
              {step.completed ? '✓' : step.number}
            </div>
            <div className="step-content">
              <div className="step-title">{step.title}</div>
              <div className="step-subtitle">{step.subtitle}</div>
            </div>
            {index < steps.length - 1 && <div className="step-connector"></div>}
          </div>
        ))}
      </div>

      {/* Finalize Section */}
      <div className="finalize-section">
        <div className="finalize-header">
          <div className="finalize-title">
            <HiOutlineSparkles size={20} />
            <h3>Finalize Plan Renewal</h3>
          </div>
          <p>Complete the renewal process for {groupName}. This will activate the new plans and archive the old ones.</p>
        </div>

        <div className="finalize-content">
          {/* Renewal Summary */}
          <div className="summary-cards">
            <div className="summary-card">
              <div className="card-icon">
                <HiOutlineDocumentText size={24} />
              </div>
              <div className="card-content">
                <div className="card-number">{renewalSummary.plansRenewed}</div>
                <div className="card-label">Plans Renewed</div>
              </div>
            </div>

            <div className="summary-card">
              <div className="card-icon">
                <HiOutlineUsers size={24} />
              </div>
              <div className="card-content">
                <div className="card-number">{renewalSummary.employeesAffected}</div>
                <div className="card-label">Employees Affected</div>
              </div>
            </div>

            <div className="summary-card">
              <div className="card-icon">
                <HiOutlineCalendar size={24} />
              </div>
              <div className="card-content">
                <div className="card-number">{renewalSummary.effectiveDate}</div>
                <div className="card-label">Effective Date</div>
              </div>
            </div>

            <div className="summary-card">
              <div className="card-icon">
                <HiOutlineUpload size={24} />
              </div>
              <div className="card-content">
                <div className="card-number">{renewalSummary.documentsUploaded}</div>
                <div className="card-label">Documents Uploaded</div>
              </div>
            </div>
          </div>

          {/* What Happens When You Finalize */}
          <div className="finalize-steps">
            <h4>What happens when you finalize:</h4>
            <div className="steps-list">
              {finalizeSteps.map((step) => (
                <div key={step.number} className="finalize-step">
                  <div className="step-indicator">
                    <div className="step-circle">{step.number}</div>
                  </div>
                  <div className="step-content">
                    <h5>{step.title}</h5>
                    <p>{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Ready to Finalize Notice */}
          <div className="ready-notice">
            <HiOutlineCheckCircle size={20} />
            <div>
              <h4>Ready to Finalize</h4>
              <p>All validation checks have passed. Once you click &quot;Finalize Renewal&quot;, the changes will be applied and cannot be undone without creating a new renewal.</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="navigation-section">
          <button
            className="nav-btn secondary"
            onClick={handlePrevious}
            disabled={isProcessing}
          >
            <HiOutlineArrowLeft size={16} />
            Previous
          </button>

          <button
            className={`finalize-btn ${isProcessing ? 'processing' : 'enabled'}`}
            onClick={handleFinalize}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <div className="spinner"></div>
                Processing...
              </>
            ) : (
              <>
                <HiOutlineSparkles size={16} />
                Finalize Renewal
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FinalizePage;
