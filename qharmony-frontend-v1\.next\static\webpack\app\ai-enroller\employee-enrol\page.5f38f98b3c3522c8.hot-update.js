"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/employee-enrol/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/manage-groups/services/planAssignmentApi.ts":
/*!*************************************************************************!*\
  !*** ./src/app/ai-enroller/manage-groups/services/planAssignmentApi.ts ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activatePlanAssignment: function() { return /* binding */ activatePlanAssignment; },\n/* harmony export */   canEditPlanAssignment: function() { return /* binding */ canEditPlanAssignment; },\n/* harmony export */   clonePlanAssignment: function() { return /* binding */ clonePlanAssignment; },\n/* harmony export */   createPlanAssignment: function() { return /* binding */ createPlanAssignment; },\n/* harmony export */   deactivatePlanAssignment: function() { return /* binding */ deactivatePlanAssignment; },\n/* harmony export */   getActivePlanAssignmentsByCompany: function() { return /* binding */ getActivePlanAssignmentsByCompany; },\n/* harmony export */   getAssignablePlans: function() { return /* binding */ getAssignablePlans; },\n/* harmony export */   getBrokerPlanAssignmentsCount: function() { return /* binding */ getBrokerPlanAssignmentsCount; },\n/* harmony export */   getPlanAssignmentsByCompany: function() { return /* binding */ getPlanAssignmentsByCompany; },\n/* harmony export */   updatePlanAssignment: function() { return /* binding */ updatePlanAssignment; }\n/* harmony export */ });\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../utils/env */ \"(app-pages-browser)/./src/utils/env.ts\");\n\n// API configuration\nconst API_BASE_URL = (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getApiBaseUrl)();\nconst getHeaders = ()=>({\n        \"Content-Type\": \"application/json\",\n        \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getUserId)()\n    });\n// Get plan assignments for a company with optimized API\nconst getPlanAssignmentsByCompany = async function(companyId) {\n    let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, pagination = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getUserId)();\n        console.log(\"\\uD83D\\uDD0D Plan Assignment API Debug:\", {\n            companyId,\n            userId,\n            filters,\n            pagination,\n            apiBaseUrl: API_BASE_URL\n        });\n        const queryParams = new URLSearchParams();\n        // Add filters to query params\n        if (filters.status) queryParams.append(\"status\", filters.status);\n        if (filters.planId) queryParams.append(\"planId\", filters.planId);\n        if (filters.assignmentYear) queryParams.append(\"assignmentYear\", filters.assignmentYear.toString());\n        if (filters.referenceDate) queryParams.append(\"referenceDate\", filters.referenceDate);\n        if (filters.includePlanData !== undefined) queryParams.append(\"includePlanData\", filters.includePlanData.toString());\n        if (filters.enrollmentPeriodOnly) queryParams.append(\"enrollmentPeriodOnly\", \"true\");\n        if (filters.effectiveOnly) queryParams.append(\"effectiveOnly\", \"true\");\n        if (filters.futureOnly) queryParams.append(\"futureOnly\", \"true\");\n        if (filters.includeInactive) queryParams.append(\"includeInactive\", \"true\");\n        if (filters.includeExpired) queryParams.append(\"includeExpired\", \"true\");\n        if (filters.brokerId) queryParams.append(\"brokerId\", filters.brokerId);\n        // Add pagination if provided\n        if (pagination) {\n            queryParams.append(\"page\", pagination.page.toString());\n            queryParams.append(\"limit\", pagination.limit.toString());\n        }\n        const url = \"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/company/\").concat(companyId).concat(queryParams.toString() ? \"?\".concat(queryParams.toString()) : \"\");\n        console.log(\"\\uD83D\\uDCE1 Fetching plan assignments from optimized API:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        console.log(\"Plan assignments API response status:\", response.status);\n        if (!response.ok) {\n            let errorMessage = \"HTTP error! status: \".concat(response.status);\n            if (response.status === 403) {\n                const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getUserId)();\n                console.error(\"\\uD83D\\uDEAB 403 Forbidden Error Details:\", {\n                    url,\n                    userId,\n                    companyId,\n                    userIdSource: localStorage.getItem(\"userid1\") ? \"userid1\" : localStorage.getItem(\"userId\") ? \"userId\" : \"none\"\n                });\n                // For 403 errors, return a special response that indicates no access but allows page to load\n                console.log(\"\\uD83D\\uDD27 Broker has no existing plan assignments for this company - returning empty result to allow plan creation\");\n                return {\n                    success: true,\n                    data: {\n                        assignments: [],\n                        count: 0,\n                        message: \"No existing plan assignments. You can create new plan assignments for this company.\",\n                        canCreateAssignments: true,\n                        accessDeniedToExisting: true\n                    }\n                };\n            }\n            try {\n                const errorData = await response.json();\n                console.error(\"API Error Response:\", errorData);\n                errorMessage += \" - \".concat(errorData.error || errorData.message || \"Unknown error\");\n            } catch (e) {\n                console.log(\"No additional error details available\");\n            }\n            throw new Error(errorMessage);\n        }\n        const result = await response.json();\n        console.log(\"Plan assignments result:\", result);\n        console.log(\"First assignment details:\", result.assignments[0]);\n        return {\n            success: true,\n            data: result\n        };\n    } catch (error) {\n        console.error(\"Error fetching plan assignments:\", error);\n        return {\n            success: false,\n            error: \"Failed to fetch plan assignments\"\n        };\n    }\n};\n// Get active plan assignments for a company\nconst getActivePlanAssignmentsByCompany = async (companyId, referenceDate)=>{\n    try {\n        const queryParams = new URLSearchParams();\n        queryParams.append(\"includeExpired\", \"false\");\n        if (referenceDate) {\n            queryParams.append(\"referenceDate\", referenceDate);\n        }\n        const url = \"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/company/\").concat(companyId, \"?\").concat(queryParams.toString());\n        console.log(\"Fetching active plan assignments from:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: result\n        };\n    } catch (error) {\n        console.error(\"Error fetching active plan assignments:\", error);\n        return {\n            success: false,\n            error: \"Failed to fetch active plan assignments\"\n        };\n    }\n};\n// Create a new plan assignment\nconst createPlanAssignment = async (assignmentData)=>{\n    try {\n        console.log(\"Creating plan assignment with data:\", assignmentData);\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments\"), {\n            method: \"POST\",\n            headers: getHeaders(),\n            body: JSON.stringify(assignmentData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            const errorMessage = errorData.error || errorData.message || \"HTTP error! status: \".concat(response.status);\n            const errorDetails = errorData.details || errorData.required || [];\n            console.error(\"Plan assignment creation failed:\", {\n                status: response.status,\n                error: errorMessage,\n                details: errorDetails,\n                data: assignmentData\n            });\n            return {\n                success: false,\n                error: \"\".concat(errorMessage).concat(errorDetails.length > 0 ? \" - \".concat(errorDetails.join(\", \")) : \"\")\n            };\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: result.assignment\n        };\n    } catch (error) {\n        console.error(\"Error creating plan assignment:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to create plan assignment\"\n        };\n    }\n};\n// Update a plan assignment\nconst updatePlanAssignment = async (assignmentId, updateData)=>{\n    try {\n        console.log(\"Updating plan assignment:\", assignmentId, \"with data:\", updateData);\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId), {\n            method: \"PUT\",\n            headers: getHeaders(),\n            body: JSON.stringify(updateData)\n        });\n        console.log(\"Update response status:\", response.status);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            const errorMessage = errorData.error || errorData.message || \"HTTP error! status: \".concat(response.status);\n            const errorDetails = errorData.details || [];\n            console.error(\"Plan assignment update failed:\", {\n                status: response.status,\n                error: errorMessage,\n                details: errorDetails,\n                updateData: updateData\n            });\n            return {\n                success: false,\n                error: \"\".concat(errorMessage).concat(errorDetails.length > 0 ? \" - \".concat(errorDetails.join(\", \")) : \"\")\n            };\n        }\n        const result = await response.json();\n        console.log(\"Update successful:\", result);\n        return {\n            success: true,\n            data: result.assignment\n        };\n    } catch (error) {\n        console.error(\"Error updating plan assignment:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to update plan assignment\"\n        };\n    }\n};\n// Activate a plan assignment\nconst activatePlanAssignment = async (assignmentId)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId, \"/activate\"), {\n            method: \"POST\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: result.assignment\n        };\n    } catch (error) {\n        console.error(\"Error activating plan assignment:\", error);\n        return {\n            success: false,\n            error: \"Failed to activate plan assignment\"\n        };\n    }\n};\n// Deactivate a plan assignment\nconst deactivatePlanAssignment = async (assignmentId)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId, \"/deactivate\"), {\n            method: \"POST\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: result.assignment\n        };\n    } catch (error) {\n        console.error(\"Error deactivating plan assignment:\", error);\n        return {\n            success: false,\n            error: \"Failed to deactivate plan assignment\"\n        };\n    }\n};\n// Clone a plan assignment\nconst clonePlanAssignment = async (assignmentId, cloneData)=>{\n    try {\n        console.log(\"Cloning plan assignment:\", assignmentId, \"with overrides:\", cloneData);\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId, \"/clone\"), {\n            method: \"POST\",\n            headers: getHeaders(),\n            body: JSON.stringify({\n                overrides: cloneData || {}\n            })\n        });\n        console.log(\"Clone response status:\", response.status);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            const errorMessage = errorData.error || errorData.message || \"HTTP error! status: \".concat(response.status);\n            const errorDetails = errorData.details || [];\n            console.error(\"Plan assignment clone failed:\", {\n                status: response.status,\n                error: errorMessage,\n                details: errorDetails,\n                cloneData: cloneData\n            });\n            return {\n                success: false,\n                error: \"\".concat(errorMessage).concat(errorDetails.length > 0 ? \" - \".concat(errorDetails.join(\", \")) : \"\")\n            };\n        }\n        const result = await response.json();\n        console.log(\"Clone successful:\", result);\n        return {\n            success: true,\n            data: result.assignment\n        };\n    } catch (error) {\n        console.error(\"Error cloning plan assignment:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to clone plan assignment\"\n        };\n    }\n};\n// Check if plan assignment can be edited\nconst canEditPlanAssignment = async (assignmentId)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments/\").concat(assignmentId, \"/can-edit\"), {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return {\n            success: true,\n            data: result\n        };\n    } catch (error) {\n        console.error(\"Error checking if plan assignment can be edited:\", error);\n        return {\n            success: false,\n            error: \"Failed to check edit permissions\"\n        };\n    }\n};\n// Get assignable plans (Active plans only)\nconst getAssignablePlans = async ()=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/assignable\"), {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        // Backend returns { plans: [...] } format for assignable plans\n        return {\n            success: true,\n            data: result.plans || []\n        };\n    } catch (error) {\n        console.error(\"Error fetching assignable plans:\", error);\n        return {\n            success: false,\n            error: \"Failed to fetch assignable plans\"\n        };\n    }\n};\n// Get aggregated plan assignments count for broker dashboard\nconst getBrokerPlanAssignmentsCount = async ()=>{\n    try {\n        const userId = (0,_utils_env__WEBPACK_IMPORTED_MODULE_0__.getUserId)();\n        console.log(\"\\uD83D\\uDD0D Fetching broker plan assignments count for userId:\", userId);\n        // Use the optimized general API without pagination to get all assignments\n        const url = \"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments?includePlanData=false\");\n        console.log(\"\\uD83D\\uDCE1 Fetching broker assignments count from:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: getHeaders()\n        });\n        console.log(\"Broker assignments count API response status:\", response.status);\n        if (response.ok) {\n            const data = await response.json();\n            console.log(\"✅ Broker assignments count response:\", data);\n            return {\n                success: true,\n                data: {\n                    count: data.count || 0\n                },\n                error: null\n            };\n        } else {\n            const errorText = await response.text();\n            console.error(\"❌ Failed to fetch broker assignments count:\", response.status, errorText);\n            return {\n                success: false,\n                data: null,\n                error: \"Failed to fetch assignments count: \".concat(response.status)\n            };\n        }\n    } catch (error) {\n        console.error(\"❌ Error fetching broker assignments count:\", error);\n        return {\n            success: false,\n            data: null,\n            error: \"Network error while fetching assignments count\"\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/manage-groups/services/planAssignmentApi.ts\n"));

/***/ })

});