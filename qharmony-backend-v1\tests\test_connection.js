/**
 * SIMPLE CONNECTION TEST
 * Test if the server is reachable
 */

const axios = require('axios');

async function testConnection() {
  const urls = [
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'http://0.0.0.0:8080'
  ];
  
  console.log('🔍 Testing server connectivity...\n');
  
  for (const url of urls) {
    try {
      console.log(`Testing: ${url}`);
      const response = await axios.get(`${url}/health`, { timeout: 5000 });
      console.log(`✅ SUCCESS: ${url} - Status: ${response.status}`);
      console.log(`Response: ${JSON.stringify(response.data, null, 2)}\n`);
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ FAILED: ${url} - Connection refused`);
      } else if (error.response) {
        console.log(`⚠️  RESPONSE: ${url} - Status: ${error.response.status}`);
        console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
      } else {
        console.log(`❌ ERROR: ${url} - ${error.message}`);
      }
      console.log('');
    }
  }
  
  // Test a simple API endpoint
  console.log('🔍 Testing API endpoint...');
  try {
    const response = await axios.get('http://127.0.0.1:8080/api/pre-enrollment/carriers', {
      headers: {
        'user-id': '6838677aef6db0212bcfdacd'
      },
      timeout: 5000
    });
    console.log(`✅ API SUCCESS: Status ${response.status}`);
    console.log(`Carriers found: ${response.data.carriers?.length || 0}`);
  } catch (error) {
    console.log(`❌ API FAILED: ${error.message}`);
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

testConnection();
