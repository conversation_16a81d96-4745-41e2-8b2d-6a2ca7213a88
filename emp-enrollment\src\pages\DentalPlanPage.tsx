import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Smile } from 'lucide-react';
import { PlanCard } from '@/components/PlanCard';
import { VideoPlayer } from '@/components/VideoPlayer';
import { PlanQADialog } from '@/components/PlanQADialog';
import { UserProfile } from '@/components/BenefitsEnrollmentBot';
import { BotQuestion } from '@/components/BotQuestion';
import { ROICalculator } from '@/components/ROICalculator';
import { PopularChoiceBadge } from '@/components/PopularChoiceBadge';

interface DentalPlanPageProps {
  userProfile: UserProfile;
  onPlanSelect: (planData: any) => void;
}

export const DentalPlanPage = ({ userProfile, onPlanSelect }: DentalPlanPageProps) => {
  const questionText = userProfile.needsDentalCare 
    ? "🦷 Great choice! Here's your dental plan recommendation"
    : "🦷 Dental coverage options for your smile";
    
  const contextText = userProfile.needsDentalCare
    ? "Based on your dental care needs, I recommend the PPO plan."
    : "Even basic coverage can save you hundreds on cleanings and care.";

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <BotQuestion 
        question={questionText}
        context={contextText}
      />

      {/* ROI Calculator for recommended plan */}
      <ROICalculator 
        planCost={userProfile.needsDentalCare ? 12.00 : 6.50}
        planType="dental"
        familySize={userProfile.familyMembers}
      />
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smile className="w-6 h-6 text-green-500" />
            <h2 className="text-xl">Choose Your Dental Plan</h2>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-3">
            <div className="flex items-center gap-2 mb-2">
              <PopularChoiceBadge percentage={72} planType="dental" />
              <span className="text-sm text-muted-foreground">Most employees choose PPO for flexibility</span>
            </div>
            
            <PlanCard
              type="dental"
              recommended={userProfile.needsDentalCare}
              title="Delta Dental PPO"
              cost="$12.00"
              period="paycheck"
              features={[
                "100% preventive care (cleanings, exams)",
                "$50 deductible",
                "Large provider network",
                "💰 Employer pays 50% (saves you $156/year)",
                "🦷 Free cleanings save $400/year"
              ]}
              onSelect={() => onPlanSelect({
                name: 'Delta Dental PPO',
                cost: 12.00,
                type: 'PPO'
              })}
            />
            <PlanCard
              type="dental"
              title="Guardian DHMO"
              cost="$6.50"
              period="paycheck"
              features={[
                "Lower cost option",
                "Fewer provider options",
                "Good for basic care",
                "💰 Employer pays 50% (saves you $84/year)"
              ]}
              onSelect={() => onPlanSelect({
                name: 'Guardian DHMO',
                cost: 6.50,
                type: 'HMO'
              })}
            />
          </div>

          {/* Value examples */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 className="font-medium mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">💡 Real Cost Examples (PPO Plan):</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Annual cleaning & exam</span>
                <span className="bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent font-medium">FREE (saves $200)</span>
              </div>
              <div className="flex justify-between">
                <span>Cavity filling</span>
                <span>$50 <span className="text-gray-500 line-through">vs $250</span></span>
              </div>
              <div className="flex justify-between">
                <span>Crown</span>
                <span>$400 <span className="text-gray-500 line-through">vs $1,200</span></span>
              </div>
            </div>
          </div>
          
          <div className="flex gap-2 pt-4">
            <VideoPlayer 
              title="Dental Benefits Guide" 
              description="How to maximize your dental benefits"
              planType="dental"
            />
            <PlanQADialog selectedPlans={{}} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
