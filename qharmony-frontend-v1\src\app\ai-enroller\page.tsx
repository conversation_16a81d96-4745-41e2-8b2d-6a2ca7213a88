'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { HiOutlinePlus, HiOutlineUsers, HiOutlineDocumentText, HiOutlineUserGroup, HiOutlineOfficeBuilding } from 'react-icons/hi';
import Image from 'next/image';
import ProtectedRoute from '@/components/ProtectedRoute';
import LoadingOptimizer from './components/LoadingOptimizer';
import { usePerformanceMonitor } from './hooks/usePerformanceMonitor';
import { useAppSelector } from '@/redux/hooks';
import { RootState } from '@/redux/store';
import './globals.css';

const AIEnrollerPage: React.FC = () => {
  const router = useRouter();
  const [userRole, setUserRole] = useState<string>('');
  const [isRedirecting, setIsRedirecting] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Get user data from Redux store
  const userDetails = useAppSelector((state: RootState) => state.user.userProfile);

  // Monitor page performance
  usePerformanceMonitor('AI Enroller Dashboard');

  // Get user's first name for personalized greeting
  const getUserFirstName = (): string => {
    if (userDetails?.name) {
      return userDetails.name.split(' ')[0];
    }
    // Fallback to localStorage or default (only in browser environment)
    if (typeof window !== 'undefined') {
      const storedBrokerName = localStorage.getItem('brokerName');
      if (storedBrokerName) {
        return storedBrokerName.split(' ')[0];
      }
    }
    return 'there'; // Generic greeting if no name available
  };

  useEffect(() => {
    // Get user role (only in browser environment)
    if (typeof window !== 'undefined') {
      const storedUserRole = localStorage.getItem('userRole') || '';
      setUserRole(storedUserRole);

      // Role-based routing: Redirect employees (users who are NOT admin and NOT broker) directly to enrollment
      const isEmployee = userDetails && !userDetails.isAdmin && !userDetails.isBroker;

      if (isEmployee) {
        setIsRedirecting(true);
        router.replace('/ai-enroller/employee-enrol');
        return;
      }

      // Prefetch routes to reduce loading time (for admins and brokers)
      router.prefetch('/ai-enroller/create-plan');
      router.prefetch('/ai-enroller/plans');
      router.prefetch('/ai-enroller/manage-groups');
      router.prefetch('/ai-enroller/employee-enrol');
      if (storedUserRole === 'superadmin') {
        router.prefetch('/ai-enroller/create-carrier');
      }
    }
  }, [router, userDetails]);

  // Navigation handlers removed - using Link components for better performance

  // Show loading state for employees while redirecting
  const isEmployee = userDetails && !userDetails.isAdmin && !userDetails.isBroker;
  if (isEmployee || isRedirecting) {
    return (
      <ProtectedRoute>
        <div style={{
          minHeight: '100vh',
          backgroundColor: '#f9fafb',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontFamily: 'SF Pro, -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            padding: '32px',
            textAlign: 'center',
            maxWidth: '400px',
            width: '90%'
          }}>
            <div style={{
              width: '48px',
              height: '48px',
              backgroundColor: '#7c3aed',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 16px'
            }}>
              <Image
                src="/brea.png"
                alt="Brea AI Assistant"
                width={48}
                height={48}
                style={{ borderRadius: '50%' }}
                priority
              />
            </div>
            <h2 style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#111827',
              margin: '0 0 8px 0'
            }}>
              Welcome to AI Enroller
            </h2>
            <p style={{
              color: '#6b7280',
              fontSize: '14px',
              margin: '0 0 16px 0',
              lineHeight: '1.5'
            }}>
              Taking you to your personalized enrollment experience...
            </p>
            <div style={{
              width: '32px',
              height: '32px',
              border: '3px solid #e5e7eb',
              borderTop: '3px solid #7c3aed',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto'
            }} />
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f9fafb',
        fontFamily: 'SF Pro, -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif'
      }}>
      <LoadingOptimizer />
      <div style={{
        maxWidth: '896px',
        margin: '0 auto',
        padding: '32px 24px'
      }}>
        {/* AI Assistant Chat */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '16px',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb',
          padding: '24px',
          marginBottom: '32px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {/* AI Avatar */}
            <div style={{
              width: '48px',
              height: '48px',
              backgroundColor: '#7c3aed',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0
            }}>
              <Image
                src="/brea.png"
                alt="Brea AI Assistant"
                width={48}
                height={48}
                style={{ borderRadius: '50%' }}
                priority
                loading="eager"
              />
            </div>

            {/* AI Message */}
            <div style={{ flex: 1, minWidth: 0 }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                marginBottom: '8px',
                flexWrap: 'wrap'
              }}>
                <span
                  className="page-title"
                  style={{
                    fontSize: 'clamp(16px, 4vw, 18px)',
                    fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                    lineHeight: '1.4',
                    wordBreak: 'break-word'
                  }}
                >
                  Hi {getUserFirstName()}! I&apos;m here to help you manage your insurance plans.
                </span>
              </div>
              <p
                className="subtitle-text"
                style={{
                  fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                  margin: 0
                }}
              >
                What would you like to do today?
              </p>
            </div>
          </div>
        </div>

        {/* Action Cards */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {/* Create New Plan */}
          <Link href="/ai-enroller/create-plan" prefetch={true}>
            <div
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e5e7eb',
                padding: '24px',
                cursor: 'pointer',
                transition: 'box-shadow 0.2s ease',
                textDecoration: 'none'
              }}
              onMouseOver={(e) => e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'}
              onMouseOut={(e) => e.currentTarget.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)'}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{
                  width: '48px',
                  height: '48px',
                  backgroundColor: '#ede9fe',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlinePlus style={{ width: '24px', height: '24px', color: '#7c3aed' }} />
                </div>
                <div>
                  <h3 className="section-header" style={{ margin: '0 0 4px 0' }}>
                    Create New Plan
                  </h3>
                  <p className="body-text">Set up a new insurance plan with AI assistance</p>
                </div>
              </div>
            </div>
          </Link>

          {/* View All Plans */}
          <Link href="/ai-enroller/plans" prefetch={true}>
            <div
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e5e7eb',
                padding: '24px',
                cursor: 'pointer',
                transition: 'box-shadow 0.2s ease',
                textDecoration: 'none'
              }}
              onMouseOver={(e) => e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'}
              onMouseOut={(e) => e.currentTarget.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)'}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{
                  width: '48px',
                  height: '48px',
                  backgroundColor: '#ede9fe',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlineDocumentText style={{ width: '24px', height: '24px', color: '#7c3aed' }} />
                </div>
                <div>
                  <h3 className="section-header" style={{ margin: '0 0 4px 0' }}>
                    View All Plans
                  </h3>
                  <p className="body-text">Browse, search, and manage your existing plans</p>
                </div>
              </div>
            </div>
          </Link>

          {/* Manage Group Plans */}
          <Link href="/ai-enroller/manage-groups" prefetch={true}>
            <div
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e5e7eb',
                padding: '24px',
                cursor: 'pointer',
                transition: 'box-shadow 0.2s ease',
                textDecoration: 'none'
              }}
              onMouseOver={(e) => e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'}
              onMouseOut={(e) => e.currentTarget.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)'}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{
                  width: '48px',
                  height: '48px',
                  backgroundColor: '#ede9fe',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlineUsers style={{ width: '24px', height: '24px', color: '#7c3aed' }} />
                </div>
                <div>
                  <h3 className="section-header" style={{ margin: '0 0 4px 0' }}>
                    Manage Group Plans
                  </h3>
                  <p className="body-text">Assign plans to employer groups and manage renewals</p>
                </div>
              </div>
            </div>
          </Link>

          {/* Employee Enrollment */}
          <Link href="/ai-enroller/employee-enrol" prefetch={true}>
            <div
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e5e7eb',
                padding: '24px',
                cursor: 'pointer',
                transition: 'box-shadow 0.2s ease',
                textDecoration: 'none'
              }}
              onMouseOver={(e) => e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'}
              onMouseOut={(e) => e.currentTarget.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)'}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{
                  width: '48px',
                  height: '48px',
                  backgroundColor: '#ede9fe',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <HiOutlineUserGroup style={{ width: '24px', height: '24px', color: '#7c3aed' }} />
                </div>
                <div>
                  <h3 className="section-header" style={{ margin: '0 0 4px 0' }}>
                    Employee Enrollment
                  </h3>
                  <p className="body-text">AI-powered benefits enrollment for employees</p>
                </div>
              </div>
            </div>
          </Link>

          {/* Create Carrier - Only for SuperAdmins */}
          {userRole === 'superadmin' && (
            <Link href="/ai-enroller/create-carrier" prefetch={true}>
              <div
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  border: '1px solid #e5e7eb',
                  padding: '24px',
                  cursor: 'pointer',
                  transition: 'box-shadow 0.2s ease',
                  textDecoration: 'none'
                }}
                onMouseOver={(e) => e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'}
                onMouseOut={(e) => e.currentTarget.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)'}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  <div style={{
                    width: '48px',
                    height: '48px',
                    backgroundColor: '#fed7aa',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <HiOutlineOfficeBuilding style={{ width: '24px', height: '24px', color: '#ea580c' }} />
                  </div>
                  <div>
                    <h3 className="section-header" style={{ margin: '0 0 4px 0' }}>
                      Create Carrier
                    </h3>
                    <p className="body-text" style={{ margin: '0 0 4px 0' }}>Add new insurance carriers to the system</p>
                    <span style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      padding: '2px 8px',
                      borderRadius: '9999px',
                      fontSize: '12px',
                      fontWeight: '500',
                      backgroundColor: '#fed7aa',
                      color: '#9a3412'
                    }}>
                      SuperAdmin Only
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          )}
        </div>
      </div>
      </div>
    </ProtectedRoute>
  );
};

export default AIEnrollerPage;
