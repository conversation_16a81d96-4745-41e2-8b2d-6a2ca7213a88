/* Import Group Plan Setup Styles */
@import './group-plan-setup/group-plan-setup.css';

/* Root layout - using design system */
body {
  background-color: var(--white);
  color: var(--black);
}

/* Card container - updated to use design system */
.enroller-card {
  background-color: var(--white);
  color: var(--black);
  padding: 32px; /* Using design system spacing */
  border-radius: 12px; /* Using design system border radius */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  max-width: 40rem;
  margin: 0 auto;
  border: 1px solid var(--gray-200);
}

/* Form labels - updated to design system */
.enroller-label {
  font-size: 16px; /* Section label size from design system */
  font-weight: 500;
  color: var(--gray-700);
  display: block;
  margin-bottom: 8px;
}

/* Input and Select - updated to design system */
.enroller-input,
.enroller-select {
  width: 100%;
  padding: 12px 16px; /* Design system form input padding */
  border-radius: 8px;
  border: 1px solid var(--gray-300);
  background-color: var(--white);
  color: var(--gray-900);
  margin-bottom: 16px; /* Design system spacing */
  font-size: 14px;
  transition: all 0.2s ease;
}

.enroller-input:focus,
.enroller-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Buttons - updated to use design system */
.enroller-btn {
  padding: 12px 20px; /* Design system button padding */
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  color: var(--white);
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.enroller-btn-next {
  background-color: var(--black);
}

.enroller-btn-next:hover {
  background-color: var(--gray-800);
  transform: translateY(-1px);
}

.enroller-btn-back {
  background-color: var(--gray-500);
}

.enroller-btn-back:hover {
  background-color: var(--gray-600);
  transform: translateY(-1px);
}

.enroller-btn-submit {
  background-color: var(--success-primary);
}

.enroller-btn-submit:hover {
  background-color: #15803d;
  transform: translateY(-1px);
}

.enroller-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Review Box */
.enroller-review-box {
  background-color: #f3f4f6; /* gray-100 */
  color: #111827;
  padding: 1rem;
  border-radius: 0.75rem;
  max-height: 16rem;
  overflow-y: auto;
  font-size: 0.875rem;
  white-space: pre-wrap;
  font-family: monospace;
}

/* Option Cards */
.enroller-option-card {
  text-decoration: none;
  color: inherit;
}

.enroller-option-card:hover {
  text-decoration: none;
  color: inherit;
}

/* Group Plan Setup Container */
.plan-setup-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.group-plan-card {
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  max-width: 300px;
  width: 100%;
}

.group-plan-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.group-plan-card .plan-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.group-plan-card .plan-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.group-plan-card .plan-description {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}
