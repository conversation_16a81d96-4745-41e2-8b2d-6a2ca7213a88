import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface OnboardingState {
  userDetails: {
    email: string;
    name: string;
    role: string;
    isAdmin: boolean;
    isBroker: boolean;
    isActivated: boolean;
  };
  companyDetails: {
    name: string;
    adminEmail: string;
    adminRole: string;
    companySize: number;
    industry: string;
    location: string;
    website: string;
    howHeard: string;
    brokerId: string;
    brokerageId: string;
    isBrokerage: boolean;
    isActivated: boolean;
  };
  additionalParams: {
    isAdmin: boolean;
  };
}

const initialState: OnboardingState = {
  additionalParams: {
    isAdmin: false,
  },
  userDetails: {
    email: "",
    name: "",
    role: "",
    isAdmin: false,
    isBroker: false,
    isActivated: false,
  },
  companyDetails: {
    name: "",
    adminEmail: "",
    adminRole: "",
    companySize: 0,
    industry: "",
    location: "",
    website: "",
    howHeard: "",
    brokerId: "",
    brokerageId: "",
    isBrokerage: false,
    isActivated: false,
  },
};

export const onboardingSlice = createSlice({
  name: "onboarding",
  initialState,
  reducers: {
    setUserDetails: (
      state,
      action: PayloadAction<OnboardingState["userDetails"]>,
    ) => {
      state.userDetails = action.payload;
    },
    setCompanyDetails: (
      state,
      action: PayloadAction<OnboardingState["companyDetails"]>,
    ) => {
      state.companyDetails = action.payload;
    },
    setAdditionalParams: (
      state,
      action: PayloadAction<OnboardingState["additionalParams"]>,
    ) => {
      state.additionalParams = action.payload;
    },
  },
});

export const { setUserDetails, setCompanyDetails, setAdditionalParams } = onboardingSlice.actions;

export default onboardingSlice.reducer;
