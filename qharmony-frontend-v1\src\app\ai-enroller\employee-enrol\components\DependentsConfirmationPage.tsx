'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { User, Users, Heart, Edit2, CheckCircle, Plus, Trash2, X, AlertCircle, UserCheck } from 'lucide-react';
import { getRequest, putRequest } from '@/APILayer/axios_helper';
import EnhancedEditProfileDialog from '@/app/dashboard/enhanced_edit_user_profile_popup';
import CustomModal from './CustomModal';

// Helper function to get company admin ID
const getCompanyAdminId = async (): Promise<string | null> => {
  try {
    const response = await getRequest('/employee/company-details');
    if (response && response.company) {
      const company = response.company;

      // Try to find admin user by email, but don't fail if endpoint doesn't exist
      try {
        const adminResponse = await getRequest('/admin/all-employees');
        if (adminResponse && adminResponse.employees) {
          const admin = adminResponse.employees.find((emp: any) =>
            emp.email === company.adminEmail && emp.isAdmin
          );
          if (admin?._id) {
            return admin._id;
          }
        }
      } catch (adminError) {
        // Silently handle 404 or other errors from admin endpoint
        console.log('Admin endpoint not available, using fallback');
      }
    }

    // Fallback: use current user ID
    const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
    return userId;
  } catch (error) {
    console.log('Using fallback admin ID due to error:', error);
    // Fallback: if we can't get company admin ID, use current user ID
    // This handles the case where admin is updating their own profile
    const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
    return userId;
  }
};

interface Dependent {
  _id?: string;
  name: string;
  relationship: string;
  dateOfBirth?: string;
  gender?: string;
  ssn?: string;
}

interface DependentsConfirmationPageProps {
  selectedDependents: string[];
  onConfirm: (dependents: Dependent[]) => void;
  onBack: () => void;
  onDependentsChange?: (dependents: Dependent[]) => void;
  onProfileUpdate?: () => void;
}

const DependentsConfirmationPage: React.FC<DependentsConfirmationPageProps> = ({
  selectedDependents,
  onConfirm,
  onBack,
  onDependentsChange,
  onProfileUpdate
}) => {
  const [userDetails, setUserDetails] = useState<any>(null);
  const [dependents, setDependents] = useState<Dependent[]>([]);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [showAddPopup, setShowAddPopup] = useState(false);
  const [showEditPopup, setShowEditPopup] = useState(false);
  const [editingDependent, setEditingDependent] = useState<Dependent | null>(null);
  const [loading, setLoading] = useState(false);

  // Profile modal state
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [profileValidation, setProfileValidation] = useState<{
    isValid: boolean;
    missingFields: string[];
    errors: string[];
  }>({ isValid: true, missingFields: [], errors: [] });
  const [newDependent, setNewDependent] = useState({
    firstName: '',
    middleName: '',
    lastName: '',
    relationship: 'Spouse',
    dateOfBirth: '',
    gender: 'Male',
    ssn: ''
  });
  const [editFormData, setEditFormData] = useState({
    name: '',
    relationship: 'Spouse',
    dateOfBirth: '',
    gender: 'Male',
    ssn: ''
  });

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    title: '',
    message: '',
    type: 'alert' as 'alert' | 'confirm' | 'success' | 'error',
    onConfirm: () => {},
    confirmText: 'OK',
    cancelText: 'Cancel'
  });

  // Modal helper functions
  const showAlert = (title: string, message: string, type: 'alert' | 'success' | 'error' = 'alert') => {
    setModalConfig({
      title,
      message,
      type,
      onConfirm: () => {},
      confirmText: 'OK',
      cancelText: 'Cancel'
    });
    setShowModal(true);
  };

  const showConfirm = (title: string, message: string, onConfirm: () => void, confirmText = 'OK', cancelText = 'Cancel') => {
    setModalConfig({
      title,
      message,
      type: 'confirm',
      onConfirm,
      confirmText,
      cancelText
    });
    setShowModal(true);
  };

  // Get user ID from localStorage
  const getUserId = () => {
    const primaryKey = process.env.NEXT_PUBLIC_USER_ID_KEY || "userid1";
    const altKey = process.env.NEXT_PUBLIC_USER_ID_ALT_KEY || "userId";
    return localStorage.getItem(primaryKey) || localStorage.getItem(altKey);
  };

  // Fetch user details directly using API
  const fetchUserDetails = async () => {
    try {
      const userId = getUserId();
      if (!userId) {
        console.error('User ID not found');
        return;
      }

      const response = await getRequest('/employee', { 'user-id': userId });

      if (response?.currentUser) {
        setUserDetails(response.currentUser);
      }
    } catch (error) {
      console.error('Error fetching user details:', error);
    }
  };

  // Fetch user details on component mount
  useEffect(() => {
    fetchUserDetails();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Notify parent component when dependents change
  useEffect(() => {
    if (onDependentsChange) {
      onDependentsChange(dependents);
    }
  }, [dependents, onDependentsChange]);



  // Load real dependent data from user profile
  useEffect(() => {
    if (userDetails?.details?.dependents && Array.isArray(userDetails.details.dependents)) {
      const formattedDependents = userDetails.details.dependents.map((dep: any, index: number) => ({
        _id: dep._id || `existing_${index}_${Date.now()}`,
        name: dep.name || "",
        relationship: dep.relationship || "",
        dateOfBirth: dep.dateOfBirth ? new Date(dep.dateOfBirth).toISOString().split('T')[0] : "",
        gender: dep.gender || "",
        ssn: dep.ssn || ""
      }));

      // Filter dependents based on selected types from personalization
      const filteredDependents = formattedDependents.filter((dep: Dependent) => {
        const rel = dep.relationship.toLowerCase();

        if (selectedDependents.includes('spouse') &&
            (rel === 'spouse' || rel === 'domestic partner')) {
          return true;
        }
        if (selectedDependents.includes('children') &&
            (rel === 'child' || rel === 'stepchild' || rel === 'adopted child')) {
          return true;
        }
        return false;
      });

      setDependents(filteredDependents);
    } else {
      setDependents([]);
    }
  }, [selectedDependents, userDetails]);

  // Profile validation function
  const validateUserProfile = (user: any): { isValid: boolean; missingFields: string[]; errors: string[] } => {
    const missingFields: string[] = [];
    const errors: string[] = [];

    // Required fields for enrollment
    if (!user?.details?.dateOfBirth) {
      missingFields.push('Date of Birth');
      errors.push('Date of birth is required for age-based cost calculations');
    }

    if (!user?.details?.hireDate) {
      missingFields.push('Hire Date');
      errors.push('Hire date is required for waiting period eligibility');
    }

    if (!user?.details?.employeeClassType) {
      missingFields.push('Employee Class Type');
      errors.push('Employee class type is required for plan eligibility');
    }

    if (!user?.details?.phoneNumber) {
      missingFields.push('Phone Number');
      errors.push('Phone number is required for enrollment communications');
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
      errors
    };
  };

  // Check profile validation when user details change
  useEffect(() => {
    if (userDetails) {
      const validation = validateUserProfile(userDetails);
      setProfileValidation(validation);
    }
  }, [userDetails]);

  const handleNameEdit = (id: string, newName: string) => {
    setDependents(prev => prev.map(dep =>
      dep._id === id ? { ...dep, name: newName } : dep
    ));
    setIsEditing(null);
  };

  const handleEditDependent = (dependent: Dependent) => {
    setEditingDependent(dependent);
    setEditFormData({
      name: dependent.name,
      relationship: dependent.relationship,
      dateOfBirth: dependent.dateOfBirth || '',
      gender: dependent.gender || 'Male',
      ssn: dependent.ssn || ''
    });
    setShowEditPopup(true);
  };

  const handleUpdateDependent = async () => {
    if (!editingDependent || !editFormData.name.trim()) {
      showAlert('Validation Error', 'Please fill in all required fields', 'error');
      return;
    }

    setLoading(true);
    try {
      // Get current dependents from API
      const currentDependents = userDetails?.details?.dependents || [];

      // Update the specific dependent
      const updatedDependents = currentDependents.map((dep: any) =>
        dep._id === editingDependent._id
          ? {
              ...dep,
              name: editFormData.name.trim(),
              relationship: editFormData.relationship,
              dateOfBirth: editFormData.dateOfBirth ? new Date(editFormData.dateOfBirth) : dep.dateOfBirth,
              gender: editFormData.gender,
              ssn: editFormData.ssn.trim()
            }
          : dep
      );

      console.log('Updating dependent in API:', editFormData);
      console.log('Updated dependents array:', updatedDependents);

      // Update user profile using correct API endpoint
      const userId = getUserId();
      if (!userId) throw new Error('User ID not found');

      // Get company admin ID for authorization
      const companyAdminId = await getCompanyAdminId();
      if (!companyAdminId) throw new Error('Company admin ID not found');

      const response = await putRequest(`/admin/update/employee`, {
        adminId: companyAdminId, // Company admin ID for authorization
        updatedDetails: {
          name: userDetails.name,
          email: userDetails.email,
          details: {
            ...userDetails.details,
            dependents: updatedDependents
          }
        }
      });

      if (response && response.status === 200) {
        // Refresh user details to get updated data
        await fetchUserDetails();
        console.log('Dependent updated successfully');
        setShowEditPopup(false);
        setEditingDependent(null);
      } else {
        throw new Error('Failed to update dependent');
      }
    } catch (error) {
      console.error('Error updating dependent:', error);
      showAlert('Update Failed', 'Failed to update dependent. Please try again.', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleAddDependent = async () => {
    if (!newDependent.firstName.trim() || !newDependent.lastName.trim() || !newDependent.dateOfBirth) {
      showAlert('Validation Error', 'Please fill in all required fields (First Name, Last Name, Date of Birth)', 'error');
      return;
    }

    setLoading(true);
    try {
      // Combine name fields
      const fullName = `${newDependent.firstName.trim()} ${newDependent.middleName.trim() ? newDependent.middleName.trim() + ' ' : ''}${newDependent.lastName.trim()}`.trim();

      // Create new dependent with temporary ID (like the working popup)
      const dependentToAdd = {
        _id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: fullName,
        relationship: newDependent.relationship,
        dateOfBirth: newDependent.dateOfBirth,
        gender: newDependent.gender,
        ssn: newDependent.ssn.trim()
      };

      // Add to local state first
      setDependents(prev => [...prev, dependentToAdd]);

      // Prepare data for API (following the working popup pattern)
      const currentDependents = userDetails?.details?.dependents || [];
      const newDependentForAPI = {
        name: fullName,
        relationship: newDependent.relationship,
        dateOfBirth: new Date(newDependent.dateOfBirth).toISOString(),
        gender: newDependent.gender,
        ssn: newDependent.ssn.trim(),
        isStudent: false,
        isDisabled: false,
        isActive: true
      };

      const updatedDependents = [...currentDependents, newDependentForAPI];

      console.log('Adding dependent to API:', newDependentForAPI);
      console.log('Updated dependents array:', updatedDependents);

      // Update user profile with new dependent using correct API endpoint
      const userId = getUserId();
      if (!userId) throw new Error('User ID not found');

      // Get company admin ID for authorization
      const companyAdminId = await getCompanyAdminId();
      if (!companyAdminId) throw new Error('Company admin ID not found');

      const response = await putRequest(`/admin/update/employee`, {
        adminId: companyAdminId, // Company admin ID for authorization
        updatedDetails: {
          name: userDetails.name,
          email: userDetails.email,
          details: {
            ...userDetails.details,
            dependents: updatedDependents
          }
        }
      });

      if (response && response.status === 200) {
        // Refresh user details to get updated data
        await fetchUserDetails();
        console.log('Dependent added successfully');
      } else {
        throw new Error('Failed to update user');
      }

      setNewDependent({ firstName: '', middleName: '', lastName: '', relationship: 'Spouse', dateOfBirth: '', gender: 'Male', ssn: '' });
      setShowAddPopup(false);
    } catch (error) {
      console.error('Error adding dependent:', error);
      showAlert('Add Failed', 'Failed to add dependent. Please try again.', 'error');
      // Remove from local state if API failed
      setDependents(prev => prev.filter(dep => !dep._id?.toString().startsWith('temp_')));
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveDependent = async (id: string) => {
    showConfirm(
      'Remove Dependent',
      'Are you sure you want to remove this dependent?',
      () => performRemoveDependent(id),
      'Remove',
      'Cancel'
    );
  };

  const performRemoveDependent = async (id: string) => {

    setLoading(true);
    try {
      // Remove from local state first
      setDependents(prev => prev.filter(dep => dep._id !== id));

      // If it's a temporary ID, don't need to update API
      if (id.toString().startsWith('temp_')) {
        setLoading(false);
        return;
      }

      // Filter out the dependent to remove from API data
      const currentDependents = userDetails?.details?.dependents || [];
      const updatedDependents = currentDependents.filter((dep: any) => dep._id !== id);

      console.log('Removing dependent from API, updated array:', updatedDependents);

      // Update user profile using correct API endpoint
      const userId = getUserId();
      if (!userId) throw new Error('User ID not found');

      // Get company admin ID for authorization
      const companyAdminId = await getCompanyAdminId();
      if (!companyAdminId) throw new Error('Company admin ID not found');

      const response = await putRequest(`/admin/update/employee`, {
        adminId: companyAdminId, // Company admin ID for authorization
        updatedDetails: {
          name: userDetails.name,
          email: userDetails.email,
          details: {
            ...userDetails.details,
            dependents: updatedDependents
          }
        }
      });

      if (response && response.status === 200) {
        // Refresh user details
        await fetchUserDetails();
        console.log('Dependent removed successfully');
      } else {
        throw new Error('Failed to update user');
      }
    } catch (error) {
      console.error('Error removing dependent:', error);
      showAlert('Remove Failed', 'Failed to remove dependent. Please try again.', 'error');
      // Restore the dependent in local state if API failed
      const dependentToRestore = userDetails?.details?.dependents?.find((dep: any) => dep._id === id);
      if (dependentToRestore) {
        const restoredDependent = {
          _id: dependentToRestore._id,
          name: dependentToRestore.name,
          relationship: dependentToRestore.relationship,
          dateOfBirth: dependentToRestore.dateOfBirth ? new Date(dependentToRestore.dateOfBirth).toISOString().split('T')[0] : "",
          gender: dependentToRestore.gender
        };
        setDependents(prev => [...prev, restoredDependent]);
      }
    } finally {
      setLoading(false);
    }
  };

  const resetAddForm = () => {
    setNewDependent({ firstName: '', middleName: '', lastName: '', relationship: 'Spouse', dateOfBirth: '', gender: 'Male', ssn: '' });
    setShowAddPopup(false);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0
          }}>
            👨‍👩‍👧‍👦 Let&#39;s confirm your family members for coverage.
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0'
          }}>
            {selectedDependents.includes('spouse') && selectedDependents.includes('children')
              ? "I see you selected family coverage. Please review your spouse and children information below."
              : selectedDependents.includes('spouse')
              ? "I see you selected spouse coverage. Please review your spouse information below."
              : selectedDependents.includes('children')
              ? "I see you selected children coverage. Please review your children information below."
              : "I see you selected employee-only coverage. Let's make sure your profile is complete before proceeding."
            } {selectedDependents.length > 0 ? "You can edit any details or add new family members if needed." : "You can add family members if your situation changes."}
          </p>
        </div>
      </div>

      {/* Profile Validation Warning */}
      {!profileValidation.isValid && (
        <div style={{
          backgroundColor: '#fef3c7',
          border: '1px solid #f59e0b',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '24px',
          display: 'flex',
          alignItems: 'flex-start',
          gap: '12px'
        }}>
          <AlertCircle size={20} style={{ color: '#f59e0b', marginTop: '2px', flexShrink: 0 }} />
          <div style={{ flex: 1 }}>
            <h4 style={{
              fontSize: '14px',
              fontWeight: '600',
              color: '#92400e',
              margin: '0 0 8px 0'
            }}>
              Complete Your Profile to Continue
            </h4>
            <p style={{
              fontSize: '14px',
              color: '#92400e',
              margin: '0 0 12px 0',
              lineHeight: '1.5'
            }}>
              The following required information is missing from your profile:
            </p>
            <ul style={{
              margin: '0 0 16px 0',
              paddingLeft: '20px',
              color: '#92400e',
              fontSize: '14px'
            }}>
              {profileValidation.missingFields.map((field, index) => (
                <li key={index} style={{ marginBottom: '4px' }}>{field}</li>
              ))}
            </ul>
            <button
              onClick={() => setShowProfileModal(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                backgroundColor: '#f59e0b',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#d97706'}
              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f59e0b'}
            >
              <UserCheck size={16} />
              Complete Profile
            </button>
          </div>
        </div>
      )}

      {/* Profile Complete Confirmation */}
      {profileValidation.isValid && (
        <div style={{
          backgroundColor: '#f0fdf4',
          border: '1px solid #16a34a',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '24px',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          <CheckCircle size={20} style={{ color: '#16a34a' }} />
          <div>
            <p style={{
              fontSize: '14px',
              color: '#16a34a',
              margin: 0,
              fontWeight: '500'
            }}>
              Your profile is complete and ready for enrollment!
            </p>
          </div>
        </div>
      )}

      {/* Dependents Confirmation */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Users style={{ width: '24px', height: '24px', color: '#3b82f6' }} />
            <h2 style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#111827',
              margin: 0
            }}>
              {selectedDependents.length > 0 ? 'Confirm Family Members' : 'Profile & Coverage Confirmation'}
            </h2>
          </div>
          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              onClick={() => setShowProfileModal(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                backgroundColor: 'white',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <Edit2 size={16} />
              Edit Profile
            </button>
            <button
              onClick={() => setShowAddPopup(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                backgroundColor: '#000000',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <Plus size={16} />
              Add Dependent
            </button>
          </div>
        </div>

        {/* Employee (Always included) */}
        <div style={{
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '16px',
          backgroundColor: '#f8fafc'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <User style={{ width: '20px', height: '20px', color: '#6b7280' }} />
            <div>
              <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: 0 }}>
                {userDetails?.name || 'Employee'} (Myself)
              </h3>
              <p style={{ fontSize: '14px', color: '#6b7280', margin: '4px 0 0 0' }}>
                Employee
              </p>
            </div>
          </div>
        </div>

        {/* Dependents */}
        {dependents.map((dependent) => (
          <div key={dependent._id} style={{
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '16px',
            position: 'relative'
          }}>
            {/* Delete Button */}
            <button
              onClick={() => handleRemoveDependent(dependent._id!)}
              disabled={loading}
              style={{
                position: 'absolute',
                top: '12px',
                right: '12px',
                background: 'none',
                border: 'none',
                cursor: loading ? 'not-allowed' : 'pointer',
                padding: '4px',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                opacity: loading ? 0.5 : 1
              }}
              onMouseOver={(e) => !loading && (e.currentTarget.style.backgroundColor = '#fee2e2')}
              onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            >
              <Trash2 style={{ width: '16px', height: '16px', color: '#dc2626' }} />
            </button>

            <div style={{ display: 'flex', alignItems: 'center', paddingRight: '32px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <Heart style={{ width: '20px', height: '20px', color: '#ec4899' }} />
                <div>
                  <h3 style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: '#111827',
                    margin: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    {dependent.name} ({dependent.relationship})
                    <button
                      onClick={() => handleEditDependent(dependent)}
                      style={{
                        background: 'none',
                        border: 'none',
                        cursor: 'pointer',
                        padding: '2px'
                      }}
                      title="Edit dependent details"
                    >
                      <Edit2 style={{ width: '14px', height: '14px', color: '#6b7280' }} />
                    </button>
                  </h3>
                  <p style={{ fontSize: '14px', color: '#6b7280', margin: '4px 0 0 0' }}>
                    {dependent.relationship}
                    {dependent.dateOfBirth && ` • Born ${new Date(dependent.dateOfBirth).toLocaleDateString()}`}
                    {dependent.gender && ` • ${dependent.gender}`}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}


      </div>

      {/* Add Dependent Popup */}
      {showAddPopup && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '24px',
            width: '90%',
            maxWidth: '400px',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            position: 'relative'
          }}>
            {/* Close Button */}
            <button
              onClick={resetAddForm}
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '4px',
                borderRadius: '4px'
              }}
            >
              <X size={20} style={{ color: '#6b7280' }} />
            </button>

            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#111827',
              margin: '0 0 20px 0'
            }}>
              Add Family Member
            </h3>

            {/* Name Fields */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '12px', marginBottom: '16px' }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  First Name *
                </label>
                <input
                  type="text"
                  value={newDependent.firstName}
                  onChange={(e) => setNewDependent(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder="First name"
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    backgroundColor: 'white',
                    color: '#111827'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Middle Name
                </label>
                <input
                  type="text"
                  value={newDependent.middleName}
                  onChange={(e) => setNewDependent(prev => ({ ...prev, middleName: e.target.value }))}
                  placeholder="Middle name"
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    backgroundColor: 'white',
                    color: '#111827'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Last Name *
                </label>
                <input
                  type="text"
                  value={newDependent.lastName}
                  onChange={(e) => setNewDependent(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder="Last name"
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    backgroundColor: 'white',
                    color: '#111827'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>
            </div>

            {/* Relationship Field */}
            <div style={{ marginBottom: '16px' }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '6px'
              }}>
                Relationship *
              </label>
              <select
                value={newDependent.relationship}
                onChange={(e) => setNewDependent(prev => ({ ...prev, relationship: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none',
                  backgroundColor: 'white',
                  color: '#111827',
                  boxSizing: 'border-box'
                }}
              >
                <option value="Spouse">Spouse</option>
                <option value="Domestic Partner">Domestic Partner</option>
                <option value="Child">Child</option>
                <option value="Stepchild">Stepchild</option>
                <option value="Adopted Child">Adopted Child</option>
              </select>
            </div>

            {/* Gender Field */}
            <div style={{ marginBottom: '16px' }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '6px'
              }}>
                Gender *
              </label>
              <select
                value={newDependent.gender}
                onChange={(e) => setNewDependent(prev => ({ ...prev, gender: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none',
                  backgroundColor: 'white',
                  color: '#111827',
                  boxSizing: 'border-box'
                }}
              >
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
                <option value="Prefer not to say">Prefer not to say</option>
              </select>
            </div>

            {/* Date of Birth Field */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '6px'
              }}>
                Date of Birth *
              </label>
              <input
                type="date"
                value={newDependent.dateOfBirth}
                onChange={(e) => setNewDependent(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none',
                  boxSizing: 'border-box',
                  backgroundColor: 'white',
                  color: '#111827',
                  colorScheme: 'light',
                  WebkitAppearance: 'none',
                  MozAppearance: 'textfield'
                } as React.CSSProperties}
                onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>

            {/* SSN Field */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '6px'
              }}>
                Social Security Number (Optional)
              </label>
              <input
                type="text"
                value={newDependent.ssn}
                onChange={(e) => setNewDependent(prev => ({ ...prev, ssn: e.target.value }))}
                placeholder="XXX-XX-XXXX"
                maxLength={11}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none',
                  boxSizing: 'border-box',
                  backgroundColor: 'white',
                  color: '#111827'
                }}
                onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>

            {/* Action Buttons */}
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
              <button
                onClick={resetAddForm}
                style={{
                  padding: '10px 20px',
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  color: '#374151',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleAddDependent}
                disabled={!newDependent.firstName.trim() || !newDependent.lastName.trim() || !newDependent.dateOfBirth || loading}
                style={{
                  padding: '10px 20px',
                  backgroundColor: (newDependent.firstName.trim() && newDependent.lastName.trim() && newDependent.dateOfBirth && !loading) ? '#000000' : '#d1d5db',
                  border: 'none',
                  borderRadius: '6px',
                  color: 'white',
                  cursor: (newDependent.firstName.trim() && newDependent.lastName.trim() && newDependent.dateOfBirth && !loading) ? 'pointer' : 'not-allowed',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {loading ? 'Adding...' : 'Add Member'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Dependent Popup */}
      {showEditPopup && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '24px',
            width: '90%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflow: 'auto',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
                Edit Dependent Details
              </h3>
              <button
                onClick={() => {
                  setShowEditPopup(false);
                  setEditingDependent(null);
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  padding: '4px'
                }}
              >
                <X style={{ width: '20px', height: '20px', color: '#6b7280' }} />
              </button>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Full Name *
                </label>
                <input
                  type="text"
                  value={editFormData.name}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter full name"
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    backgroundColor: 'white',
                    color: '#111827'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Relationship *
                </label>
                <select
                  value={editFormData.relationship}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, relationship: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    backgroundColor: 'white',
                    color: '#111827'
                  }}
                >
                  <option value="Spouse">Spouse</option>
                  <option value="Domestic Partner">Domestic Partner</option>
                  <option value="Child">Child</option>
                  <option value="Stepchild">Stepchild</option>
                  <option value="Adopted Child">Adopted Child</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Date of Birth
                </label>
                <input
                  type="date"
                  value={editFormData.dateOfBirth}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    backgroundColor: 'white',
                    color: '#111827'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Gender
                </label>
                <select
                  value={editFormData.gender}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, gender: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    backgroundColor: 'white',
                    color: '#111827'
                  }}
                >
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                  <option value="Prefer not to say">Prefer not to say</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Social Security Number (Optional)
                </label>
                <input
                  type="text"
                  value={editFormData.ssn}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, ssn: e.target.value }))}
                  placeholder="XXX-XX-XXXX"
                  maxLength={11}
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    backgroundColor: 'white',
                    color: '#111827'
                  }}
                />
              </div>
            </div>

            <div style={{ display: 'flex', gap: '12px', marginTop: '24px', justifyContent: 'flex-end' }}>
              <button
                onClick={() => {
                  setShowEditPopup(false);
                  setEditingDependent(null);
                }}
                style={{
                  padding: '10px 20px',
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  cursor: 'pointer'
                }}
                disabled={loading}
              >
                Cancel
              </button>
              <button
                onClick={handleUpdateDependent}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#111827',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'white',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  opacity: loading ? 0.6 : 1
                }}
                disabled={loading}
              >
                {loading ? 'Updating...' : 'Update Dependent'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Edit Profile Modal */}
      {showProfileModal && (
        <EnhancedEditProfileDialog
          open={showProfileModal}
          onClose={async () => {
            setShowProfileModal(false);
            // Refresh user details after profile update
            await fetchUserDetails();
            // Also refresh parent component's user details
            if (onProfileUpdate) {
              onProfileUpdate();
            }
          }}
        />
      )}

      {/* Custom Modal */}
      <CustomModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onConfirm={modalConfig.onConfirm}
        title={modalConfig.title}
        message={modalConfig.message}
        type={modalConfig.type}
        confirmText={modalConfig.confirmText}
        cancelText={modalConfig.cancelText}
      />
    </div>
  );
};

export default DependentsConfirmationPage;
