1. Overview

Plan Assignments are how benefit plans (templates) are assigned to specific companies by brokers. This involves:
- Viewing existing assignments
- Editing past assignments
- Creating new plans or assigning existing ones
- Defining coverage tiers and dates (enrollment/coverage)
- Finalizing and saving assignments to the database


## 🧭 2. Navigation Flow

1. Go to AI Enroller > Manage Groups > Existing Groups
2. Locate the company (companies are grouped under brokers via `brokerId`)
3. Click **Manage Plans** to open the Plan Assignment UI

---

## 🧩 3. Plan vs Plan Assignment

| Plan (Template) | Plan Assignment (Company-Specific) |
|------------------|--------------------------------------|
| Static blueprint | Instance assigned to a company |
| Contains: name, coverage type, carrier, tier, metadata | Contains: planId, companyId, time constraints, coverage tiers, rates |
| No dates | Has planEffectiveDate, planEndDate, enrollmentStartDate, enrollmentEndDate |

---

## 🔄 4. Plan Assignment Workflow

### 📥 View Existing Assignments
- Load assignments from API:  
   "GET /api/pre-enrollment/plan-assignments/:id/activat"
- Display plan name, coverage type, carrier, and associated dates.
based on coverage subtypes as we have the structure in frontend.

### ✏️ Edit Existing Assignment
- Clicking **Edit** on an old assignment loads editable coverage tiers.
- After saving changes:
  - A **new PlanAssignment record** is created with a new ID (duplicated with changes).
  - Set status as `"Draft"` or `"Pending"` (not finalized yet).
  -repeat this with all the plans that you need for this term.
  - Proceed to the **Date Selection Step** to activate assignment and add all the remaining dates.

5. Add or Create Plan

### Option A: Assign Existing Plan
- Click **Add Plan** → Modal with all available plans:
  - Load via: `GET /plans/assignable`
- On selection:
  - Create PlanAssignment using backend API.
  - Auto-link with selected company.

### Option B: Create New Plan
- In same modal, click **Create New Plan**
  - Submit plan details to `POST /plans/`
  - Then, assign it by calling `POST /plan-assignments/` with planId + companyId and leave the coverage tiers or and the dates blank if possible or add these coverage tiers according to last plan and the global dates for this year(upcoming)

---

## 🧮 6. Configure Coverage Tiers

After selecting/creating plans:
- Click **Edit** to configure:
  - Coverage Tiers: e.g., Employee Only, Family
  - Contribution Splits (employer vs employee)
  - Rate Structure (Age-Banded, Composite, etc.)

- Save: Creates or updates draft PlanAssignment record in DB (without dates yet)

---

## 📅 7. Final Step – Set Enrollment & Coverage Dates

Once all selected plans are assigned and tier-configured:
- Proceed to **Select Dates** UI
- Collect:
  - `planEffectiveDate`
  - `planEndDate`
  - `enrollmentStartDate`
  - `enrollmentEndDate`

- Finalize by calling:  
  `PUT /plan-assignments/:assignmentId/time-constraints`

> This updates all draft assignments to active ones with finalized dates.

---

## ✅ 8. Backend API References

### 📋 Plan Assignment APIs
   // Core CRUD operations (Section 3.5)
    this.router.post('/api/pre-enrollment/plan-assignments', this.createPlanAssignment);
    this.router.get('/api/pre-enrollment/plan-assignments/company/:companyId', this.getPlanAssignmentsByCompany);
    this.router.get('/api/pre-enrollment/plan-assignments/:id', this.getPlanAssignmentById);
    this.router.put('/api/pre-enrollment/plan-assignments/:id', this.updatePlanAssignment);
    this.router.post('/api/pre-enrollment/plan-assignments/:id/activate', this.activatePlanAssignment);
    this.router.post('/api/pre-enrollment/plan-assignments/:id/deactivate', this.deactivatePlanAssignment);
    this.router.post('/api/pre-enrollment/plan-assignments/:id/clone', this.clonePlanAssignment);
    this.router.delete('/api/pre-enrollment/plan-assignments/:id', this.deletePlanAssignment);

    // Validation endpoints (Section 3.6)
    this.router.get('/api/pre-enrollment/plan-assignments/:id/can-edit', this.canEditPlanAssignment);
    this.router.get('/api/pre-enrollment/plan-assignments/:id/can-delete', this.canDeletePlanAssignment);
    this.router.get('/api/pre-enrollment/plan-assignments/:id/enrollment-references', this.getEnrollmentReferences);
    this.router.post('/api/pre-enrollment/plan-assignments/check-expired', this.checkExpiredAssignments);

### 📄 Plan APIs
- `GET /plans/assignable` – Fetch available plans
- `POST /plans/` – Create new plan
- `GET /plans/:planId/dependent-assignments` – Validate before edit

---

9. Important Considerations

- Editing old assignments = new draft copy**
- Only finalized when dates are assigned**
- Only “Active” plans can be assigned**
- Coverage tiers are company-specific, not inherited from 

Plan
- Contribution validation needed on frontend**

check qharmony-backend-v1\src\controllers\planAssignment.controller.ts for api details everything is present there in details 