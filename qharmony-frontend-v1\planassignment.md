# ✅ Plan Assignment API Refactor — Frontend Integration Guide

## 🗂️ File Context
**Backend API Source File:**
`qharmony-backend-v1/src/controllers/planAssignment.controller.ts`

These updates improve backend responses (e.g., more embedded data) to reduce redundant frontend API calls.

---

## 🎯 GOAL

Refactor frontend page:
http://localhost:3000/ai-enroller/manage-groups/company/[companyId]/plans


To properly show plan assignments for a company using **updated backend APIs**.

---

## 🔄 Required Frontend Changes

### 1. 🧠 Identify Current API Usage

In the file responsible  `PlanAssignmentApi.ts`):

- Locate the call to fetch plan assignments.
- **Check if it's using**:
```ts
GET /api/pre-enrollment/plan-assignments?companyId=<id>
If so — replace it with:

GET /api/pre-enrollment/plan-assignments/company/:companyId
simialrly check all the apis and cross check with backend controller

 Display plan name(as plan assignment now gives plan data in it), effective dates, tiers, and company name from the enriched API response.

✅ Move company name above plan list (currently missing).
✅ Ensure the new data shape from backend is mapped properly in the frontend models/types.