/**
 * Signature API Service
 * Handles communication with backend signature endpoints
 */

export interface SignatureApiResponse {
  success: boolean;
  message?: string;
  signatureId?: string;
  error?: string;
}

/**
 * Get API base URL from environment
 */
const getApiBaseUrl = (): string => {
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
};

/**
 * Get user ID from localStorage
 */
const getUserId = (): string => {
  return localStorage.getItem('userid1') || localStorage.getItem('userId') || '';
};

/**
 * Get request headers for API calls
 */
const getHeaders = (): Record<string, string> => {
  return {
    'Content-Type': 'application/json',
    'user-id': getUserId(),
  };
};

/**
 * Save signature to database using your existing API endpoint
 * POST /admin/update/signature
 */
export const saveSignatureToDatabase = async (
  encryptedSignature: string
): Promise<SignatureApiResponse> => {
  try {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    if (!userId) {
      throw new Error('User ID not found. Please log in again.');
    }

    console.log('🚀 Saving signature to database via API:', {
      endpoint: `${API_BASE_URL}/admin/update/signature`,
      userId: userId,
      signatureLength: encryptedSignature.length
    });

    const response = await fetch(`${API_BASE_URL}/admin/update/signature`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({
        signatureData: encryptedSignature
      }),
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Signature saved to database successfully:', result);
      
      return {
        success: true,
        message: result.message || 'Signature saved successfully',
        signatureId: result.signatureId
      };
    } else {
      const errorText = await response.text();
      console.error('❌ Failed to save signature to database:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });

      return {
        success: false,
        error: `Failed to save signature: ${errorText}`
      };
    }
  } catch (error) {
    console.error('❌ Signature API error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error occurred'
    };
  }
};

/**
 * Note: Backend only has POST /admin/update/signature endpoint
 * No GET endpoints available for signature retrieval
 * Signatures are stored in user.details.enrollmentSignature
 */

/**
 * Check if user has a signature in database
 * Since no GET endpoint exists, we'll rely on localStorage for now
 */
export const checkSignatureExists = async (): Promise<boolean> => {
  // Since backend doesn't have GET endpoint, check localStorage
  // In a real implementation, you might want to add a GET endpoint
  // or check signature existence through user profile endpoint
  console.log('ℹ️ No GET endpoint available - checking localStorage only');

  try {
    const localSignature = localStorage.getItem('enrollmentSignature');
    return !!localSignature;
  } catch (error) {
    console.error('❌ Error checking signature existence:', error);
    return false;
  }
};

/**
 * Validate signature data before sending to API
 */
export const validateSignatureData = (signatureData: string): {
  isValid: boolean;
  error?: string;
} => {
  if (!signatureData || signatureData.trim() === '') {
    return {
      isValid: false,
      error: 'Signature data is required'
    };
  }

  // Check if it's a valid base64 string
  try {
    atob(signatureData);
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid signature data format'
    };
  }

  // Check size (optional - adjust limit as needed)
  const sizeInBytes = signatureData.length;
  const maxSizeInBytes = 5 * 1024 * 1024; // 5MB limit

  if (sizeInBytes > maxSizeInBytes) {
    return {
      isValid: false,
      error: 'Signature data too large'
    };
  }

  return { isValid: true };
};

/**
 * Save signature with validation and error handling
 */
export const saveSignatureWithValidation = async (
  encryptedSignature: string
): Promise<SignatureApiResponse> => {
  // Validate signature data first
  const validation = validateSignatureData(encryptedSignature);
  if (!validation.isValid) {
    return {
      success: false,
      error: validation.error
    };
  }

  // Save to database
  return await saveSignatureToDatabase(encryptedSignature);
};

/**
 * Retry mechanism for signature saving
 */
export const saveSignatureWithRetry = async (
  encryptedSignature: string,
  maxRetries: number = 3
): Promise<SignatureApiResponse> => {
  let lastError: string = '';

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(`🔄 Signature save attempt ${attempt}/${maxRetries}`);
    
    const result = await saveSignatureWithValidation(encryptedSignature);
    
    if (result.success) {
      console.log(`✅ Signature saved successfully on attempt ${attempt}`);
      return result;
    }
    
    lastError = result.error || 'Unknown error';
    console.warn(`⚠️ Attempt ${attempt} failed:`, lastError);
    
    // Wait before retry (exponential backoff)
    if (attempt < maxRetries) {
      const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return {
    success: false,
    error: `Failed after ${maxRetries} attempts. Last error: ${lastError}`
  };
};
