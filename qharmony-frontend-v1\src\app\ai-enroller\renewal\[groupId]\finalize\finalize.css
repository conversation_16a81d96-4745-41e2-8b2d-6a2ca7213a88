/* Finalize Styles */
.finalize-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
  overflow: hidden;
}

.finalize-header {
  padding: 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.finalize-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.finalize-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.finalize-header p {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Finalize Content */
.finalize-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.summary-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-icon {
  background: #3b82f6;
  color: white;
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.card-content .card-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.card-content .card-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Finalize Steps */
.finalize-steps h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.finalize-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.step-indicator {
  flex-shrink: 0;
}

.step-circle {
  width: 2rem;
  height: 2rem;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
}

.finalize-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.finalize-step .step-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Ready Notice */
.ready-notice {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #dbeafe;
  border: 1px solid #3b82f6;
  border-radius: 0.75rem;
  color: #1e40af;
}

.ready-notice h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.ready-notice p {
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.5;
}

/* Navigation */
.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-top: 1px solid #f1f5f9;
  background: #f8fafc;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.nav-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.nav-btn.secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.finalize-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  background: #059669;
  color: white;
}

.finalize-btn.enabled:hover {
  background: #047857;
  transform: translateY(-1px);
}

.finalize-btn.processing {
  background: #6b7280;
  cursor: not-allowed;
}

/* Spinner */
.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Step Progress Updates */
.renewal-step.completed .step-number {
  background: #059669;
  color: white;
}

.renewal-step.completed .step-title {
  color: #059669;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .finalize-content {
    padding: 1rem;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .finalize-step {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }
  
  .navigation-section {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-btn,
  .finalize-btn {
    width: 100%;
    justify-content: center;
  }
  
  .ready-notice {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
