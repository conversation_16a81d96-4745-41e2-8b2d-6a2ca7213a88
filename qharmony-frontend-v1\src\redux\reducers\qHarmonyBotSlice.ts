import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface ChatMessage {
  sender: "user" | "bot";
  message: string;
  timestamp: string;
}

interface QHarmonyBotState {
  chatHistory: ChatMessage[];
  isLoading: boolean;
}

const initialState: QHarmonyBotState = {
  chatHistory: [],
  isLoading: false,
};

export const qHarmonyBotSlice = createSlice({
  name: "qHarmonyBot",
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<ChatMessage>) => {
      const { sender, message, timestamp } = action.payload;

      console.log("Adding message:", action.payload);

      if (sender === "bot" && state.chatHistory.length > 0) {
        const lastMessage = state.chatHistory[state.chatHistory.length - 1];
        if (
          lastMessage.sender === "bot" &&
          !lastMessage.timestamp.includes("Done")
        ) {
          // Update last bot message
          lastMessage.message += message;
          lastMessage.timestamp = timestamp;
          return;
        }
      }
      // Otherwise, add as a new message
      state.chatHistory.push(action.payload);
    },
    clearChatHistory: (state) => {
      state.chatHistory = [];
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

export const { addMessage, clearChatHistory, setIsLoading } =
  qHarmonyBotSlice.actions;

export default qHarmonyBotSlice.reducer;
