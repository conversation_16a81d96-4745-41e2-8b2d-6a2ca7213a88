
import pandas as pd
import numpy as np
from scipy.stats import norm
import shap
import pickle
import joblib
import xgboost as xgb
import os
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Union, Any, Tuple
from langchain_openai.chat_models import ChatOpenAI
import json
from .recommendationPrompts import wellness_recommendation_prompt, wellness_docs_fetcher_prompt
from envyaml import EnvYAML
import logging

def ai_recommendations(model: ChatOpenAI, questions, user_response, predictions, retriever, document_ids, company_id, express_uri):
    sources = []
    base_uri=express_uri
    
    
    retriever_query = wellness_docs_fetcher_prompt.format(questions=questions, response=user_response, predictions=predictions)
    if retriever_query:
        query = model.invoke(retriever_query).content
    logging.info(f"------------------------{query}---------------------------")
    if query:
        docs = retriever.invoke(query)
        
    for doc_id in document_ids:
        if doc_id in str(docs):
            print(len(docs))
            sources.append(f"{base_uri}/benefits/document?objectKey={doc_id}&companyId={company_id}")
    
        # https://api.benosphere.com/benefits/document?objectKey=67bed0f250bad0a4b3d7fea0-1740558960738-39cdf344-1efc-4cb7-acc7-0d9883b2e654_____Dental_Benefits_An_Introduction_FINAL.pdf&companyId=67be8cda50bad0a4b3d7f802
    # logging.info(f"{docs}_{retriever}")
    
    input = wellness_recommendation_prompt.format(questions=questions, response=user_response, predictions=predictions, documents=docs )
    recommendations=model.invoke(input).content
    recommendations = recommendations.split("|||")
    return recommendations, sources