'use client';

import React, { useState, useEffect } from 'react';
import {
  HiOutlineArrowLeft,
  HiOutlineSparkles,
  HiOutlineDocumentText,
  HiOutlineCheckCircle,
  HiOutlinePlus,
  HiOutlineX,
  HiOutlineCloudUpload
} from 'react-icons/hi';
import {
  RiHealthBookLine,
  RiFileTextLine,
  RiCheckboxCircleLine
} from 'react-icons/ri';

import {
  getConstantsData,
  getCarriers,
  createPlan,
  uploadPlanDocuments,
  checkPlanNameDuplicate,
  checkPlanCodeDuplicate,
  type PlanData,
  type CarrierData
} from '../../../../../create-plan/services/planApi';

// Import the create-plan CSS for tooltip styles
import '../../../../../create-plan/create-plan.css';

interface Plan {
  _id: string;
  planName: string;
  planCode: string;
  coverageType: string;
  coverageSubTypes: string[];
  planType: string;
  metalTier: string;
  description?: string;
  highlights?: string[];
  carrier: string;
  informativeLinks?: string[];
}

interface CreatePlanFormProps {
  onCancel: () => void;
  onSubmit: (plan: Plan) => void;
  initialData?: Plan | null;
  isModal?: boolean;
}

interface DataStructure {
  plans: any[];
  templates: any[];
  carriers: CarrierData[];
  planTypes: string[];
  coverageCategories: string[];
  coverageMap: { [key: string]: string[] };
  metalTiers: string[];
}

const CreatePlanForm: React.FC<CreatePlanFormProps> = ({ onCancel, onSubmit, initialData, isModal = false }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showAiAssist, setShowAiAssist] = useState(false);
  const [data, setData] = useState<DataStructure | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Duplicate checking state
  const [planNameStatus, setPlanNameStatus] = useState({
    isChecking: false,
    isDuplicate: false,
    error: null as string | null,
    existingPlan: null as any
  });

  const [planCodeStatus, setPlanCodeStatus] = useState({
    isChecking: false,
    isDuplicate: false,
    error: null as string | null,
    existingPlan: null as any
  });

  const [formData, setFormData] = useState({
    // Step 1: Documents
    documents: [] as File[],

    // Step 2: Basic Details
    coverageCategory: '', // Main category (e.g., "Health Insurance")
    coverageType: '', // Subtype (e.g., "Medical")
    carrier: '', // Will be set when carriers load
    planName: initialData?.planName || '',
    planCode: initialData?.planCode || '',
    planType: initialData?.planType || '',
    metalTier: initialData?.metalTier || '',

    // Step 3: Description & Video
    videoUrl: '',
    description: initialData?.description || '',
    highlights: initialData?.highlights || ['']
  });

  const steps = [
    { number: 1, title: 'Documents', active: currentStep === 1, completed: currentStep > 1 },
    { number: 2, title: 'Basic Details', active: currentStep === 2, completed: currentStep > 2 },
    { number: 3, title: 'Description', active: currentStep === 3, completed: currentStep > 3 },
    { number: 4, title: 'Preview', active: currentStep === 4, completed: currentStep > 4 }
  ];

  useEffect(() => {
    // Load data from backend APIs and hardcoded constants
    const loadData = async () => {
      try {
        // Get constants data
        const constantsResult = getConstantsData();

        // Get carriers from database API
        const carriersResult = await getCarriers();

        if (constantsResult.success && carriersResult.success) {
          const backendData: DataStructure = {
            plans: [],
            templates: [],
            carriers: carriersResult.data || [],
            planTypes: constantsResult.data?.planTypes || [],
            coverageCategories: constantsResult.data?.coverageCategories || [],
            coverageMap: constantsResult.data?.coverageMap || {},
            metalTiers: constantsResult.data?.metalTiers || []
          };
          setData(backendData);
        } else {
          console.error('Failed to load data:', {
            constants: constantsResult.success,
            carriers: carriersResult.success,
            carriersError: carriersResult.error
          });
          // Set constants data even if carriers fail
          const fallbackData: DataStructure = {
            plans: [],
            templates: [],
            carriers: [],
            planTypes: constantsResult.data?.planTypes || [],
            coverageCategories: constantsResult.data?.coverageCategories || [],
            coverageMap: constantsResult.data?.coverageMap || {},
            metalTiers: constantsResult.data?.metalTiers || []
          };
          setData(fallbackData);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        // Set empty data structure if error occurs
        setData({
          plans: [],
          templates: [],
          carriers: [],
          planTypes: [],
          coverageCategories: [],
          coverageMap: {},
          metalTiers: []
        });
      }
    };
    loadData();
  }, []);

  // Debounced plan name duplicate check
  const debouncedNameCheck = React.useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (planName: string) => {
      clearTimeout(timeoutId);

      if (!planName.trim()) {
        setPlanNameStatus({ isChecking: false, isDuplicate: false, error: null, existingPlan: null });
        return;
      }

      setPlanNameStatus(prev => ({ ...prev, isChecking: true, error: null }));

      timeoutId = setTimeout(async () => {
        try {
          // Pass the current plan ID to exclude it from duplicate check when editing
          const result = await checkPlanNameDuplicate(planName, initialData?._id);
          if (result.success && result.data) {
            setPlanNameStatus({
              isChecking: false,
              isDuplicate: result.data.isDuplicate,
              error: null,
              existingPlan: result.data.existingPlan || null
            });
          } else {
            setPlanNameStatus({
              isChecking: false,
              isDuplicate: false,
              error: result.error || 'Failed to check for duplicates',
              existingPlan: null
            });
          }
        } catch (error) {
          setPlanNameStatus({
            isChecking: false,
            isDuplicate: false,
            error: 'Network error while checking for duplicates',
            existingPlan: null
          });
        }
      }, 800);
    };
  }, [initialData?._id]);

  // Debounced plan code duplicate check
  const debouncedCodeCheck = React.useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (planCode: string) => {
      clearTimeout(timeoutId);

      if (!planCode.trim()) {
        setPlanCodeStatus({ isChecking: false, isDuplicate: false, error: null, existingPlan: null });
        return;
      }

      setPlanCodeStatus(prev => ({ ...prev, isChecking: true, error: null }));

      timeoutId = setTimeout(async () => {
        try {
          // Pass the current plan ID to exclude it from duplicate check when editing
          const result = await checkPlanCodeDuplicate(planCode, initialData?._id);
          if (result.success && result.data) {
            setPlanCodeStatus({
              isChecking: false,
              isDuplicate: result.data.isDuplicate,
              error: null,
              existingPlan: result.data.existingPlan || null
            });
          } else {
            setPlanCodeStatus({
              isChecking: false,
              isDuplicate: false,
              error: result.error || 'Failed to check for duplicates',
              existingPlan: null
            });
          }
        } catch (error) {
          setPlanCodeStatus({
            isChecking: false,
            isDuplicate: false,
            error: 'Network error while checking for duplicates',
            existingPlan: null
          });
        }
      }, 800);
    };
  }, [initialData?._id]);

  // Check for duplicates when form data changes (for any source - AI Assist, manual input)
  useEffect(() => {
    if (formData.planName && formData.planName.trim()) {
      debouncedNameCheck(formData.planName);
    }
  }, [formData.planName, debouncedNameCheck]);

  useEffect(() => {
    if (formData.planCode && formData.planCode.trim()) {
      debouncedCodeCheck(formData.planCode);
    }
  }, [formData.planCode, debouncedCodeCheck]);

  // JavaScript-based tooltip system
  useEffect(() => {
    let activeTooltip: HTMLElement | null = null;

    const showTooltip = (icon: Element, text: string) => {
      // Remove any existing tooltip
      hideTooltip();

      const rect = icon.getBoundingClientRect();
      const tooltipWidth = 280;
      const tooltipHeight = 120; // Approximate

      // Calculate horizontal position (centered on icon, but keep within viewport)
      let leftPosition = rect.left + (rect.width / 2) - (tooltipWidth / 2);
      if (leftPosition < 10) leftPosition = 10;
      if (leftPosition + tooltipWidth > window.innerWidth - 10) {
        leftPosition = window.innerWidth - tooltipWidth - 10;
      }

      // Determine if tooltip should show above or below
      const spaceAbove = rect.top;
      const spaceBelow = window.innerHeight - rect.bottom;
      const showBelow = spaceAbove < tooltipHeight && spaceBelow > tooltipHeight;

      // Create tooltip element
      const tooltip = document.createElement('div');
      tooltip.className = 'custom-tooltip';
      tooltip.textContent = text;
      tooltip.style.cssText = `
        position: fixed;
        ${showBelow ? `top: ${rect.bottom + 4}px;` : `bottom: ${window.innerHeight - rect.top + 4}px;`}
        left: ${leftPosition}px;
        width: 280px;
        background: #1f2937;
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.8rem;
        font-weight: 400;
        line-height: 1.4;
        text-align: left;
        white-space: normal;
        word-wrap: break-word;
        hyphens: auto;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        z-index: 99999;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      `;

      // Create arrow
      const arrow = document.createElement('div');
      arrow.className = 'custom-tooltip-arrow';
      arrow.style.cssText = `
        position: fixed;
        ${showBelow ? `top: ${rect.bottom - 2}px;` : `bottom: ${window.innerHeight - rect.top - 2}px;`}
        left: ${rect.left + rect.width / 2 - 6}px;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        ${showBelow ? 'border-bottom: 6px solid #1f2937;' : 'border-top: 6px solid #1f2937;'}
        z-index: 100000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      `;

      document.body.appendChild(tooltip);
      document.body.appendChild(arrow);

      // Fade in
      requestAnimationFrame(() => {
        tooltip.style.opacity = '1';
        arrow.style.opacity = '1';
      });

      activeTooltip = tooltip;
      (activeTooltip as any).arrow = arrow;
    };

    const hideTooltip = () => {
      if (activeTooltip) {
        activeTooltip.remove();
        if ((activeTooltip as any).arrow) {
          (activeTooltip as any).arrow.remove();
        }
        activeTooltip = null;
      }
    };

    const handleMouseEnter = (e: Event) => {
      const icon = e.target as Element;
      const tooltipText = icon.getAttribute('data-tooltip');
      if (tooltipText) {
        showTooltip(icon, tooltipText);
      }
    };

    const handleMouseLeave = () => {
      hideTooltip();
    };

    // Add event listeners to all tooltip icons
    const tooltipIcons = document.querySelectorAll('.tooltip-icon[data-tooltip]');
    tooltipIcons.forEach(icon => {
      icon.addEventListener('mouseenter', handleMouseEnter);
      icon.addEventListener('mouseleave', handleMouseLeave);
    });

    // Cleanup function
    return () => {
      hideTooltip();
      tooltipIcons.forEach(icon => {
        icon.removeEventListener('mouseenter', handleMouseEnter);
        icon.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, [currentStep]);

  // Effect to populate form data when editing and data is loaded
  useEffect(() => {
    if (initialData && data) {
      console.log('🔄 Prepopulating form with initialData:', initialData);
      console.log('🔄 Available data:', data);

      // Find the carrier ID - try to match by name or use carrier field directly
      let carrierId = '';
      if (initialData.carrier) {
        // Try to find carrier by ID first, then by name
        const carrierById = data.carriers.find(c => c._id === initialData.carrier);
        const carrierByName = data.carriers.find(c =>
          c.carrierName === initialData.carrier ||
          c.displayName === initialData.carrier
        );
        carrierId = carrierById?._id || carrierByName?._id || '';
      }

      // Find the coverage category and subtype from the initial coverage type
      let coverageCategory = '';
      let coverageSubType = '';

      if (data.coverageMap && initialData.coverageType) {
        // Look for the coverage type in the map
        for (const [category, subTypes] of Object.entries(data.coverageMap)) {
          if (subTypes.includes(initialData.coverageType)) {
            coverageCategory = category;
            coverageSubType = initialData.coverageType;
            break;
          }
        }
      }

      // If coverageSubTypes array exists, use the first one as coverageType
      if (!coverageSubType && initialData.coverageSubTypes && initialData.coverageSubTypes.length > 0) {
        coverageSubType = initialData.coverageSubTypes[0];
        // Find the category for this subtype
        for (const [category, subTypes] of Object.entries(data.coverageMap || {})) {
          if (subTypes.includes(coverageSubType)) {
            coverageCategory = category;
            break;
          }
        }
      }

      console.log('🔄 Prepopulation results:', {
        planName: initialData.planName,
        planCode: initialData.planCode,
        carrierId,
        coverageCategory,
        coverageSubType,
        planType: initialData.planType,
        metalTier: initialData.metalTier
      });

      setFormData(prev => ({
        ...prev,
        planName: initialData.planName || '',
        planCode: initialData.planCode || '',
        carrier: carrierId,
        coverageCategory: coverageCategory,
        coverageType: coverageSubType,
        planType: initialData.planType || '',
        metalTier: initialData.metalTier || '',
        description: initialData.description || '',
        highlights: initialData.highlights || [''],
        videoUrl: initialData.informativeLinks?.[0] || ''
      }));
    }
  }, [initialData, data]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        documents: [...prev.documents, ...fileArray]
      }));
    }
  };

  const removeDocument = (index: number) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.filter((_, i) => i !== index)
    }));
  };

  const handleAiAssist = () => {
    setShowAiAssist(true);

    setTimeout(() => {
      // Generate AI-suggested data based on available options
      const aiData = {
        planName: 'AI-Suggested Health Plan',
        planCode: 'AI-PLAN-2025',
        carrier: data?.carriers?.[0]?._id || '',
        planType: data?.planTypes?.[0] || 'PPO',
        coverageCategory: 'Health Insurance',
        coverageType: 'Medical',
        metalTier: 'Silver',
        videoUrl: '',
        documents: [],
        description: 'Comprehensive health coverage with excellent benefits and nationwide network access.',
        highlights: ['Low deductible', 'Nationwide network', 'Preventive care covered 100%']
      };

      setFormData(aiData);
      setShowAiAssist(false);
    }, 1500);
  };

  const handleContinue = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCreatePlan = async () => {
    setIsSubmitting(true);
    try {
      // Use the selected coverage category as the main coverage type
      const coverageType = formData.coverageCategory;

      // Create or update the plan using backend API
      const planData: PlanData = {
        planName: formData.planName,
        planCode: formData.planCode,
        carrier: formData.carrier,
        coverageType: coverageType,
        coverageSubTypes: [formData.coverageType],
        planType: formData.planType,
        metalTier: formData.metalTier,
        description: formData.description,
        highlights: formData.highlights.filter(h => h.trim() !== ''),
        informativeLinks: formData.videoUrl ? [formData.videoUrl] : [],
        carrierId: formData.carrier,
        isTemplate: false,
        status: 'Active'  // Explicitly set status to Active for company assignment
      };

      let result;
      if (initialData) {
        // Update existing plan
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}/api/pre-enrollment/plans/${initialData._id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'user-id': localStorage.getItem('userId') || 'default-user'
          },
          body: JSON.stringify(planData)
        });

        if (response.ok) {
          const data = await response.json();
          result = { success: true, data: { plan: data } };
        } else {
          const errorData = await response.json();
          result = { success: false, error: errorData.error || 'Failed to update plan' };
        }
      } else {
        // Create new plan
        result = await createPlan(planData);
      }

      if (result.success && result.data) {
        console.log(`Plan ${initialData ? 'updated' : 'created'} successfully:`, result.data);

        // Upload documents if any (only for new plans)
        if (!initialData && formData.documents.length > 0) {
          const uploadResult = await uploadPlanDocuments(result.data.plan._id, formData.documents);
          if (!uploadResult.success) {
            console.warn('Failed to upload some documents:', uploadResult.error);
          }
        }

        // Create a plan object for the parent component
        const plan: Plan = {
          _id: result.data.plan._id,
          planName: result.data.plan.planName,
          planCode: result.data.plan.planCode,
          coverageType: result.data.plan.coverageType,
          coverageSubTypes: result.data.plan.coverageSubTypes || [],
          planType: result.data.plan.planType,
          metalTier: result.data.plan.metalTier || formData.metalTier || 'Bronze',
          description: result.data.plan.description,
          highlights: result.data.plan.highlights,
          carrier: result.data.plan.carrier || formData.carrier
        };

        onSubmit(plan);
      } else {
        throw new Error(result.error || `Failed to ${initialData ? 'update' : 'create'} plan`);
      }
    } catch (error) {
      console.error(`Error ${initialData ? 'updating' : 'creating'} plan:`, error);
      alert(`Error ${initialData ? 'updating' : 'creating'} plan: ${error instanceof Error ? error.message : 'Please try again.'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addHighlight = () => {
    setFormData(prev => ({
      ...prev,
      highlights: [...prev.highlights, '']
    }));
  };

  const updateHighlight = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      highlights: prev.highlights.map((h, i) => i === index ? value : h)
    }));
  };

  const removeHighlight = (index: number) => {
    if (formData.highlights.length > 1) {
      setFormData(prev => ({
        ...prev,
        highlights: prev.highlights.filter((_, i) => i !== index)
      }));
    }
  };

  // Validation functions
  const isStep1Valid = () => {
    return true; // Documents are optional
  };

  const isStep2Valid = () => {
    return formData.planName &&
           formData.planCode &&
           formData.carrier &&
           formData.planType &&
           formData.coverageCategory &&
           formData.coverageType &&
           !planNameStatus.isDuplicate &&
           !planNameStatus.isChecking &&
           !planCodeStatus.isDuplicate &&
           !planCodeStatus.isChecking;
  };

  const isStep3Valid = () => {
    return formData.description && formData.highlights.some(h => h.trim() !== '');
  };

  if (!data) {
    return <div className="p-6 text-center">Loading...</div>;
  }

  // Debug: Log data to console
  console.log('CreatePlanForm data:', data);
  console.log('Coverage categories:', data.coverageCategories);
  console.log('Coverage map:', data.coverageMap);

  const renderStep1 = () => (
    <div className="space-y-6">
      <style jsx>{`
        input, select, textarea {
          background-color: white !important;
          color: #374151 !important;
        }
        input::placeholder {
          color: #9ca3af !important;
        }
      `}</style>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <RiHealthBookLine size={20} className="text-purple-600" />
          <h3 style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px', color: '#374151' }}>Basic Details</h3>
        </div>
      </div>

      <div className="space-y-2">
        {/* Coverage Category */}
        <div>
          <label htmlFor="coverageCategory" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Coverage Category *
            <span
              className="tooltip-icon"
              data-tooltip="Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)"
            ></span>
          </label>
          <select
            id="coverageCategory"
            value={formData.coverageCategory}
            onChange={(e) => {
              handleInputChange('coverageCategory', e.target.value);
              // Reset coverage type when category changes
              handleInputChange('coverageType', '');
            }}
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
            style={{ backgroundColor: 'white', color: '#374151' }}
          >
            <option value="">Select coverage category</option>
            {data?.coverageCategories?.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            )) || []}
          </select>
        </div>

        {/* Coverage Type */}
        <div>
          <label htmlFor="coverageType" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Coverage Type *
            <span
              className="tooltip-icon"
              data-tooltip="Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)"
            ></span>
          </label>
          <select
            id="coverageType"
            value={formData.coverageType}
            onChange={(e) => handleInputChange('coverageType', e.target.value)}
            disabled={!formData.coverageCategory}
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white disabled:bg-gray-100 disabled:cursor-not-allowed"
            style={{ backgroundColor: formData.coverageCategory ? 'white' : '#f9fafb', color: '#374151' }}
          >
            <option value="">Select coverage type</option>
            {formData.coverageCategory && data?.coverageMap?.[formData.coverageCategory]?.map(subType => (
              <option key={subType} value={subType}>
                {subType}
              </option>
            )) || []}
          </select>
        </div>

        {/* Carrier */}
        <div>
          <label htmlFor="carrier" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Carrier *
            <span
              className="tooltip-icon"
              data-tooltip="Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)"
            ></span>
          </label>
          <select
            id="carrier"
            value={formData.carrier}
            onChange={(e) => handleInputChange('carrier', e.target.value)}
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
          >
            <option value="">Select carrier</option>
            {data?.carriers?.map(carrier => (
              <option key={carrier._id} value={carrier._id}>
                {carrier.displayName || carrier.carrierName}
              </option>
            )) || []}
          </select>
        </div>

        {/* Plan Name */}
        <div>
          <label htmlFor="planName" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Plan Name *
            <span
              className="tooltip-icon"
              data-tooltip="Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically."
            ></span>
          </label>
          <div className="relative">
            <input
              type="text"
              id="planName"
              placeholder="e.g. Blue Shield PPO 500"
              value={formData.planName}
              onChange={(e) => handleInputChange('planName', e.target.value)}
              className={`w-full px-4 py-2.5 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white ${
                planNameStatus.isDuplicate ? 'border-red-500' :
                planNameStatus.isChecking ? 'border-yellow-500' :
                formData.planName && !planNameStatus.isDuplicate ? 'border-green-500' : 'border-gray-300'
              }`}
              style={{ backgroundColor: 'white', color: '#374151' }}
            />
            {/* Status indicator */}
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {planNameStatus.isChecking && (
                <div className="w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"></div>
              )}
              {!planNameStatus.isChecking && formData.planName && !planNameStatus.isDuplicate && (
                <span className="text-green-600 font-bold">✓</span>
              )}
              {planNameStatus.isDuplicate && (
                <span className="text-red-600 font-bold">✗</span>
              )}
            </div>
          </div>

          {/* Status messages */}
          {planNameStatus.isDuplicate && planNameStatus.existingPlan && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              <strong>Plan name already exists:</strong> &quot;{planNameStatus.existingPlan.planName}&quot;
              {planNameStatus.existingPlan.planCode && ` (${planNameStatus.existingPlan.planCode})`}
            </div>
          )}

          {planNameStatus.error && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700">
              <strong>Warning:</strong> {planNameStatus.error}
            </div>
          )}

          {formData.planName && !planNameStatus.isChecking && !planNameStatus.isDuplicate && !planNameStatus.error && (
            <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
              ✓ Plan name is available
            </div>
          )}
        </div>

        {/* Plan Code */}
        <div>
          <label htmlFor="planCode" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Plan Code *
            <span
              className="tooltip-icon"
              data-tooltip="Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically."
            ></span>
          </label>
          <div className="relative">
            <input
              type="text"
              id="planCode"
              placeholder="e.g. BS-PPO-500"
              value={formData.planCode}
              onChange={(e) => handleInputChange('planCode', e.target.value)}
              className={`w-full px-4 py-2.5 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white ${
                planCodeStatus.isDuplicate ? 'border-red-500' :
                planCodeStatus.isChecking ? 'border-yellow-500' :
                formData.planCode && !planCodeStatus.isDuplicate ? 'border-green-500' : 'border-gray-300'
              }`}
              style={{ backgroundColor: 'white', color: '#374151' }}
            />
            {/* Status indicator */}
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {planCodeStatus.isChecking && (
                <div className="w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"></div>
              )}
              {!planCodeStatus.isChecking && formData.planCode && !planCodeStatus.isDuplicate && (
                <span className="text-green-600 font-bold">✓</span>
              )}
              {planCodeStatus.isDuplicate && (
                <span className="text-red-600 font-bold">✗</span>
              )}
            </div>
          </div>

          {/* Status messages */}
          {planCodeStatus.isDuplicate && planCodeStatus.existingPlan && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              <strong>Plan code already exists:</strong> &quot;{planCodeStatus.existingPlan.planCode}&quot;
              {planCodeStatus.existingPlan.planName && ` (${planCodeStatus.existingPlan.planName})`}
            </div>
          )}

          {planCodeStatus.error && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700">
              <strong>Warning:</strong> {planCodeStatus.error}
            </div>
          )}

          {formData.planCode && !planCodeStatus.isChecking && !planCodeStatus.isDuplicate && !planCodeStatus.error && (
            <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
              ✓ Plan code is available
            </div>
          )}
        </div>

        {/* Plan Type */}
        <div>
          <label htmlFor="planType" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Plan Type *
            <span
              className="tooltip-icon"
              data-tooltip="Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)"
            ></span>
          </label>
          <select
            id="planType"
            value={formData.planType}
            onChange={(e) => handleInputChange('planType', e.target.value)}
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
          >
            <option value="">Select type</option>
            {data?.planTypes?.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            )) || []}
          </select>
        </div>

        {/* Metal Tier */}
        <div>
          <label htmlFor="metalTier" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Metal Tier (Optional)
            <span
              className="tooltip-icon"
              data-tooltip="Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans."
            ></span>
          </label>
          <select
            id="metalTier"
            value={formData.metalTier}
            onChange={(e) => handleInputChange('metalTier', e.target.value)}
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
          >
            <option value="">Select tier</option>
            {data?.metalTiers?.map(tier => (
              <option key={tier} value={tier}>
                {tier}
              </option>
            )) || []}
          </select>
        </div>
      </div>

      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          <HiOutlineArrowLeft size={16} />
          Back to Selection
        </button>

        <button
          type="button"
          onClick={handleContinue}
          disabled={!isStep2Valid()}
          className={`px-4 py-2 rounded-md ${
            isStep2Valid()
              ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
          style={{
            fontSize: '14px',
            lineHeight: '21px',
            fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
          }}
        >
          Continue to Basic Info
        </button>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <HiOutlineDocumentText size={20} className="text-purple-600" />
        <h3 className="text-lg font-semibold text-gray-900">Media & Documents</h3>
      </div>

      <div className="space-y-6">
        {/* Video URL */}
        <div>
          <label htmlFor="videoUrl" className="block text-sm font-medium text-gray-700 mb-1">
            Video URL (Optional)
            <span
              className="tooltip-icon"
              data-tooltip="Add a YouTube or Vimeo URL to help explain plan benefits and features"
            ></span>
          </label>
          <input
            type="url"
            id="videoUrl"
            placeholder="e.g. https://youtube.com/watch?v=..."
            value={formData.videoUrl}
            onChange={(e) => handleInputChange('videoUrl', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
          />
          <small className="text-gray-500 text-sm">Add a video to help explain plan benefits and features</small>
        </div>

        {/* Document Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Plan Documents (Optional)
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors">
            <input
              type="file"
              id="documents"
              multiple
              accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="hidden"
            />
            <label htmlFor="documents" className="cursor-pointer">
              <HiOutlinePlus size={24} className="mx-auto text-gray-400 mb-2" />
              <span className="text-gray-600">Click to upload documents</span>
              <p className="text-sm text-gray-500 mt-1">PDF, DOC, TXT, or Image files</p>
            </label>
          </div>

          {formData.documents.length > 0 && (
            <div className="mt-4 space-y-2">
              <h4 className="text-sm font-medium text-gray-700">Uploaded Documents:</h4>
              {formData.documents.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center gap-2">
                    <HiOutlineDocumentText size={16} className="text-gray-500" />
                    <span className="text-sm text-gray-700">{file.name}</span>
                    <span className="text-xs text-gray-500">({(file.size / 1024).toFixed(1)} KB)</span>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeDocument(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <HiOutlineX size={16} />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={handleBack}
          className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          <HiOutlineArrowLeft size={16} />
          Back
        </button>

        <button
          type="button"
          onClick={handleContinue}
          className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-md hover:opacity-90"
          style={{
            fontSize: '14px',
            lineHeight: '21px',
            fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
          }}
        >
          Continue to Description
        </button>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <RiFileTextLine size={20} className="text-purple-600" />
        <h3 style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px', color: '#374151' }}>Description & Video</h3>
      </div>

      <div className="space-y-6">
        {/* Video URL */}
        <div>
          <label htmlFor="videoUrl" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Video URL (Optional)
            <span
              className="tooltip-icon"
              data-tooltip="Add a YouTube or Vimeo URL to help explain plan benefits and features"
            ></span>
          </label>
          <div className="input-with-prefix">
            <span className="input-prefix">🎥</span>
            <input
              type="url"
              id="videoUrl"
              placeholder="https://youtube.com/watch?v=..."
              value={formData.videoUrl}
              onChange={(e) => handleInputChange('videoUrl', e.target.value)}
              style={{ fontSize: '14px', fontWeight: '400', lineHeight: '21px' }}
              className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
            />
          </div>
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Plan Description *
            <span
              className="tooltip-icon"
              data-tooltip="Provide a detailed description of the plan benefits, coverage, and key features"
            ></span>
          </label>
          <textarea
            id="description"
            placeholder="Describe the plan benefits and features..."
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
          />
          <small className="text-gray-500 text-sm">Describe the key benefits, coverage details, and what makes this plan unique</small>
        </div>

        {/* Highlights */}
        <div>
          <label style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-1">
            Plan Highlights *
            <span
              className="tooltip-icon"
              data-tooltip="Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)"
            ></span>
          </label>
          <small className="text-gray-500 text-sm mb-3 block">Add the most important features that make this plan attractive</small>

          <div className="space-y-3">
            {formData.highlights.map((highlight, index) => (
              <div key={index} className="flex items-center gap-2">
                <input
                  type="text"
                  placeholder="e.g. Low deductible, Nationwide network"
                  value={highlight}
                  onChange={(e) => updateHighlight(index, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
                />
                {formData.highlights.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeHighlight(index)}
                    className="text-red-500 hover:text-red-700 p-1"
                  >
                    <HiOutlineX size={16} />
                  </button>
                )}
              </div>
            ))}
          </div>

          <button
            type="button"
            onClick={addHighlight}
            className="mt-3 flex items-center gap-2 text-purple-600 hover:text-purple-800 text-sm font-medium"
          >
            <HiOutlinePlus size={16} />
            Add Highlight
          </button>
        </div>
      </div>

      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={handleBack}
          className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          <HiOutlineArrowLeft size={16} />
          Back
        </button>

        <button
          type="button"
          onClick={handleContinue}
          disabled={!isStep3Valid()}
          className={`px-4 py-2 rounded-md ${
            isStep3Valid()
              ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
          style={{
            fontSize: '14px',
            lineHeight: '21px',
            fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
          }}
        >
          Preview Plan
        </button>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="form-section">
      <div className="form-header">
        <div className="form-header-content">
          <RiCheckboxCircleLine size={20} />
          <h3 style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px', color: '#374151' }}>AI-Powered Plan Preview</h3>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          padding: '0.375rem 0.75rem',
          borderRadius: '0.5rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          background: '#dcfce7',
          color: '#166534'
        }}>
          <HiOutlineCheckCircle size={16} />
          Ready to Create
        </div>
      </div>

      <div className="review-content">
        <div className="review-section">
          <div className="review-section-header">
            <RiHealthBookLine size={18} />
            <h4>Plan Information</h4>
          </div>
          <div className="review-items">
            <div className="review-item">
              <span className="review-label">Plan Name:</span>
              <span className="review-value">{formData.planName}</span>
            </div>
            <div className="review-item">
              <span className="review-label">Plan Code:</span>
              <span className="review-value font-mono">{formData.planCode}</span>
            </div>
            <div className="review-item">
              <span className="review-label">Carrier:</span>
              <span className="review-value">
                {data?.carriers?.find(c => c._id === formData.carrier)?.displayName ||
                 data?.carriers?.find(c => c._id === formData.carrier)?.carrierName || 'Unknown'}
              </span>
            </div>
            <div className="review-item">
              <span className="review-label">Plan Type:</span>
              <span className="review-value">{formData.planType}</span>
            </div>
            <div className="review-item">
              <span className="review-label">Coverage Type:</span>
              <span className="review-value">{formData.coverageType}</span>
            </div>
            {formData.metalTier && (
              <div className="review-item">
                <span className="review-label">Metal Tier:</span>
                <span className="review-value">{formData.metalTier}</span>
              </div>
            )}
          </div>
        </div>

        {/* Media & Documents */}
        {(formData.videoUrl || formData.documents.length > 0) && (
          <div className="review-section">
            <div className="review-section-header">
              <HiOutlineDocumentText size={18} />
              <h4>Media & Documents</h4>
            </div>
            <div className="review-items">
              {formData.videoUrl && (
                <div className="review-item">
                  <span className="review-label">Video URL:</span>
                  <a
                    href={formData.videoUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="review-link"
                  >
                    {formData.videoUrl}
                  </a>
                </div>
              )}
              {formData.documents.length > 0 && (
                <div className="review-item full-width">
                  <span className="review-label">Documents:</span>
                  <div className="review-documents">
                    {formData.documents.map((doc, index) => (
                      <div key={index} className="review-document">
                        <HiOutlineDocumentText size={14} />
                        <span>{doc.name}</span>
                        <small>{(doc.size / 1024 / 1024).toFixed(2)} MB</small>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="review-section">
          <div className="review-section-header">
            <RiFileTextLine size={18} />
            <h4>Description & Highlights</h4>
          </div>
          <div className="review-items">
            <div className="review-item full-width">
              <span className="review-label">Description:</span>
              <p className="review-value">{formData.description}</p>
            </div>
            <div className="review-item full-width">
              <span className="review-label">Highlights:</span>
              <ul className="review-highlights">
                {formData.highlights.filter(h => h.trim()).map((highlight, index) => (
                  <li key={index} className="review-highlight">
                    <span className="highlight-bullet"></span>
                    {highlight}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={handleBack}
          className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          <HiOutlineArrowLeft size={16} />
          Back
        </button>

        <button
          type="button"
          onClick={handleCreatePlan}
          disabled={isSubmitting}
          className={`px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-md hover:opacity-90 ${
            isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
          }`}
          style={{
            fontSize: '14px',
            lineHeight: '21px',
            fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
          }}
        >
{isSubmitting ? (initialData ? 'Updating Plan...' : 'Creating Plan...') : (initialData ? 'Update Plan' : 'Create Plan')}
        </button>
      </div>
    </div>
  );

  // New step functions with correct flow
  const renderNewStep1 = () => (
    <div className="space-y-6">
      <style jsx>{`
        input, select, textarea {
          background-color: white !important;
          color: #374151 !important;
        }
        input::placeholder {
          color: #9ca3af !important;
        }
      `}</style>

      <div className="flex items-center gap-3 mb-6">
        <HiOutlineDocumentText size={20} className="text-purple-600" />
        <h3 style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px', color: '#374151' }}>Documents Upload</h3>
      </div>

      <div className="space-y-6">
        {/* File Upload Area */}
        <div>
          <label style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }} className="block text-gray-900 mb-2">
            Plan Documents (Optional)
            <span
              className="tooltip-icon"
              data-tooltip="Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)"
            ></span>
          </label>
          <div className="file-upload-area">
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="file-input"
            />
            <div className="file-upload-label">
              <HiOutlineCloudUpload size={48} className="text-gray-400" />
              <span style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }}>
                Click to upload or drag and drop
              </span>
              <small style={{ fontSize: '12px', fontWeight: '400', lineHeight: '18px' }} className="text-gray-500">
                PDF, DOC, TXT, or Image files (Max 10MB each)
              </small>
            </div>
          </div>
        </div>

        {/* Uploaded Files Display */}
        {formData.documents.length > 0 && (
          <div className="uploaded-files">
            <h4 style={{ fontSize: '14px', fontWeight: '500', lineHeight: '21px' }}>Uploaded Files</h4>
            {formData.documents.map((file, index) => (
              <div key={index} className="uploaded-file">
                <HiOutlineDocumentText size={16} className="text-gray-500" />
                <span className="file-name" style={{ fontSize: '14px', fontWeight: '400', lineHeight: '21px' }}>{file.name}</span>
                <span className="file-size" style={{ fontSize: '12px', fontWeight: '400', lineHeight: '18px' }}>
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </span>
                <button
                  onClick={() => removeDocument(index)}
                  className="remove-file"
                  title="Remove file"
                >
                  <HiOutlineX size={12} />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleContinue}
          className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-md hover:opacity-90"
          style={{
            fontSize: '14px',
            lineHeight: '21px',
            fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
          }}
        >
          Continue to Basic Info
        </button>
      </div>
    </div>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderNewStep1();
      case 2:
        return renderStep1(); // This becomes the Basic Details step
      case 3:
        return renderStep3(); // This will be updated for Description & Video
      case 4:
        return renderStep4();
      default:
        return renderNewStep1();
    }
  };

  const getStepIcon = (stepNumber: number) => {
    switch (stepNumber) {
      case 1: return <HiOutlineDocumentText />;
      case 2: return <RiHealthBookLine />;
      case 3: return <RiFileTextLine />;
      case 4: return <RiCheckboxCircleLine />;
      default: return <HiOutlineDocumentText />;
    }
  };

  return (
    <div className="bg-white space-y-6" style={{
      fontSize: '14px',
      lineHeight: '21px',
      color: '#374151',
      fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
    }}>
      <style jsx>{`
        .text-gray-900 {
          color: #374151 !important;
        }
        input, select, textarea {
          color: #374151 !important;
        }
        .file-upload-area {
          position: relative;
          border: 2px dashed #d1d5db;
          border-radius: 0.75rem;
          padding: 2rem;
          text-align: center;
          transition: all 0.2s ease;
          background: #fafafa;
        }
        .file-upload-area:hover {
          border-color: #8b5cf6;
          background: #f8fafc;
        }
        .file-input {
          position: absolute;
          inset: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          cursor: pointer;
        }
        .file-upload-label {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          pointer-events: none;
        }
        .uploaded-files {
          margin-top: 1rem;
        }
        .uploaded-file {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem;
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
          margin-bottom: 0.5rem;
        }
        .file-name {
          flex: 1;
        }
        .file-size {
          color: #6b7280;
        }
        .remove-file {
          padding: 0.25rem;
          background: #ef4444;
          color: white;
          border: none;
          border-radius: 0.25rem;
          cursor: pointer;
        }
        .input-with-prefix {
          position: relative;
        }
        .input-prefix {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          z-index: 1;
        }
        .input-with-prefix input {
          padding-left: 2.5rem;
        }
        .form-section {
          background: white;
        }
        .form-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 1.5rem;
        }
        .form-header-content {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          color: #8b5cf6;
        }
        .status-badge {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.375rem 0.75rem;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          font-weight: 500;
        }
        .status-badge.ready {
          background: #dcfce7;
          color: #166534;
        }
        .review-content {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }
        .review-section {
          background: #f8fafc;
          border-radius: 1rem;
          padding: 1.5rem;
          border: 1px solid #e2e8f0;
          transition: all 0.3s ease;
        }
        .review-section:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .review-section-header {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin-bottom: 1.25rem;
          color: #3b82f6;
        }
        .review-section-header h4 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
          font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .review-items {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }
        .review-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .review-item.full-width {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.5rem;
        }
        .review-label {
          font-size: 0.875rem;
          color: #64748b;
          font-weight: 500;
        }
        .review-value {
          font-size: 0.875rem;
          color: #1e293b;
          font-weight: 400;
        }
        .review-link {
          color: #3b82f6;
          text-decoration: none;
          font-weight: 500;
          word-break: break-all;
        }
        .review-link:hover {
          text-decoration: underline;
        }
        .review-documents {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-top: 0.5rem;
        }
        .review-document {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem;
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
        }
        .review-document span {
          font-size: 0.875rem;
          color: #374151;
          font-weight: 500;
        }
        .review-document small {
          font-size: 0.75rem;
          color: #6b7280;
          margin-left: auto;
        }
        .review-highlights {
          list-style: none;
          padding: 0;
          margin: 0.5rem 0 0 0;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }
        .review-highlight {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
          color: #374151;
        }
        .highlight-bullet {
          width: 0.375rem;
          height: 0.375rem;
          background: #8b5cf6;
          border-radius: 50%;
          flex-shrink: 0;
        }
      `}</style>
      {/* Step Navigation */}
      <div className="flex gap-3 mb-6">
        {steps.map((step) => (
          <button
            key={step.number}
            className={`page-nav-item ${step.completed ? 'completed' : step.active ? 'active' : ''}`}
            style={{
              fontSize: '13px',
              lineHeight: '1.2',
              fontWeight: '500',
              fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
              display: 'flex',
              alignItems: 'center',
              gap: '0.375rem',
              padding: '0.5rem 1rem',
              background: step.completed ? '#ddd6fe' : step.active ? '#ede9fe' : '#f3f4f6',
              border: 'none',
              borderRadius: '1.5rem',
              cursor: step.number <= currentStep ? 'pointer' : 'not-allowed',
              color: step.completed ? '#6d28d9' : step.active ? '#7c3aed' : '#6b7280',
              transition: 'all 0.2s ease',
              whiteSpace: 'nowrap',
              minWidth: 'fit-content'
            }}
            onClick={() => step.number <= currentStep && setCurrentStep(step.number)}
            disabled={step.number > currentStep}
          >
            <span className="text-base">{getStepIcon(step.number)}</span>
            {step.title}
          </button>
        ))}
      </div>

      {/* Step Content */}
      {renderStepContent()}

      {/* AI Assist Notification */}
      {showAiAssist && (
        <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg flex items-center gap-3">
          <HiOutlineSparkles size={20} className="animate-spin" />
          <div>
            <h4 className="font-medium">AI Assist Applied</h4>
            <p className="text-sm opacity-90">Plan details have been pre-filled for you.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreatePlanForm;