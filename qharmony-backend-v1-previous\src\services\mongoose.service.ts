// mongoose.service.ts

import mongoose from 'mongoose';
import EnvService from './env.service';
import AbstractService from './service';

class MongooseService extends AbstractService {
  private MONGO_URI: string = EnvService.env().MONGO_URI;

  static init(): void {
    new MongooseService().connect();
  }

  private connect() {
    mongoose
      .connect(this.MONGO_URI, { dbName: 'testing' })
      .then(() => console.log('Connected to MongoDB'))
      .catch((err) => console.error('Error connecting to MongoDB', err));
  }
}

export default MongooseService;
