
import React, { useState, useEffect } from 'react';
import { Bot } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface BotQuestionProps {
  question: string;
  context?: string;
  enableStreaming?: boolean;
  onStreamingComplete?: () => void;
}

export const BotQuestion = ({ question, context, enableStreaming = false, onStreamingComplete }: BotQuestionProps) => {
  const [displayedQuestion, setDisplayedQuestion] = useState(enableStreaming ? '' : question);
  const [displayedContext, setDisplayedContext] = useState(enableStreaming ? '' : (context || ''));
  const [questionComplete, setQuestionComplete] = useState(!enableStreaming);
  const [contextComplete, setContextComplete] = useState(!context || !enableStreaming);

  // Much faster streaming for more natural feel
  useEffect(() => {
    if (enableStreaming && displayedQuestion.length < question.length) {
      const timer = setTimeout(() => {
        // Add 2-3 characters at once for faster streaming
        const nextChars = Math.min(3, question.length - displayedQuestion.length);
        setDisplayedQuestion(prev => prev + question.slice(displayedQuestion.length, displayedQuestion.length + nextChars));
      }, 20); // Reduced from 50ms to 20ms
      return () => clearTimeout(timer);
    } else if (displayedQuestion.length === question.length && !questionComplete) {
      setQuestionComplete(true);
    }
  }, [displayedQuestion, question, enableStreaming, questionComplete]);

  // Faster context streaming
  useEffect(() => {
    if (enableStreaming && context && questionComplete && displayedContext.length < context.length) {
      const timer = setTimeout(() => {
        const nextChars = Math.min(2, context.length - displayedContext.length);
        setDisplayedContext(prev => prev + context.slice(displayedContext.length, displayedContext.length + nextChars));
      }, 15); // Reduced from 30ms to 15ms
      return () => clearTimeout(timer);
    } else if (context && questionComplete && displayedContext.length === context.length && !contextComplete) {
      setContextComplete(true);
    }
  }, [questionComplete, displayedContext, context, enableStreaming, contextComplete]);

  // Call onStreamingComplete when both are done
  useEffect(() => {
    if (questionComplete && contextComplete && onStreamingComplete) {
      onStreamingComplete();
    }
  }, [questionComplete, contextComplete, onStreamingComplete]);

  return (
    <div className="flex gap-3 mb-6">
      <Avatar className="w-10 h-10">
        <AvatarFallback className="bg-blue-100 dark:bg-blue-900">
          <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </AvatarFallback>
      </Avatar>
      
      <div className="flex-1">
        <div className="bg-gray-100 dark:bg-gray-800 rounded-2xl px-4 py-3">
          <p className="text-gray-900 dark:text-gray-100 font-medium">
            {displayedQuestion}
          </p>
          {context && questionComplete && (
            <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
              {displayedContext}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
