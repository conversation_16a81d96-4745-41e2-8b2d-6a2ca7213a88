import { exec } from 'child_process';

const getCurrentGitBranch = (): Promise<string> => {
    return new Promise((resolve, reject) => {
        exec('git rev-parse --abbrev-ref HEAD', (error, stdout) => {
            if (error) {
                reject(`exec error: ${error}`);
                return;
            }
            resolve(stdout.trim());
        });
    });
};

export default getCurrentGitBranch;