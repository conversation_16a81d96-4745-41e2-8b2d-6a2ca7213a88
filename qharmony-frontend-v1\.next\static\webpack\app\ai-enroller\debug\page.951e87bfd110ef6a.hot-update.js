"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/debug/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/debug/page.tsx":
/*!********************************************!*\
  !*** ./src/app/ai-enroller/debug/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DebugPage() {\n    _s();\n    const [localStorageData, setLocalStorageData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get all localStorage data\n        const data = {};\n        for(let i = 0; i < localStorage.length; i++){\n            const key = localStorage.key(i);\n            if (key) {\n                data[key] = localStorage.getItem(key) || \"\";\n            }\n        }\n        setLocalStorageData(data);\n    }, []);\n    const clearUserData = ()=>{\n        localStorage.removeItem(\"userid1\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"user\");\n        // Set the ACTUAL user ID that exists in the database\n        localStorage.setItem(\"userid1\", \"67bf65bf50bad0a4b3d805bc\");\n        localStorage.setItem(\"companyId1\", \"67bf65bf50bad0a4b3d805ba\");\n        // Refresh the data\n        const data = {};\n        for(let i = 0; i < localStorage.length; i++){\n            const key = localStorage.key(i);\n            if (key) {\n                data[key] = localStorage.getItem(key) || \"\";\n            }\n        }\n        setLocalStorageData(data);\n        alert(\"User data cleared and reset to DATABASE user ID: 67bf65bf50bad0a4b3d805bc\");\n    };\n    const testDirectAPI = async ()=>{\n        const userId = localStorage.getItem(\"userid1\");\n        const companyId = localStorage.getItem(\"companyId1\");\n        if (!userId || !companyId) {\n            alert(\"Missing userId or companyId in localStorage\");\n            return;\n        }\n        try {\n            var _result_assignments;\n            // Test direct API call\n            const response = await fetch(\"http://localhost:8080/api/pre-enrollment/plan-assignments/company/\".concat(companyId, \"?includePlanData=true\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD0D Direct API Test Result:\", {\n                status: response.status,\n                ok: response.ok,\n                result\n            });\n            alert(\"API Test Result:\\nStatus: \".concat(response.status, \"\\nAssignments: \").concat(((_result_assignments = result.assignments) === null || _result_assignments === void 0 ? void 0 : _result_assignments.length) || 0, \"\\nTotal Count: \").concat(result.totalCount || 0, \"\\nApplied Filters: \").concat(JSON.stringify(result.appliedFilters)));\n        } catch (error) {\n            console.error(\"API Test Error:\", error);\n            alert(\"API Test Error: \".concat(error.message));\n        }\n    };\n    const testUserAPI = async ()=>{\n        const userId = localStorage.getItem(\"userid1\");\n        if (!userId) {\n            alert(\"Missing userId in localStorage\");\n            return;\n        }\n        try {\n            // Test CORRECT user API endpoint that matches plan assignment API pattern\n            const response = await fetch(\"http://localhost:8080/employee\", {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD0D User API Test Result:\", {\n                status: response.status,\n                ok: response.ok,\n                result\n            });\n            if (response.ok) {\n                alert(\"User API Result:\\nName: \".concat(result.name, \"\\nEmail: \").concat(result.email, \"\\nCompany ID: \").concat(result.companyId, \"\\nisBroker: \").concat(result.isBroker, \"\\nisAdmin: \").concat(result.isAdmin));\n            } else {\n                alert(\"User API Error:\\nStatus: \".concat(response.status, \"\\nError: \").concat(JSON.stringify(result)));\n            }\n        } catch (error) {\n            console.error(\"User API Test Error:\", error);\n            alert(\"User API Test Error: \".concat(error.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"monospace\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Debug - LocalStorage Data & API Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\",\n                    display: \"flex\",\n                    gap: \"10px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearUserData,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#007bff\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Clear & Reset User Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: testDirectAPI,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#28a745\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Test Direct API Call\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: testUserAPI,\n                        style: {\n                            padding: \"10px 20px\",\n                            backgroundColor: \"#ffc107\",\n                            color: \"black\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Test User API\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Current LocalStorage Contents:\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                style: {\n                    backgroundColor: \"#f5f5f5\",\n                    padding: \"10px\",\n                    borderRadius: \"4px\"\n                },\n                children: JSON.stringify(localStorageData, null, 2)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Broker Access Analysis:\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: \"#f8f9fa\",\n                    padding: \"15px\",\n                    borderRadius: \"4px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Issue:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 12\n                            }, this),\n                            \" Applied Filters show no broker-specific filtering\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Expected:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                children: '[\"activeOnly\", \"companyId:...\", \"brokerAccess:...\"]'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 39\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Actual:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                children: '[\"activeOnly\", \"companyId:6838677aef6db0212bcfdacb\"]'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Broker Access Conditions (both must be true):\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"brokerAssignments.length > 0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" - Broker owns plans assigned to this company\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"isOwnCompany\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" - user.companyId === companyId\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Your Data:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"User ID: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: localStorage.getItem(\"userid1\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"Company ID: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: localStorage.getItem(\"companyId1\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 27\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"From localStorage: User company should match target company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Likely Issue:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 12\n                            }, this),\n                            \" The \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                children: \"getBrokerAssignmentsForCompany()\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 47\n                            }, this),\n                            \" method is returning empty array, AND the company ID comparison is failing.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Current User ID from getUserId():\"\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"userid1: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"userid1\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 18\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 81\n                    }, this),\n                    \"userId: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"userId\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 79\n                    }, this),\n                    \"companyId1: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: localStorage.getItem(\"companyId1\") || \"not set\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\debug\\\\page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugPage, \"aIby2hUaZfOHmhr0X+VFs6100zo=\");\n_c = DebugPage;\nvar _c;\n$RefreshReg$(_c, \"DebugPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/debug/page.tsx\n"));

/***/ })

});