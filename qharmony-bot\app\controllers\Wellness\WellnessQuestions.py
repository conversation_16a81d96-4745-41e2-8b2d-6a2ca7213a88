from .dataModels import Question, QuestionsResponse
import json
# ---------------------
# 1. Function to Return the Questions JSON
# ---------------------

def get_life_expectancy_questions() -> QuestionsResponse:
    questions_data = [
        Question(
            id="gender",
            text="What is your gender?",
            type="categorical",
            options=["Male", "Female"],
            feature="Sex"
        ),
        Question(
            id="bmi",
            text="What is your BMI?",
            type="numeric",
            feature="BMI"
        ),
        Question(
            id="height",
            text="What is your height in (cm)?",
            type="numeric",
            feature=None
        ),
        Question(
            id="weight",
            text="What is your weight in (kg)?",
            type="numeric",
            feature=None
        ),
        Question(
            id="age",
            text="What is your current age?",
            type="numeric",
            feature="Age"
        ),
        Question(
            id="race",
            text="What is your race?",
            type="categorical",
            options=[
                "Black", "White", "Other", "American Indian/Alaskan Native", 
                "Asian", "Hispanic", "White only, Non-Hispanic", 
                "Black only, Non-Hispanic", "Multiracial, Non-Hispanic", 
                "Other race only, Non-Hispanic"
            ],
            feature="Race"
        ),
        Question(
            id="general_health",
            text="What is your general health?",
            type="ordinal",
            options=["Poor", "Fair", "Good", "Very good", "Excellent"],
            feature="GenHealth"
        ),
        Question(
            id="diabetic",
            text="Are you diabetic?",
            type="categorical",
            options=[
                "No", "Yes", "No, borderline diabetes", "Yes (during pregnancy)",
                "Yes, but only during pregnancy (female)", "No, pre-diabetes or borderline diabetes"
            ],
            feature="Diabetic",
            weights=-2.5
        ),
        Question(
            id="asthma",
            text="Do you have asthma?",
            type="boolean",
            feature="Asthma",
            weights=-1.5
        ),
        Question(
            id="kidney_problems",
            text="Do you have any kidney problems?",
            type="boolean",
            feature=None,
            weights=-2.0
        ),

        Question(
            id="smoking",
            text="How often have you smoked in the past two years?",
            type="categorical",
            options=["Rarely", "Sometimes", "Often"],
            feature="Smoking",
            weights={"Rarely": 0, "Sometimes": -2, "Often": -5}
        ),
        Question(
            id="alcohol",
            text="How often have you consumed alcohol in the past two years?",
            type="categorical",
            options=["Rarely", "Sometimes", "Often"],
            feature="AlcoholDrinking",
            weights={"Rarely": 0, "Sometimes": -1, "Often": -3}
        ),
        Question(
            id="physical_activity",
            text="How often have you been engaging in physical activities in the past year?",
            type="categorical",
            options=["Rarely", "Sometimes", "Often"],
            feature="PhysicalActivity",
            weights={"Rarely": -3, "Sometimes": 1, "Often": 5}
        ),
        Question(
            id="sleep_hours",
            text="How many hours do you sleep?",
            type="numeric",
            feature="SleepTime",
            weights={"<5": -3, "5-7": 0, "7-9": 2, ">9": -1}
        ),
        Question(
            id="walking",
            text="How often do you walk?",
            type="categorical",
            options=["Rarely", "Sometimes", "Often"],
            feature="DiffWalking",
            weights={"Rarely": -2, "Sometimes": 1, "Often": 3}
        ),
        Question(
            id="stress",
            text="Do you often take stress?",
            type="boolean",
            feature=None,
            weights={"Yes": -2, "No": 1}
        ),
        Question(
            id="social_life",
            text="Do you like engaging in social gatherings?",
            type="boolean",
            feature=None,
            weights={"Yes": -1, "No": 0}
        ),
        Question(
            id="healthy_food",
            text="How often do you include fruits and vegetables in your meal without using processed food?",
            type="categorical",
            options=["Rarely", "Sometimes", "Often"],
            feature=None,
            weights={"Rarely": -3, "Sometimes": 1, "Often": 4}
        ),
        Question(
            id="cardio",
            text="How often do you have cardio?",
            type="categorical",
            options=["Rarely", "Sometimes", "Often"],
            feature=None,
            weights={"Rarely": -3, "Sometimes": 1, "Often": 3}
        ),
        Question(
            id="life_span_grandparents",
            text="Life span of grand parents? (Average would work)",
            type="numeric",
            feature=None,
            weights=None
        ),
        Question(
            id="ever_married",
            text="Are you married or were you ever married?",
            type="boolean",
            feature=None,
            weights={"Yes": -1, "No": 0}
            
        ),
        Question(
            id="work_type",
            text="What is your work type?",
            type="categorical",
            options=['Self-employed','Govt_job','Private'],
            feature=None,
            weights=None
            
        ),
        Question(
            id="residence_type",
            text="What is your residence type?",
            type="categorical",
            options=['Rural','Urban'],
            feature=None,
            weights=None
            
        ),
        Question(
            id="avg_glucose_level",
            text="What is your average glucose level?",
            type="numeric",
            feature=None,
            weights=None
            
        ),
        
    ]
    return QuestionsResponse(questions=questions_data)

def get_life_expectancy_questions_json() -> str:
    """
    Return the life expectancy questions as a JSON string for the frontend.

    Returns:
    - str: A JSON string representing the list of questions.
    """
    questions = get_life_expectancy_questions()
    # Convert each Question object to a dictionary and then the list to a JSON string
    questions_dicts = [question.model_dump() for question in questions.questions]
    return {"wellness_questiions":questions_dicts}