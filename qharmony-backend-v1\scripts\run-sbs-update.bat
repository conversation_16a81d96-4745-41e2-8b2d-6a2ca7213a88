@echo off
echo ========================================
echo Southern Benefits Systems Data Update
echo ========================================
echo.

echo Step 1: Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    echo Make sure you have Node.js installed and try again
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully
echo.

echo Step 1.5: Checking environment variables...
if not exist "..\\.env" (
    echo ERROR: .env file not found in parent directory
    echo Please make sure the .env file exists in qharmony-backend-v1 directory
    pause
    exit /b 1
)
echo ✅ Environment file found
echo.

echo Step 1.6: Testing database connection...
node test-connection.js
if %errorlevel% neq 0 (
    echo ERROR: Database connection failed
    echo Please check your MongoDB connection and try again
    pause
    exit /b 1
)
echo ✅ Database connection successful
echo.

echo Step 2: Validating CSV data...
node validate-csv.js
if %errorlevel% neq 0 (
    echo ERROR: CSV validation failed
    pause
    exit /b 1
)
echo ✅ CSV validation passed
echo.

echo Step 3: Updating database...
echo ⚠️  WARNING: This will update the database with employee details and dependents
echo Press any key to continue or Ctrl+C to cancel...
pause > nul

node update-sbs-user-details.js
if %errorlevel% neq 0 (
    echo ERROR: Database update failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ SBS Data Update Completed Successfully!
echo ========================================
echo.
echo Summary:
echo - 20 employees updated with missing details
echo - 16 dependents added to employee profiles
echo - All data conflicts resolved
echo.
pause
