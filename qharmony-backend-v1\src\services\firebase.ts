import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import EnvService from './env.service';

let firebaseApp: FirebaseApp | null = null;
let firebaseAuth: Auth | null = null;

/**
 * Initialize Firebase with lazy loading to avoid environment access before EnvService.init()
 */
function initializeFirebase(): { app: FirebaseApp; auth: Auth } {
  if (firebaseApp && firebaseAuth) {
    return { app: firebaseApp, auth: firebaseAuth };
  }

  // Only access environment variables when actually needed
  const env = EnvService.env();

  // Firebase client config always comes from environment variables
  // Both test and production environments will have their own Firebase env variables
  const firebaseConfig = {
    apiKey: env.FIREBASE_API_KEY,
    authDomain: env.FIREBASE_AUTH_DOMAIN,
    projectId: env.FIREBASE_PROJECT_ID,
    storageBucket: env.FIREBASE_STORAGE_BUCKET,
    messagingSenderId: env.FIREBASE_MESSAGING_SENDER_ID,
    appId: env.FIREBASE_APP_ID,
  };

  // Check if Firebase app is already initialized
  if (getApps().length === 0) {
    firebaseApp = initializeApp(firebaseConfig);
  } else {
    firebaseApp = getApps()[0];
  }

  firebaseAuth = getAuth(firebaseApp);

  return { app: firebaseApp, auth: firebaseAuth };
}

/**
 * Get Firebase Auth instance (lazy-loaded)
 */
export function getFirebaseAuth(): Auth {
  const { auth } = initializeFirebase();
  return auth;
}

/**
 * Get Firebase App instance (lazy-loaded)
 */
export function getFirebaseApp(): FirebaseApp {
  const { app } = initializeFirebase();
  return app;
}

// Export auth for backward compatibility
export const auth = new Proxy({} as Auth, {
  get(_target, prop) {
    const firebaseAuth = getFirebaseAuth();
    return (firebaseAuth as any)[prop];
  }
});
