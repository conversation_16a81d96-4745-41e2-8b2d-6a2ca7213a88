#--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

from bson import ObjectId
import os
from .dataModels import Question,QuestionsResponse,UserAnswer,LifeExpectancyPrediction, WellnessRequestData
from .WellnessQuestions import get_life_expectancy_questions, get_life_expectancy_questions_json
from .lifeExp import calculate_life_expectancy
from .strokepredictions import get_stroke_prediction
from .heartpredicitons import predict_heart_disease_single_function
from .WellnessRecommendations import ai_recommendations
from .WellnessDataStore import store_wellness_data
from .helpers import get_mongo_collection, get_mongo_db
from app.Tools.vectorStore import VectorStore
from app.utils.vstore_utils.vectorstore_utils import get_retriever, fetch_document_ids
from app.DataModels.ResponseModel import ResponseModel
from . import stroke_model, stroke_preprocessors, stroke_columns # Use relative import
from . import heart_disease_model, heart_disease_preprocessors, heart_disease_columns # etc.
from . import life_exp_preprocessors, life_exp_columns, ridge_model, poly, X_train_poly, sigma # etc.
import logging
from fastapi import Response, status, HTTPException
from langchain_openai.chat_models import ChatOpenAI
from config.config import Config

# Define the preprocessing function (should be the same as used during training)
class Wellness:
    def __init__(self, mongo_client, mongo_environment, vector_store_manager: VectorStore):
        self.config=Config()
        self.mongo_client = mongo_client
        self.mongo_environment = mongo_environment
        self.vector_store_manager = vector_store_manager
        self.model = ChatOpenAI(model=self.config.openai_model_name,temperature=0.5,api_key=self.config.openai_key)
        self.express_uri = self.config.express_uri



    async def _get_wellness_predictions(self, user_answer: UserAnswer, user_id, team_id):
        questions = get_life_expectancy_questions()
        # user_response = UserAnswer(**user_response_json)
        result={}
        #call the stroke model
        
        stroke_predicitons=get_stroke_prediction(user_answers=user_answer,preprocessors=stroke_preprocessors,model=stroke_model, training_columns=stroke_columns)
        # print(user_response,"-------------USER RESPONSE-------------------")
        user_answer.answers['stroke'] = stroke_predicitons['stroke']
        life_expectency_results = calculate_life_expectancy(
        user_response=user_answer,
        model=ridge_model,
        preprocessors=life_exp_preprocessors,
        poly_transform=poly,
        sigma_value=sigma,
        X_train_poly=X_train_poly,
        training_columns=life_exp_columns,
        )
        
        heart_prediction = await predict_heart_disease_single_function(
            user_response=user_answer,
            model=heart_disease_model,
            preprocessors=heart_disease_preprocessors,
            training_columns=heart_disease_columns
        )
        
        predictions_dict = {
            "life_expectancy": life_expectency_results.model_dump(),  # Convert Pydantic model to dict
            "heart_disease_probability": heart_prediction, # Float or None
            "stroke_prediction" : stroke_predicitons
        }
        result.update({"predictions":predictions_dict})
        
        retriver, document_ids = self._fetch_wellness_context_retriever(user_id=user_id, team_id=team_id)
        recommendations, sources = ai_recommendations(model=self.model,questions=questions, 
                                user_response=user_answer, 
                                predictions=predictions_dict, 
                                retriever=retriver, document_ids=document_ids, company_id=team_id, express_uri=self.express_uri)
        result.update({"recommendations":recommendations, "sources":sources})
        
        return result

    async def _fetch_wellness_context_retriever(self, user_id, team_id):
        print(f"--------------{team_id}-{user_id}--------------------")
        
        
        try:
            docment_ids = await fetch_document_ids(user_id=user_id, team_id=team_id, vector_store=self.vector_store_manager, mongo_client=self.mongo_client)
            retriever = await get_retriever(vector_store=self.vector_store_manager, search_kwargs={"filter": {"file_key": {"$in": docment_ids}}, "k": 10})
            
            return retriever, docment_ids
        except Exception as e:
            raise e
        
    async def initiate_wellness_app(self, request_data: WellnessRequestData, response: Response):
        try:
            result = await self._get_wellness_predictions(user_answer=request_data.user_answer, user_id=request_data.user_id, team_id=request_data.team_id)
            await store_wellness_data(
                    user_id=request_data.user_id,
                        company_id=request_data.team_id,
                        wellness_predictions=result["predictions"],
                        user_response=request_data.user_answer.answers,
                        db=get_mongo_db(self.mongo_client, self.mongo_environment)
                        )           
            return ResponseModel(data=result)
                
        except HTTPException as e:
        # propagate user‑facing errors (400,404, etc.)
            response.status_code = e.status_code
            return ResponseModel(
                    success=False,
                    status_code=e.status_code,
                    error=e.detail
                )
        
        except Exception as e:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            print(e)

            return ResponseModel(success=False, error=str(e), status_code=response.status_code)
    
    async def get_wellness_questions(self):
        return ResponseModel(data=get_life_expectancy_questions_json())
        
# ---------------------
# Example Usage
# ---------------------
# Example user response JSON (as might be received from the frontend).

# Example user response JSON (as might be received from the frontend).
# user_response_json = {
#     "answers": {
#         "gender": "Male",
#         "bmi": None,
#         "age": 30,
#         "height": 175,
#         "weight": 90,
#         "race": "American Indian/Alaskan Native",
#         "general_health": "Fair",
#         "diabetic": "Yes",
#         "asthma": "Yes",
#         "kidney_problems": "Yes",
#         "smoking": "Often", # -> Smoking: 'smokes'
#         "alcohol": "Sometimes",
#         "physical_activity": "Sometimes",
#         "sleep_hours": 9,
#         "walking": "Often",
#         "stress": "No", # -> hypertension: 0
#         "social_life": "Yes",
#         "healthy_food": "Rarely",
#         "life_span_grandparents": 80,
#         "cardio": "Rarely",
#         "ever_married": "No", # -> ever_married: 'Yes'
#         "work_type": "Private", # -> work_type: 'Self-employed'
#         "residence_type": "Urban",     # -> Residence_type: 'Rural'
#         "avg_glucose_level": 250.5    # -> avg_glucose_level: 150.5
#     }
# }


# print(get_wellness_predictions(user_response_json=user_response_json))