# 🔄 QHarmony Frontend Updates – AI Enroller Module
**File:** `qharmony-frontend-v1/newupdate.md`  
**Date:** 2025-06-28

---

## ✅ Global Header Fixes

- Make the **AI Enroller header thinner and consistent** across:
  - `/create-plan`
  - `/plans`
  - `/enrollment`
  - `/manage-groups`
- Match header styling with `/create-plan` and `/plans` (height, padding, background).

---

## 🧩 `/create-plan` Page

### 🔧 Layout Adjustments:
- Align widths for:
  - Progress header
  - Page navigation
  - Chat bubble
  - Form container
- Make layout responsive to screen size (FHD, ultrawide).

### 🧹 Cleanup:
- Remove bottom margin below `.page-title`.
- Change page nav gradient from purple to the default blue-violet gradient.

---

## ✅ Final Page Layout: `Plan Created Successfully`

- Tighten spacing between:
  - Title, message, and plan detail fields


---

## 🗃️ `/ai-enroller/plans` Page

### 📐 Component Layout:
- Move "Plan Management" + subtext into `plans-page` layout directly.
- Remove redundant or disconnected component.

### 🔄 Alignment & Style:
- Align filter/search section with `plans-table`.
- Fix extra margins in grid container.
- Set background to white and fix text color (currently white on white).

### 🧩 Card Design:
- Match cards to design reference:
- White background
- Colored border
- Rounded corners
- Compact field layout
- Proper badge for status

---

## 🔄 `/ai-enroller/manage-groups`

### 🧾 Header Consistency:
- Replace old header with updated one matching `/create-plan` and `/plans`.

---

## 🧭 `/manage-groups/company/[company_id]/plans`

### 🧪 Data Fetch:
- Page **not showing plan assignments** → likely broken API integration.
- Fix API call and ensure updated structure from `planAssignment.controller.ts` is used.

### 🖼️ UI Improvements:
- Move **Company Name** above the grid layout as the page heading.
- Remove:
- Back button
- Separated top container
- Each plan assignment card should:
- Have a **white background**
- Use a **colored border** to distinguish
- Follow consistent card layout: name, status, carrier, actions (edit/delete/clone)

---

## 🔌 API Checks

- Validate:
- `GET /plans`
- `GET /plan-assignments`
- `GET /plan-assignments-by-company`
- Check:
- Data structure
- Pagination
- Filtering
- Status values returned

---

## ✅ Summary of Tasks

| Page | Task |
|------|------|
| Global | Update AI Enroller header UI |
| /create-plan | Align layout widths, fix responsiveness |
| /create-plan | Remove margin below title, change nav gradient |
| Final Page | Reduce whitespace in success screen |
| /plans | Move title into layout, fix filters & margins |
| /plans | Redesign cards to match visual reference |
| /manage-groups | Replace old header with new thin layout |
| /manage-groups/company/[id]/plans | Fix API to show plan assignments |
| /manage-groups/company/[id]/plans | Redesign UI: remove back button, white cards, colored borders |


