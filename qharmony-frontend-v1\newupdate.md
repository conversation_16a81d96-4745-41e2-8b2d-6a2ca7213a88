Here is the updated `.md` file with the API validation section cleaned up as per your instructions:


**Title:** `Plan Module Refactor and UI Fix Tasks`

---

## ✅ General Overview

Recent backend refactoring and controller changes require updates on the frontend, particularly in:

* `/create-plan`
* `/plans`
* `/manage-groups`
* Subpaths inside each of the above

Updated APIs and structures must be reflected in the frontend to reduce redundant calls and correctly display data, especially in `manage-groups`.

---

## 🔁 Backend Files Updated

Changes made to the following backend files affect current frontend API logic and data structures:

```text
src/controllers/plan.controller.ts
src/controllers/planAssignment.controller.ts
src/nosql/preEnrollment/plan.model.ts
src/services/employeeEnrollmentService.ts
src/services/enrollment/plan.service.ts
src/services/enrollment/planAssignment.service.ts
```

> ✅ Refer updated response/request structures in:
>
> * `docs/PLAN_STATUS_CHANGES_SUMMARY.md`
> * `docs/PLAN_STATUS_MANAGEMENT_ANALYSIS.md`
> * `docs/API_COMPLIANCE_AUDIT_REPORT.md`

---

## 🚨 Key Frontend Fixes & Tasks

### 1. 🔼 Add Page Headers

Add consistent headers to:

* `/create-plan`
* `/plans`
* `/manage-groups`
* All subroutes inside the above

---

### 2. 🔁 API Dependency Refactor

* Remove multiple individual API calls on:

  ```
  /ai-enroller/manage-groups
  ```

* Replace them with newer aggregated API (from:
  `plan.controller.ts`, `planAssignment.controller.ts`).

* **Updated APIs provide:**

  * Pagination
  * Filtering
  * Status control
  * Consolidated plan + assignment + carrier data

---

### 3. ⚠️ Bug Fixes

* **/plans:**

  * Fix **filter dropdown** issues (not working correctly)
  * Align **"Plan Management"** heading to top (inline with buttons)
  * Improve layout on all screen sizes

* **/create-plan:**

  * Fix inconsistent widths on larger screens
  * Ensure chatbot and navbar section do not stretch awkwardly on ultrawide monitors

---

### 4. ✅ Status Management Implementation

* On `manage-groups/company/[id]/plans`:

  * Use updated structure from backend
  * Each card should:

    * Display **plan + status + carrier**
    * Enable toggling plan status (active/archive)
    * Use updated status flow from backend

---

### 5. 🧩 UI Enhancements

* On each plan card in:

  ```
  /ai-enroller/manage-groups/company/[company_id]/plans
  ```

  Add:

  * 🗑️ Delete
  * ✏️ Edit
  * 🧬 Clone
    → Ensure all actions are functional using new APIs

* Improve UI:

  * Use cleaner **color palette**
  * Improve card **spacing & alignment**
  * Align card elements consistently



## 📌 Notes

* Backend is now fully refactored with:

  * Controller → Service → Middleware → Model layers
* All changes should be referenced with **updated API docs**


