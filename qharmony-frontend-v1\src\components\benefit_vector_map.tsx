import PeopleIcon from "@mui/icons-material/People";
import HealthAndSafetyIcon from "@mui/icons-material/HealthAndSafety";
import AssistWalkerIcon from "@mui/icons-material/AssistWalker";
import PetsIcon from "@mui/icons-material/Pets";
import ReceiptLongIcon from "@mui/icons-material/ReceiptLong";
import SpaIcon from "@mui/icons-material/Spa";
import ElderlyIcon from "@mui/icons-material/Elderly";
import EmergencyIcon from "@mui/icons-material/Emergency";
import DevicesIcon from "@mui/icons-material/Devices";
import CommuteIcon from "@mui/icons-material/Commute";
import LuggageIcon from "@mui/icons-material/Luggage";
import FamilyRestroomIcon from "@mui/icons-material/FamilyRestroom";
import VolunteerActivismIcon from "@mui/icons-material/VolunteerActivism";
import AutoStoriesIcon from "@mui/icons-material/AutoStories";
import VerifiedIcon from "@mui/icons-material/Verified";
import StarIcon from "@mui/icons-material/Star";
import FavoriteIcon from "@mui/icons-material/Favorite";
import BubbleChartIcon from "@mui/icons-material/BubbleChart";
import StrollerIcon from '@mui/icons-material/Stroller';
import SchoolIcon from '@mui/icons-material/School';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';

import TeethIcon from "../../public/vectors/teeth.svg";
import Eyeglass from "../../public/vectors/eyeglass.svg";
import GradientHeart from "../../public/vectors/gradient_heart.svg";
import Dumbell from "../../public/vectors/dumbell.svg";
import PersonInCast from "../../public/vectors/person_in_cast.svg";
import CircleStack from "../../public/vectors/circle_stack.svg";
import FamilyWithHeart from "../../public/vectors/family_with_heart.svg";
import HeadWithSwirl from "../../public/vectors/head_with_swirl.svg";
import ThreeDotsTriangle from "../../public/vectors/three_dots_triangle.svg";
import BrushAndComb from "../../public/vectors/brush_and_comb.svg";
import React from "react";

export const benefit_subtype_icon_map: { [key: string]: JSX.Element } = {
  //Your Health
  "Medical": <HealthAndSafetyIcon />,
  "Dental": <TeethIcon />,
  "Vision": <Eyeglass />,
  "Wellness": <SpaIcon />,
  "Employee Assistance Program": <GradientHeart />,
  "Gym Membership": <Dumbell />,

  //Protecting Your Income
  "Life": <HealthAndSafetyIcon />,
  "Short Term Disability": <PersonInCast />,
  "Long Term Disability": <AssistWalkerIcon />,

  //Your Money
  "Pay & Bonus": <ReceiptLongIcon />,
  "Stock Options": <CircleStack />,
  "Health Savings Account": <HealthAndSafetyIcon />,
  "Flexible Savings Accounts": <EmergencyIcon />,
  "Technology Stipend": <DevicesIcon />,
  "Commuter Benefits": <CommuteIcon />,

  //Your Time Away
  "Paid Time Off (PTO)": <LuggageIcon />,
  "Parental Leave": <FamilyRestroomIcon />,
  "Family and Medical Leave": <FamilyWithHeart />,
  "Paid Volunteer Time": <VolunteerActivismIcon />,

  //Your Family
  "On-site Child Care": <StrollerIcon />,
  "Student Loan Assistance": <SchoolIcon />,
  "Pet Insurance": <PetsIcon />,

  //Your Career
  "Employee Training & Development": <HeadWithSwirl />,
  "Tuition Reimbursement": <AutoStoriesIcon />,
  "Employee Recognition": <VerifiedIcon />,
  "Performance Goals & Process": <StarIcon />,

  //Our Culture & Workplace
  "Pet-friendly Workplace": <PetsIcon />,
  "Ergonomic Workplace": <ThreeDotsTriangle />,
  "Company Handbook": <BrushAndComb />,

  //Life Events
  "Marriage or Divorce": <FavoriteIcon />,
  "New Baby or Adoption": <FamilyRestroomIcon />,
  "Loss of Insurance": <BubbleChartIcon />,
};

export const getBenefitSubtypeIcon = (
  benefitType: string,
  customStyle: React.CSSProperties = {},
): JSX.Element => {
  console.log("GETTING ICON FOR ", benefitType);
  const IconComponent = benefit_subtype_icon_map[benefitType] || <PeopleIcon />;
  return React.cloneElement(IconComponent, { style: customStyle });
};

export const benefit_type_icon_map: { [key: string]: JSX.Element } = {
  "Your Health": <HealthAndSafetyIcon />,
  "Income Security": <AccountBalanceWalletIcon />,
  "Your Money": <ReceiptLongIcon />,
  "Your Time Away": <LuggageIcon />,
  "Your Family": <FamilyRestroomIcon />,
  "Your Career": <AutoStoriesIcon />,
  "Work Policies": <SpaIcon />,
  "Life Events": <FavoriteIcon />,
};

export const getBenefitTypeIcon = (
  benefitType: string,
  customStyle: React.CSSProperties = {},
): JSX.Element => {
  const IconComponent = benefit_type_icon_map[benefitType] || (
    <HealthAndSafetyIcon />
  );
  return React.cloneElement(IconComponent, { style: customStyle });
};
