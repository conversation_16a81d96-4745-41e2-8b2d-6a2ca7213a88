import Finch from '@tryfinch/finch-api';
import logger, { prettyJSON } from '../utils/logger';
import EnvService from './env.service';
import CommonService from './common.service';
import { homeVerifiedLayout } from '../layouts/homeVerified.layout';
import FinchConnectionModelClass from '../nosql/finchConnection.model';
import SlackOauthModelClass from '../nosql/slackOauth.model';
import axios from 'axios';
import FinchIndividualModelClass from '../nosql/finchIndividualData.model';
import CompanyDocumentModelClass from '../nosql/companyDocument.model';

/*********** Interfaces *************/
interface AuthenticationMethod {
  type: string;
  connection_status: {
    status: string;
    message: string;
  };
}

interface IntrospectDataInterface {
  account_id: string;
  category: string;
  client_id: string;
  connection_type: string;
  company_id: string;
  username: string | null;
  payroll_provider_id: string;
  provider_id: string;
  products: string[];
  manual: boolean;
  authentication_methods: AuthenticationMethod[];
  client_type: string;
}

interface Department {
  name: string;
  parent: string | null;
}

interface Location {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
}

interface Entity {
  type: string;
  subtype: string;
}

interface CompanyInterface {
  id: string;
  legal_name: string;
  ein: string;
  entity: Entity;
  primary_email: string;
  primary_phone_number: string;
  departments: Department[];
  locations: Location[];
  accounts: any[]; // You can replace 'any[]' with the appropriate type for accounts
}

/*********** Service Class *************/

class FinchService {
  public finchClient: Finch;
  public commonService = new CommonService();

  constructor() {
    logger.info('Initializing Finch client');
    this.finchClient = new Finch({
      clientId: EnvService.env().FINCH_CLIENT_ID,
      clientSecret: EnvService.env().FINCH_CLIENT_SECRET,
      sandboxClientId: EnvService.env().FINCH_SANDBOX_CLIENT_ID,
      sandboxClientSecret: EnvService.env().FINCH_SANDBOX_CLIENT_SECRET,
    });
  }

  protected stringifyQuery(query: Record<string, unknown>): string {
    return Object.entries(query)
      .filter(([_, value]) => typeof value !== 'undefined')
      .map(([key, value]) => {
        if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
        }
        if (value === null) {
          return `${encodeURIComponent(key)}=`;
        }
        throw new Error(
          `Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`
        );
      })
      .join('&');
  }

  // Had to write custom function to get the auth URL because the Finch SDK is buggy.
  // It does not support the `state` parameter.
  public getAuthURL = ({ state }: { state: string }) => {
    const url = new URL('/authorize', 'https://connect.tryfinch.com/authorize');
    url.search = this.stringifyQuery({
      client_id: EnvService.env().FINCH_CLIENT_ID,
      products:
        'company directory individual employment payment pay_statement benefits',
      redirect_uri: EnvService.env().FINCH_REDIRECT_URI,
      state,
      // sandbox: EnvService.env().NODE_ENV !== 'production',
      sandbox: 'provider',
      // sandbox: false
    });
    return url.toString();
  };

  public getIntrospect = async ({
    finchAccessToken,
  }: {
    finchAccessToken: string;
  }): Promise<IntrospectDataInterface | null> => {
    try {
      logger.info('Getting introspect data');
      const response = await axios.get('https://api.tryfinch.com/introspect', {
        headers: {
          Authorization: `Bearer ${finchAccessToken}`,
          'Finch-API-Version': '2020-09-17',
          'Content-Type': 'application/json',
        },
      });
      return response.data as IntrospectDataInterface;
    } catch (error) {
      console.error(error);
    }
    return null;
  };

  public getCompany = async ({
    finchAccessToken,
  }: {
    finchAccessToken: string;
  }): Promise<CompanyInterface | null> => {
    try {
      logger.info('Getting company data');
      const response = await axios.get(
        'https://api.tryfinch.com/employer/company',
        {
          headers: {
            Authorization: `Bearer ${finchAccessToken}`,
            'Finch-API-Version': '2020-09-17',
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data as CompanyInterface;
    } catch (error) {
      console.error(error);
    }
    return null;
  };

  public getCompanyDirectory = async ({
    finchAccessToken,
  }: {
    finchAccessToken: string;
  }) => {
    try {
      logger.info('Getting company directory');
      const response = await axios.get(
        'https://api.tryfinch.com/employer/directory',
        {
          headers: {
            Authorization: `Bearer ${finchAccessToken}`,
            'Finch-API-Version': '2020-09-17',
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error(error);
    }
  };

  public getIndividual = async ({
    finchAccessToken,
    individualId,
  }: {
    finchAccessToken: string;
    individualId: string;
  }) => {
    try {
      logger.info(
        `Getting individual data for individual with id ${prettyJSON(
          individualId
        )}`
      );
      const response = await axios.post(
        'https://api.tryfinch.com/employer/individual',
        {
          requests: [
            {
              individual_id: individualId,
            },
          ],
        },
        {
          headers: {
            Authorization: `Bearer ${finchAccessToken}`,
            'Finch-API-Version': '2020-09-17',
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error(error);
    }
  };

  public getEmployment = async ({
    finchAccessToken,
    individualId,
  }: {
    finchAccessToken: string;
    individualId: string;
  }) => {
    try {
      logger.info(
        `Getting employment data for individual with id ${prettyJSON(
          individualId
        )}`
      );
      const response = await axios.post(
        'https://api.tryfinch.com/employer/employment',
        {
          requests: [
            {
              individual_id: individualId,
            },
          ],
        },
        {
          headers: {
            Authorization: `Bearer ${finchAccessToken}`,
            'Finch-API-Version': '2020-09-17',
            'Content-Type': 'application/json',
          },
        }
      );
      logger.info(prettyJSON(response.data));
      return response.data;
    } catch (error) {
      console.error(error);
    }
  };

  public getDeductions = async ({
    finchAccessToken,
  }: {
    finchAccessToken: string;
  }) => {
    try {
      logger.info('Getting deductions data');
      const response = await axios.get(
        `https://api.tryfinch.com/employer/benefits`,
        {
          headers: {
            Authorization: `Bearer ${finchAccessToken}`,
            'Finch-API-Version': '2020-09-17',
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error(error);
    }
  };

  public getEnrolledIndividuals = async ({
    finchAccessToken,
    benefitId,
  }: {
    finchAccessToken: string;
    benefitId: string;
  }) => {
    try {
      logger.info(
        `Getting enrolled individuals for deduction with id ${benefitId}`
      );
      const response = await axios.get(
        `https://api.tryfinch.com/employer/benefits/${benefitId}/enrolled`,
        {
          headers: {
            Authorization: `Bearer ${finchAccessToken}`,
            'Finch-API-Version': '2020-09-17',
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error(error);
    }
  };

  public getPayment = async ({
    finchAccessToken,
    startDate,
    endDate,
  }: {
    finchAccessToken: string;
    startDate: string;
    endDate: string;
  }) => {
    try {
      logger.info(
        `Getting payment data for start date ${startDate} and end date ${endDate}`
      );
      const response = await axios.get(
        `https://api.tryfinch.com/employer/payment?start_date=${startDate}&end_date=${endDate}`,
        {
          headers: {
            Authorization: `Bearer ${finchAccessToken}`,
            'Finch-API-Version': '2020-09-17',
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error(error);
    }
  };

  // Read detailed pay statements for each individual.
  public getPayStatement = async ({
    finchAccessToken,
    paymentIds,
  }: {
    finchAccessToken: string;
    paymentIds: string[];
  }) => {
    try {
      logger.info('Getting pay statement data');
      const response = await axios.post(
        `https://api.tryfinch.com/employer/pay-statement`,
        {
          requests: paymentIds.map((i) => {
            return {
              payment_id: i,
            };
          }),
        },
        {
          headers: {
            Authorization: `Bearer ${finchAccessToken}`,
            'Finch-API-Version': '2020-09-17',
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error(error);
    }
  };

  public saveAccessToken = async ({
    accessToken,
    state,
  }: {
    accessToken: string;
    state: string;
  }) => {
    // TODO(ankit): Save the access token in the database. Just logging for now.
    logger.info(`Access token: ${accessToken}`);

    const introspect = await this.getIntrospect({
      finchAccessToken: accessToken,
    });
    if (!introspect) {
      logger.error('Failed to get introspect data');
      return;
    }

    await FinchConnectionModelClass.addData({
      slackTeamId: state,
      finchAccessToken: accessToken,
      finchCompanyId: introspect.company_id,
    });

    const allFinchIndividualsData = await this.getAllIndividualsData({
      finchAccessToken: accessToken,
    });

    for (const individualData of allFinchIndividualsData) {
      logger.info(prettyJSON(individualData));
      await FinchIndividualModelClass.addData(individualData);
    }

    const slackOauthModel = await SlackOauthModelClass.getDataBySlackTeamId(
      state
    );
    const slackBotToken = slackOauthModel?.slackBotOauthToken ?? '';
    const companyDocumentModel = await CompanyDocumentModelClass.getDataBySlackTeamId(
      state
    );

    // refresh Slack home tab for all the users.
    const allUsers = await this.commonService.getAllUsers({
      slackBotToken,
    });
    if (!allUsers) {
      logger.error('Failed to get all users');
      return;
    }
    for (const user of allUsers) {
      console.log('user', user);
      const userEntity = await this.commonService.getUser({
        slackBotToken,
        userId: user.id,
      });
      const email = userEntity?.profile?.email ?? '';
      const finchIndividualEntity =
        await FinchIndividualModelClass.getDataByEmail(email);
      if (finchIndividualEntity) {
        await this.commonService.publishHomeTab({
          slackBotToken,
          userId: user.id,
          view: homeVerifiedLayout({
            showDisconnectButton:
              slackOauthModel?.slackInstallerUserId === user.id,
            pageNum: 1,
            firstName: finchIndividualEntity.finchFirstName,
            lastName: finchIndividualEntity.finchLastName,
            department: finchIndividualEntity.department,
            residence: finchIndividualEntity.finchAddress,
            emails: email,
            title: finchIndividualEntity.title,
            employmentType: finchIndividualEntity.employmentSubtype,
            startDate: finchIndividualEntity.startDate,
            endDate: finchIndividualEntity.endDate,
            dob: finchIndividualEntity.dob,
            incomeUnit: finchIndividualEntity.incomeUnit,
            incomeAmount: finchIndividualEntity.incomeAmount,
            incomeCurrency: finchIndividualEntity.incomeCurrency,
            incomeEffectiveDate: finchIndividualEntity.incomeEffectiveDate,
            benefits: finchIndividualEntity.benefits,
            timeoffs: finchIndividualEntity.timeoffs,
            taxes: finchIndividualEntity.payments[0].taxes, // TODO(ankit): Ask Shan if we only need to show the recent payment.
            payStub: {
              payPeriod: finchIndividualEntity.payments[0].pay_period,
              payDate: finchIndividualEntity.payments[0].pay_date,
              debitDate: finchIndividualEntity.payments[0].debit_date,
              netPay: finchIndividualEntity.payments[0].net_pay,
              grossPay: finchIndividualEntity.payments[0].gross_pay,
            },
            notificationPaused: true,
            companyDocument: companyDocumentModel,
          }),
        });
      }
      // Send each user a welcome message.
      await this.commonService.postMessageInDm({
        slackBotToken,
        sink: user.id,
        text: `Welcome to QHarmony!`,
        blocks: JSON.stringify([
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `🔄 Welcome to qHarmony! :rocket:

Maximize your benefits with ease. *All within Slack*!:bulb::bell:

Get Real-Time Answers :stopwatch:
Get Benefits Insight :star2:
Proactive Tips & Reminders:bulb:

Never miss out on:
:car: Commute Stipends
:man-lifting-weights: Gym Memberships
:female-doctor: Preventive Care
:grin: Dental Coverage
:eye: Vision Checkups
and much more...

Seize every opportunity! Optimize your well-being now. :dart: #qHarmony #Benefits 🔄`,
            },
          },
        ]),
      });
    }
  };

  public getAllIndividualsData = async ({
    finchAccessToken,
  }: {
    finchAccessToken: string;
  }) => {
    try {
      const extractedDataArray: any[] = [];
      const companyDirectory = await this.getCompanyDirectory({
        finchAccessToken,
      });
      if (!companyDirectory) {
        logger.error('Failed to get company directory');
        return extractedDataArray;
      }

      const payments = await this.getPayment({
        finchAccessToken,
        startDate: '1970-01-01', // Have to set a start date in the past to get all payments.
        endDate: new Date().toISOString().split('T')[0],
      });
      if (!payments) {
        logger.error('Failed to get payments data');
        return extractedDataArray;
      }
      // Call getPaymentStatement for each payment.
      const paymentIds = (payments.map((p: any) => p.id) as string[]).reverse();
      const payStatements = await this.getPayStatement({
        finchAccessToken,
        paymentIds,
      });

      // Populate individualToPaymentsMap.
      // individualToPaymentsMap is a map from individual id to payments array. In the payments array, each element is a payment object for a certain time period.
      const individualToPaymentsMap = new Map<string, any>();
      for (const payment of payStatements.responses) {
        const paymentId = payment.payment_id;
        const payStatements = payment.body.pay_statements;
        for (const payStatement of payStatements) {
          const individualId = payStatement.individual_id;
          if (!individualToPaymentsMap.has(individualId)) {
            individualToPaymentsMap.set(individualId, []);
          }
          const payment = payments.find((p: any) => p.id === paymentId);
          individualToPaymentsMap.get(individualId).push({
            paymentId,
            pay_period: payment?.pay_period,
            pay_date: payment?.pay_date,
            debit_date: payment?.debit_date,
            ...payStatement,
          });
        }
      }
      const individualToBenefitsMap = new Map();
      const deductions = await this.getDeductions({ finchAccessToken });
      if (!deductions) {
        logger.error('Failed to get deductions data');
        return extractedDataArray;
      }
      for (const deduction of deductions) {
        const deductionForIndividual = await this.getEnrolledIndividuals({
          finchAccessToken,
          benefitId: deduction.benefit_id,
        });
        if (!deductionForIndividual) {
          logger.error('Failed to get deduction for individual');
          continue;
        }
        logger.info(prettyJSON(deductionForIndividual));
        for (const individualId of deductionForIndividual.individual_ids) {
          if (!individualToBenefitsMap.has(individualId)) {
            individualToBenefitsMap.set(individualId, []);
          }
          individualToBenefitsMap.get(individualId).push(deduction);
        }
      }

      const individualIds = companyDirectory.individuals.map(
        (i: any) => i.id
      ) as string[];
      // logger.info(`Individual ids: ${prettyJSON(individualIds)}`);
      for (const individualId of individualIds) {
        const individualData = await this.getIndividual({
          finchAccessToken,
          individualId: individualId,
        });
        if (!individualData) {
          logger.error('Failed to get individual data');
          continue;
        }
        const individualEmploymentData = await this.getEmployment({
          finchAccessToken,
          individualId: individualId,
        });
        if (!individualEmploymentData) {
          logger.error('Failed to get individual employment data');
          continue;
        }
        const individualDataObj = individualData.responses[0].body;
        const individualEmploymentDataObj =
          individualEmploymentData.responses[0].body;
        for (const email of individualDataObj.emails) {
          const extractedData = {
            finchIndividualId: individualDataObj.id,
            finchFirstName: individualDataObj.first_name,
            finchLastName: individualDataObj.last_name,
            email: email.data,
            finchAddress:
              individualDataObj.residence?.line1 +
              ' ' +
              individualDataObj.residence?.line2 +
              ' ' +
              individualDataObj.residence?.city +
              ' ' +
              individualDataObj.residence?.state +
              ' ' +
              individualDataObj.residence?.country +
              ' ' +
              individualDataObj.residence?.postal_code,
            dob: individualDataObj.dob,
            department: individualEmploymentDataObj.department?.name ?? 'N/A',
            title: individualEmploymentDataObj.title,
            employmentType: individualEmploymentDataObj.employment.type,
            employmentSubtype: individualEmploymentDataObj.employment.subtype,
            startDate: individualEmploymentDataObj.start_date,
            endDate: individualEmploymentDataObj.end_date,
            incomeUnit: individualEmploymentDataObj.income.unit,
            incomeAmount: individualEmploymentDataObj.income.amount,
            incomeCurrency: individualEmploymentDataObj.income.currency,
            incomeEffectiveDate:
              individualEmploymentDataObj.income.effective_date,
            benefits: individualToBenefitsMap.get(individualId),
            payments: individualToPaymentsMap.get(individualId),
          };
          extractedDataArray.push(extractedData);
        }
      }
      logger.info(prettyJSON(extractedDataArray));
      return extractedDataArray;
    } catch (error) {
      console.error(error);
    }
    return [];
  };
}

export default FinchService;
