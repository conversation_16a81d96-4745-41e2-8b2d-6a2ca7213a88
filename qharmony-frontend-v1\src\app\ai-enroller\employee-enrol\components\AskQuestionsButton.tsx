'use client';

import React from 'react';

interface AskQuestionsButtonProps {
  onClick: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export default function AskQuestionsButton({ onClick, className, style }: AskQuestionsButtonProps) {
  const defaultStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    color: '#374151',
    border: '1px solid #d1d5db',
    borderRadius: '8px',
    backgroundColor: 'white',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
    fontSize: '14px',
    fontWeight: '500',
    ...style
  };

  return (
    <button
      onClick={onClick}
      style={defaultStyle}
      className={className}
      onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
      onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
    >
      <span style={{ color: '#2563eb' }}>❓</span>
      Ask Questions
    </button>
  );
}