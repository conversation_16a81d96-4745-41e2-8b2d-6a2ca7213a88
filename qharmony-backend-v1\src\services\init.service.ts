// import AWSService from './aws';
import AzureBlobService from './azure';
import EnvService from './env.service';
import MongooseService from './mongoose.service';
import { FirebaseDomainChecker } from '../utils/firebase-domain-checker';

class InitService {
  public static async init() {
    try {
      // Initialize environment variables first (synchronous)
      EnvService.init();

      // Wait for MongoDB connection to complete before proceeding
      await MongooseService.init();

      // Initialize other services (synchronous)
      // AWSService.init();
      AzureBlobService.init();

      // Check Firebase domain configuration
      FirebaseDomainChecker.checkCurrentDomain();

      console.log('✅ All services initialized successfully');
      return null;

    } catch (error) {
      console.error('❌ Service initialization failed:', error);
      throw error; // Re-throw so the caller can handle the failure
    }
  }
}

export default InitService;
