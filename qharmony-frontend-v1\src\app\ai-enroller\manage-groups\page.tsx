'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  HiOutlineSearch,
  HiOutlinePlus,
  HiOutlineArrowLeft,
  HiOutlineUsers,
  HiOutlineOfficeBuilding,
  HiOutlineClipboardList,
  HiOutlineChartBar
} from 'react-icons/hi';
import ProtectedRoute from '@/components/ProtectedRoute';
import { getAllCompaniesUnderBroker } from '@/middleware/company_middleware';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import AddNewGroupModal from '../components/AddNewGroupModal';
import { getPlanAssignmentsByCompany, getBrokerPlanAssignmentsCount } from './services/planAssignmentApi';
import './manage-groups.css';

interface Company {
  _id: string;
  name: string;
  industry: string;
  companySize: number;
  location: string;
  adminEmail: string;
  isActivated: boolean;
  planCount?: number;
}

const ManageGroupsPage: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);

  const managedCompanies = useSelector((state: RootState) => state.user.managedCompanies);

  // Function to fetch plan assignments count for the broker
  const fetchPlanAssignmentsCount = async (userId: string) => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      console.log('🔍 Fetching plan assignments count for userId:', userId);
      console.log('🔍 API_BASE_URL:', API_BASE_URL);

      // Get all companies under this broker
      const companiesResponse = await fetch(`${API_BASE_URL}/admin/all-companies`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      console.log('🏢 Companies response status:', companiesResponse.status);

      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        const companies = companiesData.companies || [];
        console.log('🏢 Found companies:', companies.length, companies);

        let totalPlanAssignments = 0;

        // For each company, fetch plan assignments using the existing API
        for (const company of companies) {
          try {
            console.log(`📋 Fetching assignments for company: ${company.name} (${company._id})`);
            const assignmentsResult = await getPlanAssignmentsByCompany(company._id, { includePlanData: false });

            console.log(`📋 Assignments result for ${company.name}:`, assignmentsResult);

            if (assignmentsResult.success && assignmentsResult.data?.assignments) {
              const companyAssignments = assignmentsResult.data.assignments.length;
              console.log(`📋 Company ${company.name} has ${companyAssignments} assignments`);
              totalPlanAssignments += companyAssignments;
            } else {
              console.warn(`❌ Failed to fetch assignments for ${company.name}:`, assignmentsResult.error);
            }
          } catch (error) {
            console.warn(`❌ Error fetching assignments for company ${company._id}:`, error);
          }
        }

        // Also check broker's own company
        try {
          console.log('🏢 Checking broker\'s own company...');
          const ownCompanyResponse = await fetch(`${API_BASE_URL}/employee/company-details`, {
            headers: {
              'Content-Type': 'application/json',
              'user-id': userId,
            },
          });

          console.log('🏢 Own company response status:', ownCompanyResponse.status);

          if (ownCompanyResponse.ok) {
            const ownCompanyData = await ownCompanyResponse.json();
            console.log('🏢 Own company data:', ownCompanyData);

            if (ownCompanyData.company && ownCompanyData.company.isBrokerage) {
              console.log(`📋 Fetching assignments for broker's own company: ${ownCompanyData.company.name} (${ownCompanyData.company._id})`);

              const assignmentsResult = await getPlanAssignmentsByCompany(ownCompanyData.company._id, { includePlanData: false });
              console.log('📋 Own company assignments result:', assignmentsResult);

              if (assignmentsResult.success && assignmentsResult.data?.assignments) {
                const ownCompanyAssignments = assignmentsResult.data.assignments.length;
                console.log(`📋 Broker's own company has ${ownCompanyAssignments} assignments`);
                totalPlanAssignments += ownCompanyAssignments;
              } else {
                console.warn('❌ Failed to fetch broker\'s own company assignments:', assignmentsResult.error);
              }
            } else {
              console.log('🏢 User does not have a brokerage company or company not found');
            }
          }
        } catch (error) {
          console.warn('❌ Failed to fetch broker\'s own company assignments:', error);
        }

        console.log('✅ Total plan assignments managed:', totalPlanAssignments);
        setPlansManaged(totalPlanAssignments);
      } else {
        console.error('❌ Failed to fetch companies:', companiesResponse.status);
        setPlansManaged(0);
      }
    } catch (error) {
      console.error('❌ Error fetching plan assignments count:', error);
      setPlansManaged(0);
    }
  };

  // Optimized function to fetch plan assignments count using broker API
  const fetchPlanAssignmentsCountOptimized = async () => {
    try {
      console.log('🔍 Fetching broker plan assignments count using optimized API');

      // Use the optimized broker count API
      const countResult = await getBrokerPlanAssignmentsCount();

      if (countResult.success && countResult.data) {
        const totalCount = countResult.data.count;
        console.log('✅ Total plan assignments managed by broker (optimized):', totalCount);
        setPlansManaged(totalCount);
      } else {
        console.warn('❌ Failed to fetch broker plan assignments count:', countResult.error);
        setPlansManaged(0);
      }
    } catch (error) {
      console.error('❌ Error fetching plan assignments count (optimized):', error);
      setPlansManaged(0);
    }
  };

  const fetchDashboardData = useCallback(async () => {
    try {
      // Only access localStorage in browser environment
      if (typeof window !== 'undefined') {
        const userId = localStorage.getItem('userid1') || '6838677aef6db0212bcfdacd';
        await getAllCompaniesUnderBroker(dispatch, userId);
        // Try optimized API first, fallback to original if needed
        await fetchPlanAssignmentsCountOptimized();
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  }, [dispatch]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const handleNewGroup = () => {
    setShowAddModal(true);
  };

  const handleExistingGroup = () => {
    router.push('/ai-enroller/manage-groups/select-company');
  };

  const handleBackToMain = () => {
    router.push('/ai-enroller');
  };

  // State for plan assignments count
  const [plansManaged, setPlansManaged] = useState(0);

  // Calculate dashboard stats
  const totalCompanies = managedCompanies?.length || 0;
  const totalEmployees = managedCompanies?.reduce((sum, company) => sum + company.companySize, 0) || 0;

  if (loading) {
    return (
      <div className="broker-dashboard">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="broker-dashboard">
      {/* Header Section */}
      <div className="dashboard-header">
        <div className="header-content">
          <h1 className="page-title">Broker Dashboard</h1>
          <p className="subtitle-text">Manage employer groups and benefit plans with ease. Your one-stop solution for comprehensive benefits administration.</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <HiOutlineOfficeBuilding size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-number">{totalCompanies}</div>
            <div className="stat-label">Active Companies</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <HiOutlineUsers size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-number">{totalEmployees.toLocaleString()}</div>
            <div className="stat-label">Total Employees</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <HiOutlineClipboardList size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-number">{plansManaged}</div>
            <div className="stat-label">Plan Assignments</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h2 className="section-header">Quick Actions</h2>
        <p className="body-text">Choose how you&apos;d like to get started</p>

        <div className="action-cards">
          <div className="action-card" onClick={handleNewGroup}>
            <div className="action-icon">
              <HiOutlinePlus size={32} />
            </div>
            <div className="action-content">
              <h3 className="section-header" style={{ fontSize: '18px' }}>New Group</h3>
              <p className="body-text">Setting up benefits for a new organization or group that hasn&apos;t had coverage before</p>
              <div className="action-tags">
                <span className="tag">Fresh start</span>
                <span className="tag">New enrollment</span>
              </div>
            </div>
          </div>

          <div className="action-card" onClick={handleExistingGroup}>
            <div className="action-icon">
              <HiOutlineOfficeBuilding size={32} />
            </div>
            <div className="action-content">
              <h3 className="section-header" style={{ fontSize: '18px' }}>Existing Group</h3>
              <p className="body-text">Adding or modifying benefits for an organization that already has some coverage in place</p>
              <div className="action-tags">
                <span className="tag">Active enrollment</span>
                <span className="tag">Plan changes</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Back Button */}
      <div className="back-button-container">
        <button className="back-button" onClick={handleBackToMain}>
          <HiOutlineArrowLeft size={20} />
          Back to Main
        </button>
      </div>

      {/* Add New Group Modal */}
      {showAddModal && (
        <AddNewGroupModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            fetchDashboardData();
          }}
        />
      )}
    </div>
    </ProtectedRoute>
  );
};

export default ManageGroupsPage;
