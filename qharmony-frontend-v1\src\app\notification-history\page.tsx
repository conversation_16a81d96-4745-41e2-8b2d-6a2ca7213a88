"use client";

import { useState, useEffect } from "react";
import {
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useRouter } from "next/navigation";
import { getRequest } from "@/APILayer/axios_helper";

interface NotificationType {
  _id: string;
  message: string;
  createdAt: string;
}

const NotificationHistory = () => {
  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const [isNotFound, setIsNotFound] = useState(false); // New state for 404 status
  const router = useRouter();

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const response = await getRequest("/notifications");
        if (response.success) {
          setNotifications(response.data);
          setIsNotFound(false); // Reset 404 state if data is found
        }
      } catch (error: any) {
        if (error.response?.status === 404) {
          setIsNotFound(true); // Set 404 state if status code is 404
        } else {
          console.error("Error fetching notifications:", error);
        }
      }
    };
    fetchNotifications();
  }, []);

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6FA",
          px: 4,
          py: 2,
          width: "100%",
          height: "100vh",
          overflow: "hidden",
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 4,
            mt: 3,
          }}
        >
          <Typography
            sx={{
              fontWeight: 600,
              fontSize: "28px",
              color: "black",
              lineHeight: "34px",
              textAlign: "left",
            }}
          >
            Notification History
          </Typography>
        </Box>

        {/* Table */}
        <TableContainer
          component={Paper}
          sx={{
            maxHeight: "calc(100vh - 150px)",
            overflow: "auto"
          }}
        >
          <Table
            stickyHeader
            sx={{
              minWidth: 650,
              tableLayout: "fixed", // Ensures columns have fixed widths
            }}
            aria-label="notification history table"
          >
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold', width: '10%' }}>S.No</TableCell>
                <TableCell sx={{ fontWeight: 'bold', width: '20%' }}>Notification ID</TableCell>
                <TableCell sx={{ fontWeight: 'bold', width: '50%' }}>Message Text</TableCell>
                <TableCell sx={{ fontWeight: 'bold', width: '20%' }}>Date</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isNotFound ? (
                <TableRow>
                  <TableCell colSpan={4} sx={{ textAlign: 'center', fontStyle: 'italic' }}>
                    No notifications found
                  </TableCell>
                </TableRow>
              ) : (
                notifications.map((notification, index) => (
                  <TableRow
                    key={notification._id}
                    onClick={() => router.push(`/notifications-analytics/${notification._id}`)}
                    sx={{
                      '&:last-child td, &:last-child th': { border: 0 },
                      cursor: 'pointer',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)'
                      }
                    }}
                  >
                    <TableCell sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {index + 1}
                    </TableCell>
                    <TableCell sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {notification._id}
                    </TableCell>
                    <TableCell sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {notification.message}
                    </TableCell>
                    <TableCell sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {new Date(notification.createdAt).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </ProtectedRoute>
  );
};

export default withSidebar(NotificationHistory);