'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { HiOutlineCheckCircle, HiOutlineDownload, HiOutlineHome } from 'react-icons/hi';
import { getPlanAssignmentsByCompany } from '../../../services/planAssignmentApi';
import { getApiBaseUrl, getUserId } from '../../../../../../utils/env';

interface PlanAssignment {
  _id: string;
  planId: string;
  planName?: string;
  carrier?: string;
  type?: string;
  planCode?: string;
  coverageType?: string;
  coverageSubtype?: string;
  effectiveDate?: string;
  endDate?: string;
  status?: string;
}

interface Company {
  _id: string;
  companyName: string;
  employeeCount?: number;
}

export default function ConfirmationPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const companyId = params.companyId as string;
  const assignmentIds = searchParams.get('assignments')?.split(',') || [];

  const [company, setCompany] = useState<Company | null>(null);
  const [planAssignments, setPlanAssignments] = useState<PlanAssignment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCompanyAndPlanAssignments = async () => {
      try {
        setLoading(true);

        // Fetch company details using the correct endpoint
        const API_BASE_URL = getApiBaseUrl();
        const userId = getUserId();

        const companyResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/company-benefits-settings/company/${companyId}`, {
          headers: {
            'Content-Type': 'application/json',
            'user-id': userId,
          },
        });

        if (companyResponse.ok) {
          const companyData = await companyResponse.json();
          // Extract company info from the benefits settings response
          setCompany({
            _id: companyId,
            companyName: companyData.companyName || 'Company Name',
            employeeCount: companyData.employeeCount || 250
          });
        } else {
          console.error('Failed to fetch company details:', companyResponse.status);
          // Set fallback company data
          setCompany({
            _id: companyId,
            companyName: 'Company Name',
            employeeCount: 250
          });
        }

        // Fetch real plan assignments
        console.log('Fetching plan assignments for company:', companyId);
        console.log('Assignment IDs from URL:', assignmentIds);

        const planAssignmentsResponse = await getPlanAssignmentsByCompany(companyId);
        console.log('All plan assignments response:', planAssignmentsResponse);

        if (!planAssignmentsResponse.success || !planAssignmentsResponse.data || !planAssignmentsResponse.data.assignments || planAssignmentsResponse.data.assignments.length === 0) {
          console.log('No plan assignments found, using fallback data');
          setPlanAssignments([]);
          return;
        }

        const planAssignmentsData = planAssignmentsResponse.data.assignments;

        // Filter to only show the assignments that were confirmed
        const confirmedAssignments = planAssignmentsData.filter((assignment: any) =>
          assignmentIds.includes(assignment._id)
        );

        console.log('Confirmed assignments:', confirmedAssignments);

        // Fetch plan details for each assignment
        const assignmentsWithPlanDetails = await Promise.all(
          confirmedAssignments.map(async (assignment: any) => {
            try {
              const planResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${assignment.planId}`, {
                headers: {
                  'Content-Type': 'application/json',
                  'user-id': userId,
                },
              });

              if (planResponse.ok) {
                const planData = await planResponse.json();
                return {
                  ...assignment,
                  planName: planData.planName,
                  carrier: planData.carrier,
                  planCode: planData.planCode,
                  coverageType: planData.coverageType,
                  coverageSubtype: planData.coverageSubtype,
                };
              } else {
                console.error('Failed to fetch plan details for planId:', assignment.planId);
                return assignment;
              }
            } catch (error) {
              console.error('Error fetching plan details:', error);
              return assignment;
            }
          })
        );

        setPlanAssignments(assignmentsWithPlanDetails);
      } catch (error) {
        console.error('Error fetching data:', error);
        // Set fallback data even on error
        setCompany({
          _id: companyId,
          companyName: 'Company Name',
          employeeCount: 250
        });
        setPlanAssignments([]);
      } finally {
        setLoading(false);
      }
    };

    if (companyId && assignmentIds.length > 0) {
      console.log('Starting fetch with companyId:', companyId, 'assignmentIds:', assignmentIds);
      fetchCompanyAndPlanAssignments();
    } else {
      console.log('Skipping fetch - companyId:', companyId, 'assignmentIds:', assignmentIds);
      setLoading(false);
      // Set fallback data if no assignments
      setCompany({
        _id: companyId,
        companyName: 'Company Name',
        employeeCount: 250
      });
      setPlanAssignments([]);
    }
  }, [companyId, assignmentIds.join(',')]); // Use join to avoid array reference issues

  const handleDownloadSummary = () => {
    // Mock download functionality
    alert('Configuration summary downloaded successfully!');
  };

  const handleReturnToDashboard = () => {
    router.push('/ai-enroller');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading confirmation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb Navigation */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="text-green-600 text-sm">Home</span>
            </div>
            <span className="text-gray-400">›</span>
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="text-green-600 text-sm">Select Company</span>
            </div>
            <span className="text-gray-400">›</span>
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="text-green-600 text-sm">View Plans</span>
            </div>
            <span className="text-gray-400">›</span>
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="text-green-600 text-sm">Contributions</span>
            </div>
            <span className="text-gray-400">›</span>
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="text-green-600 text-sm">Review</span>
            </div>
            <span className="text-gray-400">›</span>
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="text-green-600 font-medium text-sm">Confirmation</span>
            </div>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-12 bg-white text-center">
        {/* Success Icon */}
        <div className="mb-8">
          <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <HiOutlineCheckCircle className="w-12 h-12 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Configuration Complete!</h1>
          <p className="text-lg text-gray-600">All benefit plans have been successfully configured for {company?.companyName}.</p>
        </div>

        {/* Success Message */}
        <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-8 text-left">
          <div className="flex items-center gap-2">
            <HiOutlineCheckCircle className="text-green-600 w-5 h-5" />
            <span className="text-green-800 font-medium">Success!</span>
            <span className="text-green-700">Your benefit plan configurations have been saved and are now active.</span>
          </div>
        </div>

        {/* Configuration Summary */}
        <div className="bg-white border border-gray-200 rounded-xl p-6 mb-8 text-left">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-8 h-8 bg-gray-100 rounded-xl flex items-center justify-center">
              <span className="text-gray-600 text-lg">📋</span>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Configuration Summary</h2>
              <p className="text-sm text-gray-600">
                {planAssignments.length} plan assignment{planAssignments.length !== 1 ? 's' : ''} configured for {company?.companyName}
              </p>
            </div>
          </div>

          <div className="space-y-4">
            {planAssignments.length > 0 ? (
              planAssignments.map((assignment) => (
                <div key={assignment._id} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {assignment.planName || `Plan ID: ${assignment.planId}`}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {assignment.coverageType || assignment.type || 'Coverage Type'}
                      {assignment.coverageSubtype && ` - ${assignment.coverageSubtype}`}
                    </p>
                    <p className="text-xs text-gray-500">
                      {assignment.carrier && `Carrier: ${assignment.carrier}`}
                      {assignment.planCode && ` | Code: ${assignment.planCode}`}
                    </p>
                    {assignment.effectiveDate && assignment.endDate && (
                      <p className="text-xs text-gray-500">
                        Effective: {new Date(assignment.effectiveDate).toLocaleDateString()} - {new Date(assignment.endDate).toLocaleDateString()}
                      </p>
                    )}
                    <p className="text-xs text-blue-600 font-medium">
                      Status: {assignment.status || 'Active'}
                    </p>
                  </div>
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No plan assignments found.</p>
                <p className="text-sm">Assignment IDs: {assignmentIds.join(', ')}</p>
              </div>
            )}
          </div>
        </div>

        {/* What's Next */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8 text-left">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">What&apos;s Next?</h3>
          <ul className="space-y-2 text-sm text-gray-700">
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Employee communications will be generated based on these configurations</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Plan documents will be updated with the new contribution rates</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Enrollment systems will be updated for the next plan year</span>
            </li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          <button
            onClick={handleDownloadSummary}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2"
          >
            <HiOutlineDownload className="w-5 h-5" />
            Download Summary
          </button>
          <button
            onClick={handleReturnToDashboard}
            className="px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors flex items-center gap-2"
          >
            <HiOutlineHome className="w-5 h-5" />
            Return to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}

