import axios from 'axios';
import EnvService from './env.service';

export interface ThreadResponse {
  threadId: string;
  message: string;
}

export interface AddMessageResponse {
  messageId: string;
  message: string;
}

// const API_KEY = EnvService.env().OPENAI_API_KEY;
const API_URL = 'https://api.openai.com/v1/threads';

class ThreadService {
  async createThread(): Promise<ThreadResponse> {
    const API_KEY = EnvService.env().OPENAI_API_KEY;
    // console.log("🚀 createThread ~ API_KEY:", API_KEY)
    try {
      const response = await axios.post(
        API_URL,
        {},
        {
          headers: {
            Authorization: `Bearer ${API_KEY}`,
            'Content-Type': 'application/json',
            'OpenAI-Beta': 'assistants=v1',
          },
        }
      );

      const threadId = response.data.id;
      const message = 'Thread created successfully';
      return { threadId, message };
    } catch (error) {
      throw new Error(`Error creating thread: ${error.message}`);
    }
  }

  async addMessageToThread(
    thread: string,
    userMessage: string
  ): Promise<AddMessageResponse> {
    const API_KEY = EnvService.env().OPENAI_API_KEY;
    // console.log("🚀 ~ ThreadService ~ addMessageToThread ~ API_KEY:", API_KEY)

    try {
      const response = await axios.post(
        `${API_URL}/${thread}/messages`,
        {
          role: 'user',
          content: userMessage,
        },
        {
          headers: {
            Authorization: `Bearer ${API_KEY}`,
            'Content-Type': 'application/json',
            'OpenAI-Beta': 'assistants=v1',
          },
        }
      );
      const messageId = response.data.id;
      const message = 'Added message successfully';

      return { messageId, message };
    } catch (error) {
      throw new Error(`Error adding message to thread: ${error.message}`);
    }
  }
}

export default ThreadService;
