# Hardcoded URIs Analysis

## Overview
This document identifies all hardcoded URIs, URLs, endpoints, and network addresses found throughout the QHarmony application that should be moved to environment variables or configuration files for better maintainability and deployment flexibility.

## 🔍 Critical Issues Found

### **1. Frontend API Base URL** ⚠️ HIGH PRIORITY
**File**: `qharmony-frontend-v1/src/APILayer/axios_helper.ts`
```typescript
export const BASE_URL = "https://api.benosphere.com"; // Base URL without /test
```
**Issue**: Production API URL hardcoded in frontend
**Impact**: Cannot easily switch between environments
**Recommendation**: Use environment variables

### **2. Redis Connection** ⚠️ HIGH PRIORITY  
**File**: `qharmony-backend-v1/src/nosql/redisClient.ts`
```typescript
const redisClient = new Redis({
  host: '127.0.0.1',
  port: 6379, // Default Redis port
  // TODO(ankit): Use env var
  password: 'qharmony', // If applicable
});
```
**Issue**: Redis host, port, and password hardcoded
**Impact**: Cannot deploy to different environments
**Recommendation**: Move to environment variables

### **3. Teams Bot API Endpoints** ⚠️ MEDIUM PRIORITY
**File**: `qharmony-backend-v1/src/controllers/group.controller.ts`
```typescript
let adaptiveCardApiEndpoint =
  branch === "main" ?
    "https://benosphere.azurewebsites.net/api/notification"
    : "https://qharmony-teams-bot-dev.azurewebsites.net/api/notification"
```
**Issue**: Teams bot URLs hardcoded with branch-based logic
**Impact**: Difficult to maintain multiple environments
**Recommendation**: Use environment variables for all endpoints

### **4. Finch Authorization URL** ⚠️ MEDIUM PRIORITY
**File**: `qharmony-backend-v1/src/services/finch.service.ts`
```typescript
const url = new URL('/authorize', 'https://connect.tryfinch.com/authorize');
```
**Issue**: Finch API base URL hardcoded
**Impact**: Cannot switch to sandbox/different Finch environments
**Recommendation**: Use environment variable

## 📋 Complete Inventory

### **Backend Hardcoded URIs**

#### **1. API Endpoints & External Services**
- **Finch API**: `https://connect.tryfinch.com/authorize` (finch.service.ts)
- **Teams Bot Dev**: `https://qharmony-teams-bot-dev.azurewebsites.net/api/notification`
- **Teams Bot Prod**: `https://benosphere.azurewebsites.net/api/notification`
- **S3 Document URL**: `https://qharmony-public.s3.us-east-1.amazonaws.com/meltano/Meltano_Handbook_People_Operations.pdf`

#### **2. Network Configuration**
- **Redis Host**: `127.0.0.1:6379` with password `qharmony`
- **App Binding**: `0.0.0.0` (app.ts line 21)
- **Default Port**: `8080` (app.ts line 11)

#### **3. Test URLs**
- **Test Base**: `http://127.0.0.1:8080` (comprehensive_pre_enrollment_test.js)
- **Connection Tests**: `http://localhost:8080`, `http://127.0.0.1:8080`, `http://0.0.0.0:8080`

### **Frontend Hardcoded URIs**

#### **1. API Configuration**
- **Production API**: `https://api.benosphere.com` (axios_helper.ts)
- **Admin Emails**: Hardcoded admin email list

#### **2. Image Domains**
- **S3 Domains**: `s3.amazonaws.com`, `**.s3.amazonaws.com`
- **Azure Blob**: `benosphere.blob.core.windows.net`

### **Teams App Hardcoded URIs**

#### **1. Development Environment**
- **Local Host**: `localhost:53000` (teamsapp.local.yml)
- **Local HTTPS**: `https://localhost:53000`
- **Teams URL**: `https://teams.microsoft.com/l/app/...`

### **Slack Configuration**

#### **1. Development Manifest** (`slack-config/manifest.json`)
- **OAuth Redirect**: `https://factual-conversely-lab.ngrok-free.app/slack/oauth/access-token`
- **Event URL**: `https://factual-conversely-lab.ngrok-free.app/slack/event`
- **Interaction URL**: `https://factual-conversely-lab.ngrok-free.app/slack/interaction`

#### **2. Production Manifest** (`slack-config/manifest-prod.json`)
- **OAuth Redirect**: `https://slack.getqharmony.com/slack/oauth/access-token`
- **Event URL**: `https://slack.getqharmony.com/slack/event`
- **Interaction URL**: `https://slack.getqharmony.com/slack/interaction`

### **Docker & Infrastructure**

#### **1. Docker Compose** (`docker-compose.yml`)
- **Redis Port**: `6379:6379`
- **MongoDB Port**: `27017:27017`
- **MongoDB Credentials**: `root:examplepassword`

#### **2. VS Code Settings**
- **Azure Subscription**: Hardcoded subscription ID and resource group

## 🛠️ Recommended Fixes

### **1. Environment Variables to Add**

#### **Backend (.env)**
```bash
# API Endpoints
FINCH_BASE_URL=https://connect.tryfinch.com
TEAMS_BOT_API_ENDPOINT=https://benosphere.azurewebsites.net/api/notification
TEAMS_BOT_DEV_API_ENDPOINT=https://qharmony-teams-bot-dev.azurewebsites.net/api/notification

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=qharmony

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Document URLs
EMPLOYEE_HANDBOOK_BASE_URL=https://qharmony-public.s3.us-east-1.amazonaws.com
```

#### **Frontend (.env)**
```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.benosphere.com
NEXT_PUBLIC_API_BASE_URL_DEV=http://localhost:8080

# Image Domains
NEXT_PUBLIC_S3_DOMAIN=s3.amazonaws.com
NEXT_PUBLIC_AZURE_BLOB_DOMAIN=benosphere.blob.core.windows.net
```

### **2. Code Changes Required**

#### **Fix Redis Client**
```typescript
// src/nosql/redisClient.ts
const redisClient = new Redis({
  host: EnvService.env().REDIS_HOST,
  port: parseInt(EnvService.env().REDIS_PORT),
  password: EnvService.env().REDIS_PASSWORD,
});
```

#### **Fix Frontend API Base**
```typescript
// src/APILayer/axios_helper.ts
export const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8080";
```

#### **Fix Teams Bot Endpoints**
```typescript
// src/controllers/group.controller.ts
const adaptiveCardApiEndpoint = EnvService.env().TEAMS_BOT_API_ENDPOINT;
```

#### **Fix Finch Service**
```typescript
// src/services/finch.service.ts
const url = new URL('/authorize', EnvService.env().FINCH_BASE_URL);
```

### **3. Configuration Management**

#### **Environment-Specific Configs**
- Create `.env.development`, `.env.staging`, `.env.production`
- Use environment-specific Slack manifests
- Implement config validation in EnvService

#### **Docker Environment**
- Use environment variables in docker-compose.yml
- Create docker-compose.override.yml for local development
- Use secrets management for production

## 🚨 Security Concerns

### **1. Exposed Credentials**
- Redis password in plain text
- MongoDB credentials in docker-compose
- Hardcoded API keys in some test files

### **2. Environment Leakage**
- Production URLs in development code
- Test credentials that might be real

## 📊 Priority Matrix

### **🔴 Critical (Fix Immediately)**
1. Redis connection configuration
2. Frontend API base URL
3. Database credentials in docker-compose

### **🟡 High (Fix Soon)**
1. Teams bot API endpoints
2. Finch API base URL
3. Slack webhook URLs

### **🟢 Medium (Fix When Convenient)**
1. Test file URLs
2. Documentation URLs
3. Image domain configurations

## ✅ Implementation Status

### **🎯 Completed**
- ✅ **FRONTEND_BASE_URL** added to EnvService in backend repository
  - Added to `EnvVariables` type definition
  - Added to `envVariables` validation object
  - Ready for use: `EnvService.env().FRONTEND_BASE_URL`

### **Usage Example**
```typescript
// Example: Backend making requests to frontend
import EnvService from '../services/env.service';

const frontendUrl = EnvService.env().FRONTEND_BASE_URL;
const redirectUrl = `${frontendUrl}/dashboard`;
```

### **Environment Variable to Add**
```bash
# Add to .env file
FRONTEND_BASE_URL=https://app.benosphere.com
# or for development
FRONTEND_BASE_URL=http://localhost:3000
```

## ✅ Next Steps

1. ✅ **Add missing environment variables** to EnvService - **FRONTEND_BASE_URL COMPLETED**
2. **Update all hardcoded URLs** to use environment variables
3. **Create environment-specific configuration files**
4. **Update deployment scripts** to use proper environment configs
5. **Implement configuration validation** to catch missing variables early
6. **Document environment setup** for different deployment scenarios
