import axios from 'axios';
import { getChatbotUrl } from '../utils/env';

const CHATBOT_URL = getChatbotUrl();

export interface WellnessQuestion {
  id: string;
  text: string;
  type: string;
  options?: string[];
}

export interface WellnessAnswer {
  answers: Record<string, any>;
}

export interface WellnessPrediction {
  life_expectancy: {
    predicted_baseline_age: number;
    additional_adjustment: number;
    final_adjusted_age: number;
    survival_probability_past_100: number;
    message: string;
  };
  heart_disease_probability: number;
  stroke_prediction: {
    stroke_probability: number;
    stroke: string;
  };
}

export interface WellnessResult {
  predictions: WellnessPrediction;
  recommendations: string[];
  sources: string[];
}

export const wellnessService = {
  // Fetch wellness questions from API
  async getQuestions(): Promise<WellnessQuestion[]> {
    try {
      const response = await axios.get(`${CHATBOT_URL}/wellness/questions`);
      return response.data.data.questions;
    } catch (error) {
      console.error('Error fetching wellness questions:', error);
      throw error;
    }
  },

  // Submit answers and get predictions
  async submitAnswers(userId: string, teamId: string, answers: Record<string, any>): Promise<WellnessResult> {
    try {
      const payload = {
        user_id: userId,
        team_id: teamId,
        user_answer: {
          answers: answers
        }
      };
      
      console.log("Sending payload to API:", payload);
      console.log("Endpoint URL:", `${CHATBOT_URL}/wellness/predictions`);
      
      const response = await axios.post(`${CHATBOT_URL}/wellness/predictions`, payload);
      console.log("API response:", response.data);
      return response.data.data;
    } catch (error) {
      console.error('Error submitting wellness answers:', error);
      if (axios.isAxiosError(error)) {
        console.error('Response data:', error.response?.data);
        console.error('Status code:', error.response?.status);
      }
      throw error;
    }
  }
};


