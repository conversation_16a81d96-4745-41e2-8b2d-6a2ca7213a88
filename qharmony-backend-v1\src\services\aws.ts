// import AWS from 'aws-sdk';
// import {
//   DeleteObjectRequest,
//   GetObjectRequest,
//   HeadObjectRequest,
//   ListObjectsV2Request,
//   PutObjectRequest,
// } from 'aws-sdk/clients/s3';
// import AbstractService from './service';
// import logger from '../utils/logger';

// class AWSService implements AbstractService {
//   static s3: AWS.S3;

//   static init(): void {
//     // Set region
//     AWS.config.update({
//       accessKeyId: process.env.AWS_KEY,
//       secretAccessKey: process.env.AWS_SECRET,
//       region: process.env.AWS_REGION,
//       signatureVersion: 'v4',
//     });
//     this.s3 = new AWS.S3();
//     logger.info(
//       `Finished setting up AWS service with key ${process.env.AWS_KEY} and secret ${process.env.AWS_SECRET}`
//     );
//   }

//   static async upload(params: PutObjectRequest) {
//     return await this.s3.upload(params).promise();
//   }

//   static async getAllObjects(params: ListObjectsV2Request) {
//     return (await this.s3.listObjectsV2(params).promise()).Contents;
//   }

//   static async getObject(params: GetObjectRequest) {
//     return (await this.s3.getObject(params).promise()).Body;
//   }

//   static async deleteObject(params: DeleteObjectRequest) {
//     return await this.s3.deleteObject(params).promise();
//   }

//   static async getObjectAttributes(params: HeadObjectRequest) {
//     return await this.s3.headObject(params).promise();
//   }

//   static async generatePresignedUrl(params: unknown) {
//     return await this.s3.getSignedUrlPromise('putObject', params);
//   }

//   // generate a presigned url to download an object
//   static async generatePresignedUrlForDownload(params: unknown) {
//     return await this.s3.getSignedUrlPromise('getObject', params);
//   }

//   // generate a presigned url to view an image object
//   static async generateViewableImageURL(params: unknown) {
//     return await this.s3.getSignedUrlPromise('getObject', params);
//   }

//   static async createBucket(bucketName: string) {
//     const params: AWS.S3.CreateBucketRequest = {
//       Bucket: bucketName,
//     };

//     try {
//       const data = await this.s3.createBucket(params).promise();
//       logger.info(`Bucket created successfully: ${data.Location}`);
//       return data;
//     } catch (error) {
//       if (
//         error.code === 'BucketAlreadyExists' ||
//         error.code === 'BucketAlreadyOwnedByYou'
//       ) {
//         logger.warn(`Bucket ${bucketName} already exists.`);
//         return { Location: `http://${bucketName}.s3.amazonaws.com/` };
//       }
//       logger.error('Error creating bucket:', error);
//       throw error;
//     }
//   }

//   static async bucketExists(bucketName: string): Promise<boolean> {
//     try {
//       await this.s3.headBucket({ Bucket: bucketName }).promise();
//       return true;
//     } catch (error) {
//       if (error.code === 'NotFound') {
//         return false;
//       }
//       throw error; // Re-throw if it's a different error
//     }
//   }
// }

// export default AWSService;
