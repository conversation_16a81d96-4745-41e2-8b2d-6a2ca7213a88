/**
 * AGE-BANDED CALCULATIONS UNIT TEST
 * Tests the cost calculation logic directly without requiring API server
 */

// Mock the CostCalculationService logic
class MockCostCalculationService {
  
  /**
   * Calculate age-banded cost based on employee age
   * 🎯 UPDATED: Supports both fixed amounts and multiplier factors
   */
  static calculateAgeBandedCost(planAssignment, employeeAge = 30, tierDetails) {
    if (!planAssignment.ageBandedRates || !Array.isArray(planAssignment.ageBandedRates)) {
      return tierDetails.totalCost || 0;
    }

    // Find the appropriate age band
    const ageBand = planAssignment.ageBandedRates.find((band) =>
      employeeAge >= band.ageMin && employeeAge <= band.ageMax
    );

    if (ageBand) {
      const baseCost = tierDetails.totalCost || 0;

      // Support both types: multiplier (industry standard) and fixed (QHarmony legacy)
      if (ageBand.type === 'multiplier') {
        // MULTIPLIER: Scale the base cost by factor
        // Example: baseCost (1000) * rate (1.3) = 1300
        return baseCost * (ageBand.rate || 1.0);
      } else {
        // FIXED: Add fixed amount to base cost (default behavior)
        // Example: baseCost (1000) + rate (300) = 1300
        return baseCost + (ageBand.rate || 0);
      }
    }

    // Fallback to tier cost if no age band found
    return tierDetails.totalCost || 0;
  }

  /**
   * Calculate salary-based cost
   * 🎯 NEW: Supports both fixed amounts and multiplier factors (like age-banded)
   */
  static calculateSalaryBasedCost(planAssignment, tierDetails, employeeSalary) {
    if (!employeeSalary || employeeSalary <= 0) {
      console.warn('Warning: Salary-based calculation requires valid employee salary');
      return tierDetails.totalCost || 0;
    }

    // Check if plan assignment has salary-based rates
    if (planAssignment.salaryBasedRates && Array.isArray(planAssignment.salaryBasedRates)) {
      // Find appropriate salary band
      const salaryBand = planAssignment.salaryBasedRates.find((band) =>
        employeeSalary >= band.salaryMin && employeeSalary <= band.salaryMax
      );

      if (salaryBand) {
        const baseCost = tierDetails.totalCost || 0;

        // Support both types: multiplier (industry standard) and fixed (QHarmony legacy)
        if (salaryBand.type === 'multiplier') {
          // MULTIPLIER: Scale the base cost by factor
          // Example: baseCost (1000) * rate (1.2) = 1200
          return baseCost * (salaryBand.rate || 1.0);
        } else {
          // FIXED: Add fixed amount to base cost (default behavior)
          // Example: baseCost (1000) + rate (200) = 1200
          return baseCost + (salaryBand.rate || 0);
        }
      }
    }

    // Check if plan assignment has salary percentage
    if (planAssignment.salaryPercentage && typeof planAssignment.salaryPercentage === 'number') {
      const annualCost = employeeSalary * (planAssignment.salaryPercentage / 100);
      const monthlyCost = annualCost / 12;

      // Salary percentage should REPLACE tier cost, not add to it
      return monthlyCost;
    }

    // Fallback to tier cost
    return tierDetails.totalCost || 0;
  }

  /**
   * Apply contribution policies with explicit adjustment tracking
   */
  static applyContributionPolicies(totalCost, employerContribution, employeeContribution, tierDetails, wasAdjustedByRateStructure) {
    // 🎯 PRIORITY 1: Use tier-specific costs when available
    if (tierDetails &&
        typeof tierDetails.employeeCost === 'number' &&
        typeof tierDetails.employerCost === 'number' &&
        tierDetails.employeeCost >= 0 &&
        tierDetails.employerCost >= 0) {

      const originalTierTotal = tierDetails.employeeCost + tierDetails.employerCost;

      // 🎯 IMPROVED: Use explicit flag instead of cost difference detection
      if (!wasAdjustedByRateStructure) {
        // No rate structure adjustment - use tier costs directly
        return {
          employeeAmount: Math.round(tierDetails.employeeCost * 100) / 100,
          employerAmount: Math.round(tierDetails.employerCost * 100) / 100,
          totalAmount: Math.round(totalCost * 100) / 100
        };
      } else {
        // Rate structure adjusted the cost - scale proportionally
        const scaleFactor = totalCost / originalTierTotal;

        console.log(`Rate structure adjustment detected: ${originalTierTotal} -> ${totalCost} (factor: ${scaleFactor.toFixed(3)}). Scaling tier costs proportionally.`);

        return {
          employeeAmount: Math.round(tierDetails.employeeCost * scaleFactor * 100) / 100,
          employerAmount: Math.round(tierDetails.employerCost * scaleFactor * 100) / 100,
          totalAmount: Math.round(totalCost * 100) / 100
        };
      }
    }

    // Fallback to contribution policies (simplified for testing)
    const employerAmount = totalCost * 0.8; // 80% employer
    const employeeAmount = totalCost * 0.2; // 20% employee

    return {
      employeeAmount: Math.round(employeeAmount * 100) / 100,
      employerAmount: Math.round(employerAmount * 100) / 100,
      totalAmount: Math.round(totalCost * 100) / 100
    };
  }

  /**
   * Main calculation method
   */
  static calculateEnrollmentCost(input) {
    const { planAssignment, employeeAge, selectedTier, employeeSalary } = input;

    // Find tier details
    const tierDetails = planAssignment.coverageTiers.find(tier => tier.tierName === selectedTier);
    if (!tierDetails) {
      return { success: false, error: `Coverage tier '${selectedTier}' not found` };
    }

    // Calculate base cost based on rate structure
    let baseCost = 0;
    let wasAdjusted = false;

    switch (planAssignment.rateStructure) {
      case 'Age-Banded':
        baseCost = this.calculateAgeBandedCost(planAssignment, employeeAge, tierDetails);
        wasAdjusted = Math.abs(baseCost - (tierDetails.totalCost || 0)) > 0.01;
        break;
      case 'Salary-Based':
        baseCost = this.calculateSalaryBasedCost(planAssignment, tierDetails, employeeSalary);
        wasAdjusted = Math.abs(baseCost - (tierDetails.totalCost || 0)) > 0.01;
        break;
      default:
        baseCost = tierDetails.totalCost || 0;
        wasAdjusted = false;
    }

    // Apply contribution policies
    const contribution = this.applyContributionPolicies(
      baseCost,
      planAssignment.employerContribution,
      planAssignment.employeeContribution,
      tierDetails,
      wasAdjusted
    );

    return {
      success: true,
      cost: contribution
    };
  }
}

// Test data
const testPlanAssignmentFixed = {
  rateStructure: 'Age-Banded',
  ageBandedRates: [
    { ageMin: 18, ageMax: 29, rate: 100, type: 'fixed' },
    { ageMin: 30, ageMax: 39, rate: 200, type: 'fixed' },
    { ageMin: 40, ageMax: 49, rate: 300, type: 'fixed' }
  ],
  coverageTiers: [
    {
      tierName: 'Family',
      totalCost: 1000.00,
      employeeCost: 200.00,  // 20% employee share
      employerCost: 800.00   // 80% employer share
    }
  ],
  employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
  employeeContribution: { contributionType: 'Remainder', contributionAmount: 0 }
};

const testPlanAssignmentMultiplier = {
  rateStructure: 'Age-Banded',
  ageBandedRates: [
    { ageMin: 18, ageMax: 29, rate: 0.8, type: 'multiplier' },
    { ageMin: 30, ageMax: 39, rate: 1.0, type: 'multiplier' },
    { ageMin: 40, ageMax: 49, rate: 1.3, type: 'multiplier' }
  ],
  coverageTiers: [
    {
      tierName: 'Family',
      totalCost: 1000.00,
      employeeCost: 200.00,  // 20% employee share
      employerCost: 800.00   // 80% employer share
    }
  ],
  employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
  employeeContribution: { contributionType: 'Remainder', contributionAmount: 0 }
};

const testPlanAssignmentSalaryBased = {
  rateStructure: 'Salary-Based',
  salaryBasedRates: [
    { salaryMin: 30000, salaryMax: 50000, rate: 0.9, type: 'multiplier' },
    { salaryMin: 50001, salaryMax: 80000, rate: 1.0, type: 'multiplier' },
    { salaryMin: 80001, salaryMax: 120000, rate: 1.2, type: 'multiplier' }
  ],
  coverageTiers: [
    {
      tierName: 'Family',
      totalCost: 1000.00,
      employeeCost: 200.00,  // 20% employee share
      employerCost: 800.00   // 80% employer share
    }
  ],
  employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
  employeeContribution: { contributionType: 'Remainder', contributionAmount: 0 }
};

// Test functions
function testFixedAmountAgeBanded() {
  console.log('\n=== TEST 1: FIXED AMOUNT AGE-BANDED ===');
  
  // Test 35-year-old (30-39 age band, +$200)
  const result = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: testPlanAssignmentFixed,
    employeeAge: 35,
    selectedTier: 'Family'
  });

  console.log('Input:', {
    originalTierCost: 1000.00,
    ageAdjustment: '+$200 (fixed)',
    expectedTotal: 1200.00
  });

  console.log('Result:', {
    success: result.success,
    totalAmount: result.cost?.totalAmount,
    employeeAmount: result.cost?.employeeAmount,
    employerAmount: result.cost?.employerAmount
  });

  // Verify calculations
  const expectedTotal = 1200.00;
  const expectedEmployee = 240.00; // 200 * 1.2 = 240
  const expectedEmployer = 960.00;  // 800 * 1.2 = 960

  const isCorrect = result.success && 
    result.cost.totalAmount === expectedTotal &&
    result.cost.employeeAmount === expectedEmployee &&
    result.cost.employerAmount === expectedEmployer;

  console.log('✅ Test Result:', isCorrect ? 'PASSED' : 'FAILED');
  
  if (!isCorrect) {
    console.log('Expected:', { expectedTotal, expectedEmployee, expectedEmployer });
    console.log('Actual:', result.cost);
  }

  return isCorrect;
}

function testMultiplierFactorAgeBanded() {
  console.log('\n=== TEST 2: MULTIPLIER FACTOR AGE-BANDED ===');
  
  // Test 45-year-old (40-49 age band, 1.3x multiplier)
  const result = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: testPlanAssignmentMultiplier,
    employeeAge: 45,
    selectedTier: 'Family'
  });

  console.log('Input:', {
    originalTierCost: 1000.00,
    ageMultiplier: '1.3x',
    expectedTotal: 1300.00
  });

  console.log('Result:', {
    success: result.success,
    totalAmount: result.cost?.totalAmount,
    employeeAmount: result.cost?.employeeAmount,
    employerAmount: result.cost?.employerAmount
  });

  // Verify calculations
  const expectedTotal = 1300.00;
  const expectedEmployee = 260.00; // 200 * 1.3 = 260
  const expectedEmployer = 1040.00; // 800 * 1.3 = 1040

  const isCorrect = result.success && 
    result.cost.totalAmount === expectedTotal &&
    result.cost.employeeAmount === expectedEmployee &&
    result.cost.employerAmount === expectedEmployer;

  console.log('✅ Test Result:', isCorrect ? 'PASSED' : 'FAILED');
  
  if (!isCorrect) {
    console.log('Expected:', { expectedTotal, expectedEmployee, expectedEmployer });
    console.log('Actual:', result.cost);
  }

  return isCorrect;
}

function testCompositeRate() {
  console.log('\n=== TEST 3: COMPOSITE RATE (NO AGE ADJUSTMENT) ===');

  const compositePlan = {
    rateStructure: 'Composite',
    coverageTiers: [
      {
        tierName: 'Family',
        totalCost: 1000.00,
        employeeCost: 200.00,
        employerCost: 800.00
      }
    ]
  };

  const result = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: compositePlan,
    employeeAge: 35,
    selectedTier: 'Family'
  });

  console.log('Result:', {
    success: result.success,
    totalAmount: result.cost?.totalAmount,
    employeeAmount: result.cost?.employeeAmount,
    employerAmount: result.cost?.employerAmount
  });

  // Should use tier costs directly
  const isCorrect = result.success &&
    result.cost.totalAmount === 1000.00 &&
    result.cost.employeeAmount === 200.00 &&
    result.cost.employerAmount === 800.00;

  console.log('✅ Test Result:', isCorrect ? 'PASSED' : 'FAILED');

  return isCorrect;
}

function testSalaryBasedCalculation() {
  console.log('\n=== TEST 4: SALARY-BASED CALCULATION ===');

  // Test 90k salary employee (80001-120000 salary band, 1.2x multiplier)
  const result = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: testPlanAssignmentSalaryBased,
    employeeAge: 35,
    selectedTier: 'Family',
    employeeSalary: 90000
  });

  console.log('Input:', {
    originalTierCost: 1000.00,
    employeeSalary: 90000,
    salaryMultiplier: '1.2x',
    expectedTotal: 1200.00
  });

  console.log('Result:', {
    success: result.success,
    totalAmount: result.cost?.totalAmount,
    employeeAmount: result.cost?.employeeAmount,
    employerAmount: result.cost?.employerAmount
  });

  // Verify calculations
  const expectedTotal = 1200.00;
  const expectedEmployee = 240.00; // 200 * 1.2 = 240
  const expectedEmployer = 960.00;  // 800 * 1.2 = 960

  const isCorrect = result.success &&
    result.cost.totalAmount === expectedTotal &&
    result.cost.employeeAmount === expectedEmployee &&
    result.cost.employerAmount === expectedEmployer;

  console.log('✅ Test Result:', isCorrect ? 'PASSED' : 'FAILED');

  if (!isCorrect) {
    console.log('Expected:', { expectedTotal, expectedEmployee, expectedEmployer });
    console.log('Actual:', result.cost);
  }

  return isCorrect;
}

function testEdgeCase() {
  console.log('\n=== TEST 5: EDGE CASE - COMPOSITE WITH MISMATCHED COSTS ===');

  // This tests the scenario where tier costs don't add up to total
  // but it's a Composite rate structure (should still use tier costs directly)
  const edgeCasePlan = {
    rateStructure: 'Composite',
    coverageTiers: [
      {
        tierName: 'Family',
        totalCost: 1100.00,  // Different from employee + employer
        employeeCost: 200.00,
        employerCost: 800.00  // 200 + 800 = 1000, but total is 1100
      }
    ]
  };

  const result = MockCostCalculationService.calculateEnrollmentCost({
    planAssignment: edgeCasePlan,
    employeeAge: 35,
    selectedTier: 'Family'
  });

  console.log('Input (Edge Case):', {
    totalCost: 1100.00,
    employeeCost: 200.00,
    employerCost: 800.00,
    rateStructure: 'Composite'
  });

  console.log('Result:', {
    success: result.success,
    totalAmount: result.cost?.totalAmount,
    employeeAmount: result.cost?.employeeAmount,
    employerAmount: result.cost?.employerAmount
  });

  // Should use tier costs directly (NOT scale them) because it's Composite
  const isCorrect = result.success &&
    result.cost.totalAmount === 1100.00 &&
    result.cost.employeeAmount === 200.00 &&  // Should use original tier cost
    result.cost.employerAmount === 800.00;    // Should use original tier cost

  console.log('✅ Test Result:', isCorrect ? 'PASSED' : 'FAILED');
  console.log('✅ This verifies that Composite rates use tier costs directly, regardless of total cost mismatches');

  return isCorrect;
}

// Run all tests
function runAllTests() {
  console.log('🧪 RATE STRUCTURE CALCULATIONS UNIT TESTS');
  console.log('==========================================');

  const test1 = testFixedAmountAgeBanded();
  const test2 = testMultiplierFactorAgeBanded();
  const test3 = testCompositeRate();
  const test4 = testSalaryBasedCalculation();
  const test5 = testEdgeCase();

  console.log('\n📊 SUMMARY:');
  console.log('Fixed Amount Age-Banded:', test1 ? '✅ PASSED' : '❌ FAILED');
  console.log('Multiplier Factor Age-Banded:', test2 ? '✅ PASSED' : '❌ FAILED');
  console.log('Composite Rate (Control):', test3 ? '✅ PASSED' : '❌ FAILED');
  console.log('Salary-Based Calculation:', test4 ? '✅ PASSED' : '❌ FAILED');
  console.log('Edge Case (Composite Mismatch):', test5 ? '✅ PASSED' : '❌ FAILED');

  const allPassed = test1 && test2 && test3 && test4 && test5;
  console.log('\n🎯 OVERALL RESULT:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');

  return allPassed;
}

// Execute tests
runAllTests();
