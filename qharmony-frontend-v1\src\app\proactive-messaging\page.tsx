"use client";
import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useRouter } from 'next/navigation';
import { qHarmonyAdminEmail } from '@/APILayer/axios_helper';

// Dynamically import the ProactiveMessaging component
const DynamicProactiveMessaging = dynamic(
    () => import('./ProactiveMessaging'),
    {
        loading: () => <div>Loading...</div>,
        ssr: false // Disable server-side rendering for this component
    }
);

const ProactiveMessagingPage = () => {
    const userEmail = useSelector((state: RootState) => state.user.userProfile.email);
    const router = useRouter();
    const [isCheckingAuth, setIsCheckingAuth] = useState(true);

    // Check user authorization
    useEffect(() => {
        if (qHarmonyAdminEmail.includes(userEmail)) {
            setIsCheckingAuth(false); // User is authorized
        } else {
            router.push("/dashboard"); // Redirect unauthorized users
        }
    }, [userEmail, router]);

    // Render nothing while checking authorization
    if (isCheckingAuth) {
        return <div>Loading...</div>;
    }

    return (
        <React.Suspense fallback={<div>Loading...</div>}>
            <DynamicProactiveMessaging />
        </React.Suspense>
    );
};

export default ProactiveMessagingPage;
