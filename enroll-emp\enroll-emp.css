/* AI Enroller Employee Enrollment - Modern CSS */

/* CSS Variables for consistent theming */
:root {
  --primary-blue: #2563eb;
  --primary-blue-hover: #1d4ed8;
  --primary-blue-light: #dbeafe;
  --success-green: #059669;
  --success-green-light: #d1fae5;
  --warning-yellow: #d97706;
  --warning-yellow-light: #fef3c7;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --white: #ffffff;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base styles with SF Pro font */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
  line-height: 1.6;
  color: var(--gray-900);
  background-color: var(--white);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Layout containers */
.enrollment-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
}

.enrollment-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header section */
.enrollment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 20px 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-sm);
  color: var(--gray-700);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.back-button:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-icon {
  width: 48px;
  height: 48px;
  color: var(--primary-blue);
  background: var(--primary-blue-light);
  border-radius: var(--border-radius);
  padding: 12px;
}

.header-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-subtitle {
  font-size: 14px;
  color: var(--gray-500);
  margin: 4px 0 0 0;
  font-weight: 400;
}

/* Progress section */
.progress-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  margin-bottom: 24px;
  overflow: hidden;
}

.progress-header {
  padding: 24px 32px 20px;
  border-bottom: 1px solid var(--gray-100);
}

.progress-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.progress-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.progress-counter {
  background: var(--gray-100);
  color: var(--gray-700);
  padding: 6px 12px;
  border-radius: var(--border-radius-sm);
  font-size: 13px;
  font-weight: 500;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #000000 0%, #000000 100%);
  border-radius: 4px;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Step indicators */
.steps-container {
  padding: 24px 32px;
}

.steps-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  transition: var(--transition);
  border: 1px solid transparent;
}

.step-completed {
  background: var(--success-green-light);
  color: var(--success-green);
  border-color: rgba(5, 150, 105, 0.2);
}

.step-current {
  background: var(--primary-blue-light);
  color: var(--primary-blue);
  border-color: rgba(37, 99, 235, 0.2);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.step-pending {
  background: var(--gray-100);
  color: var(--gray-500);
  border-color: var(--gray-200);
}

.step-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.step-check {
  width: 14px;
  height: 14px;
  color: var(--success-green);
}

/* Content cards */
.content-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: var(--transition);
}

.content-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Chatbot section */
.chatbot-section {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  padding: 32px;
  margin-bottom: 16px;
}

.chatbot-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
}

.chatbot-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  background: var(--primary-blue-light);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 2px solid var(--primary-blue);
}

.chatbot-avatar img {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-sm);
}

.chatbot-content {
  flex: 1;
}

.chatbot-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 8px 0;
}

.chatbot-subtitle {
  font-size: 14px;
  color: var(--gray-500);
  margin: 0 0 16px 0;
  font-weight: 400;
}

.chatbot-message {
  font-size: 16px;
  line-height: 1.6;
  color: var(--gray-700);
  margin: 0;
}

/* Form section */
.form-section {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  padding: 32px;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 8px 0;
}

.form-subtitle {
  font-size: 14px;
  color: var(--gray-500);
  margin: 0 0 24px 0;
  line-height: 1.5;
}

/* Form controls */
.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 16px;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 16px;
  font-family: inherit;
  transition: var(--transition);
  background: var(--white);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input::placeholder {
  color: var(--gray-400);
}

.form-select {
  width: 100%;
  padding: 16px;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 16px;
  font-family: inherit;
  background: var(--white);
  cursor: pointer;
  transition: var(--transition);
}

.form-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
  width: 100%;
  padding: 16px;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  min-height: 120px;
  transition: var(--transition);
}

.form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: var(--border-radius);
  font-size: 16px;
  font-weight: 500;
  font-family: inherit;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: var(--primary-blue);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-blue-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border: 2px solid var(--gray-200);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-300);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-blue-light);
  transform: translateY(-1px);
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}

.btn-small {
  padding: 8px 16px;
  font-size: 14px;
}

/* Navigation buttons */
.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--gray-200);
}

/* Option cards/grid */
.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin: 24px 0;
}

.option-card {
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: 24px;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.option-card:hover {
  border-color: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.option-card.selected {
  border-color: var(--primary-blue);
  background: var(--primary-blue-light);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.option-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.option-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-blue-light);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue);
}

.option-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.option-description {
  font-size: 14px;
  color: var(--gray-600);
  line-height: 1.5;
  margin: 0;
}

/* Floating help */
.floating-help {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.help-trigger {
  width: 56px;
  height: 56px;
  background: var(--primary-blue);
  color: var(--white);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
}

.help-trigger:hover {
  background: var(--primary-blue-hover);
  transform: scale(1.05);
}

/* Responsive design */
@media (max-width: 768px) {
  .enrollment-container {
    padding: 16px;
  }

  .enrollment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-left {
    width: 100%;
    justify-content: space-between;
  }

  .chatbot-section,
  .form-section {
    padding: 24px;
  }

  .options-grid {
    grid-template-columns: 1fr;
  }

  .navigation-section {
    flex-direction: column;
    gap: 16px;
  }

  .floating-help {
    bottom: 16px;
    right: 16px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --white: #1f2937;
    --gray-50: #374151;
    --gray-100: #4b5563;
    --gray-200: #6b7280;
    --gray-900: #f9fafb;
    --gray-700: #e5e7eb;
    --gray-600: #f3f4f6;
    --gray-500: #d1d5db;
  }
}
