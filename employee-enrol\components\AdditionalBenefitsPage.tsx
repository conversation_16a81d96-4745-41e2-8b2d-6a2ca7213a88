'use client';

import React, { useState } from 'react';
import { User, CheckCircle, X, Heart, Shield } from 'lucide-react';

interface AdditionalBenefit {
  id: string;
  name: string;
  cost: number;
  features: string[];
  category: 'pet' | 'hospital';
}

interface AdditionalBenefitsPageProps {
  onBenefitsSelect: (benefits: AdditionalBenefit[]) => void;
}

const AdditionalBenefitsPage: React.FC<AdditionalBenefitsPageProps> = ({ onBenefitsSelect }) => {
  const [selectedBenefits, setSelectedBenefits] = useState<string[]>([]);

  const additionalBenefits: AdditionalBenefit[] = [
    {
      id: 'metlife-pet',
      name: 'MetLife Pet Plan',
      cost: 18.50,
      category: 'pet',
      features: [
        'Up to 90% reimbursement for vet bills',
        'No age limits for enrollment',
        'Coverage for accidents & illness',
        'Optional wellness add-on available'
      ]
    },
    {
      id: 'aspca-pet',
      name: 'ASPCA Pet Health Plan',
      cost: 22.00,
      category: 'pet',
      features: [
        'Comprehensive accident & illness coverage',
        'Hereditary condition coverage',
        '24/7 pet helpline',
        'No breed restrictions'
      ]
    },
    {
      id: 'hospital-cash',
      name: 'Hospital Cash Plan',
      cost: 8.75,
      category: 'hospital',
      features: [
        'Cash benefit for hospital stays',
        'No network restrictions',
        'Use funds however you need',
        'Covers unexpected medical expenses'
      ]
    },
    {
      id: 'critical-illness',
      name: 'Critical Illness Plan',
      cost: 12.50,
      category: 'hospital',
      features: [
        'Lump sum for critical illness diagnosis',
        'Cancer, heart attack, stroke coverage',
        'Help with out-of-pocket expenses',
        'Peace of mind protection'
      ]
    }
  ];

  const handleBenefitToggle = (benefitId: string) => {
    const newSelected = selectedBenefits.includes(benefitId)
      ? selectedBenefits.filter(id => id !== benefitId)
      : [...selectedBenefits, benefitId];
    
    setSelectedBenefits(newSelected);
    
    const selectedBenefitObjects = additionalBenefits.filter(benefit => 
      newSelected.includes(benefit.id)
    );
    onBenefitsSelect(selectedBenefitObjects);
  };

  const petBenefits = additionalBenefits.filter(b => b.category === 'pet');
  const hospitalBenefits = additionalBenefits.filter(b => b.category === 'hospital');

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{ 
          width: '40px', 
          height: '40px', 
          backgroundColor: '#dbeafe', 
          borderRadius: '8px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <User style={{ width: '20px', height: '20px', color: '#2563eb' }} />
        </div>
        <div style={{ 
          backgroundColor: '#f9fafb', 
          borderRadius: '8px', 
          padding: '16px', 
          maxWidth: '512px' 
        }}>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: 0 
          }}>
            ⭐ Want to add some extra protection? Here are some bonus benefits!
          </p>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: '8px 0 0 0' 
          }}>
            These are optional but can provide great peace of mind and savings. Take a look and see what interests you.
          </p>
        </div>
      </div>

      {/* Additional Benefits */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        border: '1px solid #e5e7eb', 
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <span style={{ fontSize: '18px' }}>⭐</span>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: '600', 
            color: '#111827',
            margin: 0
          }}>
            Additional Benefits Recommendations
          </h2>
        </div>

        <p style={{ 
          color: '#6b7280', 
          marginBottom: '24px',
          margin: 0
        }}>
          Based on your profile, here are some additional benefits you might find valuable:
        </p>

        {/* Pet Insurance Section */}
        <div style={{ 
          backgroundColor: '#fef3e2', 
          borderRadius: '8px', 
          padding: '20px', 
          marginBottom: '24px',
          border: '1px solid #fed7aa'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
            <Heart style={{ width: '20px', height: '20px', color: '#ea580c' }} />
            <span style={{ fontSize: '16px' }}>🐾</span>
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
              Are you a pet owner?
            </h3>
            <div style={{ 
              backgroundColor: '#ea580c', 
              color: 'white', 
              padding: '2px 8px', 
              borderRadius: '12px', 
              fontSize: '12px', 
              fontWeight: '500' 
            }}>
              Recommended
            </div>
          </div>
          
          <p style={{ color: '#92400e', fontSize: '14px', marginBottom: '16px', margin: 0 }}>
            Pet insurance can help you manage unexpected veterinary costs and ensure your furry family members get the care they need.
          </p>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {petBenefits.map((benefit) => (
              <div
                key={benefit.id}
                style={{
                  backgroundColor: 'white',
                  border: selectedBenefits.includes(benefit.id) ? '2px solid #ea580c' : '1px solid #fed7aa',
                  borderRadius: '8px',
                  padding: '16px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={() => handleBenefitToggle(benefit.id)}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
                  <div>
                    <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: 0 }}>
                      {benefit.name}
                    </h4>
                    <p style={{ color: '#ea580c', fontWeight: '600', margin: '4px 0 0 0' }}>
                      ${benefit.cost}/paycheck
                    </p>
                  </div>
                  {selectedBenefits.includes(benefit.id) && (
                    <CheckCircle style={{ width: '20px', height: '20px', color: '#ea580c' }} />
                  )}
                </div>
                
                <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                  {benefit.features.map((feature, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <CheckCircle style={{ width: '14px', height: '14px', color: '#10b981', flexShrink: 0 }} />
                      <span style={{ color: '#374151', fontSize: '13px' }}>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            
            <button
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '8px 16px',
                backgroundColor: 'white',
                border: '1px solid #fed7aa',
                borderRadius: '8px',
                color: '#92400e',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              <X size={16} />
              Skip Pet Insurance
            </button>
          </div>
        </div>

        {/* Hospital Indemnity Section */}
        <div style={{ 
          backgroundColor: '#eff6ff', 
          borderRadius: '8px', 
          padding: '20px',
          border: '1px solid #bfdbfe'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
            <Shield style={{ width: '20px', height: '20px', color: '#2563eb' }} />
            <span style={{ fontSize: '16px' }}>🏥</span>
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
              Want extra financial protection?
            </h3>
          </div>
          
          <p style={{ color: '#1e40af', fontSize: '14px', marginBottom: '16px', margin: 0 }}>
            Hospital indemnity insurance provides cash benefits to help with unexpected medical expenses.
          </p>

          <p style={{ color: '#1e40af', fontSize: '14px', fontWeight: '600', marginBottom: '16px', margin: 0 }}>
            Top 3 benefits:
          </p>
          
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
              <span style={{ color: '#2563eb' }}>+</span>
              <span style={{ color: '#1e40af', fontSize: '14px' }}>Cash payments directly to you for hospital stays</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
              <span style={{ color: '#2563eb' }}>+</span>
              <span style={{ color: '#1e40af', fontSize: '14px' }}>Helps cover deductibles and out-of-pocket costs</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ color: '#2563eb' }}>+</span>
              <span style={{ color: '#1e40af', fontSize: '14px' }}>No network restrictions - use any hospital</span>
            </div>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {hospitalBenefits.map((benefit) => (
              <div
                key={benefit.id}
                style={{
                  backgroundColor: 'white',
                  border: selectedBenefits.includes(benefit.id) ? '2px solid #2563eb' : '1px solid #bfdbfe',
                  borderRadius: '8px',
                  padding: '16px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={() => handleBenefitToggle(benefit.id)}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
                  <div>
                    <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: 0 }}>
                      {benefit.name}
                    </h4>
                    <p style={{ color: '#2563eb', fontWeight: '600', margin: '4px 0 0 0' }}>
                      ${benefit.cost}/paycheck
                    </p>
                  </div>
                  {selectedBenefits.includes(benefit.id) && (
                    <CheckCircle style={{ width: '20px', height: '20px', color: '#2563eb' }} />
                  )}
                </div>
                
                <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                  {benefit.features.map((feature, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <CheckCircle style={{ width: '14px', height: '14px', color: '#10b981', flexShrink: 0 }} />
                      <span style={{ color: '#374151', fontSize: '13px' }}>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            
            <button
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '8px 16px',
                backgroundColor: 'white',
                border: '1px solid #bfdbfe',
                borderRadius: '8px',
                color: '#1e40af',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              <X size={16} />
              Skip Hospital Indemnity
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdditionalBenefitsPage;
