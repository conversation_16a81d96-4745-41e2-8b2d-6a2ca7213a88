'use client';

import React, { useState, useEffect } from 'react';
import { X, AlertCircle, CheckCircle } from 'lucide-react';

interface EnrollmentProfileModalProps {
  open: boolean;
  onClose: () => void;
  onProfileUpdated: () => void;
  userDetails: any;
}

const EnrollmentProfileModal: React.FC<EnrollmentProfileModalProps> = ({
  open,
  onClose,
  onProfileUpdated,
  userDetails
}) => {
  // Form state for required fields
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [hireDate, setHireDate] = useState('');
  const [employeeClassType, setEmployeeClassType] = useState('');
  const [annualSalary, setAnnualSalary] = useState('');
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errors, setErrors] = useState<string[]>([]);

  // Employee class type options
  const employeeClassTypes = [
    'Full-Time',
    'Part-Time', 
    'Contractor',
    'Temporary',
    'Seasonal'
  ];

  useEffect(() => {
    if (userDetails && open) {
      // Parse name
      const nameParts = userDetails.name?.split(' ') || ['', ''];
      setFirstName(nameParts[0] || '');
      setLastName(nameParts.slice(1).join(' ') || '');
      
      // Set other fields
      setEmail(userDetails.email || '');
      setPhoneNumber(userDetails.details?.phoneNumber || '');
      setEmployeeClassType(userDetails.details?.employeeClassType || '');
      setAnnualSalary(userDetails.details?.annualSalary?.toString() || '');
      
      // Format dates
      if (userDetails.details?.dateOfBirth) {
        const dob = new Date(userDetails.details.dateOfBirth);
        setDateOfBirth(dob.toISOString().split('T')[0]);
      }
      
      if (userDetails.details?.hireDate) {
        const hire = new Date(userDetails.details.hireDate);
        setHireDate(hire.toISOString().split('T')[0]);
      }
    }
  }, [userDetails, open]);

  // Validation function
  const validateRequiredFields = (): string[] => {
    const validationErrors: string[] = [];
    
    if (!firstName.trim()) validationErrors.push('First name is required');
    if (!lastName.trim()) validationErrors.push('Last name is required');
    if (!email.trim()) validationErrors.push('Email is required');
    if (!phoneNumber.trim()) validationErrors.push('Phone number is required');
    if (!dateOfBirth) validationErrors.push('Date of birth is required for age-based cost calculations');
    if (!hireDate) validationErrors.push('Hire date is required for waiting period eligibility');
    if (!employeeClassType) validationErrors.push('Employee class type is required');
    
    // Validate email format
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      validationErrors.push('Please enter a valid email address');
    }
    
    // Validate annual salary if provided
    if (annualSalary && (isNaN(parseFloat(annualSalary)) || parseFloat(annualSalary) < 0)) {
      validationErrors.push('Annual salary must be a valid positive number');
    }
    
    return validationErrors;
  };

  const handleSave = async () => {
    // Validate required fields
    const validationErrors = validateRequiredFields();
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setLoading(true);
    setErrors([]);

    try {
      // Get user ID and company admin ID
      const getUserId = () => {
        return localStorage.getItem('userid1') || localStorage.getItem('userId');
      };

      const getCompanyAdminId = async () => {
        try {
          const response = await fetch('/api/company/admin-id', {
            headers: {
              'user-id': getUserId() || ''
            }
          });
          if (response.ok) {
            const data = await response.json();
            return data.adminId;
          }
        } catch (error) {
          console.error('Error getting company admin ID:', error);
        }
        return getUserId();
      };

      const userId = getUserId();
      const companyAdminId = await getCompanyAdminId();

      if (!userId) throw new Error('User ID not found');
      if (!companyAdminId) throw new Error('Company admin ID not found');

      // Prepare update data
      const updatedDetails = {
        name: `${firstName} ${lastName}`.trim(),
        email: email,
        details: {
          ...userDetails.details,
          phoneNumber: phoneNumber,
          dateOfBirth: new Date(dateOfBirth).toISOString(),
          hireDate: new Date(hireDate).toISOString(),
          employeeClassType: employeeClassType,
          ...(annualSalary && { annualSalary: parseFloat(annualSalary) })
        }
      };

      // Make API call
      const response = await fetch('/admin/update/employee', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId
        },
        body: JSON.stringify({
          adminId: companyAdminId,
          updatedDetails: updatedDetails
        })
      });

      if (response.ok) {
        setSuccessMessage('Profile updated successfully!');
        setTimeout(() => {
          setSuccessMessage('');
          onProfileUpdated();
          onClose();
        }, 1500);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setErrors([error instanceof Error ? error.message : 'Failed to update profile']);
    } finally {
      setLoading(false);
    }
  };

  if (!open) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        padding: '24px',
        width: '90%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        position: 'relative'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <div>
            <h2 style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#111827',
              margin: 0
            }}>
              Complete Required Profile Information
            </h2>
            <p style={{
              fontSize: '14px',
              color: '#6b7280',
              margin: '4px 0 0 0'
            }}>
              These fields are required for enrollment eligibility
            </p>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '4px'
            }}
          >
            <X size={24} style={{ color: '#6b7280' }} />
          </button>
        </div>

        {/* Error Messages */}
        {errors.length > 0 && (
          <div style={{
            backgroundColor: '#fef2f2',
            border: '1px solid #fecaca',
            borderRadius: '8px',
            padding: '12px',
            marginBottom: '20px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              marginBottom: '8px'
            }}>
              <AlertCircle size={16} style={{ color: '#dc2626' }} />
              <span style={{
                fontSize: '14px',
                fontWeight: '500',
                color: '#dc2626'
              }}>
                Please fix the following errors:
              </span>
            </div>
            <ul style={{
              margin: 0,
              paddingLeft: '20px',
              color: '#dc2626',
              fontSize: '14px'
            }}>
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Success Message */}
        {successMessage && (
          <div style={{
            backgroundColor: '#f0fdf4',
            border: '1px solid #bbf7d0',
            borderRadius: '8px',
            padding: '12px',
            marginBottom: '20px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <CheckCircle size={16} style={{ color: '#16a34a' }} />
            <span style={{
              fontSize: '14px',
              color: '#16a34a',
              fontWeight: '500'
            }}>
              {successMessage}
            </span>
          </div>
        )}

        {/* Form Fields */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {/* Name Fields */}
          <div style={{ display: 'flex', gap: '12px' }}>
            <div style={{ flex: 1 }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '6px'
              }}>
                First Name <span style={{ color: '#dc2626' }}>*</span>
              </label>
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none',
                  boxSizing: 'border-box'
                }}
              />
            </div>
            <div style={{ flex: 1 }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '6px'
              }}>
                Last Name <span style={{ color: '#dc2626' }}>*</span>
              </label>
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none',
                  boxSizing: 'border-box'
                }}
              />
            </div>
          </div>

          {/* Email Field */}
          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '6px'
            }}>
              Email <span style={{ color: '#dc2626' }}>*</span>
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              style={{
                width: '100%',
                padding: '10px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box'
              }}
            />
          </div>

          {/* Phone Number Field */}
          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '6px'
            }}>
              Phone Number <span style={{ color: '#dc2626' }}>*</span>
            </label>
            <input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              style={{
                width: '100%',
                padding: '10px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box'
              }}
            />
          </div>

          {/* Date Fields */}
          <div style={{ display: 'flex', gap: '12px' }}>
            <div style={{ flex: 1 }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '6px'
              }}>
                Date of Birth <span style={{ color: '#dc2626' }}>*</span>
              </label>
              <input
                type="date"
                value={dateOfBirth}
                onChange={(e) => setDateOfBirth(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none',
                  boxSizing: 'border-box'
                }}
              />
            </div>
            <div style={{ flex: 1 }}>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '6px'
              }}>
                Hire Date <span style={{ color: '#dc2626' }}>*</span>
              </label>
              <input
                type="date"
                value={hireDate}
                onChange={(e) => setHireDate(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  outline: 'none',
                  boxSizing: 'border-box'
                }}
              />
            </div>
          </div>

          {/* Employee Class Type */}
          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '6px'
            }}>
              Employee Class Type <span style={{ color: '#dc2626' }}>*</span>
            </label>
            <select
              value={employeeClassType}
              onChange={(e) => setEmployeeClassType(e.target.value)}
              style={{
                width: '100%',
                padding: '10px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box',
                backgroundColor: 'white'
              }}
            >
              <option value="">Select employee class type</option>
              {employeeClassTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          {/* Annual Salary (Optional) */}
          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '6px'
            }}>
              Annual Salary (Optional)
            </label>
            <input
              type="number"
              value={annualSalary}
              onChange={(e) => setAnnualSalary(e.target.value)}
              placeholder="Enter annual salary"
              style={{
                width: '100%',
                padding: '10px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box'
              }}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px',
          marginTop: '24px',
          paddingTop: '20px',
          borderTop: '1px solid #e5e7eb'
        }}>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              padding: '10px 20px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              backgroundColor: 'white',
              color: '#374151',
              fontSize: '14px',
              fontWeight: '500',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={loading}
            style={{
              padding: '10px 20px',
              border: 'none',
              borderRadius: '6px',
              backgroundColor: '#000000',
              color: 'white',
              fontSize: '14px',
              fontWeight: '500',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Saving...' : 'Save Profile'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnrollmentProfileModal;
