import mongoose, { Document, Model } from 'mongoose';
import logger from '../utils/logger';

const { Schema } = mongoose;

/**
 * Interface representing a notification.
 */
export interface NotificationDataInterface {
    _id?: mongoose.Types.ObjectId;
    message: string;
    sentToOrgs: string[];
    sentToGroups: string[];
    noOfUsersSentTo: {
        companyName: string;
        userCount: number;
    }[];
    totalUserCount: number; // New field for total user count
    buttons: string[];
    createdAt?: Date;    // Will be automatically handled by Mongoose
    updatedAt?: Date;    // Will be automatically handled by Mongoose
}

/**
 * Mongoose document interface (excluding _id conflict).
 */
interface NotificationDocument extends Document, Omit<NotificationDataInterface, '_id'> { }

class NotificationModelClass {
    private static notificationModel: Model<NotificationDocument>;

    /**
     * Initializes the Mongoose model.
     */
    public static initializeModel() {
        const schema = new Schema({
            message: { type: String, required: true },
            sentToOrgs: { type: [String], default: [] },
            sentToGroups: { type: [String], default: [] },
            noOfUsersSentTo: {
                type: [
                    {
                        companyName: { type: String, required: true },
                        userCount: { type: Number, required: true }
                    }
                ],
                default: []
            },
            totalUserCount: { type: Number, default: 0 }, // New field for total user count
            buttons: { type: [String], required: true }
        }, {
            timestamps: true  // This will automatically add createdAt and updatedAt fields
        });

        this.notificationModel = mongoose.model<NotificationDocument>('Notification', schema);
    }

    /**
     * Creates a new notification.
     * @param notificationData - The notification data.
     * @returns The created notification's ID as a string, or null if an error occurs.
     */
    public static async createNotification(notificationData: Omit<NotificationDataInterface, '_id' | 'createdAt' | 'updatedAt'>): Promise<string | null> {
        try {
            const notification = await this.notificationModel.create(notificationData);
            return notification._id.toString();
        } catch (error) {
            logger.error("Error creating notification:", error);
            return null;
        }
    }

    /**
     * Retrieves a notification by its MongoDB _id.
     * @param id - The MongoDB _id of the notification.
     * @returns The notification details if found, otherwise null.
     */
    public static async getNotificationById(id: mongoose.Types.ObjectId): Promise<NotificationDataInterface | null> {
        try {
            const notification = await this.notificationModel.findById(id).lean();
            // Use unknown as an intermediate type to fix the TypeScript error
            return notification as unknown as NotificationDataInterface | null;
        } catch (error) {
            logger.error(`Error fetching notification with _id: ${id}`, error);
            return null;
        }
    }

    /**
     * Retrieves all notifications sorted by the createdAt date in descending order.
     * @returns An array of notifications, or an empty array if an error occurs.
     */
    public static async getAllNotifications(): Promise<NotificationDataInterface[]> {
        try {
            const notifications = await this.notificationModel.find().sort({ createdAt: -1 }).lean();
            // Use unknown as an intermediate type to fix the TypeScript error
            return notifications as unknown as NotificationDataInterface[];
        } catch (error) {
            logger.error("Error fetching all notifications:", error);
            return [];
        }
    }
}

// Initialize the model
NotificationModelClass.initializeModel();

export default NotificationModelClass;
