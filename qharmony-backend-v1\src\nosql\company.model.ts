import mongoose, { Document, Model } from 'mongoose';
import logger from '../utils/logger';

const { Schema } = mongoose;

export interface CompanyDataInterface {
  _id?: mongoose.Types.ObjectId;
  name: string;
  companySize: number;
  industry: string;
  location: string;
  website: string;
  adminEmail: string;
  adminRole: string;
  brokerId: string; // New Field
  brokerageId: string; // New Field
  isBrokerage: boolean; // New Field
  isActivated: boolean;
  howHeard: string;
  referralSource: string;
  ein?: string; // Employer Identification Number (moved from User model)
  details?: {
    logo: string;
  };
  tenantId?: string
}

interface CompanyDocument extends Document, Omit<CompanyDataInterface, '_id'> { }
class CompanyModelClass {
  private static companyModel: Model<CompanyDocument>;

  public static initializeModel() {
    const schema = new Schema({
      name: String,
      companySize: Number,
      industry: String,
      location: String,
      website: String,
      adminEmail: String,
      adminRole: String,
      brokerId: String, // New Field
      brokerageId: String, // New Field
      isBrokerage: Boolean, // New Field
      isActivated: Boolean,
      howHeard: String,
      referralSource: String,
      ein: { // Employer Identification Number (moved from User model)
        type: String,
        validate: {
          validator: function(value: string) {
            if (!value) return true; // Allow empty for backward compatibility
            return /^\d{2}-\d{7}$/.test(value);
          },
          message: 'EIN must be in format XX-XXXXXXX (e.g., 12-3456789)'
        }
      },
      details: {
        logo: String,
      },
      tenantId: String
    });

    this.companyModel = mongoose.model<CompanyDocument>('Company', schema);
  }

  public static async addData(data: CompanyDataInterface): Promise<
    | (mongoose.Document<unknown, {}, CompanyDocument> &
      CompanyDocument & {
        _id: mongoose.Types.ObjectId;
      })
    | null
  > {
    try {
      const company = await this.companyModel.create(data);
      // Use type assertion to fix the TypeScript error
      return company as mongoose.Document<unknown, {}, CompanyDocument> &
        CompanyDocument & {
          _id: mongoose.Types.ObjectId;
        };
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  public static async getData(): Promise<CompanyDataInterface[]> {
    try {
      const data = (await this.companyModel.find()) as CompanyDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async getDataById(
    id: string
  ): Promise<CompanyDataInterface | null> {
    try {
      const data = (await this.companyModel.findById(
        id
      )) as CompanyDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  public static async getDataByAdminEmail({
    adminEmail,
  }: {
    adminEmail: string;
  }): Promise<CompanyDataInterface | null> {
    try {
      const data = (await this.companyModel.findOne({
        adminEmail,
      })) as CompanyDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  public static async updateData({
    id,
    data,
  }: {
    id: string;
    data: Partial<CompanyDataInterface>;
  }): Promise<void> {
    try {
      // make sure you update only the fields that are present in the data object
      await this.companyModel.findByIdAndUpdate(id, data);
    } catch (error) {
      console.error(error);
    }
  }
  public static async getAllData(): Promise<CompanyDataInterface[]> {
    try {
      const data = await this.companyModel.find().lean();
      return data.map((item) => ({
        _id: item._id,
        name: item.name,
        companySize: item.companySize,
        industry: item.industry,
        location: item.location,
        website: item.website,
        howHeard: item.howHeard,
        adminEmail: item.adminEmail,
        adminRole: item.adminRole,
        brokerId: item.brokerId,
        brokerageId: item.brokerageId,
        isBrokerage: item.isBrokerage,
        isActivated: item.isActivated,
        ein: item.ein,
      })) as CompanyDataInterface[];
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async getDataByBrokerId(
    brokerId: string
  ): Promise<CompanyDataInterface[]> {
    try {
      const data = await this.companyModel.find({ brokerId }).lean();
      return data.map((item) => ({
        _id: item._id,
        name: item.name,
        companySize: item.companySize,
        industry: item.industry,
        location: item.location,
        website: item.website,
        howHeard: item.howHeard,
        adminEmail: item.adminEmail,
        adminRole: item.adminRole,
        brokerId: item.brokerId,
        brokerageId: item.brokerageId,
        isBrokerage: item.isBrokerage,
        isActivated: item.isActivated,
        ein: item.ein,
      })) as CompanyDataInterface[];
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async getCompaniesWithTenantId(): Promise<Pick<CompanyDataInterface, '_id' | 'name'>[]> {
    try {
      const data = await this.companyModel.find(
        { tenantId: { $exists: true, $ne: null } },
      ).lean();

      // Use unknown as an intermediate type to fix the TypeScript error
      return data as unknown as Pick<CompanyDataInterface, '_id' | 'name'>[];
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  /**
   * Gets all companies that have a tenantId and includes user count for each company
   * @returns Array of companies with user counts
   */
  public static async getCompaniesWithTenantIdAndUserCount(): Promise<(CompanyDataInterface & { userCount: number })[]> {
    try {
      // First let's check what data we have in both collections
      const sampleCompany = await this.companyModel.findOne({ tenantId: { $exists: true } });
      console.log('Sample company _id:', sampleCompany?._id);

      // Try to find users directly
      const UserModel = mongoose.model('User');
      const allUsers = await UserModel.find({}).lean();
      console.log('All users:', JSON.stringify(allUsers, null, 2));

      const companies = await this.companyModel.aggregate([
        {
          $match: {
            tenantId: { $exists: true, $ne: null }
          }
        },
        {
          $lookup: {
            from: 'users',
            let: { companyId: { $toString: '$_id' } },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ['$companyId', '$$companyId']  // Match directly on companyId field
                  }
                }
              }
            ],
            as: 'users'
          }
        },
        {
          $addFields: {
            userCount: { $size: '$users' }
          }
        },
        {
          $project: {
            users: 0,
            tenantId: 0
          }
        }
      ]);

      console.log('Final companies result:', JSON.stringify(companies, null, 2));
      return companies;
    } catch (error) {
      logger.error('Error fetching companies with user count:', error);
      return [];
    }
  }

  /**
   * Fetches company details based on an array of company names and ensures they have a tenantId.
   * @param companyNames - Array of company names.
   * @returns Array of company objects that match the given names and have a tenantId.
   */
  public static async getCompaniesByNames(companyNames: string[]): Promise<any[]> {
    try {
      const companies = await this.companyModel.find({
        name: { $in: companyNames },
        tenantId: { $exists: true, $ne: null } // Ensure tenantId exists and is not null
      }).lean();
      return companies;
    } catch (error) {
      logger.error("Error fetching companies by names:", error);
      return [];
    }
  }

  /**
   * Fetches company details based on a unique tenantId.
   * @param tenantId - The unique tenant ID.
   * @returns The company details object that matches the given tenantId, or null if not found.
   */
  public static async getCompanyByTenantId(tenantId: string): Promise<CompanyDataInterface | null> {
    try {
      const company = await this.companyModel.findOne({ tenantId }).lean();
      // Use unknown as an intermediate type to fix the TypeScript error
      return company as unknown as CompanyDataInterface | null;
    } catch (error) {
      logger.error(`Error fetching company by tenantId: ${tenantId}`, error);
      return null;
    }
  }

  /**
   * Validates EIN format
   * @param ein - Employer Identification Number
   * @returns boolean - true if valid format or empty, false otherwise
   */
  public static validateEIN(ein: string): boolean {
    if (!ein) return true; // Allow empty for backward compatibility
    return /^\d{2}-\d{7}$/.test(ein);
  }
}

CompanyModelClass.initializeModel();

export default CompanyModelClass;
