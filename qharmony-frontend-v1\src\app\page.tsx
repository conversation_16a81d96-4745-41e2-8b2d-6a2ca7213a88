"use client";

import {
  <PERSON>,
  <PERSON>rid,
  <PERSON><PERSON>ield,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  CircularProgress,
  useMediaQuery,
} from "@mui/material";
import Image from "next/image";
import { SetStateAction, useEffect, useState } from "react";
import HarmonyLogo from "../../public/logo.png"; // Replace with your actual logo
import { selfOnboard } from "@/middleware/user_middleware";
import RightPanelOnlyComponent from "@/components/RightPanelOnlyComponent";
import { useAuth } from "@/components/AuthContext";
import { useRouter } from "next/navigation";
import withMobileEdgeFill from "@/components/mobile_edge_fill"; // Import the HOC


const SendMagicLinkComponent = () => {
  const { user } = useAuth();
  const router = useRouter(); // Initialize useRouter

  // Redirect to /dashboard if the user is logged in
  useEffect(() => {
    if (user) {
      router.push("/dashboard");
    }
  }, [user]);

  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");
  const [emailError, setEmailError] = useState(""); // New state for email error

  const handleLogoClick = () => {
    window.location.href = "https://benosphere.com/";
  };

  const handleEmailChange = (e: {
    target: { value: SetStateAction<string> };
  }) => {
    setEmail(e.target.value);
    setEmailError(""); // Clear error message when user starts typing
  };

  const handleConfirmClick = async () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email address."); // Set error message
      return;
    }

    setLoading(true); // Start loading
    const responseMessage = await selfOnboard(email);
    setResponseMessage(responseMessage);
    setLoading(false); // Stop loading
    setSuccess(true); // Set success state
  };

  return (
    <Box>
      {/* Logo at the top */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          mb: 5,
          cursor: "pointer",
          position: "absolute",
          top: "30px", // Moves the logo to the top
          left: "100px", // Align with the start of the text
        }}
        onClick={handleLogoClick}
      >
        <Image src={HarmonyLogo} alt="BenOsphere Logo" width={40} height={40} />
        <Typography
          variant="h6"
          sx={{ ml: 1, fontWeight: "800", color: "#ffffff" }}
        >
          BenOsphere
        </Typography>
      </Box>

      {responseMessage === "ask_admin_to_add" ? (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            color: "white",
            textAlign: "center",
            height: "100%", // Take up the entire height
          }}
        >
          <Typography sx={{ fontWeight: "bold", fontSize: "60px", mb: 2 }}>
            🤔 No account yet
          </Typography>
          <Typography
            variant="body1"
            sx={{ fontSize: "20px", color: "#bbbbbb" }}
          >
            Contact your company&apos;s admin HR to learn more on how to get
            access to BenOsphere. We will see you again shortly.
          </Typography>
        </Box>
      ) : responseMessage === "magic_link_sent" ? (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            color: "white",
            textAlign: "center",
            height: "100%", // Take up the entire height
          }}
        >
          <Typography sx={{ fontWeight: "bold", fontSize: "60px", mb: 2 }}>
            ✅ Magic Link Sent
          </Typography>
          <Typography
            variant="body1"
            sx={{ fontSize: "20px", color: "#bbbbbb" }}
          >
            Please check your email for the magic link to access BenOsphere. If
            it&apos;s not in your inbox, please check your spam or junk folder.
          </Typography>
        </Box>
      ) : (
        <Box sx={{ maxWidth: "600px", width: "100%", mt: 8 }}>
          <Typography
            variant="h4"
            sx={{
              fontSize: "70px", // Matches the font size in the screenshot
              fontWeight: "bold",
              mb: 2,
              lineHeight: "1.2",
              color: "#ffffff",
            }}
          >
            Maximize Your Employee Benefits
          </Typography>
          <Typography
            variant="body1"
            sx={{ mb: 6, fontSize: "18px", color: "#bbbbbb" }}
          >
            Enter your work email, and we&apos;ll send you a magic link to
            easily access all your benefits.
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column", // Stacks the text field and button
              alignItems: "stretch", // Ensures both elements stretch to full width
              mb: 3,
            }}
          >
            <TextField
              fullWidth
              variant="outlined"
              placeholder="<EMAIL>"
              value={email}
              onChange={handleEmailChange}
              sx={{
                bgcolor: "#333333",
                input: { color: "#ffffff" },
                borderRadius: "10px", // Added border radius to the background
                "& .MuiOutlinedInput-root": {
                  borderRadius: "10px", // Added border radius
                  "& fieldset": {
                    borderColor: "#555555",
                  },
                  "&:hover fieldset": {
                    borderColor: "#888888",
                  },
                },
                mb: 2, // Margin between text field and button
              }}
            />
            {emailError && (
              <Typography variant="body2" sx={{ color: "red", mt: 1 }}>
                {emailError}
              </Typography>
            )}
            <Button
              variant="contained"
              onClick={handleConfirmClick}
              sx={{
                textTransform: "none",
                background: "linear-gradient(90deg, #7206E6, #B54BFF)", // Gradient to match the image
                color: "#ffffff", // Text color
                height: "54px",
                borderRadius: "10px", // Match button's border radius
                boxShadow: "none", // Optional: remove box shadow
                fontSize: "17px",
                marginTop: "10px",
                "&:hover": {
                  background: "linear-gradient(90deg, #7206E6, #B54BFF)", // Same gradient on hover
                },
              }}
              disabled={loading} // Disable button when loading
            >
              {loading ? (
                <CircularProgress size={24} sx={{ color: "#ffffff" }} />
              ) : (
                "Send Magic Link"
              )}
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

const Home = () => {
  const isMobile = useMediaQuery("(max-width:600px)"); // Adjust the width as needed

  const MobileComponent = withMobileEdgeFill(SendMagicLinkComponent); // Wrap the component

  return isMobile ? (
    <Box sx={{ padding: 2 }}>
      <MobileComponent />
    </Box>
  ) : (
    <RightPanelOnlyComponent LeftComponent={<SendMagicLinkComponent />} />
  );
};

export default Home;
