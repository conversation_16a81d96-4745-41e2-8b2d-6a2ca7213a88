import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Stethoscope } from 'lucide-react';
import { PlanCard } from '@/components/PlanCard';
import { VideoPlayer } from '@/components/VideoPlayer';
import { PlanComparison } from '@/components/PlanComparison';
import { PlanQADialog } from '@/components/PlanQADialog';
import { UserProfile } from '@/components/BenefitsEnrollmentBot';
import { BotQuestion } from '@/components/BotQuestion';
import { ROICalculator } from '@/components/ROICalculator';
import { PopularChoiceBadge } from '@/components/PopularChoiceBadge';
import { CompactROIBadge } from '@/components/CompactROIBadge';

interface MedicalPlanPageProps {
  userProfile: UserProfile;
  onPlanSelect: (planData: any) => void;
  recommendation: any;
}

export const MedicalPlanPage = ({ userProfile, onPlanSelect, recommendation }: MedicalPlanPageProps) => {
  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <BotQuestion 
        question="🎯 Here's your personalized medical plan recommendation!"
        context="Based on your healthcare needs, budget, and family situation."
      />
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Stethoscope className="w-6 h-6 text-red-500" />
            <h2 className="text-xl">Smart Recommendation</h2>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">💡 Why this plan:</p>
            <p className="text-sm mt-1">{recommendation.reason}</p>
          </div>

          {/* ROI Calculator for recommended plan */}
          <ROICalculator 
            planCost={recommendation.plan.cost}
            planType="medical"
            deductible={recommendation.plan.deductible}
            familySize={userProfile.familyMembers}
          />
          
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-medium text-lg">Available Plans</h3>
              <PopularChoiceBadge percentage={78} planType="medical" />
            </div>
            
            <PlanCard
              type="medical"
              recommended={true}
              title={recommendation.plan.name}
              cost={`$${recommendation.plan.cost}`}
              period="paycheck"
              features={[
                ...recommendation.plan.features,
                `💰 Employer pays 80% (${(recommendation.plan.cost * 0.8 * 26).toLocaleString()} value)`,
                `🔒 Max out-of-pocket: $${recommendation.plan.deductible * 2}`
              ]}
              onSelect={() => onPlanSelect(recommendation.plan)}
            />

            {/* Alternative plans with compact ROI badges */}
            <div className="grid gap-3">
              <Card className="border-gray-200 bg-gray-50 dark:bg-gray-950 dark:border-gray-800">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">Alternative: Kaiser HMO Basic</h4>
                      <p className="text-sm text-muted-foreground">Lower cost, integrated care</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">$55.30/paycheck</p>
                      <div className="flex items-center gap-2 mt-1">
                        <PopularChoiceBadge percentage={45} planType="medical" />
                        <CompactROIBadge planCost={55.30} planType="medical" familySize={userProfile.familyMembers} />
                      </div>
                    </div>
                  </div>
                  <button 
                    onClick={() => onPlanSelect({ name: 'Kaiser HMO Basic', cost: 55.30, type: 'HMO' })}
                    className="text-sm text-blue-600 hover:underline"
                  >
                    Select This Plan Instead
                  </button>
                </CardContent>
              </Card>
            </div>
          </div>
          
          <div className="flex gap-2 flex-wrap pt-4">
            <VideoPlayer 
              title="Medical Plans Explained" 
              description="Understand PPO vs HMO vs HDHP"
              planType="medical"
            />
            <PlanComparison />
            <PlanQADialog selectedPlans={{}} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
