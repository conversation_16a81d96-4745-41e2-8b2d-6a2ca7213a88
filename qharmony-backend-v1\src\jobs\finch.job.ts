import dotenv from 'dotenv';
dotenv.config();

// import EnvService from '../services/env.service';
// import axios from 'axios';
import InitService from '../services/init.service';
import logger, { prettyJSON } from '../utils/logger';
import FinchService from '../services/finch.service';
import FinchConnectionModelClass from '../nosql/finchConnection.model';
import FinchCompanyModelClass from '../nosql/finchCompanyData.model';

InitService.init().then(async () => {
  const finchService = new FinchService();
  const finchConnectionModel = FinchConnectionModelClass;
  const allFinchConnections = await finchConnectionModel.getAllData();

  // Extract the unique finchCompanyId
  const finchCompanyIds = allFinchConnections
    ?.map((connection) => connection.finchCompanyId)
    .filter((value, index, self) => self.indexOf(value) === index);
  logger.info(prettyJSON(finchCompanyIds));

  if (!finchCompanyIds) {
    logger.info('No finch company ids found');
    return;
  }

  for (const finchCompanyId of finchCompanyIds) {
    const finchAccessToken = allFinchConnections
      ?.reverse()
      .find(
        (connection) => connection.finchCompanyId === finchCompanyId
      )?.finchAccessToken;
    if (!finchAccessToken) {
      logger.info(
        `No finch access token found for company id: ${finchCompanyId}`
      );
      continue;
    }
    const finchConnection = await finchService.getCompany({ finchAccessToken });
    if (!finchConnection) {
      logger.info(
        `No finch connection found for company id: ${finchCompanyId}`
      );
      continue;
    }
    logger.info(prettyJSON(finchConnection));
    await FinchCompanyModelClass.addData([finchConnection]);
  }

  logger.info(prettyJSON(allFinchConnections));
  // const finchConnections = await
  // const companyData = await finchService.getCompany({})
});
