import React from 'react';
import { <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { BotQuestion } from './BotQuestion';
import { PlanQADialog } from './PlanQADialog';
import { VideoPlayer } from './VideoPlayer';
import { PlanComparison } from './PlanComparison';

interface WelcomePageProps {
  onNext: () => void;
}

export const WelcomePage = ({ onNext }: WelcomePageProps) => {
  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <BotQuestion 
        question="👋 Hi there! Ready to find the perfect benefits for 2025?"
        context="I'm here to make this super easy. I'll ask smart questions and recommend the best plans for your situation."
      />
      
      <div className="bg-white rounded-lg border shadow-sm">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Bell className="w-6 h-6 text-blue-500" />
            Welcome to Your AI Benefits Assistant!
            <Sparkles className="w-5 h-5 text-yellow-500" />
          </h3>
        </div>
        <div className="p-6 space-y-4">
          <p className="text-lg">👋 Hi there! It's time to enroll in your 2025 Medical, Dental, and Vision benefits.</p>
          
          <div className="bg-red-50 p-4 rounded-lg border border-red-200">
            <p className="font-semibold"><strong>⏰ Deadline:</strong> December 15, 2024</p>
          </div>
          
          <div className="space-y-3">
            <p className="font-medium">🧠 I'm your personalized benefits assistant. I'll:</p>
            <ul className="list-disc list-inside space-y-2 ml-4">
              <li>Ask smart questions to understand your needs</li>
              <li>Recommend the best plans for your situation</li>
              <li>Show you helpful videos explaining each plan</li>
              <li>Answer any questions you have</li>
              <li>Help you compare plans side-by-side</li>
            </ul>
          </div>
          
          <div className="flex gap-2 flex-wrap pt-4">
            <PlanQADialog selectedPlans={{}} />
            <VideoPlayer 
              title="Benefits Overview" 
              description="Learn about all your benefit options"
              planType="medical"
            />
            <PlanComparison />
          </div>
          
          <button 
            onClick={onNext}
            className="w-full mt-6 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium text-lg"
          >
            🚀 Start My Smart Enrollment
          </button>
        </div>
      </div>
    </div>
  );
};

const space_y_6 = {
  '& > * + *': {
    marginTop: '1.5rem'
  }
};

const space_y_4 = {
  '& > * + *': {
    marginTop: '1rem'
  }
};

const space_y_3 = {
  '& > * + *': {
    marginTop: '0.75rem'
  }
};

const space_y_2 = {
  '& > * + *': {
    marginTop: '0.5rem'
  }
};
