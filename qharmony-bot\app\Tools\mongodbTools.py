
from pymongo import MongoClient
from bson import ObjectId
from typing import List, Dict, Optional    


def get_mongo_collection(mongo_client: MongoClient, collection_name: str, db_name: str = "prod"):
    """Get MongoDB collection."""
    db = mongo_client[db_name]
    collection = db[collection_name]
    return collection

def get_mongo_db(mongo_client:MongoClient, db_name:str="prod"):
    """Get MongoDB database."""
    return mongo_client[db_name]