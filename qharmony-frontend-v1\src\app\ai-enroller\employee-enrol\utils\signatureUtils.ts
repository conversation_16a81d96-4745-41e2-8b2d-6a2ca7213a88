/**
 * Signature utilities for enrollment process
 * Handles encryption, storage, and retrieval of digital signatures
 */

export interface SignatureData {
  signature: string; // Base64 image data
  timestamp: string;
  employeeName: string;
  userAgent: string;
  ipAddress: string;
  signatureHash: string;
  signaturePadData?: any[]; // Vector data from SignaturePad for editing
  quality?: number;
  format?: string;
}

/**
 * Modern UTF-8 safe base64 encoding for signature data
 * Uses TextEncoder/TextDecoder for proper Unicode handling
 */
export const encryptSignature = (signatureObject: SignatureData): string => {
  try {
    const jsonString = JSON.stringify(signatureObject);
    // Use modern TextEncoder for UTF-8 encoding
    const utf8Bytes = new TextEncoder().encode(jsonString);
    const binaryString = Array.from(utf8Bytes)
      .map(byte => String.fromCharCode(byte))
      .join('');
    return btoa(binaryString);
  } catch (error) {
    console.error('Error encrypting signature:', error);
    throw new Error('Failed to encrypt signature');
  }
};

/**
 * Decrypt signature data from encrypted string with modern UTF-8 decoding
 */
export const decryptSignature = (encryptedData: string): SignatureData => {
  try {
    // Use modern TextDecoder for UTF-8 decoding
    const binaryString = atob(encryptedData);
    const utf8Bytes = new Uint8Array(
      Array.from(binaryString).map(char => char.charCodeAt(0))
    );
    const jsonString = new TextDecoder().decode(utf8Bytes);
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Error decrypting signature:', error);
    throw new Error('Failed to decrypt signature');
  }
};

/**
 * Store encrypted signature in localStorage
 */
export const storeSignature = (signatureData: SignatureData): string => {
  try {
    const encrypted = encryptSignature(signatureData);
    localStorage.setItem('enrollmentSignature', encrypted);
    
    // Also store a reference with timestamp for easy lookup
    const signatureRef = {
      timestamp: signatureData.timestamp,
      employeeName: signatureData.employeeName,
      signatureHash: signatureData.signatureHash
    };
    localStorage.setItem('enrollmentSignatureRef', JSON.stringify(signatureRef));
    
    console.log('✅ Signature stored successfully:', signatureRef);
    return encrypted;
  } catch (error) {
    console.error('Error storing signature:', error);
    throw new Error('Failed to store signature');
  }
};

/**
 * Retrieve and decrypt signature from localStorage
 */
export const getStoredSignature = (): SignatureData | null => {
  try {
    const encrypted = localStorage.getItem('enrollmentSignature');
    if (!encrypted) {
      return null;
    }
    
    return decryptSignature(encrypted);
  } catch (error) {
    console.error('Error retrieving signature:', error);
    return null;
  }
};

/**
 * Get signature reference (metadata only, no image data)
 */
export const getSignatureReference = (): { timestamp: string; employeeName: string; signatureHash: string } | null => {
  try {
    const ref = localStorage.getItem('enrollmentSignatureRef');
    if (!ref) {
      return null;
    }
    
    return JSON.parse(ref);
  } catch (error) {
    console.error('Error retrieving signature reference:', error);
    return null;
  }
};

/**
 * Clear stored signature data
 */
export const clearStoredSignature = (): void => {
  try {
    localStorage.removeItem('enrollmentSignature');
    localStorage.removeItem('enrollmentSignatureRef');
    console.log('🗑️ Signature data cleared');
  } catch (error) {
    console.error('Error clearing signature:', error);
  }
};

/**
 * Verify signature integrity using hash comparison
 */
export const verifySignature = (signatureData: SignatureData): boolean => {
  try {
    const expectedHash = btoa(signatureData.signature).substring(0, 32);
    return expectedHash === signatureData.signatureHash;
  } catch (error) {
    console.error('Error verifying signature:', error);
    return false;
  }
};

/**
 * Create signature data object
 */
export const createSignatureData = (
  signatureBase64: string,
  employeeName: string
): SignatureData => {
  return {
    signature: signatureBase64,
    timestamp: new Date().toISOString(),
    employeeName: employeeName,
    userAgent: navigator.userAgent,
    ipAddress: 'client-side', // Would need backend to get real IP
    signatureHash: btoa(signatureBase64).substring(0, 32)
  };
};

/**
 * Format signature timestamp for display
 */
export const formatSignatureTimestamp = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    });
  } catch (error) {
    return timestamp;
  }
};

/**
 * Debug function to view stored signature (for development)
 */
export const debugSignature = (): void => {
  const signature = getStoredSignature();
  const reference = getSignatureReference();
  
  console.log('🔍 Signature Debug Info:');
  console.log('Reference:', reference);
  
  if (signature) {
    console.log('Full Signature Data:', {
      timestamp: signature.timestamp,
      employeeName: signature.employeeName,
      signatureHash: signature.signatureHash,
      userAgent: signature.userAgent,
      signatureSize: signature.signature.length,
      isValid: verifySignature(signature)
    });
    
    // Create a temporary image to display signature
    const img = new Image();
    img.onload = () => {
      console.log('📝 Signature Image:', img);
      console.log('Dimensions:', img.width, 'x', img.height);
    };
    img.src = signature.signature;
  } else {
    console.log('No signature found in storage');
  }
};
