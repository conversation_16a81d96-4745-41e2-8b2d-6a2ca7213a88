"use client";

import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { getDocumentsForBenefit } from "@/middleware/benefits_middleware";
import { useParams, useRouter } from "next/navigation";
import { Box, Typography, Button, Grid, CircularProgress } from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew"; // Small back arrow icon
import { useSelector } from "react-redux";
import { selectBenefitById } from "@/redux/reducers/benefitsSlice";
import { getUsersCompanyId } from "@/redux/reducers/userSlice";
import OpenInNewIcon from "@mui/icons-material/OpenInNew"; // Import the icon
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";
import * as microsoftTeams from "@microsoft/teams-js";
import { maskBenefitCategory, maskedSubCategory } from "@/middleware/company_middleware";

const DocumentsForBenefitsView = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { benefitId } = useParams();
  const [isInTeams, setIsInTeams] = useState(false);

  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));

  const documents = useAppSelector(
    (state: RootState) => state.benefits.documentsPerBenefit,
  );

  const viewableDocuments = useAppSelector(
    (state: RootState) => state.benefits.viewableDocuments,
  );

  const benefitInfo = useSelector((state: RootState) =>
    selectBenefitById(state, benefitId as string),
  );

  const loadingDocuments = useAppSelector(
    (state: RootState) => state.benefits.loadingDocuments,
  );

  useEffect(() => {
    // Initialize Teams SDK
    microsoftTeams.app.initialize().then(() => {
      microsoftTeams.app.getContext().then((context) => {
        if (context.app.host.name === microsoftTeams.HostName.teams) {
          setIsInTeams(true);
        }
      });
    });
  }, []);

  useEffect(() => {
    if (benefitId !== "") {
      getDocumentsForBenefit(
        dispatch,
        benefitId as string,
        companyId,
        "view_benefits",
      );
    }
  }, [benefitId, companyId, dispatch]);

  const handleBack = () => {
    router.back();
  };

  const openPdfExternally = (objectId: string, companyId: string) => {
    window.open(`https://api.benosphere.com/benefits/document?objectKey=${objectId}&companyId=${companyId}`, "_blank");
  };

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6F8",
          height: "95vh",
          padding: "32px",
          overflow: "auto",
        }}
      >
        {/* Back Arrow Section */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
          <Button
            startIcon={<ArrowBackIosNewIcon sx={{ fontSize: 16 }} />} // Small back arrow
            onClick={handleBack}
            sx={{
              color: "#6c757d",
              fontWeight: "normal",
              textTransform: "none",
              fontSize: "1.2rem",
              "&:hover": {
                bgcolor: "transparent",
              },
            }}
          >
            {benefitInfo ? (
              <>
                {maskBenefitCategory(benefitInfo.benefitType)} /
                <span
                  style={{
                    fontWeight: "bold",
                    color: "#000000",
                    marginLeft: 5,
                  }}
                >
                  {maskedSubCategory(benefitInfo.benefit.subType)}
                </span>
              </>
            ) : (
              "Back"
            )}
          </Button>
        </Box>

        <Typography sx={{ fontWeight: 800, fontSize: "42px", mb: 0 }}>
          {maskedSubCategory(benefitInfo?.benefit?.subType || "")}
        </Typography>
        <Typography
          variant="body1"
          sx={{ color: "#6c757d", mb: 6, fontSize: "16px" }}
        >
          You can find all your health insurance details here, including
          coverage options, policy documents, and claim information.
        </Typography>

        <Grid container spacing={3} alignItems="flex-start">
          <Grid item xs={12}>
            <Typography sx={{ mb: 3, fontWeight: 700, fontSize: "24px" }}>
              ☕ Documents
            </Typography>
            {documents.documents.length === 0 ? (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  minHeight: "150px",
                  borderRadius: "8px",
                  border: "2px dashed #e0e0e0",
                  bgcolor: "#f9f9f9",
                  p: 4,
                  textAlign: "left",
                  maxWidth: "400px",
                }}
              >
                <Typography
                  variant="body1"
                  sx={{ color: "#6c757d", fontSize: "1rem" }}
                >
                  No documents available at the moment.
                </Typography>
              </Box>
            ) : (
              <Box
                sx={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "50px",
                  // Removed maxHeight and overflowY to show all documents without scrolling
                }}
              >
                {documents.documents
                  // .concat(documents.documents, documents.documents)
                  .map((documentObjectKey, index) => {
                    const viewableDocument = viewableDocuments.find(
                      (doc) => doc.documentObjectKey === documentObjectKey,
                    );
                    const gradientOptions = [
                      "linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)",
                      "linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)",
                      "linear-gradient(135deg, #43cea2 0%, #185a9d 100%)",
                      "linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)",
                      "linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)",
                    ];
                    const gradient =
                      gradientOptions[index % gradientOptions.length];
                    return (
                      <Box
                        key={documentObjectKey}
                        sx={{
                          position: "relative",
                          width: "240px",
                          height: "367.5px",
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "flex-start",
                          justifyContent: "flex-start",
                        }}
                      >
                        <Box
                          sx={{
                            width: "240px",
                            minHeight: "322.5px",
                            borderRadius: "12px",
                            overflow: "hidden",
                            boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
                            position: "relative",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            background: gradient,
                            color: "#ffffff",
                            cursor: "pointer",
                          }}
                          onClick={() =>
                            openPdfExternally(viewableDocument!.documentObjectKey, companyId)
                          }
                        >
                          {loadingDocuments.includes(documentObjectKey) ||
                            !viewableDocument ? (
                            <CircularProgress />
                          ) : (
                            <Box
                              sx={{
                                padding: "16px",
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                justifyContent: "center",
                                textAlign: "center",
                                overflow: "hidden", // Ensures no overflow beyond the box
                              }}
                            >
                              <Typography
                                sx={{
                                  fontSize: "27px", // Adjust font size for better wrapping
                                  fontWeight: "bold",
                                  textAlign: "center",
                                  wordWrap: "break-word", // Wrap long words to the next line
                                  wordBreak: "break-word", // Break words that exceed the box width
                                  whiteSpace: "normal", // Allow text to wrap
                                  overflow: "hidden", // Avoid overflow of content
                                  display: "-webkit-box",
                                  WebkitLineClamp: 3, // Limit text to 3 lines, if required
                                  WebkitBoxOrient: "vertical",
                                }}
                              >
                                {viewableDocument?.originalFileName ||
                                  "Document Preview"}
                              </Typography>
                              <OpenInNewIcon
                                sx={{
                                  position: "absolute",
                                  top: 12,
                                  right: 12,
                                  color: "#ffffff",
                                  cursor: "pointer",
                                  height: 25,
                                  width: 25,
                                }}
                                onClick={() =>
                                  window.open(
                                    viewableDocument?.document,
                                    "_blank",
                                  )
                                }
                              />
                            </Box>
                          )}
                        </Box>
                      </Box>
                    );
                  })}
              </Box>
            )}
          </Grid>

          <Grid item xs={12}>
            <Typography
              sx={{ mb: 3, fontWeight: 700, mt: 10, fontSize: "17px" }}
            >
              Other helpful links
            </Typography>
            <Box
              sx={{
                // bgcolor: "#ffffff",
                borderRadius: "12px",
                width: "400px",
                // boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
              }}
            >
              {documents.links.length === 0 ? (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    minHeight: "150px",
                    borderRadius: "8px",
                    border: "2px dashed #e0e0e0",
                    bgcolor: "#f9f9f9",
                    py: 4,
                    textAlign: "left",
                    maxWidth: "400px",
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ color: "#6c757d", fontSize: "16px" }}
                  >
                    No links available right now.
                  </Typography>
                </Box>
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 2, // Space between each link
                  }}
                >
                  {documents.links.map((link, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between", // Ensures the text and icon are on opposite ends
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <Typography
                        component="a"
                        href={
                          link.startsWith("http") ? link : `https://${link}`
                        }
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{
                          color: "#1A7ECF", // Black color for text
                          textDecoration: "none",
                          fontWeight: 500, // Semi-bold font weight for link text
                          fontSize: "16px",
                        }}
                      >
                        {link || `Link ${index + 1}`}
                      </Typography>
                      <Button
                        onClick={() =>
                          window.open(
                            link.startsWith("http") ? link : `https://${link}`,
                            "_blank",
                          )
                        }
                        sx={{ minWidth: 0, padding: 0 }} // Remove default button padding
                      >
                        <OpenInNewIcon
                          sx={{ color: "#6c757d", marginLeft: 2 }} // Gray color for the icon
                        />
                      </Button>
                    </Box>
                  ))}
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </ProtectedRoute>
  );
};

export default withSidebar(DocumentsForBenefitsView);
