import axios from 'axios';
import EnvService from './env.service';

export interface AssistantResponse {
  assistantId: string;
  message: string;
}

const API_KEY = EnvService.env().OPENAI_API_KEY;
const API_URL = 'https://api.openai.com/v1/assistants';

class AssistantService {
  async createAssistant(
    employerId: string,
    instructions: string
  ): Promise<AssistantResponse> {
    try {
      const response = await axios.post(
        API_URL,
        {
          name: `Assistant for employer ${employerId}`,
          tools: [{ type: 'retrieval' }],
          model: 'gpt-3.5-turbo', // Specify the model for the assistant
          instructions: instructions,
        },
        {
          headers: {
            Authorization: `Bearer ${API_KEY}`,
            'Content-Type': 'application/json',
            'OpenAI-Beta': 'assistants=v1',
          },
        }
      );

      const assistantId = response.data.id;
      const message = 'Assistant created successfully';

      return { assistantId, message };
    } catch (error) {
      throw new Error(`Error creating assistant: ${error.message}`);
    }
  }
}

export default AssistantService;
