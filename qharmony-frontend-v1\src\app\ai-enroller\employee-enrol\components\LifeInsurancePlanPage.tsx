'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Shield, CheckCircle, Play, MessageCircle, BarChart3 } from 'lucide-react';
import { FloatingHelp } from './FloatingHelp';
import { VideoPlayer } from './VideoPlayer';
import { CompareModal } from './CompareModal';
import CustomModal from './CustomModal';
import ChatModal from './ChatModal';

interface LifeInsurancePlan {
  id: string;
  name: string;
  type: string;
  carrier: string;
  coverage: string;
  premium: string;
  cost: number;
  features: string[];
  recommended?: boolean;
  originalPlan?: any; // Original plan data from API
  coverageTiers?: any[]; // Coverage tiers for cost calculation
}

interface LifeInsurancePlanPageProps {
  onPlanSelect: (plan: LifeInsurancePlan | null) => void; // Match dental and vision pattern
  planAssignments?: any[]; // Real plan assignments from API
  selectedCoverageTier?: string; // Coverage tier selected in personalization
}

const LifeInsurancePlanPage: React.FC<LifeInsurancePlanPageProps> = ({ onPlanSelect, planAssignments, selectedCoverageTier }) => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [showHelp, setShowHelp] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [showWaiveConfirm, setShowWaiveConfirm] = useState(false);
  const [waiveReason, setWaiveReason] = useState<string>('');
  const [showCompare, setShowCompare] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    title: '',
    message: '',
    type: 'alert' as 'alert' | 'confirm' | 'success' | 'error',
    onConfirm: () => {},
    confirmText: 'OK',
    cancelText: 'Cancel'
  });

  // Modal helper function
  const showAlert = (title: string, message: string, type: 'alert' | 'success' | 'error' = 'alert') => {
    setModalConfig({
      title,
      message,
      type,
      onConfirm: () => {},
      confirmText: 'OK',
      cancelText: 'Cancel'
    });
    setShowModal(true);
  };

  // Helper function to calculate cost based on selected coverage tier
  const calculateCostForTier = (plan: any): number => {
    console.log('🛡️ Calculating cost for life insurance plan:', {
      planName: plan.name,
      selectedCoverageTier,
      coverageTiers: plan.coverageTiers,
      fallbackCost: plan.cost
    });

    if (!selectedCoverageTier || !plan.coverageTiers || !Array.isArray(plan.coverageTiers)) {
      console.log('🛡️ Using fallback cost:', plan.cost || 0);
      return plan.cost || 0;
    }

    const matchingTier = plan.coverageTiers.find(
      (tier: any) => tier.tierName === selectedCoverageTier
    );

    if (matchingTier) {
      console.log('🛡️ Found matching tier:', matchingTier);
      return matchingTier.employeeCost || 0;
    } else {
      console.log('🛡️ No matching tier found, available tiers:', plan.coverageTiers.map((t: any) => t.tierName));
      // Try Employee Only as fallback
      const employeeOnlyTier = plan.coverageTiers.find((tier: any) => tier.tierName === 'Employee Only');
      if (employeeOnlyTier) {
        console.log('🛡️ Using Employee Only tier as fallback:', employeeOnlyTier);
        return employeeOnlyTier.employeeCost || 0;
      }
      return plan.cost || 0;
    }
  };

  // Use real plan assignments if available, otherwise show empty state
  const lifeInsurancePlans: LifeInsurancePlan[] = planAssignments && planAssignments.length > 0
    ? planAssignments.map((plan: any) => {
        const tierCost = calculateCostForTier(plan);
        return {
          id: plan.id,
          name: plan.name,
          type: plan.planType || 'Term Life',
          carrier: plan.carrierName || 'Insurance Carrier',
          coverage: '$50,000', // Could be extracted from plan data
          premium: `$${tierCost.toFixed(2)}/paycheck`,
          cost: tierCost,
          features: plan.features || ['Comprehensive life insurance coverage', 'Death benefit protection', 'Financial security'],
          recommended: false, // Could be determined by business logic
          // Preserve original plan data for summary page
          originalPlan: plan,
          coverageTiers: plan.coverageTiers // Keep coverage tiers for cost calculation
        };
      })
    : [];

  console.log('🛡️ Life insurance plans to display:', lifeInsurancePlans);
  console.log('🛡️ Plan assignments received:', planAssignments);
  console.log('🛡️ Selected coverage tier:', selectedCoverageTier);

  // Load previous selection on component mount
  React.useEffect(() => {
    const savedSelection = localStorage.getItem('selectedLifePlan');
    const savedWaive = localStorage.getItem('lifeWaived');

    if (savedWaive === 'true') {
      setSelectedPlan('WAIVE');
    } else if (savedSelection) {
      try {
        const planData = JSON.parse(savedSelection);
        setSelectedPlan(planData.id);
      } catch (e) {
        console.error('Error parsing saved life plan:', e);
      }
    }
  }, []);

  const handlePlanSelection = (planId: string) => {
    // Toggle selection - if clicking the same plan, deselect it
    if (selectedPlan === planId) {
      setSelectedPlan(null);
      localStorage.removeItem('selectedLifePlan');
      localStorage.removeItem('lifeWaived');
      onPlanSelect(null);
      return;
    }

    // Single plan selection
    setSelectedPlan(planId);

    // Store selected plan in localStorage
    const selectedPlanData = lifeInsurancePlans.find(p => p.id === planId);
    localStorage.setItem('selectedLifePlan', JSON.stringify(selectedPlanData));
    localStorage.removeItem('lifeWaived'); // Clear waive status

    // Call the parent callback with the selected plan (EXACTLY like dental and vision)
    onPlanSelect(selectedPlanData || null);
  };

  const handleWaiveSelect = () => {
    // Toggle waive selection - if already waived, deselect it
    if (selectedPlan === 'WAIVE') {
      setSelectedPlan(null);
      localStorage.removeItem('lifeWaived');
      localStorage.removeItem('lifeWaiveReason');
      onPlanSelect(null);
      return;
    }

    setShowWaiveConfirm(true);
  };

  const confirmWaive = () => {
    if (!waiveReason) {
      showAlert('Validation Error', 'Please select a reason for waiving life insurance coverage.', 'error');
      return;
    }

    setSelectedPlan('WAIVE');
    localStorage.setItem('lifeWaived', 'true');
    localStorage.setItem('lifeWaiveReason', waiveReason);
    localStorage.removeItem('selectedLifePlan'); // Clear plan selection
    setShowWaiveConfirm(false);
    setWaiveReason(''); // Reset reason

    // Call parent callback with null to indicate waived
    onPlanSelect(null);
  };

  const cancelWaive = () => {
    setShowWaiveConfirm(false);
    setWaiveReason(''); // Reset reason when canceling
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '24px',
      fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0
          }}>
            🛡️ Let&apos;s secure your family&apos;s financial future with life insurance.
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0'
          }}>
            Life insurance provides financial protection for your loved ones. Choose the coverage level that best fits your needs and budget.
          </p>
        </div>
      </div>



      {/* Life Insurance Plans */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '24px' }}>
          <Shield style={{ width: '24px', height: '24px', color: '#3b82f6' }} />
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            Choose Your Life Insurance Plan
          </h2>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {lifeInsurancePlans.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '40px 20px',
              backgroundColor: '#f9fafb',
              borderRadius: '8px',
              border: '2px dashed #e5e7eb'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>🛡️</div>
              <h3 style={{
                fontSize: '18px',
                fontWeight: '600',
                color: '#374151',
                margin: '0 0 8px 0'
              }}>
                No Life Insurance Plans Available
              </h3>
              <p style={{
                color: '#6b7280',
                fontSize: '14px',
                margin: 0,
                lineHeight: '1.5'
              }}>
                Your company hasn&apos;t set up any life insurance plan assignments yet.
                <br />
                Please contact your HR administrator for more information.
              </p>
            </div>
          ) : (
            <>

              {lifeInsurancePlans.map((plan) => (
            <div
              key={plan.id}
              style={{
                border: '1px solid #e5e7eb',
                borderRadius: '12px',
                padding: '24px',
                backgroundColor: 'white',
                position: 'relative',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}
            >
              {plan.recommended && (
                <div style={{
                  position: 'absolute',
                  top: '-8px',
                  right: '20px',
                  background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                  color: 'white',
                  padding: '6px 16px',
                  borderRadius: '20px',
                  fontSize: '12px',
                  fontWeight: '600',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                }}>
                  ⭐ Recommended
                </div>
              )}

              <div style={{ marginBottom: '20px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                  <Shield style={{ width: '24px', height: '24px', color: '#3b82f6' }} />
                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#111827', margin: 0 }}>
                    {plan.name}
                  </h3>
                </div>
                <p style={{ fontSize: '14px', color: '#6b7280', margin: '0 0 8px 0' }}>
                  {plan.type} • {plan.carrier}
                </p>
                <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px' }}>
                  <span style={{ fontSize: '24px', fontWeight: '700', color: '#111827' }}>
                    ${plan.cost.toFixed(2)}
                  </span>
                  <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                </div>
              </div>



              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: '0 0 12px 0' }}>
                  Key Features
                </h4>
                <ul style={{ margin: 0, paddingLeft: 0, listStyle: 'none' }}>
                  {plan.features.map((feature, index) => (
                    <li key={index} style={{
                      fontSize: '14px',
                      color: '#6b7280',
                      marginBottom: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      <CheckCircle style={{ width: '16px', height: '16px', color: '#10b981', flexShrink: 0 }} />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <button
                onClick={() => handlePlanSelection(plan.id)}
                style={{
                  width: '100%',
                  padding: '12px 24px',
                  backgroundColor: selectedPlan === plan.id ? '#000000' : '#f3f4f6',
                  color: selectedPlan === plan.id ? 'white' : '#6b7280',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseOver={(e) => {
                  if (selectedPlan !== plan.id) {
                    e.currentTarget.style.backgroundColor = '#e5e7eb';
                  }
                }}
                onMouseOut={(e) => {
                  if (selectedPlan !== plan.id) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }
                }}
              >
                {selectedPlan === plan.id ? (
                  <>
                    <CheckCircle size={16} />
                    Selected
                  </>
                ) : (
                  'Select This Plan'
                )}
              </button>
            </div>
          ))}

              {/* Waive Coverage Option */}
              <div
                style={{
                  border: selectedPlan === 'WAIVE'
                    ? '2px solid #ef4444'
                    : '2px solid #e5e7eb',
                  borderRadius: '8px',
                  padding: '20px',
                  backgroundColor: selectedPlan === 'WAIVE'
                    ? '#fef2f2'
                    : '#f9fafb',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={handleWaiveSelect}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                  <div>
                    <h3 style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color: '#111827',
                      margin: 0
                    }}>
                      Waive Life Insurance Coverage
                    </h3>
                    <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '4px' }}>
                      <span style={{
                        fontSize: '24px',
                        fontWeight: '700',
                        color: '#111827'
                      }}>
                        $0.00
                      </span>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                    </div>
                  </div>
                  <div style={{
                    backgroundColor: '#ef4444',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}>
                    No Coverage
                  </div>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>No life insurance coverage or benefits</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>No financial protection for your family</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>Can only enroll during next open enrollment</span>
                  </div>
                </div>

                <button
                  style={{
                    width: '100%',
                    backgroundColor: selectedPlan === 'WAIVE' ? '#ef4444' : '#f3f4f6',
                    color: selectedPlan === 'WAIVE' ? 'white' : '#6b7280',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    fontWeight: '500',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  {selectedPlan === 'WAIVE' ? '✓ Coverage Waived' : 'Waive Coverage'}
                </button>
              </div>
            </>
          )}
        </div>

        {/* Selection Summary */}
        {selectedPlan && (
          <div style={{
            backgroundColor: selectedPlan === 'WAIVE' ? '#fef2f2' : '#eff6ff',
            border: selectedPlan === 'WAIVE' ? '1px solid #fecaca' : '1px solid #bfdbfe',
            borderRadius: '8px',
            padding: '16px',
            marginTop: '16px'
          }}>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '600',
              color: selectedPlan === 'WAIVE' ? '#dc2626' : '#1e40af',
              margin: '0 0 12px 0'
            }}>
              {selectedPlan === 'WAIVE' ? 'Life Insurance Coverage Waived' : 'Selected Life Insurance Plan'}
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {selectedPlan === 'WAIVE' ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: 'white',
                  padding: '12px',
                  borderRadius: '6px'
                }}>
                  <span style={{ fontWeight: '500', color: '#111827' }}>No Life Insurance Coverage</span>
                  <span style={{ color: '#dc2626', fontWeight: '600' }}>$0.00/paycheck</span>
                </div>
              ) : (() => {
                const plan = lifeInsurancePlans.find(p => p.id === selectedPlan);
                return plan ? (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    padding: '12px',
                    borderRadius: '6px'
                  }}>
                    <span style={{ fontWeight: '500', color: '#111827' }}>{plan.name}</span>
                    <span style={{ color: '#1e40af', fontWeight: '600' }}>${plan.cost.toFixed(2)}/paycheck</span>
                  </div>
                ) : null;
              })()}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          gap: '12px',
          paddingTop: '24px',
          borderTop: '1px solid #e5e7eb',
          marginTop: '24px',
          flexWrap: 'wrap'
        }}>
          <button
            onClick={() => setShowChatModal(true)}
            style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <span style={{ color: '#2563eb' }}>❓</span>
            Ask Questions
          </button>

          <button
            onClick={() => setShowVideo(true)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151'
            }}
          >
            <Play size={16} />
            Watch Video
          </button>

          <button
            onClick={() => setShowCompare(true)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151'
            }}
          >
            <BarChart3 size={16} />
            Compare Plans
          </button>
        </div>
      </div>

      {/* Modal Components */}
      {showHelp && (
        <FloatingHelp onClose={() => setShowHelp(false)} />
      )}

      {showVideo && (
        <VideoPlayer
          title="Life Insurance Overview"
          description="Learn about your life insurance options and benefits"
          planType="dental"
          onClose={() => setShowVideo(false)}
        />
      )}

      {showCompare && (
        <CompareModal
          plans={lifeInsurancePlans.map(plan => ({
            id: plan.id,
            name: plan.name,
            type: 'dental' as const, // Using dental type as workaround since life is not supported
            tier: 'Gold' as const,
            monthlyPremium: calculateCostForTier(plan),
            deductible: 0,
            outOfPocketMax: 0,
            features: plan.features?.slice(0, 3) || [],
            network: 'Life Insurance Network'
          }))}
          onClose={() => setShowCompare(false)}
        />
      )}

      {/* Waive Confirmation Modal */}
      {showWaiveConfirm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '80vh',
            overflowY: 'auto',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            position: 'relative'
          }}>
            {/* Close Button */}
            <button
              onClick={cancelWaive}
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: '#6b7280',
                padding: '4px'
              }}
            >
              ×
            </button>

            {/* Header */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{
                fontSize: '24px',
                fontWeight: '600',
                color: '#111827',
                margin: '0 0 12px 0'
              }}>
                Waive Life Insurance Coverage
              </h3>
              <p style={{
                color: '#6b7280',
                fontSize: '14px',
                margin: 0,
                lineHeight: '21px'
              }}>
                Please select a reason for waiving life insurance coverage. This information helps us understand your decision.
              </p>
            </div>

            {/* Reason Options */}
            <div style={{ marginBottom: '32px' }}>
              {[
                {
                  value: 'spouse_partner_plan',
                  title: "Covered under spouse/partner's plan",
                  description: "I have life insurance coverage through my spouse or partner's insurance plan"
                },
                {
                  value: 'parent_plan',
                  title: "Covered under parent's plan",
                  description: "I am covered under my parent's life insurance plan"
                },
                {
                  value: 'other_insurance',
                  title: 'Have other life insurance',
                  description: 'I have life insurance coverage through another insurance provider'
                },
                {
                  value: 'dont_need',
                  title: "Don't need life insurance coverage",
                  description: 'I prefer not to have life insurance coverage at this time'
                },
                {
                  value: 'cost_too_high',
                  title: 'Cost is too high',
                  description: 'The premium cost is beyond my budget'
                }
              ].map((option) => (
                <div
                  key={option.value}
                  style={{
                    border: waiveReason === option.value ? '2px solid #3b82f6' : '1px solid #e5e7eb',
                    borderRadius: '8px',
                    padding: '16px',
                    marginBottom: '12px',
                    cursor: 'pointer',
                    backgroundColor: waiveReason === option.value ? '#eff6ff' : 'white',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={() => setWaiveReason(option.value)}
                >
                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                    <div style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      border: waiveReason === option.value ? '6px solid #3b82f6' : '2px solid #d1d5db',
                      backgroundColor: waiveReason === option.value ? '#3b82f6' : 'white',
                      marginTop: '2px',
                      flexShrink: 0
                    }} />
                    <div>
                      <div style={{
                        fontSize: '14px',
                        fontWeight: '600',
                        color: '#111827',
                        marginBottom: '4px',
                        lineHeight: '21px'
                      }}>
                        {option.title}
                      </div>
                      <div style={{
                        fontSize: '14px',
                        color: '#6b7280',
                        lineHeight: '21px'
                      }}>
                        {option.description}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
              <button
                onClick={cancelWaive}
                style={{
                  padding: '12px 24px',
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  color: '#374151',
                  cursor: 'pointer',
                  fontWeight: '500',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={confirmWaive}
                disabled={!waiveReason}
                style={{
                  padding: '12px 24px',
                  backgroundColor: waiveReason ? '#1f2937' : '#9ca3af',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  cursor: waiveReason ? 'pointer' : 'not-allowed',
                  fontWeight: '500',
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                Continue
                <span style={{ fontSize: '14px' }}>→</span>
              </button>
            </div>
          </div>
        </div>
      )}



      {/* Custom Modal */}
      <CustomModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onConfirm={modalConfig.onConfirm}
        title={modalConfig.title}
        message={modalConfig.message}
        type={modalConfig.type}
        confirmText={modalConfig.confirmText}
        cancelText={modalConfig.cancelText}
      />
      {showChatModal && (
        <ChatModal
          isOpen={showChatModal}
          onClose={() => setShowChatModal(false)}
        />
      )}
    </div>
  );
};

export default LifeInsurancePlanPage;

