"use client";

import {
  <PERSON>,
  Drawer,
  <PERSON>ssB<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Divider,
  Button,
} from "@mui/material";
import Image from "next/image"; // Assuming you're using Next.js
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { useEffect, useState } from "react";
import { getCompanyBenefitTypes } from "@/middleware/company_middleware";
import {
  getUsersCompanyId,
  setSelectedBenefitType,
} from "@/redux/reducers/userSlice";
import { usePathname, useRouter } from "next/navigation";
import assistant_image from "../../../../public/ai_assistant.png";
import { useSelector } from "react-redux";
import { getBenefitTypeIcon } from "../../../components/benefit_vector_map";
import HomeIcon from "@mui/icons-material/Home";
import { useAuth } from "@/components/AuthContext";
import LogoutIcon from "@mui/icons-material/Logout";
import { closeDrawer } from "@/redux/reducers/mobileSidebarSlice";

const drawerWidth = "75vw"; // Change the sidebar width to 75% of the viewport width

const MobileSidebar = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();

  const { logout } = useAuth();

  const benefitTypes = useAppSelector(
    (state: RootState) => state.company.companyBenefitTypes,
  );
  const selectedBenefitType = useAppSelector(
    (state: RootState) => state.user.selectedBenefitType,
  );

  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));

  useEffect(() => {
    if (companyId) {
      getCompanyBenefitTypes(dispatch, companyId);
    }
  }, [companyId, dispatch]);

  const [isTeamsApp, setIsTeamsApp] = useState(false);

  useEffect(() => {
    const isTeamsApp = localStorage.getItem("isTeamsApp1");
    setIsTeamsApp(isTeamsApp === "true");
  }, []);

  const handleBenefitTypeClick = (benefitType: string) => {
    dispatch(setSelectedBenefitType(benefitType));
    router.push(`/viewBenefitsByType/${benefitType}`);
  };

  return (
    <Drawer
      sx={{
        width: drawerWidth,
        height: "100vh",
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          width: drawerWidth,
          boxSizing: "border-box",
          bgcolor: "#ffffff",
          position: "relative",
        },
      }}
      variant="permanent"
      anchor="left"
    >
      <Box
        sx={{
          padding: 0,
          height: "100%",
          position: "relative",
          bgcolor: "#ffffff",
        }}
      >
        <Box
          sx={{
            mx: 2,
            mt: 2,
            px: 1,
            py: 0.5,
            borderRadius: 2,
            position: "relative",
            "&:hover": {
              backgroundColor: "#f0f0f0",
            },
            bgcolor: "#F5F6FA",
          }}
        >
          <Button
            variant="text"
            sx={{
              width: "100%",
              borderRadius: 2,
              bgcolor: "#F5F6FA",
              color: "#333",
              fontWeight: "medium",
              fontSize: "1rem",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#f0f0f0",
              },
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-start",
            }}
            onClick={() => {
              router.push("/mobile/dashboard");
              dispatch(closeDrawer());
            }}
          >
            <HomeIcon sx={{ mr: 1 }} />
            Home
          </Button>
        </Box>
        <Typography
          sx={{
            mt: 2,
            fontWeight: 500,
            paddingX: 2.5,
            fontSize: "1.2rem",
            color: "black",
          }}
        >
          My Benefits
        </Typography>
        <Typography
          sx={{
            fontWeight: 500,
            paddingX: 2.5,
            paddingY: 1,
            fontSize: ".7rem",
            color: "rgba(0, 0, 0, 0.4)",
          }}
        >
          SELECT ANY TO VIEW
        </Typography>
        <Divider sx={{ my: 1 }} />
        <List>
          {benefitTypes.length > 0 ? (
            benefitTypes.map((benefitType) => (
              <ListItem key={benefitType} disablePadding>
                <ListItemButton
                  onClick={() => {
                    handleBenefitTypeClick(benefitType);
                    dispatch(closeDrawer());
                  }}
                  sx={{
                    borderRadius: 2,
                    position: "relative",
                    "&:hover": {
                      backgroundColor: "#f0f0f0",
                    },
                    bgcolor: "#F5F6FA",
                    mx: 2,
                    mt: 2,
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 0, mr: 2, pt: 0.5 }}>
                    {getBenefitTypeIcon(benefitType)}
                  </ListItemIcon>
                  <ListItemText
                    primary={benefitType}
                    sx={{
                      fontWeight: "medium",
                      color: "#333",
                      fontSize: "1rem",
                    }}
                  />
                </ListItemButton>
              </ListItem>
            ))
          ) : (
            <Typography variant="body1" sx={{ color: "#999", padding: 2.5 }}>
              No benefits available at the moment
            </Typography>
          )}
        </List>
      </Box>

      {!isTeamsApp && (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            bgcolor: "#F5F6FA",
            borderRadius: "30px",
            padding: "10px 20px",
            cursor: "pointer",
            position: "absolute",
            bottom: "50px",
            left: "50%",
            transform: "translateX(-50%)",
            width: "calc(100% - 40px)",
          }}
          onClick={() => {
            router.push("/qHarmonyBot");
            dispatch(closeDrawer());
          }}
        >
          <Image
            src={assistant_image}
            alt="AI Chat"
            style={{
              borderRadius: "100%",
              width: "40px",
              height: "40px",
              marginRight: "10px",
            }}
          />
          <Box>
            <Typography variant="body1" sx={{ fontWeight: "bold" }}>
              Chat with Brea
            </Typography>
            <Typography variant="body2" sx={{ color: "#6c757d" }}>
              24/7 available
            </Typography>
          </Box>
        </Box>
      )}
      <Button
        onClick={logout}
        sx={{
          backgroundColor: "transparent",
          color: "#333",
          marginBottom: "5px",
          textTransform: "none",
          padding: "8px 16px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          gap: "8px",
          "&:hover": {
            backgroundColor: "transparent",
            color: "#555",
          },
          boxShadow: "none",
        }}
      >
        <LogoutIcon sx={{ fontSize: "18px" }} />
        <Typography sx={{ fontWeight: 500, fontSize: "14px" }}>
          Logout
        </Typography>
      </Button>
    </Drawer>
  );
};

export default MobileSidebar;
