'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { wellnessService, WellnessQuestion, WellnessResult } from '@/services/wellness.service';
import './wellness.css';

// Fallback to local data if API fails
import fallbackData from './data.json';

export default function Page() {
  const router = useRouter();
  const [questions, setQuestions] = useState<WellnessQuestion[]>([]);
  const [activeQuestion, setActiveQuestion] = useState(0);
  const [userAnswers, setUserAnswers] = useState<Record<string, any>>({});
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState('123'); // Default fallback ID
  const [teamId, setTeamId] = useState('456'); // Default fallback ID

  // Get user ID and team ID from localStorage or context
  useEffect(() => {
    // Access localStorage only in browser environment
    setUserId(localStorage.getItem('userid1') || '123');
    setTeamId(localStorage.getItem('teamId') || '456');
  }, []);

  useEffect(() => {
    // Load questions from API
    const loadQuestions = async () => {
      try {
        const apiQuestions = await wellnessService.getQuestions();
        setQuestions(apiQuestions);
      } catch (error) {
        console.error('Failed to load questions from API, using fallback data', error);
        console.log('Fallback data:', fallbackData);
        // Check if wellness_questions exists in fallbackData
        if (fallbackData.wellness_questions) {
          setQuestions(fallbackData.wellness_questions.map(q => ({
            ...q,
            options: q.options || undefined
          })));
        } else {
          console.error('wellness_questions not found in fallback data');
          setError('Failed to load questions. Please check console for details.');
        }
      } finally {
        setLoading(false);
      }
    };

    loadQuestions();

    // Load saved answers from localStorage
    const saved = localStorage.getItem('wellness_user_answers');
    if (saved) setUserAnswers(JSON.parse(saved));
  }, []);

  useEffect(() => {
    localStorage.setItem('wellness_user_answers', JSON.stringify(userAnswers));
  }, [userAnswers]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      console.log("Submitting answers:", userId, teamId, userAnswers);
      const result = await wellnessService.submitAnswers(userId, teamId, userAnswers);
      console.log("Submission result:", result);
      
      // Store result in localStorage for the results page
      localStorage.setItem('wellness_results', JSON.stringify(result));
      
      // Clear answers from localStorage
      localStorage.removeItem('wellness_user_answers');
      
      // Navigate to results page
      router.push('/wellness/results');
    } catch (error) {
      console.error('Error submitting wellness answers:', error);
      setError('Failed to submit answers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    // List of optional question IDs
    const optionalIds = ['life_span_grandparents', 'avg_glucose_level'];
    
    if (!questions[activeQuestion]) return;

    if (!optionalIds.includes(questions[activeQuestion].id)) {
      if (!(questions[activeQuestion].id in userAnswers) || userAnswers[questions[activeQuestion].id] === '') {
        setError('Please answer before continuing.');
        return;
      }
    }

    setError('');
    if (activeQuestion < questions.length - 1) {
      setActiveQuestion(prev => prev + 1);
    } else {
      handleSubmit();
    }
  };

  const handleChange = (e: any) => {
    if (!questions[activeQuestion]) return;
    
    let val = e.target.value;
    if (questions[activeQuestion].type === 'numeric') {
      val = parseFloat(val);
      if (isNaN(val)) val = '';
    }
    setUserAnswers(prev => ({ ...prev, [questions[activeQuestion].id]: val }));
  };

  const handleOptionClick = (option: string) => {
    if (!questions[activeQuestion]) return;
    
    setUserAnswers(prev => ({ ...prev, [questions[activeQuestion].id]: option }));
    setError('');
  };

  const renderInput = () => {
    if (!questions[activeQuestion]) return null;
    
    const current = questions[activeQuestion];

    if (current.type === 'categorical' || current.type === 'ordinal') {
      return current.options?.map(option => (
        <li
          key={option}
          onClick={() => handleOptionClick(option)}
          className={`option-card ${userAnswers[current.id] === option ? 'option-selected' : ''}`}
        >
          {option}
        </li>
      ));
    }

    if (current.type === 'boolean') {
      return ['Yes', 'No'].map(option => (
        <li
          key={option}
          onClick={() => handleOptionClick(option)}
          className={`option-card ${userAnswers[current.id] === option ? 'option-selected' : ''}`}
        >
          {option}
        </li>
      ));
    }

    if (current.type === 'numeric') {
      return (
        <input
          type="number"
          onChange={handleChange}
          value={userAnswers[current.id] || ''}
          className="numeric-input"
          placeholder="Enter your answer"
        />
      );
    }

    return null;
  };

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  if (!questions || questions.length === 0) {
    return <div className="error">Failed to load questions. Please refresh the page.</div>;
  }

  const current = questions[activeQuestion];

  return (
    <div className="quiz-container">
      <div className="nav-header">
        <button
          className="back-arrow"
          onClick={() => setActiveQuestion(prev => Math.max(prev - 1, 0))}
          disabled={activeQuestion === 0}
        >
          «
        </button>

        <div className="progress-bar-wrapper">
          <div
            className="progress-bar-inner"
            style={{
              width: `${((activeQuestion + 1) / questions.length) * 100}%`
            }}
          />
        </div>
      </div>

      <h2 className="question-text">{current.text}</h2>
      <ul className="options-wrapper">{renderInput()}</ul>
      {error && <p className="error">{error}</p>}

      <div className="btn-container">
        <button onClick={handleNext} className="next-btn" disabled={loading}>
          {activeQuestion === questions.length - 1 ? 'Finish' : 'Next'}
        </button>
      </div>
    </div>
  );
}
