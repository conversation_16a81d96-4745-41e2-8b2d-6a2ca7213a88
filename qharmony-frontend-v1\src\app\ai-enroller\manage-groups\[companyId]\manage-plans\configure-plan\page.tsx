'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import {
  HiOutlineArrowLeft,
  HiOutlineX,
  HiOutlinePlus,
  HiOutlineTrash
} from 'react-icons/hi';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import '../manage-plans.css';

interface Plan {
  _id: string;
  planName: string;
  planCode?: string;
  coverageType: string;
  planType: string;
  metalTier?: string;
  carrier?: {
    carrierName: string;
  };
}

interface CoverageTier {
  tierName: string;
  totalCost: number;
  employeeCost: number;
  employerCost: number;
}

const ConfigurePlanPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const companyId = params.companyId as string;
  const planId = searchParams.get('planId');

  const [plan, setPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // Plan configuration state
  const [contributionType, setContributionType] = useState<'Percentage' | 'Fixed'>('Percentage');
  const [coverageTiers, setCoverageTiers] = useState<CoverageTier[]>([
    {
      tierName: 'Employee Only',
      totalCost: 450.00,
      employeeCost: 90.00,
      employerCost: 360.00
    },
    {
      tierName: 'Employee + Spouse',
      totalCost: 890.00,
      employeeCost: 178.00,
      employerCost: 712.00
    },
    {
      tierName: 'Employee + Children',
      totalCost: 720.00,
      employeeCost: 144.00,
      employerCost: 576.00
    },
    {
      tierName: 'Employee + Family',
      totalCost: 1250.00,
      employeeCost: 250.00,
      employerCost: 1000.00
    }
  ]);

  const managedCompanies = useSelector((state: RootState) => state.user.managedCompanies);
  const company = managedCompanies?.find(c => c._id === companyId);

  const fetchPlanDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const getUserId = () => localStorage.getItem('userid1') || localStorage.getItem('userId') || '6838677aef6db0212bcfdacd';

      const response = await fetch(
        `${API_BASE_URL}/api/plans/${planId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'user-id': getUserId(),
          },
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log('Plan details result:', result);
        setPlan(result.plan || result);
      } else {
        throw new Error('Failed to fetch plan details');
      }
    } catch (error) {
      console.error('Error fetching plan details:', error);
      setError('Failed to load plan details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (planId) {
      fetchPlanDetails();
    }
  }, [planId, fetchPlanDetails]);

  const handleBack = () => {
    router.push(`/ai-enroller/manage-groups/${companyId}/manage-plans/add-plan`);
  };

  const handleCancel = () => {
    router.push(`/ai-enroller/manage-groups/${companyId}/manage-plans`);
  };

  const handleTierChange = (index: number, field: keyof CoverageTier, value: string | number) => {
    const updatedTiers = [...coverageTiers];
    updatedTiers[index] = {
      ...updatedTiers[index],
      [field]: field === 'tierName' ? value : parseFloat(value.toString()) || 0
    };

    // Recalculate employer/employee costs based on percentage
    if (field === 'totalCost' || field === 'employeeCost') {
      const totalCost = updatedTiers[index].totalCost;
      const employeeCost = updatedTiers[index].employeeCost;
      updatedTiers[index].employerCost = totalCost - employeeCost;
    }

    setCoverageTiers(updatedTiers);
  };

  const handleEmployerPercentageChange = (index: number, percentage: number) => {
    const updatedTiers = [...coverageTiers];
    const totalCost = updatedTiers[index].totalCost;
    const employerCost = (totalCost * percentage) / 100;
    const employeeCost = totalCost - employerCost;

    updatedTiers[index] = {
      ...updatedTiers[index],
      employerCost: employerCost,
      employeeCost: employeeCost
    };

    setCoverageTiers(updatedTiers);
  };

  const addTier = () => {
    setCoverageTiers([
      ...coverageTiers,
      {
        tierName: 'New Tier',
        totalCost: 0,
        employeeCost: 0,
        employerCost: 0
      }
    ]);
  };

  const removeTier = (index: number) => {
    if (coverageTiers.length > 1) {
      setCoverageTiers(coverageTiers.filter((_, i) => i !== index));
    }
  };

  const handleSaveChanges = async () => {
    try {
      setSaving(true);

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const getUserId = () => localStorage.getItem('userid1') || localStorage.getItem('userId') || '6838677aef6db0212bcfdacd';

      // Create plan assignment with deactivated status
      const assignmentData = {
        planId: planId,
        companyId: companyId,
        groupNumber: `POL-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
        waitingPeriod: { enabled: false, days: 0, rule: 'Immediate' },
        enrollmentType: 'Active',
        rateStructure: 'Flat',
        coverageTiers: coverageTiers,
        planEffectiveDate: new Date().toISOString(),
        planEndDate: new Date(new Date().getFullYear() + 1, 11, 31).toISOString(),
        enrollmentStartDate: new Date().toISOString(),
        enrollmentEndDate: new Date(new Date().getFullYear(), 11, 31).toISOString(),
        employerContribution: { contributionType: contributionType, contributionAmount: 80 },
        employeeContribution: { contributionType: contributionType, contributionAmount: 20 },
        ageBandedRates: [],
        salaryBasedRates: [],
        planCustomizations: {},
        isActive: false, // Start as deactivated
        status: 'Draft' // Draft status until dates are configured
      };

      const response = await fetch(
        `${API_BASE_URL}/api/pre-enrollment/plan-assignments`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'user-id': getUserId(),
          },
          body: JSON.stringify(assignmentData)
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log('Plan assignment created:', result);
        
        // Navigate back to manage plans
        router.push(`/ai-enroller/manage-groups/${companyId}/manage-plans`);
      } else {
        throw new Error('Failed to create plan assignment');
      }
    } catch (error) {
      console.error('Error saving plan assignment:', error);
      alert('Failed to save plan configuration. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="manage-plans-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading plan details...</p>
        </div>
      </div>
    );
  }

  if (error || !plan) {
    return (
      <div className="manage-plans-page">
        <div className="error-container">
          <p>{error || 'Plan not found'}</p>
          <button onClick={handleBack} className="back-button">
            <HiOutlineArrowLeft size={20} />
            Back to Plan Selection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="manage-plans-page">
      {/* Modal-like Header */}
      <div className="modal-header">
        <div className="modal-title">
          <h1>Edit Plan</h1>
          <button onClick={handleCancel} className="close-button">
            <HiOutlineX size={24} />
          </button>
        </div>
      </div>

      {/* Plan Info Section */}
      <div className="plan-info-section">
        <div className="plan-title">
          <h2>{plan.planName}</h2>
          <span className="status-badge active">active</span>
        </div>
        <div className="plan-carrier">{plan.carrier?.carrierName}</div>
        
        <div className="plan-details-grid">
          <div className="detail-item">
            <span className="label">Type:</span>
            <span className="value">{plan.planType}</span>
          </div>
          <div className="detail-item">
            <span className="label">Plan Code:</span>
            <span className="value">{plan.planCode || 'N/A'}</span>
          </div>
          <div className="detail-item">
            <span className="label">Category:</span>
            <span className="value">{plan.coverageType}</span>
          </div>
          <div className="detail-item">
            <span className="label">Policy #:</span>
            <span className="value">POL-{Math.random().toString(36).substr(2, 9).toUpperCase()}</span>
          </div>
        </div>
      </div>

      {/* Coverage Tiers & Contributions */}
      <div className="coverage-section">
        <div className="section-header">
          <h3>Coverage Tiers & Contributions</h3>
          <div className="contribution-type-selector">
            <select 
              value={contributionType} 
              onChange={(e) => setContributionType(e.target.value as 'Percentage' | 'Fixed')}
            >
              <option value="Percentage">Percentage</option>
              <option value="Fixed">Fixed Amount</option>
            </select>
            <button onClick={addTier} className="add-tier-btn">
              <HiOutlinePlus size={16} />
              Add
            </button>
          </div>
        </div>

        <div className="tiers-table">
          <div className="table-header">
            <div className="col-tier">Coverage Tier</div>
            <div className="col-premium">Premium</div>
            <div className="col-employer">Employer (%)</div>
            <div className="col-employer-pays">Employer Pays</div>
            <div className="col-employee-pays">Employee Pays</div>
            <div className="col-actions"></div>
          </div>

          {coverageTiers.map((tier, index) => (
            <div key={index} className="table-row">
              <div className="col-tier">
                <input
                  type="text"
                  value={tier.tierName}
                  onChange={(e) => handleTierChange(index, 'tierName', e.target.value)}
                  className="tier-name-input"
                />
              </div>
              <div className="col-premium">
                <input
                  type="number"
                  value={tier.totalCost}
                  onChange={(e) => handleTierChange(index, 'totalCost', e.target.value)}
                  className="cost-input"
                  step="0.01"
                />
              </div>
              <div className="col-employer">
                <input
                  type="number"
                  value={tier.totalCost > 0 ? Math.round((tier.employerCost / tier.totalCost) * 100) : 0}
                  onChange={(e) => handleEmployerPercentageChange(index, parseFloat(e.target.value) || 0)}
                  className="percentage-input"
                  min="0"
                  max="100"
                />
              </div>
              <div className="col-employer-pays">
                <span className="cost-display">${tier.employerCost.toFixed(2)}</span>
              </div>
              <div className="col-employee-pays">
                <span className="cost-display">${tier.employeeCost.toFixed(2)}</span>
              </div>
              <div className="col-actions">
                {coverageTiers.length > 1 && (
                  <button
                    onClick={() => removeTier(index)}
                    className="remove-tier-btn"
                  >
                    <HiOutlineTrash size={16} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="modal-actions">
        <button onClick={handleCancel} className="cancel-btn">
          Cancel
        </button>
        <button 
          onClick={handleSaveChanges} 
          className="save-btn"
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
};

export default ConfigurePlanPage;
