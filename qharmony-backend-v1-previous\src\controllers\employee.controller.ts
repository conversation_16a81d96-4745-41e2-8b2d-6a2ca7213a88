import express from 'express';
import Controller from '../interfaces/controller.interface';
import logger from '../utils/logger';
import UserModelClass from '../nosql/user.model';
import CompanyModelClass from '../nosql/company.model';
import { AuthService } from '../services/auth.service';
// import AWSService from '../services/aws';
import AzureBlobService from '../services/azure';

class EmployeeController implements Controller {
  public router = express.Router();
  private authService = new AuthService();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post('/employee/onboard', this.onboardEmployee);
    this.router.post('/employee/offboard', this.offboardEmployee);
    this.router.get('/employee', this.employeeDetails);
    this.router.post('/user/self-onboard', this.selfOnboardEmployee);
    this.router.post(
      '/teams/user/self-onboard',
      this.selfOnboardEmployeeViaTeams
    );
    this.router.get('/employee/company-details', this.getCompanyDetails); // New route
    this.router.post('/employee/enable', this.enableEmployee);

    this.router.get('/users', this.getAllUsers);
  }

  private onboardEmployee = async (
    request: express.Request,
    response: express.Response
  ) => {
    // used when admin activates already present employees from the database
    try {
      const { companyId } = request.body as { companyId: string };
      const { userId } = request.body as {
        userId: string;
      };

      const user = await UserModelClass.getDataById(userId);

      if (!user) {
        response.status(400).json({ error: 'User not found' });
        return;
      }

      if (user.companyId !== companyId) {
        response
          .status(400)
          .json({ error: 'User does not belong to the company' });
        return;
      }

      await UserModelClass.updateData(userId, { isActivated: true });

      response.status(200).json({ isSuccess: true });
    } catch (error) {
      logger.error(error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private offboardEmployee = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { companyId } = request.body as { companyId: string };
      const { userId } = request.body as {
        userId: string;
      };

      const user = await UserModelClass.getDataById(userId);

      if (!user) {
        response.status(400).json({ error: 'User not found' });
        return;
      }

      if (user.companyId !== companyId) {
        response
          .status(400)
          .json({ error: 'User does not belong to the company' });
        return;
      }

      await UserModelClass.updateData(userId, {
        isActivated: false,
        isDisabled: true,
      });

      response.status(200).json({ isSuccess: true });
      return;
    } catch (error) {
      logger.error(error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private employeeDetails = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;

      const user = await UserModelClass.getDataById(userId);

      if (!user) {
        response.status(400).json({ error: 'User not found' });
        return;
      }
      response.status(200).json({ currentUser: user });
    } catch (error) {
      logger.error(error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private selfOnboardEmployee = async (
    request: express.Request,
    response: express.Response
  ) => {
// Used when an employee tries to register themselves
// Checks if the user already exists
// If yes and not disabled, sends a login magic link
// If yes but disabled, tells them to ask admin
// If no, checks if their domain matches a company
// If domain matches, tells them to ask admin
// If domain doesn't match, offers admin onboarding
    try {
      let { userEmail } = request.body as { userEmail: string };
      userEmail = userEmail.toLowerCase();

      // Check if the user exists
      const user = await UserModelClass.getDataByEmail({ email: userEmail });

      if (user) {
        // Check if the user is disabled
        if (user.isDisabled) {
          response.status(200).json({ data: 'ask_admin_to_add' });
          return;
        }

        // Check if the company exists
        const company = await CompanyModelClass.getDataById(user.companyId);
        if (company) {
          // Send user onboarding magic link
          await this.authService.sendLoginMagicLink(userEmail, {
            email: userEmail,
            companyId: user.companyId,
            userId: user._id,
          });
          response.status(200).json({ data: 'magic_link_sent' });
        } else {
          response.status(400).json({ error: 'Company not found' });
        }
      } else {
        // Check if the company exists by email domain
        const userDomain = userEmail.split('@')[1];
        const companies = await CompanyModelClass.getAllData();
        const company = companies.find(
          (company) => company.adminEmail.split('@')[1] === userDomain
        );

        if (company) {
          response.status(200).json({
            data: 'ask_admin_to_add',
          });
        } else {
          // Send admin onboarding link
          const companyDetails = {
            name: '', // companyName variable
            adminEmail: userEmail, // companyAdminEmail variable
            adminRole: '',
            companySize: 0,
            industry: '',
            location: '',
            website: '',
            howHeard: '',
            brokerId: '', // brokerId variable
            brokerageId: '', // brokerageEntity._id converted to string
            isBrokerage: false,
            isActivated: false,
          };
          const userDetails = {
            name: '', // companyAdminName variable
            email: userEmail, // companyAdminEmail variable
            isAdmin: true,
            isBroker: false,
            isActivated: true,
            role: 'admin',
            details: {
              phoneNumber: '', // Add phone number here
              department: '', // Add department here
              title: '', // Add title here
              role: '', // Add role here
            },
          };

          const additionalParams = {
            email: userEmail,
            isAdmin: true,
          };

          await this.authService.sendOnboardingMagicLink(
            userEmail,
            companyDetails,
            userDetails,
            additionalParams
          );
          response.status(200).json({ data: 'magic_link_sent' });
        }
      }
    } catch (error) {
      logger.error('Error during self-onboarding:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private selfOnboardEmployeeViaTeams = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      let { userEmail, tenantId } = request.body as { userEmail: string, tenantId: string };
      userEmail = userEmail.toLowerCase();

      console.log("tenantId >>>", tenantId)

      // Check if the user exists
      const user = await UserModelClass.getDataByEmail({ email: userEmail });

      if (user && !user.isDisabled) {
        // Check if the company exists
        const company = await CompanyModelClass.getDataById(user.companyId);
        if (company) {

          // Update company with tenantId if it doesn't exist
          if (tenantId && !company.tenantId) {
            await CompanyModelClass.updateData({
              id: String(company._id),
              data: { tenantId } // Wrap tenantId in a data object
            });
          }

          // Login User
          response.status(200).json({
            data: 'login_user',
            userId: user._id,
            companyId: user.companyId,
          });
        } else {
          response.status(400).json({ error: 'company_not_found' });
        }
      } else {
        // Check if the company exists by email domain
        const userDomain = userEmail.split('@')[1];
        const companies = await CompanyModelClass.getAllData();
        const company = companies.find(
          (company) => company.adminEmail.split('@')[1] === userDomain
        );

        if (company) {
          response.status(200).json({
            data: 'ask_admin_to_add',
          });
        } else {
          // Company does not exist, tell user to create company through website
          response.status(200).json({ data: 'create_company_on_website' });
        }
      }
    } catch (error) {
      logger.error('Error during self-onboarding:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private getCompanyDetails = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;

      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(400).json({ error: 'Invalid user' });
        return;
      }

      const company = await CompanyModelClass.getDataById(user.companyId);

      if (!company) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      // Check if company has a logo and generate a viewable URL
      if (company.details?.logo && company.details.logo !== '' && company._id) {
        // ========================================================================
        // AWS LOGIC
        // ========================================================================
        // const bucketName = `employer-${company._id.toString()}`;
        // // const logoUrl = await AWSService.generateViewableImageURL({
        // //   Bucket: bucketName,
        // //   Key: company.details.logo,
        // // });
        // ========================================================================
        // AZURE LOGIC
        // ========================================================================
        const containerName = `employer-${company._id.toString()}`;
        const blobName = company.details.logo;
        const expiryMinutes = 30;
        const blobUrl = await AzureBlobService.generateViewableImageURL(
          containerName,
          blobName,
          expiryMinutes
        );

        // company.details.logo = logoUrl;
        company.details.logo = blobUrl; // replace with blob url
      } else if (
        company.brokerageId &&
        company.brokerageId !== '' &&
        company.brokerageId !== null
      ) {
        logger.info(`Company has a brokerage ID: ${company.brokerageId}`);
        const brokerageCompany = await CompanyModelClass.getDataById(
          company.brokerageId
        );
        if (
          brokerageCompany?.details?.logo &&
          brokerageCompany.details.logo !== ''
        ) {
          logger.info(
            `Brokerage company has a logo: ${brokerageCompany.details.logo}`
          );
          // const bucketName = `employer-${company.brokerageId.toString()}`;
          // logger.info(`Bucket name: ${bucketName}`);
          // const logoUrl = await AWSService.generateViewableImageURL({
          //   Bucket: bucketName,
          //   Key: brokerageCompany.details?.logo,
          // });

          // ========================================================================
          // AZURE LOGIC
          // ========================================================================
          const containerName = `employer-${company.brokerageId.toString()}`;
          const blobName = company.details!.logo;
          const expiryMinutes = 30;
          const blobUrl = await AzureBlobService.generateViewableImageURL(
            containerName,
            blobName,
            expiryMinutes
          );

          if (company.details) {
            // company.details.logo = logoUrl;
            company.details.logo = blobUrl; // replace with blob url
          }
        } else {
          logger.info(
            `Brokerage company does not have a logo: ${company.brokerageId}`
          );
          if (company.details) {
            company.details.logo = '';
          }
        }
      } else {
        logger.info(`Company does not have a logo: ${company._id}`);
        if (company.details) {
          company.details.logo = '';
        }
      }

      response.status(200).json({ company });
    } catch (error) {
      logger.error('Error fetching company details.', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private enableEmployee = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId } = request.body as { userId: string };

      const user = await UserModelClass.getDataById(userId);

      if (!user) {
        response.status(400).json({ error: 'User not found' });
        return;
      }

      await UserModelClass.updateData(userId, {
        isDisabled: false,
        isActivated: false,
      });

      // Send login magic link after enabling the employee
      await this.authService.sendLoginMagicLink(user.email, {
        email: user.email,
        companyId: user.companyId,
        userId: user._id,
      });

      response
        .status(200)
        .json({ isSuccess: true, message: 'Magic link sent' });
    } catch (error) {
      logger.error('Error enabling employee:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private getAllUsers = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const apiKey = request.query.apiKey as string;

      if (!apiKey) {
        response.status(400).json({ error: 'API key is required' });
        return;
      }

      // Validate the API key (this is just a placeholder, implement your own validation logic)
      if (apiKey !== '24$FrostySnow') {
        response.status(403).json({ error: 'Invalid API key' });
        return;
      }

      const users = await UserModelClass.getAllData();
      const userSummaries = users.map((user) => ({
        name: user.name,
        email: user.email,
        isBroker: user.isBroker,
        isAdmin: user.isAdmin,
      }));
      response.status(200).json({ users: userSummaries });
    } catch (error) {
      logger.error('Error fetching all users:', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };
}

export default EmployeeController;
