/* Plan Renewal Detail Styles */
.plan-renewal-detail {
  min-height: 100vh;
  background-color: #f8fafc;
  padding: 2rem;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: 1px solid #e5e7eb;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.header-info {
  text-align: center;
  flex: 1;
}

.header-info h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.header-info h2 {
  font-size: 1rem;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.step-indicator {
  font-size: 0.875rem;
  color: #6b7280;
}

.completion-status {
  font-size: 0.875rem;
  color: #059669;
  font-weight: 500;
}

/* Renewal Steps */
.renewal-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1.5rem;
  border-radius: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
  position: relative;
  overflow-x: auto;
}

.renewal-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 120px;
  text-align: center;
}

.step-number {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
}

.renewal-step.active .step-number {
  background: #3b82f6;
  color: white;
}

.step-content {
  text-align: center;
}

.step-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.step-subtitle {
  font-size: 0.625rem;
  color: #9ca3af;
}

.step-connector {
  position: absolute;
  top: 1rem;
  left: 60%;
  right: -60%;
  height: 1px;
  background: #e5e7eb;
  z-index: 1;
}

/* Plan Portfolio */
.plan-portfolio {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
  overflow: hidden;
}

.portfolio-header {
  padding: 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.portfolio-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.portfolio-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.portfolio-header p {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
}

/* Plans Grid */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.plan-card {
  background: #fafbfc;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
  position: relative;
}

.plan-card.selected {
  border-color: #3b82f6;
  background: #f0f9ff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.plan-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.plan-select {
  flex-shrink: 0;
}

.select-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #9ca3af;
  transition: all 0.2s ease;
}

.select-btn.selected {
  color: #3b82f6;
}

.plan-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.plan-icon {
  background: #f0f9ff;
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: #3b82f6;
}

.plan-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.plan-carrier {
  font-size: 0.875rem;
  color: #6b7280;
}

.plan-type-badge {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.plan-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-row .label {
  font-size: 0.875rem;
  color: #6b7280;
}

.detail-row .value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
}

.premium-section {
  margin-bottom: 1rem;
}

.premium-section h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
}

.premium-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.premium-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 0.375rem;
  font-size: 0.75rem;
}

.premium-label {
  color: #6b7280;
}

.premium-value {
  font-weight: 600;
  color: #1e293b;
}

.plan-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.enrollment-info,
.expiration-info {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: #6b7280;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: #f8fafc;
  border-top: 1px solid #f1f5f9;
}

.actions-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
}

.actions-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.actions-info p {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
  max-width: 500px;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.action-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.action-btn.secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

/* Continue Section */
.continue-section {
  padding: 2rem;
  text-align: center;
  border-top: 1px solid #f1f5f9;
}

.continue-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.continue-btn.enabled:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.continue-btn.disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plan-renewal-detail {
    padding: 1rem;
  }
  
  .detail-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .renewal-steps {
    overflow-x: auto;
    padding: 1rem;
  }
  
  .plans-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .quick-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
