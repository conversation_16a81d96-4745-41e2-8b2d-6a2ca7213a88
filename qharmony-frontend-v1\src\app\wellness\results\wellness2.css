/* wellness.css */
.wellness-body {
    background-color: #000;
    color: #f0f0f0;
    font-family: "SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  }
  
  .wellness-nav {
    background: #000;
    padding: 1rem;
    text-align: center;
  }
  .wellness-nav a {
    color: #eeabe1;
    margin: 0 1rem;
    text-decoration: none;
    font-weight: bold;
  }
  .wellness-nav a:hover {
    text-decoration: underline;
  }
  
  .section {
    background: rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(8px);
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem auto;
    border: 0px solid rgba(255, 255, 255, 0.05);
  }
  
  .container {
    max-width: 720px;
  }
  
  h2 {
    color: #eeabe1;
    text-align: center;
  }
  p {
    text-align: center;
    line-height: 1.6;
  }
  
  .highlight {
    font-size: 2.1rem;
    font-weight: bold;
    color: #eeabe1;
    margin-bottom: 0rem;
    text-align: center;
  }
  .highlight a {
    font-size: 0.8rem;
    color: #eeabe1;
    text-decoration: underline;
    margin-left: 10px;
  }
  
  .share-button,
  .reminder-button {
    display: inline-block;
    margin: 1rem;
    padding: 0.75rem 1.5rem;
    background-image: linear-gradient(to right, #643ff9d6 40%,#aa75ffcc);
    color: #0d0d0d;
    font-weight: bold;
    border: none;
    border-radius: 800px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s ease;
    font-family: "SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  }

  .share-button:hover,
  .reminder-button:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 204, 255, 0.3);
  }

  
  .centered {
    text-align: center;
    margin-top: 1.5rem;
  }
  .faces img {
    border-radius: 50%;
    width: 48px;
    height: 48px;
    margin: 0 4px;
    vertical-align: middle;
  }
  .faces p {
    margin-top: 10px;
  }
  
  .disclaimer {
    font-size: 0.85rem;
    color: #888;
    text-align: center;
    margin-top: 3rem;
    padding: 1rem;
    border-top: 1px solid #b1b1b165;
    max-width: 720px;
    margin-left: auto;
    margin-right: auto;
  }
  
  
  
  .wellness-footer {
    text-align: center;
    padding: 1.5rem;
    font-size: 0.9rem;
    color: #777;
    background: #000;
    margin-top: 4rem;
    
  }
  .demo-banner {
  background-color: #ffe8cc;
  color: #663c00;
  padding: 10px;
  text-align: center;
  margin-bottom: 20px;
  border-radius: 5px;
}

.demo-banner button {
  background-color: #ff9800;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
}

.demo-banner button:hover {
  background-color: #e68a00;
}

/* Add these styles to your wellness2.css file */
.benefits-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
}

.benefit-link {
  background-color: #111;
  border: 1px solid #333;
  border-radius: 8px;
  font-family: "SF Pro",-apple-system,BlinkMacSystemFont,sans-serif;
  color: #fff;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  padding: 6px 16px;
  font-size: 0.95rem;
  margin: 5px;
  transition: background-color 0.3s ease;
}

.benefit-link:hover {
  background-color: #222;
  border-color: #444;
}

.no-benefits-message {
  text-align: center;
  width: 100%;
  padding: 20px;
  color: #888;
}

@media (max-width: 600px) {
  .benefit-link {
    padding: 4px 12px;
    font-size: 0.8rem;
  }
}
