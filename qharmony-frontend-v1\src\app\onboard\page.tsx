"use client";

import React, { useEffect, useState, FC } from "react";
import { isSignInWithEmailLink, signInWithEmailLink } from "firebase/auth";
import { auth } from "@/utils/firebase";
import {
  Box,
  Typography,
  Button,
  TextField,
  Grid,
  CircularProgress,
  MenuItem,
  FormControl,
  FormLabel,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { parseParamsFromUrl } from "@/middleware/user_middleware";
import { onboardAdmin } from "@/middleware/company_middleware";
import { useAppDispatch } from "@/redux/hooks";
import Image from "next/image";
import Harmony<PERSON>ogo from "../../../public/logo.png";
import RightPanelOnlyComponent from "@/components/RightPanelOnlyComponent";
import Link from 'next/link';

interface UserDetails {
  email: string;
  name: string;
  role: string;
  isAdmin: boolean;
  isBroker: boolean;
  isActivated: boolean;
}

interface CompanyDetails {
  name: string;
  adminEmail: string;
  adminRole: string;
  companySize: number;
  industry: string;
  location: string;
  website: string;
  brokerId: string;
  brokerageId: string;
  isBrokerage: boolean;
  isActivated: boolean;
  howHeard: string;
  referralSource: string;
}

interface AdditionalParams {
  isAdmin: boolean;
}

const StyledInput: FC<{
  label: string;
  value: any;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  helperText?: string;
  placeholder?: string;
  select?: boolean;
  children?: React.ReactNode;
  readOnly?: boolean;
}> = ({
  label,
  value,
  onChange,
  error,
  helperText,
  placeholder,
  select,
  children,
  readOnly,
}) => (
  <FormControl fullWidth>
    <FormLabel
      sx={{
        color: "#ffffff",
        fontWeight: "500",
        mb: 1,
        fontSize: "17px",
      }}
    >
      {label}
    </FormLabel>
    <TextField
      variant="outlined"
      value={value}
      onChange={onChange}
      error={!!error}
      helperText={helperText}
      placeholder={placeholder}
      select={select}
      inputProps={{ readOnly }}
      sx={{
        borderRadius: "10px",
        backgroundColor: "#333333",
        mb: 0,
        input: { color: "#ffffff" },
        "& .MuiOutlinedInput-root": {
          borderRadius: "10px",
          "&:hover fieldset": {
            borderColor: error ? "#ff0000" : "#ffffff",
          },
          "&.Mui-focused fieldset": {
            borderColor: error ? "#ff0000" : "#ffffff",
          },
        },
        "& .MuiInputLabel-root": { color: "#ffffff" },
        "& .MuiSvgIcon-root": { color: "#ffffff" },
        "& .MuiSelect-select": { color: "#ffffff" },
      }}
    >
      {children}
    </TextField>
  </FormControl>
);

const OnboardWithEmailLink: FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [openDialog, setOpenDialog] = useState(false);
  const [email, setEmail] = useState<string | null>(null);
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [emailError, setEmailError] = useState<boolean | null>(false);

  const [userDetails, setUserDetails] = useState<UserDetails>({
    email: "",
    name: "",
    role: "",
    isAdmin: false,
    isBroker: false,
    isActivated: false,
  });
  const [companyDetails, setCompanyDetails] = useState<CompanyDetails>({
    name: "",
    adminEmail: "",
    adminRole: "",
    companySize: 0,
    industry: "",
    location: "",
    website: "",
    brokerId: "",
    brokerageId: "",
    isBrokerage: false,
    isActivated: false,
    howHeard: "",
    referralSource: "",
  });
  const [additionalParams, setAdditionalParams] = useState<AdditionalParams>({
    isAdmin: false,
  });

  const [errors, setErrors] = useState<any>({});
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [termsError, setTermsError] = useState("");

  useEffect(() => {
    const handleSignInLinkRedirect = async () => {
      const url = window.location.href;
      const params = await parseParamsFromUrl(url);

      if (params.companyDetails) {
        setCompanyDetails(params.companyDetails);
      }

      if (params.userDetails) {
        setUserDetails(params.userDetails);
      }

      if (params.additionalParams) {
        setAdditionalParams(params.additionalParams);
        if (params.additionalParams.email) {
          setEmail(params.additionalParams.email);
        }
      }

      if (isSignInWithEmailLink(auth, url)) {
        let storedEmail = window.localStorage.getItem("emailForSignIn1");
        if (!storedEmail && !params.additionalParams.email) {
          setOpenDialog(true);
        } else {
          setEmail(storedEmail || params.additionalParams.email);
        }
      }
      setLoading(false);
    };

    handleSignInLinkRedirect();
  }, []);

  useEffect(() => {
    const signIn = async () => {
      if (email) {
        try {
          await signInWithEmailLink(auth, email, window.location.href);
          console.log("Sign-in successful");
          window.localStorage.removeItem("emailForSignIn1");
          setIsSignedIn(true);
        } catch (error) {
          console.error("Error signing in with email link:", error);
          setEmailError(true);
        }
      }
    };

    signIn();
  }, [email]);

  const validateFields = () => {
    let newErrors: any = {};
    let isValid = true;

    // Validate userDetails fields
    if (!userDetails.name) {
      newErrors.name = "Full Name is required";
      isValid = false;
    }

    // Validate terms agreement
    if (!agreedToTerms) {
      setTermsError("You must accept the Terms & Conditions and Privacy Policy to continue");
      isValid = false;
    } else {
      setTermsError("");
    }

    // Validate companyDetails fields (if the user is an admin)
    if (additionalParams.isAdmin) {
      if (!companyDetails.name) {
        newErrors.companyName = "Company Name is required";
        isValid = false;
      }
      if (!companyDetails.adminRole) {
        newErrors.adminRole = "Role/Title is required";
        isValid = false;
      }
      if (!companyDetails.companySize) {
        newErrors.companySize = "Company Size is required";
        isValid = false;
      }
      if (!companyDetails.industry) {
        newErrors.industry = "Industry is required";
        isValid = false;
      }
      if (!companyDetails.location) {
        newErrors.location = "Country is required";
        isValid = false;
      }

      if (!companyDetails.howHeard) {
        newErrors.howHeard = "How did you hear about us is required";
        isValid = false;
      }
      if (!companyDetails.referralSource) {
        newErrors.referralSource = "Referral Source is required";
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleConfirmDetails = async () => {
    if (!validateFields()) {
      return;
    }

    setSubmitting(true);

    const response = await onboardAdmin(dispatch, companyDetails, userDetails);
    if (response && response.status === 200) {
      router.push("/dashboard");
    }
    setSubmitting(false);
  };

  const handleInputChange = (
    field: string,
    value: any,
    section: "userDetails" | "companyDetails",
  ) => {
    setErrors({ ...errors, [field]: "" }); // Clear the error for the field

    if (section === "userDetails") {
      setUserDetails({ ...userDetails, [field]: value });
    } else {
      setCompanyDetails({ ...companyDetails, [field]: value });
    }
  };

  return (
    <Box
      sx={{
        backgroundColor: "#000000", // Full black background
        minHeight: "98px",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "flex-start",
      }}
    >
      {/* Logo at the top */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          mb: 5,
          cursor: "pointer",
          position: "absolute",
          top: "30px", // Moves the logo to the top
          left: "30px", // Align with the start of the text
        }}
        onClick={() => console.log("Logo Clicked")}
      >
        <Image src={HarmonyLogo} alt="BenOsphere Logo" width={40} height={40} />
        <Typography
          variant="h6"
          sx={{ ml: 1, fontWeight: "800", color: "#ffffff" }}
        >
          BenOsphere
        </Typography>
      </Box>

      {/* Text and Form */}
      {loading ? (
        <CircularProgress color="inherit" />
      ) : emailError ? (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            color: "white",
            textAlign: "center",
            height: "100%", // Take up the entire height
          }}
        >
          <Typography sx={{ fontWeight: "bold", fontSize: "60px", mb: 2 }}>
            ❌ Invalid Magic Link
          </Typography>
          <Typography
            variant="body1"
            sx={{ fontSize: "20px", color: "#bbbbbb", mb: 4 }}
          >
            You can sign in again by requesting a new link <Link href="/" style={{ color: '#B983FF', textDecoration: 'underline' }}>here</Link>.
          </Typography>
        </Box>
      ) : (
        isSignedIn && (
          <Box sx={{ width: "100%", maxWidth: "100%" }}>
            <Typography
              variant="h4"
              sx={{
                fontSize: "50px", // Matches the font size in the screenshot
                fontWeight: "bold",
                lineHeight: "1.2",
                color: "#ffffff",
                mb: 3,
              }}
            >
              Get Free Access
            </Typography>

            <Grid container spacing={3} sx={{ marginBottom: "16px" }}>
              <Grid item xs={12}>
                <StyledInput
                  label="Full Name"
                  value={userDetails.name}
                  onChange={(e) =>
                    handleInputChange("name", e.target.value, "userDetails")
                  }
                  error={errors.name}
                  helperText={errors.name}
                  placeholder="John Doe"
                />
              </Grid>
              <Grid item xs={12}>
                <StyledInput
                  label="Email"
                  value={userDetails.email}
                  onChange={() => {}}
                  readOnly
                />
              </Grid>
            </Grid>

            {additionalParams.isAdmin && (
              <Box sx={{ mt: 3 }}>
                <Grid container spacing={3} sx={{ marginBottom: "16px" }}>
                  <Grid item xs={12}>
                    <StyledInput
                      label="Company Name"
                      value={companyDetails.name}
                      onChange={(e) =>
                        handleInputChange(
                          "name",
                          e.target.value,
                          "companyDetails",
                        )
                      }
                      error={errors.companyName}
                      helperText={errors.companyName}
                      placeholder="Doe LLC"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <StyledInput
                      label="Role/Title"
                      value={companyDetails.adminRole}
                      onChange={(e) =>
                        handleInputChange(
                          "adminRole",
                          e.target.value,
                          "companyDetails",
                        )
                      }
                      error={errors.adminRole}
                      helperText={errors.adminRole}
                      select
                    >
                      <MenuItem value="CEO">CEO</MenuItem>
                      <MenuItem value="HR Manager">HR Manager</MenuItem>
                      <MenuItem value="Benefits Specialist">
                        Benefits Specialist
                      </MenuItem>
                      <MenuItem value="CFO">CFO</MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                    </StyledInput>
                  </Grid>
                  <Grid item xs={12}>
                    <StyledInput
                      label="Company Size"
                      value={companyDetails.companySize}
                      onChange={(e) =>
                        handleInputChange(
                          "companySize",
                          e.target.value,
                          "companyDetails",
                        )
                      }
                      error={errors.companySize}
                      helperText={errors.companySize}
                      select
                    >
                      <MenuItem value={1}>1-10 employees</MenuItem>
                      <MenuItem value={2}>11-50 employees</MenuItem>
                      <MenuItem value={3}>51-200 employees</MenuItem>
                      <MenuItem value={4}>201-500 employees</MenuItem>
                      <MenuItem value={5}>500+ employees</MenuItem>
                    </StyledInput>
                  </Grid>
                  <Grid item xs={12}>
                    <StyledInput
                      label="Industry"
                      value={companyDetails.industry}
                      onChange={(e) =>
                        handleInputChange(
                          "industry",
                          e.target.value,
                          "companyDetails",
                        )
                      }
                      error={errors.industry}
                      helperText={errors.industry}
                      select
                    >
                      <MenuItem value="Technology">Technology</MenuItem>
                      <MenuItem value="Healthcare">Healthcare</MenuItem>
                      <MenuItem value="Finance">Finance</MenuItem>
                      <MenuItem value="Education">Education</MenuItem>
                      <MenuItem value="Insurance Broker">
                        Insurance Broker
                      </MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                    </StyledInput>
                  </Grid>
                  <Grid item xs={12}>
                    <StyledInput
                      label="Country"
                      value={companyDetails.location}
                      onChange={(e) =>
                        handleInputChange(
                          "location",
                          e.target.value,
                          "companyDetails",
                        )
                      }
                      error={errors.location}
                      helperText={errors.location}
                      select
                    >
                      <MenuItem value="United States">United States</MenuItem>
                    </StyledInput>
                  </Grid>
                  <Grid item xs={12}>
                    <StyledInput
                      label="How did you hear about us"
                      value={companyDetails.howHeard}
                      onChange={(e) =>
                        handleInputChange(
                          "howHeard",
                          e.target.value,
                          "companyDetails",
                        )
                      }
                      error={errors.howHeard}
                      helperText={errors.howHeard}
                      select
                    >
                      <MenuItem value="Referral">Referral</MenuItem>
                      <MenuItem value="Social Media">Social Media</MenuItem>
                      <MenuItem value="Search Engine">Search Engine</MenuItem>
                      <MenuItem value="Advertisement">Advertisement</MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                    </StyledInput>
                  </Grid>
                  <Grid item xs={12}>
                    <StyledInput
                      label="Referral Source"
                      value={companyDetails.referralSource}
                      onChange={(e) =>
                        handleInputChange(
                          "referralSource",
                          e.target.value,
                          "companyDetails",
                        )
                      }
                      error={errors.referralSource}
                      helperText={errors.referralSource}
                      placeholder="LinkedIn, Twitter, etc."
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            <Box sx={{ mt: 4 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={agreedToTerms}
                    onChange={(e) => {
                      setAgreedToTerms(e.target.checked);
                      if (e.target.checked) setTermsError("");
                    }}
                    sx={{
                      color: "#ffffff",
                      '&.Mui-checked': {
                        color: "#B983FF",
                      },
                    }}
                  />
                }
                label={
                  <Typography sx={{ color: "#ffffff", fontSize: "14px" }}>
                    I agree to the{" "}
                    <Link 
                      href="https://benosphere.com/terms-conditions" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      style={{ color: '#B983FF', textDecoration: 'underline' }}
                    >
                      Terms & Conditions
                    </Link>{" "}
                    and{" "}
                    <Link 
                      href="https://benosphere.com/privacy-policy" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      style={{ color: '#B983FF', textDecoration: 'underline' }}
                    >
                      Privacy Policy
                    </Link>
                    .
                  </Typography>
                }
              />
              {termsError && (
                <Typography sx={{ color: "#ff4d4f", fontSize: "12px", mt: 1 }}>
                  {termsError}
                </Typography>
              )}
              {submitting ? (
                <CircularProgress size={24} sx={{ color: "#ffffff" }} />
              ) : (
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  sx={{
                    background: "linear-gradient(90deg, #7206E6, #B54BFF)", // Gradient to match the image
                    padding: "12px",
                    fontSize: "16px",
                    textTransform: "none",
                    borderRadius: 2,
                    mb: 10,
                  }}
                  onClick={handleConfirmDetails}
                >
                  Confirm Details
                </Button>
              )}
            </Box>
          </Box>
        )
      )}
    </Box>
  );
};

const AdminOnboardComponent = () => {
  return <RightPanelOnlyComponent LeftComponent={<OnboardWithEmailLink />} />;
};

export default AdminOnboardComponent;
