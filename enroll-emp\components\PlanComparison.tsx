
import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, CheckCircle, XCircle, X } from 'lucide-react';

interface Plan {
  name: string;
  type: string;
  cost: number;
  deductible: number;
  features: string[];
  network: string;
  copays: {
    primaryCare: string;
    specialist: string;
    emergency: string;
  };
}

const samplePlans: Plan[] = [
  {
    name: "Anthem PPO 035",
    type: "PPO",
    cost: 82.90,
    deductible: 2000,
    features: ["Large provider network", "No referrals needed", "Out-of-network coverage"],
    network: "Large",
    copays: { primaryCare: "$25", specialist: "$50", emergency: "$200" }
  },
  {
    name: "Kaiser HMO", 
    type: "HMO",
    cost: 65.40,
    deductible: 1500,
    features: ["Integrated care", "Lower costs", "Coordinated care"],
    network: "Kaiser only",
    copays: { primaryCare: "$20", specialist: "$35", emergency: "$150" }
  },
  {
    name: "Blue Cross HSA",
    type: "HDHP",
    cost: 45.20,
    deductible: 3000,
    features: ["HSA eligible", "Lower premium", "Tax advantages"],
    network: "Large",
    copays: { primaryCare: "After deductible", specialist: "After deductible", emergency: "After deductible" }
  }
];

export const PlanComparison = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPlans, setSelectedPlans] = useState<string[]>([]);

  const togglePlanSelection = (planName: string) => {
    setSelectedPlans(prev => 
      prev.includes(planName)
        ? prev.filter(p => p !== planName)
        : prev.length < 3 ? [...prev, planName] : prev
    );
  };

  const filteredPlans = samplePlans.filter(plan => 
    selectedPlans.length === 0 || selectedPlans.includes(plan.name)
  );

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
      >
        <BarChart className="w-4 h-4" />
        Compare Plans
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <BarChart className="w-5 h-5" />
                  Medical Plan Comparison
                </h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              {/* Plan Selection */}
              <div className="flex gap-2 flex-wrap items-center">
                <span className="text-sm text-gray-600">Select plans to compare:</span>
                {samplePlans.map(plan => (
                  <button
                    key={plan.name}
                    onClick={() => togglePlanSelection(plan.name)}
                    disabled={!selectedPlans.includes(plan.name) && selectedPlans.length >= 3}
                    className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                      selectedPlans.includes(plan.name)
                        ? 'bg-blue-500 text-white'
                        : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                    } ${!selectedPlans.includes(plan.name) && selectedPlans.length >= 3 ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {plan.name}
                  </button>
                ))}
              </div>

              {/* Comparison Table */}
              <div className="bg-white border rounded-lg">
                <div className="p-4 border-b">
                  <h4 className="font-semibold">Side-by-Side Comparison</h4>
                </div>
                <div className="p-4">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2 font-medium">Feature</th>
                          {filteredPlans.map(plan => (
                            <th key={plan.name} className="text-center p-2">
                              <div>
                                <div className="font-medium">{plan.name}</div>
                                <span className="inline-block px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs mt-1">{plan.type}</span>
                              </div>
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="p-2 font-medium">Monthly Cost</td>
                          {filteredPlans.map(plan => (
                            <td key={plan.name} className="text-center p-2">
                              <div className="text-lg font-semibold">${plan.cost}</div>
                              <div className="text-xs text-gray-600">per paycheck</div>
                            </td>
                          ))}
                        </tr>

                        <tr className="border-b">
                          <td className="p-2 font-medium">Annual Deductible</td>
                          {filteredPlans.map(plan => (
                            <td key={plan.name} className="text-center p-2">
                              ${plan.deductible.toLocaleString()}
                            </td>
                          ))}
                        </tr>

                        <tr className="border-b">
                          <td className="p-2 font-medium">Primary Care Visit</td>
                          {filteredPlans.map(plan => (
                            <td key={plan.name} className="text-center p-2">
                              {plan.copays.primaryCare}
                            </td>
                          ))}
                        </tr>

                        <tr className="border-b">
                          <td className="p-2 font-medium">Specialist Visit</td>
                          {filteredPlans.map(plan => (
                            <td key={plan.name} className="text-center p-2">
                              {plan.copays.specialist}
                            </td>
                          ))}
                        </tr>

                        <tr className="border-b">
                          <td className="p-2 font-medium">Emergency Room</td>
                          {filteredPlans.map(plan => (
                            <td key={plan.name} className="text-center p-2">
                              {plan.copays.emergency}
                            </td>
                          ))}
                        </tr>

                        <tr className="border-b">
                          <td className="p-2 font-medium">Provider Network</td>
                          {filteredPlans.map(plan => (
                            <td key={plan.name} className="text-center p-2">
                              {plan.network}
                            </td>
                          ))}
                        </tr>

                        <tr>
                          <td className="p-2 font-medium">Referrals Required</td>
                          {filteredPlans.map(plan => (
                            <td key={plan.name} className="text-center p-2">
                              {plan.type === 'HMO' ?
                                <XCircle className="w-4 h-4 text-red-500 mx-auto" /> :
                                <CheckCircle className="w-4 h-4 text-green-500 mx-auto" />
                              }
                            </td>
                          ))}
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              {/* Annual Cost Calculator */}
              <div className="bg-white border rounded-lg">
                <div className="p-4 border-b">
                  <h4 className="font-semibold">Annual Cost Comparison</h4>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {filteredPlans.map(plan => (
                      <div key={plan.name} className="text-center p-4 border rounded-lg">
                        <h4 className="font-medium mb-2">{plan.name}</h4>
                        <div className="text-2xl font-bold text-blue-600">
                          ${(plan.cost * 26).toFixed(0)}
                        </div>
                        <div className="text-sm text-gray-600">
                          Annual premium (26 paychecks)
                        </div>
                        <div className="text-sm mt-2">
                          + ${plan.deductible.toLocaleString()} max deductible
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
