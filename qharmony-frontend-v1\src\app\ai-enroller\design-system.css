/* AI Enroller Design System */
/* Based on user guidelines for consistent styling across ai-enroller directory */

/* ===== COLOR PALETTE ===== */
:root {
  /* Primary Colors - Blue to Purple Gradient */
  --primary-gradient: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  --primary-blue: #2563eb;
  --primary-purple: #8b5cf6;
  
  /* Blue Variants */
  --blue-50: #eff6ff;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;
  
  /* Status Colors */
  --success-bg: #dcfce7;
  --success-text: #166534;
  --success-primary: #16a34a;
  
  --error-bg: #fef2f2;
  --error-text: #dc2626;
  --error-primary: #ef4444;
  
  --warning-bg: #fef3c7;
  --warning-text: #d97706;
  --warning-primary: #f59e0b;
  
  /* Gray Scale */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Black Theme */
  --black: #000000;
  --white: #ffffff;
}

/* ===== TYPOGRAPHY ===== */
/* SF Pro Font Stack */
* {
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Typography Classes - Based on Employee Enroller Reference */
.text-xs { font-size: 12px; }      /* Small labels, timestamps */
.text-sm { font-size: 14px; }      /* Body text, navigation, buttons */
.text-lg { font-size: 18px; }      /* Card titles */
.text-xl { font-size: 20px; }      /* Section headers */
.text-2xl { font-size: 24px; }     /* Page titles */

/* Font Weights - Employee Enroller Reference */
.font-normal { font-weight: 450; }   /* Navigation steps */
.font-medium { font-weight: 500; }   /* Labels, secondary buttons */
.font-semibold { font-weight: 600; } /* Titles, primary buttons, emphasis */
.font-bold { font-weight: 700; }     /* Brand name, metric values */

/* Page Title Styling - Employee Enroller Reference */
.page-title {
  font-size: clamp(20px, 5vw, 24px); /* Responsive like employee enroller */
  line-height: 1.5;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Section Headers - Employee Enroller Reference */
.section-header {
  font-size: clamp(18px, 4vw, 20px); /* Responsive like employee enroller */
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Subtitle/Description - Employee Enroller Reference */
.subtitle-text {
  font-size: clamp(12px, 3vw, 14px); /* Responsive like employee enroller */
  line-height: 1.4;
  color: var(--gray-500);
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Body Text - Employee Enroller Reference */
.body-text {
  font-size: 14px;
  line-height: 1.6;
  color: var(--gray-700);
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Navigation Steps - Employee Enroller Reference */
.nav-step {
  font-size: 13px;
  font-weight: 450;
  line-height: 1.2;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Step Navigation Colors - Gradient Theme */
.step-completed {
  background-color: #ddd6fe; /* Light purple background */
  color: #6d28d9; /* Darker purple text */
}

.step-active {
  background-color: #ede9fe; /* Light purple (matching icon scheme) */
  color: #7c3aed; /* Purple (matching gradient) */
}

.step-pending {
  background-color: #f3f4f6; /* Light gray */
  color: #6b7280; /* Medium gray */
}

.step-tick-mark {
  background-color: #7c3aed; /* Purple matching gradient */
  color: white;
}

/* ===== BUTTON STYLES ===== */
/* Primary Button - Employee Enroller Reference */
.btn-primary {
  background-color: var(--black);
  color: var(--white);
  font-size: 14px;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.btn-primary:hover {
  background-color: var(--gray-800);
  transform: translateY(-1px);
}

/* Secondary Button - Employee Enroller Reference */
.btn-secondary {
  background-color: var(--white);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
  font-size: 14px;
  font-weight: 500;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.btn-secondary:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-300);
}

/* Gradient Button (for special cases) */
.btn-gradient {
  background: var(--primary-gradient);
  color: var(--white);
  font-size: 14px;
  font-weight: 600;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.btn-gradient:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* Icon Buttons */
.btn-icon {
  padding: 8px;
  border-radius: 8px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: var(--gray-100);
}

/* ===== COMPONENT PATTERNS ===== */
/* Cards */
.card {
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-prominent {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Form Elements */
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  background-color: var(--white);
  color: var(--gray-900);
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Form Container */
.form-container {
  padding: 32px;
  background-color: var(--white);
  border-radius: 12px;
  border: 1px solid var(--gray-200);
}

/* ===== SPACING & LAYOUT ===== */
/* Standard Spacing */
.spacing-standard > * + * {
  margin-top: 16px;
}

.spacing-large > * + * {
  margin-top: 20px;
}

/* Grid Gaps */
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

/* Border Radius - Let Tailwind handle these utilities */

/* ===== ANIMATIONS & TRANSITIONS ===== */
.transition-standard {
  transition: all 0.2s ease;
}

.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Hover Effects */
.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* ===== PROGRESS BARS ===== */
.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--black);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .page-title {
    font-size: 1.25rem; /* 20px */
  }
  
  .form-container {
    padding: 24px;
  }
  
  .btn-primary,
  .btn-black,
  .btn-secondary {
    padding: 10px 16px;
    font-size: 14px;
  }
}

/* ===== ACCESSIBILITY ===== */
/* High contrast focus states */
*:focus-visible {
  outline: 2px solid var(--black);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
