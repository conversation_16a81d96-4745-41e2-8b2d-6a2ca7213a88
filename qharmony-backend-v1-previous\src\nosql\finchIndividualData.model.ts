import mongoose, { Document, Model } from 'mongoose';

const { Schema } = mongoose;

export interface FinchIndividualDataInterface {
  finchIndividualId: string;
  finchFirstName: string;
  finchLastName: string;
  email: string;
  finchAddress: string;
  dob: string;
  department: string;
  title: string;
  employmentType: string;
  employmentSubtype: string;
  startDate: string;
  endDate: string;
  incomeUnit: string;
  incomeAmount: number;
  incomeCurrency: string;
  incomeEffectiveDate: string;
  benefits: any[];
  payments: any[];
  timeoffs: any[];
}

interface FinchIndividualDocument extends Document {
  data: FinchIndividualDataInterface[];
}

class FinchIndividualModelClass {
  private static finchIndividualModel: Model<FinchIndividualDocument>;

  public static initializeModel() {
    const schema = new Schema({
      finchIndividualId: String,
      finchFirstName: String,
      finchLastName: String,
      email: String,
      finchAddress: String,
      dob: String,
      department: String,
      title: String,
      employmentType: String,
      employmentSubtype: String,
      startDate: String,
      endDate: String,
      incomeUnit: String,
      incomeAmount: Number,
      incomeCurrency: String,
      incomeEffectiveDate: String,
      benefits: Array,
      payments: Array,
      timeoffs: Array,
    });

    this.finchIndividualModel = mongoose.model<FinchIndividualDocument>(
      'FinchIndividual',
      schema
    );
  }

  public static async addData(
    data: FinchIndividualDataInterface
  ): Promise<void> {
    try {
      // Extract the data. If slackTeamId exists, update the data. Otherwise, insert the data.
      const { finchIndividualId } = data;
      const existingData = await this.finchIndividualModel.findOne({
        finchIndividualId,
      });
      if (existingData) {
        await this.finchIndividualModel.updateOne({ finchIndividualId }, data);
      } else {
        await this.finchIndividualModel.create(data);
      }
    } catch (error) {
      console.error(error);
    }
  }

  public static async getDataByEmail(
    email: string
  ): Promise<FinchIndividualDataInterface | null> {
    try {
      const result = (await this.finchIndividualModel.findOne({
        email,
      })) as unknown as FinchIndividualDataInterface;
      return result;
    } catch (error) {
      console.error(error);
      return null;
    }
  }
}

FinchIndividualModelClass.initializeModel();

export default FinchIndividualModelClass;
