
import React, { useState, useEffect } from 'react';
import { Bot } from 'lucide-react';

interface BotQuestionProps {
  question: string;
  context?: string;
  enableStreaming?: boolean;
  onStreamingComplete?: () => void;
}

export const BotQuestion = ({ question, context, enableStreaming = true, onStreamingComplete }: BotQuestionProps) => {
  const [displayedQuestion, setDisplayedQuestion] = useState(enableStreaming ? '' : question);
  const [displayedContext, setDisplayedContext] = useState(enableStreaming ? '' : (context || ''));
  const [questionComplete, setQuestionComplete] = useState(!enableStreaming);
  const [contextComplete, setContextComplete] = useState(!context || !enableStreaming);

  // Stream question text
  useEffect(() => {
    if (enableStreaming && displayedQuestion.length < question.length) {
      const timer = setTimeout(() => {
        setDisplayedQuestion(prev => prev + question[displayedQuestion.length]);
      }, 50);
      return () => clearTimeout(timer);
    } else if (displayedQuestion.length === question.length && !questionComplete) {
      setQuestionComplete(true);
    }
  }, [displayedQuestion, question, enableStreaming, questionComplete]);

  // Stream context text after question is complete
  useEffect(() => {
    if (enableStreaming && context && questionComplete && displayedContext.length < context.length) {
      const timer = setTimeout(() => {
        setDisplayedContext(prev => prev + context[displayedContext.length]);
      }, 30);
      return () => clearTimeout(timer);
    } else if (context && questionComplete && displayedContext.length === context.length && !contextComplete) {
      setContextComplete(true);
    }
  }, [questionComplete, displayedContext, context, enableStreaming, contextComplete]);

  // Call onStreamingComplete when both are done
  useEffect(() => {
    if (questionComplete && contextComplete && onStreamingComplete) {
      onStreamingComplete();
    }
  }, [questionComplete, contextComplete, onStreamingComplete]);

  return (
    <div className="flex gap-3 mb-6">
      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
        <Bot className="w-5 h-5 text-blue-600" />
      </div>

      <div className="flex-1">
        <div className="bg-gray-100 rounded-2xl px-4 py-3">
          <p className="text-gray-900 font-medium">
            {displayedQuestion}
          </p>
          {context && questionComplete && (
            <p className="text-gray-600 text-sm mt-1">
              {displayedContext}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
