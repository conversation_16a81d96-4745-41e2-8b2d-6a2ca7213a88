/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  images: {
    domains: [
      process.env.NEXT_PUBLIC_S3_DOMAIN || 's3.amazonaws.com',
      process.env.NEXT_PUBLIC_AZURE_BLOB_DOMAIN || 'benosphere.blob.core.windows.net'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.s3.amazonaws.com',
      },
    ],
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    return config;
  },
};

export default nextConfig;
