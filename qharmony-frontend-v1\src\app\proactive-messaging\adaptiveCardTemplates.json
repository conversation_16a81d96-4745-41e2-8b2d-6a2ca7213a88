{"scheduler-nudge": {"type": "AdaptiveCard", "body": [{"type": "ColumnSet", "columns": [{"type": "Column", "items": [{"type": "Image", "style": "Person", "url": "https://firebasestorage.googleapis.com/v0/b/qharmony-dev.firebasestorage.app/o/ai_assistant.png?alt=media&token=e8aea1ee-bce5-4308-9880-0a7c6691f8ea", "altText": "Brea - Your 24/7 Benefits Specialist", "size": "Small"}], "width": "auto"}, {"type": "Column", "items": [{"type": "TextBlock", "weight": "Bolder", "text": "Brea - Your 24/7 Benefits Specialist", "wrap": true}], "width": "stretch"}]}, {"type": "TextBlock", "text": "{{ADAPTIVECARD_TEXT}}", "wrap": true}, {"type": "ActionSet", "actions": [{"type": "Action.Execute", "title": "✅ I Did It", "data": {"messageId": "{{MESSAGE_ID}}", "messageType": "I Did It"}}, {"type": "Action.Execute", "title": "👍 Good Reminder", "data": {"messageId": "{{MESSAGE_ID}}", "messageType": "Good Reminder"}}, {"type": "Action.Execute", "title": "📅 Scheduling Soon", "data": {"messageId": "{{MESSAGE_ID}}", "messageType": "Scheduling Soon"}}]}, {"type": "TextBlock", "text": "--------------------", "wrap": true, "weight": "Bolder"}, {"type": "TextBlock", "text": "AI-generated content—verify before use.", "wrap": true, "weight": "Bolder"}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.5"}, "general-nudge": {"type": "AdaptiveCard", "body": [{"type": "ColumnSet", "columns": [{"type": "Column", "items": [{"type": "Image", "style": "Person", "url": "https://firebasestorage.googleapis.com/v0/b/qharmony-dev.firebasestorage.app/o/ai_assistant.png?alt=media&token=e8aea1ee-bce5-4308-9880-0a7c6691f8ea", "altText": "Brea - Your 24/7 Benefits Specialist", "size": "Small"}], "width": "auto"}, {"type": "Column", "items": [{"type": "TextBlock", "weight": "Bolder", "text": "Brea - Your 24/7 Benefits Specialist", "wrap": true}], "width": "stretch"}]}, {"type": "TextBlock", "text": "{{ADAPTIVECARD_TEXT}}", "wrap": true}, {"type": "ActionSet", "actions": [{"type": "Action.Execute", "title": "👍 I like it", "data": {"messageId": "{{MESSAGE_ID}}", "messageType": "I like it"}}, {"type": "Action.Execute", "title": "😐 Not Relevant", "data": {"messageId": "{{MESSAGE_ID}}", "messageType": "Not Relevant"}}, {"type": "Action.Execute", "title": "✅ Done", "data": {"messageId": "{{MESSAGE_ID}}", "messageType": "Done"}}]}, {"type": "TextBlock", "text": "--------------------", "wrap": true, "weight": "Bolder"}, {"type": "TextBlock", "text": "AI-generated content—verify before use.", "wrap": true, "weight": "Bolder"}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.5"}}