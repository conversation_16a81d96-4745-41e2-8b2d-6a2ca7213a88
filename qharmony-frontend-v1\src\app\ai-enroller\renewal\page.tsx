'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { HiOutlineArrowLeft } from 'react-icons/hi';

const RenewalPage: React.FC = () => {
  const router = useRouter();

  const handleBackToMain = () => {
    router.push('/ai-enroller');
  };

  return (
    <div className="renewal-wrapper">
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        padding: '2rem',
        fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif'
      }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <button
          onClick={handleBackToMain}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            background: 'white',
            border: '1px solid #e5e7eb',
            padding: '0.5rem 1rem',
            borderRadius: '0.5rem',
            color: '#374151',
            cursor: 'pointer',
            fontSize: '0.875rem',
            fontWeight: '500'
          }}
        >
          <HiOutlineArrowLeft size={16} />
          Back to Main
        </button>
      </div>

      {/* AI Assistant Message */}
      <div style={{
        display: 'flex',
        alignItems: 'flex-start',
        gap: '1rem',
        background: 'white',
        padding: '1.5rem',
        borderRadius: '1rem',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
        border: '1px solid #e2e8f0',
        marginBottom: '2rem'
      }}>
        <div style={{
          width: '2.5rem',
          height: '2.5rem',
          background: 'linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%)',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '1.25rem',
          flexShrink: 0
        }}>
          🤖
        </div>
        <div style={{
          flex: 1,
          fontSize: '0.875rem',
          color: '#374151',
          lineHeight: 1.6
        }}>
          Welcome to the Group Plan Management section! This feature is coming soon. You&apos;ll be able to assign plans to employer groups and manage renewals here.
        </div>
      </div>

      {/* Coming Soon Card */}
      <div style={{
        background: 'white',
        border: '1px solid #e2e8f0',
        borderRadius: '1rem',
        padding: '3rem 2rem',
        textAlign: 'center',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)'
      }}>
        <div style={{
          fontSize: '3rem',
          marginBottom: '1rem'
        }}>
          🚧
        </div>
        <h2 style={{
          fontSize: '1.5rem',
          fontWeight: '600',
          color: '#1e293b',
          marginBottom: '1rem'
        }}>
          Group Plan Management
        </h2>
        <p style={{
          fontSize: '1rem',
          color: '#64748b',
          marginBottom: '2rem',
          maxWidth: '400px',
          margin: '0 auto 2rem auto'
        }}>
          This section will allow you to assign insurance plans to employer groups and manage plan renewals.
        </p>

        <div style={{
          background: '#f8fafc',
          border: '1px solid #e2e8f0',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          marginBottom: '2rem'
        }}>
          <h3 style={{
            fontSize: '1rem',
            fontWeight: '600',
            color: '#1e293b',
            marginBottom: '1rem'
          }}>
            Coming Features:
          </h3>
          <ul style={{
            textAlign: 'left',
            color: '#374151',
            fontSize: '0.875rem',
            lineHeight: 1.6,
            margin: 0,
            paddingLeft: '1.5rem'
          }}>
            <li>Assign plans to employer groups</li>
            <li>Manage plan renewals and updates</li>
            <li>Track group enrollment status</li>
            <li>Generate renewal reports</li>
            <li>Automated renewal notifications</li>
          </ul>
        </div>

        <button
          onClick={handleBackToMain}
          style={{
            background: '#8b5cf6',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.5rem',
            fontSize: '0.875rem',
            fontWeight: '600',
            cursor: 'pointer'
          }}
        >
          Back to Main Menu
        </button>
      </div>
    </div>
    </div>
  );
};

export default RenewalPage;
