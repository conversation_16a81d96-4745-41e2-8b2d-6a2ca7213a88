
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import { PlanQADialog } from '@/components/PlanQADialog';
import { BotQuestion } from '@/components/BotQuestion';

export const ConfirmationPage = () => {
  return (
    <div className="max-w-3xl mx-auto space-y-6">
      {/* Page heading */}
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Enrollment Complete!
        </h1>
      </div>

      <BotQuestion 
        question="🎉 Congratulations! Your enrollment is complete!"
        context="You're all set for 2025! Here are some helpful next steps."
      />
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-6 h-6 text-green-500" />
            <h2 className="text-xl">What's Next</h2>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-lg">🎊 Your benefits start January 1, 2025</p>
          <p className="text-base">📌 You can change choices until December 15, 2024</p>
          
          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg space-y-3">
            <h3 className="font-medium">📱 Pro Tips:</h3>
            <ul className="text-sm space-y-2">
              <li>🦷 Find dentists in your member portal</li>
              <li>👓 Use vision benefit for free exam & $150 frames</li>
              <li>💰 Set up HSA/FSA to save on taxes</li>
              <li>📱 Download insurance apps for easy access</li>
              <li>🗓️ Schedule preventive care visits early</li>
            </ul>
          </div>
          
          <div className="flex gap-2 pt-4">
            <PlanQADialog selectedPlans={{}} />
            <Button variant="outline">📧 Email Summary</Button>
            <Button variant="outline">📅 Add to Calendar</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
