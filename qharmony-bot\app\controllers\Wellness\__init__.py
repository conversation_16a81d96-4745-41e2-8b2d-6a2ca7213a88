# Wellness/__init__.py

import os
import joblib
import pickle
from typing import Tuple, Dict, List, Any
import logging # Use logging instead of print for package initialization

# --- Configure Logging ---
# It's better practice for libraries/packages to configure logging minimally
# or let the application configure it. We'll use basic logging here.
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__) # Get logger for this module

# --- Default Model Paths ---
# Determine base path relative to this __init__.py file
_PACKAGE_DIR = os.path.dirname(os.path.abspath(__file__))
print(_PACKAGE_DIR)
_MODELS_BASE = os.path.join(_PACKAGE_DIR,"..","..", "models", "Wellness Models") # Go up one level from Wellness/

DEFAULT_LIFE_EXPECTANCY_BASE_DIR = os.path.normpath(os.path.join(_MODELS_BASE, "Life Expectancy Model"))
DEFAULT_HEART_DISEASE_BASE_DIR = os.path.normpath(os.path.join(_MODELS_BASE, "Heart Model"))
DEFAULT_STROKE_BASE_DIR = os.path.normpath(os.path.join(_MODELS_BASE, "Stroke Model"))

# --- Helper Loading Function (can be defined here or imported) ---
def _load_artifacts(base_dir: str, file_map: Dict[str, str], method: str = 'joblib') -> Dict[str, Any]:
    """ Helper function to load artifacts using specified method. """
    artifacts = {}
    log.info(f"Loading artifacts from: {base_dir} using {method}")
    for key, filename in file_map.items():
        file_path = os.path.join(base_dir, filename)
        if not os.path.exists(file_path):
            log.error(f"Required file not found: {file_path}")
            raise FileNotFoundError(f"Required file not found: {file_path}")
        try:
            if method == 'joblib':
                artifacts[key] = joblib.load(file_path)
            elif method == 'pickle':
                with open(file_path, "rb") as f:
                    artifacts[key] = pickle.load(f)
            else:
                raise ValueError(f"Unsupported loading method: {method}")
            log.debug(f"  Loaded: {filename}") # Use debug level for successful loads
        except Exception as e:
            log.exception(f"Error loading {file_path} using {method}: {e}") # Log exception info
            raise # Re-raise the exception after logging
    log.info(f"Finished loading artifacts from: {base_dir}")
    return artifacts

# --- Define File Maps ---
_life_expectancy_files = {
    "ridge_model": "ridge_model_expectancy.pkl", "life_exp_preprocessors": "fitted_preprocessors_expectancy.pkl",
    "poly_transform": "poly_transform_expectancy.pkl", "sigma": "sigma_expectancy.pkl",
    "X_train_poly": "X_train_poly_background_expectancy.pkl", "life_exp_columns": "training_columns_expectancy.pkl"
}
_heart_disease_files = {
    "heart_disease_model": "heart_disease_model_xgboost.pkl",
    "heart_disease_preprocessors": "heart_disease_model_fitted_preprocessors.pkl",
    "heart_disease_columns": "X_train_columns.pkl"
}
_stroke_files = {
    "stroke_model": "stroke_model_xgboost.pkl", "stroke_preprocessors": "stroke_model_fitted_preprocessors.pkl",
    "stroke_columns": "X_train_columns_stoke.pkl"
}

# --- Load Models on Import ---
# Use module-level variables (leading underscore suggests internal use)
try:
    log.info("Loading Wellness models...")
    # Load Life Expectancy
    _life_exp_artifacts = _load_artifacts(DEFAULT_LIFE_EXPECTANCY_BASE_DIR, _life_expectancy_files, method='pickle')
    ridge_model = _life_exp_artifacts["ridge_model"]
    life_exp_preprocessors = _life_exp_artifacts["life_exp_preprocessors"]
    poly = _life_exp_artifacts["poly_transform"]
    sigma = _life_exp_artifacts["sigma"]
    X_train_poly = _life_exp_artifacts["X_train_poly"]
    life_exp_columns = _life_exp_artifacts["life_exp_columns"]

    # Load Heart Disease
    _heart_disease_artifacts = _load_artifacts(DEFAULT_HEART_DISEASE_BASE_DIR, _heart_disease_files, method='joblib')
    heart_disease_model = _heart_disease_artifacts["heart_disease_model"]
    heart_disease_preprocessors = _heart_disease_artifacts["heart_disease_preprocessors"]
    heart_disease_columns = _heart_disease_artifacts["heart_disease_columns"]

    # Load Stroke Prediction
    _stroke_artifacts = _load_artifacts(DEFAULT_STROKE_BASE_DIR, _stroke_files, method='joblib')
    stroke_model = _stroke_artifacts["stroke_model"]
    stroke_preprocessors = _stroke_artifacts["stroke_preprocessors"]
    stroke_columns = _stroke_artifacts["stroke_columns"]

    log.info("Wellness models loaded successfully.")

    # Optionally expose specific items directly via __all__ for cleaner imports from the package
    __all__ = [
        'ridge_model', 'life_exp_preprocessors', 'poly_transform', 'sigma', 'X_train_poly', 'life_exp_columns',
        'heart_disease_model', 'heart_disease_preprocessors', 'heart_disease_columns',
        'stroke_model', 'stroke_preprocessors', 'stroke_columns'
    ]

except FileNotFoundError as e:
    log.critical(f"CRITICAL ERROR: Could not load models during package initialization. File missing: {e}")
    # Decide how to handle this: raise the error to prevent import, or set models to None?
    # Raising the error is often better as the package is unusable without models.
    raise RuntimeError(f"Failed to initialize Wellness package due to missing model file: {e}") from e
except Exception as e:
    log.critical(f"CRITICAL ERROR: An unexpected error occurred during Wellness package initialization: {e}")
    raise RuntimeError(f"Failed to initialize Wellness package: {e}") from e

# --- You can also directly import key classes/functions ---
# Make the WellnessPredictor class easily accessible via 'from Wellness import WellnessPredictor'
# Assuming WellnessPredictor is defined in Wellness/Wellness.py