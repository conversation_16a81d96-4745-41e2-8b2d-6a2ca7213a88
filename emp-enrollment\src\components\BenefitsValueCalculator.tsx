
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DollarSign, TrendingUp, Calculator } from 'lucide-react';

interface BenefitsValueCalculatorProps {
  familySize?: string;
}

export const BenefitsValueCalculator = ({ familySize = 'self' }: BenefitsValueCalculatorProps) => {
  const [showDetails, setShowDetails] = useState(false);

  const calculateValue = () => {
    const baseValue = {
      medical: { employee: 1200, employer: 4800 },
      dental: { employee: 150, employer: 150 },
      vision: { employee: 65, employer: 65 },
      wellness: { employee: 0, employer: 500 }
    };

    const multiplier = familySize === 'family' ? 2.5 : familySize === 'spouse' ? 1.8 : 1;
    
    return {
      employeeContribution: Math.round((baseValue.medical.employee + baseValue.dental.employee + baseValue.vision.employee) * multiplier),
      employerContribution: Math.round((baseValue.medical.employer + baseValue.dental.employer + baseValue.vision.employer + baseValue.wellness.employer) * multiplier),
      totalValue: Math.round((baseValue.medical.employee + baseValue.medical.employer + baseValue.dental.employee + baseValue.dental.employer + baseValue.vision.employee + baseValue.vision.employer + baseValue.wellness.employer) * multiplier)
    };
  };

  const values = calculateValue();
  const roiPercentage = Math.round(((values.employerContribution + values.totalValue - values.employeeContribution) / values.employeeContribution) * 100);

  return (
    <Card className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-950 dark:to-emerald-900 border-green-200 dark:border-green-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="w-5 h-5 text-green-600" />
          💰 Your Benefits Value Calculator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
            <p className="text-sm text-muted-foreground">You Pay Annually</p>
            <p className="text-2xl font-bold text-blue-600">${values.employeeContribution.toLocaleString()}</p>
          </div>
          <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
            <p className="text-sm text-muted-foreground">Employer Pays</p>
            <p className="text-2xl font-bold text-green-600">${values.employerContribution.toLocaleString()}</p>
          </div>
        </div>
        
        <div className="text-center p-4 bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900 dark:to-blue-900 rounded-lg">
          <p className="text-sm text-muted-foreground mb-1">Total Benefits Package Value</p>
          <p className="text-3xl font-bold text-green-700 dark:text-green-300">${values.totalValue.toLocaleString()}</p>
          <Badge className="mt-2 bg-green-600 text-white">
            <TrendingUp className="w-3 h-3 mr-1" />
            {roiPercentage}% ROI
          </Badge>
        </div>

        <Button 
          variant="outline" 
          onClick={() => setShowDetails(!showDetails)}
          className="w-full"
        >
          {showDetails ? 'Hide' : 'Show'} Value Breakdown
        </Button>

        {showDetails && (
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Medical Insurance Value:</span>
              <span className="font-medium">${(1200 * (familySize === 'family' ? 2.5 : familySize === 'spouse' ? 1.8 : 1) + 4800 * (familySize === 'family' ? 2.5 : familySize === 'spouse' ? 1.8 : 1)).toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>Dental + Vision Value:</span>
              <span className="font-medium">${(430 * (familySize === 'family' ? 2.5 : familySize === 'spouse' ? 1.8 : 1)).toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>Wellness Programs:</span>
              <span className="font-medium">${(500 * (familySize === 'family' ? 2.5 : familySize === 'spouse' ? 1.8 : 1)).toLocaleString()}</span>
            </div>
            <div className="pt-2 border-t flex justify-between font-bold">
              <span>Your Total ROI:</span>
              <span className="text-green-600">${(values.totalValue - values.employeeContribution).toLocaleString()}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
