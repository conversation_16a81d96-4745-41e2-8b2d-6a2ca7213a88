"use client";

import {
  <PERSON>,
  Drawer,
  <PERSON>ssB<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Button,
} from "@mui/material";
import Image from "next/image";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { useEffect, useState } from "react";
import { getCompanyBenefitTypes, maskBenefitCategory } from "@/middleware/company_middleware";
import {
  getUsersCompanyId,
  setSelectedBenefitType,
} from "@/redux/reducers/userSlice";
import HarmonyLogo from "../../public/logo.png";
import { usePathname, useRouter } from "next/navigation";
import assistant_image from "../../public/ai_assistant.png";
import { useSelector } from "react-redux";
import { getBenefitTypeIcon } from "./benefit_vector_map";
import SlackLogo from "../../public/vectors/slack_logo.png";
import Teams<PERSON>ogo from "../../public/vectors/teams_logo.png";
import { qHarmonyAdminEmail } from "@/APILayer/axios_helper";

const drawerWidth = 240;

const Sidebar = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();

  const isAdmin = useSelector(
    (state: RootState) => state.user.userProfile.isAdmin,
  );
  const isBroker = useSelector(
    (state: RootState) => state.user.userProfile.isBroker,
  );
  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));

  const companyLogo = useSelector(
    (state: RootState) => state.company?.companyDetails?.details?.logo,
  );

  const benefitTypes = useAppSelector(
    (state: RootState) => state.company.companyBenefitTypes,
  );
  const selectedBenefitType = useAppSelector(
    (state: RootState) => state.user.selectedBenefitType,
  );

  const [isTeamsApp, setIsTeamsApp] = useState(false);

  const userEmail = useSelector((state: RootState) => state.user.userProfile.email);

  useEffect(() => {
    setIsTeamsApp(localStorage.getItem("isTeamsApp1") === "true");
  }, []);

  useEffect(() => {
    if (companyId) {
      getCompanyBenefitTypes(dispatch, companyId);
    }
  }, [companyId, dispatch]);

  const handleBenefitTypeClick = (benefitType: string) => {
    dispatch(setSelectedBenefitType(benefitType));
    router.push(`/viewBenefitsByType/${benefitType}`);
  };

  const excludedPaths = [
    "/qHarmonyBot",
    "/proactive-messaging",
    "/notification-history",
    /^\/notifications-analytics\/[^/]+$/, // Regex to match dynamic route notifications-analytics/:id
  ]; // Add paths to exclude

  return (
    <Box sx={{ display: "flex", bgcolor: "#f6f8fc", minHeight: "100vh" }}>
      <CssBaseline />
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            boxSizing: "border-box",
            bgcolor: "#ffffff",
            position: "relative",
            display: "flex",
            flexDirection: "column",
          },
        }}
        variant="permanent"
        anchor="left"
      >
        <Box
          sx={{
            padding: 0,
            height: "100%",
            position: "relative",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {/* Logo and Title */}
          <Box
            sx={{
              height: "80px",
              borderBottom: "1px solid #D2D2D2",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => router.push("/dashboard")}
          >
            {companyLogo ? (
              <img
                src={companyLogo}
                alt="Company Logo"
                style={{ height: "50px", width: "auto" }}
              />
            ) : (
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Image
                  src={HarmonyLogo}
                  alt="BenOsphere Logo"
                  width={40}
                  height={40}
                />
                <Typography sx={{ fontWeight: 800, fontSize: "1.5rem", ml: 1 }}>
                  BenOsphere
                </Typography>
              </Box>
            )}
          </Box>

          {/* Benefits List (Scrollable) */}
          <Box sx={{ flexGrow: 1, overflow: "auto", paddingX: 2.5 }}>
            <Typography
              sx={{
                fontWeight: 860,
                fontSize: "14px",
                color: "rgba(0, 0, 0, 0.4)",
                marginBottom: "8px",
              }}
            >
              MY BENEFITS
            </Typography>

            <List>
              {benefitTypes.length > 0 ? (
                benefitTypes.map((benefitType) => (
                  <ListItem key={benefitType} disablePadding>
                    <ListItemButton
                      onClick={() => handleBenefitTypeClick(benefitType)}
                      sx={{
                        borderRadius: 2,
                        "&:hover": { backgroundColor: "#f0f0f0" },
                        bgcolor:
                          selectedBenefitType === benefitType
                            ? "#f0f4ff"
                            : "inherit",
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 0, mr: 2, color: "black" }}>
                        {getBenefitTypeIcon(benefitType)}
                      </ListItemIcon>
                      <ListItemText
                        primary={maskBenefitCategory(benefitType)}
                        sx={{ fontWeight: "medium", color: "#333" }}
                      />
                    </ListItemButton>
                  </ListItem>
                ))
              ) : (
                <Typography
                  variant="body1"
                  sx={{ color: "#999", padding: 2.5 }}
                >
                  No benefits available at the moment
                </Typography>
              )}
            </List>
          </Box>

          {/* AI Chat Section */}
          {!excludedPaths.some((path) =>
            typeof path === "string" ? pathname.includes(path) : path.test(pathname)
          ) && !isTeamsApp && (
              <Box
                sx={{
                  position: "fixed",
                  bottom: "80px", // Adjusted to make space for admin/broker buttons
                  right: "20px",
                  display: "flex",
                  alignItems: "center",
                  bgcolor: "#ffffff",
                  borderRadius: "30px",
                  padding: "10px 20px",
                  boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
                  cursor: "pointer",
                }}
                onClick={() => router.push("/qHarmonyBot")}
              >
                <Image
                  src={assistant_image}
                  alt="AI Chat"
                  style={{
                    borderRadius: "100%",
                    width: "40px",
                    height: "40px",
                    marginRight: "10px",
                  }}
                />
                <Box>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                    Chat with Brea
                  </Typography>
                  <Typography variant="body2" sx={{ color: "#6c757d" }}>
                    24/7 available
                  </Typography>
                </Box>
              </Box>
            )}

          {/* Admin and Broker Panel */}
          <Box
            sx={{
              position: "relative",
              marginTop: "auto",
              paddingBottom: "20px",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {isAdmin && (
              <>
                <Typography
                  sx={{
                    fontWeight: 700,
                    paddingY: 1,
                    fontSize: ".7rem",
                    color: "rgba(0, 0, 0, 0.4)",
                  }}
                >
                  ADMIN HUB
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => router.push("/manageBenefits/")}
                  sx={{
                    textTransform: "none",
                    borderRadius: "8px",
                    bgcolor: "rgba(0, 0, 0, 0.06)",
                    color: "black",
                    boxShadow: "none",
                    width: "90%",
                    paddingY: "10px",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
                      boxShadow: "none",
                    },
                  }}
                >
                  Upload Benefits
                </Button>
                <Button
                  variant="contained"
                  onClick={() => router.push("/manage-groups/")}
                  sx={{
                    textTransform: "none",
                    borderRadius: "8px",
                    bgcolor: "rgba(0, 0, 0, 0.06)",
                    color: "black",
                    boxShadow: "none",
                    width: "90%",
                    paddingY: "10px",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
                      boxShadow: "none",
                    },
                  }}
                >
                  Assign Benefits
                </Button>
                <Button
                  variant="contained"
                  onClick={() => router.push("/team-members/")}
                  sx={{
                    textTransform: "none",
                    borderRadius: "8px",
                    bgcolor: "rgba(0, 0, 0, 0.06)",
                    color: "black",
                    boxShadow: "none",
                    width: "90%",
                    paddingY: "10px",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
                      boxShadow: "none",
                    },
                  }}
                >
                  Manage Team
                </Button>
                {qHarmonyAdminEmail.includes(userEmail) && (
                  <Button
                    variant="contained"
                    onClick={() => router.push("/proactive-messaging/")}
                    sx={{
                      textTransform: "none",
                      borderRadius: "8px",
                      bgcolor: "rgba(0, 0, 0, 0.06)",
                      color: "black",
                      boxShadow: "none",
                      width: "90%",
                      paddingY: "10px",
                      "&:hover": {
                        backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
                        boxShadow: "none",
                      },
                    }}
                  >
                    Broadcast Center
                  </Button>
                )}
              </>
            )}

            {isBroker && (
              <>
                <Button
                  variant="contained"
                  onClick={() => router.push("/manage-companies/")}
                  sx={{
                    textTransform: "none",
                    borderRadius: "8px",
                    bgcolor: "rgba(0, 0, 0, 0.06)",
                    color: "black",
                    boxShadow: "none",
                    width: "90%",
                    paddingY: "10px",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
                      boxShadow: "none",
                    },
                  }}
                >
                  Add Employers
                </Button>
              </>
            )}

            {!isTeamsApp && (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 1.5,
                  alignItems: "center",
                  width: "90%", // Ensure the buttons take up the same width as admin buttons
                }}
              >
                <Button
                  variant="outlined"
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    width: "100%", // Set width to 100% to cover the entire width
                    borderRadius: "12px",
                    padding: "9px 16px",
                    border: "1px solid rgba(0, 0, 0, 0.12)",
                    color: "black",
                    fontSize: "16px",
                    fontWeight: 500,
                    textTransform: "none",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.04)",
                    },
                  }}
                >
                  <Image
                    src={SlackLogo}
                    alt="Teams Logo"
                    width={21}
                    height={21}
                    style={{ marginRight: "8px" }}
                  />
                  Add to Slack
                </Button>

                <Button
                  variant="outlined"
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    width: "100%", // Set width to 100% to cover the entire width
                    borderRadius: "12px",
                    padding: "9px 16px",
                    border: "1px solid rgba(0, 0, 0, 0.12)",
                    color: "black",
                    fontSize: "16px",
                    fontWeight: 500,
                    textTransform: "none",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.04)",
                    },
                  }}
                >
                  <Image
                    src={TeamsLogo}
                    alt="Teams Logo"
                    width={21}
                    height={21}
                    style={{ marginRight: "8px" }}
                  />
                  Add to Teams
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </Drawer>
    </Box>
  );
};

export default Sidebar;
