/**
 * A<PERSON>M. Best Financial Strength Ratings
 * 
 * A.M. <PERSON> is the leading credit rating agency for the insurance industry.
 * These ratings assess an insurance company's financial strength and ability to meet policyholder obligations.
 * 
 * Source: A.M. Best Company
 * Last Updated: 2024
 */

// A.M. Best Rating Categories
export const AM_BEST_RATING_CATEGORIES = {
  SUPERIOR: 'Superior',
  EXCELLENT: 'Excellent', 
  GOOD: 'Good',
  FAIR: 'Fair',
  MARGINAL: 'Marginal',
  WEAK: 'Weak',
  POOR: 'Poor',
  UNDER_SUPERVISION: 'Under Regulatory Supervision',
  IN_LIQUIDATION: 'In Liquidation',
  NOT_RATED: 'Not Rated'
} as const;

// Individual A.M. Best Ratings with descriptions
export const AM_BEST_RATINGS = {
  // Superior (Highest)
  'A++': {
    category: AM_BEST_RATING_CATEGORIES.SUPERIOR,
    description: 'Superior - Highest level of financial security',
    riskLevel: 'Very Low',
    numericScore: 15
  },
  'A+': {
    category: AM_BEST_RATING_CATEGORIES.SUPERIOR,
    description: 'Superior - Very high level of financial security',
    riskLevel: 'Very Low',
    numericScore: 14
  },

  // Excellent
  'A': {
    category: AM_BEST_RATING_CATEGORIES.EXCELLENT,
    description: 'Excellent - High level of financial security',
    riskLevel: 'Low',
    numericScore: 13
  },
  'A-': {
    category: AM_BEST_RATING_CATEGORIES.EXCELLENT,
    description: 'Excellent - Good level of financial security',
    riskLevel: 'Low',
    numericScore: 12
  },

  // Good
  'B++': {
    category: AM_BEST_RATING_CATEGORIES.GOOD,
    description: 'Good - Adequate financial security',
    riskLevel: 'Moderate',
    numericScore: 11
  },
  'B+': {
    category: AM_BEST_RATING_CATEGORIES.GOOD,
    description: 'Good - Adequate financial security with some areas of concern',
    riskLevel: 'Moderate',
    numericScore: 10
  },

  // Fair
  'B': {
    category: AM_BEST_RATING_CATEGORIES.FAIR,
    description: 'Fair - Adequate financial security but vulnerable to adverse changes',
    riskLevel: 'Moderate High',
    numericScore: 9
  },
  'B-': {
    category: AM_BEST_RATING_CATEGORIES.FAIR,
    description: 'Fair - Adequate financial security but more vulnerable to adverse changes',
    riskLevel: 'Moderate High',
    numericScore: 8
  },

  // Marginal
  'C++': {
    category: AM_BEST_RATING_CATEGORIES.MARGINAL,
    description: 'Marginal - Adequate financial security but significant vulnerabilities',
    riskLevel: 'High',
    numericScore: 7
  },
  'C+': {
    category: AM_BEST_RATING_CATEGORIES.MARGINAL,
    description: 'Marginal - Adequate financial security but major vulnerabilities',
    riskLevel: 'High',
    numericScore: 6
  },

  // Weak
  'C': {
    category: AM_BEST_RATING_CATEGORIES.WEAK,
    description: 'Weak - Inadequate financial security with significant risk',
    riskLevel: 'Very High',
    numericScore: 5
  },
  'C-': {
    category: AM_BEST_RATING_CATEGORIES.WEAK,
    description: 'Weak - Inadequate financial security with major risk',
    riskLevel: 'Very High',
    numericScore: 4
  },

  // Poor
  'D': {
    category: AM_BEST_RATING_CATEGORIES.POOR,
    description: 'Poor - Very inadequate financial security',
    riskLevel: 'Extreme',
    numericScore: 3
  },

  // Under Regulatory Supervision
  'E': {
    category: AM_BEST_RATING_CATEGORIES.UNDER_SUPERVISION,
    description: 'Under Regulatory Supervision - Regulatory action initiated',
    riskLevel: 'Extreme',
    numericScore: 2
  },

  // In Liquidation
  'F': {
    category: AM_BEST_RATING_CATEGORIES.IN_LIQUIDATION,
    description: 'In Liquidation - Company is being liquidated',
    riskLevel: 'Total Loss',
    numericScore: 1
  },

  // Not Rated
  'NR': {
    category: AM_BEST_RATING_CATEGORIES.NOT_RATED,
    description: 'Not Rated - Insufficient data for rating assignment',
    riskLevel: 'Unknown',
    numericScore: 0
  }
} as const;

// Array of all valid rating codes for validation
export const AM_BEST_RATING_CODES = Object.keys(AM_BEST_RATINGS) as Array<keyof typeof AM_BEST_RATINGS>;

// Minimum recommended ratings for different use cases
export const AM_BEST_MINIMUM_RATINGS = {
  CONSERVATIVE: ['A++', 'A+', 'A', 'A-'],           // Conservative clients
  STANDARD: ['A++', 'A+', 'A', 'A-', 'B++', 'B+'], // Standard risk tolerance
  AGGRESSIVE: ['A++', 'A+', 'A', 'A-', 'B++', 'B+', 'B', 'B-'] // Higher risk tolerance
} as const;

// Rating categories for filtering
export const AM_BEST_RATING_FILTERS = {
  INVESTMENT_GRADE: ['A++', 'A+', 'A', 'A-', 'B++', 'B+'], // Investment grade ratings
  SECURE: ['A++', 'A+', 'A', 'A-'],                        // Secure ratings only
  ACCEPTABLE: ['A++', 'A+', 'A', 'A-', 'B++', 'B+', 'B'], // Generally acceptable
  ALL_RATED: AM_BEST_RATING_CODES.filter(code => code !== 'NR') // All except Not Rated
} as const;

// Helper functions
export const getAmBestRatingInfo = (rating: string) => {
  return AM_BEST_RATINGS[rating as keyof typeof AM_BEST_RATINGS] || null;
};

export const isValidAmBestRating = (rating: string): boolean => {
  return AM_BEST_RATING_CODES.includes(rating as keyof typeof AM_BEST_RATINGS);
};

export const getAmBestRatingsByCategory = (category: string) => {
  return AM_BEST_RATING_CODES.filter(code => 
    AM_BEST_RATINGS[code].category === category
  );
};

export const compareAmBestRatings = (rating1: string, rating2: string): number => {
  const info1 = getAmBestRatingInfo(rating1);
  const info2 = getAmBestRatingInfo(rating2);
  
  if (!info1 || !info2) return 0;
  
  return info2.numericScore - info1.numericScore; // Higher score = better rating
};

// Type definitions
export type AmBestRatingCode = keyof typeof AM_BEST_RATINGS;
export type AmBestRatingCategory = typeof AM_BEST_RATING_CATEGORIES[keyof typeof AM_BEST_RATING_CATEGORIES];
export type AmBestRatingInfo = typeof AM_BEST_RATINGS[keyof typeof AM_BEST_RATINGS];
