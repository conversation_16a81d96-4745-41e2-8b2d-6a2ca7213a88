# QHarmony Authentication Flow Documentation

This document provides a comprehensive overview of the authentication flow in the QHarmony application, detailing the interaction between frontend and backend components, API routes, and handling of various authentication scenarios.

## Table of Contents

1. [Overview](#overview)
2. [Authentication Components](#authentication-components)
3. [Authentication Flows](#authentication-flows)
   - [Magic Link Authentication](#magic-link-authentication)
   - [Automatic Login](#automatic-login)
   - [API Request Authentication](#api-request-authentication)
4. [Edge Cases and Error Handling](#edge-cases-and-error-handling)
5. [Security Considerations](#security-considerations)
6. [Middleware Usage](#middleware-usage)

## Overview

QHarmony uses a passwordless authentication system based on Firebase Authentication with email links (magic links). The system combines:

- **Firebase Authentication**: For initial user authentication via email links
- **User ID Header**: For authenticating API requests
- **Local Storage**: For persisting authentication state
- **Specialized Middleware**: For role-based access control

## Authentication Components

### Frontend Components

1. **AuthContext** (`src/components/AuthContext.tsx`)
   - Manages authentication state
   - Listens for Firebase auth state changes
   - Provides user information to the application

2. **ProtectedRoute** (`src/components/ProtectedRoute.tsx`)
   - Wraps protected pages
   - Redirects unauthenticated users
   - Fetches user and company details

3. **Axios Helper** (`src/APILayer/axios_helper.ts`)
   - Adds `user-id` header to all requests
   - Makes API calls to the backend

4. **User Middleware** (`src/middleware/user_middleware.ts`)
   - Handles user-related API calls
   - Manages magic link authentication

### Backend Components

1. **Auth Service** (`src/services/auth.service.ts`)
   - Generates magic links
   - Sends emails
   - Handles token verification

2. **Auth Middleware** (`src/middleware/auth.middleware.ts`)
   - Verifies JWT tokens in cookies
   - Fetches user and company data
   - Attaches data to the request

3. **Specialized Middleware**
   - `adminAuthMiddleware`: Verifies admin privileges
   - `isEmployeeMiddleware`: Verifies employee role
   - `isEmployerMiddleware`: Verifies employer role

4. **Controllers**
   - `AuthController`: Handles magic link generation
   - `EmployeeController`: Handles employee onboarding
   - `AdminController`: Handles admin operations

## Authentication Flows

The QHarmony application has two main authentication flows:

1. **Employee Authentication Flow**: For existing employees to log in
2. **Admin Onboarding Flow**: For new company admins to create an account and company

### 1. Initial Authentication Process

#### User Enters Email on Homepage

**Frontend**:
- User enters email on homepage (`src/app/page.tsx`)
- Frontend calls `selfOnboard(email)` from `user_middleware.ts`
- Makes POST request to `/user/self-onboard`

**Backend** (`EmployeeController.selfOnboardEmployee`):
- Checks if user exists in the database
- **If user exists and not disabled**:
  - Sends login magic link via `authService.sendLoginMagicLink()`
  - Returns `{ data: 'magic_link_sent' }`
- **If user exists but disabled**:
  - Returns `{ data: 'ask_admin_to_add' }`
- **If user doesn't exist**:
  - Checks if email domain matches an existing company
  - **If domain matches**:
    - Returns `{ data: 'ask_admin_to_add' }`
  - **If domain doesn't match**:
    - Creates empty company and admin user objects
    - Sends admin onboarding magic link via `authService.sendOnboardingMagicLink()`
    - Returns `{ data: 'magic_link_sent' }`

**API Route**: `POST /user/self-onboard`
```typescript
// Frontend request
const response = await postRequest("/user/self-onboard", {
  userEmail: email,
});

// Backend handler (simplified)
private selfOnboardEmployee = async (request, response) => {
  let { userEmail } = request.body;
  userEmail = userEmail.toLowerCase();

  // Check if user exists
  const user = await UserModelClass.getDataByEmail({ email: userEmail });

  if (user) {
    // User exists logic
    if (user.isDisabled) {
      return response.status(200).json({ data: 'ask_admin_to_add' });
    }

    // Send user login magic link
    await this.authService.sendLoginMagicLink(userEmail, {
      email: userEmail,
      companyId: user.companyId,
      userId: user._id,
    });
    return response.status(200).json({ data: 'magic_link_sent' });
  } else {
    // Check if domain matches existing company
    const userDomain = userEmail.split('@')[1];
    const companies = await CompanyModelClass.getAllData();
    const company = companies.find(
      (company) => company.adminEmail.split('@')[1] === userDomain
    );

    if (company) {
      // Domain matches existing company
      return response.status(200).json({
        data: 'ask_admin_to_add',
      });
    } else {
      // New domain - send admin onboarding link
      const companyDetails = {
        name: '',
        adminEmail: userEmail,
        // other empty fields...
      };
      const userDetails = {
        name: '',
        email: userEmail,
        isAdmin: true,
        // other fields...
      };

      await this.authService.sendOnboardingMagicLink(
        userEmail,
        companyDetails,
        userDetails,
        { email: userEmail, isAdmin: true }
      );
      return response.status(200).json({ data: 'magic_link_sent' });
    }
  }
};
```

### 2. Magic Link Processing

When a user clicks a magic link in their email, the system handles it differently based on whether it's a regular login link or an admin onboarding link.

#### 2.1 Regular Employee Login Flow

**Frontend**:
- User clicks link in email, redirected to `/login`
- `login/page.tsx` extracts parameters from URL
- Calls `parseParamsFromUrl(url)` which makes POST request to `/auth/parse-params`
- Completes Firebase authentication with `signInWithEmailLink`
- Stores user ID and company ID in localStorage
- Calls `onboardEmployee(companyId, userId)`
- Redirects to dashboard

**Backend**:
- `AuthController.parseParams` decrypts the parameters
- Returns decrypted data to frontend
- `EmployeeController.onboardEmployee` activates the user
- Updates user record with `isActivated: true`

**API Routes**:
1. `POST /auth/parse-params`
```typescript
// Frontend request
const params = await parseParamsFromUrl(url);

// Backend handler
private parseParams = async (request, response) => {
  try {
    const { link } = request.body;
    const decryptedParams = this.authService.parseAdditionalParams(link);
    response.status(200).json(decryptedParams);
  } catch (error) {
    logger.error('Error parsing params.', error);
    response.status(500).json({ error: 'Internal server error' });
  }
};
```

2. `POST /employee/onboard`
```typescript
// Frontend request
onboardEmployee(companyId as string, userId as string);

// Backend handler
private onboardEmployee = async (request, response) => {
  try {
    const { companyId, userId } = request.body;

    const user = await UserModelClass.getDataById(userId);
    if (!user) {
      return response.status(400).json({ error: 'User not found' });
    }

    if (user.companyId !== companyId) {
      return response.status(400).json({ error: 'User does not belong to the company' });
    }

    await UserModelClass.updateData(userId, { isActivated: true });
    response.status(200).json({ isSuccess: true });
  } catch (error) {
    logger.error(error);
    response.status(500).json({ error: 'Internal server error' });
  }
};
```

#### 2.2 Admin Onboarding Flow

**Frontend**:
- User clicks link in email, redirected to `/onboard`
- `onboard/page.tsx` extracts parameters from URL
- Calls `parseParamsFromUrl(url)` which makes POST request to `/auth/parse-params`
- Completes Firebase authentication with `signInWithEmailLink`
- Displays form to collect company and admin details
- When form is submitted, calls `onboardAdmin(companyDetails, userDetails)`
- Redirects to dashboard after successful onboarding

**Backend**:
- `AuthController.parseParams` decrypts the parameters
- Returns decrypted data including empty company and user templates
- `AdminController.onboardAdmin` creates the company and admin user
- Creates benefit templates for the new company

**API Routes**:
1. `POST /auth/parse-params` (same as above)

2. `POST /admin/onboard`
```typescript
// Frontend request
const response = await onboardAdmin(dispatch, companyDetails, userDetails);

// Backend handler
private onboardAdmin = async (request, response) => {
  try {
    const { company, user } = request.body;

    // Validate admin email matches
    if (company.adminEmail !== user.email) {
      return response.status(400).json({
        error: 'Admin email in company and current user email do not match',
      });
    }

    // Check if company already exists
    const existingCompany = await CompanyModelClass.getDataByAdminEmail({
      adminEmail: user.email,
    });
    if (existingCompany) {
      return response.status(400).json({ error: 'Company already exists' });
    }

    // Save company in database
    const companyEntity = await CompanyModelClass.addData(company);

    // Save admin user in database
    const userEntity = await UserModelClass.addData({
      ...user,
      companyId: companyEntity._id.toString(),
      isActivated: true,
      isDisabled: false,
    });

    // Create benefit templates
    // ...

    return response.status(200).json({
      companyId: companyEntity._id,
      userId: userEntity._id
    });
  } catch (error) {
    logger.error('Error onboarding company and user:', error);
    return response.status(500).json({ error: 'Internal server error' });
  }
};
```

### 3. Automatic Login

When a user returns to app.benosphere.com, the application automatically logs them in without requiring them to enter their email or click a magic link again. This process works through a multi-layered authentication approach:

#### 3.1 Firebase Authentication Persistence

**Frontend** (`src/components/AuthContext.tsx`):
- `AuthContext` initializes and calls Firebase's `onAuthStateChanged`
- If Firebase session exists, user is automatically authenticated
- This happens silently in the background when the app loads

```typescript
useEffect(() => {
  const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
    if (firebaseUser) {
      console.log("Firebase user exists:", firebaseUser);
      setUser(firebaseUser);
    } else {
      // Check for other auth methods...
    }
  });
  return () => unsubscribe();
}, []);
```

#### 3.2 Local Storage Persistence

**Frontend** (`src/components/ProtectedRoute.tsx`):
- Even if Firebase session expires, the app has a fallback mechanism
- Checks localStorage for `userid1` and `companyId1` values
- If found, uses these to fetch user and company details
- This provides a seamless experience even if Firebase tokens expire

```typescript
useEffect(() => {
  const userId = localStorage.getItem("userid1");
  if (userId) {
    dispatch(setUserId(userId));
    getUserDetails(dispatch, userId);
    getCompanyDetails(dispatch);
  }
}, [dispatch]);
```

#### 3.3 User Data Retrieval

**Frontend** (`src/middleware/user_middleware.ts` and `company_middleware.ts`):
- Makes GET requests to `/employee` and `/employee/company-details`
- Uses user ID in request header via axios interceptor
- Updates Redux store with user and company data

**Backend**:
- `EmployeeController.employeeDetails` and `getCompanyDetails` handle requests
- Verify user ID from header
- Return user and company data

**API Routes**:
1. `GET /employee`
```typescript
// Frontend
export async function getUserDetails(dispatch: AppDispatch, userId: string) {
  const response = await getRequest(`/employee`);
  dispatch(setUserDetails({
    id: response.currentUser._id,
    name: response.currentUser.name,
    email: response.currentUser.email,
    // other fields...
  }));
  return response;
}

// Backend
private employeeDetails = async (request, response) => {
  try {
    const userId = request.headers['user-id'] as string;
    const currentUser = await UserModelClass.getDataById(userId);

    if (!currentUser) {
      return response.status(404).json({ error: 'User not found' });
    }

    // Update login timestamp when user details are fetched
    await UserModelClass.updateLoginTimestamp(userId);

    response.status(200).json({ currentUser });
  } catch (error) {
    logger.error('Error fetching employee details:', error);
    response.status(500).json({ error: 'Internal server error' });
  }
};
```

2. `GET /employee/company-details`
```typescript
// Frontend
export async function getCompanyDetails(dispatch: AppDispatch) {
  const response = await getRequest(`/employee/company-details`);
  dispatch(setCompanyDetails(response.company));
  return response.status;
}

// Backend
private getCompanyDetails = async (request, response) => {
  try {
    const userId = request.headers['user-id'] as string;
    const user = await UserModelClass.getDataById(userId);

    if (!user) {
      return response.status(404).json({ error: 'User not found' });
    }

    const company = await CompanyModelClass.getDataById(user.companyId);
    if (!company) {
      return response.status(404).json({ error: 'Company not found' });
    }

    response.status(200).json({ company });
  } catch (error) {
    logger.error('Error fetching company details:', error);
    response.status(500).json({ error: 'Internal server error' });
  }
};
```

### 4. API Request Authentication

After the initial authentication, all subsequent API requests use a header-based authentication approach rather than traditional JWT tokens in cookies.

#### 4.1 Header-Based Authentication

**Frontend** (`src/APILayer/axios_helper.ts`):
- Axios interceptor automatically adds `user-id` header to all requests
- This header contains the user ID stored in localStorage
- This approach is used for all API calls after initial authentication

```typescript
// In axios_helper.ts
axiosInstance.interceptors.request.use((config) => {
  const userId = localStorage.getItem("userid1");
  if (userId) {
    config.headers["user-id"] = userId;
  }
  return config;
});
```

**Backend**:
- Controllers extract user ID from header
- Use the ID to fetch user data and verify permissions
- This approach is simpler than JWT verification but relies on secure user IDs

```typescript
// Example from a controller method
const userId = request.headers['user-id'] as string;
const user = await UserModelClass.getDataById(userId);

if (!user) {
  return response.status(404).json({ error: 'User not found' });
}

// Process the request...
```

#### 4.2 Role-Based Authorization

The system uses specialized middleware for role-based access control:

1. **Admin Authentication** (`adminAuthMiddleware`):
   - Verifies the user is an admin for the specified company
   - Used for admin-only operations like adding employees

```typescript
// Example from adminAuthMiddleware
const userId = req.headers['user-id'] as string;
const { companyId } = req.body;

const company = await CompanyModelClass.getDataById(companyId);
const user = await UserModelClass.getDataById(userId);

if (!company || !user) {
  return res.status(404).json({ error: 'Company or user not found' });
}

if (company.adminEmail !== user.email) {
  return res.status(401).json({
    error: 'User is not authorized to perform this action for this company',
  });
}

next();
```

2. **Employee Authentication** (`isEmployeeMiddleware`):
   - Verifies the user has the 'employee' role
   - Used for employee-specific operations

3. **Employer Authentication** (`isEmployerMiddleware`):
   - Verifies the user has the 'employer' role
   - Used for employer-specific operations

#### 4.3 Unused JWT Authentication

The codebase includes a traditional JWT-based authentication middleware (`auth.middleware.ts`), but it appears to be largely unused in the current implementation:

```typescript
export async function auth(req, res, next) {
  try {
    const cookies = req.cookies;
    if (!cookies?.Authorization) {
      next(new HttpException(401, 'You are not authorized'));
      return;
    }

    const secret = process.env.QHARMONY_SECRET as string;
    const verificationResponse = jwt.verify(
      cookies.Authorization,
      secret
    ) as DataStoredInToken;
    // ...
  } catch (error) {
    // ...
  }
}
```

This middleware is designed to verify JWT tokens in cookies, but the application primarily relies on the `user-id` header mechanism instead.

## Edge Cases and Error Handling

### 1. Invalid or Expired Magic Link

**Frontend**:
- Displays error message
- Provides option to request new link

**Backend**:
- Returns appropriate error status

### 2. User Not Found

**Frontend**:
- Redirects to homepage
- Shows appropriate message

**Backend**:
- Returns 404 status

### 3. Disabled User

**Frontend**:
- Shows "ask admin to add" message

**Backend**:
- Returns specific status code and message

## Security Considerations

1. **Encryption**: Parameters in magic links are encrypted using AES
2. **Firebase Auth**: Handles secure token generation and validation
3. **User Verification**: Backend verifies user exists and belongs to company
4. **Role-Based Access**: Specialized middleware enforces role-based permissions

## Middleware Usage

The application uses several middleware types for authentication and authorization:

### 1. Auth Middleware

Designed to verify JWT tokens in cookies, but appears to be largely unused in the current implementation:

```typescript
export async function auth(req, res, next) {
  try {
    const cookies = req.cookies;
    if (!cookies?.Authorization) {
      next(new HttpException(401, 'You are not authorized'));
      return;
    }

    const secret = process.env.QHARMONY_SECRET as string;
    const verificationResponse = jwt.verify(
      cookies.Authorization,
      secret
    ) as DataStoredInToken;
    const email = verificationResponse.email.toLowerCase();

    // Verify that email exists in the database
    const userData = await UserModelClass.getDataByEmail({ email });
    if (!userData) {
      res.clearCookie('Authorization');
      next(new HttpException(401, 'You are not authorized'));
      return;
    }

    // Fetch the company data
    const companyData = await CompanyModelClass.getDataById(userData.companyId);
    if (!companyData) {
      res.clearCookie('Authorization');
      next(new HttpException(401, 'You are not authorized'));
      return;
    }

    req.body.user = userData;
    req.body.company = companyData;
  } catch (error) {
    res.clearCookie('Authorization');
    next(new HttpException(401, 'You are not authorized'));
    return;
  }
  next();
}
```

### 2. Admin Auth Middleware

Verifies the user is an admin for the specified company:

```typescript
const adminAuthMiddleware = async (req, res, next) => {
  try {
    const userId = req.headers['user-id'] as string;
    const { companyId } = req.body as { companyId: string };

    if (!companyId) {
      res.status(400).json({ error: 'Company ID is required' });
      return;
    }

    const company = await CompanyModelClass.getDataById(companyId);
    const user = await UserModelClass.getDataById(userId);

    if (!company) {
      res.status(404).json({ error: 'Company not found' });
      return;
    }
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    if (company.adminEmail !== user.email) {
      res.status(401).json({
        error: 'User is not authorized to perform this action for this company',
      });
      return;
    }

    next();
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
    return;
  }
};
```

### 3. Role-Based Middleware

Checks if the user has the appropriate role:

```typescript
// isEmployeeMiddleware
export async function isEmployee(req, _res, next) {
  try {
    // Check if the user is an employee
    if (req.body.user.role !== 'employee') {
      next(new HttpException(401, 'You are not authorized'));
      return;
    }
  } catch (error) {
    next(new HttpException(401, 'You are not authorized'));
    return;
  }
  next();
}

// isEmployerMiddleware
export async function isEmployer(req, _res, next) {
  try {
    // Check if the user is an employer
    if (req.body.user.role !== 'employer') {
      next(new HttpException(401, 'You are not authorized'));
      return;
    }
  } catch (error) {
    next(new HttpException(401, 'You are not authorized'));
    return;
  }
  next();
}
```

These middleware functions are used to protect routes based on user roles.

## Authentication Flow Diagrams

### 1. Magic Link Authentication Flow

```
┌─────────────────────┐                                 ┌─────────────────────┐
│     FRONTEND        │                                 │      BACKEND        │
└──────────┬──────────┘                                 └──────────┬──────────┘
           │                                                       │
           │  1. User enters email on homepage                     │
           │  ────────────────────────────────                     │
           │                                                       │
           │  POST /user/self-onboard                              │
           ├───────────────────────────────────────────────────────►
           │                                                       │
           │                                                       │  2. Check if user exists
           │                                                       │  ─────────────────────
           │                                                       │
           │                                                       │  3. Generate magic link
           │                                                       │  ─────────────────────
           │                                                       │
           │                                                       │  4. Send email with link
           │                                                       │  ──────────────────────
           │                                                       │
           │  Response: { data: "magic_link_sent" }                │
           │◄───────────────────────────────────────────────────────┤
           │                                                       │
           │  5. User clicks link in email                         │
           │  ───────────────────────────                          │
           │                                                       │
           │  6. Redirected to /login                              │
           │  ────────────────────────                             │
           │                                                       │
           │  POST /auth/parse-params                              │
           ├───────────────────────────────────────────────────────►
           │                                                       │
           │                                                       │  7. Decrypt parameters
           │                                                       │  ───────────────────
           │                                                       │
           │  Response: Decrypted parameters                       │
           │◄───────────────────────────────────────────────────────┤
           │                                                       │
           │  8. Complete Firebase auth                            │
           │  ─────────────────────────                            │
           │                                                       │
           │  9. Store userId & companyId                          │
           │  ───────────────────────────                          │
           │                                                       │
           │  POST /employee/onboard                               │
           ├───────────────────────────────────────────────────────►
           │                                                       │
           │                                                       │  10. Activate user
           │                                                       │  ──────────────────
           │                                                       │
           │  Response: { isSuccess: true }                        │
           │◄───────────────────────────────────────────────────────┤
           │                                                       │
           │  11. Redirect to dashboard                            │
           │  ───────────────────────────                          │
           │                                                       │
           ▼                                                       ▼
```

### 2. Automatic Login Flow

```
┌─────────────────────┐                                 ┌─────────────────────┐
│     FRONTEND        │                                 │      BACKEND        │
└──────────┬──────────┘                                 └──────────┬──────────┘
           │                                                       │
           │  1. User visits app.benosphere.com                    │
           │  ─────────────────────────────────                    │
           │                                                       │
           │  2. Check Firebase auth state                         │
           │  ───────────────────────────                          │
           │                                                       │
           │  3. If authenticated, set user state                  │
           │  ─────────────────────────────────                    │
           │                                                       │
           │  4. If not, check localStorage                        │
           │  ────────────────────────────                         │
           │                                                       │
           │  GET /employee                                        │
           ├───────────────────────────────────────────────────────►
           │  Header: { user-id: "userId" }                        │
           │                                                       │
           │                                                       │  5. Fetch user data
           │                                                       │  ────────────────
           │                                                       │
           │  Response: { currentUser: {...} }                     │
           │◄───────────────────────────────────────────────────────┤
           │                                                       │
           │  GET /employee/company-details                        │
           ├───────────────────────────────────────────────────────►
           │  Header: { user-id: "userId" }                        │
           │                                                       │
           │                                                       │  6. Fetch company data
           │                                                       │  ──────────────────
           │                                                       │
           │  Response: { company: {...} }                         │
           │◄───────────────────────────────────────────────────────┤
           │                                                       │
           │  7. Update Redux store                                │
           │  ────────────────────                                 │
           │                                                       │
           │  8. Render protected route                            │
           │  ──────────────────────                               │
           │                                                       │
           ▼                                                       ▼
```

### 3. API Request Authentication Flow

```
┌─────────────────────┐                                 ┌─────────────────────┐
│     FRONTEND        │                                 │      BACKEND        │
└──────────┬──────────┘                                 └──────────┬──────────┘
           │                                                       │
           │  1. User performs action                              │
           │  ─────────────────────                                │
           │                                                       │
           │  2. Axios interceptor adds user-id header             │
           │  ──────────────────────────────────────               │
           │                                                       │
           │  API Request                                          │
           ├───────────────────────────────────────────────────────►
           │  Header: { user-id: "userId" }                        │
           │                                                       │
           │                                                       │  3. Extract user-id
           │                                                       │  ─────────────────
           │                                                       │
           │                                                       │  4. Apply middleware
           │                                                       │  ─────────────────
           │                                                       │  - adminAuthMiddleware
           │                                                       │  - isEmployeeMiddleware
           │                                                       │  - isEmployerMiddleware
           │                                                       │
           │                                                       │  5. Process request
           │                                                       │  ────────────────
           │                                                       │
           │  Response                                             │
           │◄───────────────────────────────────────────────────────┤
           │                                                       │
           │  6. Update UI based on response                       │
           │  ─────────────────────────────                        │
           │                                                       │
           ▼                                                       ▼
```

## Broker-Employer-Employee Relationship

QHarmony implements a hierarchical relationship between brokers, employers, and employees. This section explains how these relationships are structured and managed in the system.

### 1. Data Model Relationships

#### 1.1 User Model Flags

The `UserDataInterface` in `user.model.ts` includes flags that identify user roles:

```typescript
export interface UserDataInterface {
  _id?: mongoose.Types.ObjectId;
  name: string;
  email: string;
  role: string;
  companyId: string;
  isAdmin: boolean;
  isBroker: boolean;  // Flag to identify broker users
  isActivated: boolean;
  isDisabled: boolean;
  // ...other fields
}
```

- `isBroker: true` - Identifies a user as a broker
- `isAdmin: true` - Identifies a user as an admin of their company
- Both flags can be true for broker admins

#### 1.2 Company Model Relationships

The `CompanyDataInterface` in `company.model.ts` includes fields that establish broker relationships:

```typescript
export interface CompanyDataInterface {
  _id?: mongoose.Types.ObjectId;
  name: string;
  // ...other fields
  brokerId: string;      // ID of the broker user who added this company
  brokerageId: string;   // ID of the brokerage company
  isBrokerage: boolean;  // Flag to identify brokerage companies
  isActivated: boolean;  // Flag to identify if company is activated
  // ...more fields
}
```

### 2. Hierarchical Structure

The QHarmony system implements a three-tier hierarchical structure:

```
┌─────────────────┐
│     BROKER      │
│  (isBroker=true)│
└────────┬────────┘
         │
         │ Manages
         ▼
┌─────────────────┐     ┌─────────────────┐
│    EMPLOYER 1   │     │    EMPLOYER 2   │
│(isBrokerage=false)    │(isBrokerage=false)
└────────┬────────┘     └────────┬────────┘
         │                       │
         │ Manages               │ Manages
         ▼                       ▼
┌─────────────────┐     ┌─────────────────┐
│   EMPLOYEES     │     │   EMPLOYEES     │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

#### 2.1 Broker Level

- A broker is a user with `isBroker: true`
- Belongs to a brokerage company (`isBrokerage: true`)
- The broker's `companyId` points to their brokerage company
- Can add and manage multiple employer companies

#### 2.2 Employer Level

- An employer company has `isBrokerage: false`
- Has `brokerId` set to the ID of the broker who added them
- Has `brokerageId` set to the ID of the broker's brokerage company
- Has an admin user who manages the company

#### 2.3 Employee Level

- Employees belong to employer companies
- Their `companyId` points to their employer company
- They don't have direct relationships with brokers

### 3. How Broker Relationships Are Established

When a broker adds an employer company, the system:

1. Validates that the user is a broker:
   ```typescript
   if (!broker || broker.isBroker === false) {
     response.status(400).json({ error: 'Invalid broker' });
     return;
   }
   ```

2. Validates that the broker belongs to a brokerage:
   ```typescript
   const brokerageEntity = await CompanyModelClass.getDataById(broker.companyId);
   if (!brokerageEntity || brokerageEntity.isBrokerage !== true) {
     response.status(400).json({ error: 'Invalid brokerage' });
     return;
   }
   ```

3. Creates the employer company with broker relationship:
   ```typescript
   const companyDetails = {
     // ...other fields
     brokerId: brokerId,                // The broker's user ID
     brokerageId: broker.companyId,     // The broker's company ID
     isBrokerage: false,                // This is an employer, not a brokerage
     isActivated: true,                 // Employer is immediately activated
   };
   ```

### 4. Retrieving Companies for a Broker

The `getAdminCompanies` endpoint allows brokers to retrieve all employer companies they've added:

```typescript
private getAdminCompanies = async (request, response) => {
  try {
    const adminId = request.headers['user-id'] as string;

    // Check if admin is valid.
    const admin = await UserModelClass.getDataById(adminId);
    if (!admin || !admin.isAdmin) {
      response.status(400).json({ error: 'Invalid admin' });
      return;
    }

    // Get companies added by the admin
    const companies = await CompanyModelClass.getDataByBrokerId(adminId);

    response.status(200).json({ companies });
  } catch (error) {
    logger.error('Error fetching companies added by admin.', error);
    response.status(500).json({ error: 'Internal server error' });
    return;
  }
};
```

This uses `getDataByBrokerId` which finds all companies where the `brokerId` field matches the broker's user ID:

```typescript
public static async getDataByBrokerId(
  brokerId: string
): Promise<CompanyDataInterface[]> {
  try {
    const data = await this.companyModel.find({ brokerId }).lean();
    return data.map((item) => ({
      _id: item._id,
      name: item.name,
      // ...other fields
    })) as CompanyDataInterface[];
  } catch (error) {
    console.error(error);
    return [];
  }
}
```

## Activation Status Management

The `isActivated` flag is used throughout QHarmony to control access to the system. This section explains how activation status is managed for different entity types.

### 1. Company Activation Status

The `isActivated` flag in the `CompanyDataInterface` indicates whether a company account is active in the system:

```typescript
export interface CompanyDataInterface {
  // ...other fields
  isActivated: boolean;
  // ...more fields
}
```

#### 1.1 Self-Onboarded Companies

When a user self-onboards as an admin:
1. A temporary company record is created with `isActivated: false`
2. When the user completes the onboarding form after clicking the magic link, a new company record is created with `isActivated: true`
3. The activation happens during the onboarding process in `onboardAdmin`:

```typescript
// Save the company in the database.
const companyEntity = await CompanyModelClass.addData(company);
// company object includes isActivated: true from frontend
```

#### 1.2 Broker-Added Companies

When a broker adds an employer company:
1. The company is immediately created with `isActivated: true`
2. This is set in the `addEmployer` method:

```typescript
const companyDetails = {
  // ...other fields
  isActivated: true,
};
```

### 2. User Activation Status

The `isActivated` flag in the `UserDataInterface` indicates whether a user account is active:

```typescript
export interface UserDataInterface {
  // ...other fields
  isActivated: boolean;
  isDisabled: boolean;
  // ...more fields
}
```

#### 2.1 Admin Users

When an admin completes onboarding:
1. The admin user is created with `isActivated: true`
2. This happens in the `onboardAdmin` method:

```typescript
const userEntity = await UserModelClass.addData({
  ...user,
  companyId: companyEntity._id.toString(),
  isActivated: true,
  isDisabled: false,
  // ...other fields
});
```

#### 2.2 Employees Added by Admins

When an admin adds employees:
1. Employees are initially created with `isActivated: false`
2. When an employee clicks their magic link, their account is activated in the `onboardEmployee` method:

```typescript
await UserModelClass.updateData(userId, { isActivated: true });
```

#### 2.3 Disabled Users

Users can be disabled by admins:
1. When an admin offboards an employee, both flags are updated:

```typescript
await UserModelClass.updateData(userId, {
  isActivated: false,
  isDisabled: true,
});
```

2. Disabled users can be re-enabled:

```typescript
await UserModelClass.updateData(userId, {
  isDisabled: false,
  isActivated: false,  // Note: Still needs to be activated via magic link
});
```

### 3. Activation Status in Authentication Flow

The activation status affects the authentication flow:

1. **Login Attempts**:
   - If a user exists but `isActivated` is false, they need to complete onboarding
   - If a user exists and `isActivated` is true, they can log in normally

2. **API Access**:
   - Controllers can check `isActivated` status to determine if a user should have access
   - This provides an additional layer of access control beyond role-based permissions

3. **User Interface**:
   - The frontend can use activation status to show appropriate UI elements
   - For example, showing onboarding forms for non-activated users

## Conclusion

The QHarmony authentication system combines Firebase Authentication with custom middleware to create a secure, passwordless authentication flow. The system uses:

1. **Magic Links** for initial authentication, providing a secure and user-friendly login experience
2. **Firebase Auth** for session management and token generation
3. **Local Storage** for persisting user context
4. **User ID Headers** for authenticating API requests
5. **Specialized Middleware** for role-based access control

This multi-layered approach ensures:

- **Security**: By eliminating password-based authentication
- **Usability**: Through passwordless login and automatic session persistence
- **Flexibility**: With role-based access control
- **Reliability**: Through multiple authentication fallbacks

The system also implements a hierarchical relationship between brokers, employers, and employees, with clear data models and relationships. Activation status is managed for both companies and users, providing fine-grained control over system access.

While the system includes a JWT-based cookie authentication middleware, it primarily relies on the user ID header mechanism for API authentication, with the Firebase authentication providing the initial security layer.

The authentication flow handles various edge cases, including invalid links, expired sessions, and unauthorized access attempts, providing a robust authentication system for the QHarmony application.

## User Profile Update API

### Current User Update Endpoint

The system provides a streamlined user profile update API that allows users to update their own profile information with proper authentication and validation.

#### API Specification

**Primary Endpoint**: `PUT /admin/update/employee`
**Dedicated Signature Endpoint**: `POST /admin/update/signature`

**Authentication**: User ID header required for both endpoints

#### Primary Update API Request Format

**Endpoint**: `PUT /admin/update/employee`

```typescript
Headers: {
  'user-id': 'user_id'  // ID of user making the request (user updates themselves)
}

Body: {
  updatedDetails: {
    // Top-level user fields
    name?: string;
    role?: string;
    isActivated?: boolean;
    isDisabled?: boolean;

    // Details object (comprehensive user profile data)
    details?: {
      // 📞 Basic Contact Information
      phoneNumber?: string;
      department?: string;
      title?: string;
      role?: string;

      // 🎯 Employee demographic data
      dateOfBirth?: Date;
      hireDate?: Date;
      annualSalary?: number;
      employeeClassType?: string;
      customPayrollFrequency?: string;

      // 🆔 Personal Identification
      ssn?: string;

      // 🏠 Address Information
      address?: {
        street1?: string;
        street2?: string;
        city?: string;
        state?: string;
        zipCode?: string;
        country?: string;
      };
      mailingAddress?: {
        street1?: string;
        street2?: string;
        city?: string;
        state?: string;
        zipCode?: string;
        country?: string;
      };

      // 👨‍👩‍👧‍👦 Family Information
      dependents?: Array<{
        name: string;
        gender: string;
        dateOfBirth: Date;
        relationship: string;
        ssn?: string;
        isStudent?: boolean;
        isDisabled?: boolean;
        // ... additional dependent fields
      }>;

      // 🚨 Emergency Contact
      emergencyContact?: {
        name?: string;
        relationship?: string;
        phoneNumber?: string;
        email?: string;
        address?: AddressInterface;
      };

      // 💼 Employment Details
      employeeId?: string;
      managerId?: string;
      workLocation?: string;
      workSchedule?: string;

      // 🏥 Health & Benefits
      tobaccoUser?: boolean;
      disabilityStatus?: string;
      veteranStatus?: string;

      // 📋 Compliance & Reporting
      i9Verified?: boolean;
      w4OnFile?: boolean;
      directDepositSetup?: boolean;

      // 📅 Important Dates
      terminationDate?: Date;
      rehireDate?: Date;
      lastReviewDate?: Date;
      nextReviewDate?: Date;

      // 📝 Notes & Comments
      notes?: string;
      hrNotes?: string;

      // ✍️ Digital Signature (NEW)
      signature?: string;  // Base64-encoded signature data
    };
  }
}
```

#### Dedicated Signature API Request Format

**Endpoint**: `POST /admin/update/signature`

```typescript
Headers: {
  'user-id': 'user_id'  // ID of user making the request (user updates themselves)
}

Body: {
  signatureData: string;  // Base64-encoded signature data (up to 1MB)
}
```

**Response Format** (Signature API):
```typescript
{
  message: 'Enrollment signature updated successfully',
  signedAt: '2025-06-18T10:30:00.000Z',
  employeeName: 'John Doe'  // Uses existing user.name
}
```

#### Primary Update API Response Format
```typescript
{
  message: 'User updated successfully',
  updatedUser: {
    id: 'user_id',
    name: 'User Name',
    email: '<EMAIL>'
  }
}
```

#### Authorization Logic

Both APIs use a flexible self-update authorization system with activation-based access control:

#### **Primary Update API (`PUT /admin/update/employee`):**
1. **Activated Users** - Full profile updates allowed:
   - Can update all profile fields including signature
   - Must belong to a valid company
   - Email updates are blocked for security

2. **Non-Activated Users** - Limited signature-only updates:
   - Can only update `signature` or `enrollmentSignature` fields
   - Needed for enrollment workflow before account activation
   - All other profile updates blocked until activation

#### **Signature API (`POST /admin/update/signature`):**
1. **All Users** (activated and non-activated):
   - Can update enrollment signature regardless of activation status
   - 1MB size limit with Base64 format validation
   - Designed for enrollment workflow support

#### Usage Examples

#### **Primary Update API Examples**

**Activated User - Full Profile Update**:
```typescript
PUT /admin/update/employee
Headers: { 'user-id': 'user123' }
Body: {
  updatedDetails: {
    name: 'John Doe',
    details: {
      phoneNumber: '+**********',
      department: 'Engineering',
      title: 'Senior Developer'
    }
  }
}
```

**Non-Activated User - Signature Only Update**:
```typescript
PUT /admin/update/employee
Headers: { 'user-id': 'non-activated-user-456' }
Body: {
  updatedDetails: {
    details: {
      signature: "base64string...",
      enrollmentSignature: {
        signatureData: "base64string...",
        signedAt: "2025-06-18T10:30:00Z",
        isVerified: false
      }
    }
  }
}
```

**Comprehensive Profile Update** (Activated Users Only):
```typescript
PUT /admin/update/employee
Headers: { 'user-id': 'user123' }
Body: {
  updatedDetails: {
    name: 'John Doe',
    details: {
      phoneNumber: '+**********',
      department: 'Engineering',
      title: 'Senior Developer',
      dateOfBirth: '1990-01-15',
      hireDate: '2023-01-01',
      annualSalary: 75000,
      employeeClassType: 'Full-Time',
      address: {
        street1: '123 Main St',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94105',
        country: 'US'
      },
      emergencyContact: {
        name: 'Jane Doe',
        relationship: 'Spouse',
        phoneNumber: '+1234567891',
        email: '<EMAIL>'
      },
      signature: 'eyJzaWduYXR1cmUiOiJkYXRhOmltYWdlL3BuZztiYXNlNjQs...' // Base64 signature
    }
  }
}
```

#### **Dedicated Signature API Examples**

**Simple Signature Update** (Works for all users):
```typescript
POST /admin/update/signature
Headers: { 'user-id': 'any-user-123' }
Body: {
  signatureData: "eyJzaWduYXR1cmUiOiJkYXRhOmltYWdlL3BuZztiYXNlNjQs..."
}

// Response:
{
  message: 'Enrollment signature updated successfully',
  signedAt: '2025-06-18T10:30:00.000Z',
  employeeName: 'John Doe'
}
```

**Large Signature Upload** (Up to 1MB):
```typescript
POST /admin/update/signature
Headers: { 'user-id': 'user-with-large-signature' }
Body: {
  signatureData: "very-long-base64-string-up-to-1MB..."
}
```

#### Security Features

#### **Primary Update API Security:**
- ✅ **Self-updates only** - users can only update their own profile
- ✅ **Email updates blocked** for security (email field filtered out)
- ✅ **Activation-based access control** - full updates for activated users, signature-only for non-activated
- ✅ **Company validation** - user must belong to valid company
- ✅ **Comprehensive validation** - all fields validated according to schema

#### **Signature API Security:**
- ✅ **Universal access** - works for all users regardless of activation status
- ✅ **Size validation** - 1MB maximum limit to prevent abuse
- ✅ **Format validation** - Base64 format validation
- ✅ **Self-updates only** - users can only update their own signature
- ✅ **Enrollment workflow support** - enables signing before account activation

#### Error Responses

#### **Common Errors (Both APIs):**

**400 Bad Request - Missing User ID**:
```typescript
{
  error: 'User ID required in headers'
}
```

**400 Bad Request - User Not Found**:
```typescript
{
  error: 'User to update does not exist'
}
```

**403 Forbidden - Account Not Activated** (Primary Update API only):
```typescript
{
  error: 'User account is not activated. Only signature updates are allowed for enrollment purposes.'
}
```

**400 Bad Request - Invalid Company**:
```typescript
{
  error: 'Company does not exist'
}
```

**500 Internal Server Error**:
```typescript
{
  error: 'Internal server error'
}
```

#### **Signature API Specific Errors:**

**400 Bad Request - Missing Signature Data**:
```typescript
{
  error: 'signatureData is required'
}
```

**400 Bad Request - Signature Too Large**:
```typescript
{
  error: 'Signature data too large. Maximum size is 1MB.'
}
```

**400 Bad Request - Invalid Format**:
```typescript
{
  error: 'Invalid signature data format. Must be Base64 encoded.'
}
```

#### Validation Rules

**Field Validation**:
- **SSN**: Must be in format XXX-XX-XXXX or XXXXXXXXX
- **State**: Must be valid 2-character abbreviation (e.g., CA, NY)
- **ZIP Code**: Must be in format XXXXX or XXXXX-XXXX
- **Email**: Must be valid email format (for emergency contacts)
- **Signature**: Must be valid Base64-encoded string
- **Employee Class**: Must be from predefined enum values
- **Payroll Frequency**: Must be from predefined enum values

**Business Rules**:

#### **Primary Update API Rules:**
- Users can only update their own profile
- Email field is automatically filtered out for security
- All updates are partial - only provided fields are updated
- Nested objects (details) are merged, not replaced
- **Activated users**: Full profile updates allowed
- **Non-activated users**: Only signature fields allowed (enrollment support)
- User must belong to a valid company

#### **Signature API Rules:**
- Users can only update their own signature
- Works for all users regardless of activation status
- Maximum 1MB Base64-encoded signature data
- Automatically sets `signedAt` timestamp and `isVerified: false`
- Uses existing `user.name` for `employeeName` in response

### Recent Updates

#### **🎯 Current Implementation (Updated)**

1. **Simplified Authorization Model**:
   - **Removed complex admin authorization** - users now update themselves directly
   - **Self-update only** - user-id in header identifies the user being updated
   - **No adminId required** - eliminates confusion and simplifies frontend integration
   - **Activation-based access control** - flexible permissions based on user activation status

2. **Enhanced User Data Model**: The `UserDataInterface` now includes comprehensive employee information:
   - **Personal identification** (SSN with format validation)
   - **Address information** (primary and mailing addresses)
   - **Family information** (comprehensive dependent management)
   - **Emergency contact details** (full contact information)
   - **Employment details** (employee ID, manager, work location)
   - **Health & benefits** (tobacco use, disability status, veteran status)
   - **Compliance & reporting** (I-9, W-4, direct deposit status)
   - **Important dates** (termination, rehire, review dates)
   - **Digital signature** (Base64-encoded signature support)

3. **Comprehensive Validation**:
   - **Format validation** for SSN, ZIP codes, state abbreviations
   - **Email validation** for emergency contacts
   - **Base64 validation** for digital signatures
   - **Enum validation** for employee class and payroll frequency

4. **Security & Data Integrity**:
   - **Email updates blocked** automatically for security
   - **Account activation required** - only activated users can update
   - **Company validation** - user must belong to valid company
   - **Partial updates** - only provided fields are updated

5. **Frontend Integration Ready**:
   - **Simple request format** - just user-id header and updatedDetails body
   - **Comprehensive field support** - supports all user profile fields
   - **Signature integration** - ready for digital signature functionality
   - **Error handling** - clear error messages for all validation failures

6. **Dedicated Signature API** (`POST /admin/update/signature`):
   - **Universal access** - works for all users regardless of activation status
   - **Large signature support** - up to 1MB Base64-encoded signatures
   - **Enrollment workflow** - enables signing before account activation
   - **Optimized for signatures** - dedicated endpoint for signature-only updates
   - **Size validation** - prevents abuse with 1MB limit

#### **🔄 Migration from Old Version**

**Old Format** (Deprecated):
```typescript
Body: {
  adminId: 'admin456',
  updatedDetails: { ... }
}
```

**New Format** (Current):
```typescript
// Primary Update API
PUT /admin/update/employee
Body: {
  updatedDetails: { ... }  // adminId no longer needed
}

// NEW: Dedicated Signature API
POST /admin/update/signature
Body: {
  signatureData: "base64string..."  // Simple signature-only updates
}
```

The system now provides **two complementary APIs**:
- **Primary API** (`PUT /admin/update/employee`) - Full profile management with activation-based access control
- **Signature API** (`POST /admin/update/signature`) - Dedicated signature updates for all users

This dual-API approach supports both comprehensive profile management and streamlined enrollment workflows.

#### **🔍 Login Activity Tracking**

The `employeeDetails` method in `EmployeeController` has been updated to track user login activity more accurately. Now, whenever a user's details are fetched through the `/employee` endpoint (which happens during automatic login and when accessing protected routes), the `lastLoginAt` timestamp is updated. This ensures that user login timestamps are tracked not just during the initial login process but also during subsequent sessions, providing more accurate user activity tracking.
