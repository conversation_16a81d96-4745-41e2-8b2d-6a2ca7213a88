'use client';

import React from 'react';
import { User, Play, BarChart3 } from 'lucide-react';

interface WelcomePageProps {
  onNext: () => void;
}

export const WelcomePage: React.FC<WelcomePageProps> = ({ onNext }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          backgroundColor: '#dbeafe',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <User style={{ width: '20px', height: '20px', color: '#2563eb' }} />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '512px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0
          }}>
            👋 Hi there! Ready to find the perfect benefits for 2025?
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0'
          }}>
            I'm here to make this super easy. I'll ask smart questions and recommend the best plans for your situation.
          </p>
        </div>
      </div>

      {/* Welcome Card */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <div style={{
            width: '24px',
            height: '24px',
            backgroundColor: '#dbeafe',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <User style={{ width: '16px', height: '16px', color: '#2563eb' }} />
          </div>
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            Welcome to Your AI Benefits Assistant! ✨
          </h2>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <p style={{
            color: '#374151',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            margin: 0
          }}>
            👋 Hi there! It's time to enroll in your 2025 Medical, Dental, and Vision benefits.
          </p>

          {/* Deadline Notice */}
          <div style={{
            backgroundColor: '#fdf2f8',
            border: '1px solid #fce7f3',
            borderRadius: '8px',
            padding: '16px'
          }}>
            <p style={{
              color: '#be185d',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              margin: 0
            }}>
              ⏰ <span style={{ fontWeight: '600' }}>Deadline:</span> December 15, 2024
            </p>
          </div>

          {/* Assistant Features */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <p style={{
              color: '#ec4899',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              margin: 0
            }}>
              🧠 I'm your personalized benefits assistant. I'll:
            </p>

            <ul style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              marginLeft: '24px',
              listStyle: 'none',
              padding: 0,
              margin: 0
            }}>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Ask smart questions to understand your needs
              </li>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Recommend the best plans for your situation
              </li>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Show you helpful videos explaining each plan
              </li>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Answer any questions you have
              </li>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Help you compare plans side-by-side
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div style={{ display: 'flex', gap: '12px', paddingTop: '16px' }}>
            <button style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <span style={{ color: '#2563eb' }}>❓</span>
              Ask Questions
            </button>
            <button style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <Play size={16} style={{ color: '#6b7280' }} />
              Watch Video
            </button>
            <button style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <BarChart3 size={16} style={{ color: '#6b7280' }} />
              Compare Plans
            </button>
          </div>

          {/* Start Button */}
          <button
            onClick={onNext}
            style={{
              width: '100%',
              backgroundColor: '#111827',
              color: 'white',
              padding: '12px 24px',
              borderRadius: '8px',
              fontWeight: '500',
              border: 'none',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              marginTop: '24px'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#374151'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#111827'}
          >
            🚀 Start My Smart Enrollment
          </button>
        </div>
      </div>
    </div>
  );
};
