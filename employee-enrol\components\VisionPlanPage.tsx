'use client';

import React, { useState } from 'react';
import { User, CheckCircle, Play, Eye } from 'lucide-react';

interface VisionPlan {
  id: string;
  name: string;
  cost: number;
  features: string[];
}

interface VisionPlanPageProps {
  onPlanSelect: (plan: VisionPlan | null) => void;
}

const VisionPlanPage: React.FC<VisionPlanPageProps> = ({ onPlanSelect }) => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const visionPlans: VisionPlan[] = [
    {
      id: 'vsp-choice',
      name: 'VSP Choice Plan',
      cost: 5.25,
      features: [
        'Exam every 12 months, $10 copay',
        '$150 frame allowance',
        'Contact lens coverage'
      ]
    },
    {
      id: 'no-vision',
      name: 'No Vision Plan',
      cost: 0.00,
      features: [
        'You can skip this if not needed',
        'Save money if you don\'t wear glasses'
      ]
    }
  ];

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
    const plan = visionPlans.find(p => p.id === planId);
    onPlanSelect(plan || null);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{ 
          width: '40px', 
          height: '40px', 
          backgroundColor: '#dbeafe', 
          borderRadius: '8px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <User style={{ width: '20px', height: '20px', color: '#2563eb' }} />
        </div>
        <div style={{ 
          backgroundColor: '#f9fafb', 
          borderRadius: '8px', 
          padding: '16px', 
          maxWidth: '512px' 
        }}>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: 0 
          }}>
            👓 Last step - do you want vision coverage?
          </p>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: '8px 0 0 0' 
          }}>
            Even if you don't wear glasses now, regular eye exams are important for your health.
          </p>
        </div>
      </div>

      {/* Vision Plan Selection */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        border: '1px solid #e5e7eb', 
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <Eye style={{ width: '20px', height: '20px', color: '#3b82f6' }} />
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: '600', 
            color: '#111827',
            margin: 0
          }}>
            Vision Plan Selection
          </h2>
        </div>

        <p style={{ 
          color: '#6b7280', 
          marginBottom: '24px',
          margin: 0
        }}>
          Finally, let's handle your vision needs:
        </p>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {visionPlans.map((plan) => (
            <div
              key={plan.id}
              style={{
                border: selectedPlan === plan.id ? '2px solid #3b82f6' : '2px solid #e5e7eb',
                borderRadius: '8px',
                padding: '20px',
                backgroundColor: selectedPlan === plan.id ? '#eff6ff' : '#f9fafb',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onClick={() => handlePlanSelect(plan.id)}
            >
              <div style={{ marginBottom: '12px' }}>
                <h3 style={{ 
                  fontSize: '18px', 
                  fontWeight: '600', 
                  color: '#111827',
                  margin: 0
                }}>
                  {plan.name}
                </h3>
                <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '4px' }}>
                  <span style={{ 
                    fontSize: '24px', 
                    fontWeight: '700', 
                    color: '#111827' 
                  }}>
                    ${plan.cost.toFixed(2)}
                  </span>
                  <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                {plan.features.map((feature, index) => (
                  <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <CheckCircle style={{ width: '16px', height: '16px', color: '#10b981', flexShrink: 0 }} />
                    <span style={{ color: '#374151', fontSize: '14px' }}>{feature}</span>
                  </div>
                ))}
              </div>

              <button
                style={{
                  width: '100%',
                  backgroundColor: selectedPlan === plan.id ? '#3b82f6' : '#f3f4f6',
                  color: selectedPlan === plan.id ? 'white' : '#6b7280',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  fontWeight: '500',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                {selectedPlan === plan.id ? 'Selected' : 'Select This Plan'}
              </button>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div style={{ 
          display: 'flex', 
          gap: '12px', 
          paddingTop: '24px', 
          borderTop: '1px solid #e5e7eb', 
          marginTop: '24px' 
        }}>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <Play size={16} style={{ color: '#6b7280' }} />
            Watch Video
          </button>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <span style={{ color: '#2563eb' }}>❓</span>
            Ask Questions
          </button>
        </div>
      </div>
    </div>
  );
};

export default VisionPlanPage;
