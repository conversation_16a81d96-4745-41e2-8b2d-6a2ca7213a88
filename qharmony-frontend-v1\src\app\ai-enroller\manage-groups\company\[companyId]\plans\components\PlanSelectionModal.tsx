'use client';

import React, { useState, useEffect } from 'react';
import { HiOutlineX, HiOutlinePlus, HiOutlineSearch, HiOutlineCheck } from 'react-icons/hi';
import CreatePlanForm from './CreatePlanForm';
import { getAssignablePlans } from '../../../../services/planAssignmentApi';

interface Plan {
  _id: string;
  planName: string;
  planCode: string;
  coverageType: string;
  coverageSubTypes?: string[];
  planType: string;
  metalTier?: string;
  description?: string;
  highlights?: string[];
  carrierId?: string;
  status?: string;
  isTemplate?: boolean;
}

interface PlanSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectPlan: (plan: Plan) => void;
  onCreatePlan?: (plan: Plan) => void;
  availablePlans?: Plan[]; // Made optional since we'll fetch from API
  companyId?: string;
}

const PlanSelectionModal: React.FC<PlanSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectPlan,
  onCreatePlan,
  availablePlans = [],
  companyId
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [coverageType, setCoverageType] = useState<string>('plan');
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlanId, setSelectedPlanId] = useState<string>(''); // Single plan selection
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('All Statuses');
  const [carrierFilter, setCarrierFilter] = useState<string>('All Carriers');
  const [currentStep, setCurrentStep] = useState<'select' | 'configure' | 'dates'>('select');
  const [contributionType, setContributionType] = useState<'percentage' | 'fixed'>('percentage');
  const [coverageTiers, setCoverageTiers] = useState([
    { id: '1', tier: 'Employee Only', premium: 450.00, employerPercent: 80, employerPays: 360.00, employeePays: 90.00 },
    { id: '2', tier: 'Employee + Spouse', premium: 880.00, employerPercent: 80, employerPays: 704.00, employeePays: 176.00 },
    { id: '3', tier: 'Employee + Children', premium: 720.00, employerPercent: 80, employerPays: 576.00, employeePays: 144.00 },
    { id: '4', tier: 'Employee + Family', premium: 1250.00, employerPercent: 80, employerPays: 1000.00, employeePays: 250.00 }
  ]);
  const [enrollmentStartDate, setEnrollmentStartDate] = useState('2024-11-01');
  const [enrollmentEndDate, setEnrollmentEndDate] = useState('2024-11-30');
  const [planStartDate, setPlanStartDate] = useState('2025-01-01');
  const [planEndDate, setPlanEndDate] = useState('2025-12-31');

  const fetchPlans = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all assignable plans (Active plans only)
      const result = await getAssignablePlans();

      if (result.success && result.data) {
        setPlans(result.data);
        // Set coverage type based on first plan or default
        if (result.data.length > 0) {
          setCoverageType(result.data[0].coverageType || 'plan');
        }
      } else {
        setError(result.error || 'Failed to fetch assignable plans');
        // Fallback to provided plans if API fails
        setPlans(availablePlans);
      }
    } catch (error) {
      console.error('Error fetching assignable plans:', error);
      setError('Failed to fetch assignable plans');
      // Fallback to provided plans if API fails
      setPlans(availablePlans);
    } finally {
      setLoading(false);
    }
  };

  // Fetch plans from backend API
  useEffect(() => {
    if (isOpen) {
      fetchPlans();
    }
  }, [isOpen]); // Remove fetchPlans from dependency array to prevent infinite loop

  if (!isOpen) return null;

  const filteredPlans = plans.filter(plan => {
    // Search filter - include coverage subtypes in search
    const matchesSearch = searchTerm === '' ||
      plan.planName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plan.planCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plan.planType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plan.coverageType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (plan.coverageSubTypes && plan.coverageSubTypes.some(subtype =>
        subtype.toLowerCase().includes(searchTerm.toLowerCase())
      ));

    // Status filter
    const matchesStatus = statusFilter === 'All Statuses' ||
      (plan.status || 'Active') === statusFilter;

    // Carrier filter - use coverage subtypes for filtering
    const matchesCarrier = carrierFilter === 'All Carriers' ||
      plan.coverageType === carrierFilter ||
      (plan.coverageSubTypes && plan.coverageSubTypes.includes(carrierFilter));

    return matchesSearch && matchesStatus && matchesCarrier;
  });

  const handleCreateNewPlan = (newPlan: Plan) => {
    // Use onCreatePlan if provided, otherwise fall back to onSelectPlan
    if (onCreatePlan) {
      onCreatePlan(newPlan);
    } else {
      onSelectPlan(newPlan);
    }
    setShowCreateForm(false);
    onClose();
  };

  const handleSelectPlan = (plan: Plan) => {
    // Single plan selection - move to configure step
    setSelectedPlanId(plan._id);
    setSelectedPlan(plan);
    setCurrentStep('configure');
  };

  const handleBackToSelect = () => {
    setCurrentStep('select');
    setSelectedPlanId('');
    setSelectedPlan(null);
  };

  const handleContinueToDates = () => {
    setCurrentStep('dates');
  };

  const handleBackFromDates = () => {
    setCurrentStep('configure');
  };

  const updateTier = (id: string, field: string, value: number) => {
    setCoverageTiers(prev => prev.map(tier => {
      if (tier.id === id) {
        const updated = { ...tier, [field]: value };

        // Recalculate based on contribution type
        if (contributionType === 'percentage') {
          if (field === 'premium' || field === 'employerPercent') {
            const premium = field === 'premium' ? value : updated.premium;
            const employerPercent = field === 'employerPercent' ? value : updated.employerPercent;

            updated.employerPays = (premium * employerPercent) / 100;
            updated.employeePays = premium - updated.employerPays;
          }
        } else {
          // Fixed amount - recalculate percentage when amounts change
          if (field === 'premium' || field === 'employerPays') {
            const premium = field === 'premium' ? value : updated.premium;
            const employerPays = field === 'employerPays' ? value : updated.employerPays;

            updated.employerPays = employerPays;
            updated.employeePays = premium - employerPays;
            updated.employerPercent = premium > 0 ? Math.round((employerPays / premium) * 100) : 0;
          }
        }

        return updated;
      }
      return tier;
    }));
  };

  const deleteTier = (id: string) => {
    setCoverageTiers(prev => prev.filter(tier => tier.id !== id));
  };



  const handleCompleteAssignment = async () => {
    if (!selectedPlan || !companyId) {
      alert('Missing required information');
      return;
    }

    try {
      // Create plan assignment with coverage tiers and dates
      const assignmentData = {
        planId: selectedPlan._id,
        companyId: companyId,
        rateStructure: 'Composite',
        coverageTiers: coverageTiers.map(tier => ({
          tierName: tier.tier,
          totalCost: tier.premium,
          employerCost: tier.employerPays,
          employeeCost: tier.employeePays
        })),
        enrollmentStartDate: enrollmentStartDate,
        enrollmentEndDate: enrollmentEndDate,
        planEffectiveDate: planStartDate,
        planEndDate: planEndDate,
        status: 'Active',
        isActive: true
      };

      // Import the API function
      const { createPlanAssignment } = await import('../../../../services/planAssignmentApi');
      const result = await createPlanAssignment(assignmentData);

      if (result.success) {
        alert('Plan assignment created successfully!');
        onSelectPlan(selectedPlan); // Notify parent component
        onClose(); // Close modal
      } else {
        alert(`Failed to create plan assignment: ${result.error}`);
      }
    } catch (error) {
      console.error('Error creating plan assignment:', error);
      alert('Error creating plan assignment');
    }
  };



  const getStepTitle = () => {
    switch (currentStep) {
      case 'select': return showCreateForm ? 'Create a new plan' : 'Select a plan';
      case 'configure': return `Configure Coverage Tiers - ${selectedPlan?.planName}`;
      case 'dates': return `Set Enrollment & Coverage Dates - ${selectedPlan?.planName}`;
      default: return 'Select a plan';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl max-h-[95vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              {currentStep !== 'select' && (
                <button
                  onClick={currentStep === 'dates' ? handleBackFromDates : handleBackToSelect}
                  className="text-gray-600 hover:text-gray-800 transition-colors"
                >
                  ← Back
                </button>
              )}
              <h2 className="text-2xl font-bold text-gray-900">
                {getStepTitle()}
              </h2>
            </div>
            <div className="flex items-center gap-3">
              {currentStep === 'select' && !showCreateForm && (
                <button
                  onClick={() => setShowCreateForm(true)}
                  className="flex items-center gap-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors"
                >
                  <HiOutlinePlus className="w-4 h-4" />
                  Create new plan
                </button>
              )}
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <HiOutlineX className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Step Progress Indicator */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center gap-4">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep === 'select' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}`}>
                1
              </div>
              <div className="w-16 h-1 bg-gray-200"></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep === 'configure' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}`}>
                2
              </div>
              <div className="w-16 h-1 bg-gray-200"></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep === 'dates' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}`}>
                3
              </div>
            </div>
          </div>

          {/* Step Content */}
          {currentStep === 'select' && (
            <>
              {!showCreateForm ? (
            <>
              {/* Search & Filter Section */}
              <div className="mb-6">
                <div className="flex items-center gap-2 mb-4">
                  <HiOutlineSearch className="w-5 h-5 text-gray-400" />
                  <h3 className="text-lg font-semibold text-gray-900">Search & Filter</h3>
                </div>

                <div className="flex gap-4 items-center">
                  <div className="relative flex-1">
                    <input
                      type="text"
                      placeholder="Search by plan name, code, or coverage subtype..."
                      className="w-full px-4 py-2.5 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <HiOutlineSearch className="absolute left-3 top-3 text-gray-400 w-5 h-5" />
                  </div>

                  <select
                    className="px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-700 min-w-[140px]"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option>All Statuses</option>
                    <option>Active</option>
                    <option>Draft</option>
                    <option>Archived</option>
                  </select>

                  <select
                    className="px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-700 min-w-[140px]"
                    value={carrierFilter}
                    onChange={(e) => setCarrierFilter(e.target.value)}
                  >
                    <option>All Carriers</option>
                    <option>Your Health</option>
                    <option>Income Security</option>
                  </select>
                </div>

                <div className="mt-3 text-sm text-gray-600">
                  Showing {filteredPlans.length} of {plans.length} plans
                </div>
              </div>

              {/* Plans List Section */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Plans List</h3>
              </div>

              {/* Loading State */}
              {loading && (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  <span className="ml-2 text-gray-600">Loading plans...</span>
                </div>
              )}

              {/* Error State */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <p className="text-red-600 text-sm">{error}</p>
                  <button
                    onClick={fetchPlans}
                    className="mt-2 text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    Try again
                  </button>
                </div>
              )}

              {/* Plans List */}
              {!loading && !error && (
                <div className="space-y-4">
                  {/* Table Header */}
                  <div className="grid grid-cols-12 gap-6 px-6 py-3 bg-gray-50 rounded-lg text-sm font-medium text-gray-700">
                    <div className="col-span-3">Plan Name</div>
                    <div className="col-span-3">Plan Code</div>
                    <div className="col-span-2">Coverage Subtype</div>
                    <div className="col-span-1">Status</div>
                    <div className="col-span-1">Metal Tier</div>
                    <div className="col-span-2">Action</div>
                  </div>

                  {/* Plans Cards */}
                  {filteredPlans.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      {searchTerm ? 'No plans found matching your search.' : 'No plans available.'}
                    </div>
                  ) : (
                    filteredPlans.map((plan) => (
                      <div
                        key={plan._id}
                        className={`grid grid-cols-12 gap-6 px-6 py-4 bg-white border rounded-lg hover:shadow-md transition-all ${
                          selectedPlanId === plan._id
                            ? 'border-blue-300 bg-blue-50 shadow-md'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        {/* Plan Name */}
                        <div className="col-span-3">
                          <div className="font-semibold text-gray-900">{plan.planName}</div>
                          {plan.description && (
                            <div className="text-sm text-gray-500 truncate mt-1">{plan.description}</div>
                          )}
                        </div>

                        {/* Plan Code */}
                        <div className="col-span-3">
                          <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800">
                            {plan.planCode}
                          </span>
                        </div>

                        {/* Coverage Subtype */}
                        <div className="col-span-2">
                          <div className="flex flex-wrap gap-1">
                            {plan.coverageSubTypes && plan.coverageSubTypes.length > 0 ? (
                              plan.coverageSubTypes.map((subtype, index) => (
                                <span key={index} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700">
                                  {subtype}
                                </span>
                              ))
                            ) : (
                              <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-50 text-gray-700">
                                {plan.coverageType}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Status */}
                        <div className="col-span-1">
                          <span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${
                            plan.status === 'Active'
                              ? 'bg-green-100 text-green-800'
                              : plan.status === 'Draft'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {plan.status || 'Active'}
                          </span>
                        </div>

                        {/* Metal Tier */}
                        <div className="col-span-1">
                          <span className="text-sm text-gray-600 font-medium">
                            {plan.metalTier || '-'}
                          </span>
                        </div>

                        {/* Action */}
                        <div className="col-span-2">
                          <button
                            onClick={() => handleSelectPlan(plan)}
                            className="px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-blue-600 text-white hover:bg-blue-700"
                          >
                            Select Plan
                          </button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}

            </>
              ) : (
                <CreatePlanForm
                  onCancel={() => setShowCreateForm(false)}
                  onSubmit={handleCreateNewPlan}
                />
              )}
            </>
          )}

          {/* Step 2: Configure Coverage Tiers */}
          {currentStep === 'configure' && selectedPlan && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 mb-2">Selected Plan</h3>
                <div className="text-sm text-blue-800">
                  <div><strong>Plan Name:</strong> {selectedPlan.planName}</div>
                  <div><strong>Coverage Type:</strong> {selectedPlan.coverageType}</div>
                  <div><strong>Plan Type:</strong> {selectedPlan.planType}</div>
                </div>
              </div>

              <div className="bg-white rounded-xl border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-6">
                  <h4 className="text-lg font-semibold text-gray-900">Coverage Tiers & Contributions</h4>
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-gray-700">Contribution Type:</span>
                    <select
                      value={contributionType}
                      onChange={(e) => setContributionType(e.target.value as 'percentage' | 'fixed')}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                    >
                      <option value="percentage">Percentage</option>
                      <option value="fixed">Fixed Amount</option>
                    </select>
                  </div>
                </div>

                {/* Coverage Tiers Table */}
                <div className="space-y-4">
                  <div className="grid grid-cols-5 gap-4 text-sm font-medium text-gray-700 pb-2 border-b">
                    <div>Coverage Tier</div>
                    <div>Premium</div>
                    <div>{contributionType === 'percentage' ? 'Employer %' : 'Employer Pays'}</div>
                    <div>{contributionType === 'percentage' ? 'Employer Pays' : 'Employee Pays'}</div>
                    <div>{contributionType === 'percentage' ? 'Employee Pays' : 'Employer %'}</div>
                  </div>

                  {coverageTiers.map((tier) => (
                    <div key={tier.id} className="grid grid-cols-5 gap-4 items-center text-sm">
                      <input
                        type="text"
                        value={tier.tier}
                        onChange={(e) => setCoverageTiers(prev => prev.map(t =>
                          t.id === tier.id ? { ...t, tier: e.target.value } : t
                        ))}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                      />
                      <input
                        type="number"
                        value={tier.premium}
                        onChange={(e) => updateTier(tier.id, 'premium', parseFloat(e.target.value) || 0)}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                      />
                      {contributionType === 'percentage' ? (
                        <>
                          <input
                            type="number"
                            value={tier.employerPercent}
                            onChange={(e) => updateTier(tier.id, 'employerPercent', parseFloat(e.target.value) || 0)}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                            min="0"
                            max="100"
                          />
                          <div className="px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium">
                            ${tier.employerPays.toFixed(2)}
                          </div>
                          <div className="px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium">
                            ${tier.employeePays.toFixed(2)}
                          </div>
                        </>
                      ) : (
                        <>
                          <input
                            type="number"
                            value={tier.employerPays}
                            onChange={(e) => updateTier(tier.id, 'employerPays', parseFloat(e.target.value) || 0)}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                            min="0"
                          />
                          <div className="px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium">
                            ${tier.employeePays.toFixed(2)}
                          </div>
                          <div className="px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium">
                            {tier.employerPercent.toFixed(1)}%
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={handleContinueToDates}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all"
                >
                  Continue to Dates
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Set Dates */}
          {currentStep === 'dates' && selectedPlan && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 mb-2">Plan Assignment Summary</h3>
                <div className="text-sm text-blue-800">
                  <div><strong>Plan:</strong> {selectedPlan.planName}</div>
                  <div><strong>Coverage Tiers:</strong> {coverageTiers.length} configured</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900">Enrollment Period</h4>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Enrollment Start Date
                    </label>
                    <input
                      type="date"
                      value={enrollmentStartDate}
                      onChange={(e) => setEnrollmentStartDate(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Enrollment End Date
                    </label>
                    <input
                      type="date"
                      value={enrollmentEndDate}
                      onChange={(e) => setEnrollmentEndDate(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900">Coverage Period</h4>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Plan Effective Date
                    </label>
                    <input
                      type="date"
                      value={planStartDate}
                      onChange={(e) => setPlanStartDate(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Plan End Date
                    </label>
                    <input
                      type="date"
                      value={planEndDate}
                      onChange={(e) => setPlanEndDate(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={handleCompleteAssignment}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all"
                >
                  Complete Plan Assignment
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PlanSelectionModal;
