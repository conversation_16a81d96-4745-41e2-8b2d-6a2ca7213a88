/**
 * Utility functions for dynamic date generation in employee enrollment
 */

export const getCurrentEnrollmentYear = (): number => {
  const now = new Date();
  const currentMonth = now.getMonth(); // 0-based (0 = January, 11 = December)
  
  // If we're in November or December, enrollment is for next year
  // Otherwise, enrollment is for current year
  if (currentMonth >= 10) { // November (10) or December (11)
    return now.getFullYear() + 1;
  }
  return now.getFullYear();
};

export const getEnrollmentDeadline = (): string => {
  // TODO: Eventually this should be dynamically populated from plan assignment
  // For now, using hardcoded date as requested
  return `June 30, 2025`;
};

export const getCoverageYear = (): number => {
  return getCurrentEnrollmentYear();
};

export const getCoverageYearText = (): string => {
  const year = getCoverageYear();
  return `${year} Medical, Dental & Vision`;
};

export const getPlanEffectiveDates = () => {
  const year = getCoverageYear();
  return {
    startDate: `January 1, ${year}`,
    endDate: `December 31, ${year}`,
    year: year
  };
};

// For development/testing - you can override the year
export const setTestYear = (year: number) => {
  // This could be used for testing different years
  // In production, this would be removed or controlled by environment variables
  if (typeof window !== 'undefined') {
    (window as any).__TEST_ENROLLMENT_YEAR = year;
  }
};

export const getTestYear = (): number | null => {
  if (typeof window !== 'undefined') {
    return (window as any).__TEST_ENROLLMENT_YEAR || null;
  }
  return null;
};
