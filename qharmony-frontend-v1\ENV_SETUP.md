# Environment Variables Setup Guide

This document explains how to set up environment variables for the QHarmony Frontend application.

## Quick Start

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Update the values in `.env` according to your environment

3. Restart your development server:
   ```bash
   npm run dev
   ```

## Required Environment Variables

### API Configuration
- `NEXT_PUBLIC_API_URL` - Backend API base URL (required)

### User Configuration
- `NEXT_PUBLIC_USER_ID_KEY` - Primary localStorage key for user ID
- `NEXT_PUBLIC_USER_ID_ALT_KEY` - Alternative localStorage key for user ID

### Admin Configuration
- `NEXT_PUBLIC_ADMIN_EMAILS` - Comma-separated list of admin emails (required)

### AI/Chatbot Configuration
- `NEXT_PUBLIC_CHATBOT_URL` - Chatbot API base URL (required)

### External Services Configuration
- `NEXT_PUBLIC_TEAMS_BOT_DEV_URL` - Teams Bot development URL
- `NEXT_PUBLIC_TEAMS_BOT_PROD_URL` - Teams Bot production URL
- `NEXT_PUBLIC_FINCH_API_URL` - Finch API URL
- `NEXT_PUBLIC_FRONTEND_URL` - Frontend URL for backend integration

### Image Domains Configuration
- `NEXT_PUBLIC_S3_DOMAIN` - S3 domain for images
- `NEXT_PUBLIC_AZURE_BLOB_DOMAIN` - Azure Blob domain for images

### Firebase Configuration (Hostname-based)
The application automatically selects Firebase configuration based on hostname:
- **test.benosphere.com** → Uses test environment variables
- **All other domains** → Uses production environment variables

#### Test Environment Variables:
- `NEXT_PUBLIC_FIREBASE_TEST_API_KEY` - Test Firebase API key
- `NEXT_PUBLIC_FIREBASE_TEST_AUTH_DOMAIN` - Test Firebase auth domain
- `NEXT_PUBLIC_FIREBASE_TEST_PROJECT_ID` - Test Firebase project ID
- `NEXT_PUBLIC_FIREBASE_TEST_STORAGE_BUCKET` - Test Firebase storage bucket
- `NEXT_PUBLIC_FIREBASE_TEST_MESSAGING_SENDER_ID` - Test Firebase messaging sender ID
- `NEXT_PUBLIC_FIREBASE_TEST_APP_ID` - Test Firebase app ID

#### Production Environment Variables:
- `NEXT_PUBLIC_FIREBASE_PROD_API_KEY` - Production Firebase API key
- `NEXT_PUBLIC_FIREBASE_PROD_AUTH_DOMAIN` - Production Firebase auth domain
- `NEXT_PUBLIC_FIREBASE_PROD_PROJECT_ID` - Production Firebase project ID
- `NEXT_PUBLIC_FIREBASE_PROD_STORAGE_BUCKET` - Production Firebase storage bucket
- `NEXT_PUBLIC_FIREBASE_PROD_MESSAGING_SENDER_ID` - Production Firebase messaging sender ID
- `NEXT_PUBLIC_FIREBASE_PROD_APP_ID` - Production Firebase app ID

### Development Configuration
- `NEXT_PUBLIC_DEBUG_LOGGING` - Enable debug logging (true/false)

## Environment-Specific Configurations

### Development (.env)
```bash
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_DEBUG_LOGGING=true

# AI/Chatbot Configuration
NEXT_PUBLIC_CHATBOT_URL=https://bot.benosphere.com

# External Services
NEXT_PUBLIC_TEAMS_BOT_DEV_URL=https://qharmony-teams-bot-dev.azurewebsites.net
NEXT_PUBLIC_TEAMS_BOT_PROD_URL=https://benosphere.azurewebsites.net
NEXT_PUBLIC_FINCH_API_URL=https://connect.tryfinch.com
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000

# Image Domains
NEXT_PUBLIC_S3_DOMAIN=s3.amazonaws.com
NEXT_PUBLIC_AZURE_BLOB_DOMAIN=benosphere.blob.core.windows.net

# Firebase Test Environment (for test.benosphere.com)
NEXT_PUBLIC_FIREBASE_TEST_API_KEY=AIzaSyCPGurROOIHelcNrg3KK3UZpT5NY_v33cw
NEXT_PUBLIC_FIREBASE_TEST_PROJECT_ID=qharmony-test

# Firebase Production Environment (default)
NEXT_PUBLIC_FIREBASE_PROD_API_KEY=AIzaSyAOalm98qF_u74s3qQlIqQ5A6IpWyd0wKA
NEXT_PUBLIC_FIREBASE_PROD_PROJECT_ID=qharmony-dev
```

### Production (.env.production)
```bash
NEXT_PUBLIC_API_URL=https://api.benosphere.com
NEXT_PUBLIC_FALLBACK_USER_ID=your_production_fallback_id
NEXT_PUBLIC_DEBUG_LOGGING=false

# Firebase configurations (same as development)
NEXT_PUBLIC_FIREBASE_TEST_API_KEY=AIzaSyCPGurROOIHelcNrg3KK3UZpT5NY_v33cw
NEXT_PUBLIC_FIREBASE_PROD_API_KEY=AIzaSyAOalm98qF_u74s3qQlIqQ5A6IpWyd0wKA
```

## Environment Utilities

The application includes a centralized environment utility at `src/utils/env.ts` that provides:

- Type-safe access to environment variables
- Default fallback values
- Firebase configuration
- Environment validation

### Usage Examples

```typescript
import {
  getApiBaseUrl,
  getUserId,
  getFirebaseConfig,
  isDebugLoggingEnabled,
  getChatbotUrl,
  getImageDomains,
  getTeamsBotDevUrl
} from '@/utils/env';

// Get API base URL
const apiUrl = getApiBaseUrl();

// Get user ID with fallbacks
const userId = getUserId();

// Get chatbot URL
const chatbotUrl = getChatbotUrl();

// Get image domains
const imageDomains = getImageDomains();

// Get Firebase configuration (automatically selects based on hostname)
const firebaseConfig = getFirebaseConfig();

// Check if debug logging is enabled
if (isDebugLoggingEnabled()) {
  console.log('Debug mode enabled');
}
```

## Firebase Environment Detection

The application uses hostname-based detection to automatically select the correct Firebase configuration:

### How it works:
1. **Test Environment**: When hostname contains `test.benosphere.com` → Uses `NEXT_PUBLIC_FIREBASE_TEST_*` variables
2. **Production Environment**: For all other hostnames (including localhost) → Uses `NEXT_PUBLIC_FIREBASE_PROD_*` variables

### Benefits:
- **Automatic switching** between Firebase projects based on deployment environment
- **No manual configuration** needed when deploying to different environments
- **Consistent with existing** `firebase.js` implementation

### For Development:
- **localhost** uses production Firebase config by default
- To test with test Firebase config, modify your hosts file or use test domain

## Migration from Hardcoded Values

The following hardcoded values have been replaced with environment variables:

### Before
```typescript
const BASE_URL = "http://localhost:8080";
const getUserId = () => localStorage.getItem('userid1') || 'fallback-user-id';
const adminEmails = ["<EMAIL>", "<EMAIL>"];
```

### After
```typescript
import { getApiBaseUrl, getUserId, getAdminEmails } from '@/utils/env';

const BASE_URL = getApiBaseUrl();
const userId = getUserId(); // Throws error if not authenticated
const adminEmails = getAdminEmails();
```

## Validation

The application includes environment validation that checks for required variables including Firebase configuration. Missing variables will be logged to the console.

## Security Notes

- Never commit `.env` files to version control
- Use `.env.example` for documentation
- Prefix client-side variables with `NEXT_PUBLIC_`
- Keep sensitive values in server-side environment variables only

## Troubleshooting

### Common Issues

1. **Environment variables not loading**
   - Ensure variables are prefixed with `NEXT_PUBLIC_` for client-side access
   - Restart the development server after changing `.env`

2. **API calls failing**
   - Check `NEXT_PUBLIC_API_URL` is correct
   - Verify backend is running on the specified URL

3. **User ID issues**
   - Check `NEXT_PUBLIC_FALLBACK_USER_ID` is set
   - Verify localStorage keys match `NEXT_PUBLIC_USER_ID_KEY`

### Debug Mode

Enable debug logging to troubleshoot issues:

```bash
NEXT_PUBLIC_DEBUG_LOGGING=true
```

This will log additional information to the browser console.
