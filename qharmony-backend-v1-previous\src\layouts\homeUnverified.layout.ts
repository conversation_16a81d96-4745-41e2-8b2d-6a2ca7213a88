import FinchService from '../services/finch.service';

export const homeUnverifiedLayout = ({
  slackTeamId,
}: {
  slackTeamId: string;
}) => {
  const finchService = new FinchService();
  const blocks = [
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: 'Please connect your slack account with qHarmony',
      },
    },
    {
      type: 'divider',
    },
    {
      type: 'actions',
      elements: [
        {
          type: 'button',
          text: {
            type: 'plain_text',
            text: 'Connect',
            emoji: true,
          },
          value: 'connect',
          url: `${finchService.getAuthURL({ state: slackTeamId })}`,
          action_id: 'connect',
        },
      ],
    },
  ] as any[];
  return {
    type: 'home',
    blocks,
  };
};
