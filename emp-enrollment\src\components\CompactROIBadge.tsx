
import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrendingUp, DollarSign, ChevronDown, ChevronUp } from 'lucide-react';

interface CompactROIBadgeProps {
  planCost: number;
  planType: 'medical' | 'dental' | 'vision';
  familySize?: string;
  className?: string;
}

export const CompactROIBadge = ({ planCost, planType, familySize = 'self', className = '' }: CompactROIBadgeProps) => {
  const [showDetails, setShowDetails] = useState(false);

  const calculateROI = () => {
    const annualCost = planCost * 26;
    const familyMultiplier = familySize === 'family' ? 3 : familySize === 'spouse' ? 2 : 1;
    
    let employerContribution = 0;
    let potentialSavings = 0;

    switch (planType) {
      case 'medical':
        employerContribution = annualCost * 4; // Employer pays 80%
        potentialSavings = 3000 * familyMultiplier;
        break;
      case 'dental':
        employerContribution = annualCost * 0.5; // 50% employer contribution
        potentialSavings = 800 * familyMultiplier;
        break;
      case 'vision':
        employerContribution = annualCost * 0.5;
        potentialSavings = 400 * familyMultiplier;
        break;
    }

    const totalValue = employerContribution + potentialSavings;
    const netBenefit = totalValue - annualCost;
    const roiPercentage = annualCost > 0 ? Math.round((netBenefit / annualCost) * 100) : 0;

    return { annualCost, employerContribution, potentialSavings, netBenefit, roiPercentage };
  };

  const roi = calculateROI();

  return (
    <div className={`inline-block ${className}`}>
      <div className="flex items-center gap-2">
        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800">
          <TrendingUp className="w-3 h-3 mr-1" />
          {roi.roiPercentage}% ROI
        </Badge>
        <Badge variant="outline" className="text-xs">
          <DollarSign className="w-3 h-3 mr-1" />
          ${roi.netBenefit.toLocaleString()} benefit
        </Badge>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="h-6 w-6 p-0"
        >
          {showDetails ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
        </Button>
      </div>

      {showDetails && (
        <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg border text-xs space-y-1">
          <div className="flex justify-between">
            <span>You pay annually:</span>
            <span className="font-medium">${roi.annualCost.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span>Employer contributes:</span>
            <span className="font-medium text-green-600">${roi.employerContribution.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span>Potential savings:</span>
            <span className="font-medium text-blue-600">${roi.potentialSavings.toLocaleString()}</span>
          </div>
          <div className="border-t pt-1 flex justify-between font-bold">
            <span>Net benefit:</span>
            <span className="text-green-600">${roi.netBenefit.toLocaleString()}</span>
          </div>
        </div>
      )}
    </div>
  );
};
