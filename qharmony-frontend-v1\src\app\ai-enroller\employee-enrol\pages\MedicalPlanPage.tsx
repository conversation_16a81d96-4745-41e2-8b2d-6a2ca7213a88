'use client';

import React, { useState } from 'react';
import { HiOutlineHeart, HiOutlineCheckCircle } from 'react-icons/hi';
import { RiRobotLine } from 'react-icons/ri';
// import { PlanCard } from '../components/PlanCard';
// import { VideoPlayer } from '../components/VideoPlayer';
// import { PlanComparison } from '../components/PlanComparison';
// import { PlanQADialog } from '../components/PlanQADialog';
// import { UserProfile } from '../components/BenefitsEnrollmentBot';
// import { BotQuestion } from '../components/BotQuestion';

interface MedicalPlanPageProps {
  onNext: () => void;
  onBack: () => void;
  onPlanSelect?: (plan: any) => void;
}

export const MedicalPlanPage: React.FC<MedicalPlanPageProps> = ({
  onNext,
  onBack,
  onPlanSelect
}) => {
  // Mock recommendation data
  const recommendation = {
    reason: "Based on your low healthcare usage and budget preference, this plan offers the best value.",
    plan: { name: 'Kaiser HMO Basic', cost: '85' }
  };
  const medicalPlans = [
    {
      id: 'kaiser-hmo',
      title: 'Kaiser HMO Basic',
      cost: '$85',
      period: '/month',
      features: [
        '$250 deductible',
        '80% coverage after deductible',
        'Integrated care model',
        'Lower monthly cost',
        'Referrals required for specialists'
      ],
      type: 'medical' as const,
      recommended: recommendation?.plan?.name === 'Kaiser HMO Basic'
    },
    {
      id: 'blue-shield-ppo-500',
      title: 'Blue Shield PPO 500',
      cost: '$125',
      period: '/month',
      features: [
        '$500 deductible',
        '90% coverage after deductible',
        'Large provider network',
        'No referrals needed',
        'Out-of-network coverage'
      ],
      type: 'medical' as const,
      recommended: recommendation?.plan?.name === 'Blue Shield PPO 500'
    },
    {
      id: 'blue-shield-premium',
      title: 'Blue Shield PPO Premium',
      cost: '$185',
      period: '/month',
      features: [
        '$250 deductible',
        '95% coverage after deductible',
        'Comprehensive coverage',
        'Specialist access',
        'Premium provider network'
      ],
      type: 'medical' as const,
      recommended: recommendation?.plan?.name === 'Blue Shield PPO Premium'
    }
  ];

  const [selectedPlans, setSelectedPlans] = useState<string[]>([]);

  const handlePlanSelect = (plan: any) => {
    console.log('Plan selected:', plan.id); // Debug log
    // Toggle plan selection for multiple selections
    setSelectedPlans(prev => {
      const isSelected = prev.includes(plan.id);
      const newSelection = isSelected
        ? prev.filter(id => id !== plan.id)
        : [...prev, plan.id];

      console.log('New selection:', newSelection); // Debug log

      // Store selected plans in localStorage
      const selectedPlanData = medicalPlans.filter(p => newSelection.includes(p.id));
      localStorage.setItem('selectedMedicalPlans', JSON.stringify(selectedPlanData));

      // Also call the parent callback with the current plan
      if (!isSelected && onPlanSelect) {
        onPlanSelect(plan);
      }

      return newSelection;
    });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Bot Question */}
      <div style={{ display: 'flex', gap: '16px', marginBottom: '24px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          backgroundColor: '#dbeafe',
          borderRadius: '13px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <RiRobotLine style={{ width: '20px', height: '20px', color: '#2563eb' }} />
        </div>

        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1012px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0,
            fontWeight: '500'
          }}>
            Perfect! Now let&apos;s find your ideal medical plan 🏥
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0',
            fontSize: '14px'
          }}>
            {recommendation ? `${recommendation.reason} Here are your options:` : "Based on your preferences, here are the medical plans available to you:"}
          </p>
        </div>
      </div>

      {/* Recommendation Banner */}
      {recommendation && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
              <HiOutlineHeart className="w-4 h-4 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900 mb-1">🎯 AI Recommendation</h3>
              <p className="text-green-800 text-sm mb-2">{recommendation.reason}</p>
              <div className="text-sm text-green-700">
                <strong>Recommended:</strong> {recommendation.plan.name} - ${recommendation.plan.cost}/month
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Plan Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {medicalPlans.map((plan) => {
          const isSelected = selectedPlans.includes(plan.id);
          return (
            <div
              key={plan.id}
              className={`bg-white rounded-xl p-6 border-2 transition-all hover:shadow-lg ${
                isSelected
                  ? 'ring-2 ring-blue-500 border-blue-500 bg-blue-50'
                  : plan.recommended
                    ? 'ring-2 ring-red-200 border-red-300'
                    : 'border-red-200 hover:border-red-300'
              }`}
            >

            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-50 rounded-lg flex items-center justify-center">
                  <HiOutlineHeart className="w-5 h-5 text-red-500" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{plan.title}</h3>
                  <div className="flex items-baseline gap-1">
                    <span className="text-2xl font-bold text-gray-900">{plan.cost}</span>
                    <span className="text-sm text-gray-500">{plan.period}</span>
                  </div>
                </div>
              </div>

              {plan.recommended && (
                <div className="px-3 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-md">
                  Recommended
                </div>
              )}
            </div>

            {/* Features */}
            <div className="space-y-2 mb-6">
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-2">
                  <HiOutlineCheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-600">{feature}</span>
                </div>
              ))}
            </div>

            {/* Select Button */}
            <button
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                isSelected
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : plan.recommended
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              onClick={(e) => {
                e.stopPropagation();
                handlePlanSelect(plan);
              }}
            >
              {isSelected
                ? '✓ Selected'
                : plan.recommended
                  ? 'Select Recommended Plan'
                  : 'Select This Plan'
              }
            </button>

            {/* AI Insight */}
            {plan.recommended && (
              <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">
                  🤖 <strong>AI Insight:</strong> This plan matches your preferences and provides the best value for your needs.
                </p>
              </div>
            )}
            </div>
          );
        })}
      </div>

      {/* Additional Information */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <HiOutlineHeart className="w-5 h-5 text-red-500" />
          Understanding Medical Plans
        </h3>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">HMO Plans</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Lower monthly premiums</li>
              <li>• Requires primary care physician</li>
              <li>• Referrals needed for specialists</li>
              <li>• Network-only coverage</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">PPO Plans</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• More flexibility in provider choice</li>
              <li>• No referrals required</li>
              <li>• Out-of-network coverage available</li>
              <li>• Higher monthly premiums</li>
            </ul>
          </div>
        </div>

        {/* Helper Tools */}
        <div className="flex gap-2 mt-6 pt-4 border-t">
          <button className="inline-flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
            Ask Questions
          </button>
          <button className="inline-flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
            Watch Video
          </button>
          <button className="inline-flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
            Compare Plans
          </button>
        </div>
      </div>

      {/* Selection Summary */}
      {selectedPlans.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <h3 className="font-semibold text-blue-900 mb-2">Selected Plans ({selectedPlans.length})</h3>
          <div className="space-y-2">
            {selectedPlans.map(planId => {
              const plan = medicalPlans.find(p => p.id === planId);
              return plan ? (
                <div key={planId} className="flex items-center justify-between bg-white rounded-lg p-3">
                  <span className="font-medium">{plan.title}</span>
                  <span className="text-blue-600">{plan.cost}{plan.period}</span>
                </div>
              ) : null;
            })}
          </div>
        </div>
      )}

      {/* Skip Option */}
      <div className="text-center">
        <button
          onClick={() => {
            localStorage.setItem('selectedMedicalPlans', JSON.stringify([]));
            setSelectedPlans([]);
          }}
          className="text-gray-500 hover:text-gray-700 text-sm underline"
        >
          Clear all selections
        </button>
      </div>
    </div>
  );
};
