import React from 'react';
import { HiOutlineOfficeBuilding, HiOutlineLocationMarker, HiOutlineUsers } from 'react-icons/hi';

interface CompanyCardProps {
  company: {
    _id: string;
    companyName: string;
    ein: string;
    location: string;
    companySize: number;
    status: 'active' | 'pending' | 'inactive';
  };
  onManagePlans: (companyId: string) => void;
}

export const CompanyCard: React.FC<CompanyCardProps> = ({ company, onManagePlans }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'inactive':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
      {/* Company Header with Icon and Status */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
              <HiOutlineOfficeBuilding className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{company.companyName}</h3>
            </div>
          </div>
          <span className={`inline-flex items-center px-2.5 py-1 rounded-xl text-xs font-medium border ${getStatusColor(company.status)}`}>
            {company.status}
          </span>
        </div>

        {/* Company Details */}
        <div className="space-y-2">
          <div className="flex items-center text-sm text-gray-600">
            <span className="font-medium">EIN:</span>
            <span className="ml-1">{company.ein}</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <HiOutlineLocationMarker className="w-4 h-4 mr-1 text-gray-400" />
            <span>{company.location}</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <HiOutlineUsers className="w-4 h-4 mr-1 text-gray-400" />
            <span>{company.companySize} employees</span>
          </div>
        </div>
      </div>

      {/* Manage Plans Button */}
      <div className="px-6 pb-6">
        <button
          onClick={() => onManagePlans(company._id)}
          className="w-full bg-black text-white py-3 px-4 rounded-xl hover:bg-gray-800 transition-colors font-medium text-sm"
        >
          Manage Plans
        </button>
      </div>
    </div>
  );
};
