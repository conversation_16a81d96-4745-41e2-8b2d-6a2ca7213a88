"use client";

import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  IconButton,
  CircularProgress,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { RootState } from "@/redux/store";
import { getUserDetails, updateUser } from "@/middleware/company_middleware";

interface EditProfileDialogProps {
  open: boolean;
  onClose: () => void;
}

const EditProfileDialog: React.FC<EditProfileDialogProps> = ({
  open,
  onClose,
}) => {
  const dispatch = useDispatch();
  const userId = useSelector((state: RootState) => state.user._id);
  const userDetails = useSelector((state: RootState) => state.user.userProfile);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [department, setDepartment] = useState("");
  const [title, setTitle] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (userDetails) {
      setFirstName(userDetails.name.split(" ")[0]);
      setLastName(userDetails.name.split(" ")[1]);
      setEmail(userDetails.email);
      setPhoneNumber(userDetails.details?.phoneNumber || "");
      setDepartment(userDetails.details?.department || "");
      setTitle(userDetails.details?.title || "");
    }
  }, [userDetails]);

  const handleUpdateProfile = async () => {
    setLoading(true);
    const updatedUser = {
      name: `${firstName} ${lastName}`,
      email: email,
      phoneNumber: phoneNumber,
      department: department,
      title: title,
    };

    await updateUser(dispatch, userId, updatedUser);
    await getUserDetails(dispatch, userId);
    setLoading(false);
    setSuccessMessage("Profile updated successfully!");

    setTimeout(() => {
      setSuccessMessage("");
      onClose();
    }, 1500);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        style: {
          borderRadius: "16px",
          boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
          padding: "5px",
          width: "550px",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          fontWeight: "bold",
          fontSize: "1.5rem",
        }}
      >
        Edit Profile
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Grid
          container
          spacing={3}
          sx={{ marginBottom: "16px", marginTop: "0px" }}
        >
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="First name"
              variant="outlined"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Last name"
              variant="outlined"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Email"
              variant="outlined"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
              disabled
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Phone number"
              variant="outlined"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Department"
              variant="outlined"
              value={department}
              onChange={(e) => setDepartment(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Title"
              variant="outlined"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
        </Grid>
        {loading && <CircularProgress />}
        {successMessage && (
          <div
            style={{
              color: "green",
              marginTop: "10px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <CheckCircleIcon style={{ marginRight: "5px" }} />
            {successMessage}
          </div>
        )}
      </DialogContent>

      <DialogActions sx={{ padding: "16px" }}>
        <Button
          onClick={handleUpdateProfile}
          sx={{
            color: "#ffffff",
            backgroundColor: "#000000",
            borderRadius: "12px",
            padding: "8px 24px",
            textTransform: "none",
            fontWeight: "bold",
            "&:hover": {
              backgroundColor: "#333333",
            },
          }}
          disabled={loading}
        >
          {loading ? "Saving..." : "Save Changes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditProfileDialog;
