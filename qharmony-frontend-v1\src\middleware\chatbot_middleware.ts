import { AppDispatch } from "../redux/store";
import {
  addMessage,
  ChatMessage,
  setIsLoading,
} from "../redux/reducers/qHarmonyBotSlice";
import { getChatbotUrl } from "../utils/env";

const CHATBOT_URL = getChatbotUrl();

export async function sendChatMessage(
  dispatch: AppDispatch,
  userMessage: string,
  userId: string,
  teamId: string,
) {
  const data = {
    user_id: userId,
    user_message: userMessage,
    team_id: teamId,
  };

  try {
    console.log("Sending chat message:", data);
    const response = await fetch(`${CHATBOT_URL}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.body) {
      throw new Error("Readable stream not supported");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let botMessage: ChatMessage = {
      sender: "bot",
      message: "",
      timestamp: new Date().toISOString(),
    };

    dispatch(setIsLoading(true)); // Show loading indicator

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });

      dispatch(setIsLoading(false)); // Hide loading indicator

      console.log("Chunk:", chunk);

      // Directly append the text chunk to the bot message
      botMessage.message += chunk;
      dispatch(
        addMessage({
          sender: "bot",
          message: chunk,
          timestamp: new Date().toISOString(),
        }),
      );
    }
  } catch (error) {
    console.error("Error sending chat message:", error);
    const errorMessage: ChatMessage = {
      sender: "bot",
      message: "Sorry, I encountered an error while processing your request.",
      timestamp: new Date().toISOString(),
    };
    dispatch(addMessage(errorMessage));
    dispatch(setIsLoading(false)); // Hide loading indicator in case of error
  }
}
