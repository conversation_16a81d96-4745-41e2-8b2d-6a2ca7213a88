'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Play } from 'lucide-react';
import { FloatingHelp } from './FloatingHelp';
import { VideoPlayer } from './VideoPlayer';
import ChatModal from './ChatModal';
import { getEnrollmentDeadline } from '../utils/dateUtils';

interface WelcomePageProps {
  onNext: () => void;
}

export const WelcomePage: React.FC<WelcomePageProps> = ({ onNext }) => {
  const [showHelp, setShowHelp] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '24px',
      fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0
          }}>
            <span style={{ fontWeight: '600' }}>👋 Hi there! Let&apos;s simplify your benefits enrollment.</span>
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0'
          }}>
            I&apos;ll ask a few quick questions and guide you to the best-fit plans — no stress, no guesswork.
          </p>
        </div>
      </div>

      {/* Welcome Card */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        

        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <p style={{
            color: '#374151',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            margin: 0,
            fontWeight: '600',
            fontSize: '16px',
            lineHeight: '1.6',
            fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
          }}>
            🔔 Smarter Benefits Start Now
          </p>

          {/* Deadline Notice */}
          <div style={{
            backgroundColor: '#fdf2f8',
            border: '1px solid #fce7f3',
            borderRadius: '8px',
            padding: '16px'
          }}>
            <p style={{
              color: '#be185d',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              margin: 0,
              fontSize: '14px',
              lineHeight: '1.6',
              fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }}>
              📅 <span style={{ fontWeight: '600' }}>Enrollment Deadline:</span> {getEnrollmentDeadline()}
            </p>
          </div>

          {/* Assistant Features */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <p style={{
              color: '#ec4899',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              margin: 0,
              fontSize: '14px',
              lineHeight: '1.6',
              fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }}>
              🧠 I&apos;m Brea, your AI benefits assistant. Here&apos;s how I&apos;ll help:
            </p>

            <ul style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              marginLeft: '24px',
              listStyle: 'none',
              padding: 0,
              margin: 0
            }}>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
                fontSize: '14px',
                lineHeight: '1.6',
                fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Understand your needs in minutes
              </li>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
                fontSize: '14px',
                lineHeight: '1.6',
                fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Recommend plans tailored to you
              </li>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
                fontSize: '14px',
                lineHeight: '1.6',
                fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Show short, clear explainer videos
              </li>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
                fontSize: '14px',
                lineHeight: '1.6',
                fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Answer your questions on the spot
              </li>
              <li style={{
                color: '#374151',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
                fontSize: '14px',
                lineHeight: '1.6',
                fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
              }}>
                <span style={{ color: '#2563eb', marginTop: '4px' }}>•</span>
                Compare top plan choices side by side
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div style={{ display: 'flex', gap: '12px', paddingTop: '16px' }}>
            <button
              onClick={() => setShowChatModal(true)}
              style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              fontSize: '14px',
              fontWeight: '500',
              fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <span style={{ color: '#2563eb' }}>❓</span>
              Ask Questions
            </button>
            <button
              onClick={() => setShowVideo(true)}
              style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              fontSize: '14px',
              fontWeight: '500',
              fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <Play size={16} style={{ color: '#6b7280' }} />
              Watch Video
            </button>

          </div>

          {/* Start Button */}
          <button
            onClick={onNext}
            style={{
              width: '100%',
              backgroundColor: '#111827',
              color: 'white',
              padding: '12px 24px',
              borderRadius: '8px',
              fontWeight: '600',
              border: 'none',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              marginTop: '24px',
              fontSize: '14px',
              fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#374151'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#111827'}
          >
            🚀 Start My Smart Enrollment
          </button>
        </div>
      </div>

      {/* Floating Help Component */}
      {showHelp && (
        <FloatingHelp onClose={() => setShowHelp(false)} />
      )}

      {/* Video Player Component */}
      {showVideo && (
        <VideoPlayer
          title="Benefits Enrollment Overview"
          description="Learn how to navigate your benefits enrollment process"
          planType="medical"
          onClose={() => setShowVideo(false)}
        />
      )}

      {/* Chat Modal */}
      {showChatModal && (
        <ChatModal
          isOpen={showChatModal}
          onClose={() => setShowChatModal(false)}
        />
      )}

    </div>
  );
};
