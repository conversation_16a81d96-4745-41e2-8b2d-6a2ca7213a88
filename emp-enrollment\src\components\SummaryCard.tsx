import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { UserProfile } from './BenefitsEnrollmentBot';
import { Download, Edit, CheckCircle, DollarSign } from 'lucide-react';

interface SummaryCardProps {
  userProfile: UserProfile;
  onConfirm: () => void;
  onEdit: () => void;
}

export const SummaryCard = ({ userProfile, onConfirm, onEdit }: SummaryCardProps) => {
  // Add debugging to see what's in userProfile
  console.log('SummaryCard - userProfile:', userProfile);
  console.log('SummaryCard - selectedMedical:', userProfile.selectedMedical);
  console.log('SummaryCard - selectedDental:', userProfile.selectedDental);
  console.log('SummaryCard - selectedVision:', userProfile.selectedVision);

  const calculateTotal = () => {
    const medical = userProfile.selectedMedical?.cost || 0;
    const dental = userProfile.selectedDental?.cost || 0;
    const vision = userProfile.selectedVision?.cost || 0;
    const pet = userProfile.selectedPetInsurance?.cost || 0;
    const hospital = userProfile.selectedHospitalIndemnity?.cost || 0;
    const total = medical + dental + vision + pet + hospital;
    console.log('Total calculation:', { medical, dental, vision, pet, hospital, total });
    return total.toFixed(2);
  };

  const calculateEmployerContribution = () => {
    // Employer typically covers 80% of medical, 50% of dental/vision
    const medicalEmployer = (userProfile.selectedMedical?.cost || 0) * 0.8;
    const dentalEmployer = (userProfile.selectedDental?.cost || 0) * 0.5;
    const visionEmployer = (userProfile.selectedVision?.cost || 0) * 0.5;
    // Pet and hospital are typically 100% employee paid
    return (medicalEmployer + dentalEmployer + visionEmployer).toFixed(2);
  };

  const calculateEmployeeTotal = () => {
    const total = parseFloat(calculateTotal());
    const employerContribution = parseFloat(calculateEmployerContribution());
    return (total - employerContribution).toFixed(2);
  };

  const getCoverage = () => {
    switch (userProfile.familyMembers) {
      case 'self': return 'You only';
      case 'spouse': return 'You + Spouse';
      case 'family': return 'You + Family';
      default: return 'Not specified';
    }
  };

  const getEmployerContribution = (planType: string, cost: number) => {
    if (planType === 'medical') return (cost * 0.8).toFixed(2);
    if (planType === 'dental' || planType === 'vision') return (cost * 0.5).toFixed(2);
    return '0.00'; // Pet and hospital are employee-paid
  };

  // Check if we have any selected plans
  const hasSelectedPlans = userProfile.selectedMedical || userProfile.selectedDental || userProfile.selectedVision || userProfile.selectedPetInsurance || userProfile.selectedHospitalIndemnity;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-green-500" />
          Enrollment Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!hasSelectedPlans && (
          <div className="p-4 bg-yellow-50 dark:bg-yellow-950 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              ⚠️ No plans have been selected yet. Please complete your enrollment to see your summary.
            </p>
          </div>
        )}
        
        <div className="space-y-3">
          {userProfile.selectedMedical && (
            <div className="p-3 bg-red-50 dark:bg-red-950 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <p className="font-medium">Medical: {userProfile.selectedMedical.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {userProfile.selectedMedical.type} Plan
                  </p>
                </div>
                <div className="text-right">
                  <Badge variant="secondary">${userProfile.selectedMedical.cost}/paycheck</Badge>
                </div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="flex items-center gap-1">
                  <DollarSign className="w-3 h-3" />
                  Employer pays: ${getEmployerContribution('medical', userProfile.selectedMedical.cost)}
                </span>
                <span>You pay: ${(userProfile.selectedMedical.cost * 0.2).toFixed(2)}</span>
              </div>
            </div>
          )}

          {userProfile.selectedDental && (
            <div className="p-3 bg-green-50 dark:bg-green-950 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <p className="font-medium">Dental: {userProfile.selectedDental.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {userProfile.selectedDental.type} Plan
                  </p>
                </div>
                <div className="text-right">
                  <Badge variant="secondary">${userProfile.selectedDental.cost}/paycheck</Badge>
                </div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="flex items-center gap-1">
                  <DollarSign className="w-3 h-3" />
                  Employer pays: ${getEmployerContribution('dental', userProfile.selectedDental.cost)}
                </span>
                <span>You pay: ${(userProfile.selectedDental.cost * 0.5).toFixed(2)}</span>
              </div>
            </div>
          )}

          {userProfile.selectedVision && (
            <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <p className="font-medium">Vision: {userProfile.selectedVision.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {userProfile.selectedVision.type} Plan
                  </p>
                </div>
                <div className="text-right">
                  <Badge variant="secondary">${userProfile.selectedVision.cost}/paycheck</Badge>
                </div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="flex items-center gap-1">
                  <DollarSign className="w-3 h-3" />
                  Employer pays: ${getEmployerContribution('vision', userProfile.selectedVision.cost)}
                </span>
                <span>You pay: ${(userProfile.selectedVision.cost * 0.5).toFixed(2)}</span>
              </div>
            </div>
          )}

          {userProfile.selectedPetInsurance && userProfile.selectedPetInsurance.name !== 'No Pet Insurance' && (
            <div className="p-3 bg-orange-50 dark:bg-orange-950 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <p className="font-medium">Pet Insurance: {userProfile.selectedPetInsurance.name}</p>
                  <p className="text-sm text-muted-foreground">
                    Additional Protection
                  </p>
                </div>
                <div className="text-right">
                  <Badge variant="secondary">${userProfile.selectedPetInsurance.cost}/paycheck</Badge>
                </div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="flex items-center gap-1">
                  <DollarSign className="w-3 h-3" />
                  Employer pays: $0.00
                </span>
                <span>You pay: ${userProfile.selectedPetInsurance.cost.toFixed(2)}</span>
              </div>
            </div>
          )}

          {userProfile.selectedHospitalIndemnity && userProfile.selectedHospitalIndemnity.name !== 'No Hospital Indemnity' && (
            <div className="p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <p className="font-medium">Hospital Indemnity: {userProfile.selectedHospitalIndemnity.name}</p>
                  <p className="text-sm text-muted-foreground">
                    Extra Financial Protection
                  </p>
                </div>
                <div className="text-right">
                  <Badge variant="secondary">${userProfile.selectedHospitalIndemnity.cost}/paycheck</Badge>
                </div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="flex items-center gap-1">
                  <DollarSign className="w-3 h-3" />
                  Employer pays: $0.00
                </span>
                <span>You pay: ${userProfile.selectedHospitalIndemnity.cost.toFixed(2)}</span>
              </div>
            </div>
          )}
        </div>

        <Separator />

        <div className="space-y-3">
          <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm">Total Plan Cost:</span>
              <span className="font-medium">${calculateTotal()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Employer Contribution:</span>
              <span className="font-medium text-green-600">-${calculateEmployerContribution()}</span>
            </div>
            <Separator />
            <div className="flex justify-between items-center">
              <span className="font-bold">Your Cost per Paycheck:</span>
              <span className="text-xl font-bold text-blue-600">${calculateEmployeeTotal()}</span>
            </div>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Coverage:</span>
            <span className="text-sm">{getCoverage()}</span>
          </div>
        </div>

        <div className="flex gap-2 pt-4">
          <Button variant="outline" onClick={onEdit} className="flex-1">
            <Edit className="w-4 h-4 mr-2" />
            Make Changes
          </Button>
          <Button onClick={onConfirm} className="flex-1">
            <CheckCircle className="w-4 h-4 mr-2" />
            Confirm Enrollment
          </Button>
        </div>

        <Button variant="ghost" className="w-full">
          <Download className="w-4 h-4 mr-2" />
          Download Summary PDF
        </Button>
      </CardContent>
    </Card>
  );
};
