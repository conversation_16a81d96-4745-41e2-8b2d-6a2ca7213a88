/**
 * API Validation Script for Plan Management Endpoints
 * 
 * This script validates the following endpoints:
 * - GET /api/pre-enrollment/plans
 * - GET /api/pre-enrollment/plan-assignments  
 * - GET /api/pre-enrollment/plan-assignments/company/:companyId
 * 
 * Checks:
 * - Data structure consistency
 * - Pagination functionality
 * - Filtering capabilities
 * - Status values returned
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';
const USER_ID = process.env.NEXT_PUBLIC_USER_ID || 'test-user-id';

// Test company ID - replace with actual company ID for testing
const TEST_COMPANY_ID = '6756b8b4e5f4a2b8c9d0e1f2';

/**
 * Make API request with proper headers
 */
async function makeApiRequest(endpoint, params = {}) {
  const url = new URL(`${API_BASE_URL}${endpoint}`);
  
  // Add query parameters
  Object.keys(params).forEach(key => {
    if (params[key] !== undefined && params[key] !== null) {
      url.searchParams.append(key, params[key]);
    }
  });

  console.log(`🔍 Testing: ${url.toString()}`);

  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'user-id': USER_ID,
      },
    });

    const data = await response.json();
    
    return {
      status: response.status,
      ok: response.ok,
      data: data,
      url: url.toString()
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message,
      url: url.toString()
    };
  }
}

/**
 * Validate data structure for plans endpoint
 */
function validatePlansStructure(data) {
  const issues = [];
  
  if (!data.plans || !Array.isArray(data.plans)) {
    issues.push('Missing or invalid plans array');
    return issues;
  }

  // Check first plan structure if available
  if (data.plans.length > 0) {
    const plan = data.plans[0];
    const requiredFields = ['_id', 'planName', 'coverageType', 'status'];
    
    requiredFields.forEach(field => {
      if (!(field in plan)) {
        issues.push(`Missing required field: ${field}`);
      }
    });

    // Check status values
    const validStatuses = ['Active', 'Inactive', 'Draft'];
    if (plan.status && !validStatuses.includes(plan.status)) {
      issues.push(`Invalid status value: ${plan.status}. Expected: ${validStatuses.join(', ')}`);
    }
  }

  return issues;
}

/**
 * Validate data structure for plan assignments endpoint
 */
function validatePlanAssignmentsStructure(data) {
  const issues = [];
  
  if (!data.assignments || !Array.isArray(data.assignments)) {
    issues.push('Missing or invalid assignments array');
    return issues;
  }

  // Check pagination structure
  if (data.totalCount === undefined) {
    issues.push('Missing totalCount field');
  }

  // Check first assignment structure if available
  if (data.assignments.length > 0) {
    const assignment = data.assignments[0];
    const requiredFields = ['_id', 'planId', 'companyId', 'status'];
    
    requiredFields.forEach(field => {
      if (!(field in assignment)) {
        issues.push(`Missing required field: ${field}`);
      }
    });

    // Check status values
    const validStatuses = ['Active', 'Expired', 'Deactivated'];
    if (assignment.status && !validStatuses.includes(assignment.status)) {
      issues.push(`Invalid status value: ${assignment.status}. Expected: ${validStatuses.join(', ')}`);
    }
  }

  return issues;
}

/**
 * Test pagination functionality
 */
async function testPagination(endpoint) {
  console.log(`\n📄 Testing pagination for ${endpoint}`);
  
  // Test page 1 with limit 5
  const page1 = await makeApiRequest(endpoint, { page: 1, limit: 5 });
  
  if (!page1.ok) {
    console.log(`❌ Pagination test failed: ${page1.error || page1.status}`);
    return false;
  }

  const issues = [];
  
  // Check if pagination fields exist
  if (page1.data.totalCount === undefined) {
    issues.push('Missing totalCount in paginated response');
  }
  
  if (page1.data.totalPages === undefined) {
    issues.push('Missing totalPages in paginated response');
  }

  // Check if data is limited correctly
  const dataArray = page1.data.plans || page1.data.assignments || [];
  if (dataArray.length > 5) {
    issues.push(`Expected max 5 items, got ${dataArray.length}`);
  }

  if (issues.length === 0) {
    console.log(`✅ Pagination working correctly`);
    return true;
  } else {
    console.log(`❌ Pagination issues: ${issues.join(', ')}`);
    return false;
  }
}

/**
 * Test filtering functionality
 */
async function testFiltering(endpoint) {
  console.log(`\n🔍 Testing filtering for ${endpoint}`);
  
  // Test status filtering
  const activeFilter = await makeApiRequest(endpoint, { status: 'Active' });
  
  if (!activeFilter.ok) {
    console.log(`❌ Filtering test failed: ${activeFilter.error || activeFilter.status}`);
    return false;
  }

  // Check if all returned items have Active status
  const dataArray = activeFilter.data.plans || activeFilter.data.assignments || [];
  const nonActiveItems = dataArray.filter(item => item.status !== 'Active');
  
  if (nonActiveItems.length > 0) {
    console.log(`❌ Filtering failed: Found ${nonActiveItems.length} non-Active items when filtering for Active`);
    return false;
  }

  console.log(`✅ Filtering working correctly`);
  return true;
}

/**
 * Main validation function
 */
async function validateEndpoints() {
  console.log('🚀 Starting API Validation...\n');
  
  const results = {
    plans: { structure: false, pagination: false, filtering: false },
    assignments: { structure: false, pagination: false, filtering: false },
    companyAssignments: { structure: false, pagination: false, filtering: false }
  };

  // Test 1: GET /api/pre-enrollment/plans
  console.log('📋 Testing Plans Endpoint');
  console.log('=' .repeat(50));
  
  const plansResponse = await makeApiRequest('/api/pre-enrollment/plans');
  if (plansResponse.ok) {
    const structureIssues = validatePlansStructure(plansResponse.data);
    if (structureIssues.length === 0) {
      console.log('✅ Plans structure validation passed');
      results.plans.structure = true;
    } else {
      console.log(`❌ Plans structure issues: ${structureIssues.join(', ')}`);
    }
    
    results.plans.pagination = await testPagination('/api/pre-enrollment/plans');
    results.plans.filtering = await testFiltering('/api/pre-enrollment/plans');
  } else {
    console.log(`❌ Plans endpoint failed: ${plansResponse.error || plansResponse.status}`);
  }

  // Test 2: GET /api/pre-enrollment/plan-assignments
  console.log('\n📋 Testing Plan Assignments Endpoint');
  console.log('=' .repeat(50));
  
  const assignmentsResponse = await makeApiRequest('/api/pre-enrollment/plan-assignments');
  if (assignmentsResponse.ok) {
    const structureIssues = validatePlanAssignmentsStructure(assignmentsResponse.data);
    if (structureIssues.length === 0) {
      console.log('✅ Plan assignments structure validation passed');
      results.assignments.structure = true;
    } else {
      console.log(`❌ Plan assignments structure issues: ${structureIssues.join(', ')}`);
    }
    
    results.assignments.pagination = await testPagination('/api/pre-enrollment/plan-assignments');
    results.assignments.filtering = await testFiltering('/api/pre-enrollment/plan-assignments');
  } else {
    console.log(`❌ Plan assignments endpoint failed: ${assignmentsResponse.error || assignmentsResponse.status}`);
  }

  // Test 3: GET /api/pre-enrollment/plan-assignments/company/:companyId
  console.log('\n📋 Testing Company Plan Assignments Endpoint');
  console.log('=' .repeat(50));
  
  const companyAssignmentsResponse = await makeApiRequest(`/api/pre-enrollment/plan-assignments/company/${TEST_COMPANY_ID}`);
  if (companyAssignmentsResponse.ok) {
    const structureIssues = validatePlanAssignmentsStructure(companyAssignmentsResponse.data);
    if (structureIssues.length === 0) {
      console.log('✅ Company plan assignments structure validation passed');
      results.companyAssignments.structure = true;
    } else {
      console.log(`❌ Company plan assignments structure issues: ${structureIssues.join(', ')}`);
    }
    
    results.companyAssignments.pagination = await testPagination(`/api/pre-enrollment/plan-assignments/company/${TEST_COMPANY_ID}`);
    results.companyAssignments.filtering = await testFiltering(`/api/pre-enrollment/plan-assignments/company/${TEST_COMPANY_ID}`);
  } else {
    console.log(`❌ Company plan assignments endpoint failed: ${companyAssignmentsResponse.error || companyAssignmentsResponse.status}`);
  }

  // Summary
  console.log('\n📊 Validation Summary');
  console.log('=' .repeat(50));
  
  Object.keys(results).forEach(endpoint => {
    const result = results[endpoint];
    const passed = Object.values(result).filter(Boolean).length;
    const total = Object.keys(result).length;
    console.log(`${endpoint}: ${passed}/${total} tests passed`);
    
    Object.keys(result).forEach(test => {
      const status = result[test] ? '✅' : '❌';
      console.log(`  ${status} ${test}`);
    });
  });

  const allPassed = Object.values(results).every(result => 
    Object.values(result).every(Boolean)
  );

  console.log(`\n${allPassed ? '🎉' : '⚠️'} Overall: ${allPassed ? 'All validations passed!' : 'Some validations failed'}`);
  
  return results;
}

// Run validation if this script is executed directly
if (typeof window === 'undefined') {
  validateEndpoints().catch(console.error);
}

module.exports = { validateEndpoints, makeApiRequest };
