import {
  PublicClientApplication,
  Configuration,
  LogLevel,
} from "@azure/msal-browser";

/**
 * Get redirect URI based on environment
 */
function getRedirectUri(): string {
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    if (hostname.includes('test.benosphere.com')) {
      return "https://test.benosphere.com/teams-landing";
    }
  }
  return "https://app.benosphere.com/teams-landing";
}

const msalConfig: Configuration = {
  auth: {
    clientId: "08e8620a-5979-4a37-b279-b2a92a75f515",
    authority:
      "https://login.microsoftonline.com/ca41443d-acdd-4223-9c81-dcaeb58b3406",
    redirectUri: getRedirectUri(), // Dynamic redirect URI based on environment
    navigateToLoginRequestUrl: false,
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: false,
  },
  system: {
    allowRedirectInIframe: true, // Allow authentication flows inside an iframe (important for Teams)
    loggerOptions: {
      loggerCallback: (level, message) => {
        console.log(message); // Verbose logging for debugging
      },
      logLevel: LogLevel.Verbose,
    },
  },
};

const msalInstance = new PublicClientApplication(msalConfig);

// Async function to initialize MSAL
export const initializeMsal = async () => {
  try {
    await msalInstance.initialize();
    console.log("MSAL initialized successfully");
  } catch (error) {
    console.error("Error initializing MSAL", error);
  }
};

export default msalInstance;