"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/plans/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/plans/page.tsx":
/*!********************************************!*\
  !*** ./src/app/ai-enroller/plans/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineDuplicate,HiOutlinePause,HiOutlinePencil,HiOutlinePlay,HiOutlinePlus,HiOutlineQuestionMarkCircle,HiOutlineSearch,HiOutlineTrash,HiOutlineViewGrid,HiOutlineX!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=RiCalendarLine,RiHealthBookLine,RiMoneyDollarCircleLine,RiShieldCheckLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../create-plan/services/planApi */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/services/planApi.ts\");\n/* harmony import */ var _manage_groups_company_companyId_plans_components_CreatePlanForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../manage-groups/company/[companyId]/plans/components/CreatePlanForm */ \"(app-pages-browser)/./src/app/ai-enroller/manage-groups/company/[companyId]/plans/components/CreatePlanForm.tsx\");\n/* harmony import */ var _employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../employee-enrol/components/EnrollmentHeader */ \"(app-pages-browser)/./src/app/ai-enroller/employee-enrol/components/EnrollmentHeader.tsx\");\n/* harmony import */ var _plans_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plans.css */ \"(app-pages-browser)/./src/app/ai-enroller/plans/plans.css\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../utils/env */ \"(app-pages-browser)/./src/utils/env.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// API configuration\nconst API_BASE_URL = (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getApiBaseUrl)();\nconst PlansPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [carrierFilter, setCarrierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [carriers, setCarriers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPlanModal, setShowPlanModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPlan, setEditingPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [planAssignmentCounts, setPlanAssignmentCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Custom modal states\n    const [showConfirmModal, setShowConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmModalData, setConfirmModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAlertModal, setShowAlertModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [alertModalData, setAlertModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInputModal, setShowInputModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inputModalData, setInputModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadPlans();\n    }, []);\n    // Function to fetch assignment counts for all plans\n    const loadPlanAssignmentCounts = async (planIds)=>{\n        try {\n            const counts = {};\n            // Fetch assignment counts for each plan\n            await Promise.all(planIds.map(async (planId)=>{\n                try {\n                    const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments?planId=\").concat(planId), {\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                        }\n                    });\n                    if (response.ok) {\n                        const result = await response.json();\n                        counts[planId] = result.count || 0;\n                    } else {\n                        counts[planId] = 0;\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching assignment count for plan \".concat(planId, \":\"), error);\n                    counts[planId] = 0;\n                }\n            }));\n            setPlanAssignmentCounts(counts);\n        } catch (error) {\n            console.error(\"Error loading plan assignment counts:\", error);\n        }\n    };\n    const loadPlans = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load both plans and carriers\n            const [plansResult, carriersResult] = await Promise.all([\n                (0,_create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__.getPlans)(),\n                (0,_create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__.getCarriers)()\n            ]);\n            if (plansResult.success && plansResult.data) {\n                const plans = plansResult.data.plans;\n                setPlans(plans);\n                // Calculate statistics\n                const totalPlans = plans.length;\n                const activePlans = plans.filter((p)=>p.status === \"Active\").length;\n                const recentPlans = plans.filter((p)=>{\n                    if (!p.createdAt) return false;\n                    const createdDate = new Date(p.createdAt);\n                    const weekAgo = new Date();\n                    weekAgo.setDate(weekAgo.getDate() - 7);\n                    return createdDate > weekAgo;\n                });\n                const plansByStatus = plans.reduce((acc, plan)=>{\n                    const status = plan.status || \"Unknown\";\n                    acc[status] = (acc[status] || 0) + 1;\n                    return acc;\n                }, {});\n                setStats({\n                    totalPlans,\n                    plansByStatus,\n                    recentPlans\n                });\n                // Load assignment counts for all plans\n                const planIds = plans.map((plan)=>plan._id);\n                loadPlanAssignmentCounts(planIds);\n            } else {\n                setError(plansResult.error || \"Failed to load plans\");\n            }\n            // Load carriers for display purposes\n            if (carriersResult.success && carriersResult.data) {\n                setCarriers(carriersResult.data);\n            }\n        } catch (err) {\n            setError(\"Failed to load plans\");\n            console.error(\"Error loading plans:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredPlans = plans.filter((plan)=>{\n        var _plan_planType;\n        const matchesSearch = (plan.planName || \"\").toLowerCase().includes(searchQuery.toLowerCase()) || (plan.description || \"\").toLowerCase().includes(searchQuery.toLowerCase()) || (plan.planCode || \"\").toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesFilter = filterType === \"all\" || ((_plan_planType = plan.planType) === null || _plan_planType === void 0 ? void 0 : _plan_planType.toLowerCase()) === filterType.toLowerCase() || (plan.status || \"\").toLowerCase() === filterType.toLowerCase();\n        const matchesCarrier = carrierFilter === \"all\" || plan.carrierId === carrierFilter;\n        return matchesSearch && matchesFilter && matchesCarrier;\n    });\n    // Pagination logic\n    const totalPages = Math.ceil(filteredPlans.length / itemsPerPage);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    const paginatedPlans = filteredPlans.slice(startIndex, endIndex);\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    const handleClearFilters = ()=>{\n        setSearchQuery(\"\");\n        setFilterType(\"all\");\n        setCarrierFilter(\"all\");\n        setCurrentPage(1);\n    };\n    // Custom modal helpers\n    const showCustomAlert = (title, message, onClose)=>{\n        setAlertModalData({\n            title,\n            message,\n            onClose\n        });\n        setShowAlertModal(true);\n    };\n    const showCustomConfirm = (title, message, onConfirm, onCancel)=>{\n        setConfirmModalData({\n            title,\n            message,\n            onConfirm,\n            onCancel\n        });\n        setShowConfirmModal(true);\n    };\n    const closeAlertModal = ()=>{\n        setShowAlertModal(false);\n        if (alertModalData === null || alertModalData === void 0 ? void 0 : alertModalData.onClose) {\n            alertModalData.onClose();\n        }\n        setAlertModalData(null);\n    };\n    const closeConfirmModal = ()=>{\n        setShowConfirmModal(false);\n        if (confirmModalData === null || confirmModalData === void 0 ? void 0 : confirmModalData.onCancel) {\n            confirmModalData.onCancel();\n        }\n        setConfirmModalData(null);\n    };\n    const confirmAction = ()=>{\n        if (confirmModalData === null || confirmModalData === void 0 ? void 0 : confirmModalData.onConfirm) {\n            confirmModalData.onConfirm();\n        }\n        closeConfirmModal();\n    };\n    const showCustomInput = (title, fields, onSubmit, onCancel)=>{\n        setInputModalData({\n            title,\n            fields,\n            onSubmit,\n            onCancel\n        });\n        setShowInputModal(true);\n    };\n    const closeInputModal = ()=>{\n        setShowInputModal(false);\n        if (inputModalData === null || inputModalData === void 0 ? void 0 : inputModalData.onCancel) {\n            inputModalData.onCancel();\n        }\n        setInputModalData(null);\n    };\n    const handleEditPlan = async (planId)=>{\n        try {\n            // Check if plan can be edited\n            const canEditResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/can-edit\"), {\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (canEditResponse.ok) {\n                const canEditResult = await canEditResponse.json();\n                if (canEditResult.canEdit) {\n                    // Find the plan and open edit modal\n                    const plan = plans.find((p)=>p._id === planId);\n                    if (plan) {\n                        setEditingPlan(plan);\n                        setShowPlanModal(true);\n                    } else {\n                        showCustomAlert(\"Error\", \"Plan not found\");\n                    }\n                } else {\n                    showCustomAlert(\"Cannot Edit Plan\", canEditResult.message);\n                }\n            } else {\n                showCustomAlert(\"Error\", \"Error checking plan editability\");\n            }\n        } catch (error) {\n            console.error(\"Error checking plan editability:\", error);\n            showCustomAlert(\"Error\", \"Error checking plan editability\");\n        }\n    };\n    const handleCopyPlan = async (planId)=>{\n        try {\n            const plan = plans.find((p)=>p._id === planId);\n            if (!plan) {\n                showCustomAlert(\"Error\", \"Plan not found\");\n                return;\n            }\n            // Show custom input modal for plan details\n            showCustomInput(\"Copy Plan\", [\n                {\n                    name: \"planName\",\n                    label: \"Plan Name\",\n                    placeholder: \"Enter name for the copied plan\",\n                    defaultValue: \"\".concat(plan.planName, \" (Copy)\"),\n                    required: true\n                },\n                {\n                    name: \"planCode\",\n                    label: \"Plan Code (Optional)\",\n                    placeholder: \"Enter plan code for the copied plan\",\n                    defaultValue: \"\".concat(plan.planCode || \"\", \"-COPY\"),\n                    required: false\n                }\n            ], async (values)=>{\n                const newPlanName = values.planName;\n                const newPlanCode = values.planCode;\n                try {\n                    // Call duplicate API\n                    const duplicateResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/duplicate\"), {\n                        method: \"POST\",\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)(),\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            planName: newPlanName,\n                            planCode: newPlanCode || undefined\n                        })\n                    });\n                    if (duplicateResponse.ok) {\n                        const result = await duplicateResponse.json();\n                        showCustomAlert(\"Success\", \"Plan copied successfully!\");\n                        loadPlans(); // Reload the plans list\n                    } else {\n                        const errorData = await duplicateResponse.json();\n                        showCustomAlert(\"Error\", \"Error copying plan: \".concat(errorData.error));\n                    }\n                } catch (error) {\n                    console.error(\"Error copying plan:\", error);\n                    showCustomAlert(\"Error\", \"Error copying plan\");\n                }\n            });\n        } catch (error) {\n            console.error(\"Error copying plan:\", error);\n            showCustomAlert(\"Error\", \"Error copying plan\");\n        }\n    };\n    const handleDeletePlan = async (planId)=>{\n        try {\n            // Check if plan can be deleted\n            const canDeleteResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/can-delete\"), {\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (canDeleteResponse.ok) {\n                const canDeleteResult = await canDeleteResponse.json();\n                if (canDeleteResult.canDelete) {\n                    showCustomConfirm(\"Delete Plan\", \"Are you sure you want to delete this plan? This action cannot be undone.\", async ()=>{\n                        try {\n                            const deleteResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId), {\n                                method: \"DELETE\",\n                                headers: {\n                                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                                }\n                            });\n                            if (deleteResponse.ok) {\n                                showCustomAlert(\"Success\", \"Plan deleted successfully!\");\n                                loadPlans(); // Reload the plans list\n                            } else {\n                                const errorData = await deleteResponse.json();\n                                showCustomAlert(\"Error\", \"Error deleting plan: \".concat(errorData.error || \"Unknown error\"));\n                            }\n                        } catch (deleteError) {\n                            console.error(\"Error deleting plan:\", deleteError);\n                            showCustomAlert(\"Error\", \"Error deleting plan. Please try again.\");\n                        }\n                    });\n                } else {\n                    // Show dependencies using correct endpoint\n                    const dependenciesResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/dependent-assignments\"), {\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                        }\n                    });\n                    if (dependenciesResponse.ok) {\n                        var _dependencies_dependentAssignments;\n                        const dependencies = await dependenciesResponse.json();\n                        const assignmentsList = ((_dependencies_dependentAssignments = dependencies.dependentAssignments) === null || _dependencies_dependentAssignments === void 0 ? void 0 : _dependencies_dependentAssignments.map((assignment)=>\"Assignment \".concat(assignment._id)).join(\", \")) || \"Unknown assignments\";\n                        showCustomAlert(\"Cannot Delete Plan\", \"\".concat(canDeleteResult.message, \"\\n\\nThis plan is referenced by \").concat(dependencies.count, \" assignment(s):\\n\").concat(assignmentsList));\n                    } else {\n                        showCustomAlert(\"Cannot Delete Plan\", canDeleteResult.message);\n                    }\n                }\n            } else {\n                showCustomAlert(\"Error\", \"Error checking plan dependencies\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting plan:\", error);\n            showCustomAlert(\"Error\", \"Error deleting plan\");\n        }\n    };\n    const handleActivatePlan = async (planId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/activate\"), {\n                method: \"POST\",\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (response.ok) {\n                showCustomAlert(\"Success\", \"Plan activated successfully!\");\n                loadPlans(); // Reload the plans list\n            } else {\n                const errorData = await response.json();\n                showCustomAlert(\"Error\", \"Error activating plan: \".concat(errorData.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error activating plan:\", error);\n            showCustomAlert(\"Error\", \"Error activating plan. Please try again.\");\n        }\n    };\n    const handleDeactivatePlan = async (planId)=>{\n        try {\n            showCustomConfirm(\"Convert to Draft\", \"Are you sure you want to convert this plan to draft? It will no longer be available for new assignments.\", async ()=>{\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/convert-to-draft\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                    }\n                });\n                if (response.ok) {\n                    showCustomAlert(\"Success\", \"Plan converted to draft successfully!\");\n                    loadPlans(); // Reload the plans list\n                } else {\n                    const errorData = await response.json();\n                    showCustomAlert(\"Error\", \"Error converting plan to draft: \".concat(errorData.error || \"Unknown error\"));\n                }\n            });\n        } catch (error) {\n            console.error(\"Error converting plan to draft:\", error);\n            showCustomAlert(\"Error\", \"Error converting plan to draft. Please try again.\");\n        }\n    };\n    // Helper function to get carrier name by ID\n    const getCarrierName = (carrierId)=>{\n        const carrier = carriers.find((c)=>c._id === carrierId);\n        return carrier ? carrier.carrierName : \"Unknown Carrier\";\n    };\n    // Handle plan modal submission\n    const handlePlanSubmit = (plan)=>{\n        setShowPlanModal(false);\n        setEditingPlan(null);\n        loadPlans(); // Reload plans list (this will also reload assignment counts)\n    };\n    // Handle plan modal cancel\n    const handlePlanCancel = ()=>{\n        setShowPlanModal(false);\n        setEditingPlan(null);\n    };\n    const headerActions = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"create-btn\",\n        onClick: ()=>{\n            setEditingPlan(null);\n            setShowPlanModal(true);\n        },\n        style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"8px\",\n            padding: \"10px 16px\",\n            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"8px\",\n            fontSize: \"14px\",\n            fontWeight: \"500\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                size: 16\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, undefined),\n            \"Create Plan\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n        lineNumber: 491,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"plans-wrapper\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: \"white\",\n                        padding: \"24px 0\",\n                        borderBottom: \"1px solid #E5E7EB\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            maxWidth: \"95%\",\n                            margin: \"0 auto\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\",\n                            padding: \"0 2%\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n                                            borderRadius: \"8px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HiOutlineClipboardList, {\n                                            style: {\n                                                width: \"18px\",\n                                                height: \"18px\",\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"600\",\n                                                    color: \"#111827\",\n                                                    margin: 0\n                                                },\n                                                children: \"Plan Management\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: \"#6B7280\",\n                                                    margin: 0\n                                                },\n                                                children: \"Manage and view all insurance plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"white\",\n                                            border: \"1px solid #D1D5DB\",\n                                            borderRadius: \"8px\",\n                                            color: \"#374151\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineQuestionMarkCircle, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Ask Questions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"white\",\n                                            border: \"1px solid #D1D5DB\",\n                                            borderRadius: \"8px\",\n                                            color: \"#374151\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineViewGrid, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPlanModal(true),\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"8px\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create New Plan\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"plans-page\",\n                    children: [\n                        stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"grid\",\n                                gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n                                gap: \"16px\",\n                                maxWidth: \"95%\",\n                                margin: \"24px auto\",\n                                padding: \"0 2%\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#EFF6FF\",\n                                        border: \"1px solid #DBEAFE\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#2563EB\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Total Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#1E40AF\"\n                                                    },\n                                                    children: stats.totalPlans\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#2563EB\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiHealthBookLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#F0FDF4\",\n                                        border: \"1px solid #BBF7D0\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#16A34A\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Active Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#15803D\"\n                                                    },\n                                                    children: stats.plansByStatus.Active || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#16A34A\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiCalendarLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#FEF3C7\",\n                                        border: \"1px solid #FDE68A\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#D97706\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Recent Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#B45309\"\n                                                    },\n                                                    children: stats.recentPlans.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#D97706\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiMoneyDollarCircleLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"search-filter-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"filter-icon\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineSearch, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Search & Filter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 11\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 9\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-controls\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search by plan name, code, or carrier type...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"search-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"status-filter\",\n                                            value: filterType,\n                                            onChange: (e)=>setFilterType(e.target.value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"active\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"inactive\",\n                                                    children: \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"draft\",\n                                                    children: \"Draft\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 749,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"template\",\n                                                    children: \"Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"archived\",\n                                                    children: \"Archived\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 13\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"carrier-filter\",\n                                            value: carrierFilter,\n                                            onChange: (e)=>setCarrierFilter(e.target.value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Carriers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                carriers.map((carrier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: carrier._id,\n                                                        children: carrier.carrierName\n                                                    }, carrier._id, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 15\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"clear-filters-btn\",\n                                            onClick: handleClearFilters,\n                                            children: \"Clear Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 11\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 9\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"results-count\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredPlans.length,\n                                        \" of \",\n                                        plans.length,\n                                        \" plans\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 9\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 7\n                        }, undefined),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-state\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-spinner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading plans...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 779,\n                            columnNumber: 9\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"error-state\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Error: \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadPlans,\n                                    className: \"retry-btn\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 787,\n                            columnNumber: 9\n                        }, undefined),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"plans-table-container\",\n                            children: filteredPlans.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"empty-state\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiShieldCheckLine, {\n                                        size: 48\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 800,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"No Plans Found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: plans.length === 0 ? \"You haven't created any plans yet. Create your first plan to get started.\" : \"No plans match your search criteria. Try adjusting your filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"create-first-plan-btn\",\n                                        onClick: ()=>router.push(\"/ai-enroller/create-plan\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create Your First Plan\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 799,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"table-header\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"Plans List\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"table-wrapper\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"plans-table\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Plan Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Plan Code\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Coverage Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Groups\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: paginatedPlans.map((plan)=>{\n                                                        var _this, _plan_coverageSubTypes, _plan_coverageSubTypes1;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"plan-name-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"plan-name\",\n                                                                        children: plan.planName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 838,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"plan-code-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"plan-code-badge\",\n                                                                        children: plan.planCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 841,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"carrier-type-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"carrier-type-badge \".concat((_this = ((_plan_coverageSubTypes = plan.coverageSubTypes) === null || _plan_coverageSubTypes === void 0 ? void 0 : _plan_coverageSubTypes[0]) || plan.coverageType) === null || _this === void 0 ? void 0 : _this.toLowerCase().replace(\" \", \"-\")),\n                                                                        children: ((_plan_coverageSubTypes1 = plan.coverageSubTypes) === null || _plan_coverageSubTypes1 === void 0 ? void 0 : _plan_coverageSubTypes1[0]) || plan.coverageType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 844,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"status-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"status-badge \".concat((plan.status || \"unknown\").toLowerCase()),\n                                                                        children: plan.status || \"Unknown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 849,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"groups-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"groups-count\",\n                                                                        children: planAssignmentCounts[plan._id] !== undefined ? planAssignmentCounts[plan._id] : \"...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 854,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"actions-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"action-buttons\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn edit\",\n                                                                                onClick: ()=>handleEditPlan(plan._id),\n                                                                                title: \"Edit Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePencil, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 865,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 860,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn copy\",\n                                                                                onClick: ()=>handleCopyPlan(plan._id),\n                                                                                title: \"Copy Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineDuplicate, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 872,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 867,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            plan.status === \"Active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn deactivate\",\n                                                                                onClick: ()=>handleDeactivatePlan(plan._id),\n                                                                                title: \"Convert to Draft\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePause, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 880,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 875,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn activate\",\n                                                                                onClick: ()=>handleActivatePlan(plan._id),\n                                                                                title: \"Activate Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlay, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 888,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 883,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn delete\",\n                                                                                onClick: ()=>handleDeletePlan(plan._id),\n                                                                                title: \"Delete Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineTrash, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 896,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 891,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 859,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, plan._id, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pagination-container\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pagination-info\",\n                                                children: [\n                                                    \"Showing \",\n                                                    startIndex + 1,\n                                                    \"-\",\n                                                    Math.min(endIndex, filteredPlans.length),\n                                                    \" of \",\n                                                    filteredPlans.length,\n                                                    \" plans\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 909,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pagination-controls\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"pagination-btn\",\n                                                        onClick: ()=>handlePageChange(currentPage - 1),\n                                                        disabled: currentPage === 1,\n                                                        children: \"Previous\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    Array.from({\n                                                        length: totalPages\n                                                    }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"pagination-btn \".concat(page === currentPage ? \"active\" : \"\"),\n                                                            onClick: ()=>handlePageChange(page),\n                                                            children: page\n                                                        }, page, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 23\n                                                        }, undefined)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"pagination-btn\",\n                                                        onClick: ()=>handlePageChange(currentPage + 1),\n                                                        disabled: currentPage === totalPages,\n                                                        children: \"Next\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 797,\n                            columnNumber: 9\n                        }, undefined),\n                        showPlanModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: handlePlanCancel,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content plan-modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: editingPlan ? \"Edit Plan\" : \"Create New Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: handlePlanCancel,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 950,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_manage_groups_company_companyId_plans_components_CreatePlanForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            initialData: editingPlan,\n                                            onSubmit: handlePlanSubmit,\n                                            onCancel: handlePlanCancel,\n                                            isModal: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 954,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 947,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 946,\n                            columnNumber: 9\n                        }, undefined),\n                        showAlertModal && alertModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeAlertModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: alertModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 971,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeAlertModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 973,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 972,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                whiteSpace: \"pre-line\"\n                                            },\n                                            children: alertModalData.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 976,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"modal-btn primary\",\n                                            onClick: closeAlertModal,\n                                            children: \"OK\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 969,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 968,\n                            columnNumber: 9\n                        }, undefined),\n                        showConfirmModal && confirmModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeConfirmModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: confirmModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 993,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeConfirmModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 994,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 992,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                whiteSpace: \"pre-line\"\n                                            },\n                                            children: confirmModalData.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-btn secondary\",\n                                                onClick: closeConfirmModal,\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-btn primary\",\n                                                onClick: confirmAction,\n                                                children: \"Confirm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1005,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 990,\n                            columnNumber: 9\n                        }, undefined),\n                        showInputModal && inputModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeInputModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: inputModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeInputModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1020,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1017,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: (e)=>{\n                                            e.preventDefault();\n                                            const formData = new FormData(e.target);\n                                            const values = {};\n                                            inputModalData.fields.forEach((field)=>{\n                                                values[field.name] = formData.get(field.name) || \"\";\n                                            });\n                                            inputModalData.onSubmit(values);\n                                            closeInputModal();\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-body\",\n                                                children: inputModalData.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-group\",\n                                                        style: {\n                                                            marginBottom: \"1rem\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: field.name,\n                                                                style: {\n                                                                    display: \"block\",\n                                                                    marginBottom: \"0.5rem\",\n                                                                    fontSize: \"14px\",\n                                                                    lineHeight: \"21px\",\n                                                                    fontWeight: \"500\",\n                                                                    color: \"#374151\"\n                                                                },\n                                                                children: [\n                                                                    field.label,\n                                                                    field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: \"#dc2626\"\n                                                                        },\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 1045,\n                                                                        columnNumber: 42\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: field.name,\n                                                                name: field.name,\n                                                                placeholder: field.placeholder,\n                                                                defaultValue: field.defaultValue,\n                                                                required: field.required,\n                                                                style: {\n                                                                    width: \"100%\",\n                                                                    padding: \"0.75rem\",\n                                                                    border: \"1px solid #d1d5db\",\n                                                                    borderRadius: \"0.5rem\",\n                                                                    fontSize: \"14px\",\n                                                                    lineHeight: \"21px\",\n                                                                    fontFamily: \"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, field.name, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-footer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"modal-btn secondary\",\n                                                        onClick: closeInputModal,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1068,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        className: \"modal-btn primary\",\n                                                        children: \"Submit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1071,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1067,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1016,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1015,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n            lineNumber: 515,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n        lineNumber: 514,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"rj+iLjGTAPCU1ONVJi170qf1XYk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = PlansPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/plans/page.tsx\n"));

/***/ })

});