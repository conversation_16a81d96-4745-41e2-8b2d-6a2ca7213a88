"use client";

import CompanyDetails from "@/app/dashboard/CompanyDetails";
import UserDetails from "@/app/dashboard/UserDetails";
import ProtectedRoute from "@/components/ProtectedRoute";
import { Box, Button } from "@mui/material";
import { useEffect, useState } from "react";
import withMobileEdgeFill from "@/components/mobile_edge_fill";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { openDrawer } from "@/redux/reducers/mobileSidebarSlice";

const Dashboard = () => {
  const router = useRouter();
  const dispatch = useDispatch();

  const [isTeamsApp, setIsTeamsApp] = useState(false);

  useEffect(() => {
    const isTeamsApp = localStorage.getItem("isTeamsApp1");
    setIsTeamsApp(isTeamsApp === "true");
  }, []);

  return (
    <ProtectedRoute>
      <Box
        sx={{
          height: "100%",
          width: "100vw",
          bgcolor: "#f6f8fc",
          display: "flex",
          flexDirection: "column",
          alignItems: "center", // Center items horizontally
        }}
      >
        <UserDetails />
        <CompanyDetails />

        <Box sx={{ width: "100%", mt: 2 }}></Box>
        <Button
          variant="contained"
          onClick={() => dispatch(openDrawer())}
          sx={{
            textTransform: "none",
            borderRadius: "8px",
            bgcolor: "rgba(0, 0, 0, 0.06)",
            color: "black",
            boxShadow: "none",
            width: "90%",
            paddingY: "10px",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
              boxShadow: "none",
            },
          }}
        >
          View Benefits
        </Button>
        <Box sx={{ width: "100%", mt: 2 }}></Box>
        {!isTeamsApp && (
          <Button
            variant="contained"
            onClick={() => router.push("/qHarmonyBot")}
            sx={{
              textTransform: "none",
              borderRadius: "8px",
              bgcolor: "rgba(0, 0, 0, 0.06)",
              color: "black",
              boxShadow: "none",
              width: "90%",
              paddingY: "10px",
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
                boxShadow: "none",
              },
              mb: 10,
            }}
          >
            Chat with Brea
          </Button>
        )}
      </Box>
    </ProtectedRoute>
  );
};

export default withMobileEdgeFill(Dashboard);
