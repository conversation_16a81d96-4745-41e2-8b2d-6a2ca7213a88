import mongoose, { Document, Model } from 'mongoose';

const { Schema } = mongoose;

export interface UserDataInterface {
  _id?: mongoose.Types.ObjectId;
  name: string;
  email: string;
  role: string;
  companyId: string;
  isAdmin: boolean;
  isBroker: boolean;
  isActivated: boolean;
  isDisabled: boolean;
  groupIds?: string[];
  details?: {
    phoneNumber: string;
    department: string;
    title: string;
    role: string;
  };
}

interface UserDocument extends Document {
  data: UserDataInterface[];
}

class UserModelClass {
  private static userModel: Model<UserDocument>;

  public static initializeModel() {
    const schema = new Schema({
      name: String,
      email: String,
      role: String,
      companyId: String,
      isAdmin: Boolean,
      isBroker: Boolean,
      isActivated: Boolean,
      isDisabled: Boolean,
      groupIds: { type: [String], default: [] },
      details: {
        phoneNumber: String,
        department: String,
        title: String,
        role: String,
      },
    });

    this.userModel = mongoose.model<UserDocument>('User', schema);
  }

  public static async addData(data: UserDataInterface): Promise<
    | (mongoose.Document<unknown, {}, UserDocument> &
      UserDocument & {
        _id: mongoose.Types.ObjectId;
      })
    | null>
    {
    try {
      // Use type assertion to fix the TypeScript error
      const createdUser = await this.userModel.create(data);
      return createdUser as mongoose.Document<unknown, {}, UserDocument> &
        UserDocument & {
          _id: mongoose.Types.ObjectId;
        };
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  public static async getData(): Promise<UserDataInterface[]> {
    try {
      const data = (await this.userModel.find()) as UserDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async getDataById(id: string): Promise<UserDataInterface> {
    try {
      const data = (await this.userModel.findById(id)) as UserDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return {} as UserDataInterface;
    }
  }

  public static async getDataByCompanyId(
    id: string
  ): Promise<UserDataInterface[]> {
    try {
      const data = (await this.userModel.find({
        companyId: id,
      })) as UserDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async getDataByEmail({
    email,
  }: {
    email: string;
  }): Promise<UserDataInterface> {
    try {
      const data = (await this.userModel.findOne({
        email,
      })) as UserDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return {} as UserDataInterface;
    }
  }

  public static async updateData(
    id: string,
    data: Partial<UserDataInterface>
  ): Promise<void> {
    try {
      // make sure you update only the fields that are present in the data object
      await this.userModel.findByIdAndUpdate(id, data);
    } catch (error) {
      console.error(error);
    }
  }

  public static async getAllData(): Promise<UserDataInterface[]> {
    try {
      const data = (await this.userModel.find()) as UserDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async addGroupToUsers(userIds: string[], groupId: string): Promise<void> {
    try {
      await this.userModel.updateMany(
        { _id: { $in: userIds } },
        { $addToSet: { groupIds: groupId } } // Ensures groupIds is unique
      );
    } catch (error) {
      console.error("Error adding group to users:", error);
    }
  }

  public static async removeGroupFromUsers(userIds: string[], groupId: string): Promise<void> {
    try {
      await this.userModel.updateMany(
        { _id: { $in: userIds } },
        { $pull: { groupIds: groupId } } // Removes the groupId from users
      );
    } catch (error) {
      console.error("Error removing group from users:", error);
    }
  }

  public static async getUsersByIds(userIds: string[]): Promise<UserDataInterface[]> {
    try {
      const users = await this.userModel.find({ _id: { $in: userIds } }).lean();
      return users as unknown as UserDataInterface[];
    } catch (error) {
      console.error("Error fetching users by IDs:", error);
      return [];
    }
  }

  public static async getUsersByCompanyIdsWithTenantId(companyIds: string[]): Promise<{ email: string; companyName: string; tenantId: string }[]> {
    try {
      const users = await this.userModel.aggregate([
        {
          $match: {
            companyId: { $in: companyIds },
            isDisabled: false
          }
        },
        {
          $addFields: {
            companyIdObject: { $toObjectId: '$companyId' } // Convert companyId to ObjectId
          }
        },
        {
          $lookup: {
            from: 'companies', // The name of the companies collection
            localField: 'companyIdObject', // Use the converted ObjectId field
            foreignField: '_id', // Field in the companies collection
            as: 'companyData' // The name of the array field to store the joined data
          }
        },
        {
          $unwind: {
            path: '$companyData',
            preserveNullAndEmptyArrays: true // In case no matching company is found
          }
        },
        {
          $addFields: {
            tenantId: '$companyData.tenantId', // Add tenantId from the companyData to the user object
            companyName: '$companyData.name' // Add companyName from the companyData to the user object
          }
        },
        {
          $project: {
            _id: 0,
            email: 1, // Include only the email field
            tenantId: 1, // Include only the tenantId field
            companyName: 1 // Include companyName
          }
        }
      ]) as unknown as { email: string; companyName: string; tenantId: string }[]; // Explicitly cast to the desired return type

      return users;
    } catch (error) {
      console.error("Error fetching users by company IDs with tenantId:", error);
      return [];
    }
  }

  public static async getUsersByIdsWithTenantId(userIds: string[]): Promise<{ email: string; companyName: string; tenantId: string }[]> {
    try {
      const users = await this.userModel.aggregate([
        {
          $match: {
            _id: { $in: userIds.map(id => new mongoose.Types.ObjectId(id)) }, // Convert userIds to ObjectId
            isDisabled: false
          }
        },
        {
          $addFields: {
            companyIdObject: { $toObjectId: '$companyId' } // Convert companyId to ObjectId
          }
        },
        {
          $lookup: {
            from: 'companies', // The name of the companies collection
            localField: 'companyIdObject', // Use the converted ObjectId field
            foreignField: '_id', // Field in the companies collection
            as: 'companyData' // The name of the array field to store the joined data
          }
        },
        {
          $unwind: {
            path: '$companyData',
            preserveNullAndEmptyArrays: true // In case no matching company is found
          }
        },
        {
          $addFields: {
            tenantId: '$companyData.tenantId', // Add tenantId from the companyData to the user object
            companyName: '$companyData.name' // Add companyName from the companyData to the user object
          }
        },
        {
          $project: {
            _id: 0,
            email: 1, // Include only the email field
            tenantId: 1, // Include only the tenantId field
            companyName: 1 // Include companyName
          }
        }
      ]) as unknown as { email: string; companyName: string; tenantId: string }[]; // Explicitly cast to the desired return type

      return users;
    } catch (error) {
      console.error("Error fetching users by user IDs with tenantId:", error);
      return [];
    }
  }
}
UserModelClass.initializeModel();

export default UserModelClass;
