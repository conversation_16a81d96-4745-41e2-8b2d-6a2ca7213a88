import mongoose, { Document, Model } from "mongoose";

const { Schema } = mongoose;

export interface OtpDataInterface {
  email: string;
  otp: string;
  expiryInEpochSecs: number;
}

interface OtpDocument extends Document {
  data: OtpDataInterface[];
}

class OtpModelClass {
  private static otpModel: Model<OtpDocument>;

  public static initializeModel() {
    const schema = new Schema({
      email: String,
      otp: String,
      expiryInEpochSecs: Number,
    });

    this.otpModel = mongoose.model<OtpDocument>("Otp", schema);
  }

  public static async addData(data: OtpDataInterface): Promise<void> {
    try {
      await this.otpModel.create(data);
    } catch (error) {
      console.error(error);
    }
  }

  public static async getData(): Promise<OtpDataInterface[]> {
    try {
      const data = await this.otpModel.find() as OtpDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async getDataById({id}: {id: string}): Promise<OtpDataInterface> {
    try {
      const data = await this.otpModel.findById (id) as OtpDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return {} as OtpDataInterface;
    }
  }

  public static async getDataByEmail({email}: {email: string}): Promise<OtpDataInterface> {
    try {
      const data = await this
        .otpModel
        .findOne({ email }) as OtpDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return {} as OtpDataInterface;
    }
  }

  public static async deleteDataByEmail({email}: {email: string}): Promise<void> {
    try {
      await this.otpModel.deleteMany({ email });
    } catch (error) {
      console.error(error);
    }
  }

  public static async updateData({id, data}: {id: string, data: Partial<OtpDataInterface>}): Promise<void> {
    try {
      // make sure you update only the fields that are present in the data object
      await this.otpModel.findByIdAndUpdate(id, data);
    } catch (error) {
      console.error(error);
    }
  }
}

OtpModelClass.initializeModel();

export default OtpModelClass;