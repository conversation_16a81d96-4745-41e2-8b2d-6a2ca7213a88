'use client';

import React from 'react';
import Image from 'next/image';

interface ChatMessageProps {
  sender: 'bot' | 'user';
  content: React.ReactNode;
  timestamp: Date;
}

export const ChatMessage = ({ sender, content, timestamp }: ChatMessageProps) => {
  const isBot = sender === 'bot';

  return (
    <div style={{
      display: 'flex',
      gap: '12px',
      justifyContent: isBot ? 'flex-start' : 'flex-end',
      marginBottom: '16px'
    }}>
      {isBot && (
        <div style={{
          width: '32px',
          height: '32px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={32}
            height={32}
            style={{ borderRadius: '50%' }}
          />
        </div>
      )}
      
      <div style={{
        maxWidth: '80%',
        order: isBot ? 2 : 1
      }}>
        <div style={{
          borderRadius: '16px',
          padding: '12px 16px',
          backgroundColor: isBot ? '#f9fafb' : '#3b82f6',
          color: isBot ? '#374151' : 'white',
          border: isBot ? '1px solid #e5e7eb' : 'none'
        }}>
          {content}
        </div>
        <div style={{
          fontSize: '12px',
          color: '#6b7280',
          marginTop: '4px',
          textAlign: isBot ? 'left' : 'right'
        }}>
          {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      </div>

      {!isBot && (
        <div style={{
          width: '32px',
          height: '32px',
          borderRadius: '50%',
          backgroundColor: '#10b981',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <span style={{ color: 'white', fontSize: '14px', fontWeight: '600' }}>U</span>
        </div>
      )}
    </div>
  );
};
