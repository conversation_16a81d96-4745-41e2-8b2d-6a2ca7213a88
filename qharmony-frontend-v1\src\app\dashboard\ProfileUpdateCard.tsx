import React, { useState } from "react";
import { <PERSON>, Typo<PERSON>, Avatar, Chip } from "@mui/material";
import { User<PERSON>he<PERSON>, CheckCircle } from "lucide-react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import EnhancedEditProfileDialog from "./enhanced_edit_user_profile_popup";

export default function ProfileUpdateCard() {
  const [editProfileDialogOpen, setEditProfileDialogOpen] = useState(false);
  const userDetails = useSelector((state: RootState) => state.user.userProfile);

  // Calculate profile completion percentage
  const calculateProfileCompletion = () => {
    const requiredFields = [
      userDetails.name,
      userDetails.email,
      userDetails.details?.phoneNumber,
      userDetails.details?.department,
      userDetails.details?.title,
    ];

    const optionalFields = [
      userDetails.details?.dateOfBirth,
      userDetails.details?.hireDate,
      userDetails.details?.address?.street1,
      userDetails.details?.emergencyContact?.name,
    ];

    const completedRequired = requiredFields.filter(field => field && field.trim() !== "").length;
    const completedOptional = optionalFields.filter(field => field && field.toString().trim() !== "").length;
    
    const totalFields = requiredFields.length + optionalFields.length;
    const completedFields = completedRequired + completedOptional;
    
    return {
      percentage: Math.round((completedFields / totalFields) * 100),
      isComplete: completedRequired === requiredFields.length && completedOptional >= 2, // At least 2 optional fields
      missingRequired: requiredFields.length - completedRequired,
    };
  };

  const profileStatus = calculateProfileCompletion();
  const isUrgent = profileStatus.missingRequired > 0 || profileStatus.percentage < 70;

  const handleClick = () => {
    setEditProfileDialogOpen(true);
  };

  const handleEditDialogClose = () => {
    setEditProfileDialogOpen(false);
  };

  const getStatusColor = () => {
    if (profileStatus.percentage >= 90) return "success";
    if (profileStatus.percentage >= 70) return "warning";
    return "error";
  };

  const getStatusText = () => {
    if (profileStatus.percentage >= 90) return "Complete";
    if (profileStatus.percentage >= 70) return "Almost Done";
    return "Needs Attention";
  };

  const getUrgencyLevel = () => {
    if (profileStatus.missingRequired > 0) return "High Priority";
    if (profileStatus.percentage < 50) return "Medium Priority";
    return "Low Priority";
  };

  return (
    <>
      <Box
        onClick={handleClick}
        sx={{
          backgroundColor: "white",
          padding: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          borderRadius: "30px",
          boxShadow: "none",
          maxWidth: "100%",
          mt: 3,
          cursor: "pointer",
          transition: "all 0.2s ease",
          "&:hover": {
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
            transform: "translateY(-2px)",
          },
        }}
      >
        {/* Left side with Avatar */}
        <Box sx={{ display: "flex", alignItems: "center", flexDirection: "row" }}>
          <Avatar
            sx={{
              width: 50,
              height: 50,
              mr: 2,
              backgroundColor: "#8b5cf6",
              background: "linear-gradient(135deg, #8b5cf6 10%, #a78bfa 100%)",
            }}
          >
            <UserCheck size={28} color="white" />
          </Avatar>
          <Box>
            <Box
              sx={{ display: "flex", alignItems: "center", flexDirection: "row", gap: 1 }}
            >
              {/* Profile Update Text */}
              <Typography
                sx={{
                  fontWeight: 700,
                  fontSize: "24px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                Update Profile
              </Typography>
              
              {/* Status Chip */}
              <Chip
                label={getStatusText()}
                color={getStatusColor()}
                size="small"
                sx={{
                  fontWeight: 600,
                  fontSize: "0.75rem",
                }}
              />
            </Box>
            
            <Typography
              sx={{
                fontWeight: 500,
                fontSize: "14px",
                color: "#6c757d",
              }}
            >
              Complete your profile information and add dependents
            </Typography>
          </Box>
        </Box>

        {/* Right side with status indicator */}
        <Box sx={{ display: "flex", alignItems: "center" }}>
          {isUrgent && (
            <Chip
              label="URGENT"
              size="small"
              sx={{
                backgroundColor: "#ff6b6b",
                color: "white",
                fontWeight: 700,
                fontSize: "0.75rem",
                height: "24px",
              }}
            />
          )}
        </Box>
      </Box>

      {/* Enhanced Edit Profile Dialog */}
      <EnhancedEditProfileDialog
        open={editProfileDialogOpen}
        onClose={handleEditDialogClose}
      />


    </>
  );
}
