import { getApiBaseUrl, getUserId } from '../../../../utils/env';

// API configuration
const API_BASE_URL = getApiBaseUrl();

const getHeaders = () => ({
  'Content-Type': 'application/json',
  'user-id': getUserId(),
});

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface PlanAssignment {
  _id: string;
  planId: string | {
    _id: string;
    planName: string;
    planCode?: string;
    coverageType: string;
    coverageSubTypes: string[];
    planType: string;
    metalTier?: string;
    carrierId?: string;
    carrier?: {
      carrierName: string;
    };
  };
  companyId: string;
  groupNumber?: string;
  assignmentYear: number;
  planEffectiveDate: string;
  planEndDate: string;
  enrollmentStartDate: string;
  enrollmentEndDate: string;
  isActive: boolean;
  status: string;
  coverageTiers?: Array<{
    tierName: string;
    totalCost: number;
    employerCost: number;
    employeeCost: number;
  }>;
  plan?: {
    _id: string;
    planName: string;
    planCode?: string;
    coverageType: string;
    coverageSubTypes: string[];
    planType: string;
    metalTier?: string;
    carrierId?: string;
    carrier?: {
      carrierName: string;
    };
  };
  // New optimized API fields
  planData?: {
    _id: string;
    planName: string;
    planCode?: string;
    coverageType: string;
    coverageSubTypes: string[];
    planType: string;
    metalTier?: string;
    carrierId?: string;
    carrierName?: string;
    status: string;
    isTemplate: boolean;
    brokerId?: string;
    description?: string;
    planDetails?: {
      deductible?: any;
      outOfPocketMax?: any;
    };
  };
  carrierData?: {
    _id: string;
    carrierName: string;
    carrierCode: string;
    status: string;
    isActive: boolean;
  };
}

export interface PlanAssignmentsResponse {
  assignments: PlanAssignment[];
  count: number;
  message?: string;
  canCreateAssignments?: boolean;
  accessDeniedToExisting?: boolean;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  userRole?: string;
  appliedFilters?: string[];
  expiryInfo?: any;
}

export interface PlanAssignmentFilters {
  status?: 'Active' | 'Expired' | 'Deactivated';
  companyId?: string;
  planId?: string;
  assignmentYear?: number;
  referenceDate?: string;
  includePlanData?: boolean;
  enrollmentPeriodOnly?: boolean;
  effectiveOnly?: boolean;
  futureOnly?: boolean;
  includeInactive?: boolean;
  includeExpired?: boolean;
  brokerId?: string;
}

// Get plan assignments for a company with optimized API
export const getPlanAssignmentsByCompany = async (
  companyId: string,
  filters: PlanAssignmentFilters = {},
  pagination?: { page: number; limit: number }
): Promise<ApiResponse<PlanAssignmentsResponse>> => {
  try {
    const userId = getUserId();
    console.log('🔍 Plan Assignment API Debug:', {
      companyId,
      userId,
      filters,
      pagination,
      apiBaseUrl: API_BASE_URL
    });

    const queryParams = new URLSearchParams();

    // Add filters to query params
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.planId) queryParams.append('planId', filters.planId);
    if (filters.assignmentYear) queryParams.append('assignmentYear', filters.assignmentYear.toString());
    if (filters.referenceDate) queryParams.append('referenceDate', filters.referenceDate);
    if (filters.includePlanData !== undefined) queryParams.append('includePlanData', filters.includePlanData.toString());
    if (filters.enrollmentPeriodOnly) queryParams.append('enrollmentPeriodOnly', 'true');
    if (filters.effectiveOnly) queryParams.append('effectiveOnly', 'true');
    if (filters.futureOnly) queryParams.append('futureOnly', 'true');
    if (filters.includeInactive) queryParams.append('includeInactive', 'true');
    if (filters.includeExpired) queryParams.append('includeExpired', 'true');
    if (filters.brokerId) queryParams.append('brokerId', filters.brokerId);

    // Add pagination if provided
    if (pagination) {
      queryParams.append('page', pagination.page.toString());
      queryParams.append('limit', pagination.limit.toString());
    }

    const url = `${API_BASE_URL}/api/pre-enrollment/plan-assignments/company/${companyId}${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;

    console.log('📡 Fetching plan assignments from optimized API:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(),
    });

    console.log('Plan assignments API response status:', response.status);

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;

      if (response.status === 403) {
        const userId = getUserId();
        console.error('🚫 403 Forbidden Error Details:', {
          url,
          userId,
          companyId,
          userIdSource: localStorage.getItem('userid1') ? 'userid1' : localStorage.getItem('userId') ? 'userId' : 'none'
        });

        // For 403 errors, return a special response that indicates no access but allows page to load
        console.log('🔧 Broker has no existing plan assignments for this company - returning empty result to allow plan creation');
        return {
          success: true,
          data: {
            assignments: [],
            count: 0,
            message: 'No existing plan assignments. You can create new plan assignments for this company.',
            canCreateAssignments: true,
            accessDeniedToExisting: true
          }
        };
      }

      try {
        const errorData = await response.json();
        console.error('API Error Response:', errorData);
        errorMessage += ` - ${errorData.error || errorData.message || 'Unknown error'}`;
      } catch (e) {
        console.log('No additional error details available');
      }

      throw new Error(errorMessage);
    }

    const result = await response.json();
    console.log('Plan assignments result:', result);
    console.log('First assignment details:', result.assignments[0]);

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching plan assignments:', error);
    return {
      success: false,
      error: 'Failed to fetch plan assignments'
    };
  }
};

// Get active plan assignments for a company
export const getActivePlanAssignmentsByCompany = async (
  companyId: string,
  referenceDate?: string
): Promise<ApiResponse<PlanAssignmentsResponse>> => {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append('includeExpired', 'false');
    if (referenceDate) {
      queryParams.append('referenceDate', referenceDate);
    }

    const url = `${API_BASE_URL}/api/pre-enrollment/plan-assignments/company/${companyId}?${queryParams.toString()}`;

    console.log('Fetching active plan assignments from:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching active plan assignments:', error);
    return {
      success: false,
      error: 'Failed to fetch active plan assignments'
    };
  }
};

// Create a new plan assignment
export const createPlanAssignment = async (
  assignmentData: Partial<PlanAssignment>
): Promise<ApiResponse<PlanAssignment>> => {
  try {
    console.log('Creating plan assignment with data:', assignmentData);

    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(assignmentData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.error || errorData.message || `HTTP error! status: ${response.status}`;
      const errorDetails = errorData.details || errorData.required || [];

      console.error('Plan assignment creation failed:', {
        status: response.status,
        error: errorMessage,
        details: errorDetails,
        data: assignmentData
      });

      return {
        success: false,
        error: `${errorMessage}${errorDetails.length > 0 ? ` - ${errorDetails.join(', ')}` : ''}`
      };
    }

    const result = await response.json();

    return {
      success: true,
      data: result.assignment
    };
  } catch (error) {
    console.error('Error creating plan assignment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create plan assignment'
    };
  }
};

// Update a plan assignment
export const updatePlanAssignment = async (
  assignmentId: string,
  updateData: Partial<PlanAssignment>
): Promise<ApiResponse<PlanAssignment>> => {
  try {
    console.log('Updating plan assignment:', assignmentId, 'with data:', updateData);

    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(updateData),
    });

    console.log('Update response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.error || errorData.message || `HTTP error! status: ${response.status}`;
      const errorDetails = errorData.details || [];

      console.error('Plan assignment update failed:', {
        status: response.status,
        error: errorMessage,
        details: errorDetails,
        updateData: updateData
      });

      return {
        success: false,
        error: `${errorMessage}${errorDetails.length > 0 ? ` - ${errorDetails.join(', ')}` : ''}`
      };
    }

    const result = await response.json();
    console.log('Update successful:', result);

    return {
      success: true,
      data: result.assignment
    };
  } catch (error) {
    console.error('Error updating plan assignment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update plan assignment'
    };
  }
};

// Activate a plan assignment
export const activatePlanAssignment = async (
  assignmentId: string
): Promise<ApiResponse<{ message: string }>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}/activate`, {
      method: 'POST',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      data: { message: result.message || 'Plan assignment activated successfully' }
    };
  } catch (error) {
    console.error('Error activating plan assignment:', error);
    return {
      success: false,
      error: 'Failed to activate plan assignment'
    };
  }
};

// Deactivate a plan assignment
export const deactivatePlanAssignment = async (
  assignmentId: string
): Promise<ApiResponse<{ message: string }>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}/deactivate`, {
      method: 'POST',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      data: { message: result.message || 'Plan assignment deactivated successfully' }
    };
  } catch (error) {
    console.error('Error deactivating plan assignment:', error);
    return {
      success: false,
      error: 'Failed to deactivate plan assignment'
    };
  }
};

// Deactivate a plan assignment
export const deactivatePlanAssignment = async (
  assignmentId: string
): Promise<ApiResponse<PlanAssignment>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}/deactivate`, {
      method: 'POST',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      data: result.assignment
    };
  } catch (error) {
    console.error('Error deactivating plan assignment:', error);
    return {
      success: false,
      error: 'Failed to deactivate plan assignment'
    };
  }
};

// Clone a plan assignment
export const clonePlanAssignment = async (
  assignmentId: string,
  cloneData?: Partial<PlanAssignment>
): Promise<ApiResponse<PlanAssignment>> => {
  try {
    console.log('Cloning plan assignment:', assignmentId, 'with overrides:', cloneData);

    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}/clone`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ overrides: cloneData || {} }),
    });

    console.log('Clone response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.error || errorData.message || `HTTP error! status: ${response.status}`;
      const errorDetails = errorData.details || [];

      console.error('Plan assignment clone failed:', {
        status: response.status,
        error: errorMessage,
        details: errorDetails,
        cloneData: cloneData
      });

      return {
        success: false,
        error: `${errorMessage}${errorDetails.length > 0 ? ` - ${errorDetails.join(', ')}` : ''}`
      };
    }

    const result = await response.json();
    console.log('Clone successful:', result);

    return {
      success: true,
      data: result.assignment
    };
  } catch (error) {
    console.error('Error cloning plan assignment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to clone plan assignment'
    };
  }
};

// Check if plan assignment can be edited
export const canEditPlanAssignment = async (
  assignmentId: string
): Promise<ApiResponse<{ canEdit: boolean; referenceCount: number; referencedBy: string[]; message: string }>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}/can-edit`, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error checking if plan assignment can be edited:', error);
    return {
      success: false,
      error: 'Failed to check edit permissions'
    };
  }
};

// Get assignable plans (Active plans only)
export const getAssignablePlans = async (): Promise<ApiResponse<any[]>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/assignable`, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Backend returns { plans: [...] } format for assignable plans
    return {
      success: true,
      data: result.plans || []
    };
  } catch (error) {
    console.error('Error fetching assignable plans:', error);
    return {
      success: false,
      error: 'Failed to fetch assignable plans'
    };
  }
};

// Get aggregated plan assignments count for broker dashboard
export const getBrokerPlanAssignmentsCount = async (): Promise<ApiResponse<{ count: number }>> => {
  try {
    const userId = getUserId();
    console.log('🔍 Fetching broker plan assignments count for userId:', userId);

    // Use the optimized general API without pagination to get all assignments
    const url = `${API_BASE_URL}/api/pre-enrollment/plan-assignments?includePlanData=false`;

    console.log('📡 Fetching broker assignments count from:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(),
    });

    console.log('Broker assignments count API response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Broker assignments count response:', data);

      return {
        success: true,
        data: { count: data.count || 0 },
        error: null
      };
    } else {
      const errorText = await response.text();
      console.error('❌ Failed to fetch broker assignments count:', response.status, errorText);

      return {
        success: false,
        data: null,
        error: `Failed to fetch assignments count: ${response.status}`
      };
    }
  } catch (error) {
    console.error('❌ Error fetching broker assignments count:', error);
    return {
      success: false,
      data: null,
      error: 'Network error while fetching assignments count'
    };
  }
};
