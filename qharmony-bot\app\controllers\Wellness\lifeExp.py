
import pandas as pd
pd.set_option('future.no_silent_downcasting', True)
import numpy as np
from scipy.stats import norm
import shap
import pickle
import joblib
import xgboost as xgb
import os
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Union, Any, Tuple
from langchain_openai.chat_models import ChatOpenAI
import json
from scipy.stats import norm
from .dataModels import Question,QuestionsResponse,UserAnswer,LifeExpectancyPrediction
from .WellnessQuestions import get_life_expectancy_questions, get_life_expectancy_questions_json
import datetime, math
from .helpers import preprocess_dataframe, calculate_bmi







def calculate_life_expectancy(user_response: UserAnswer, model, preprocessors, poly_transform, sigma_value, X_train_poly, training_columns):
    """
    Accepts a JSON with user responses and returns the predicted baseline age,
    additional adjustments (based on weights), final adjusted age, survival probability,
    and SHAP explanations.
    
    Parameters:
      user_response: dict with key "answers", where answers is a dict keyed by question id.
      model, preprocessors, poly_transform, sigma_value, X_train_poly, training_columns: 
         Pre-loaded artifacts from the trained life expectancy model.
         
    Returns:
      A dict containing:
        - predicted_baseline_age: float,
        - additional_adjustment: float,
        - final_adjusted_age: float,
        - survival_probability: float,
        - shap_values: list (SHAP contributions).
    """
    # Define the mapping of questions (must match what is sent to the frontend).
    questions_mapping = {
      "gender": {"feature": "Sex", "type": "categorical", "options": ["Male", "Female"], "weights": None},
      "bmi": {"feature": "BMI", "type": "numeric", "weights": None},
      "age": {"feature": "Age", "type": "numeric", "weights": None},
      "race": {"feature": "Race", "type": "categorical", 
               "options": ["Black", "White", "Other", "American Indian/Alaskan Native", "Asian", "Hispanic", 
                           "White only, Non-Hispanic", "Black only, Non-Hispanic", "Multiracial, Non-Hispanic", 
                           "Other race only, Non-Hispanic"], 
                              "weights": {
                   "Black": 3,
                   "White": -5,
                   "Other": 0,
                   "American Indian/Alaskan Native": -2,
                   "Asian": 2,
                   "Hispanic": 2.5,
                   "White only, Non-Hispanic": -4, # Assuming same weight as "White"
                   "Black only, Non-Hispanic": 3,  # Assuming same weight as "Black"
                   "Multiracial, Non-Hispanic": 2,
                   "Other race only, Non-Hispanic": 0
               }},
      "general_health": {"feature": "GenHealth", "type": "ordinal", 
                         "options": ["Poor", "Fair", "Good", "Very good", "Excellent"], "weights": None},
      "diabetic": {"feature": "Diabetic", "type": "categorical", 
                   "options": ["No", "Yes", "No, borderline diabetes", "Yes (during pregnancy)",
                               "Yes, but only during pregnancy (female)", "No, pre-diabetes or borderline diabetes"],
                   "weights": {"No": 0,"No, borderline diabetes": -1,"No, pre-diabetes or borderline diabetes": -1,"Yes (during pregnancy)": -1.5,"Yes, but only during pregnancy (female)": -1.5,"Yes": -2.5}},
      "asthma": {"feature": "Asthma", "type": "boolean", "weights": -1.5},
      "kidney_problems": {"feature": None, "type": "boolean", "weights": -2.0},
      "stroke": {"feature": "Stroke", "type": "boolean", "weights": -2},
      "smoking": {"feature": "Smoking", "type": "categorical", 
                  "options": ["Rarely", "Sometimes", "Often"], "weights": {"Rarely": 0.5, "Sometimes": -2, "Often": -3}},
      "alcohol": {"feature": "AlcoholDrinking", "type": "categorical", 
                  "options": ["Rarely", "Sometimes", "Often"], "weights": {"Rarely": 0.5, "Sometimes": -2, "Often": -4}},
      "physical_activity": {"feature": "PhysicalActivity", "type": "categorical", 
                            "options": ["Rarely", "Sometimes", "Often"], "weights": {"Rarely": -3, "Sometimes": 1, "Often": 2.5}},
      "sleep_hours": {"feature": "SleepTime", "type": "numeric", 
                      "weights": {"<5": -3, "<7": -0.5, "7-10": 3, ">10": 1}},
      "walking": {"feature": "DiffWalking", "type": "categorical", 
                  "options": ["Rarely", "Sometimes", "Often"], "weights": {"Rarely": -2, "Sometimes": 1, "Often": 2.5}},
      "stress": {"feature": None, "type": "boolean", "weights": {"Yes": -4, "No": 5}},
      "social_life": {"feature": None, "type": "boolean", "weights": {"Yes": 3, "No": -3}},
      "healthy_food": {"feature": None, "type": "categorical", 
                       "options": ["Rarely", "Sometimes", "Often"], "weights": {"Rarely": -3, "Sometimes": 1, "Often": 3}},
      "cardio": {"feature": None, "type": "categorical", "weights": {"Rarely": -3, "Sometimes": 1, "Often": 3}},
      "life_span_grandparents": {"feature": None, "type": "numeric", "weights": {"<50": -5, "<70": 1, "<80": 2, "<90": 2.5, "<100":3.25, ">100":3.75}}
      
    }
    
    # Define special questions that need to be encoded to boolean for the ML model.
    special_bool = {"smoking", "alcohol", "physical_activity", "walking"}
    
    # Extract user answers.
    special_bool = {"smoking", "alcohol", "physical_activity", "walking"}
    answers = user_response.answers
    
    if answers.get('bmi') is None:
        answers['bmi'] = calculate_bmi(answers.get('height'), answers.get('weight'))
    
    baseline_input = {}
    additional_adjustment = 0.0
    for qid, mapping in questions_mapping.items():
        ans = answers.get(qid)
        if ans is None:
            continue
        if mapping["feature"] is not None:
            if qid in special_bool:
                baseline_input[mapping["feature"]] = "No" if str(ans).strip().lower() == "rarely" else "Yes"
            else:
                baseline_input[mapping["feature"]] = ans
        if mapping['weights'] is not None:
            if mapping["type"] == "boolean":
                if isinstance(mapping["weights"], dict):
                    if isinstance(ans, str) and ans.strip().lower() == "yes":
                        additional_adjustment += mapping["weights"].get("Yes", 0)
                    else:
                        additional_adjustment += mapping["weights"].get("No", 0)
                else:
                    if isinstance(ans, str) and ans.strip().lower() == "yes":
                        additional_adjustment += mapping["weights"]
            elif mapping["type"] == "categorical":
                additional_adjustment += mapping["weights"].get(ans, 0)
            elif mapping["type"] == "numeric":
                if qid == "sleep_hours":
                    if int(ans) <= 5:
                        cat = "<5"
                    elif int(ans) >= 6 and int(ans) < 7:
                        cat = "<7"
                    elif int(ans) >= 7 and int(ans) <= 10:
                        cat = "7-10"
                    else:
                        cat = ">10"
                    additional_adjustment += mapping["weights"].get(cat, 0)
                elif qid == "life_span_grandparents":
                    if int(ans) < 50:
                        cat = "<50"
                    elif int(ans) < 70:
                        cat = "<70"
                    elif int(ans) < 80:
                        cat = "<80"
                    elif int(ans) < 90:
                        cat = "<90"
                    elif int(ans) <= 100:
                         cat = "<100"
                    else:
                        cat = ">100"
                    additional_adjustment += mapping["weights"].get(cat, 0)
    
    baseline_pred, surv_prob, message = predict_and_explain(
        new_user_input=baseline_input,
        model=model,
        preprocessors=preprocessors,
        poly_transform=poly_transform,
        sigma_value=sigma_value,
        X_train_poly=X_train_poly,
        training_columns=training_columns,
        drop_cols=['Age', 'HeartDisease']
    )
    
    final_adjusted_age = baseline_pred + additional_adjustment
    
    return LifeExpectancyPrediction(
        predicted_baseline_age=baseline_pred,
        additional_adjustment=additional_adjustment,
        final_adjusted_age=final_adjusted_age,
        survival_probability_past_100=surv_prob,
        message=message
    )


def predict_and_explain(new_user_input, 
                        model, 
                        preprocessors, 
                        poly_transform, 
                        sigma_value, 
                        X_train_poly,
                        training_columns,
                        drop_cols=['Age', 'HeartDisease']):
    """
    Takes raw new user input (a dict), preprocesses it using hardcoded column types and ordinal categories,
    reorders the columns to match the training data, computes the prediction (age), uses SHAP to get dynamic feature 
    contributions, and computes the survival probability for living past 90 years.
    
    Parameters:
      new_user_input: dict, raw input from user.
      model: trained Ridge model.
      preprocessors: fitted preprocessors.
      poly_transform: fitted PolynomialFeatures transformer.
      sigma_value: estimated residual uncertainty.
      X_train_poly: poly-transformed training data (for SHAP explainer).
      training_columns: list, feature names (in order) used during training.
      drop_cols: list of columns to drop (default: ['Age', 'HeartDisease']).
      
    Returns:
      predicted_age: predicted age (proxy for death age).
      survival_prob: probability of living past 90 years.
      shap_values: SHAP values for the new user input.
    """
    # Hardcoded column types and ordinal categories.
    col_types = {
        'yes_or_no': ['Asthma', 'Stroke', 'Smoking', 'AlcoholDrinking', 'PhysicalActivity', 'DiffWalking'],
        'ordinal': ['GenHealth'],
        'numeric': ['SleepTime', 'BMI'],
        'nominal': ['Sex', 'Race', 'Diabetic']
    }
    ord_categories = {
        'GenHealth': ['Poor', 'Fair', 'Good', 'Very good', 'Excellent']
    }
    
    # Remove target and unused columns if present.
    input_copy = new_user_input.copy()
    for col in drop_cols:
        input_copy.pop(col, None)
        
    # Convert raw input to DataFrame.
    new_user_df = pd.DataFrame([input_copy])
    
    # Preprocess using the fitted preprocessors.
    new_user_preprocessed = preprocess_dataframe(
        new_user_df,
        column_types=col_types,
        ordinal_categories=ord_categories,
        fitted_preprocessors=preprocessors,
        columns_to_drop=drop_cols
    )
    
    # Reindex to match training columns.
    new_user_preprocessed = new_user_preprocessed.reindex(columns=training_columns, fill_value=0)
    
    # Apply polynomial transformation.
    new_user_poly = poly_transform.transform(new_user_preprocessed)
    
    # Predict age.
    predicted_age = model.predict(new_user_poly)[0]
    
    # Compute survival probability for living past 90 years.
    survival_prob_past_100 = 1 - norm.cdf(100, loc=predicted_age, scale=sigma_value)
    
    # Create SHAP explainer on the fly using training polynomial data.
    shap_explainer = shap.Explainer(model, X_train_poly)
    # shap_vals = shap_explainer(new_user_poly)
    
    if predicted_age<=60:
        message="Congratulations, You are very likely to live past this age."
    elif predicted_age<=70 and predicted_age>60:
        message="Congratulations, You are likely to live past or around this age, based on the data provided."
    elif predicted_age>70 and predicted_age<=90:
        message="Congratulations, you are expected to live around this age."
    else:
        message="Congratulations on living past 90s"
    
    return predicted_age, survival_prob_past_100, message