'use client';

import React, { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  HiOutlineArrowLeft,
  HiOutlineRefresh,
  HiOutlinePencilAlt,
  HiOutlineSparkles,
  HiOutlineCheckCircle
} from 'react-icons/hi';
import '../../renewal.css';
import '../plan-detail.css';
import './renewal-options.css';

interface RenewalOption {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  recommended?: boolean;
  features: string[];
  benefits: string[];
}

const RenewalOptionsPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [currentStep, setCurrentStep] = useState(2);

  const groupName = 'TechCorp Solutions';

  const renewalOptions: RenewalOption[] = [
    {
      id: 'renew-as-is',
      title: 'Renew As-Is',
      description: 'Keep all existing plan terms, benefits, and premium structures unchanged',
      icon: <HiOutlineRefresh size={24} />,
      recommended: true,
      features: [
        'Fastest processing time',
        'Automatic rate rollover'
      ],
      benefits: [
        'No disruption to employees',
        'Same plan codes and IDs'
      ]
    },
    {
      id: 'copy-modify',
      title: 'Copy & Modify Plans',
      description: 'Start with current plans but make adjustments to rates, benefits, or terms',
      icon: <HiOutlinePencilAlt size={24} />,
      features: [
        'Flexible rate adjustments',
        'Modify plan names'
      ],
      benefits: [
        'Update deductibles/copays',
        'Change carrier terms'
      ]
    },
    {
      id: 'major-changes',
      title: 'Major Plan Changes',
      description: 'Significant modifications including new carriers, plan designs, or benefit structures',
      icon: <HiOutlineSparkles size={24} />,
      features: [
        'New carrier integration',
        'Benefit restructuring'
      ],
      benefits: [
        'Plan design overhaul',
        'Custom plan creation'
      ]
    }
  ];

  const steps = [
    { number: 1, title: 'Review Current Plans', subtitle: 'View existing benefit plans', active: false },
    { number: 2, title: 'Renewal Options', subtitle: 'Choose renewal type', active: currentStep === 2 },
    { number: 3, title: 'Plan Configuration', subtitle: 'Set dates and modifications', active: false },
    { number: 4, title: 'Document Upload', subtitle: 'Upload plan documents', active: false },
    { number: 5, title: 'Validation', subtitle: 'Review and validate setup', active: false },
    { number: 6, title: 'Finalize', subtitle: 'Complete renewal process', active: false },
    { number: 7, title: 'Export', subtitle: 'Download and share data', active: false }
  ];

  const handleOptionSelect = (optionId: string) => {
    setSelectedOption(optionId);
  };

  const handleContinue = () => {
    if (selectedOption) {
      router.push(`/ai-enroller/renewal/${params.groupId}/plan-configuration?option=${selectedOption}`);
    }
  };

  const handlePrevious = () => {
    router.back();
  };

  return (
    <div className="plan-renewal-detail">
      {/* Header */}
      <div className="detail-header">
        <button 
          className="back-btn"
          onClick={() => router.push('/ai-enroller/renewal')}
        >
          <HiOutlineArrowLeft size={20} />
          Back to Dashboard
        </button>
        
        <div className="header-info">
          <h1>Plan Renewal</h1>
          <h2>{groupName}</h2>
          <div className="step-indicator">Step {currentStep} of 7</div>
        </div>

        <div className="completion-status">
          29% Complete
        </div>
      </div>

      {/* Progress Steps */}
      <div className="renewal-steps">
        {steps.map((step, index) => (
          <div key={step.number} className={`renewal-step ${step.active ? 'active' : ''}`}>
            <div className="step-number">{step.number}</div>
            <div className="step-content">
              <div className="step-title">{step.title}</div>
              <div className="step-subtitle">{step.subtitle}</div>
            </div>
            {index < steps.length - 1 && <div className="step-connector"></div>}
          </div>
        ))}
      </div>

      {/* Renewal Options */}
      <div className="renewal-options-section">
        <div className="options-header">
          <div className="options-title">
            <HiOutlineRefresh size={20} />
            <h3>Plan Renewal Strategy</h3>
          </div>
          <p>Choose how you want to approach the renewal process for {groupName}. This will determine the workflow and options available in subsequent steps.</p>
        </div>

        <div className="options-grid">
          {renewalOptions.map((option) => (
            <div 
              key={option.id} 
              className={`option-card ${selectedOption === option.id ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option.id)}
            >
              {option.recommended && (
                <div className="recommended-badge">
                  Recommended
                </div>
              )}
              
              <div className="option-header">
                <div className="option-select">
                  <div className={`radio-btn ${selectedOption === option.id ? 'selected' : ''}`}>
                    {selectedOption === option.id && <HiOutlineCheckCircle size={16} />}
                  </div>
                </div>
                
                <div className="option-icon">
                  {option.icon}
                </div>
                
                <div className="option-info">
                  <h4>{option.title}</h4>
                  <p>{option.description}</p>
                </div>
              </div>

              <div className="option-features">
                <div className="features-section">
                  <h5>Key Features:</h5>
                  <ul>
                    {option.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
                
                <div className="benefits-section">
                  <ul>
                    {option.benefits.map((benefit, index) => (
                      <li key={index}>{benefit}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation */}
        <div className="navigation-section">
          <button 
            className="nav-btn secondary"
            onClick={handlePrevious}
          >
            <HiOutlineArrowLeft size={16} />
            Previous
          </button>
          
          <button 
            className={`nav-btn primary ${selectedOption ? 'enabled' : 'disabled'}`}
            onClick={handleContinue}
            disabled={!selectedOption}
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};

export default RenewalOptionsPage;
