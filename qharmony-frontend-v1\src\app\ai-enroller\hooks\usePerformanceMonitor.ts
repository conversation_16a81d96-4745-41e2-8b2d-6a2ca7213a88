'use client';

import { useEffect } from 'react';

export const usePerformanceMonitor = (pageName: string) => {
  useEffect(() => {
    // Mark the start time
    const startTime = performance.now();
    
    // Mark when the page is fully loaded
    const handleLoad = () => {
      const loadTime = performance.now() - startTime;
      
      // Log performance metrics (in production, send to analytics)
      if (process.env.NODE_ENV === 'development') {
        console.log(`${pageName} loaded in ${loadTime.toFixed(2)}ms`);
      }
      
      // Store in localStorage for debugging (only in browser environment)
      if (typeof window !== 'undefined') {
        const perfData = JSON.parse(localStorage.getItem('ai-enroller-perf') || '{}');
        perfData[pageName] = {
          loadTime: loadTime.toFixed(2),
          timestamp: new Date().toISOString()
        };
        localStorage.setItem('ai-enroller-perf', JSON.stringify(perfData));
      }
    };

    // Check if page is already loaded
    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
    }

    return () => {
      window.removeEventListener('load', handleLoad);
    };
  }, [pageName]);
};

export default usePerformanceMonitor;
