import { Grid, Box, Typography } from "@mui/material";
import Image from "next/image";
import FormatQuoteIcon from "@mui/icons-material/FormatQuote";
import LandingImage1 from "../../public/landing_page_image_1.png";
import LandingImage2 from "../../public/landing_page_image_2.png";
import LandingImage3 from "../../public/landing_page_image_3.png";
import LandingImage4 from "../../public/landing_page_image_4.png";
import { useState, useEffect } from "react";

interface RightPanelOnlyComponentProps {
  LeftComponent: React.ReactNode;
}

const images = [
  {
    src: LandingImage1,
    text: "I get to keep track of all my benefits so I never miss out on the stuff that matters most.",
    name: "<PERSON>",
    title: "Associate Manager",
  },
  {
    src: LandingImage2,
    text: "Now I can track my benefits instantly without having to search through emails. It’s a game–changer!",
    name: "<PERSON>",
    title: "Software Engineer",
  },
  {
    src: LandingImage3,
    text: "This is by far the most convenient way to access my benefits. Everything I need is right at my fingertips.",
    name: "<PERSON>",
    title: "HR Specialist",
  },
  {
    src: LandingImage4,
    text: "I love how I can get all my benefits info through Slack. It saves me so much time!",
    name: "Troy Edward",
    title: "Associate Manager",
  },
];

const RightPanelOnlyComponent: React.FC<RightPanelOnlyComponentProps> = ({
  LeftComponent,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const currentImage = images[currentIndex];

  return (
    <Grid container style={{ height: "95vh", width: "100%" }}>
      {/* Left Side: Dynamic Component */}
      <Grid
        item
        xs={12}
        md={6}
        sx={{
          bgcolor: "#000000",
          color: "#ffffff",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center", // Center vertically
          alignItems: "center", // Center horizontally
          padding: "100px",
          height: "95vh", // Full viewport height
          overflow: "auto", // Enable scrolling
        }}
      >
        {LeftComponent}
      </Grid>

      {/* Right Side: Image with Quote */}
      <Grid
        item
        xs={12}
        md={6}
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#f8f9fa",
          padding: "0",
          position: "relative",
          overflow: "hidden",
          height: "95vh", // Full viewport height
        }}
      >
        {/* Full-screen Image */}
        <Box
          sx={{
            position: "relative",
            width: "100%",
            height: "100%",
            overflow: "hidden",
          }}
        >
          <Image
            src={currentImage.src}
            alt="Profile"
            layout="fill"
            objectFit="cover"
            style={{
              objectPosition: "center",
            }}
          />
          {/* Gradient Overlay */}
          <Box
            sx={{
              position: "absolute",
              bottom: 0,
              left: 0,
              width: "100%",
              height: "100%",
              background:
                "linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0) 60%)",
            }}
          />
          {/* Quote Section */}
          <Box
            sx={{
              position: "absolute",
              bottom: "10%",
              left: "10%",
              color: "#ffffff",
              width: "550px",
            }}
          >
            {/* Quotes Icon */}
            <FormatQuoteIcon
              sx={{
                fontSize: 70,
                marginBottom: "10px",
                opacity: 0.5,
                marginLeft: -2,
              }}
            />
            <Typography
              variant="h5"
              sx={{
                fontSize: "32px", // Match font size
                fontWeight: "bold",
                lineHeight: "1.5",
                mb: 2,
              }}
            >
              {currentImage.text}
            </Typography>
            <Typography
              variant="h5"
              sx={{
                fontSize: "24px", // Match font size
                fontWeight: "bold",
                lineHeight: "1.5",
                mt: 4,
              }}
            >
              {currentImage.name}
            </Typography>
            <Typography
              variant="body2"
              sx={{ fontSize: "20px", fontWeight: "light" }}
            >
              {currentImage.title}
            </Typography>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default RightPanelOnlyComponent;
