export type Result<T, E = Error> =
  | { ok: true; value: T }
  | { ok: false; error: E };

export type Enumerate<
  N extends number,
  Acc extends number[] = []
> = Acc['length'] extends N
  ? Acc[number]
  : Enumerate<N, [...Acc, Acc['length']]>;

export type IntRange<F extends number, T extends number> = Exclude<
  Enumerate<T>,
  Enumerate<F>
>;

export type ErrorResponse<T> = {
  code: IntRange<400, 599>;
  error: string;
  data: T | null;
};

export type SuccessResponse<T> = {
  code: 200;
  error: null;
  data: T;
};

export type ApiResponse<T> = SuccessResponse<T> | ErrorResponse<T>;

export function createFailureResponse<T>(
  code: IntRange<400, 599>,
  error: string,
  data: T | null = null
): ErrorResponse<T> {
  const response: ErrorResponse<T> = {
    code,
    error,
    data,
  };
  return response;
}

export function createSuccessResponse<T>(data: T): SuccessResponse<T> {
  const response: SuccessResponse<T> = {
    code: 200,
    data: data,
    error: null,
  };
  return response;
}

export default ApiResponse;
