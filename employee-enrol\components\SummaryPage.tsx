'use client';

import React from 'react';
import { User, CheckCircle, Edit, Download, FileText, Printer } from 'lucide-react';

interface EnrollmentData {
  medicalPlan?: any;
  dentalPlan?: any;
  visionPlan?: any;
  additionalBenefits?: any[];
}

interface SummaryPageProps {
  enrollmentData: EnrollmentData;
  onMakeChanges: () => void;
  onConfirmEnrollment: () => void;
}

const SummaryPage: React.FC<SummaryPageProps> = ({ 
  enrollmentData, 
  onMakeChanges, 
  onConfirmEnrollment 
}) => {
  // Calculate costs
  const medicalCost = enrollmentData.medicalPlan?.cost || 0;
  const dentalCost = enrollmentData.dentalPlan?.cost || 0;
  const visionCost = enrollmentData.visionPlan?.cost || 0;
  const additionalCosts = enrollmentData.additionalBenefits?.reduce((sum, benefit) => sum + benefit.cost, 0) || 0;
  
  const totalCost = medicalCost + dentalCost + visionCost + additionalCosts;
  const employerContribution = 0; // Assuming employer pays $0 for this example
  const yourCost = totalCost - employerContribution;

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{ 
          width: '40px', 
          height: '40px', 
          backgroundColor: '#dbeafe', 
          borderRadius: '8px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <User style={{ width: '20px', height: '20px', color: '#2563eb' }} />
        </div>
        <div style={{ 
          backgroundColor: '#f9fafb', 
          borderRadius: '8px', 
          padding: '16px', 
          maxWidth: '512px' 
        }}>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: 0 
          }}>
            📋 Perfect! Here's your personalized benefits package:
          </p>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: '8px 0 0 0' 
          }}>
            Take a moment to review everything. You can always go back and make changes if needed.
          </p>
        </div>
      </div>

      {/* Summary Card */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        border: '1px solid #e5e7eb', 
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <FileText style={{ width: '20px', height: '20px', color: '#f59e0b' }} />
          <span style={{ fontSize: '18px' }}>📋</span>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: '600', 
            color: '#111827',
            margin: 0
          }}>
            Smart Enrollment Summary
          </h2>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '24px' }}>
          <CheckCircle style={{ width: '20px', height: '20px', color: '#10b981' }} />
          <p style={{ 
            color: '#6b7280', 
            margin: 0
          }}>
            Here's your personalized benefits package:
          </p>
        </div>

        {/* Enrollment Summary */}
        <div style={{ 
          backgroundColor: '#f9fafb', 
          borderRadius: '8px', 
          padding: '20px',
          marginBottom: '24px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <CheckCircle style={{ width: '20px', height: '20px', color: '#10b981' }} />
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
              Enrollment Summary
            </h3>
          </div>

          {/* Selected Plans */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {/* Medical Plan */}
            {enrollmentData.medicalPlan && (
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
                    Medical Plan: {enrollmentData.medicalPlan.name}
                  </p>
                  <p style={{ color: '#6b7280', fontSize: '14px', margin: '4px 0 0 0' }}>
                    Core Health Coverage
                  </p>
                </div>
                <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
                  ${medicalCost.toFixed(2)}/paycheck
                </p>
              </div>
            )}

            {/* Dental Plan */}
            {enrollmentData.dentalPlan && (
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
                    Dental Plan: {enrollmentData.dentalPlan.name}
                  </p>
                  <p style={{ color: '#6b7280', fontSize: '14px', margin: '4px 0 0 0' }}>
                    Dental Coverage
                  </p>
                </div>
                <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
                  ${dentalCost.toFixed(2)}/paycheck
                </p>
              </div>
            )}

            {/* Vision Plan */}
            {enrollmentData.visionPlan && enrollmentData.visionPlan.cost > 0 && (
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
                    Vision Plan: {enrollmentData.visionPlan.name}
                  </p>
                  <p style={{ color: '#6b7280', fontSize: '14px', margin: '4px 0 0 0' }}>
                    Eye Care Coverage
                  </p>
                </div>
                <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
                  ${visionCost.toFixed(2)}/paycheck
                </p>
              </div>
            )}

            {/* Additional Benefits */}
            {enrollmentData.additionalBenefits?.map((benefit, index) => (
              <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
                    {benefit.category === 'pet' ? 'Pet Insurance' : 'Hospital Indemnity'}: {benefit.name}
                  </p>
                  <p style={{ color: '#6b7280', fontSize: '14px', margin: '4px 0 0 0' }}>
                    {benefit.category === 'pet' ? 'Additional Protection' : 'Extra Financial Protection'}
                  </p>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
                    <span style={{ color: '#6b7280', fontSize: '14px' }}>💰 Employer pays: $0.00</span>
                    <span style={{ color: '#111827', fontSize: '14px', fontWeight: '500' }}>
                      You pay: ${benefit.cost.toFixed(2)}
                    </span>
                  </div>
                </div>
                <p style={{ fontWeight: '600', color: '#111827', margin: 0 }}>
                  ${benefit.cost.toFixed(2)}/paycheck
                </p>
              </div>
            ))}
          </div>

          {/* Cost Summary */}
          <div style={{ 
            borderTop: '1px solid #e5e7eb', 
            paddingTop: '16px', 
            marginTop: '16px' 
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span style={{ fontWeight: '600', color: '#111827' }}>Total Plan Cost:</span>
              <span style={{ fontWeight: '600', color: '#111827' }}>${totalCost.toFixed(2)}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span style={{ color: '#10b981' }}>Employer Contribution:</span>
              <span style={{ color: '#10b981' }}>-${employerContribution.toFixed(2)}</span>
            </div>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              paddingTop: '8px',
              borderTop: '1px solid #e5e7eb'
            }}>
              <span style={{ fontSize: '18px', fontWeight: '700', color: '#111827' }}>
                Your Cost per Paycheck:
              </span>
              <span style={{ fontSize: '18px', fontWeight: '700', color: '#2563eb' }}>
                ${yourCost.toFixed(2)}
              </span>
            </div>
            
            <div style={{ marginTop: '12px' }}>
              <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
                Coverage: You + Spouse
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div style={{ display: 'flex', gap: '12px', marginBottom: '24px' }}>
          <button
            onClick={onMakeChanges}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 24px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontWeight: '500',
              flex: 1
            }}
          >
            <Edit size={16} />
            Make Changes
          </button>
          <button
            onClick={onConfirmEnrollment}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 24px',
              backgroundColor: '#1f2937',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer',
              fontWeight: '500',
              flex: 1
            }}
          >
            <CheckCircle size={16} />
            Confirm Enrollment
          </button>
        </div>

        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            <Download size={16} />
            Download Summary PDF
          </button>
        </div>

        {/* Bottom Action Buttons */}
        <div style={{ 
          display: 'flex', 
          gap: '12px', 
          paddingTop: '24px', 
          borderTop: '1px solid #e5e7eb', 
          marginTop: '24px' 
        }}>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <span style={{ color: '#2563eb' }}>❓</span>
            Ask Questions
          </button>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}>
            <Printer size={16} style={{ color: '#6b7280' }} />
            Print Summary
          </button>
        </div>
      </div>
    </div>
  );
};

export default SummaryPage;
