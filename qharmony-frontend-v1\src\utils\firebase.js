import { initializeApp, getApps } from 'firebase/app';
import { getAuth } from 'firebase/auth'; // Import getAuth

/**
 * Get Firebase configuration based on environment
 * Uses hostname to determine which Firebase project to use
 */
function getFirebaseConfig() {
  // Check if we're in browser environment
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;

    if (hostname.includes('test.benosphere.com') || hostname === 'localhost') {
      // Test Environment - qharmony-test project (includes localhost for development)
      return {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_TEST_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_TEST_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_TEST_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_TEST_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_TEST_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_TEST_APP_ID,
      };
    }
  }

  // Production Environment - qharmony-dev project (default)
  return {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_PROD_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_PROD_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROD_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_PROD_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_PROD_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_PROD_APP_ID,
  };
}

const firebaseConfig = getFirebaseConfig();
const app = !getApps().length ? initializeApp(firebaseConfig) : getApps()[0];
const auth = getAuth(app); // Initialize auth

export { auth }; // Export auth
export default app;