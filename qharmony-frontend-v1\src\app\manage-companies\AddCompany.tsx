"use client";

import React, { useState, useEffect, FC } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  IconButton,
  CircularProgress,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useRouter } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  brokerAddsCompany,
  getAllCompaniesUnderBroker,
} from "@/middleware/company_middleware";

interface CompanyAdminDetails {
  companyName: string;
  companyAdminEmail: string;
  companyAdminName: string;
}

interface AddCompanyDialogProps {
  open: boolean;
  onClose: () => void;
}

const AddCompanyDialog: FC<AddCompanyDialogProps> = ({ open, onClose }) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const userId = useAppSelector((state) => state.user._id);
  const [companyDetails, setCompanyDetails] = useState<CompanyAdminDetails>({
    companyName: "",
    companyAdminEmail: "",
    companyAdminName: "",
  });
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    getAllCompaniesUnderBroker(dispatch, userId);
  }, [dispatch, userId]);

  const handleInputChange = (field: keyof CompanyAdminDetails, value: any) => {
    setCompanyDetails({ ...companyDetails, [field]: value });
  };

  const handleConfirmDetails = async () => {
    setLoading(true);
    if (
      !companyDetails.companyName ||
      !companyDetails.companyAdminEmail ||
      !companyDetails.companyAdminName
    ) {
      alert("Please fill out all required fields.");
      setLoading(false);
      return;
    }

    const response = await brokerAddsCompany(
      userId,
      companyDetails.companyName,
      companyDetails.companyAdminEmail,
      companyDetails.companyAdminName,
    );
    setLoading(false);
    if (response && response.status === 200) {
      setSuccessMessage("Invite has been sent to the admin! They will show up in your team members list once they accept the invite.");
      setCompanyDetails({
        companyName: "",
        companyAdminEmail: "",
        companyAdminName: "",
      });
    }
  };

  const handleAddAnother = () => {
    setSuccessMessage("");
    setCompanyDetails({
      companyName: "",
      companyAdminEmail: "",
      companyAdminName: "",
    });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        style: {
          borderRadius: "16px",
          boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
          padding: "5px",
          width: "550px",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          fontWeight: "bold",
          fontSize: "1.5rem",
        }}
      >
        Add Company and Admin
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3} sx={{ marginBottom: "16px", marginTop: "0px" }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              required
              label="Company Name"
              variant="outlined"
              value={companyDetails.companyName}
              onChange={(e) => handleInputChange("companyName", e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              required
              label="Admin Email"
              variant="outlined"
              value={companyDetails.companyAdminEmail}
              onChange={(e) => handleInputChange("companyAdminEmail", e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              required
              label="Admin Name"
              variant="outlined"
              value={companyDetails.companyAdminName}
              onChange={(e) => handleInputChange("companyAdminName", e.target.value)}
              InputProps={{
                style: {
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  height: "48px",
                },
              }}
              InputLabelProps={{
                shrink: true,
                style: { fontSize: "1rem" },
              }}
              sx={{ alignItems: "flex-start" }}
            />
          </Grid>
        </Grid>
        {loading && <CircularProgress />}
        {successMessage && (
          <div
            style={{
              color: "green",
              marginTop: "10px",
              display: "flex",
              alignItems: "start",
            }}
          >
            <CheckCircleIcon style={{ marginRight: "10px" }} />
            {successMessage}
          </div>
        )}
      </DialogContent>

      <DialogActions sx={{ padding: "16px" }}>
        {successMessage ? (
          <Button
            onClick={handleAddAnother}
            sx={{
              color: "#ffffff",
              backgroundColor: "#000000",
              borderRadius: "12px",
              padding: "8px 24px",
              textTransform: "none",
              fontWeight: "bold",
              "&:hover": {
                backgroundColor: "#333333",
              },
            }}
          >
            Add Another Company
          </Button>
        ) : (
          <Button
            onClick={handleConfirmDetails}
            sx={{
              color: "#ffffff",
              backgroundColor: "#000000",
              borderRadius: "12px",
              padding: "8px 24px",
              textTransform: "none",
              fontWeight: "bold",
              "&:hover": {
                backgroundColor: "#333333",
              },
            }}
            disabled={loading}
          >
            {loading ? "Adding..." : "Confirm Details"}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AddCompanyDialog;
