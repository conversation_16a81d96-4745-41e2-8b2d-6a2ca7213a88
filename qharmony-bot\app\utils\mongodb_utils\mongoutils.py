from pymongo import MongoClient
from bson import ObjectId
from typing import List, Dict, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fetch_user_and_group_data(
    mongodb_uri: str,
    user_id: str,
    db_name: str = "prod"
) -> Dict[str, any]:
    """
    Fetch user and group data from MongoDB, including document IDs associated with the user's groups.

    Args:
        mongodb_uri (str): MongoDB connection URI.
        user_id (str): The user ID to fetch data for.
        db_name (str): The name of the database (default: "prod").

    Returns:
        Dict[str, any]: Dictionary containing user data, group IDs, group data, and document IDs.
    """
    try:
        # Connect to MongoDB
        client = MongoClient(mongodb_uri)
        db = client[db_name]

        # Fetch user data from the "users" collection
        user_collection = db["users"]
        user_data = user_collection.find_one({"_id": ObjectId(user_id)})
        print("user_data >>", user_data)

        if not user_data:
            client.close()
            raise ValueError(f"No user found with _id: {user_id}")

        # Extract groupIds from user data
        group_ids = user_data.get("groupIds", [])
        print("groupIds >>", group_ids)

        # Fetch group data from the "groups" collection
        group_collection = db["groups"]
        group_data = {}

        # Check if the "groups" collection exists
        if "groups" not in db.list_collection_names():
            logger.warning("The 'groups' collection does not exist.")
            group_data = []
        else:
            group_data = list(group_collection.find({"_id": {"$in": [ObjectId(group_id) for group_id in group_ids]}}))

        # Extract document IDs from group data
        document_ids = []
        if group_ids:
            for group in group_data:
                document_ids.extend(group.get("document_ids", []))
        print("document_ids >>", document_ids)

        client.close()

        return {
            "user_data": user_data,
            "group_ids": group_ids,
            "group_data": group_data,
            "document_ids": document_ids
        }

    except Exception as e:
        logger.error(f"Error fetching user and group data from MongoDB: {str(e)}")
        raise
    

def fetch_mongo_data(
    mongodb_uri: str,
    company_id: str = None,
    employee_id: str = None,
    db_name: str = "prod"
) -> Dict[str, any]:
    """
    Connect to MongoDB and fetch relevant data for a company or employee.
    """
    try:
        client = MongoClient(mongodb_uri)
        db = client[db_name]

        result = {}

        if company_id:
            companies_collection = db["companies"]
            company_data = companies_collection.find_one({"companyId": company_id})
            if not company_data:
                raise ValueError(f"No company found with companyId: {company_id}")
            result["company_data"] = {
                "companyId": company_data["companyId"],
                "document_ids": company_data.get("document_ids", []),
                "employee_ids": company_data.get("employee_ids", [])
            }

        if employee_id:
            users_collection = db["users"]
            employee_data = users_collection.find_one({"_id": ObjectId(employee_id)})
            if not employee_data:
                raise ValueError(f"No employee found with _id: {employee_id}")
            result["employee_data"] = {
                "employee_id": str(employee_data["_id"]),
                "name": employee_data["name"],
                "companyId": employee_data["companyId"],
                "groupIds": employee_data.get("groupIds", [])
            }

        client.close()
        return result

    except Exception as e:
        print(f"Error fetching data from MongoDB: {str(e)}")
        raise