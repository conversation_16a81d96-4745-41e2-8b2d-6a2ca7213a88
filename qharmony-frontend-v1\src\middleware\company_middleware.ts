import { getRequest, postRequest, putRequest } from "@/APILayer/axios_helper";
import {
  setCompanyBenefitTypes,
  setCompanyDetails,
  setCompanyTeamMembers,
} from "../redux/reducers/companySlice";
import { AppDispatch } from "../redux/store";
import { getBenefitsForType } from "./benefits_middleware";
import { setAllBenefitsPerType } from "@/redux/reducers/benefitsSlice";
import {
  setManagedCompanies,
  setUserProfile,
} from "@/redux/reducers/userSlice";

export function maskBenefitCategory(category: string): string {
  switch (category) {
    case "Our Culture & Workplace":
      return "Work Policies";
    case "Protecting Your Income":
      return "Income Security";
    default:
      return category;
  }
}

export function maskedSubCategory(subCategory: string): string {
  switch (subCategory) {
    case "On-site Amenities":
      return "Company Handbook";
    case "Protecting Your Income":
      return "Income Security";
    case "Got married/divorced":
      return "Marriage or Divorce";
    case "Had a baby or adopted a baby":
      return "New Baby or Adoption";
    case "Lost your insurance":
      return "Loss of Insurance";
    default:
      return subCategory;
  }
}

export async function getCompanyBenefitTypes(
  dispatch: AppDispatch,
  companyId: string,
) {
  const response = await getRequest(`/benefits/benefit-types`, {
    companyId: companyId,
  });
  console.log("COMPANY BENEFIT TYPES RESPONSE: ", response.benefitTypes);
  dispatch(setCompanyBenefitTypes(response.benefitTypes));
  return response.benefitTypes;
}

type Benefit = {
  _id: string;
  companyId: string;
  type: string;
  subType: string;
  heading: string;
  description: string;
  imageS3Urls: string[];
  links: string[];
  isActivated: boolean;
  __v: number;
};

type BenefitsPerType = {
  benefitType: string;
  benefits: Benefit[];
};

export function renameBenefits(response: any): BenefitsPerType[] {
  return response.benefitsPerType.map((benefitTypeObj: any) => {
    // Rename based on the benefitType
    if (benefitTypeObj.benefitType === "Our Culture & Workplace") {
      benefitTypeObj.benefitType = "Work Policies";
    } else if (benefitTypeObj.benefitType === "Protecting Your Income") {
      benefitTypeObj.benefitType = "Income Security";
    }

    // Rename the subTypes as per the instructions
    benefitTypeObj.benefits = benefitTypeObj.benefits.map((benefit: any) => {
      switch (benefit.subType) {
        case "On-site Amenities":
          benefit.subType = "Company Handbook";
          break;
        case "Protecting Your Income":
          benefit.subType = "Income Security";
          break;
        case "Got married/divorced":
          benefit.subType = "Marriage or Divorce";
          break;
        case "Had a baby or adopted a baby":
          benefit.subType = "New Baby or Adoption";
          break;
        case "Lost your insurance":
          benefit.subType = "Loss of Insurance";
          break;
        default:
          break;
      }
      return benefit;
    });

    return benefitTypeObj;
  });
}

export async function getCompanyBenefitTypesWithBenefits(
  dispatch: AppDispatch,
  companyId: string,
) {
  const response = await getRequest(`/benefits/all-benefits`, {
    companyId: companyId,
  });
  console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ", response);

  dispatch(setAllBenefitsPerType(response.benefitsPerType));
}

export async function getCompanyTeamMembers(dispatch: AppDispatch) {
  const response = await getRequest(`/admin/all-employees`);
  console.log("COMPANY TEAM MEMBERS RESPONSE: ", response);
  dispatch(setCompanyTeamMembers(response.employees));
  return response.employees;
}

export async function adminAddsUsers(
  dispatch: AppDispatch,
  users: {
    name: string;
    email: string;
  }[],
) {
  console.log("ADDING USERS: ", users);
  const response = await postRequest(`/admin/add/employees`, {
    employeeList: users,
  });
  return response;
}

// Helper function to get company admin ID using available endpoints
const getCompanyAdminId = async (): Promise<string | null> => {
  try {
    console.log('🔍 Debug: Getting company admin ID...');
    const response = await getRequest('/employee/company-details');
    console.log('🔍 Debug: Company details response:', response);

    if (response && response.company) {
      const company = response.company;
      console.log('🔍 Debug: Company admin email:', company.adminEmail);

      // Check if current user is the admin
      const currentUserId = getCurrentUserId();
      const currentUserResponse = await getRequest('/employee');

      if (currentUserResponse?.currentUser) {
        const currentUser = currentUserResponse.currentUser;
        console.log('🔍 Debug: Current user email:', currentUser.email);
        console.log('🔍 Debug: Current user isAdmin:', currentUser.isAdmin);

        // If current user is the company admin, use their ID
        if (currentUser.email === company.adminEmail && currentUser.isAdmin) {
          console.log('🔍 Debug: Current user is company admin, using their ID:', currentUserId);
          return currentUserId;
        }
      }

      // For non-admin users, we need to find the actual admin
      // Since we can't access /admin/all-employees, we'll try a different approach

      // Try to use the /users endpoint with API key (if available)
      try {
        const usersResponse = await getRequest('/users?apiKey=24$FrostySnow');
        console.log('🔍 Debug: Users response:', usersResponse);

        if (usersResponse?.users) {
          const admin = usersResponse.users.find((user: any) =>
            user.email === company.adminEmail && user.isAdmin
          );
          console.log('🔍 Debug: Found admin from users endpoint:', admin);

          if (admin) {
            // Note: This endpoint doesn't return user IDs, so we can't use this approach
            console.log('🔍 Debug: Users endpoint found admin but no ID available');
          }
        }
      } catch (usersError) {
        console.log('🔍 Debug: Users endpoint not accessible:', usersError);
      }
    }

    // If we can't find the actual admin ID, return null to indicate failure
    console.log('🔍 Debug: Could not determine company admin ID');
    return null;
  } catch (error) {
    console.log('🔍 Debug: Error getting company admin ID:', error);
    return null;
  }
};

// Helper function to get current user ID
const getCurrentUserId = (): string | null => {
  const primaryKey = process.env.NEXT_PUBLIC_USER_ID_KEY || "userid1";
  const altKey = process.env.NEXT_PUBLIC_USER_ID_ALT_KEY || "userId";
  return typeof window !== 'undefined'
    ? (localStorage.getItem(primaryKey) || localStorage.getItem(altKey))
    : null;
};

export async function updateUser(
  dispatch: AppDispatch,
  userId: string,
  updatedDetails: {
    name?: string;
    email?: string;
    phoneNumber?: string;
    department?: string;
    title?: string;
    dateOfBirth?: string;
    hireDate?: string;
    annualSalary?: number;
    employeeClassType?: string;
    customPayrollFrequency?: string;
    ssn?: string;
    address?: any;
    mailingAddress?: any;
    dependents?: any[];
    emergencyContact?: any;
    employeeId?: string;
    managerId?: string;
    workLocation?: string;
    workSchedule?: string;
    ein?: string;
  },
) {
  try {
    console.log('🔍 Debug: User being updated:', userId);

    // Build the request data with only the fields that have values
    const requestData: any = {
      // Remove adminId requirement
      employeeId: userId,
      updatedDetails: {
        name: updatedDetails.name,
        email: updatedDetails.email,
        details: {
          phoneNumber: updatedDetails.phoneNumber || "",
          department: updatedDetails.department || "",
          title: updatedDetails.title || "",
          role: updatedDetails.title || "", // Backend might expect role field
        },
      },
    };

    // Add optional fields only if they exist
    if (updatedDetails.dateOfBirth) {
      requestData.updatedDetails.details.dateOfBirth = updatedDetails.dateOfBirth;
    }

    if (updatedDetails.hireDate) {
      requestData.updatedDetails.details.hireDate = updatedDetails.hireDate;
    }

    if (updatedDetails.annualSalary) {
      requestData.updatedDetails.details.annualSalary = updatedDetails.annualSalary;
    }

    if (updatedDetails.employeeClassType) {
      requestData.updatedDetails.details.employeeClassType = updatedDetails.employeeClassType;
    }

    if (updatedDetails.workSchedule) {
      requestData.updatedDetails.details.workSchedule = updatedDetails.workSchedule;
    }

    if (updatedDetails.ssn) {
      requestData.updatedDetails.details.ssn = updatedDetails.ssn;
    }

    if (updatedDetails.employeeId) {
      requestData.updatedDetails.details.employeeId = updatedDetails.employeeId;
    }

    if (updatedDetails.workLocation) {
      requestData.updatedDetails.details.workLocation = updatedDetails.workLocation;
    }

    if (updatedDetails.address) {
      requestData.updatedDetails.details.address = updatedDetails.address;
    }

    if (updatedDetails.mailingAddress) {
      requestData.updatedDetails.details.mailingAddress = updatedDetails.mailingAddress;
    }

    if (updatedDetails.emergencyContact) {
      requestData.updatedDetails.details.emergencyContact = updatedDetails.emergencyContact;
    }

    // Always include dependents (even if empty array)
    requestData.updatedDetails.details.dependents = updatedDetails.dependents || [];

    console.log("Middleware - dependents being sent:", requestData.updatedDetails.details.dependents);
    console.log("Sending PUT request with cleaned data:", requestData);

    const response = await putRequest(`/admin/update/employee`, requestData);
    return response;
  } catch (error) {
    console.error("Error in updateUser middleware:", error);
    throw error;
  }
}

export async function getUserDetails(dispatch: AppDispatch, userId: string) {
  const response = await getRequest(`/employee`, {
    "user-id": userId,
  });
  dispatch(
    setUserProfile({
      name: response.currentUser.name,
      email: response.currentUser.email,
      companyId: response.currentUser.companyId,
      role: response.currentUser.role,
      isAdmin: response.currentUser.isAdmin,
      isBroker: response.currentUser.isBroker,
      details: response.currentUser.details,
    }),
  );
  return response;
}

export async function onboardAdmin(
  dispatch: AppDispatch,
  companyDetails: {
    name: string;
    adminEmail: string;
    adminRole: string;
    companySize: number;
    industry: string;
    location: string;
    website: string;
    howHeard: string;
    brokerId: string;
    brokerageId: string;
    isBrokerage: boolean;
    isActivated: boolean;
    referralSource: string;
  },
  userDetails: {
    email: string;
    name: string;
    role: string;
    isAdmin: boolean;
    isBroker: boolean;
    isActivated: boolean;
  },
) {
  const response = await postRequest(`/admin/onboard`, {
    company: {
      name: companyDetails.name,
      adminEmail: companyDetails.adminEmail,
      adminRole: companyDetails.adminRole,
      companySize: companyDetails.companySize,
      industry: companyDetails.industry,
      location: companyDetails.location,
      website: companyDetails.website,
      howHeard: companyDetails.howHeard,
      brokerId: companyDetails.brokerId,
      brokerageId: companyDetails.brokerageId,
      isBrokerage: companyDetails.isBrokerage,
      isActivated: companyDetails.isActivated,
      referralSource: companyDetails.referralSource,
      details: {
        logo: "",
      },
    },
    user: {
      email: userDetails.email,
      name: userDetails.name,
      role: userDetails.role,
      isAdmin: userDetails.isAdmin,
      isBroker: userDetails.isBroker,
      isActivated: userDetails.isActivated,
    },
  });

  const userId = response.data.userId;
  const companyId = response.data.companyId;

  // store userId and companyId locally
  localStorage.setItem("userid1", userId);
  localStorage.setItem("companyId1", companyId);

  return response;
}

export async function sendLoginLinkToEmployee(
  userId: string,
  companyId: string,
) {
  console.log("SENDING LOGIN LINK TO EMPLOYEE: ", userId, companyId);
  const response = await postRequest(`/admin/send-user-login-link`, {
    userId: userId,
    companyId: companyId,
  });
  return response;
}

export async function brokerAddsCompany(
  brokerId: string,
  companyName: string,
  companyAdminEmail: string,
  companyAdminName: string,
) {
  const response = await postRequest(`/admin/add/employer`, {
    brokerId: brokerId,
    companyName: companyName,
    companyAdminEmail: companyAdminEmail,
    companyAdminName: companyAdminName,
  });
  console.log("BROKER ADDS COMPANY RESPONSE: ", response);
  return response;
}

export async function offboardEmployee(employeeId: string, companyId: string) {
  const response = await postRequest(`/employee/offboard/`, {
    userId: employeeId,
    companyId: companyId,
  });
  if (response.status === 200) {
    return true;
  }
  return false;
}

export async function enableEmployee(employeeId: string, companyId: string) {
  const response = await postRequest(`/employee/enable/`, {
    userId: employeeId,
    companyId: companyId,
  });
  return response;
}

export async function getAllCompaniesUnderBroker(
  dispatch: AppDispatch,
  brokerId: string,
) {
  try {
    // Get client companies (companies where this user is the broker)
    const clientCompaniesResponse = await getRequest(`/admin/all-companies`);
    console.log("CLIENT COMPANIES UNDER BROKER: ", clientCompaniesResponse);

    let allCompanies = clientCompaniesResponse.companies || [];

    // Get the broker's own company details
    try {
      const ownCompanyResponse = await getRequest(`/employee/company-details`);
      console.log("BROKER'S OWN COMPANY: ", ownCompanyResponse);

      if (ownCompanyResponse.company && ownCompanyResponse.company.isBrokerage) {
        // Check if this company is already in the client companies list
        const isAlreadyIncluded = allCompanies.some(
          (company: any) => company._id === ownCompanyResponse.company._id
        );

        if (!isAlreadyIncluded) {
          // Add the broker's own company to the list
          allCompanies.unshift(ownCompanyResponse.company); // Add at the beginning
          console.log("Added broker's own company to the list");
        }
      }
    } catch (ownCompanyError) {
      console.log("Could not fetch broker's own company (this is normal if user is not a broker):", ownCompanyError);
    }

    console.log("ALL COMPANIES (CLIENT + OWN): ", allCompanies);
    dispatch(setManagedCompanies(allCompanies));

    return {
      ...clientCompaniesResponse,
      companies: allCompanies
    };
  } catch (error) {
    console.error("Error fetching companies:", error);
    dispatch(setManagedCompanies([]));
    return { companies: [] };
  }
}

export async function getCompanyDetails(dispatch: AppDispatch) {
  const response = await getRequest(`/employee/company-details`);
  dispatch(setCompanyDetails(response.company));
  return response.status;
}
