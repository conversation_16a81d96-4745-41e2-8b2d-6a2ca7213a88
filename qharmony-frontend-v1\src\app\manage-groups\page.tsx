"use client";

import { useEffect, useState } from "react";
import {
  <PERSON>po<PERSON>,
  Box,
  Grid,
  Paper,
  Divider,
  Button,
  Modal,
  TextField,
} from "@mui/material";
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";
import { Router } from "next/router";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { getUsersCompanyId } from "@/redux/reducers/userSlice";
import { createGroup, getAllGroups } from "@/middleware/group_middleware";

interface GroupType {
  _id: string;
  companyId: string;
  name: string;
  document_ids: string[];
  employee_ids: string[];
}

const ManageBenefitsDisplay = () => {
  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));
  const [open, setOpen] = useState(false);
  const [groups, setGroups] = useState<GroupType[]>([]);
  const [groupName, setGroupName] = useState("");
  const router = useRouter();

  useEffect(() => {
    const fetchGroups = async () => {
      const response = await getAllGroups(companyId);
      console.log("data >>", response);
      setGroups(response);
    };
    fetchGroups();
  }, []);

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    setGroupName("");
  };

  const handleCreateGroup = async () => {
    const groupId = await createGroup(companyId, groupName);
    router.push(`/group/${groupId}`);
    handleClose();
  };

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6FA",
          px: 4,
          py: 2,
          width: "100%",
          height: "95vh",
          overflow: "auto",
        }}
      >
        {/* Header with Button */}
        <Box
          sx={{
            display: "flex",
            // justifyContent: "space-between",
            gap: "32px",
            alignItems: "center",
            mb: 4,
            mt: 5,
          }}
        >
          <Typography
            sx={{
              fontWeight: 600,
              fontSize: "34px",
              color: "black",
              lineHeight: "41px",
              textAlign: "left",
            }}
          >
            Groups
          </Typography>

          {/* Right Aligned Button */}
          <Button
            variant="contained"
            onClick={handleOpen} // Opens modal
            sx={{
              textTransform: "none",
              borderRadius: "6px",
              bgcolor: "white",
              color: "black",
              boxShadow: "none",
              width: "140px",
              paddingY: "10px",
              paddingX: "16px",
              border: "1px solid #D2D2D2",
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
                boxShadow: "none",
              },
            }}
          >
            + Create Group
          </Button>
        </Box>

        {/* White background for group list */}
        <Paper
          sx={{
            bgcolor: "#ffffff",
            borderRadius: "12px",
            marginBottom: 9,
            boxShadow: "none",
          }}
        >
          {/* Table Headers */}
          <Grid container sx={{ borderBottom: "1px solid #E0E0E0", background: "#F0F0F0", borderTopLeftRadius: "12px", borderTopRightRadius: "12px" }}>
            <Grid item xs={2}>
              <Typography
                variant="body2"
                sx={{ fontWeight: 600, color: "#B0B0B0", py: 1, px: 3 }}
              >
                S NO
              </Typography>
            </Grid>
            <Grid item xs={10}>
              <Typography
                variant="body2"
                sx={{ fontWeight: 600, color: "#B0B0B0", py: 1 }}
              >
                GROUP NAME
              </Typography>
            </Grid>
          </Grid>

          {/* Group Rows */}
          {groups && groups.length > 0 ? (
            groups?.map((group: GroupType, index: number) => (
              <Box
                key={index}
                sx={{
                  transition: "background-color 0.3s ease",
                  "&:hover": { bgcolor: "#f0f0f0", cursor: "pointer" },
                  borderBottom: index === groups.length - 1 ? "none" : "1px solid #E0E0E0",
                }}
                onClick={() => router.push(`/group/${group._id}`)}
              >
                <Grid
                  container
                  alignItems="center"
                  sx={{
                    py: 1,
                    borderRadius: "8px",
                  }}
                >
                  <Grid item xs={2} sx={{ py: 1, px: 3 }}>
                    <Typography
                      sx={{
                        fontWeight: "500",
                        fontSize: "17px",
                        color: "black",
                      }}
                    >
                      {index + 1}
                    </Typography>
                  </Grid>
                  <Grid item xs={10}>
                    <Typography
                      sx={{
                        fontWeight: "500",
                        fontSize: "17px",
                        color: "black",
                        textAlign: "left",
                      }}
                    >
                      {group.name}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            ))
          ) : (
            <Box
              sx={{ display: "flex", justifyContent: "center", minWidth: "100%", py: 2 }}
            >
              <Typography>No Groups Found</Typography>
            </Box>
          )}
        </Paper>
      </Box>

      {/* Modal for Creating Group */}
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            bgcolor: "#F5F6FA", // Matching the background color
            boxShadow: 24,
            p: 4,
            borderRadius: "12px",
            width: 400,
          }}
        >
          <Typography
            id="modal-title"
            variant="h6"
            sx={{ mb: 2, color: "black" }}
          >
            Create New Group
          </Typography>

          <TextField
            fullWidth
            label="Enter Group Name"
            variant="outlined"
            value={groupName}
            onChange={(e) => setGroupName(e.target.value)}
            sx={{
              mb: 3,
              bgcolor: "white", // White input field
              borderRadius: "8px",
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: "rgba(0, 0, 0, 0.2)", // Light gray border
                },
                "&:hover fieldset": {
                  borderColor: "rgba(0, 0, 0, 0.4)", // Darker gray on hover
                },
              },
            }}
          />

          <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
            <Button
              onClick={handleClose}
              sx={{
                textTransform: "none",
                color: "black",
                bgcolor: "rgba(0, 0, 0, 0.06)",
                borderRadius: "8px",
                "&:hover": { bgcolor: "rgba(0, 0, 0, 0.1)" },
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              sx={{
                textTransform: "none",
                bgcolor: "rgba(0, 0, 0, 0.06)",
                color: "black",
                borderRadius: "8px",
                "&:hover": { bgcolor: "rgba(0, 0, 0, 0.1)" },
              }}
              onClick={handleCreateGroup}
            >
              Save
            </Button>
          </Box>
        </Box>
      </Modal>
    </ProtectedRoute>
  );
};

export default withSidebar(ManageBenefitsDisplay);
