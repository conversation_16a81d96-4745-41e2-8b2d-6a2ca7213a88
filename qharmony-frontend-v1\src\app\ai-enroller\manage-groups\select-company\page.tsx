'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { HiOutlineSearch, HiOutlineOfficeBuilding } from 'react-icons/hi';
import { CompanyCard } from '../components/CompanyCard';
import ProtectedRoute from '@/components/ProtectedRoute';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { getAllCompaniesUnderBroker } from '@/middleware/company_middleware';

interface Company {
  _id: string;
  companyName: string;
  ein: string;
  location: string;
  companySize: number;
  status: 'active' | 'pending' | 'inactive';
}

export default function SelectCompanyPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  const managedCompanies = useSelector((state: RootState) => state.user.managedCompanies);

  const fetchCompanies = useCallback(async () => {
    try {
      // Only access localStorage in browser environment
      if (typeof window !== 'undefined') {
        const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
        if (userId) {
          await getAllCompaniesUnderBroker(dispatch, userId);
        } else {
          console.error('User ID not found. Please authenticate first.');
        }
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setLoading(false);
    }
  }, [dispatch]);

  useEffect(() => {
    fetchCompanies();
  }, [fetchCompanies]);

  // Transform managed companies to match our interface
  const companies: Company[] = managedCompanies?.map(company => ({
    _id: company._id,
    companyName: company.name,
    ein: company.ein || 'N/A',
    location: company.location || 'N/A',
    companySize: company.companySize || 0,
    status: company.isActivated ? 'active' : 'pending'
  })) || [];

  const filteredCompanies = companies.filter(company =>
    company.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.ein.includes(searchTerm) ||
    company.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleManagePlans = (companyId: string) => {
    router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading companies...</p>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center space-x-2 text-sm">
            <button
              onClick={() => router.push('/ai-enroller')}
              className="text-black hover:text-gray-700 font-medium"
            >
              Home
            </button>
            <span className="text-gray-400">›</span>
            <span className="text-gray-600 font-medium">Select Company</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8 bg-white">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Choose an Employer Group</h1>
          <p className="text-gray-600">Select a company to manage their benefit plans</p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <HiOutlineSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search by company name, EIN, or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-black focus:border-black text-sm bg-white"
            />
          </div>
        </div>

        {/* Companies Grid */}
        {filteredCompanies.length === 0 ? (
          <div className="text-center py-12">
            <HiOutlineOfficeBuilding className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No companies found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Try adjusting your search terms.' : 'No companies available.'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCompanies.map((company) => (
              <CompanyCard
                key={company._id}
                company={company}
                onManagePlans={handleManagePlans}
              />
            ))}
          </div>
        )}
      </div>
      </div>
    </ProtectedRoute>
  );
}
