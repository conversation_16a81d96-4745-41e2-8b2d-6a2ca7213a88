import { getRequest, postRequest } from "@/APILayer/axios_helper";

export async function createGroup(companyId: string, name: string) {
  const response = await postRequest("/group", { companyId, name });

  if (response.status === 201) {
    return response.data.groupId;
  } else {
    console.error("Error Creating Group");
    return;
  }
}

export async function getAllGroups(companyId: string) {
  try {
    const response = await getRequest(`/groups/${companyId}`);
    return response.groups;
  } catch (error) {
    console.error("Error getting Groups");
    return;
  }
}
