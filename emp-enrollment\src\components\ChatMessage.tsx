
import React from 'react';
import { Bot, User } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface ChatMessageProps {
  sender: 'bot' | 'user';
  content: React.ReactNode;
  timestamp: Date;
}

export const ChatMessage = ({ sender, content, timestamp }: ChatMessageProps) => {
  const isBot = sender === 'bot';

  return (
    <div className={`flex gap-3 ${isBot ? 'justify-start' : 'justify-end'}`}>
      {isBot && (
        <Avatar className="w-8 h-8">
          <AvatarFallback className="bg-blue-100 dark:bg-blue-900">
            <Bot className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </AvatarFallback>
        </Avatar>
      )}
      
      <div className={`max-w-[80%] ${isBot ? 'order-2' : 'order-1'}`}>
        <div
          className={`rounded-2xl px-4 py-3 ${
            isBot
              ? 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
              : 'bg-blue-500 text-white'
          }`}
        >
          {content}
        </div>
        <div className={`text-xs text-muted-foreground mt-1 ${isBot ? 'text-left' : 'text-right'}`}>
          {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      </div>

      {!isBot && (
        <Avatar className="w-8 h-8">
          <AvatarFallback className="bg-green-100 dark:bg-green-900">
            <User className="w-4 h-4 text-green-600 dark:text-green-400" />
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};
