'use client';

import { Box, Container, Typography, Link as MuiLink, useMediaQuery, useTheme } from '@mui/material';

const Footer = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box 
      component="footer" 
      sx={{ 
        bgcolor: '#000', 
        color: '#f0f0f0', 
        py: { xs: 2, sm: 3 }, 
        px: { xs: 1, sm: 2 }, 
        mt: { xs: 6, sm: 10 }
      }}
    >
      <Container maxWidth="md">
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'center',
            alignItems: 'center',
            flexWrap: 'wrap',
            textAlign: 'center',
            gap: { xs: 1, sm: 2 }
          }}
        >
          {/* © Text */}
          <Typography 
            variant="body2" 
            sx={{ 
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              order: { xs: 1, sm: 1 },
              color: '#888' // Grey color
            }}
          >
            © BenOsphere 2025
          </Typography>

          {/* Links Container */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 1, sm: 2 },
              order: { xs: 3, sm: 2 }
            }}
          >
            {/* Terms */}
            <MuiLink
              href="https://benosphere.com/terms-conditions"
              target="_blank"
              rel="noopener noreferrer"
              sx={{
                color: '#888', // Grey color
                textDecoration: 'none',
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              Terms of Service
            </MuiLink>

            {/* Privacy */}
            <MuiLink
              href="https://benosphere.com/privacy-policy"
              target="_blank"
              rel="noopener noreferrer"
              sx={{
                color: '#888', // Grey color
                textDecoration: 'none',
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              Privacy Policy
            </MuiLink>

            {/* Email */}
            <MuiLink
              href="mailto:<EMAIL>"
              sx={{
                color: '#888', // Grey color
                textDecoration: 'none',
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              <EMAIL>
            </MuiLink>
          </Box>

          {/* With Love */}
          <Typography 
            variant="body2" 
            sx={{ 
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              order: { xs: 2, sm: 3 },
              color: '#888' // Grey color
            }}
          >
            🤍 With love from Seattle
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
