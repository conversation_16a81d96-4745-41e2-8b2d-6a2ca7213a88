import { isSignInWithEmailLink, signInWithEmailLink } from 'firebase/auth';
import { auth } from './firebase';
import { auth as adminAuth } from './firebase-admin';
import logger from '../utils/logger';
import crypto from 'crypto-js';
import nodemailer from 'nodemailer';

export class AuthService {
  async sendOnboardingMagicLink(
    email: string,
    companyDetails?: Record<string, any>,
    userDetails?: Record<string, any>,
    additionalParams?: Record<string, any>
  ): Promise<void> {
    const finalParams = {
      companyDetails: JSON.stringify(companyDetails),
      userDetails: JSON.stringify(userDetails),
      additionalParams: JSON.stringify(additionalParams),
    };

    const encryptedParams = crypto.AES.encrypt(
      JSON.stringify(finalParams),
      'my-secret-key'
    ).toString();
    const actionCodeSettings = {
      url: `http://localhost:3000/onboard?params=${encodeURIComponent(
        encryptedParams
      )}`,
      handleCodeInApp: true,
    };

    const link = await adminAuth.generateSignInWithEmailLink(
      email,
      actionCodeSettings
    );
    logger.info(`Generated sign-in magic link: ${link}`);

    // Send email using Nodemailer
    await this.sendEmail(email, link);
  }

  async sendLoginMagicLink(
    email: string,
    additionalParams?: Record<string, any>
  ): Promise<void> {
    if (!additionalParams) {
      additionalParams = {};
    }
    const encryptedParams = crypto.AES.encrypt(
      JSON.stringify(additionalParams),
      'my-secret-key'
    ).toString();
    const actionCodeSettings = {
      url: `http://localhost:3000/login?params=${encodeURIComponent(
        encryptedParams
      )}`,
      handleCodeInApp: true,
    };

    const link = await adminAuth.generateSignInWithEmailLink(
      email,
      actionCodeSettings
    );
    logger.info(`Generated sign-in magic link: ${link}`);

    // Send email using Nodemailer
    await this.sendEmail(email, link);
  }

  async signInWithMagicLink(
    email: string,
    link: string
  ): Promise<Record<string, string> | null> {
    try {
      if (!isSignInWithEmailLink(auth, link)) {
        throw new Error('Invalid magic link');
      }
      await signInWithEmailLink(auth, email, link);
      console.log('Signed in with magic link');
      return this.parseAdditionalParams(link);
    } catch (error) {
      logger.error('Error signing in with magic link:', error);
      return null;
    }
  }

  parseAdditionalParams(link: string): Record<string, any> {
    const url = new URL(link);
    const encryptedParams = url.searchParams.get('params');
    if (!encryptedParams) {
      return {};
    }
    const decryptedParams = crypto.AES.decrypt(
      encryptedParams,
      'my-secret-key'
    ).toString(crypto.enc.Utf8);
    const params = JSON.parse(decryptedParams);

    // Parse nested JSON strings
    if (params.companyDetails) {
      params.companyDetails = JSON.parse(params.companyDetails);
    }
    if (params.userDetails) {
      params.userDetails = JSON.parse(params.userDetails);
    }
    if (params.additionalParams) {
      params.additionalParams = JSON.parse(params.additionalParams);
    }

    return params;
  }

  async generateSignInMagicLink(
    email: string,
    companyDetails?: Record<string, any>,
    userDetails?: Record<string, any>,
    additionalParams?: Record<string, any>
  ): Promise<void> {
    const finalParams = {
      companyDetails: JSON.stringify(companyDetails),
      userDetails: JSON.stringify(userDetails),
      additionalParams: JSON.stringify(additionalParams),
    };

    const encryptedParams = crypto.AES.encrypt(
      JSON.stringify(finalParams),
      'my-secret-key'
    ).toString();
    const actionCodeSettings = {
      url: `http://localhost:3000/onboard?params=${encodeURIComponent(
        encryptedParams
      )}`,
      handleCodeInApp: true,
    };

    const link = await adminAuth.generateSignInWithEmailLink(
      email,
      actionCodeSettings
    );
    logger.info(`Generated sign-in magic link: ${link}`);
  }

  async sendEmail(email: string, link: string): Promise<void> {
    const transporter = nodemailer.createTransport({
      host: 'smtp.zoho.com',
      port: 465,
      secure: true, // true for 465, false for other ports
      auth: {
        user: '<EMAIL>',
        pass: 'e5zz_Mka',
      },
    });

    const mailOptions = {
      from: '<EMAIL>', // Updated to match the SMTP user
      to: email,
      subject: 'Your Magic Link to Sign In',
      html: `Hey there,<br><br>
Use the link below to log in and explore your benefits through BenOsphere:<br>
<a href="${link}">BenOsphere Magic Link</a><br><br>
With BenOsphere, you'll:<br>
-Receive benefits support round the clock<br>
-Maximize your benefits utilization<br>
-Stay connected through Slack or MS Teams<br><br>
If this wasn't initiated by you, you can safely ignore this email.<br><br>
Best,<br>
Brea - Your Benefits Advisor, 24/7`,
    };

    await transporter.sendMail(mailOptions);
    console.log('Magic link sent to email');
  }
}