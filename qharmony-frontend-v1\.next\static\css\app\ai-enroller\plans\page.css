/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/ai-enroller/create-plan/create-plan.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/* Create Plan Page Styles */
:root {
  --primary-gradient: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
}

.create-plan-wrapper {
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.create-plan-page {
  max-width: min(85%, 1400px);
  margin: 0 auto;
  background: white;
  min-height: 100vh;
}

/* New Progress Header */
.progress-header {
  background: white;
  padding: 1.5rem 2rem 0;
  border-bottom: 0px solid #e5e7eb;
}

.progress-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  margin-left: clamp(2rem, 5%, 4rem); /* Responsive left margin */
  margin-right: clamp(2rem, 5%, 4rem); /* Responsive right margin */
  width: calc(100% - clamp(4rem, 10%, 8rem)); /* Responsive width calculation */
}

/* Progress title uses design system classes */
.progress-counter {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.progress-bar-container {
  width: calc(100% - clamp(4rem, 10%, 8rem)); /* Responsive width calculation */
  margin-left: clamp(2rem, 5%, 4rem); /* Responsive left margin */
  margin-right: clamp(2rem, 5%, 4rem); /* Responsive right margin */
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.progress-bar-fill {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Page Navigation */
.page-navigation {
  display: flex;
  gap: 0.75rem;
  padding: 0rem 2rem 1rem;
  padding-left: clamp(3rem, 7%, 6rem); /* Responsive alignment with progress bar */
  background: white;
  overflow-x: auto;
}

.page-nav-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: none;
  border-radius: 1.5rem;
  cursor: pointer;
  font-size: 13px;
  font-weight: 450;
  line-height: 1.2;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #6b7280;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.page-nav-item.active {
  background: #ede9fe; /* Light purple (matching icon scheme) */
  color: #7c3aed; /* Purple (matching gradient) */
}

.page-nav-item.completed {
  background: #ddd6fe; /* Light purple background (lighter than active) */
  color: #6d28d9; /* Darker purple text */
}

.page-nav-item.completed::after {
  content: '✓';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  background: #7c3aed; /* Purple matching gradient */
  border-radius: 50%;
  margin-left: 0.5rem;
  font-size: 0.625rem;
  font-weight: 600;
  color: white;
  line-height: 1;
}

.page-nav-item:hover:not(.active):not(.completed) {
  background: #e5e7eb;
  color: #374151;
}

/* Header */
.create-plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 3vw, 1.5rem);
  border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
  color: #374151;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  font-weight: 500;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.auto-save-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: clamp(0.75rem, 2vw, 0.875rem);
  color: #059669;
  font-weight: 500;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.save-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #059669;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.save-checkmark {
  color: #059669;
  font-weight: 600;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* AI Assistant Message */
.ai-assistant-message {
  background: #dbeafe00;
  padding: 1.5rem 2rem 1rem 2rem;
  margin: 0;
}

.ai-message-content {
  display: flex;
  align-items: flex-start;
  gap: 0rem;
  max-width: 1200px;
  margin: 0 auto;
}

.chat-bubble {
  background: white;
  border-radius: 1rem;
  padding: 1.25rem 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  margin-left: 1rem;
  margin-bottom: 0.5rem;
  flex: 1;
}

.chat-bubble::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 1rem;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid white;
}

.ai-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 3rem;
  height: 3rem;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.brea-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-text {
  flex: 1;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-greeting {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.ai-text p {
  font-size: 0.9rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.chat-message {
  font-size: 0.875rem;
  color: #1f2937;
  line-height: 1.6;
  margin-bottom: 0.75rem;
}

.chat-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.saved-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: #dcfce7;
  color: #166534;
  padding: 0.25rem 0.75rem;
  border-radius: 0.5rem;
  font-size: clamp(0.75rem, 1.8vw, 0.875rem);
  font-weight: 600;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main Content Area */
.main-content {
  max-width: min(90%, 1600px);
  margin: 0 auto;
  padding: 0.5rem clamp(1rem, 3%, 2rem) 2rem clamp(1rem, 3%, 2rem);
  background: white;
}

.form-container {
  background: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-left: 4rem;
  position: relative;
}

.form-container::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 1.5rem;
  width: 0.5rem;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 50%;
}

/* Form Section */
.form-section {
  background: white;
  display: flex;
  flex-direction: column;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 1.5rem 0;
  border-bottom: 1px solid #e5e7eb00;
  margin-bottom: 0.5rem;
}

.form-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #8b5cf6;
}

.form-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-assist-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 0.625rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ai-assist-btn:hover {
  background: var(--primary-gradient);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
  filter: brightness(1.1);
}

/* Form Content */
.form-content {
  width : 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Tooltip Styles */
.tooltip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 0.7rem;
  height: 0.7rem;
  border: 1px solid #9ca3af;
  border-radius: 50%;
  font-size: 0.625rem;
  font-weight: 500;
  color: #6b7280;
  cursor: help;
  transition: all 0.2s ease;
  position: relative;
  margin-left: 0rem;
}

.tooltip-icon::after {
  content: 'i';
}

.tooltip-icon:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #eff6ff;
}

/* Hide default tooltips - we'll use JavaScript to create them */
.tooltip-icon[data-tooltip]:hover::before,
.tooltip-icon[data-tooltip]:hover::after {
  display: none;
}

/* Custom tooltip styles for JavaScript-created tooltips */
.custom-tooltip {
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}



.field-hint {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
  margin-top: 0.25rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  transform: translateY(-1px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #9ca3af;
}

/* Navigation */
.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0 0 0;
  margin-top: 1rem;
  gap: 1rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-width: 140px;
  justify-content: center;
}

.nav-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-btn.secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.nav-btn.primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
}

.nav-btn.primary.enabled:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.nav-btn.primary.disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
}

/* Step 2 Specific Styles */
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.2s ease;
  resize: vertical;
  min-height: 100px;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.form-group textarea:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.highlight-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.highlight-input input {
  flex: 1;
}

.remove-highlight {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
}

.remove-highlight:hover {
  background: #dc2626;
}

.add-highlight {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px dashed #d1d5db;
  border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 3vw, 1.25rem);
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 0.75rem;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  justify-content: center;
}

.add-highlight:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* File Upload Styles */
.file-upload-area {
  position: relative;
  margin-top: 0.5rem;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 2rem;
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  background: #f9fafb;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.file-upload-label:hover {
  border-color: #8b5cf6;
  background: #faf5ff;
  color: #8b5cf6;
}

.file-upload-label span {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.file-upload-label small {
  font-size: 0.75rem;
  color: #6b7280;
}

.uploaded-files {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.uploaded-files h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
}

.uploaded-file:last-child {
  margin-bottom: 0;
}

.file-name {
  flex: 1;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.file-size {
  font-size: 0.75rem;
  color: #6b7280;
}

.remove-file {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.remove-file:hover {
  background: #dc2626;
}

/* Step 3 Specific Styles */
.input-with-prefix {
  position: relative;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  z-index: 1;
}

.input-with-prefix input {
  padding-left: 2rem;
}

/* Step 4 Review Styles */
.ready-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #dcfce7;
  color: #166534;
  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.75rem, 2vw, 1rem);
  border-radius: 1rem;
  font-size: clamp(0.7rem, 1.8vw, 0.8rem);
  font-weight: 600;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.review-content {
  padding: clamp(0.5rem, 0.25rem, 1rem);
  display: flex;
  flex-direction: column;
  gap: clamp(0.75rem, 2vw, 1.5rem);
}

.review-section {
  background: #f8fafc;
  border-radius: clamp(0.75rem, 2vw, 1rem);
  padding: clamp(1rem, 3vw, 1.5rem);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.review-section:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.review-section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.25rem;
  color: #3b82f6;
}

.review-section-header h4 {
  font-size: clamp(0.9rem, 2.5vw, 1.125rem);
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.review-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.review-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.review-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.review-value {
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 600;
}

.review-value.metal-tier {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
}

.review-value.plan-code {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 0.75rem;
  font-weight: 700;
}

.review-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  word-break: break-all;
}

.review-link:hover {
  text-decoration: underline;
}

.review-documents {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
}

.review-document {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
}

.review-document span {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.review-document small {
  font-size: 0.75rem;
  color: #6b7280;
}

.review-description {
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  background: hsl(0, 0%, 93%);
  padding: 0.7rem;
  border-radius: 0.5rem;

}

.review-highlights {
  margin: 0;
  padding: 0;
  list-style: none;
  background: hsl(0, 0%, 93%);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 0.7rem;
}

.review-highlights li {
  font-size: 0.875rem;
  color: #374151;
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.review-highlights li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #059669;
  font-weight: bold;
}

.create-confirmation {
  background-color: rgb(246, 237, 255);              /* bg-purple-50 */
  border: 1px solid rgb(220, 189, 253);    
  color: rgb(147, 7, 247);
  border-radius: 1rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
  gap: 1rem;
}

.confirmation-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  flex-shrink: 0;
}

.confirmation-text h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.confirmation-text p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
  line-height: 1.5;
}

/* Step 5 Success Styles */
.success-section {
  text-align: center;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-radius: 1rem;
  margin: 1rem;
}

.success-content {
  padding: 3rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.success-icon {
  background: #dcfce7;
  color: #166534;
  border-radius: 50%;
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.success-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.success-content p {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
  max-width: 400px;
}

.success-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 300px;
}

/* Plan Details Card */
.plan-details-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: clamp(0.75rem, 2vw, 1rem);
  padding: clamp(1.5rem, 4vw, 2rem);
  margin: clamp(1.5rem, 4vw, 2rem) 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  max-width: 500px;
  width: 100%;
}

.plan-details-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  color: #3b82f6;
  border-bottom: 1px solid #f1f5f9;
  padding-bottom: 1rem;
}

.plan-details-header h4 {
  font-size: clamp(1rem, 2.5vw, 1.125rem);
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.plan-details-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.plan-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f8fafc;
}

.plan-detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #64748b;
  font-weight: 500;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.detail-value {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #1e293b;
  font-weight: 600;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.detail-value.plan-id {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
}

.detail-value.plan-code {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 700;
}

.detail-value.status-active {
  background: #dcfce7;
  color: #166534;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 600;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .create-plan-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 1rem;
  }

  .progress-header {
    padding: 1rem;
  }

  .page-navigation {
    overflow-x: auto;
  }

  .ai-assistant-message {
    padding: 0 0 0 0rem;
  }

  .content-section {
    padding: 1rem;
  }

  .form-container {
    margin-left: 0;
  }

  .form-container::before {
    display: none;
  }

  .main-content {
    padding: 0.5rem 1rem 2rem 1rem;
  }

  .form-navigation {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-btn {
    width: 100%;
    min-width: auto;
  }

  .ai-assist-notification {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
  }

  .review-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .create-confirmation {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .success-actions {
    width: 100%;
  }

  .ai-assistant-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .ai-greeting {
    justify-content: center;
  }

  .plan-details-card {
    max-width: 100%;
  }

  .plan-detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    text-align: left;
  }

  .detail-value {
    max-width: 100%;
    text-align: left;
    word-break: break-word;
  }
}

@media (max-width: 480px) {
  .progress-steps {
    padding: 1rem;
  }

  .step-title,
  .step-subtitle {
    white-space: normal;
    text-align: center;
  }

  .highlight-input {
    flex-direction: column;
    gap: 0.75rem;
  }

  .remove-highlight {
    align-self: flex-end;
  }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
  .form-content {
    grid-template-columns: repeat(2, 1fr);
    max-width: min(70%, 1000px);
    margin: 0 auto;
  }

  .form-group:last-child {
    grid-column: 1 / -1;
  }

  .review-content {
    max-width: min(80%, 1200px);
    margin: 0 auto;
  }
}

/* Large screen optimizations */
@media (min-width: 1600px) {
  .create-plan-page {
    max-width: min(90%, 1800px);
  }

  .form-content {
    grid-template-columns: repeat(3, 1fr);
    max-width: min(75%, 1400px);
  }

  .main-content {
    max-width: min(85%, 1800px);
    padding: 0.5rem clamp(2rem, 4%, 3rem) 2rem clamp(2rem, 4%, 3rem);
  }
}

/* Ultra-wide screen optimizations */
@media (min-width: 2000px) {
  .create-plan-page {
    max-width: min(80%, 2000px);
  }

  .form-content {
    grid-template-columns: repeat(4, 1fr);
    max-width: min(70%, 1600px);
  }

  .main-content {
    max-width: min(80%, 2000px);
    padding: 0.5rem clamp(3rem, 5%, 4rem) 2rem clamp(3rem, 5%, 4rem);
  }
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/ai-enroller/plans/plans.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/* Plans Management Page Styles */
:root {
  --primary-gradient: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
}

.plans-page {
  max-width: min(95vw, 1400px);
  margin: 0 auto;
  padding: clamp(1rem, 4vw, 3rem);
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Header */
.plans-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
  gap: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.back-btn, .create-btn, .data-manager-btn, .ask-questions-btn, .dashboard-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: none;
}

.back-btn {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.create-btn {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.25);
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.ask-questions-btn {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.ask-questions-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.dashboard-btn {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.25);
}

.dashboard-btn:hover {
  background: var(--primary-gradient);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
  filter: brightness(1.1);
}

.data-manager-btn {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.25);
}

.data-manager-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(5, 150, 105, 0.4);
}

/* Page Title */
.page-title {
  text-align: left;
  margin-bottom: clamp(2rem, 5vw, 3rem);
}

.page-title h1 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-title p {
  font-size: 14px;
  line-height: 21px;
  color: #64748b;
  margin: 0;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 200px), 1fr));
  gap: clamp(1rem, 3vw, 1.5rem);
  margin-bottom: clamp(2rem, 5vw, 3rem);
}

.stat-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: clamp(0.75rem, 2vw, 1rem);
  padding: clamp(1.5rem, 4vw, 2rem);
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  width: clamp(2.5rem, 5vw, 3rem);
  height: clamp(2.5rem, 5vw, 3rem);
  background: #f0f9ff;
  color: #3b82f6;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon.active {
  background: #dcfce7;
  color: #166534;
}

.stat-icon.recent {
  background: #fef3c7;
  color: #d97706;
}

.stat-number {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.stat-label {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #64748b;
  font-weight: 500;
}

/* Search and Filter */
.search-filter-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 1rem;
}

.search-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.search-input {
  flex: 1;
  min-width: 300px;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.2s ease;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.status-filter,
.carrier-filter {
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-width: 140px;
}

.status-filter:focus,
.carrier-filter:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-filters-btn {
  padding: 0.75rem 1rem;
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.clear-filters-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.results-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Plans Table */
.plans-table-container {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.table-header h3 {
  font-size: clamp(18px, 4vw, 20px);
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.table-wrapper {
  overflow-x: auto;
}

.plans-table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.plans-table thead th {
  background: #f8fafc;
  padding: 1rem 1.5rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: #475569;
  border-bottom: 1px solid #e2e8f0;
  white-space: nowrap;
}

.plans-table tbody td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.plans-table tbody tr:hover {
  background: #f8fafc;
}

.plan-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.plan-code-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  border: 1px solid #e2e8f0;
}

.carrier-type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.carrier-type-badge.health,
.carrier-type-badge.medical {
  background: #dcfce7;
  color: #166534;
}

.carrier-type-badge.dental {
  background: #fef3c7;
  color: #92400e;
}

.carrier-type-badge.vision {
  background: #dbeafe;
  color: #1e40af;
}

.carrier-type-badge.life {
  background: #f3e8ff;
  color: #7c3aed;
}

.carrier-type-badge.your-health {
  background: #dbeafe;
  color: #1e40af;
}

/* Default for unknown types */
.carrier-type-badge:not(.health):not(.medical):not(.dental):not(.vision):not(.life):not(.your-health) {
  background: #f1f5f9;
  color: #475569;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge.draft {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.template {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.archived {
  background: #fee2e2;
  color: #dc2626;
}

.groups-count {
  font-weight: 600;
  color: #3b82f6;
  font-size: 0.875rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.action-btn {
  width: 2rem;
  height: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn.edit:hover {
  color: #3b82f6;
  border-color: #3b82f6;
  background: #eff6ff;
}

.action-btn.copy:hover {
  color: #059669;
  border-color: #059669;
  background: #ecfdf5;
}

.action-btn.activate:hover {
  color: #16a34a;
  border-color: #16a34a;
  background: #f0fdf4;
}

.action-btn.deactivate:hover {
  color: #ea580c;
  border-color: #ea580c;
  background: #fff7ed;
}

.action-btn.delete:hover {
  color: #dc2626;
  border-color: #dc2626;
  background: #fef2f2;
}

/* Tooltip styles for action buttons */
.action-btn::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 10;
  margin-bottom: 0.25rem;
}

.action-btn:hover::after {
  opacity: 1;
}

/* Empty State */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: clamp(3rem, 8vw, 5rem) clamp(2rem, 5vw, 3rem);
  background: white;
  border-radius: clamp(0.75rem, 2vw, 1rem);
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.empty-state svg {
  color: #d1d5db;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: clamp(1.125rem, 3vw, 1.25rem);
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.empty-state p {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
  line-height: 1.6;
  margin: 0 0 2rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.create-first-plan-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  border: none;
  padding: clamp(0.75rem, 2.5vw, 1rem) clamp(1.5rem, 4vw, 2rem);
  border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
}

.create-first-plan-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

/* Plan Cards */
.plan-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: clamp(0.75rem, 2vw, 1rem);
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #8b5cf6;
}

.plan-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: clamp(1.5rem, 4vw, 2rem);
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #fafbff 0%, #f8fafc 100%);
}

.plan-info h3 {
  font-size: clamp(1rem, 2.5vw, 1.125rem);
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.plan-meta {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.plan-code {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

.plan-status {
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 600;
-transform: uppercase;
}

.plan-status.active {
  background: #dcfce7;
  color: #166534;
}

.plan-status.inactive {
  background: #fee2e2;
  color: #dc2626;
}

.plan-status.draft {
  background: #fef3c7;
  color: #d97706;
}

.plan-status.archived {
  background: #fee2e2;
  color: #dc2626;
}

.plan-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 2rem;
  height: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.action-btn.view:hover {
  color: #3b82f6;
  border-color: #3b82f6;
}

.action-btn.duplicate:hover {
  color: #059669;
  border-color: #059669;
}

.action-btn.delete:hover {
  color: #dc2626;
  border-color: #dc2626;
}

/* Plan Card Content */
.plan-card-content {
  padding: clamp(1.5rem, 4vw, 2rem);
}

.plan-description {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.plan-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.plan-detail {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  color: #9ca3af;
  font-weight: 500;
-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #374151;
  font-weight: 600;
}

.plan-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.highlight-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 500;
}

.highlight-more {
  background: #e2e8f0;
  color: #64748b;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 500;
}

/* Plan Card Footer */
.plan-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(1rem, 3vw, 1.5rem) clamp(1.5rem, 4vw, 2rem);
  border-top: 1px solid #f1f5f9;
  background: #fafbfc;
}

.activation-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: clamp(0.75rem, 2vw, 0.8rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.activation-btn.active {
  background: #dcfce7;
  color: #166534;
}

.activation-btn.active:hover {
  background: #bbf7d0;
}

.activation-btn.inactive {
  background: #fee2e2;
  color: #dc2626;
}

.activation-btn.inactive:hover {
  background: #fecaca;
}

.plan-id {
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  color: #9ca3af;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-filter-section {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-select {
    min-width: auto;
  }

  .plans-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .plan-card-header {
    flex-direction: column;
    gap: 1rem;
  }

  .plan-actions {
    align-self: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .plan-details {
    grid-template-columns: 1fr;
  }

  .plan-card-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .plan-id {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .plans-list {
    grid-template-columns: 1fr;
  }

  .plan-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Loading and Error States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #dc2626;
  text-align: center;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #2563eb;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.plan-modal-content {
  max-width: 800px;
  width: 95%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modal-body {
  padding: 1.5rem;
  font-size: 14px;
  line-height: 21px;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modal-body p {
  font-size: 14px;
  line-height: 21px;
  color: #374151;
  margin: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.modal-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.modal-btn.primary {
  background: var(--primary-gradient);
  color: white;
}

.modal-btn.primary:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.modal-btn.secondary {
  background: #f9fafb;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.modal-btn.secondary:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
}

.modal-body p {
  color: #6b7280;
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
}

.modal-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-action-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.modal-action-btn:hover {
  border-color: var(--primary-blue);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

.modal-action-btn.primary:hover {
  background: #eff6ff;
}

.modal-action-btn.secondary:hover {
  background: #f8fafc;
}

.modal-action-btn h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.modal-action-btn p {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  margin-top: 1rem;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.pagination-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.pagination-btn.active {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Responsive */
@media (max-width: 768px) {
  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .modal-actions {
    gap: 0.75rem;
  }

  .modal-action-btn {
    padding: 0.75rem;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .pagination-controls {
    justify-content: center;
  }
}

