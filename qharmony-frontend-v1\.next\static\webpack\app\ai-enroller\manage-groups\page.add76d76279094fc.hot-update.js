"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/manage-groups/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/manage-groups/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/ai-enroller/manage-groups/page.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineArrowLeft_HiOutlineClipboardList_HiOutlineOfficeBuilding_HiOutlinePlus_HiOutlineUsers_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineArrowLeft,HiOutlineClipboardList,HiOutlineOfficeBuilding,HiOutlinePlus,HiOutlineUsers!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _middleware_company_middleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/middleware/company_middleware */ \"(app-pages-browser)/./src/middleware/company_middleware.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_AddNewGroupModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AddNewGroupModal */ \"(app-pages-browser)/./src/app/ai-enroller/components/AddNewGroupModal.tsx\");\n/* harmony import */ var _services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./services/planAssignmentApi */ \"(app-pages-browser)/./src/app/ai-enroller/manage-groups/services/planAssignmentApi.ts\");\n/* harmony import */ var _manage_groups_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./manage-groups.css */ \"(app-pages-browser)/./src/app/ai-enroller/manage-groups/manage-groups.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ManageGroupsPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_8__.useDispatch)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const managedCompanies = (0,react_redux__WEBPACK_IMPORTED_MODULE_8__.useSelector)((state)=>state.user.managedCompanies);\n    // Function to fetch plan assignments count for the broker\n    const fetchPlanAssignmentsCount = async (userId)=>{\n        try {\n            const API_BASE_URL = \"http://localhost:8080\" || 0;\n            console.log(\"\\uD83D\\uDD0D Fetching plan assignments count for userId:\", userId);\n            console.log(\"\\uD83D\\uDD0D API_BASE_URL:\", API_BASE_URL);\n            // Get all companies under this broker\n            const companiesResponse = await fetch(\"\".concat(API_BASE_URL, \"/admin/all-companies\"), {\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"user-id\": userId\n                }\n            });\n            console.log(\"\\uD83C\\uDFE2 Companies response status:\", companiesResponse.status);\n            if (companiesResponse.ok) {\n                const companiesData = await companiesResponse.json();\n                const companies = companiesData.companies || [];\n                console.log(\"\\uD83C\\uDFE2 Found companies:\", companies.length, companies);\n                let totalPlanAssignments = 0;\n                // For each company, fetch plan assignments using the existing API\n                for (const company of companies){\n                    try {\n                        var _assignmentsResult_data;\n                        console.log(\"\\uD83D\\uDCCB Fetching assignments for company: \".concat(company.name, \" (\").concat(company._id, \")\"));\n                        const assignmentsResult = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.getPlanAssignmentsByCompany)(company._id, {\n                            includePlanData: false\n                        });\n                        console.log(\"\\uD83D\\uDCCB Assignments result for \".concat(company.name, \":\"), assignmentsResult);\n                        if (assignmentsResult.success && ((_assignmentsResult_data = assignmentsResult.data) === null || _assignmentsResult_data === void 0 ? void 0 : _assignmentsResult_data.assignments)) {\n                            const companyAssignments = assignmentsResult.data.assignments.length;\n                            console.log(\"\\uD83D\\uDCCB Company \".concat(company.name, \" has \").concat(companyAssignments, \" assignments\"));\n                            totalPlanAssignments += companyAssignments;\n                        } else {\n                            console.warn(\"❌ Failed to fetch assignments for \".concat(company.name, \":\"), assignmentsResult.error);\n                        }\n                    } catch (error) {\n                        console.warn(\"❌ Error fetching assignments for company \".concat(company._id, \":\"), error);\n                    }\n                }\n                // Also check broker's own company\n                try {\n                    console.log(\"\\uD83C\\uDFE2 Checking broker's own company...\");\n                    const ownCompanyResponse = await fetch(\"\".concat(API_BASE_URL, \"/employee/company-details\"), {\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            \"user-id\": userId\n                        }\n                    });\n                    console.log(\"\\uD83C\\uDFE2 Own company response status:\", ownCompanyResponse.status);\n                    if (ownCompanyResponse.ok) {\n                        const ownCompanyData = await ownCompanyResponse.json();\n                        console.log(\"\\uD83C\\uDFE2 Own company data:\", ownCompanyData);\n                        if (ownCompanyData.company && ownCompanyData.company.isBrokerage) {\n                            var _assignmentsResult_data1;\n                            console.log(\"\\uD83D\\uDCCB Fetching assignments for broker's own company: \".concat(ownCompanyData.company.name, \" (\").concat(ownCompanyData.company._id, \")\"));\n                            const assignmentsResult = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.getPlanAssignmentsByCompany)(ownCompanyData.company._id, {\n                                includePlanData: false\n                            });\n                            console.log(\"\\uD83D\\uDCCB Own company assignments result:\", assignmentsResult);\n                            if (assignmentsResult.success && ((_assignmentsResult_data1 = assignmentsResult.data) === null || _assignmentsResult_data1 === void 0 ? void 0 : _assignmentsResult_data1.assignments)) {\n                                const ownCompanyAssignments = assignmentsResult.data.assignments.length;\n                                console.log(\"\\uD83D\\uDCCB Broker's own company has \".concat(ownCompanyAssignments, \" assignments\"));\n                                totalPlanAssignments += ownCompanyAssignments;\n                            } else {\n                                console.warn(\"❌ Failed to fetch broker's own company assignments:\", assignmentsResult.error);\n                            }\n                        } else {\n                            console.log(\"\\uD83C\\uDFE2 User does not have a brokerage company or company not found\");\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"❌ Failed to fetch broker's own company assignments:\", error);\n                }\n                console.log(\"✅ Total plan assignments managed:\", totalPlanAssignments);\n                setPlansManaged(totalPlanAssignments);\n            } else {\n                console.error(\"❌ Failed to fetch companies:\", companiesResponse.status);\n                setPlansManaged(0);\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching plan assignments count:\", error);\n            setPlansManaged(0);\n        }\n    };\n    // Optimized function to fetch plan assignments count using broker API\n    const fetchPlanAssignmentsCountOptimized = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D Fetching broker plan assignments count using optimized API\");\n            // Use the optimized broker count API\n            const countResult = await (0,_services_planAssignmentApi__WEBPACK_IMPORTED_MODULE_6__.getBrokerPlanAssignmentsCount)();\n            if (countResult.success && countResult.data) {\n                const totalCount = countResult.data.count;\n                console.log(\"✅ Total plan assignments managed by broker (optimized):\", totalCount);\n                setPlansManaged(totalCount);\n            } else {\n                console.warn(\"❌ Failed to fetch broker plan assignments count:\", countResult.error);\n                setPlansManaged(0);\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching plan assignments count (optimized):\", error);\n            setPlansManaged(0);\n        }\n    };\n    const fetchDashboardData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            // Only access localStorage in browser environment\n            if (true) {\n                const userId = localStorage.getItem(\"userid1\") || \"6838677aef6db0212bcfdacd\";\n                await (0,_middleware_company_middleware__WEBPACK_IMPORTED_MODULE_4__.getAllCompaniesUnderBroker)(dispatch, userId);\n                await fetchPlanAssignmentsCount(userId);\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n            setLoading(false);\n        }\n    }, [\n        dispatch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, [\n        fetchDashboardData\n    ]);\n    const handleNewGroup = ()=>{\n        setShowAddModal(true);\n    };\n    const handleExistingGroup = ()=>{\n        router.push(\"/ai-enroller/manage-groups/select-company\");\n    };\n    const handleBackToMain = ()=>{\n        router.push(\"/ai-enroller\");\n    };\n    // State for plan assignments count\n    const [plansManaged, setPlansManaged] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Calculate dashboard stats\n    const totalCompanies = (managedCompanies === null || managedCompanies === void 0 ? void 0 : managedCompanies.length) || 0;\n    const totalEmployees = (managedCompanies === null || managedCompanies === void 0 ? void 0 : managedCompanies.reduce((sum, company)=>sum + company.companySize, 0)) || 0;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"broker-dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"loading-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"loading-spinner\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"broker-dashboard\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"dashboard-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"page-title\",\n                                children: \"Broker Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"subtitle-text\",\n                                children: \"Manage employer groups and benefit plans with ease. Your one-stop solution for comprehensive benefits administration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"stats-grid\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"stat-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stat-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineClipboardList_HiOutlineOfficeBuilding_HiOutlinePlus_HiOutlineUsers_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineOfficeBuilding, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stat-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-number\",\n                                            children: totalCompanies\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-label\",\n                                            children: \"Active Companies\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"stat-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stat-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineClipboardList_HiOutlineOfficeBuilding_HiOutlinePlus_HiOutlineUsers_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineUsers, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stat-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-number\",\n                                            children: totalEmployees.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-label\",\n                                            children: \"Total Employees\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"stat-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stat-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineClipboardList_HiOutlineOfficeBuilding_HiOutlinePlus_HiOutlineUsers_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineClipboardList, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stat-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-number\",\n                                            children: plansManaged\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-label\",\n                                            children: \"Plan Assignments\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"quick-actions\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"section-header\",\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"body-text\",\n                            children: \"Choose how you'd like to get started\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"action-cards\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"action-card\",\n                                    onClick: handleNewGroup,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"action-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineClipboardList_HiOutlineOfficeBuilding_HiOutlinePlus_HiOutlineUsers_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                                                size: 32\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"action-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"section-header\",\n                                                    style: {\n                                                        fontSize: \"18px\"\n                                                    },\n                                                    children: \"New Group\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"body-text\",\n                                                    children: \"Setting up benefits for a new organization or group that hasn't had coverage before\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"action-tags\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"tag\",\n                                                            children: \"Fresh start\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"tag\",\n                                                            children: \"New enrollment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"action-card\",\n                                    onClick: handleExistingGroup,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"action-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineClipboardList_HiOutlineOfficeBuilding_HiOutlinePlus_HiOutlineUsers_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineOfficeBuilding, {\n                                                size: 32\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"action-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"section-header\",\n                                                    style: {\n                                                        fontSize: \"18px\"\n                                                    },\n                                                    children: \"Existing Group\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"body-text\",\n                                                    children: \"Adding or modifying benefits for an organization that already has some coverage in place\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"action-tags\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"tag\",\n                                                            children: \"Active enrollment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"tag\",\n                                                            children: \"Plan changes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"back-button-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"back-button\",\n                        onClick: handleBackToMain,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineClipboardList_HiOutlineOfficeBuilding_HiOutlinePlus_HiOutlineUsers_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineArrowLeft, {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Back to Main\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 7\n                }, undefined),\n                showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddNewGroupModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: showAddModal,\n                    onClose: ()=>setShowAddModal(false),\n                    onSuccess: ()=>{\n                        setShowAddModal(false);\n                        fetchDashboardData();\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\manage-groups\\\\page.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageGroupsPage, \"+RTmqi/qbKgBiakisPGzVjMx3oU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_redux__WEBPACK_IMPORTED_MODULE_8__.useDispatch,\n        react_redux__WEBPACK_IMPORTED_MODULE_8__.useSelector\n    ];\n});\n_c = ManageGroupsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageGroupsPage);\nvar _c;\n$RefreshReg$(_c, \"ManageGroupsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/manage-groups/page.tsx\n"));

/***/ })

});