# =============================================================================
# QHarmony Frontend Environment Variables - Example Configuration
# =============================================================================
# Copy this file to .env and update the values according to your environment

# =============================================================================
# API Configuration
# =============================================================================
# Backend API Base URL - REQUIRED
NEXT_PUBLIC_API_URL=http://localhost:8080

# =============================================================================
# User Configuration
# =============================================================================
# Default User Storage Keys
NEXT_PUBLIC_USER_ID_KEY=userid1
NEXT_PUBLIC_USER_ID_ALT_KEY=userId

# =============================================================================
# Admin Configuration
# =============================================================================
# QHarmony Admin Emails (comma-separated) - REQUIRED
NEXT_PUBLIC_ADMIN_EMAILS=<EMAIL>,<EMAIL>

# =============================================================================
# AI/Chatbot Service Configuration
# =============================================================================
# Chatbot API Base URL - REQUIRED
NEXT_PUBLIC_CHATBOT_URL=https://bot.benosphere.com

# =============================================================================
# External Services Configuration
# =============================================================================
# Teams Bot URLs
NEXT_PUBLIC_TEAMS_BOT_DEV_URL=https://qharmony-teams-bot-dev.azurewebsites.net
NEXT_PUBLIC_TEAMS_BOT_PROD_URL=https://benosphere.azurewebsites.net

# Finch API URL
NEXT_PUBLIC_FINCH_API_URL=https://connect.tryfinch.com

# Frontend URL (for backend integration)
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000

# =============================================================================
# Image Domains Configuration
# =============================================================================
# S3 Domain for images
NEXT_PUBLIC_S3_DOMAIN=s3.amazonaws.com

# Azure Blob Domain for images
NEXT_PUBLIC_AZURE_BLOB_DOMAIN=benosphere.blob.core.windows.net

# =============================================================================
# Firebase Configuration - Test Environment (test.benosphere.com)
# =============================================================================
NEXT_PUBLIC_FIREBASE_TEST_API_KEY=your_test_firebase_api_key_here
NEXT_PUBLIC_FIREBASE_TEST_AUTH_DOMAIN=your_test_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_TEST_PROJECT_ID=your_test_project_id
NEXT_PUBLIC_FIREBASE_TEST_STORAGE_BUCKET=your_test_project.firebasestorage.app
NEXT_PUBLIC_FIREBASE_TEST_MESSAGING_SENDER_ID=your_test_sender_id
NEXT_PUBLIC_FIREBASE_TEST_APP_ID=your_test_app_id

# =============================================================================
# Firebase Configuration - Production Environment (default)
# =============================================================================
NEXT_PUBLIC_FIREBASE_PROD_API_KEY=your_prod_firebase_api_key_here
NEXT_PUBLIC_FIREBASE_PROD_AUTH_DOMAIN=your_prod_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROD_PROJECT_ID=your_prod_project_id
NEXT_PUBLIC_FIREBASE_PROD_STORAGE_BUCKET=your_prod_project.appspot.com
NEXT_PUBLIC_FIREBASE_PROD_MESSAGING_SENDER_ID=your_prod_sender_id
NEXT_PUBLIC_FIREBASE_PROD_APP_ID=your_prod_app_id

# =============================================================================
# Development Configuration
# =============================================================================
# Enable debug logging in development
NEXT_PUBLIC_DEBUG_LOGGING=true
