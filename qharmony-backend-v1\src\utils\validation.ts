import mongoose from 'mongoose';

/**
 * Validation utilities for type safety and data integrity
 */

/**
 * Validate ObjectId format and return boolean
 * Works with string ObjectIds from frontend (URL params, JSON body, query params)
 */
export const isValidObjectId = (id: string): boolean => {
  if (!id || typeof id !== 'string') {
    return false;
  }
  // mongoose.Types.ObjectId.isValid() validates string format:
  // - Must be 24 characters long
  // - Must be valid hexadecimal (0-9, a-f, A-F)
  // - Examples: "507f1f77bcf86cd799439011" ✅, "invalid-id" ❌
  return mongoose.Types.ObjectId.isValid(id);
};

/**
 * Validate ObjectId and throw descriptive error if invalid
 */
export const validateObjectId = (id: string, fieldName: string = 'ID'): void => {
  if (!isValidObjectId(id)) {
    throw new Error(`Invalid ${fieldName} format. Must be a valid 24-character hex string.`);
  }
};

/**
 * Safely convert string to Date object
 */
export const safeParseDate = (dateInput: string | Date, fieldName: string = 'date'): Date => {
  if (dateInput instanceof Date) {
    return dateInput;
  }
  
  if (typeof dateInput === 'string') {
    const parsedDate = new Date(dateInput);
    if (isNaN(parsedDate.getTime())) {
      throw new Error(`Invalid ${fieldName} format. Must be a valid date string or Date object.`);
    }
    return parsedDate;
  }
  
  throw new Error(`Invalid ${fieldName} type. Must be a string or Date object.`);
};

/**
 * Validate array of ObjectIds
 */
export const validateObjectIdArray = (ids: string[], fieldName: string = 'IDs'): void => {
  if (!Array.isArray(ids)) {
    throw new Error(`${fieldName} must be an array`);
  }
  
  for (let i = 0; i < ids.length; i++) {
    if (!isValidObjectId(ids[i])) {
      throw new Error(`Invalid ${fieldName}[${i}] format. Must be a valid 24-character hex string.`);
    }
  }
};

/**
 * Safely convert date inputs in an object
 */
export const safeParseDateFields = (
  data: any, 
  dateFields: string[]
): any => {
  const result = { ...data };
  
  for (const field of dateFields) {
    if (result[field] !== undefined && result[field] !== null) {
      try {
        result[field] = safeParseDate(result[field], field);
      } catch (error) {
        throw new Error(`Error parsing ${field}: ${error.message}`);
      }
    }
  }
  
  return result;
};

/**
 * Check if employee has access to a specific plan through their enrollments
 * OPTIMIZED: Single query using indexed fields
 * INCLUSIVE: Checks ALL enrollment statuses (Enrolled, Pending, Waived, Terminated, Expired)
 */
export const checkEmployeePlanAccess = async (
  employeeId: string,
  planId: string
): Promise<{ hasAccess: boolean; planAssignmentId?: string; enrollmentId?: string }> => {
  try {
    const mongoose = require('mongoose');

    // Single optimized aggregation query using indexes
    const result = await mongoose.model('EmployeeEnrollment').aggregate([
      {
        // Stage 1: Match employee's enrollments (uses employeeId index)
        $match: {
          employeeId: new mongoose.Types.ObjectId(employeeId)
          // No status filter - employee can access plans regardless of enrollment status
        }
      },
      {
        // Stage 2: Join with PlanAssignment to get planId (uses planAssignmentId index)
        $lookup: {
          from: 'planassignments',
          localField: 'planAssignmentId',
          foreignField: '_id',
          as: 'planAssignment'
        }
      },
      {
        // Stage 3: Filter for the specific plan (uses planId index)
        $match: {
          'planAssignment.planId': new mongoose.Types.ObjectId(planId),
          'planAssignment.isActive': true
        }
      },
      {
        // Stage 4: Project only needed fields
        $project: {
          _id: 1,
          planAssignmentId: 1,
          'planAssignment.planId': 1
        }
      },
      {
        // Stage 5: Limit to 1 result (we only need to know if access exists)
        $limit: 1
      }
    ]);

    if (result.length > 0) {
      return {
        hasAccess: true,
        planAssignmentId: result[0].planAssignmentId.toString(),
        enrollmentId: result[0]._id.toString()
      };
    }

    return { hasAccess: false };
  } catch (error) {
    console.error('Error checking employee plan access:', error);
    return { hasAccess: false };
  }
};



/**
 * Validate dependent date of birth and return age
 */
export const validateDependentAge = (dateOfBirth: Date | string, relationship: string): number => {
  const dob = safeParseDate(dateOfBirth, 'dateOfBirth');
  const now = new Date();

  // Check if date is in the future
  if (dob > now) {
    throw new Error('Date of birth cannot be in the future');
  }
  
  // Calculate age
  const age = now.getFullYear() - dob.getFullYear();
  const monthDiff = now.getMonth() - dob.getMonth();
  const dayDiff = now.getDate() - dob.getDate();
  
  // Adjust age if birthday hasn't occurred this year
  const actualAge = (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) ? age - 1 : age;
  
  // Validate age constraints based on relationship
  if (relationship.toLowerCase() === 'spouse' && actualAge < 18) {
    throw new Error('Spouse must be at least 18 years old');
  }
  
  if (relationship.toLowerCase() === 'child' && actualAge > 26) {
    throw new Error('Child dependents must be 26 years old or younger');
  }
  
  return actualAge;
};

/**
 * Validate enrollment date constraints
 */
export const validateEnrollmentDates = (
  enrollmentDate: Date | string,
  effectiveDate: Date | string,
  terminationDate?: Date | string
): void => {
  const enrollment = safeParseDate(enrollmentDate, 'enrollmentDate');
  const effective = safeParseDate(effectiveDate, 'effectiveDate');
  
  if (effective < enrollment) {
    throw new Error('Effective date cannot be before enrollment date');
  }
  
  if (terminationDate) {
    const termination = safeParseDate(terminationDate, 'terminationDate');
    if (termination <= effective) {
      throw new Error('Termination date must be after effective date');
    }
  }
};

/**
 * Validate plan assignment date constraints
 */
export const validatePlanAssignmentDates = (
  planEffectiveDate: Date | string,
  planEndDate: Date | string,
  enrollmentStartDate: Date | string,
  enrollmentEndDate: Date | string
): void => {
  const planEffective = safeParseDate(planEffectiveDate, 'planEffectiveDate');
  const planEnd = safeParseDate(planEndDate, 'planEndDate');
  const enrollmentStart = safeParseDate(enrollmentStartDate, 'enrollmentStartDate');
  const enrollmentEnd = safeParseDate(enrollmentEndDate, 'enrollmentEndDate');
  
  if (planEffective >= planEnd) {
    throw new Error('Plan effective date must be before plan end date');
  }
  
  if (enrollmentStart >= enrollmentEnd) {
    throw new Error('Enrollment start date must be before enrollment end date');
  }
  
  if (enrollmentEnd > planEffective) {
    throw new Error('Enrollment must end before or when plan becomes effective');
  }
  
  // Enrollment period should be reasonable (not too far in advance)
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
  
  if (enrollmentStart > oneYearFromNow) {
    throw new Error('Enrollment start date cannot be more than one year in the future');
  }
};
