import {
  Box,
  Typography,
  Grid,
  Paper,
  Avatar,
  IconButton,
} from "@mui/material";
import { useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { useState } from "react";
import EditIcon from "@mui/icons-material/Edit";
import EnhancedEditProfileDialog from "./enhanced_edit_user_profile_popup";

const UserDetails = () => {
  const userDetails = useAppSelector(
    (state: RootState) => state.user.userProfile,
  );

  const [editProfileDialogOpen, setEditProfileDialogOpen] = useState(false);

  const handleEditDialogClose = () => {
    setEditProfileDialogOpen(false);
  };

  const getInitials = (name: string) => {
    console.log("Getting initials for name: ", JSON.stringify(userDetails));
    const [firstName, lastName] = name.split(" ");
    return `${firstName[0].toUpperCase()}${
      lastName ? lastName[0].toUpperCase() : ""
    }`;
  };

  const capitalizeWords = (name: string) => {
    return name.replace(/\b\w/g, (char) => char.toUpperCase());
  };

  const handleEditProfileClick = () => {
    setEditProfileDialogOpen(true);
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        bgcolor: "#ffffff",
        paddingTop: 7,
        paddingBottom: "12px",
        paddingX: "12px",
        borderRadius: "30px",
        width: "100%",
        position: "relative",
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: -60,
          display: "flex",
          justifyContent: "center",
          width: "100%",
        }}
      >
        <Avatar
          sx={{
            backgroundImage:
              "linear-gradient(180deg, #4BD1E9 0%, #1274CF 100%)",
            color: "#ffffff",
            width: 130,
            height: 130,
            border: "10px solid #ffffff",
            fontSize: "48px",
            fontWeight: 800,
          }}
        >
          {getInitials(userDetails.name)}
        </Avatar>
      </Box>

      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          mt: 4,
          mb: 4,
          position: "relative",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            fontWeight: "bold",
            fontSize: "32px",
            flexGrow: 1,
          }}
        >
          {capitalizeWords(userDetails.name) || "N/A"}
        </Typography>
        <IconButton
          onClick={handleEditProfileClick}
          sx={{
            bgcolor: "#000000",
            color: "#ffffff",
            borderRadius: "50%",
            width: "24px",
            height: "24px",
            p: "2px",
            ml: 2,
          }}
        >
          <EditIcon sx={{ fontSize: "16px" }} />
        </IconButton>
      </Box>

      <Paper
        sx={{
          borderRadius: "30px",
          p: 2.5,
          boxShadow: "none",
          bgcolor: "rgba(245, 245, 245, 0.7)",
          width: "100%",
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography
              sx={{
                color: "#6c757d",
                fontSize: "14px",
                textAlign: "left",
              }}
            >
              Title
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography
              sx={{ fontSize: "14px", textAlign: "right", fontWeight: 600 }}
            >
              {userDetails.details?.title || "N/A"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography
              variant="body2"
              sx={{ color: "#6c757d", fontSize: "14px", textAlign: "left" }}
            >
              Department
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography
              variant="body2"
              sx={{ fontSize: "14px", textAlign: "right", fontWeight: 600 }}
            >
              {userDetails.details?.department || "N/A"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography
              variant="body2"
              sx={{ color: "#6c757d", fontSize: "14px", textAlign: "left" }}
            >
              Employment Type
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography
              variant="body2"
              sx={{ fontSize: "14px", textAlign: "right", fontWeight: 600 }}
            >
              Full Time
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      <EnhancedEditProfileDialog
        open={editProfileDialogOpen}
        onClose={handleEditDialogClose}
      />
    </Box>
  );
};

export default UserDetails;
