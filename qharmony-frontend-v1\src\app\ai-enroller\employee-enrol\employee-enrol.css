/* Employee Enrollment Styles */
/* Font family is handled by design-system.css */

/* Custom slider styling - using design system */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: var(--gray-200);
  outline: none;
  border-radius: 8px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--black);
  cursor: pointer;
  border-radius: 50%;
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--black);
  cursor: pointer;
  border-radius: 50%;
  border: none;
}

/* Progress bar styling - using design system */
.progress-bar {
  background: linear-gradient(to right, var(--black) 0%, var(--black) var(--progress, 0%), var(--gray-200) var(--progress, 0%), var(--gray-200) 100%);
}

/* Step pill animations */
.step-pill {
  transition: all 0.3s ease;
}

.step-pill:hover {
  transform: translateY(-1px);
}

/* Card hover effects */
.plan-card {
  transition: all 0.3s ease;
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button animations */
.btn-primary {
  transition: all 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
}

/* Custom checkbox and radio styling - using design system */
input[type="checkbox"]:checked,
input[type="radio"]:checked {
  background-color: var(--black);
  border-color: var(--black);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom focus styles - using design system */
button:focus,
input:focus {
  outline: 2px solid var(--black);
  outline-offset: 2px;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .step-pills {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .step-pill {
    font-size: 12px;
    padding: 6px 12px;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .navigation-footer {
    padding: 16px;
  }
}

/* Dark mode support handled by design-system.css */

/* Enhanced Responsive Design */

/* Step navigation grid layout */
.step-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: flex-start;
  align-items: flex-start;
}

@media (max-width: 768px) {
  .step-grid {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .step-grid {
    gap: 6px;
    justify-content: center;
  }

  .hide-on-mobile {
    display: none !important;
  }

  .step-title-mobile {
    display: none;
  }

  .nav-text-mobile {
    display: none;
  }

  .header-title {
    font-size: 16px !important;
  }

  .header-subtitle {
    font-size: 10px !important;
  }
}

@media (max-width: 768px) {
  .icon-small {
    width: 14px !important;
    height: 14px !important;
  }

  .step-pill-compact {
    padding: 6px 8px !important;
    font-size: 11px !important;
  }

  .responsive-padding {
    padding: 12px 16px !important;
  }
}

/* Smooth scrolling for step navigation */
.step-navigation {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Custom scrollbar for step navigation */
.step-navigation::-webkit-scrollbar {
  height: 4px;
}

.step-navigation::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.step-navigation::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.step-navigation::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Prevent layout shift and blank space */
.main-content-wrapper {
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}

/* Footer shadow for better separation */
.navigation-footer {
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* Responsive utilities */
.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 clamp(12px, 3vw, 24px);
}

/* Responsive text sizing */
.responsive-title {
  font-size: clamp(16px, 4vw, 18px);
}

.responsive-subtitle {
  font-size: clamp(10px, 2.5vw, 12px);
}

.responsive-body {
  font-size: clamp(12px, 3vw, 14px);
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fix for viewport units on mobile */
@supports (-webkit-touch-callout: none) {
  .min-h-screen {
    min-height: -webkit-fill-available;
  }
}
