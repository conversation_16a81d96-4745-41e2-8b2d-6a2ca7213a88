import { UserDataInterface, DependentInterface, AddressInterface } from '../nosql/user.model';

// 🔄 LEGACY: Original EmployeeData interface (for backward compatibility)
interface EmployeeData {
  name: string;
  email: string;
  phoneNumber: string;
  department: string;
  title: string;
  role: string;
}

// 🎯 NEW: Enhanced Employee Update interface that matches actual UserDataInterface structure
interface EnhancedEmployeeUpdateData {
  // Top-level user fields
  name?: string;
  email?: string;
  role?: string;
  isActivated?: boolean;
  isDisabled?: boolean;

  // Details object (matches UserDataInterface.details structure)
  details?: {
    // 📞 Basic Contact Information
    phoneNumber?: string;
    department?: string;
    title?: string;
    role?: string;

    // 🎯 Employee demographic data
    dateOfBirth?: Date;
    hireDate?: Date;
    annualSalary?: number;
    employeeClassType?: string;
    customPayrollFrequency?: string;

    // 🆔 Personal Identification
    ssn?: string;

    // 🏠 Address Information
    address?: AddressInterface;
    mailingAddress?: AddressInterface;

    // 👨‍👩‍👧‍👦 Family Information
    dependents?: DependentInterface[];

    // 🚨 Emergency Contact
    emergencyContact?: {
      name?: string;
      relationship?: string;
      phoneNumber?: string;
      email?: string;
      address?: AddressInterface;
    };

    // 💼 Employment Details
    employeeId?: string;
    managerId?: string;
    workLocation?: string;
    workSchedule?: string;

    // 🏥 Health & Benefits
    tobaccoUser?: boolean;
    disabilityStatus?: string;
    veteranStatus?: string;

    // 📋 Compliance & Reporting
    i9Verified?: boolean;
    w4OnFile?: boolean;
    directDepositSetup?: boolean;

    // 📅 Important Dates
    terminationDate?: Date;
    rehireDate?: Date;
    lastReviewDate?: Date;
    nextReviewDate?: Date;

    // 📝 Notes & Comments
    notes?: string;
    hrNotes?: string;
  };
}

// 🎯 ACTUAL FRONTEND FORMAT: What the frontend actually sends (already correct!)
interface ActualFrontendUpdateData {
  // Top-level user fields
  name?: string;
  email?: string;
  role?: string;
  isActivated?: boolean;
  isDisabled?: boolean;

  // Details object (exactly what frontend sends)
  details?: {
    phoneNumber?: string;
    department?: string;
    title?: string;
    role?: string;

    // Enhanced fields (available for future use)
    dateOfBirth?: Date;
    hireDate?: Date;
    annualSalary?: number;
    employeeClassType?: string;
    customPayrollFrequency?: string;
    ssn?: string;
    address?: AddressInterface;
    mailingAddress?: AddressInterface;
    dependents?: DependentInterface[];
    emergencyContact?: {
      name?: string;
      relationship?: string;
      phoneNumber?: string;
      email?: string;
      address?: AddressInterface;
    };
    employeeId?: string;
    managerId?: string;
    workLocation?: string;
    workSchedule?: string;
    tobaccoUser?: boolean;
    disabilityStatus?: string;
    veteranStatus?: string;
    i9Verified?: boolean;
    w4OnFile?: boolean;
    directDepositSetup?: boolean;
    terminationDate?: Date;
    rehireDate?: Date;
    lastReviewDate?: Date;
    nextReviewDate?: Date;
    notes?: string;
    hrNotes?: string;
  };
}

// 🔄 Type alias for controller (matches actual frontend usage)
type EmployeeUpdateData = ActualFrontendUpdateData;

export default EmployeeData;
export { EnhancedEmployeeUpdateData, EmployeeUpdateData };
