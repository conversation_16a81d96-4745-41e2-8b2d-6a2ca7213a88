interface AuthorizationI {
  enterprise_id: string | null;
  team_id: string;
  user_id: string;
  is_bot: boolean;
  is_enterprise_install: boolean;
}

interface EventPayloadI {
  token: string;
  team_id: string;
  api_app_id: string;
  event: {
    bot_id?: string;
    client_msg_id?: string;
    type: string;
    subtype?: string;
    text: string;
    tab?: string;
    user: string;
    channel: string;
    channel_type?: string;
  };
  type: string;
  event_id: string;
  event_time: number;
  authorizations: AuthorizationI[];
  is_ext_shared_channel: boolean;
  event_context: string;
}

interface MessageEventPayloadI extends EventPayloadI {
  context_team_id: string;
  context_enterprise_id: string | null;
}

type AppMentionEventPayloadI = EventPayloadI;

export default EventPayloadI;
export { MessageEventPayloadI, AppMentionEventPayloadI };
