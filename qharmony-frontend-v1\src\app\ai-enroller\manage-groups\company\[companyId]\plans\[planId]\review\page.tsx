'use client';

import React from 'react';
import { useRouter, useParams } from 'next/navigation';
import { HiOutlineCheckCircle, HiOutlineDownload, HiOutlineEye } from 'react-icons/hi';

export default function ReviewPage() {
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;
  const planId = params.planId as string;

  const handleViewPlansPage = () => {
    router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`);
  };

  const handleDownloadSummary = () => {
    // Handle download functionality
    console.log('Download summary');
  };

  const handleConfirmAndSave = () => {
    router.push(`/ai-enroller/manage-groups/company/${companyId}/plans/${planId}/confirmation`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center space-x-2 text-sm text-gray-500">
            <button
              onClick={() => router.push('/ai-enroller')}
              className="hover:text-gray-700"
            >
              Home
            </button>
            <span>›</span>
            <button
              onClick={() => router.push('/ai-enroller/manage-groups/select-company')}
              className="hover:text-gray-700"
            >
              Select Company
            </button>
            <span>›</span>
            <button
              onClick={() => router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`)}
              className="hover:text-gray-700"
            >
              View Plans
            </button>
            <span>›</span>
            <span className="text-gray-400">Contributions</span>
            <span>›</span>
            <span className="text-gray-400">Set Dates</span>
            <span>›</span>
            <span className="text-blue-600 font-medium">Review</span>
          </nav>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-6">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Final Review & Confirmation</h1>
            <p className="text-gray-600">Review all changes before saving for TechCorp Inc.</p>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Step 5 of 5</div>
            <div className="text-xs text-blue-600">Final review</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Ready to Save Banner */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
          <div className="flex items-center gap-2">
            <HiOutlineCheckCircle className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-900">Ready to save! Review the changes below and confirm when ready.</span>
          </div>
        </div>

        {/* Plan Summary */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <div className="flex items-center gap-2 mb-4">
            <span className="text-lg font-semibold text-gray-900">📋 Plan Summary</span>
          </div>
          <p className="text-gray-600 mb-6">Side-by-side comparison of new vs existing plans</p>

          {/* Blue Cross Blue Shield PPO */}
          <div className="border border-blue-200 rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Blue Cross Blue Shield PPO</h3>
                <p className="text-sm text-gray-600">Blue Cross Blue Shield • PPO</p>
              </div>
              <div className="flex gap-2">
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-md">Updated</span>
                <button className="text-blue-600 hover:text-blue-700">
                  <HiOutlineEye className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="bg-gray-50 rounded-lg p-4 text-center">
                <div className="text-sm text-gray-600 mb-1">Total Monthly Premium</div>
                <div className="text-xl font-bold text-gray-900">$3,310.00</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <div className="text-sm text-gray-600 mb-1">Employer Contribution</div>
                <div className="text-xl font-bold text-green-600">$2,648.00</div>
              </div>
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <div className="text-sm text-gray-600 mb-1">Employee Contribution</div>
                <div className="text-xl font-bold text-blue-600">$662.00</div>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Changes Made:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Contribution rates updated</li>
                <li>• New tier added</li>
              </ul>
            </div>
          </div>

          {/* Delta Dental PPO */}
          <div className="border border-green-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Delta Dental PPO</h3>
                <p className="text-sm text-gray-600">Delta Dental • PPO</p>
              </div>
              <div className="flex gap-2">
                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-md">New</span>
                <button className="text-blue-600 hover:text-blue-700">
                  <HiOutlineEye className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="bg-gray-50 rounded-lg p-4 text-center">
                <div className="text-sm text-gray-600 mb-1">Total Monthly Premium</div>
                <div className="text-xl font-bold text-gray-900">$480.00</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <div className="text-sm text-gray-600 mb-1">Employer Contribution</div>
                <div className="text-xl font-bold text-green-600">$384.00</div>
              </div>
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <div className="text-sm text-gray-600 mb-1">Employee Contribution</div>
                <div className="text-xl font-bold text-blue-600">$96.00</div>
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-2">Changes Made:</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• New plan added</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Company Impact Summary */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <div className="flex items-center gap-2 mb-4">
            <span className="text-lg font-semibold text-gray-900">📊 Company Impact Summary</span>
          </div>
          <p className="text-gray-600 mb-6">Total cost impact for TechCorp Inc.</p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
              <div className="text-sm text-gray-600 mb-2">Total Monthly Employer Cost</div>
              <div className="text-2xl font-bold text-green-600">$3,032.00</div>
              <div className="text-xs text-green-600">+$184.00 from previous</div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
              <div className="text-sm text-gray-600 mb-2">Total Annual Employer Cost</div>
              <div className="text-2xl font-bold text-blue-600">$36,384.00</div>
              <div className="text-xs text-blue-600">Projected for full year</div>
            </div>

            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <span className="text-orange-600">👥</span>
                <span className="text-sm text-gray-600">Average Cost per Employee</span>
              </div>
              <div className="text-2xl font-bold text-orange-600">$145.53</div>
              <div className="text-xs text-orange-600">Based on 250 employees</div>
            </div>
          </div>
        </div>

        {/* Validation Check */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
          <div className="flex items-center gap-2">
            <HiOutlineCheckCircle className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-900">Validation Check: All contribution amounts are within acceptable ranges. No issues detected.</span>
          </div>
        </div>

        {/* Important Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <div className="flex items-start gap-2">
            <span className="text-blue-600 mt-0.5">⚠️</span>
            <div>
              <div className="font-medium text-blue-900">Important:</div>
              <div className="text-sm text-blue-800">Once confirmed, these changes will be applied to TechCorp Inc.&apos;s benefit plans. Plan documents and employee communications should be updated accordingly.</div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <button
            onClick={handleViewPlansPage}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
          >
            <HiOutlineEye className="w-4 h-4" />
            View Plans Page
          </button>

          <div className="flex gap-3">
            <button
              onClick={handleDownloadSummary}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
            >
              <HiOutlineDownload className="w-4 h-4" />
              Download Summary
            </button>
            <button
              onClick={handleConfirmAndSave}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Confirm and Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

