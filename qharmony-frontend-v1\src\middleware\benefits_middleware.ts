import {
  getBlobRequest,
  getRequest,
  postRequest,
  uploadDocument,
} from "@/APILayer/axios_helper";
import {
  upsertBenefitsPerType,
  setDocumentsPerBenefit,
  setViewableDocuments,
  clearBenefitsState,
  addDocument,
  deleteDocument,
  addLink,
  deleteLink,
  setSnackbarMessage,
} from "../redux/reducers/benefitsSlice";
import { AppDispatch } from "../redux/store";
import { Console } from "console";
import {
  getCompanyBenefitTypes,
  getCompanyDetails,
} from "./company_middleware";

export async function getBenefitsForType(
  dispatch: AppDispatch,
  companyId: string,
  benefitType: string,
) {
  const data = {
    companyId: companyId,
    type: benefitType,
  };
  try {
    const response = await getRequest("/benefits/benefit-by-type", data);
    if (response && response.benefits) {
      console.log("GET BENEFITS FOR TYPE RESPONSE: ", response.benefits);
      dispatch(
        upsertBenefitsPerType({
          benefitType: benefitType,
          benefits: response.benefits,
        }),
      );
      dispatch(setSnackbarMessage("Benefits fetched successfully"));
    } else {
      console.error("Invalid response format:", response);
      dispatch(setSnackbarMessage("Failed to fetch benefits"));
    }
  } catch (error) {
    console.error("Error fetching benefits:", error);
    dispatch(setSnackbarMessage("Error fetching benefits"));
  }
}

export async function getDocumentsForBenefit(
  dispatch: AppDispatch,
  benefitId: string,
  companyId: string,
  page: string,
) {
  const data = {
    benefitId: benefitId,
    page: page
  };

  console.log("data", data);
  const response = await getRequest("/benefits/one-benefit", data);
  const responseDataWithBenefitId = { ...response, benefitId: benefitId };
  dispatch(setDocumentsPerBenefit(responseDataWithBenefitId));
  for (const documentObjectKey of response.documents) {
    const originalFileName = decodeURIComponent(
      documentObjectKey.split("_____")[1],
    );
    getViewableDocument(
      dispatch,
      documentObjectKey,
      companyId,
      originalFileName,
    );
  }
}

export async function getViewableDocument(
  dispatch: AppDispatch,
  documentObjectKey: string,
  companyId: string,
  originalFileName: string,
) {
  const data = {
    objectKey: documentObjectKey,
    companyId: companyId,
  };

  console.log("data", data);

  const response = await getBlobRequest("/benefits/document", data);

  console.log("VIEW BENEFIT RESPONSE: ", response);

  if (response) {
    const blob = new Blob([response], { type: "application/pdf" });
    const blobUrl = URL.createObjectURL(blob);
    dispatch(
      setViewableDocuments([
        {
          documentObjectKey,
          document: blobUrl,
          originalFileName: originalFileName,
        },
      ]),
    );
  }
}

export const toggleBenefitActivation = async (
  dispatch: AppDispatch,
  companyId: string,
  benefitId: string,
  isActivated: boolean,
  benefitType: string,
) => {
  const data = {
    benefitId: benefitId,
    companyId: companyId,
    isActivated: isActivated,
  };

  const response = await postRequest("/benefits/toggle-benefits/", data);
  if (response.status === 200) {
    await getBenefitsForType(dispatch, companyId, benefitType);
    await getCompanyBenefitTypes(dispatch, companyId);
    return true;
  }
  return false;
};

export async function addDocumentForBenefit(
  dispatch: AppDispatch,
  benefitId: string,
  companyId: string,
  files: File[],
) {
  const formData = new FormData();
  files.forEach((file) => formData.append("documents", file));
  formData.append("companyId", companyId);
  formData.append("benefitId", benefitId);

  try {
    console.log("uploadDocument", formData);
    const response = await uploadDocument("/benefits/add/document", formData);

    const newObjectKeys = response.data.objectKeys;

    console.log("newObjectKeys", newObjectKeys);

    if (response.status === 200) {
      newObjectKeys.forEach((objectKey: string, index: number) => {
        const originalFileName = files[index].name;
        dispatch(addDocument({ benefitId, document: objectKey }));
        getViewableDocument(dispatch, objectKey, companyId, originalFileName);
      });
      dispatch(setSnackbarMessage("Document added successfully"));
      return true;
    } else {
      console.error("Error adding document:", response.data.error);
      dispatch(setSnackbarMessage("Failed to add document"));
      return false;
    }
  } catch (error) {
    console.error("Error adding document:", error);
    dispatch(setSnackbarMessage("Error adding document"));
    return false;
  }
}

export async function deleteDocumentForBenefit(
  dispatch: AppDispatch,
  benefitId: string,
  companyId: string,
  documentObjectKey: string,
) {
  const data = {
    benefitId: benefitId,
    companyId: companyId,
    objectKey: documentObjectKey,
  };

  try {
    const response = await postRequest("/benefits/delete/document", data);

    if (response.status === 200) {
      dispatch(deleteDocument({ benefitId, document: documentObjectKey }));
      dispatch(setSnackbarMessage("Document deleted successfully"));
      return true;
    } else {
      console.error("Error deleting document:", response.data.error);
      dispatch(setSnackbarMessage("Failed to delete document"));
      return false;
    }
  } catch (error) {
    console.error("Error deleting document:", error);
    dispatch(setSnackbarMessage("Error deleting document"));
    return false;
  }
}

export async function addLinkForBenefit(
  dispatch: AppDispatch,
  benefitId: string,
  companyId: string,
  link: string,
) {
  const data = {
    benefitId: benefitId,
    companyId: companyId,
    urls: [link],
  };

  try {
    const response = await postRequest("/benefits/add/links", data);

    if (response.status === 200) {
      dispatch(addLink({ benefitId, link }));
      dispatch(setSnackbarMessage("Link added successfully"));
      return true;
    } else {
      console.error("Error adding link:", response.data.error);
      dispatch(setSnackbarMessage("Failed to add link"));
      return false;
    }
  } catch (error) {
    console.error("Error adding link:", error);
    dispatch(setSnackbarMessage("Error adding link"));
    return false;
  }
}

export async function deleteLinkForBenefit(
  dispatch: AppDispatch,
  benefitId: string,
  companyId: string,
  link: string,
) {
  const data = {
    benefitId: benefitId,
    companyId: companyId,
    urls: link,
  };

  try {
    const response = await postRequest("/benefits/delete/link", data);

    if (response.status === 200) {
      dispatch(deleteLink({ benefitId, link }));
      dispatch(setSnackbarMessage("Link deleted successfully"));
      return true;
    } else {
      console.error("Error deleting link:", response.data.error);
      dispatch(setSnackbarMessage("Failed to delete link"));
      return false;
    }
  } catch (error) {
    console.error("Error deleting link:", error);
    dispatch(setSnackbarMessage("Error deleting link"));
    return false;
  }
}

export async function updateCompanyLogo(dispatch: AppDispatch, file: File) {
  const formData = new FormData();
  formData.append("logoImage", file);

  try {
    console.log("uploading company logo", formData);
    const response = await uploadDocument(
      "/admin/update-company-logo",
      formData,
    );

    await getCompanyDetails(dispatch);

    if (response.status === 200) {
      console.log("Company logo updated successfully");
      dispatch(setSnackbarMessage("Company logo updated successfully"));
      return true;
    } else {
      console.error("Error updating company logo:", response.data.error);
      dispatch(setSnackbarMessage("Failed to update company logo"));
      return false;
    }
  } catch (error) {
    console.error("Error updating company logo:", error);
    dispatch(setSnackbarMessage("Error updating company logo"));
    return false;
  }
}

export async function getViewableDocumentForTeams(
  documentObjectKey: string,
  companyId: string,
) {
  const data = {
    objectKey: documentObjectKey,
    companyId: companyId,
  };
  const response = await getBlobRequest("/benefits/document", data);
  if (response) {
    const blob = new Blob([response], { type: "application/pdf" });
    const blobUrl = URL.createObjectURL(blob);
    return blobUrl;
  }
}