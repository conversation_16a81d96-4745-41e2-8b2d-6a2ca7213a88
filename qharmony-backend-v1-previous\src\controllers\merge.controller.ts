import express from 'express';

import Controller from '../interfaces/controller.interface';
import CommonService from '../services/common.service';
import { Merge, MergeClient } from '@mergeapi/merge-node-client';
import EnvService from '../services/env.service';

class MergeController implements Controller {
  public router = express.Router();
  public commonService = new CommonService();
  public merge = new MergeClient({ apiKey: EnvService.env().MERGE_APIKEY });

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post(`/merge/create-link-token`, this.createLinkToken);
    this.router.post(`/merge/get-linked-accounts`, this.getLinkedAccounts);
    this.router.post(`/merge/get-benefits`, this.getBenefits);
  }

  private createLinkToken = async (
    request: express.Request,
    response: express.Response
  ) => {
    const { endUserEmailAddress, endUserOrganizationName, endUserOriginId } =
      request.body;
    // @ts-ignore - Type definitions for Merge API are incomplete
    const linkTokenResponse = await this.merge.ats.linkToken.create({
      endUserEmailAddress,
      endUserOrganizationName,
      endUserOriginId,
      // @ts-ignore - Type definitions for Merge API are incomplete
      categories: [Merge.hris.CategoriesEnum.Hris],
      linkExpiryMins: 30,
      shouldCreateMagicLinkUrl: true,
    });
    response.send(linkTokenResponse);
  };

  private getBenefits = async (
    _request: express.Request,
    response: express.Response
  ) => {
    // @ts-ignore - Type definitions for Merge API are incomplete
    const merge = new MergeClient({
      apiKey: EnvService.env().MERGE_APIKEY,
      // @ts-ignore - Type definitions for Merge API are incomplete
      accountToken: EnvService.env().MERGE_SAMPLE_ACCOUNT_TOKEN,
    });
    // @ts-ignore - Type definitions for Merge API are incomplete
    const benefits = await merge.hris.benefits.list({
      includeRemoteData: true,
    });
    response.send(benefits);
  };

  private getLinkedAccounts = async (
    _request: express.Request,
    response: express.Response
  ) => {
    // @ts-ignore - Type definitions for Merge API are incomplete
    const merge = new MergeClient({
      apiKey: EnvService.env().MERGE_APIKEY,
      // @ts-ignore - Type definitions for Merge API are incomplete
      accountToken: EnvService.env().MERGE_SAMPLE_ACCOUNT_TOKEN,
    });
    // @ts-ignore - Type definitions for Merge API are incomplete
    const accountDetails = await merge.ats.accountDetails.retrieve();
    response.send(accountDetails);
  };
}

export default MergeController;
