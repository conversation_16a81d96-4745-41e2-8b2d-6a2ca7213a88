# Comprehensive Cost Calculation Guide

## 🎯 **Complete Broker Setup → Employee Cost Flow**

This guide shows exactly what brokers configure, how each decision affects pricing, and the complete calculation flow with all permutations.

## 📋 **Phase 1: What Broker Configures (Plan Assignment Setup)**

### **1. 🏗️ Rate Structure Selection (Primary Decision)**

The broker must choose ONE rate structure that determines how costs are calculated:

```typescript
// Broker's Primary Choice:
rateStructure: "Composite" | "Age-Banded" | "Four-Tier" | "Age-Banded-Four-Tier" | "Salary-Based"
```

### **2. 💰 Contribution Policy Configuration (Cost Splitting)**

The broker configures how costs are split between employer and employee:

```typescript
// Broker configures BOTH policies:
employerContribution: {
  contributionType: "Fixed" | "Percentage",
  contributionAmount: number  // Dollar amount OR percentage
}

employeeContribution: {
  contributionType: "Fixed" | "Percentage" | "Remainder",
  contributionAmount: number  // Dollar amount OR percentage (0 for Remainder)
}
```

### **3. 🎯 Coverage Tiers (Available Options)**

The broker sets up available coverage tiers with base costs:

```typescript
// Broker defines available tiers:
coverageTiers: [
  { tierName: "Employee Only", totalCost: 300 },
  { tierName: "Employee + Spouse", totalCost: 600 },
  { tierName: "Employee + Child(ren)", totalCost: 550 },
  { tierName: "Family", totalCost: 800 }
]
```

### **4. 📊 Rate Structure Specific Data**

Based on rate structure choice, broker configures additional data:

#### **For Age-Banded (Fixed Amount):**
```typescript
ageBandedRates: [
  { ageMin: 18, ageMax: 29, rate: 100, type: 'fixed' },  // +$100 for young employees
  { ageMin: 30, ageMax: 39, rate: 150, type: 'fixed' },  // +$150 for mid-age
  { ageMin: 40, ageMax: 49, rate: 200, type: 'fixed' },  // +$200 for older employees
  { ageMin: 50, ageMax: 65, rate: 300, type: 'fixed' }   // +$300 for seniors
]
```

#### **For Age-Banded (Multiplier Factor):**
```typescript
ageBandedRates: [
  { ageMin: 18, ageMax: 29, rate: 0.8, type: 'multiplier' },  // 20% discount for young
  { ageMin: 30, ageMax: 39, rate: 1.0, type: 'multiplier' },  // Base rate
  { ageMin: 40, ageMax: 49, rate: 1.2, type: 'multiplier' },  // 20% increase
  { ageMin: 50, ageMax: 65, rate: 1.5, type: 'multiplier' }   // 50% increase
]
```

#### **For Salary-Based:**
```typescript
// Option 1: Salary Bands
salaryBasedRates: [
  { salaryMin: 30000, salaryMax: 50000, rate: 50 },   // Lower income adjustment
  { salaryMin: 50001, salaryMax: 75000, rate: 75 },   // Middle income adjustment
  { salaryMin: 75001, salaryMax: 100000, rate: 100 }  // Higher income adjustment
]

// Option 2: Salary Percentage
salaryPercentage: 2.5  // 2.5% of annual salary
```

## 🧮 **Phase 2: Complete Cost Calculation Matrix**

### **Scenario Setup:**
- **Company**: TechCorp
- **Plan**: Premium Health Plan (Medical + Dental + Vision)
- **Payroll Frequency**: Biweekly (26 pay periods/year)

### **Employee Profiles:**
- **John**: Age 25, Salary $45K, Selects "Employee Only"
- **Sarah**: Age 35, Salary $65K, Selects "Employee + Spouse"  
- **Mike**: Age 45, Salary $85K, Selects "Family"

---

## 📊 **CALCULATION 1: COMPOSITE RATE STRUCTURE**

### **Broker Configuration:**
```typescript
{
  rateStructure: "Composite",
  employerContribution: { contributionType: "Percentage", contributionAmount: 80 },
  employeeContribution: { contributionType: "Remainder", contributionAmount: 0 },
  coverageTiers: [
    { tierName: "Employee Only", totalCost: 300 },
    { tierName: "Employee + Spouse", totalCost: 600 },
    { tierName: "Family", totalCost: 800 }
  ]
}
```

### **Cost Calculations:**

#### **John (Age 25, $45K, Employee Only):**
```typescript
// Step 1: Base Cost (Composite = Direct from tier)
baseCost = 300  // Employee Only tier

// Step 2: Apply Contribution Policy
employerAmount = 300 * 0.80 = 240  // 80% employer
employeeAmount = 300 - 240 = 60    // Employee pays remainder

// Step 3: Enhanced Breakdown
monthlyEmployeeAmount = 60
annualEmployeeAmount = 60 * 12 = 720
payrollEmployeeAmount = 60 / (26/12) = 27.69  // Biweekly deduction
```

#### **Sarah (Age 35, $65K, Employee + Spouse):**
```typescript
// Step 1: Base Cost
baseCost = 600  // Employee + Spouse tier

// Step 2: Apply Contribution Policy  
employerAmount = 600 * 0.80 = 480
employeeAmount = 600 - 480 = 120

// Step 3: Enhanced Breakdown
monthlyEmployeeAmount = 120
annualEmployeeAmount = 120 * 12 = 1440
payrollEmployeeAmount = 120 / (26/12) = 55.38
```

#### **Mike (Age 45, $85K, Family):**
```typescript
// Step 1: Base Cost
baseCost = 800  // Family tier

// Step 2: Apply Contribution Policy
employerAmount = 800 * 0.80 = 640
employeeAmount = 800 - 640 = 160

// Step 3: Enhanced Breakdown
monthlyEmployeeAmount = 160
annualEmployeeAmount = 160 * 12 = 1920
payrollEmployeeAmount = 160 / (26/12) = 73.85
```

**🎯 Composite Result: Age and salary don't matter - only tier selection affects cost**

---

## 📊 **CALCULATION 2: AGE-BANDED RATE STRUCTURE**

### **Broker Configuration:**
```typescript
{
  rateStructure: "Age-Banded",
  employerContribution: { contributionType: "Percentage", contributionAmount: 75 },
  employeeContribution: { contributionType: "Remainder", contributionAmount: 0 },
  ageBandedRates: [
    { ageMin: 18, ageMax: 29, rate: 100, type: 'fixed' },    // Young employee (+$100)
    { ageMin: 30, ageMax: 39, rate: 150, type: 'fixed' },    // Mid-age (+$150)
    { ageMin: 40, ageMax: 49, rate: 200, type: 'fixed' }     // Older (+$200)
  ],
  coverageTiers: [
    { tierName: "Employee Only", totalCost: 250, employeeCost: 62.50, employerCost: 187.50 },
    { tierName: "Employee + Spouse", totalCost: 500, employeeCost: 125, employerCost: 375 },
    { tierName: "Family", totalCost: 700, employeeCost: 175, employerCost: 525 }
  ]
}
```

### **Cost Calculations:**

#### **John (Age 25, $45K, Employee Only):**
```typescript
// Step 1: Find Age Band
ageBand = { ageMin: 18, ageMax: 29, rate: 100 }  // Age 25 falls here

// Step 2: Calculate Base Cost (Age Rate + Tier Cost)
baseCost = 100 + 250 = 350

// Step 3: Apply Contribution Policy
employerAmount = 350 * 0.75 = 262.50  // 75% employer
employeeAmount = 350 - 262.50 = 87.50 // Employee remainder

// Step 4: Enhanced Breakdown
monthlyEmployeeAmount = 87.50
annualEmployeeAmount = 87.50 * 12 = 1050
payrollEmployeeAmount = 87.50 / (26/12) = 40.38
```

#### **Sarah (Age 35, $65K, Employee + Spouse):**
```typescript
// Step 1: Find Age Band
ageBand = { ageMin: 30, ageMax: 39, rate: 150 }  // Age 35 falls here

// Step 2: Calculate Base Cost
baseCost = 150 + 500 = 650

// Step 3: Apply Contribution Policy
employerAmount = 650 * 0.75 = 487.50
employeeAmount = 650 - 487.50 = 162.50

// Step 4: Enhanced Breakdown
monthlyEmployeeAmount = 162.50
annualEmployeeAmount = 162.50 * 12 = 1950
payrollEmployeeAmount = 162.50 / (26/12) = 75.00
```

#### **Mike (Age 45, $85K, Family):**
```typescript
// Step 1: Find Age Band
ageBand = { ageMin: 40, ageMax: 49, rate: 200 }  // Age 45 falls here

// Step 2: Calculate Base Cost
baseCost = 200 + 700 = 900

// Step 3: Apply Contribution Policy
employerAmount = 900 * 0.75 = 675
employeeAmount = 900 - 675 = 225

// Step 4: Enhanced Breakdown
monthlyEmployeeAmount = 225
annualEmployeeAmount = 225 * 12 = 2700
payrollEmployeeAmount = 225 / (26/12) = 103.85
```

**🎯 Age-Banded Result: Older employees pay more, tier selection also affects cost**

### **🔄 Age-Banded Types: Fixed vs Multiplier**

QHarmony supports both fixed amount and multiplier factor age adjustments:

#### **Fixed Amount (QHarmony Legacy):**
```typescript
// Age band adds fixed dollar amount
ageBand = { ageMin: 40, ageMax: 49, rate: 200, type: 'fixed' }
finalCost = baseCost + ageRate = 700 + 200 = 900
```

#### **Multiplier Factor (Industry Standard):**
```typescript
// Age band multiplies base cost
ageBand = { ageMin: 40, ageMax: 49, rate: 1.29, type: 'multiplier' }
finalCost = baseCost * ageFactor = 700 * 1.29 = 903
```

**Key Benefit:** Both methods maintain the same contribution ratios through proportional scaling.

---

## 📊 **CALCULATION 3: SALARY-BASED RATE STRUCTURE**

### **Broker Configuration:**
```typescript
{
  rateStructure: "Salary-Based",
  employerContribution: { contributionType: "Fixed", contributionAmount: 400 },
  employeeContribution: { contributionType: "Remainder", contributionAmount: 0 },
  salaryBasedRates: [
    { salaryMin: 30000, salaryMax: 50000, rate: 0.8, type: 'multiplier' },   // Lower income (20% discount)
    { salaryMin: 50001, salaryMax: 75000, rate: 1.0, type: 'multiplier' },   // Middle income (standard rate)
    { salaryMin: 75001, salaryMax: 100000, rate: 1.2, type: 'multiplier' }   // Higher income (20% premium)
  ],
  coverageTiers: [
    { tierName: "Employee Only", totalCost: 200, employeeCost: 50, employerCost: 150 },
    { tierName: "Employee + Spouse", totalCost: 400, employeeCost: 100, employerCost: 300 },
    { tierName: "Family", totalCost: 600, employeeCost: 150, employerCost: 450 }
  ]
}
```

### **Cost Calculations:**

#### **John (Age 25, $45K, Employee Only):**
```typescript
// Step 1: Find Salary Band
salaryBand = { salaryMin: 30000, salaryMax: 50000, rate: 0.8, type: 'multiplier' }  // $45K falls here

// Step 2: Calculate Base Cost (Salary Multiplier * Tier Cost)
baseCost = 200 * 0.8 = 160  // 20% discount for lower income

// Step 3: Apply Contribution Policy (Fixed employer amount)
employerAmount = 400  // Fixed amount (but capped at total cost)
if (employerAmount > baseCost) employerAmount = baseCost  // Cap at 160
employeeAmount = 160 - 160 = 0  // Employee pays remainder

// Step 4: Enhanced Breakdown
monthlyEmployeeAmount = 0      // Employer covers full cost
annualEmployeeAmount = 0
payrollEmployeeAmount = 0
```

#### **Sarah (Age 35, $65K, Employee + Spouse):**
```typescript
// Step 1: Find Salary Band
salaryBand = { salaryMin: 50001, salaryMax: 75000, rate: 1.0, type: 'multiplier' }  // $65K falls here

// Step 2: Calculate Base Cost
baseCost = 400 * 1.0 = 400  // Standard rate for middle income

// Step 3: Apply Contribution Policy
employerAmount = 400  // Fixed amount
employeeAmount = 400 - 400 = 0  // Employee pays remainder

// Step 4: Enhanced Breakdown
monthlyEmployeeAmount = 0      // Employer covers full cost
annualEmployeeAmount = 0
payrollEmployeeAmount = 0
```

#### **Mike (Age 45, $85K, Family):**
```typescript
// Step 1: Find Salary Band
salaryBand = { salaryMin: 75001, salaryMax: 100000, rate: 1.2, type: 'multiplier' }  // $85K falls here

// Step 2: Calculate Base Cost
baseCost = 600 * 1.2 = 720  // 20% premium for higher income

// Step 3: Apply Contribution Policy
employerAmount = 400  // Fixed amount
employeeAmount = 720 - 400 = 320  // Employee pays remainder

// Step 4: Enhanced Breakdown
monthlyEmployeeAmount = 320
annualEmployeeAmount = 320 * 12 = 3840
payrollEmployeeAmount = 320 / (26/12) = 147.69
```

**🎯 Salary-Based Result: Higher earners pay more, fixed employer contribution creates variable employee costs**

### **🔄 Salary-Based Types: Fixed vs Multiplier**

QHarmony supports both fixed amount and multiplier factor salary adjustments:

#### **Fixed Amount (QHarmony Legacy):**
```typescript
// Salary band adds fixed dollar amount
salaryBand = { salaryMin: 75000, salaryMax: 100000, rate: 100, type: 'fixed' }
finalCost = baseCost + salaryRate = 600 + 100 = 700
```

#### **Multiplier Factor (Industry Standard):**
```typescript
// Salary band multiplies base cost
salaryBand = { salaryMin: 75000, salaryMax: 100000, rate: 1.2, type: 'multiplier' }
finalCost = baseCost * salaryFactor = 600 * 1.2 = 720
```

#### **Salary Percentage (Alternative Approach):**
```typescript
// Premium as percentage of annual salary
salaryPercentage = 2.5  // 2.5% of annual salary
annualCost = 85000 * 0.025 = 2125
monthlyCost = 2125 / 12 = 177.08
```

**Key Benefit:** All methods maintain the same contribution ratios through proportional scaling.

---

## 💰 **CONTRIBUTION POLICY DEEP DIVE**

### **How Each Contribution Type Affects Final Costs:**

#### **1. 🎯 Percentage + Remainder (Most Common)**
```typescript
// Broker Setup:
employerContribution: { contributionType: "Percentage", contributionAmount: 80 }
employeeContribution: { contributionType: "Remainder", contributionAmount: 0 }

// Calculation for $1000 total cost:
employerAmount = 1000 * 0.80 = 800
employeeAmount = 1000 - 800 = 200  // Remainder
```

#### **2. 🎯 Fixed + Remainder**
```typescript
// Broker Setup:
employerContribution: { contributionType: "Fixed", contributionAmount: 600 }
employeeContribution: { contributionType: "Remainder", contributionAmount: 0 }

// Calculation for $1000 total cost:
employerAmount = 600  // Fixed amount
employeeAmount = 1000 - 600 = 400  // Employee pays remainder
```

#### **3. 🎯 Percentage + Percentage**
```typescript
// Broker Setup:
employerContribution: { contributionType: "Percentage", contributionAmount: 70 }
employeeContribution: { contributionType: "Percentage", contributionAmount: 30 }

// Calculation for $1000 total cost:
employerAmount = 1000 * 0.70 = 700
employeeAmount = 1000 * 0.30 = 300
// Note: Must add up to 100%
```

#### **4. 🎯 Fixed + Fixed**
```typescript
// Broker Setup:
employerContribution: { contributionType: "Fixed", contributionAmount: 500 }
employeeContribution: { contributionType: "Fixed", contributionAmount: 200 }

// Calculation for $1000 total cost:
employerAmount = 500  // Fixed amount
employeeAmount = 200  // Fixed amount
// Note: Total may not equal plan cost (unusual scenario)
```

---

## 📅 **PAYROLL FREQUENCY IMPACT ON EMPLOYEE DEDUCTIONS**

### **How payrollEmployeeAmount is Calculated:**

```typescript
// Formula:
payrollEmployeeAmount = monthlyEmployeeAmount / (payPeriodsPerYear / 12)

// Where payPeriodsPerYear depends on payroll frequency:
const payrollFrequencies = {
  "Weekly": 52,        // 52 weeks per year
  "Biweekly": 26,      // Every 2 weeks
  "Semi-Monthly": 24,  // Twice per month
  "Monthly": 12        // Once per month
}
```

### **Example: $200 Monthly Employee Cost Across Different Payroll Frequencies:**

#### **Weekly Payroll:**
```typescript
payrollEmployeeAmount = 200 / (52/12) = 200 / 4.33 = 46.15
// Employee pays $46.15 per week
```

#### **Biweekly Payroll:**
```typescript
payrollEmployeeAmount = 200 / (26/12) = 200 / 2.17 = 92.31
// Employee pays $92.31 every two weeks
```

#### **Semi-Monthly Payroll:**
```typescript
payrollEmployeeAmount = 200 / (24/12) = 200 / 2.00 = 100.00
// Employee pays $100.00 twice per month
```

#### **Monthly Payroll:**
```typescript
payrollEmployeeAmount = 200 / (12/12) = 200 / 1.00 = 200.00
// Employee pays $200.00 once per month
```

---

## 🎯 **COMPLETE COST CALCULATION SUMMARY TABLE**

### **All Scenarios Compared:**

| Employee | Rate Structure | Age Factor | Salary Factor | Tier Cost | Total Cost | Employer | Employee | Biweekly Deduction |
|----------|---------------|------------|---------------|-----------|------------|----------|----------|-------------------|
| **John (25, $45K, Employee Only)** |
| | Composite | N/A | N/A | $300 | $300 | $240 (80%) | $60 (20%) | $27.69 |
| | Age-Banded | +$100 | N/A | $250 | $350 | $262.50 (75%) | $87.50 (25%) | $40.38 |
| | Salary-Based | N/A | ×0.8 | $200 | $160 | $160* | $0* | $0 |
| **Sarah (35, $65K, Employee + Spouse)** |
| | Composite | N/A | N/A | $600 | $600 | $480 (80%) | $120 (20%) | $55.38 |
| | Age-Banded | +$150 | N/A | $500 | $650 | $487.50 (75%) | $162.50 (25%) | $75.00 |
| | Salary-Based | N/A | ×1.0 | $400 | $400 | $400* | $0* | $0 |
| **Mike (45, $85K, Family)** |
| | Composite | N/A | N/A | $800 | $800 | $640 (80%) | $160 (20%) | $73.85 |
| | Age-Banded | +$200 | N/A | $700 | $900 | $675 (75%) | $225 (25%) | $103.85 |
| | Salary-Based | N/A | ×1.2 | $600 | $720 | $400* | $320* | $147.69 |

*Fixed employer contribution of $400 in salary-based example

---

## 🔍 **KEY INSIGHTS FROM COMPREHENSIVE ANALYSIS**

### **1. 📊 Rate Structure Impact:**
- **Composite**: Simplest - only tier matters
- **Age-Banded**: Age significantly affects cost (older = more expensive)
- **Salary-Based**: Income affects cost (higher salary = higher cost)

### **2. 💰 Contribution Policy Impact:**
- **Percentage**: Scales with total cost
- **Fixed**: Creates variable employee costs
- **Remainder**: Ensures one party pays what's left

### **3. 📅 Payroll Frequency Impact:**
- **Weekly**: Smallest per-paycheck deduction
- **Monthly**: Largest per-paycheck deduction
- **Biweekly**: Most common, moderate deduction

### **4. 🎯 Business Logic Validation:**
- Age-banded rates make older employees more expensive ✅
- Salary-based rates make higher earners pay more ✅
- Contribution policies properly split costs ✅
- Payroll frequency correctly calculates deductions ✅

**This comprehensive guide shows exactly how broker configurations translate to employee costs across all scenarios!** 🚀
