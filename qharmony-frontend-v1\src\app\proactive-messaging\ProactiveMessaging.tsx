"use client"
import withSidebar from '@/components/withSidebar';
import React, { useEffect, useState } from 'react';
import Select from 'react-select';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css'; // Import Quill styles
import './ProactiveMessaging.css';
import axios from 'axios'; // Import axios for API calls
import ProtectedRoute from '@/components/ProtectedRoute';
import { getRequest, postRequest } from '@/APILayer/axios_helper';
import TurndownService from 'turndown'; // Import Turndown for HTML to Markdown conversion
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import adaptiveCardTemplates from './adaptiveCardTemplates.json'; // Import the JSON file
import { Router } from 'lucide-react';
import { useRouter } from 'next/navigation';


// Type for the adaptive card state
type AdaptiveCard = {
    value: string;
    label: string;
} | null;

const ProactiveMessaging = () => {
    const [selectedCompanies, setSelectedCompanies] = useState<any[]>([]);
    const [companyOptions, setCompanyOptions] = useState<any[]>([]); // State for company options
    const [selectedGroups, setSelectedGroups] = useState<any[]>([]);
    const [selectedAdaptiveCard, setSelectedAdaptiveCard] = useState<AdaptiveCard>(null);
    const [messageContent, setMessageContent] = useState('');
    const [htmlContent, setHtmlContent] = useState(''); // To store HTML content for the editor
    const [groupOptions, setGroupOptions] = useState<any[]>([]); // State for all group options
    const [filteredGroupOptions, setFilteredGroupOptions] = useState<any[]>([]); // State for filtered group options
    const [isSending, setIsSending] = useState(false);

    const router = useRouter(); // Initialize router for navigation

    // Initialize Turndown service for HTML to Markdown conversion
    const turndownService = new TurndownService();

    useEffect(() => {
        console.log("Selected Companies >>", selectedCompanies);
    }, [selectedCompanies]);
    useEffect(() => {
        console.log("Selected Groups >>", selectedGroups);
    }, [selectedGroups]);
    useEffect(() => {
        console.log("Selected Adaptive Card >>", selectedAdaptiveCard);
    }, [selectedAdaptiveCard]);
    useEffect(() => {
        console.log("Message Content >>", messageContent);
    }, [messageContent]);

    // First, modify the useEffect for fetching groups and companies
    useEffect(() => {
        const fetchData = async () => {
            try {
                // First fetch groups
                const groupsResponse = await getRequest('/groups-by-company-ids');
                if (groupsResponse.success) {
                    const transformedGroups = groupsResponse.data.groups.map((group: any) => ({
                        value: group._id,
                        label: `${group.name} (${group.userCount})`,
                        companyId: group.companyId,
                        name: group.name
                    }));
                    setGroupOptions(transformedGroups);

                    // After getting groups, fetch companies
                    const companiesResponse = await getRequest('/company/get-all-companies');
                    if (companiesResponse.success) {
                        const transformedData = companiesResponse.data.map((company: any) => {
                            // Find all groups that belong to this company
                            const companyGroupIds = transformedGroups
                                .filter((group: any) => group.companyId === company._id)
                                .map((group: any) => group.value);

                            return {
                                value: company._id,
                                label: `${company.name} (${company.userCount})`,
                                groupIds: companyGroupIds,
                                name: company.name
                            };
                        });

                        const allOption = { value: 'all', label: 'All', groupIds: [] };
                        setCompanyOptions([allOption, ...transformedData]);
                    }
                }
            } catch (error) {
                console.error("Error fetching data:", error);
                toast.error("Failed to fetch companies and groups");
            }
        };

        fetchData();
    }, []); // Empty dependency array means this runs once on component mount

    // Then modify the handleCompanyChange function to use the groupIds
    const handleCompanyChange = (selected: any) => {
        let selectedComps;
        if (selected.some((option: any) => option.value === 'all')) {
            selectedComps = companyOptions.filter(option => option.value !== 'all');
            setSelectedCompanies(selectedComps);
        } else {
            selectedComps = selected;
            setSelectedCompanies(selected);
        }

        // Clear existing selected groups
        setSelectedGroups([]);

        // Get all group IDs from selected companies
        const allGroupIds = selectedComps.flatMap((company: any) => company.groupIds);

        // Filter groups based on the collected group IDs
        const filtered = groupOptions.filter(group => allGroupIds.includes(group.value));

        // Add 'All' option to filtered groups
        const allOption = { value: 'all', label: 'All' };
        setFilteredGroupOptions([allOption, ...filtered]);
    };

    // Modify the handleGroupChange function to match the company behavior
    const handleGroupChange = (selected: any) => {
        if (!selected) {
            setSelectedGroups([]);
            return;
        }

        if (selected.some((option: any) => option.value === 'all')) {
            // If 'All' is selected, select all available filtered groups except the 'All' option
            const allGroups = filteredGroupOptions.filter(option => option.value !== 'all');
            setSelectedGroups(allGroups);
        } else {
            setSelectedGroups(selected);
        }
    };

    const adaptiveCardOptions = [
        { value: 'scheduler-nudge', label: 'Scheduler Nudge' },
        { value: 'general-nudge', label: 'General Nudge' }
    ];

    const customStyles = {
        container: (provided: any) => ({
            ...provided,
            width: '100%', // Ensure the dropdown container takes the full width
        }),
        control: (provided: any) => ({
            ...provided,
            width: '100%', // Ensure the control element takes the full width
        }),
        menu: (provided: any) => ({
            ...provided,
            width: '100%', // Ensure the dropdown menu takes the full width
        }),
    };

    // Handle editor change
    const handleEditorChange = (html: string) => {
        setHtmlContent(html); // Keep HTML for the editor display
        const markdown = turndownService.turndown(html); // Convert to markdown
        setMessageContent(markdown); // Store markdown in state for API
        console.log('HTML content:', html);
        console.log('Converted to Markdown:', markdown);
    };

    const transformSelectedCompanies = (selectedCompanies: any[]) => {
        if (selectedCompanies.length === 0) return []
        return selectedCompanies.map(company => ({ _id: company.value, name: company.name, groupIds: company.groupIds }))
    }

    const transformSelectedGroups = (selectedGroups: any[]) => {
        if (selectedGroups.length === 0) return []
        return selectedGroups.map(group => ({ _id: group.value, name: group.name, companyId: group.companyId }))
    }

    const handleSendNotifications = async () => {
        let payload: any;

        setIsSending(true);
        try {
            let selectedCardTemplate = selectedAdaptiveCard
                ? adaptiveCardTemplates[selectedAdaptiveCard.value as keyof typeof adaptiveCardTemplates]
                : null;

            // Escape the markdown content to make it JSON-safe
            const escapedMessageContent = messageContent.replace(/\\/g, '\\\\').replace(/"/g, '\\"').replace(/\n/g, '\\n');

            // Replace the placeholder {{ADAPTIVECARD_TEXT}} with the escaped content
            if (selectedCardTemplate) {
                selectedCardTemplate = JSON.parse(
                    JSON.stringify(selectedCardTemplate).replace(/{{ADAPTIVECARD_TEXT}}/g, escapedMessageContent)
                );
            }

            payload = {
                selectedGroups: transformSelectedGroups(selectedGroups),
                selectedCompanies: transformSelectedCompanies(selectedCompanies),
                body_type: 'adaptivecard', // Default to adaptive card
                message: selectedCardTemplate, // Use adaptive card template or text content
                messageText: escapedMessageContent // Add the escaped message content
            };
            console.log('Notification payload:', payload);

            const response = await postRequest('/send-group-notifications', payload);

            // Show success toast
            toast.success('Notifications sent successfully!');

            // Reset all states to initial values
            setSelectedCompanies([]);
            setSelectedGroups([]);
            setSelectedAdaptiveCard(null);
            setMessageContent('');
            setHtmlContent('');
            setFilteredGroupOptions([]);
        } catch (error: any) {
            console.error('Error sending notifications:', error);
            // Show error toast
            toast.error(error.response?.data.message);
        } finally {
            setIsSending(false); // Reset loading state regardless of success/failure
        }
    };

    const isFormValid = () => {
        // Check if companies and adaptive card are selected
        return selectedCompanies.length > 0 && selectedAdaptiveCard !== null && messageContent.trim() !== '';
    };

    return (
        <ProtectedRoute>
            <div className="main-container">
                <div className="header-container">
                    <div style={{ width: '100px' }}></div> {/* Spacer for alignment */}
                    <h1 className="header-title">Broadcast Messages</h1>
                    <button className="view-history-btn" onClick={() => router.push('/notification-history')}>
                        View History
                    </button>
                </div>
                <div className='select-company-dropdown'>
                    <label>Choose Employers</label>
                    <Select
                        isMulti
                        // @ts-ignore
                        options={companyOptions} // Use the fetched company options
                        value={selectedCompanies} // Show only individual companies as selected
                        onChange={handleCompanyChange}
                        placeholder="Choose Employers..."
                        styles={customStyles}
                    />
                </div>
                <div className='select-group-dropdown'>
                    <label>Target Employee Groups</label>
                    <Select
                        isMulti
                        // @ts-ignore
                        options={filteredGroupOptions}
                        value={selectedGroups}
                        onChange={handleGroupChange}
                        placeholder="Target Employee Groups..."
                        styles={customStyles}
                    />
                </div>
                <div className='select-adaptive-card-dropdown'>
                    <label>Select Adaptive Card</label>
                    <Select
                        options={adaptiveCardOptions}
                        value={selectedAdaptiveCard}
                        onChange={(selected: AdaptiveCard) => setSelectedAdaptiveCard(selected)}
                        placeholder="Select Adaptive Card..."
                        styles={customStyles}
                    />
                </div>
                <div className='wysiwyg-editor'>
                    <label>Message Content</label>
                    <ReactQuill
                        theme="snow"
                        value={htmlContent}
                        onChange={handleEditorChange}
                        placeholder="Write your message here..."
                        modules={{
                            toolbar: [
                                ['bold', 'italic', 'underline'],
                                [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                                ['link'],
                            ]
                        }}
                        style={{ height: '250px' }}
                    />
                </div>

                <div className='send-notifications-container'>
                    <button
                        onClick={handleSendNotifications}
                        disabled={isSending || !isFormValid()}
                    >
                        {isSending ? 'Sending...' : 'Send Message'}
                    </button>
                </div>
            </div>
            <ToastContainer
                position="top-right"
                autoClose={3000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="light"
            />
        </ProtectedRoute>
    )
}

export default withSidebar(ProactiveMessaging);