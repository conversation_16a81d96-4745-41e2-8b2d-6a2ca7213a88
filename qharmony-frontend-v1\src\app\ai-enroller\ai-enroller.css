/* AI Enroller Main Page Styles - using design system */
.ai-enroller-page {
  background: var(--white);
  min-height: 100vh;
}

/* AI Assistant Header - using design system */
.ai-assistant-header {
  display: flex;
  align-items: flex-start;
  gap: clamp(1rem, 3vw, 1.5rem);
  background: var(--gray-50);
  padding: clamp(1.5rem, 4vw, 2.5rem);
  border-radius: clamp(0.75rem, 2vw, 1.25rem);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--gray-200);
  margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
}

.ai-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: clamp(3rem, 6vw, 4rem);
  height: clamp(3rem, 6vw, 4rem);
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.ai-message {
  flex: 1;
}

.ai-greeting {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: clamp(1rem, 2.5vw, 1.125rem);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}

.sparkle-icon {
  color: var(--warning-primary);
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.1) rotate(180deg); opacity: 0.8; }
}

.ai-message p {
  font-size: clamp(0.875rem, 2vw, 1rem);
  color: var(--gray-600);
  line-height: 1.6;
  margin: 0;
}

/* Action Cards - using design system */
.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
  gap: clamp(1rem, 3vw, 1.5rem);
}

.action-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: clamp(0.75rem, 2vw, 1rem);
  padding: clamp(1.25rem, 4vw, 2rem);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: clamp(1rem, 3vw, 1.5rem);
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover {
  border-color: var(--black);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.action-card:hover::before {
  opacity: 1;
}

.action-icon {
  background: var(--primary-gradient);
  color: var(--white);
  width: clamp(3rem, 6vw, 4rem);
  height: clamp(3rem, 6vw, 4rem);
  border-radius: clamp(0.75rem, 2vw, 1rem);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.action-card:hover .action-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
}

.action-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.action-content h3 {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.action-card:hover .action-content h3 {
  color: var(--black);
}

.action-content p {
  font-size: clamp(0.8rem, 2vw, 0.9rem);
  color: var(--gray-600);
  margin: 0;
  line-height: 1.5;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .ai-assistant-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .ai-greeting {
    justify-content: center;
  }

  .action-cards {
    grid-template-columns: 1fr;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .action-card {
    padding: 1.5rem 1rem;
  }

  .ai-assistant-header {
    padding: 1.5rem;
  }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
  .action-cards {
    grid-template-columns: repeat(2, 1fr);
    max-width: 800px;
    margin: 0 auto;
  }
}
