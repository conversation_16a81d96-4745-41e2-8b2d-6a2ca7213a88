
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrendingUp, DollarSign, Shield, ChevronDown, ChevronUp } from 'lucide-react';

interface ROICalculatorProps {
  planCost: number;
  planType: 'medical' | 'dental' | 'vision';
  deductible?: number;
  familySize?: string;
}

export const ROICalculator = ({ planCost, planType, deductible = 0, familySize = 'self' }: ROICalculatorProps) => {
  const [showFullDetails, setShowFullDetails] = useState(false);

  const calculateROI = () => {
    const annualCost = planCost * 26; // bi-weekly paychecks
    const familyMultiplier = familySize === 'family' ? 3 : familySize === 'spouse' ? 2 : 1;
    
    let potentialSavings = 0;
    let employerContribution = 0;
    let scenarios = [];

    switch (planType) {
      case 'medical':
        employerContribution = annualCost * 4; // Employer typically pays 80%
        potentialSavings = 15000 * familyMultiplier; // Average medical costs without insurance
        scenarios = [
          { event: 'Annual Physical', withPlan: 25, withoutPlan: 400 },
          { event: 'Urgent Care Visit', withPlan: 50, withoutPlan: 300 },
          { event: 'Specialist Visit', withPlan: 40, withoutPlan: 250 },
          { event: 'Emergency Room', withPlan: 250, withoutPlan: 2500 }
        ];
        break;
      case 'dental':
        employerContribution = annualCost * 0.5; // 50% employer contribution
        potentialSavings = 1200 * familyMultiplier;
        scenarios = [
          { event: 'Cleaning & Exam', withPlan: 0, withoutPlan: 200 },
          { event: 'Filling', withPlan: 50, withoutPlan: 250 },
          { event: 'Crown', withPlan: 400, withoutPlan: 1200 },
          { event: 'Root Canal', withPlan: 300, withoutPlan: 1500 }
        ];
        break;
      case 'vision':
        employerContribution = annualCost * 0.5;
        potentialSavings = 500 * familyMultiplier;
        scenarios = [
          { event: 'Eye Exam', withPlan: 10, withoutPlan: 150 },
          { event: 'Glasses Frame', withPlan: 20, withoutPlan: 200 },
          { event: 'Contact Lenses', withPlan: 50, withoutPlan: 300 }
        ];
        break;
    }

    const totalValue = employerContribution + potentialSavings;
    const netBenefit = totalValue - annualCost;
    const roiPercentage = Math.round((netBenefit / annualCost) * 100);

    return { annualCost, employerContribution, potentialSavings, totalValue, netBenefit, roiPercentage, scenarios };
  };

  const roi = calculateROI();

  return (
    <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-900 border-blue-200 dark:border-blue-800">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between text-blue-800 dark:text-blue-200">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            <span className="text-sm">💰 ROI Analysis</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFullDetails(!showFullDetails)}
            className="h-6 text-xs"
          >
            {showFullDetails ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3 pt-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge className="bg-green-600 text-white text-xs">
              {roi.roiPercentage}% ROI
            </Badge>
            <span className="text-sm text-muted-foreground">
              ${roi.netBenefit.toLocaleString()} net benefit
            </span>
          </div>
        </div>

        {showFullDetails && (
          <>
            <div className="grid grid-cols-2 gap-2">
              <div className="text-center p-2 bg-white dark:bg-gray-800 rounded text-xs">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <DollarSign className="w-3 h-3 text-green-600" />
                  <span className="text-muted-foreground">You Pay</span>
                </div>
                <p className="font-bold text-blue-600">${roi.annualCost.toLocaleString()}</p>
              </div>
              <div className="text-center p-2 bg-white dark:bg-gray-800 rounded text-xs">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Shield className="w-3 h-3 text-purple-600" />
                  <span className="text-muted-foreground">You Get</span>
                </div>
                <p className="font-bold text-green-600">${roi.totalValue.toLocaleString()}</p>
              </div>
            </div>

            <div className="space-y-1">
              <p className="font-medium text-xs">💡 Cost Examples:</p>
              {roi.scenarios.slice(0, 2).map((scenario, index) => (
                <div key={index} className="flex justify-between text-xs bg-white dark:bg-gray-800 p-2 rounded">
                  <span>{scenario.event}</span>
                  <div className="flex gap-2">
                    <span className="text-green-600 font-medium">${scenario.withPlan}</span>
                    <span className="text-gray-400">vs</span>
                    <span className="text-red-500 line-through">${scenario.withoutPlan}</span>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
