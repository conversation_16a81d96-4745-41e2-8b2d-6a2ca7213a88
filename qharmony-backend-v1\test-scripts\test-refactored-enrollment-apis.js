/**
 * 🎯 REFACTORED ENROLLMENT APIS TEST SUITE
 * 
 * This script tests the 4 refactored employee enrollment APIs to ensure they maintain
 * the same functionality after being refactored to use service layer methods:
 * 
 * 1. GET /api/pre-enrollment/employee-enrollments/enrollment-periods/:planAssignmentId
 * 2. POST /api/pre-enrollment/employee-enrollments/estimate-plan-costs
 * 3. GET /api/pre-enrollment/employee-enrollments/expired
 * 4. POST /api/pre-enrollment/employee-enrollments/check-expired
 * 
 * Tests both functionality and response format consistency.
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/pre-enrollment`;

// Test user IDs (replace with actual IDs from your system)
const TEST_USER_IDS = {
  SUPER_ADMIN: '6838677aef6db0212bcfdacd', // Replace with actual SuperAdmin ID
  BROKER: '6838677aef6db0212bcfdace',     // Replace with actual Broker ID
  COMPANY_ADMIN: '6838677aef6db0212bcfdacf', // Replace with actual Company Admin ID
  EMPLOYEE: '6838677aef6db0212bcfdad0'      // Replace with actual Employee ID
};

// Test data (replace with actual IDs from your system)
const TEST_DATA = {
  PLAN_ASSIGNMENT_ID: '6838677aef6db0212bcfdad1', // Replace with actual Plan Assignment ID
  EMPLOYEE_ID: '6838677aef6db0212bcfdad2',        // Replace with actual Employee ID
  COMPANY_ID: '6838677aef6db0212bcfdad3'          // Replace with actual Company ID
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
};

const logSection = (title) => {
  console.log('\n' + '='.repeat(70));
  console.log(`🎯 ${title}`);
  console.log('='.repeat(70));
};

const makeRequest = async (method, endpoint, data = null, userId = TEST_USER_IDS.SUPER_ADMIN) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        'user-id': userId
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
};

const assert = (condition, message, testName = 'unknown') => {
  if (condition) {
    testResults.passed++;
    log(`✅ PASS: ${message}`, 'success');
  } else {
    testResults.failed++;
    testResults.errors.push({ test: testName, message });
    log(`❌ FAIL: ${message}`, 'error');
  }
};

const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Test 1: Get Enrollment Periods API
 * GET /api/pre-enrollment/employee-enrollments/enrollment-periods/:planAssignmentId
 */
async function testGetEnrollmentPeriods() {
  logSection('TEST 1: GET ENROLLMENT PERIODS API');
  
  try {
    // Test 1.1: Valid request with SuperAdmin
    log('Test 1.1: Testing valid request with SuperAdmin');
    const result1 = await makeRequest(
      'GET', 
      `/api/pre-enrollment/employee-enrollments/enrollment-periods/${TEST_DATA.PLAN_ASSIGNMENT_ID}`,
      null,
      TEST_USER_IDS.SUPER_ADMIN
    );
    
    assert(result1.success, 'SuperAdmin should be able to get enrollment periods', 'enrollmentPeriods');
    
    if (result1.success) {
      const data = result1.data;
      assert(data.success === true, 'Response should have success: true', 'enrollmentPeriods');
      assert(data.planAssignmentId === TEST_DATA.PLAN_ASSIGNMENT_ID, 'Response should include planAssignmentId', 'enrollmentPeriods');
      assert(data.enrollmentPeriods !== undefined, 'Response should include enrollmentPeriods', 'enrollmentPeriods');
      assert(data.currentDate !== undefined, 'Response should include currentDate', 'enrollmentPeriods');
      
      // Check enrollment periods structure
      const periods = data.enrollmentPeriods;
      assert(periods.openEnrollment !== undefined, 'Should include openEnrollment period', 'enrollmentPeriods');
      assert(periods.newHire !== undefined, 'Should include newHire period', 'enrollmentPeriods');
      assert(periods.qualifyingLifeEvent !== undefined, 'Should include qualifyingLifeEvent period', 'enrollmentPeriods');
    }
    
    await wait(500);
    
    // Test 1.2: Invalid plan assignment ID
    log('Test 1.2: Testing invalid plan assignment ID');
    const result2 = await makeRequest(
      'GET', 
      '/api/pre-enrollment/employee-enrollments/enrollment-periods/invalid-id',
      null,
      TEST_USER_IDS.SUPER_ADMIN
    );
    
    assert(!result2.success, 'Invalid plan assignment ID should fail', 'enrollmentPeriods');
    assert(result2.status === 404 || result2.status === 400, 'Should return 404 or 400 for invalid ID', 'enrollmentPeriods');
    
    await wait(500);
    
    // Test 1.3: Access control - Employee access
    log('Test 1.3: Testing employee access control');
    const result3 = await makeRequest(
      'GET', 
      `/api/pre-enrollment/employee-enrollments/enrollment-periods/${TEST_DATA.PLAN_ASSIGNMENT_ID}`,
      null,
      TEST_USER_IDS.EMPLOYEE
    );
    
    // Employee should either succeed (if they have access) or fail with 403
    if (!result3.success) {
      assert(result3.status === 403, 'Employee without access should get 403', 'enrollmentPeriods');
    }
    
  } catch (error) {
    log(`Test 1 failed with error: ${error.message}`, 'error');
    testResults.failed++;
    testResults.errors.push({ test: 'enrollmentPeriods', message: error.message });
  }
}

/**
 * Test 2: Estimate Plan Costs API
 * POST /api/pre-enrollment/employee-enrollments/estimate-plan-costs
 */
async function testEstimatePlanCosts() {
  logSection('TEST 2: ESTIMATE PLAN COSTS API');
  
  try {
    // Test 2.1: Valid request with default scenarios
    log('Test 2.1: Testing valid request with default scenarios');
    const requestData1 = {
      planAssignmentId: TEST_DATA.PLAN_ASSIGNMENT_ID
    };
    
    const result1 = await makeRequest(
      'POST', 
      '/api/pre-enrollment/employee-enrollments/estimate-plan-costs',
      requestData1,
      TEST_USER_IDS.SUPER_ADMIN
    );
    
    assert(result1.success, 'SuperAdmin should be able to estimate plan costs', 'estimatePlanCosts');
    
    if (result1.success) {
      const data = result1.data;
      assert(data.success === true, 'Response should have success: true', 'estimatePlanCosts');
      assert(data.planAssignmentId === TEST_DATA.PLAN_ASSIGNMENT_ID, 'Response should include planAssignmentId', 'estimatePlanCosts');
      assert(data.costEstimations !== undefined, 'Response should include costEstimations', 'estimatePlanCosts');
      assert(data.metadata !== undefined, 'Response should include metadata', 'estimatePlanCosts');
      
      // Check metadata structure
      const metadata = data.metadata;
      assert(metadata.scenarioCount !== undefined, 'Metadata should include scenarioCount', 'estimatePlanCosts');
      assert(metadata.tierCount !== undefined, 'Metadata should include tierCount', 'estimatePlanCosts');
      assert(metadata.calculatedAt !== undefined, 'Metadata should include calculatedAt', 'estimatePlanCosts');
    }
    
    await wait(500);
    
    // Test 2.2: Valid request with custom scenarios
    log('Test 2.2: Testing valid request with custom scenarios');
    const requestData2 = {
      planAssignmentId: TEST_DATA.PLAN_ASSIGNMENT_ID,
      scenarios: [
        { employeeAge: 30, employeeSalary: 60000, description: 'Test scenario 1' },
        { employeeAge: 40, employeeSalary: 80000, description: 'Test scenario 2' }
      ]
    };
    
    const result2 = await makeRequest(
      'POST', 
      '/api/pre-enrollment/employee-enrollments/estimate-plan-costs',
      requestData2,
      TEST_USER_IDS.SUPER_ADMIN
    );
    
    assert(result2.success, 'Custom scenarios should work', 'estimatePlanCosts');
    
    if (result2.success) {
      const metadata = result2.data.metadata;
      assert(metadata.scenarioCount === 2, 'Should use custom scenario count', 'estimatePlanCosts');
    }
    
    await wait(500);
    
    // Test 2.3: Missing planAssignmentId
    log('Test 2.3: Testing missing planAssignmentId');
    const result3 = await makeRequest(
      'POST', 
      '/api/pre-enrollment/employee-enrollments/estimate-plan-costs',
      {},
      TEST_USER_IDS.SUPER_ADMIN
    );
    
    assert(!result3.success, 'Missing planAssignmentId should fail', 'estimatePlanCosts');
    assert(result3.status === 400, 'Should return 400 for missing planAssignmentId', 'estimatePlanCosts');
    
  } catch (error) {
    log(`Test 2 failed with error: ${error.message}`, 'error');
    testResults.failed++;
    testResults.errors.push({ test: 'estimatePlanCosts', message: error.message });
  }
}

/**
 * Test 3: Get Expired Enrollments API
 * GET /api/pre-enrollment/employee-enrollments/expired
 */
async function testGetExpiredEnrollments() {
  logSection('TEST 3: GET EXPIRED ENROLLMENTS API');

  try {
    // Test 3.1: Valid request with user mode (default)
    log('Test 3.1: Testing user mode (default)');
    const result1 = await makeRequest(
      'GET',
      '/api/pre-enrollment/employee-enrollments/expired',
      null,
      TEST_USER_IDS.SUPER_ADMIN
    );

    assert(result1.success, 'SuperAdmin should be able to get expired enrollments', 'getExpiredEnrollments');

    if (result1.success) {
      const data = result1.data;
      assert(data.success === true, 'Response should have success: true', 'getExpiredEnrollments');
      assert(data.mode === 'user', 'Default mode should be user', 'getExpiredEnrollments');
      assert(data.expiredEnrollments !== undefined, 'Response should include expiredEnrollments', 'getExpiredEnrollments');
      assert(data.count !== undefined, 'Response should include count', 'getExpiredEnrollments');
      assert(data.message !== undefined, 'Response should include message', 'getExpiredEnrollments');
      assert(data.expiryCheckPerformed === true, 'Should indicate expiry check was performed', 'getExpiredEnrollments');
      assert(data.timestamp !== undefined, 'Response should include timestamp', 'getExpiredEnrollments');
    }

    await wait(500);

    // Test 3.2: Valid request with planAssignments mode
    log('Test 3.2: Testing planAssignments mode');
    const result2 = await makeRequest(
      'GET',
      `/api/pre-enrollment/employee-enrollments/expired?mode=planAssignments&planAssignmentIds=${TEST_DATA.PLAN_ASSIGNMENT_ID}`,
      null,
      TEST_USER_IDS.SUPER_ADMIN
    );

    assert(result2.success, 'SuperAdmin should be able to get expired enrollments by plan assignments', 'getExpiredEnrollments');

    if (result2.success) {
      const data = result2.data;
      assert(data.mode === 'planAssignments', 'Mode should be planAssignments', 'getExpiredEnrollments');
      assert(data.planAssignmentIds !== undefined, 'Response should include planAssignmentIds', 'getExpiredEnrollments');
    }

    await wait(500);

    // Test 3.3: Invalid mode
    log('Test 3.3: Testing invalid mode');
    const result3 = await makeRequest(
      'GET',
      '/api/pre-enrollment/employee-enrollments/expired?mode=invalid',
      null,
      TEST_USER_IDS.SUPER_ADMIN
    );

    assert(!result3.success, 'Invalid mode should fail', 'getExpiredEnrollments');
    assert(result3.status === 400, 'Should return 400 for invalid mode', 'getExpiredEnrollments');

    await wait(500);

    // Test 3.4: planAssignments mode without planAssignmentIds
    log('Test 3.4: Testing planAssignments mode without planAssignmentIds');
    const result4 = await makeRequest(
      'GET',
      '/api/pre-enrollment/employee-enrollments/expired?mode=planAssignments',
      null,
      TEST_USER_IDS.SUPER_ADMIN
    );

    assert(!result4.success, 'planAssignments mode without planAssignmentIds should fail', 'getExpiredEnrollments');
    assert(result4.status === 400, 'Should return 400 for missing planAssignmentIds', 'getExpiredEnrollments');

    await wait(500);

    // Test 3.5: Employee access control
    log('Test 3.5: Testing employee access control');
    const result5 = await makeRequest(
      'GET',
      '/api/pre-enrollment/employee-enrollments/expired',
      null,
      TEST_USER_IDS.EMPLOYEE
    );

    // Employee should be able to see their own expired enrollments
    if (result5.success) {
      assert(result5.data.mode === 'user', 'Employee should get user mode', 'getExpiredEnrollments');
    } else {
      // If it fails, it should be due to access control
      assert(result5.status === 403, 'Employee access failure should be 403', 'getExpiredEnrollments');
    }

  } catch (error) {
    log(`Test 3 failed with error: ${error.message}`, 'error');
    testResults.failed++;
    testResults.errors.push({ test: 'getExpiredEnrollments', message: error.message });
  }
}

/**
 * Test 4: Check Expired Enrollments API
 * POST /api/pre-enrollment/employee-enrollments/check-expired
 */
async function testCheckExpiredEnrollments() {
  logSection('TEST 4: CHECK EXPIRED ENROLLMENTS API');

  try {
    // Test 4.1: Valid request with SuperAdmin
    log('Test 4.1: Testing valid request with SuperAdmin');
    const result1 = await makeRequest(
      'POST',
      '/api/pre-enrollment/employee-enrollments/check-expired',
      {},
      TEST_USER_IDS.SUPER_ADMIN
    );

    assert(result1.success, 'SuperAdmin should be able to check expired enrollments', 'checkExpiredEnrollments');

    if (result1.success) {
      const data = result1.data;
      assert(data.success === true, 'Response should have success: true', 'checkExpiredEnrollments');
      assert(data.message !== undefined, 'Response should include message', 'checkExpiredEnrollments');
      assert(data.expiredCount !== undefined, 'Response should include expiredCount', 'checkExpiredEnrollments');
      assert(data.updatedEnrollmentIds !== undefined, 'Response should include updatedEnrollmentIds', 'checkExpiredEnrollments');
      assert(data.timestamp !== undefined, 'Response should include timestamp', 'checkExpiredEnrollments');
      assert(data.performedBy !== undefined, 'Response should include performedBy', 'checkExpiredEnrollments');
    }

    await wait(500);

    // Test 4.2: Access control - Non-SuperAdmin should fail
    log('Test 4.2: Testing access control with Broker (should fail)');
    const result2 = await makeRequest(
      'POST',
      '/api/pre-enrollment/employee-enrollments/check-expired',
      {},
      TEST_USER_IDS.BROKER
    );

    assert(!result2.success, 'Non-SuperAdmin should not be able to check expired enrollments', 'checkExpiredEnrollments');
    assert(result2.status === 403, 'Should return 403 for non-SuperAdmin', 'checkExpiredEnrollments');

    await wait(500);

    // Test 4.3: Access control - Company Admin should fail
    log('Test 4.3: Testing access control with Company Admin (should fail)');
    const result3 = await makeRequest(
      'POST',
      '/api/pre-enrollment/employee-enrollments/check-expired',
      {},
      TEST_USER_IDS.COMPANY_ADMIN
    );

    assert(!result3.success, 'Company Admin should not be able to check expired enrollments', 'checkExpiredEnrollments');
    assert(result3.status === 403, 'Should return 403 for Company Admin', 'checkExpiredEnrollments');

    await wait(500);

    // Test 4.4: Access control - Employee should fail
    log('Test 4.4: Testing access control with Employee (should fail)');
    const result4 = await makeRequest(
      'POST',
      '/api/pre-enrollment/employee-enrollments/check-expired',
      {},
      TEST_USER_IDS.EMPLOYEE
    );

    assert(!result4.success, 'Employee should not be able to check expired enrollments', 'checkExpiredEnrollments');
    assert(result4.status === 403, 'Should return 403 for Employee', 'checkExpiredEnrollments');

  } catch (error) {
    log(`Test 4 failed with error: ${error.message}`, 'error');
    testResults.failed++;
    testResults.errors.push({ test: 'checkExpiredEnrollments', message: error.message });
  }
}

/**
 * Test 5: Cross-API Integration Test
 * Test the APIs work together correctly
 */
async function testCrossAPIIntegration() {
  logSection('TEST 5: CROSS-API INTEGRATION');

  try {
    log('Test 5.1: Testing API integration flow');

    // Step 1: Get enrollment periods
    const periodsResult = await makeRequest(
      'GET',
      `/api/pre-enrollment/employee-enrollments/enrollment-periods/${TEST_DATA.PLAN_ASSIGNMENT_ID}`,
      null,
      TEST_USER_IDS.SUPER_ADMIN
    );

    if (periodsResult.success) {
      log('✅ Step 1: Successfully retrieved enrollment periods');

      // Step 2: Estimate plan costs
      const costsResult = await makeRequest(
        'POST',
        '/api/pre-enrollment/employee-enrollments/estimate-plan-costs',
        { planAssignmentId: TEST_DATA.PLAN_ASSIGNMENT_ID },
        TEST_USER_IDS.SUPER_ADMIN
      );

      if (costsResult.success) {
        log('✅ Step 2: Successfully estimated plan costs');

        // Step 3: Get expired enrollments
        const expiredResult = await makeRequest(
          'GET',
          '/api/pre-enrollment/employee-enrollments/expired',
          null,
          TEST_USER_IDS.SUPER_ADMIN
        );

        if (expiredResult.success) {
          log('✅ Step 3: Successfully retrieved expired enrollments');

          // Step 4: Check expired enrollments (SuperAdmin only)
          const checkResult = await makeRequest(
            'POST',
            '/api/pre-enrollment/employee-enrollments/check-expired',
            {},
            TEST_USER_IDS.SUPER_ADMIN
          );

          if (checkResult.success) {
            log('✅ Step 4: Successfully checked expired enrollments');
            assert(true, 'All 4 refactored APIs work together correctly', 'integration');
          } else {
            assert(false, 'Step 4 failed: Check expired enrollments', 'integration');
          }
        } else {
          assert(false, 'Step 3 failed: Get expired enrollments', 'integration');
        }
      } else {
        assert(false, 'Step 2 failed: Estimate plan costs', 'integration');
      }
    } else {
      assert(false, 'Step 1 failed: Get enrollment periods', 'integration');
    }

  } catch (error) {
    log(`Integration test failed with error: ${error.message}`, 'error');
    testResults.failed++;
    testResults.errors.push({ test: 'integration', message: error.message });
  }
}

/**
 * Main test runner
 */
async function runRefactoredAPITests() {
  try {
    logSection('🎯 REFACTORED ENROLLMENT APIS TEST SUITE');
    log(`📅 Test Started: ${new Date().toISOString()}`);
    log(`🌐 Base URL: ${BASE_URL}`);
    log(`🎯 Testing 4 Refactored APIs\n`);

    // Validate test configuration
    log('🔧 Validating test configuration...');
    if (!TEST_DATA.PLAN_ASSIGNMENT_ID || TEST_DATA.PLAN_ASSIGNMENT_ID === '6838677aef6db0212bcfdad1') {
      log('⚠️  WARNING: Using default test data. Please update TEST_DATA with actual IDs from your system.', 'warning');
    }

    // Run all tests
    await testGetEnrollmentPeriods();
    await testEstimatePlanCosts();
    await testGetExpiredEnrollments();
    await testCheckExpiredEnrollments();
    await testCrossAPIIntegration();

    // Generate test summary
    logSection('🎯 TEST RESULTS SUMMARY');

    const totalTests = testResults.passed + testResults.failed;
    const successRate = totalTests > 0 ? ((testResults.passed / totalTests) * 100).toFixed(1) : 0;

    log(`📊 Total Tests: ${totalTests}`);
    log(`✅ Passed: ${testResults.passed}`);
    log(`❌ Failed: ${testResults.failed}`);
    log(`📈 Success Rate: ${successRate}%`);

    if (testResults.failed === 0) {
      log('\n🎉 ALL TESTS PASSED! The refactored APIs are working correctly.', 'success');
      log('\n📋 Verified Functionality:', 'info');
      log('  ✅ Get Enrollment Periods - Service layer delegation working', 'info');
      log('  ✅ Estimate Plan Costs - Service layer delegation working', 'info');
      log('  ✅ Get Expired Enrollments - Service layer delegation working', 'info');
      log('  ✅ Check Expired Enrollments - Service layer delegation working', 'info');
      log('  ✅ Cross-API Integration - All APIs work together', 'info');
      log('  ✅ Access Control - Proper permission validation', 'info');
      log('  ✅ Error Handling - Appropriate error responses', 'info');
      log('  ✅ Response Format - Consistent API response structure', 'info');
    } else {
      log('\n❌ SOME TESTS FAILED. Please review the errors below:', 'error');
      testResults.errors.forEach((error, index) => {
        log(`${index + 1}. [${error.test}] ${error.message}`, 'error');
      });
      log('\n💡 Please fix the issues and run the tests again.', 'warning');
    }

    return testResults.failed === 0;

  } catch (error) {
    log(`Test suite failed with error: ${error.message}`, 'error');
    return false;
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runRefactoredAPITests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test suite crashed:', error);
      process.exit(1);
    });
}

module.exports = {
  runRefactoredAPITests,
  testGetEnrollmentPeriods,
  testEstimatePlanCosts,
  testGetExpiredEnrollments,
  testCheckExpiredEnrollments,
  testCrossAPIIntegration
};
