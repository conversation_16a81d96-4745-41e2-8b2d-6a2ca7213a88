'use client';

import React, { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { 
  HiOutlineArrowLeft,
  HiOutlineCheckCircle,
  HiOutlineExclamationCircle,
  HiOutlineShieldCheck
} from 'react-icons/hi';
import '../../renewal.css';
import '../plan-detail.css';
import './validation.css';

interface ValidationCheck {
  id: string;
  title: string;
  description: string;
  details: string;
  status: 'passed' | 'warning' | 'failed';
}

const ValidationPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(5);

  const groupName = 'Green Valley Manufacturing';

  const validationChecks: ValidationCheck[] = [
    {
      id: 'eligibility',
      title: 'Eligibility',
      description: 'All employee tiers and age bands configured correctly',
      details: '142 employees eligible for medical, 138 for dental',
      status: 'passed'
    },
    {
      id: 'premiums',
      title: 'Premiums',
      description: 'Medical premium increase detected',
      details: 'Employee-only rate increased by 8.2% from previous year',
      status: 'warning'
    },
    {
      id: 'compliance',
      title: 'Compliance',
      description: 'All compliance requirements met',
      details: 'SBC documents uploaded, ACA requirements satisfied',
      status: 'passed'
    },
    {
      id: 'data-integrity',
      title: 'Data Integrity',
      description: 'No missing data detected',
      details: 'All required fields populated for each plan tier',
      status: 'passed'
    },
    {
      id: 'carrier-sync',
      title: 'Carrier Sync',
      description: 'Carrier rate verification pending',
      details: 'Recommend confirming rates with BCBS before finalizing',
      status: 'warning'
    }
  ];

  const steps = [
    { number: 1, title: 'Review Current Plans', subtitle: 'View existing benefit plans', active: false, completed: true },
    { number: 2, title: 'Renewal Options', subtitle: 'Choose renewal type', active: false, completed: true },
    { number: 3, title: 'Plan Configuration', subtitle: 'Set dates and modifications', active: false, completed: true },
    { number: 4, title: 'Document Upload', subtitle: 'Upload plan documents', active: false, completed: true },
    { number: 5, title: 'Validation', subtitle: 'Review and validate setup', active: currentStep === 5 },
    { number: 6, title: 'Finalize', subtitle: 'Complete renewal process', active: false },
    { number: 7, title: 'Export', subtitle: 'Download and share data', active: false }
  ];

  const passedCount = validationChecks.filter(check => check.status === 'passed').length;
  const warningCount = validationChecks.filter(check => check.status === 'warning').length;
  const failedCount = validationChecks.filter(check => check.status === 'failed').length;

  const handleContinue = () => {
    router.push(`/ai-enroller/renewal/${params.groupId}/finalize`);
  };

  const handleRerunValidation = () => {
    // Simulate re-running validation
    console.log('Re-running validation...');
  };

  const handlePrevious = () => {
    router.back();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <HiOutlineCheckCircle size={20} className="status-icon passed" />;
      case 'warning':
        return <HiOutlineExclamationCircle size={20} className="status-icon warning" />;
      case 'failed':
        return <HiOutlineExclamationCircle size={20} className="status-icon failed" />;
      default:
        return null;
    }
  };

  return (
    <div className="plan-renewal-detail">
      {/* Header */}
      <div className="detail-header">
        <button 
          className="back-btn"
          onClick={() => router.push('/ai-enroller/renewal')}
        >
          <HiOutlineArrowLeft size={20} />
          Back to Dashboard
        </button>
        
        <div className="header-info">
          <h1>Plan Renewal</h1>
          <h2>{groupName}</h2>
          <div className="step-indicator">Step {currentStep} of 7</div>
        </div>

        <div className="completion-status">
          71% Complete
        </div>
      </div>

      {/* Progress Steps */}
      <div className="renewal-steps">
        {steps.map((step, index) => (
          <div key={step.number} className={`renewal-step ${step.active ? 'active' : ''} ${step.completed ? 'completed' : ''}`}>
            <div className="step-number">
              {step.completed ? '✓' : step.number}
            </div>
            <div className="step-content">
              <div className="step-title">{step.title}</div>
              <div className="step-subtitle">{step.subtitle}</div>
            </div>
            {index < steps.length - 1 && <div className="step-connector"></div>}
          </div>
        ))}
      </div>

      {/* Validation Section */}
      <div className="validation-section">
        <div className="validation-header">
          <div className="validation-title">
            <HiOutlineShieldCheck size={20} />
            <h3>Plan Setup Validation</h3>
          </div>
          <p>Reviewing plan configuration for {groupName} to ensure everything is set up correctly before finalizing.</p>
        </div>

        <div className="validation-content">
          {/* Validation Checks */}
          <div className="validation-checks">
            {validationChecks.map((check) => (
              <div key={check.id} className={`validation-check ${check.status}`}>
                <div className="check-header">
                  {getStatusIcon(check.status)}
                  <div className="check-info">
                    <h4>{check.title}</h4>
                    <p>{check.description}</p>
                  </div>
                  <div className="check-status">
                    {check.status}
                  </div>
                </div>
                <div className="check-details">
                  {check.details}
                </div>
              </div>
            ))}
          </div>

          {/* Validation Summary */}
          <div className="validation-summary">
            <h4>Validation Summary</h4>
            <div className="summary-stats">
              <div className="stat-item passed">
                <div className="stat-number">{passedCount}</div>
                <div className="stat-label">Checks Passed</div>
              </div>
              <div className="stat-item warning">
                <div className="stat-number">{warningCount}</div>
                <div className="stat-label">Warnings</div>
              </div>
              <div className="stat-item failed">
                <div className="stat-number">{failedCount}</div>
                <div className="stat-label">Failed Checks</div>
              </div>
            </div>
          </div>

          {/* Warnings Notice */}
          {warningCount > 0 && (
            <div className="warnings-notice">
              <HiOutlineExclamationCircle size={20} />
              <div>
                <h4>Warnings Detected</h4>
                <p>Your plan setup has some warnings but no critical issues. You can proceed with the renewal, but we recommend reviewing the flagged items.</p>
              </div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="navigation-section">
          <button 
            className="nav-btn secondary"
            onClick={handlePrevious}
          >
            <HiOutlineArrowLeft size={16} />
            Previous
          </button>
          
          <div className="nav-actions">
            <button 
              className="nav-btn secondary"
              onClick={handleRerunValidation}
            >
              Re-run Validation
            </button>
            
            <button 
              className="nav-btn primary enabled"
              onClick={handleContinue}
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ValidationPage;
