'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface BotQuestionProps {
  question: string;
  context?: string;
  enableStreaming?: boolean;
  onStreamingComplete?: () => void;
}

export const BotQuestion: React.FC<BotQuestionProps> = ({
  question,
  context,
  enableStreaming = true,
  onStreamingComplete
}) => {
  const [displayedQuestion, setDisplayedQuestion] = useState(enableStreaming ? '' : question);
  const [displayedContext, setDisplayedContext] = useState(enableStreaming ? '' : (context || ''));
  const [questionComplete, setQuestionComplete] = useState(!enableStreaming);
  const [contextComplete, setContextComplete] = useState(!context || !enableStreaming);

  // Stream question text
  useEffect(() => {
    if (enableStreaming && displayedQuestion.length < question.length) {
      const timer = setTimeout(() => {
        setDisplayedQuestion(prev => prev + question[displayedQuestion.length]);
      }, 50);
      return () => clearTimeout(timer);
    } else if (displayedQuestion.length === question.length && !questionComplete) {
      setQuestionComplete(true);
    }
  }, [displayedQuestion, question, enableStreaming, questionComplete]);

  // Stream context text after question is complete
  useEffect(() => {
    if (enableStreaming && context && questionComplete && displayedContext.length < context.length) {
      const timer = setTimeout(() => {
        setDisplayedContext(prev => prev + context[displayedContext.length]);
      }, 30);
      return () => clearTimeout(timer);
    } else if (context && questionComplete && displayedContext.length === context.length && !contextComplete) {
      setContextComplete(true);
    }
  }, [questionComplete, displayedContext, context, enableStreaming, contextComplete]);

  // Call onStreamingComplete when both are done
  useEffect(() => {
    if (questionComplete && contextComplete && onStreamingComplete) {
      onStreamingComplete();
    }
  }, [questionComplete, contextComplete, onStreamingComplete]);

  return (
    <div style={{ display: 'flex', gap: '12px', marginBottom: '24px' }}>
      <div style={{
        width: '40px',
        height: '40px',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexShrink: 0,
        overflow: 'hidden'
      }}>
        <Image
          src="/brea.png"
          alt="Brea - AI Benefits Assistant"
          width={40}
          height={40}
          style={{ borderRadius: '50%' }}
        />
      </div>

      <div style={{ flex: 1 }}>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '16px',
          padding: '16px',
          border: '1px solid #e5e7eb'
        }}>
          <p style={{
            color: '#111827',
            fontWeight: '500',
            margin: 0,
            lineHeight: '1.5'
          }}>
            {displayedQuestion}
            {enableStreaming && displayedQuestion.length < question.length && (
              <span style={{ animation: 'pulse 1s infinite' }}>|</span>
            )}
          </p>
          {context && questionComplete && (
            <p style={{
              color: '#6b7280',
              fontSize: '14px',
              marginTop: '4px',
              margin: '4px 0 0 0',
              lineHeight: '1.5'
            }}>
              {displayedContext}
              {enableStreaming && displayedContext.length < context.length && (
                <span style={{ animation: 'pulse 1s infinite' }}>|</span>
              )}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
