{
  "compilerOptions": {
    "moduleResolution": "Node16",
    "target": "es2021",
    "sourceMap": true,
    "rootDir": "src",
    "outDir": "dist",
    "module": "node16",
    "removeComments": true,
    "importHelpers": true,
    "esModuleInterop": true,
    "strict": false,
    "useUnknownInCatchVariables": false,
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": false,
    "forceConsistentCasingInFileNames": false,
    "emitDecoratorMetadata": true,
    "noImplicitAny": false,
    "typeRoots": [
      "node_modules/@types",
      "src/types"
    ],
    "paths": {},
    "types": [
      "node"
    ],
    "skipLibCheck": true,
  },
  "include": [
    "src/**/*.ts"
  ],
  "exclude": [
    "node_modules/*"
  ],
  "typeRoots": [
    "./node_module/@types",
    "./src/types"
  ]
}