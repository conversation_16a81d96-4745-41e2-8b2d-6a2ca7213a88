import mongoose from 'mongoose';

// Interfaces for cost calculation
export interface CostCalculationInput {
  planAssignment: any;
  employeeAge?: number;
  selectedTier: string;
  employeeSalary?: number;  // For salary-based calculations
  payrollFrequency?: string; // ✅ NEW: For payroll deduction calculations
  // 🎯 REMOVED: dependentCount - cost is based on selected tier only, not dependent count
}

export interface CostCalculationResult {
  success: boolean;
  cost?: {
    // Monthly amounts (existing)
    employeeAmount: number;
    employerAmount: number;
    totalAmount: number;

    // ✅ NEW: Enhanced cost breakdown
    monthlyEmployeeAmount: number;
    monthlyEmployerAmount: number;
    monthlyTotalAmount: number;

    annualEmployeeAmount: number;
    annualEmployerAmount: number;
    annualTotalAmount: number;

    payrollEmployeeAmount: number;
    payrollEmployerAmount: number;
    payrollTotalAmount: number;

    // Metadata
    payrollFrequency: string;
    payPeriodsPerYear: number;
  };
  error?: string;
}

export interface ContributionPolicy {
  contributionType: string;   // "Fixed" or "Percentage"
  contributionAmount: number; // Contribution amount
}

/**
 * Cost Calculation Service
 *
 * This service handles all cost calculations for plan assignments and enrollments.
 *
 * 🎯 KEY PRINCIPLE: ONE PREMIUM PER PLAN ASSIGNMENT
 * - Each PlanAssignment has a single total premium covering ALL coverage subtypes
 * - Rate Structure + Coverage Tier = One cost calculation
 * - Employee pays once for the entire plan (Medical + Dental + Vision, etc.)
 * - No cost duplication across coverage subtypes
 *
 * Example:
 * - Plan: Health Plan with ["Medical", "Dental", "Vision"]
 * - PlanAssignment: Family tier = $800/month total
 * - Employee pays: $160/month for ALL three subtypes combined
 * - NOT: $160 for Medical + $160 for Dental + $160 for Vision
 *
 * This service is separated from models to avoid circular dependencies and follows
 * the single responsibility principle.
 */
export class CostCalculationService {

  /**
   * Calculate enrollment cost based on plan assignment and employee details
   *
   * 🎯 RETURNS: Single total cost for ALL coverage subtypes in the plan
   *
   * Example:
   * Input: Plan with ["Medical", "Dental", "Vision"], Family tier
   * Output: { totalAmount: 800, employeeAmount: 160, employerAmount: 640 }
   *
   * This represents the TOTAL cost for all three coverage subtypes combined,
   * not individual costs per subtype.
   */
  public static calculateEnrollmentCost(input: CostCalculationInput): CostCalculationResult {
    try {
      const { planAssignment, employeeAge, selectedTier, employeeSalary, payrollFrequency } = input;

      if (!planAssignment) {
        return { success: false, error: 'Plan assignment is required' };
      }

      // Get the coverage tier details
      const tierDetails = this.findCoverageTier(planAssignment, selectedTier);
      if (!tierDetails) {
        return { success: false, error: `Coverage tier '${selectedTier}' not found` };
      }

      // 🎯 Calculate base cost based on rate structure
      let baseCost = 0;
      let wasAdjusted = false; // Track if cost was adjusted by rate structure

      switch (planAssignment.rateStructure) {
        case 'Composite':
          baseCost = this.calculateCompositeCost(tierDetails);
          wasAdjusted = false; // Composite uses tier cost directly
          break;
        case 'Age-Banded':
          baseCost = this.calculateAgeBandedCost(planAssignment, employeeAge, tierDetails);
          wasAdjusted = Math.abs(baseCost - (tierDetails.totalCost || 0)) > 0.01;
          break;
        case 'Four-Tier':
          baseCost = this.calculateFourTierCost(tierDetails);
          wasAdjusted = false; // Four-tier uses tier cost directly
          break;
        case 'Age-Banded-Four-Tier':
          baseCost = this.calculateAgeBandedFourTierCost(planAssignment, employeeAge, tierDetails);
          wasAdjusted = Math.abs(baseCost - (tierDetails.totalCost || 0)) > 0.01;
          break;
        case 'Salary-Based':
          baseCost = this.calculateSalaryBasedCost(planAssignment, tierDetails, employeeSalary);
          wasAdjusted = Math.abs(baseCost - (tierDetails.totalCost || 0)) > 0.01;
          break;
        default:
          // Fallback to composite cost
          baseCost = tierDetails.totalCost || 0;
          wasAdjusted = false;
      }

      // 🎯 Apply employer and employee contribution policies (or use tier-specific costs if provided)
      const monthlyContribution = this.applyContributionPolicies(
        baseCost,
        planAssignment.employerContribution,
        planAssignment.employeeContribution,
        tierDetails, // Pass tier details to check for pre-calculated costs
        wasAdjusted  // 🎯 NEW: Explicitly tell if cost was adjusted
      );

      // ✅ NEW: Calculate enhanced cost breakdown with payroll frequency
      const enhancedCost = this.calculateEnhancedCostBreakdown(monthlyContribution, payrollFrequency);

      return {
        success: true,
        cost: enhancedCost
      };

    } catch (error) {
      console.error('Error calculating enrollment cost:', error);
      return { success: false, error: 'Internal error calculating cost' };
    }
  }

  /**
   * Find coverage tier details from plan assignment
   */
  private static findCoverageTier(planAssignment: any, tierName: string): any {
    if (!planAssignment.coverageTiers || !Array.isArray(planAssignment.coverageTiers)) {
      return null;
    }

    return planAssignment.coverageTiers.find(tier => tier.tierName === tierName);
  }



  /**
   * Calculate composite rate cost (flat rate regardless of age/dependents)
   */
  private static calculateCompositeCost(tierDetails: any): number {
    return tierDetails.totalCost || 0;
  }

  /**
   * Calculate age-banded cost based on employee age
   * 🎯 UPDATED: Supports both fixed amounts and multiplier factors
   */
  private static calculateAgeBandedCost(planAssignment: any, employeeAge: number = 30, tierDetails: any): number {
    if (!planAssignment.ageBandedRates || !Array.isArray(planAssignment.ageBandedRates)) {
      return tierDetails.totalCost || 0;
    }

    // Find the appropriate age band
    const ageBand = planAssignment.ageBandedRates.find((band: any) =>
      employeeAge >= band.ageMin && employeeAge <= band.ageMax
    );

    if (ageBand) {
      const baseCost = tierDetails.totalCost || 0;

      // Support both types: multiplier (industry standard) and fixed (QHarmony legacy)
      if (ageBand.type === 'multiplier') {
        // MULTIPLIER: Scale the base cost by factor
        // Example: baseCost (1000) * rate (1.3) = 1300
        return baseCost * (ageBand.rate || 1.0);
      } else {
        // FIXED: Add fixed amount to base cost (default behavior)
        // Example: baseCost (1000) + rate (300) = 1300
        return baseCost + (ageBand.rate || 0);
      }
    }

    // Fallback to tier cost if no age band found
    return tierDetails.totalCost || 0;
  }

  /**
   * Calculate four-tier cost
   * 🎯 CORRECTED: Cost is based on selected tier only, not dependent count
   */
  private static calculateFourTierCost(tierDetails: any): number {
    // Four-tier typically means: Employee Only, Employee+Spouse, Employee+Child(ren), Family
    // The tier should already be selected, so return the tier cost
    // Note: Validation of tier vs dependents should be done at enrollment time, not cost calculation
    return tierDetails.totalCost || 0;
  }

  /**
   * Calculate age-banded four-tier cost (combination of both)
   * 🎯 UPDATED: Supports both fixed amounts and multiplier factors (same as age-banded)
   */
  private static calculateAgeBandedFourTierCost(planAssignment: any, employeeAge: number = 30, tierDetails: any): number {
    if (!planAssignment.ageBandedRates || !Array.isArray(planAssignment.ageBandedRates)) {
      return this.calculateFourTierCost(tierDetails);
    }

    // Find the appropriate age band
    const ageBand = planAssignment.ageBandedRates.find((band: any) =>
      employeeAge >= band.ageMin && employeeAge <= band.ageMax
    );

    if (ageBand) {
      const baseCost = tierDetails.totalCost || 0;

      // Support both types: multiplier (industry standard) and fixed (QHarmony legacy)
      if (ageBand.type === 'multiplier') {
        // MULTIPLIER: Scale the base cost by factor
        return baseCost * (ageBand.rate || 1.0);
      } else {
        // FIXED: Add fixed amount to base cost (default behavior)
        return baseCost + (ageBand.rate || 0);
      }
    }

    // Fallback to four-tier calculation
    return this.calculateFourTierCost(tierDetails);
  }

  /**
   * Calculate salary-based cost
   * 🎯 UPDATED: Supports both fixed amounts and multiplier factors (like age-banded)
   */
  private static calculateSalaryBasedCost(planAssignment: any, tierDetails: any, employeeSalary?: number): number {
    if (!employeeSalary || employeeSalary <= 0) {
      console.warn('Warning: Salary-based calculation requires valid employee salary');
      return tierDetails.totalCost || 0;
    }

    // Check if plan assignment has salary-based rates
    if (planAssignment.salaryBasedRates && Array.isArray(planAssignment.salaryBasedRates)) {
      // Find appropriate salary band
      const salaryBand = planAssignment.salaryBasedRates.find((band: any) =>
        employeeSalary >= band.salaryMin && employeeSalary <= band.salaryMax
      );

      if (salaryBand) {
        const baseCost = tierDetails.totalCost || 0;

        // Support both types: multiplier (industry standard) and fixed (QHarmony legacy)
        if (salaryBand.type === 'multiplier') {
          // MULTIPLIER: Scale the base cost by factor
          // Example: baseCost (1000) * rate (1.2) = 1200
          return baseCost * (salaryBand.rate || 1.0);
        } else {
          // FIXED: Add fixed amount to base cost (default behavior)
          // Example: baseCost (1000) + rate (200) = 1200
          return baseCost + (salaryBand.rate || 0);
        }
      }
    }

    // Check if plan assignment has salary percentage
    if (planAssignment.salaryPercentage && typeof planAssignment.salaryPercentage === 'number') {
      const annualCost = employeeSalary * (planAssignment.salaryPercentage / 100);
      const monthlyCost = annualCost / 12;

      // 🎯 FIXED: Salary percentage should REPLACE tier cost, not add to it
      // This represents the total premium as a percentage of salary
      return monthlyCost;
    }

    // Fallback to tier cost
    return tierDetails.totalCost || 0;
  }

  /**
   * Apply employer and employee contribution policies to calculate final costs
   * 🎯 IMPROVED: Rate structure aware cost application with explicit adjustment tracking
   */
  private static applyContributionPolicies(
    totalCost: number,
    employerContribution: ContributionPolicy,
    employeeContribution: ContributionPolicy,
    tierDetails?: any, // Optional tier details with pre-calculated costs
    wasAdjustedByRateStructure?: boolean // 🎯 NEW: Explicit flag indicating if cost was adjusted
  ): { employeeAmount: number; employerAmount: number; totalAmount: number } {

    // 🎯 PRIORITY 1: Use tier-specific costs when available
    if (tierDetails &&
        typeof tierDetails.employeeCost === 'number' &&
        typeof tierDetails.employerCost === 'number' &&
        tierDetails.employeeCost >= 0 &&
        tierDetails.employerCost >= 0) {

      const originalTierTotal = tierDetails.employeeCost + tierDetails.employerCost;

      // 🎯 IMPROVED: Use explicit flag instead of cost difference detection
      if (!wasAdjustedByRateStructure) {
        // No rate structure adjustment - use tier costs directly
        // This handles Composite and Four-Tier rate structures
        return {
          employeeAmount: Math.round(tierDetails.employeeCost * 100) / 100,
          employerAmount: Math.round(tierDetails.employerCost * 100) / 100,
          totalAmount: Math.round(totalCost * 100) / 100
        };
      } else {
        // Rate structure adjusted the cost (Age-Banded, Salary-Based) - scale proportionally
        const scaleFactor = totalCost / originalTierTotal;

        console.log(`Rate structure adjustment detected: ${originalTierTotal} -> ${totalCost} (factor: ${scaleFactor.toFixed(3)}). Scaling tier costs proportionally.`);

        return {
          employeeAmount: Math.round(tierDetails.employeeCost * scaleFactor * 100) / 100,
          employerAmount: Math.round(tierDetails.employerCost * scaleFactor * 100) / 100,
          totalAmount: Math.round(totalCost * 100) / 100
        };
      }
    }

    // 🎯 FALLBACK: Use contribution policies when tier-specific costs are not available or invalid
    let employerAmount = 0;
    let employeeAmount = 0;

    // 🎯 PRIORITY 1: Handle "Remainder" type first (most common pattern)
    if (employeeContribution?.contributionType === 'Remainder') {
      // Calculate employer contribution first
      if (employerContribution?.contributionType === 'Fixed') {
        employerAmount = Math.min(employerContribution.contributionAmount, totalCost);
      } else if (employerContribution?.contributionType === 'Percentage') {
        employerAmount = Math.min(totalCost * (employerContribution.contributionAmount / 100), totalCost);
      }

      // Employee pays the remainder
      employeeAmount = Math.max(0, totalCost - employerAmount);
    }
    // 🎯 PRIORITY 2: Handle explicit employee contribution
    else if (employeeContribution?.contributionType === 'Fixed') {
      employeeAmount = Math.min(employeeContribution.contributionAmount, totalCost);

      // Calculate employer contribution
      if (employerContribution?.contributionType === 'Fixed') {
        employerAmount = Math.min(employerContribution.contributionAmount, totalCost - employeeAmount);
      } else if (employerContribution?.contributionType === 'Percentage') {
        employerAmount = Math.min(totalCost * (employerContribution.contributionAmount / 100), totalCost - employeeAmount);
      } else {
        // Employer pays the remainder
        employerAmount = Math.max(0, totalCost - employeeAmount);
      }
    }
    // 🎯 PRIORITY 3: Handle percentage-based employee contribution
    else if (employeeContribution?.contributionType === 'Percentage') {
      employeeAmount = Math.min(totalCost * (employeeContribution.contributionAmount / 100), totalCost);

      // Calculate employer contribution
      if (employerContribution?.contributionType === 'Fixed') {
        employerAmount = Math.min(employerContribution.contributionAmount, totalCost - employeeAmount);
      } else if (employerContribution?.contributionType === 'Percentage') {
        employerAmount = Math.min(totalCost * (employerContribution.contributionAmount / 100), totalCost - employeeAmount);
      } else {
        // Employer pays the remainder
        employerAmount = Math.max(0, totalCost - employeeAmount);
      }
    }
    // 🎯 FALLBACK: Only employer contribution specified
    else {
      if (employerContribution?.contributionType === 'Fixed') {
        employerAmount = Math.min(employerContribution.contributionAmount, totalCost);
      } else if (employerContribution?.contributionType === 'Percentage') {
        employerAmount = Math.min(totalCost * (employerContribution.contributionAmount / 100), totalCost);
      }

      // Employee pays the remainder
      employeeAmount = Math.max(0, totalCost - employerAmount);
    }

    // 🎯 VALIDATION: Ensure amounts add up correctly
    const calculatedTotal = employerAmount + employeeAmount;
    if (Math.abs(calculatedTotal - totalCost) > 0.01) { // Allow for rounding differences
      console.warn(`Warning: Contribution amounts (${calculatedTotal}) don't match total cost (${totalCost})`);

      // Adjust employee amount to make up the difference
      employeeAmount = Math.max(0, totalCost - employerAmount);
    }

    return {
      employeeAmount: Math.round(employeeAmount * 100) / 100, // Round to 2 decimal places
      employerAmount: Math.round(employerAmount * 100) / 100,
      totalAmount: Math.round(totalCost * 100) / 100
    };
  }

  /**
   * ✅ NEW: Calculate enhanced cost breakdown with payroll frequency
   */
  private static calculateEnhancedCostBreakdown(
    monthlyContribution: any,
    payrollFrequency?: string
  ): any {

    // ✅ FALLBACK: Default to Monthly if not provided
    const frequency = payrollFrequency || 'Monthly';

    const payrollFrequencyMap = {
      'Weekly': 52,
      'Biweekly': 26,
      'Semi-Monthly': 24,
      'Monthly': 12
    };

    const payPeriodsPerYear = payrollFrequencyMap[frequency] || 12; // ✅ Fallback to 12

    // Calculate annual amounts
    const annualEmployeeAmount = monthlyContribution.employeeAmount * 12;
    const annualEmployerAmount = monthlyContribution.employerAmount * 12;
    const annualTotalAmount = monthlyContribution.totalAmount * 12;

    // Calculate payroll deduction amounts
    const payrollEmployeeAmount = annualEmployeeAmount / payPeriodsPerYear;
    const payrollEmployerAmount = annualEmployerAmount / payPeriodsPerYear;
    const payrollTotalAmount = annualTotalAmount / payPeriodsPerYear;

    return {
      // Backward compatibility (existing fields)
      employeeAmount: monthlyContribution.employeeAmount,
      employerAmount: monthlyContribution.employerAmount,
      totalAmount: monthlyContribution.totalAmount,

      // Enhanced breakdown
      monthlyEmployeeAmount: monthlyContribution.employeeAmount,
      monthlyEmployerAmount: monthlyContribution.employerAmount,
      monthlyTotalAmount: monthlyContribution.totalAmount,

      annualEmployeeAmount: Math.round(annualEmployeeAmount * 100) / 100,
      annualEmployerAmount: Math.round(annualEmployerAmount * 100) / 100,
      annualTotalAmount: Math.round(annualTotalAmount * 100) / 100,

      payrollEmployeeAmount: Math.round(payrollEmployeeAmount * 100) / 100,
      payrollEmployerAmount: Math.round(payrollEmployerAmount * 100) / 100,
      payrollTotalAmount: Math.round(payrollTotalAmount * 100) / 100,

      // Metadata
      payrollFrequency: frequency,
      payPeriodsPerYear
    };
  }

  /**
   * Get plan assignment by ID (helper method to avoid circular dependency)
   */
  public static async getPlanAssignmentById(planAssignmentId: string): Promise<any> {
    try {
      const planAssignmentModel = mongoose.model('PlanAssignment');
      return await planAssignmentModel.findById(planAssignmentId);
    } catch (error) {
      console.error('Error fetching plan assignment:', error);
      return null;
    }
  }

  /**
   * Validate cost calculation inputs
   * 🎯 IMPROVED: Added validation for salary and rate structure compatibility
   */
  public static validateCostCalculationInputs(input: CostCalculationInput): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!input.planAssignment) {
      errors.push('Plan assignment is required');
    }

    if (!input.selectedTier) {
      errors.push('Selected coverage tier is required');
    }

    if (input.employeeAge !== undefined && (input.employeeAge < 0 || input.employeeAge > 120)) {
      errors.push('Employee age must be between 0 and 120');
    }

    // 🎯 REMOVED: dependentCount validation - not used in cost calculation

    if (input.employeeSalary !== undefined && input.employeeSalary < 0) {
      errors.push('Employee salary cannot be negative');
    }

    // 🎯 NEW: Validate rate structure requirements
    if (input.planAssignment) {
      const rateStructure = input.planAssignment.rateStructure;

      if ((rateStructure === 'Age-Banded' || rateStructure === 'Age-Banded-Four-Tier') && input.employeeAge === undefined) {
        errors.push('Employee age is required for age-banded rate structures');
      }

      if (rateStructure === 'Salary-Based' && input.employeeSalary === undefined) {
        errors.push('Employee salary is required for salary-based rate structures');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default CostCalculationService;
