"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/create-plan/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/create-plan/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/ai-enroller/create-plan/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/usePerformanceMonitor */ \"(app-pages-browser)/./src/app/ai-enroller/hooks/usePerformanceMonitor.ts\");\n/* harmony import */ var _components_CreatePlanOptimizer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/CreatePlanOptimizer */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/components/CreatePlanOptimizer.tsx\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineArrowLeft,HiOutlineCheckCircle,HiOutlineCloudUpload,HiOutlineDocumentText,HiOutlinePlus,HiOutlineX!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=RiCheckboxCircleLine,RiFileTextLine,RiFileUploadLine,RiHealthBookLine,RiShieldCheckLine,RiVideoLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _create_plan_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./create-plan.css */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/create-plan.css\");\n/* harmony import */ var _services_planApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./services/planApi */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/services/planApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst CreatePlanPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [createdPlan, setCreatedPlan] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // State for plan name duplicate checking\n    const [planNameStatus, setPlanNameStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isChecking: false,\n        isDuplicate: false\n    });\n    // State for plan code duplicate checking\n    const [planCodeStatus, setPlanCodeStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isChecking: false,\n        isDuplicate: false\n    });\n    // Monitor page performance\n    (0,_hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_7__.usePerformanceMonitor)(\"Create Plan Page\");\n    // Debug: Log state changes (removed for production)\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        // Step 1: Basic Info\n        planName: \"\",\n        planCode: \"\",\n        carrier: \"\",\n        planType: \"\",\n        coverageCategory: \"\",\n        coverageType: \"\",\n        metalTier: \"\",\n        // Step 2: Media & Documents\n        videoUrl: \"\",\n        documents: [],\n        // Step 3: Description & Highlights\n        description: \"\",\n        highlights: [\n            \"\"\n        ],\n        // Legacy fields (for compatibility)\n        effectiveDate: \"\",\n        endDate: \"\",\n        copay: \"\",\n        deductible: \"\"\n    });\n    const steps = [\n        {\n            number: 1,\n            title: \"Documents\",\n            subtitle: \"Upload files\",\n            active: currentStep === 1,\n            completed: currentStep > 1\n        },\n        {\n            number: 2,\n            title: \"Basic Info\",\n            subtitle: \"Plan details\",\n            active: currentStep === 2,\n            completed: currentStep > 2\n        },\n        {\n            number: 3,\n            title: \"Description\",\n            subtitle: \"Details & video\",\n            active: currentStep === 3,\n            completed: currentStep > 3\n        },\n        {\n            number: 4,\n            title: \"Preview\",\n            subtitle: \"Review & create\",\n            active: currentStep === 4,\n            completed: currentStep > 4\n        },\n        {\n            number: 5,\n            title: \"Success\",\n            subtitle: \"Plan created\",\n            active: currentStep === 5,\n            completed: false\n        }\n    ];\n    // Memoize constants data to avoid recalculation\n    const constantsData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>(0,_services_planApi__WEBPACK_IMPORTED_MODULE_10__.getConstantsData)(), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Clear any problematic draft data on component mount\n        localStorage.removeItem(\"ai-enroller-draft-plan\");\n        // Prefetch related routes\n        router.prefetch(\"/ai-enroller\");\n        router.prefetch(\"/ai-enroller/plans\");\n        // Load data from backend APIs and hardcoded constants\n        const loadData = async ()=>{\n            setIsLoading(true);\n            try {\n                var _constantsResult_data, _constantsResult_data1, _constantsResult_data2, _constantsResult_data3;\n                // Check cache first\n                const cachedData = sessionStorage.getItem(\"ai-enroller-create-plan-data\");\n                const cacheTimestamp = sessionStorage.getItem(\"ai-enroller-create-plan-cache-time\");\n                const cacheAge = cacheTimestamp ? Date.now() - parseInt(cacheTimestamp) : Infinity;\n                // Use cache if less than 5 minutes old\n                if (cachedData && cacheAge < 5 * 60 * 1000) {\n                    const parsedCache = JSON.parse(cachedData);\n                    setData(parsedCache);\n                    setIsLoading(false);\n                    // Load draft data only if form is empty\n                    const draftData = localStorage.getItem(\"ai-enroller-draft-plan\");\n                    if (draftData && !formData.planName && !formData.coverageCategory) {\n                        const parsedDraft = JSON.parse(draftData);\n                        console.log(\"Loading draft data from cache:\", parsedDraft);\n                        setFormData(parsedDraft);\n                    // Duplicate checking will be triggered automatically by useEffect\n                    }\n                    // Data loaded from cache\n                    return;\n                }\n                // Load fresh data\n                const constantsResult = constantsData;\n                // Load carriers in parallel with a timeout\n                const carriersPromise = Promise.race([\n                    (0,_services_planApi__WEBPACK_IMPORTED_MODULE_10__.getCarriers)(),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Carriers API timeout\")), 3000))\n                ]);\n                const carriersResult = await carriersPromise.catch((error)=>{\n                    console.warn(\"Carriers API failed or timed out:\", error);\n                    return {\n                        success: false,\n                        error: error.message\n                    };\n                });\n                const backendData = {\n                    plans: [],\n                    templates: [],\n                    carriers: carriersResult.success ? carriersResult.data || [] : [],\n                    planTypes: ((_constantsResult_data = constantsResult.data) === null || _constantsResult_data === void 0 ? void 0 : _constantsResult_data.planTypes) || [],\n                    coverageCategories: ((_constantsResult_data1 = constantsResult.data) === null || _constantsResult_data1 === void 0 ? void 0 : _constantsResult_data1.coverageCategories) || [],\n                    coverageMap: ((_constantsResult_data2 = constantsResult.data) === null || _constantsResult_data2 === void 0 ? void 0 : _constantsResult_data2.coverageMap) || {},\n                    metalTiers: ((_constantsResult_data3 = constantsResult.data) === null || _constantsResult_data3 === void 0 ? void 0 : _constantsResult_data3.metalTiers) || []\n                };\n                setData(backendData);\n                console.log(\"Data loaded successfully:\", backendData);\n                console.log(\"Coverage categories:\", backendData.coverageCategories);\n                console.log(\"Loaded carriers:\", backendData.carriers);\n                // Cache the data\n                sessionStorage.setItem(\"ai-enroller-create-plan-data\", JSON.stringify(backendData));\n                sessionStorage.setItem(\"ai-enroller-create-plan-cache-time\", Date.now().toString());\n                // Load draft data only if form is empty\n                const draftData = localStorage.getItem(\"ai-enroller-draft-plan\");\n                if (draftData && !formData.planName && !formData.coverageCategory) {\n                    const parsedDraft = JSON.parse(draftData);\n                    console.log(\"Loading draft data from fresh load:\", parsedDraft);\n                    setFormData(parsedDraft);\n                // Duplicate checking will be triggered automatically by useEffect\n                }\n            // Data loaded successfully\n            } catch (error) {\n                var _constantsData_data, _constantsData_data1, _constantsData_data2, _constantsData_data3;\n                console.error(\"Error loading data:\", error);\n                // Set fallback data structure\n                setData({\n                    plans: [],\n                    templates: [],\n                    carriers: [],\n                    planTypes: ((_constantsData_data = constantsData.data) === null || _constantsData_data === void 0 ? void 0 : _constantsData_data.planTypes) || [],\n                    coverageCategories: ((_constantsData_data1 = constantsData.data) === null || _constantsData_data1 === void 0 ? void 0 : _constantsData_data1.coverageCategories) || [],\n                    coverageMap: ((_constantsData_data2 = constantsData.data) === null || _constantsData_data2 === void 0 ? void 0 : _constantsData_data2.coverageMap) || {},\n                    metalTiers: ((_constantsData_data3 = constantsData.data) === null || _constantsData_data3 === void 0 ? void 0 : _constantsData_data3.metalTiers) || []\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        router,\n        constantsData\n    ]);\n    // Debounced auto-save to localStorage\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        let timeoutId;\n        return (data)=>{\n            clearTimeout(timeoutId);\n            timeoutId = setTimeout(()=>{\n                localStorage.setItem(\"ai-enroller-draft-plan\", JSON.stringify(data));\n            }, 500);\n        };\n    }, []);\n    // Debounced plan name duplicate check\n    const debouncedNameCheck = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        let timeoutId;\n        return (planName)=>{\n            clearTimeout(timeoutId);\n            if (!planName.trim()) {\n                setPlanNameStatus({\n                    isChecking: false,\n                    isDuplicate: false\n                });\n                return;\n            }\n            setPlanNameStatus((prev)=>({\n                    ...prev,\n                    isChecking: true,\n                    error: undefined\n                }));\n            timeoutId = setTimeout(async ()=>{\n                try {\n                    // No excludeId needed for create-plan page since it's always creating new plans\n                    const result = await (0,_services_planApi__WEBPACK_IMPORTED_MODULE_10__.checkPlanNameDuplicate)(planName);\n                    if (result.success && result.data) {\n                        setPlanNameStatus({\n                            isChecking: false,\n                            isDuplicate: result.data.isDuplicate,\n                            existingPlan: result.data.existingPlan\n                        });\n                    } else {\n                        setPlanNameStatus({\n                            isChecking: false,\n                            isDuplicate: false,\n                            error: result.error || \"Failed to check for duplicates\"\n                        });\n                    }\n                } catch (error) {\n                    setPlanNameStatus({\n                        isChecking: false,\n                        isDuplicate: false,\n                        error: \"Error checking for duplicates\"\n                    });\n                }\n            }, 800);\n        };\n    }, []);\n    // Debounced plan code duplicate check\n    const debouncedCodeCheck = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        let timeoutId;\n        return (planCode)=>{\n            clearTimeout(timeoutId);\n            if (!planCode.trim()) {\n                setPlanCodeStatus({\n                    isChecking: false,\n                    isDuplicate: false\n                });\n                return;\n            }\n            setPlanCodeStatus((prev)=>({\n                    ...prev,\n                    isChecking: true,\n                    error: undefined\n                }));\n            timeoutId = setTimeout(async ()=>{\n                try {\n                    // No excludeId needed for create-plan page since it's always creating new plans\n                    const result = await (0,_services_planApi__WEBPACK_IMPORTED_MODULE_10__.checkPlanCodeDuplicate)(planCode);\n                    if (result.success && result.data) {\n                        setPlanCodeStatus({\n                            isChecking: false,\n                            isDuplicate: result.data.isDuplicate,\n                            existingPlan: result.data.existingPlan\n                        });\n                    } else {\n                        setPlanCodeStatus({\n                            isChecking: false,\n                            isDuplicate: false,\n                            error: result.error || \"Failed to check for duplicates\"\n                        });\n                    }\n                } catch (error) {\n                    setPlanCodeStatus({\n                        isChecking: false,\n                        isDuplicate: false,\n                        error: \"Error checking for duplicates\"\n                    });\n                }\n            }, 800);\n        };\n    }, []);\n    // Check for duplicates when form data changes (for any source - AI Assist, localStorage, manual input)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (formData.planName && formData.planName.trim()) {\n            console.log(\"\\uD83D\\uDD04 Form data changed: Checking plan name for duplicates\");\n            debouncedNameCheck(formData.planName);\n        }\n    }, [\n        formData.planName,\n        debouncedNameCheck\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (formData.planCode && formData.planCode.trim()) {\n            console.log(\"\\uD83D\\uDD04 Form data changed: Checking plan code for duplicates\");\n            debouncedCodeCheck(formData.planCode);\n        }\n    }, [\n        formData.planCode,\n        debouncedCodeCheck\n    ]);\n    // JavaScript-based tooltip system\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let activeTooltip = null;\n        const showTooltip = (icon, text)=>{\n            // Remove any existing tooltip\n            hideTooltip();\n            const rect = icon.getBoundingClientRect();\n            const tooltipWidth = 280;\n            const tooltipHeight = 120; // Approximate\n            // Calculate horizontal position (centered on icon, but keep within viewport)\n            let leftPosition = rect.left + rect.width / 2 - tooltipWidth / 2;\n            if (leftPosition < 10) leftPosition = 10;\n            if (leftPosition + tooltipWidth > window.innerWidth - 10) {\n                leftPosition = window.innerWidth - tooltipWidth - 10;\n            }\n            // Determine if tooltip should show above or below\n            const spaceAbove = rect.top;\n            const spaceBelow = window.innerHeight - rect.bottom;\n            const showBelow = spaceAbove < tooltipHeight && spaceBelow > tooltipHeight;\n            // Create tooltip element\n            const tooltip = document.createElement(\"div\");\n            tooltip.className = \"custom-tooltip\";\n            tooltip.textContent = text;\n            tooltip.style.cssText = \"\\n        position: fixed;\\n        \".concat(showBelow ? \"top: \".concat(rect.bottom + 4, \"px;\") : \"bottom: \".concat(window.innerHeight - rect.top + 4, \"px;\"), \"\\n        left: \").concat(leftPosition, \"px;\\n        width: 280px;\\n        background: #1f2937;\\n        color: white;\\n        padding: 0.75rem 1rem;\\n        border-radius: 0.5rem;\\n        font-size: 0.8rem;\\n        font-weight: 400;\\n        line-height: 1.4;\\n        text-align: left;\\n        white-space: normal;\\n        word-wrap: break-word;\\n        hyphens: auto;\\n        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\\n        z-index: 99999;\\n        pointer-events: none;\\n        opacity: 0;\\n        transition: opacity 0.2s ease-in-out;\\n      \");\n            // Create arrow\n            const arrow = document.createElement(\"div\");\n            arrow.className = \"custom-tooltip-arrow\";\n            arrow.style.cssText = \"\\n        position: fixed;\\n        \".concat(showBelow ? \"top: \".concat(rect.bottom - 2, \"px;\") : \"bottom: \".concat(window.innerHeight - rect.top - 2, \"px;\"), \"\\n        left: \").concat(rect.left + rect.width / 2 - 6, \"px;\\n        width: 0;\\n        height: 0;\\n        border-left: 6px solid transparent;\\n        border-right: 6px solid transparent;\\n        \").concat(showBelow ? \"border-bottom: 6px solid #1f2937;\" : \"border-top: 6px solid #1f2937;\", \"\\n        z-index: 100000;\\n        pointer-events: none;\\n        opacity: 0;\\n        transition: opacity 0.2s ease-in-out;\\n      \");\n            document.body.appendChild(tooltip);\n            document.body.appendChild(arrow);\n            // Fade in\n            requestAnimationFrame(()=>{\n                tooltip.style.opacity = \"1\";\n                arrow.style.opacity = \"1\";\n            });\n            activeTooltip = tooltip;\n            activeTooltip.arrow = arrow;\n        };\n        const hideTooltip = ()=>{\n            if (activeTooltip) {\n                activeTooltip.remove();\n                if (activeTooltip.arrow) {\n                    activeTooltip.arrow.remove();\n                }\n                activeTooltip = null;\n            }\n        };\n        const handleMouseEnter = (e)=>{\n            const icon = e.target;\n            const tooltipText = icon.getAttribute(\"data-tooltip\");\n            if (tooltipText) {\n                showTooltip(icon, tooltipText);\n            }\n        };\n        const handleMouseLeave = ()=>{\n            hideTooltip();\n        };\n        // Add event listeners to all tooltip icons\n        const tooltipIcons = document.querySelectorAll(\".tooltip-icon[data-tooltip]\");\n        tooltipIcons.forEach((icon)=>{\n            icon.addEventListener(\"mouseenter\", handleMouseEnter);\n            icon.addEventListener(\"mouseleave\", handleMouseLeave);\n        });\n        // Cleanup function\n        return ()=>{\n            hideTooltip();\n            tooltipIcons.forEach((icon)=>{\n                icon.removeEventListener(\"mouseenter\", handleMouseEnter);\n                icon.removeEventListener(\"mouseleave\", handleMouseLeave);\n            });\n        };\n    }, [\n        currentStep\n    ]);\n    const handleInputChange = (field, value)=>{\n        console.log(\"handleInputChange called:\", {\n            field,\n            value,\n            currentFormData: formData\n        });\n        const updatedData = {\n            ...formData,\n            [field]: value\n        };\n        console.log(\"Updated form data:\", updatedData);\n        setFormData(updatedData);\n        // Log after a small delay to see if state updated\n        setTimeout(()=>{\n            console.log(\"Form data after setState (delayed):\", formData);\n        }, 100);\n        // Debounced auto-save\n        debouncedSave(updatedData);\n    // Duplicate checking will be triggered automatically by useEffect\n    };\n    const handleFileUpload = (files)=>{\n        if (files) {\n            const fileArray = Array.from(files);\n            setFormData((prev)=>({\n                    ...prev,\n                    documents: [\n                        ...prev.documents,\n                        ...fileArray\n                    ]\n                }));\n        }\n    };\n    const removeDocument = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                documents: prev.documents.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleContinue = ()=>{\n        if (currentStep < 5) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleCreatePlan = async ()=>{\n        try {\n            // Use the selected coverage category as the main coverage type\n            const coverageType = formData.coverageCategory;\n            // Create the plan using backend API\n            const planData = {\n                planName: formData.planName,\n                planCode: formData.planCode,\n                carrier: formData.carrier,\n                coverageType: coverageType,\n                coverageSubTypes: [\n                    formData.coverageType\n                ],\n                planType: formData.planType,\n                metalTier: formData.metalTier,\n                description: formData.description,\n                highlights: formData.highlights.filter((h)=>h.trim() !== \"\"),\n                informativeLinks: formData.videoUrl ? [\n                    formData.videoUrl\n                ] : [],\n                carrierId: formData.carrier,\n                isTemplate: false,\n                status: \"Active\" // Set status to Active by default for company assignment\n            };\n            console.log(\"Creating plan with data:\", planData);\n            console.log(\"Form data mapping:\");\n            console.log(\"- Coverage Category (formData.coverageCategory):\", formData.coverageCategory, \"→ coverageType\");\n            console.log(\"- Coverage Type (formData.coverageType):\", formData.coverageType, \"→ coverageSubTypes\");\n            console.log(\"- Carrier ID:\", formData.carrier);\n            console.log(\"- Plan Type:\", formData.planType);\n            console.log(\"- Available carriers:\", data === null || data === void 0 ? void 0 : data.carriers);\n            const result = await (0,_services_planApi__WEBPACK_IMPORTED_MODULE_10__.createPlan)(planData);\n            if (result.success && result.data) {\n                // Plan created successfully\n                const finalPlan = result.data.plan;\n                // Upload documents if any\n                if (formData.documents.length > 0) {\n                    const uploadResult = await (0,_services_planApi__WEBPACK_IMPORTED_MODULE_10__.uploadPlanDocuments)(finalPlan._id, formData.documents);\n                    if (!uploadResult.success) {\n                        console.warn(\"Failed to upload some documents:\", uploadResult.error);\n                    }\n                }\n                // Store the created plan for display\n                setCreatedPlan(finalPlan);\n                // Clear draft data\n                localStorage.removeItem(\"ai-enroller-draft-plan\");\n                // Move to success step\n                setCurrentStep(5);\n            } else {\n                throw new Error(result.error || \"Failed to create plan\");\n            }\n        } catch (error) {\n            // Error creating plan\n            alert(\"Error creating plan: \".concat(error instanceof Error ? error.message : \"Please try again.\"));\n        }\n    };\n    const addHighlight = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                highlights: [\n                    ...prev.highlights,\n                    \"\"\n                ]\n            }));\n    };\n    const updateHighlight = (index, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                highlights: prev.highlights.map((h, i)=>i === index ? value : h)\n            }));\n    };\n    const removeHighlight = (index)=>{\n        if (formData.highlights.length > 1) {\n            setFormData((prev)=>({\n                    ...prev,\n                    highlights: prev.highlights.filter((_, i)=>i !== index)\n                }));\n        }\n    };\n    // Memoized validation functions for better performance\n    const isStep1Valid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return true; // Documents are optional, so step 1 is always valid\n    }, []);\n    const isStep2Valid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return formData.planName && formData.planCode && formData.carrier && formData.planType && formData.coverageCategory && formData.coverageType && // metalTier is now optional\n        !planNameStatus.isDuplicate && !planNameStatus.isChecking && !planCodeStatus.isDuplicate && !planCodeStatus.isChecking;\n    }, [\n        formData.planName,\n        formData.planCode,\n        formData.carrier,\n        formData.planType,\n        formData.coverageCategory,\n        formData.coverageType,\n        planNameStatus.isDuplicate,\n        planNameStatus.isChecking,\n        planCodeStatus.isDuplicate,\n        planCodeStatus.isChecking\n    ]);\n    const isStep3Valid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return formData.description && formData.highlights.some((h)=>h.trim() !== \"\");\n    }, [\n        formData.description,\n        formData.highlights\n    ]);\n    // Loading component\n    const LoadingSpinner = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"400px\",\n                flexDirection: \"column\",\n                gap: \"16px\"\n            },\n            className: \"jsx-ff161281ed666c63\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        width: \"40px\",\n                        height: \"40px\",\n                        border: \"3px solid #f3f4f6\",\n                        borderTop: \"3px solid #3b82f6\",\n                        borderRadius: \"50%\",\n                        animation: \"spin 1s linear infinite\"\n                    },\n                    className: \"jsx-ff161281ed666c63\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: \"#6b7280\",\n                        fontSize: \"14px\",\n                        lineHeight: \"1.6\",\n                        fontFamily: \"sans-serif\"\n                    },\n                    className: \"jsx-ff161281ed666c63\",\n                    children: \"Loading plan data...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"ff161281ed666c63\",\n                    children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n                }, void 0, false, void 0, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 651,\n            columnNumber: 5\n        }, undefined);\n    const renderStep1 = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiFileUploadLine, {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Plan Documents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-content\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                children: [\n                                    \"Plan Documents (Optional)\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"tooltip-icon\",\n                                        \"data-tooltip\": \"Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"file-upload-area\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        id: \"documents\",\n                                        multiple: true,\n                                        accept: \".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png\",\n                                        onChange: (e)=>handleFileUpload(e.target.files),\n                                        className: \"file-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"documents\",\n                                        className: \"file-upload-label\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineCloudUpload, {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Click to upload documents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                children: \"PDF, DOC, TXT, or Image files\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 11\n                            }, undefined),\n                            formData.documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"uploaded-files\",\n                                children: formData.documents.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"uploaded-file\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineDocumentText, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"file-name\",\n                                                children: file.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"file-size\",\n                                                children: [\n                                                    \"(\",\n                                                    (file.size / 1024).toFixed(1),\n                                                    \" KB)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"remove-file\",\n                                                onClick: ()=>removeDocument(index),\n                                                title: \"Remove this document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineX, {\n                                                    size: 14\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 686,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-navigation\",\n                    style: {\n                        justifyContent: \"flex-end\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"nav-btn primary enabled\",\n                        onClick: handleContinue,\n                        children: \"Continue to Basic Info\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 678,\n            columnNumber: 5\n        }, undefined);\n    const renderStep2 = ()=>{\n        var _data_coverageMap_formData_coverageCategory, _data_coverageMap, _data_carriers, _data_planTypes, _data_metalTiers;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiHealthBookLine, {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 747,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Basic Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 746,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 745,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"coverageCategory\",\n                                    children: [\n                                        \"Coverage Category\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"coverageCategory\",\n                                    value: formData.coverageCategory,\n                                    onChange: (e)=>{\n                                        console.log(\"Coverage category dropdown changed:\", e.target.value);\n                                        console.log(\"Current formData.coverageCategory before change:\", formData.coverageCategory);\n                                        // Update both coverageCategory and reset coverageType in a single state update\n                                        const updatedData = {\n                                            ...formData,\n                                            coverageCategory: e.target.value,\n                                            coverageType: \"\" // Reset coverage type when category changes\n                                        };\n                                        console.log(\"Updated form data (combined):\", updatedData);\n                                        setFormData(updatedData);\n                                        // Debounced auto-save\n                                        debouncedSave(updatedData);\n                                    },\n                                    title: \"Choose the main category of benefits\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select coverage category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        ((data === null || data === void 0 ? void 0 : data.coverageCategories) || []).map((category)=>{\n                                            console.log(\"Rendering category option:\", category);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category,\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 754,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"coverageType\",\n                                    children: [\n                                        \"Coverage Type\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"coverageType\",\n                                    value: formData.coverageType,\n                                    onChange: (e)=>handleInputChange(\"coverageType\", e.target.value),\n                                    disabled: !formData.coverageCategory,\n                                    title: \"Choose the specific type of benefits covered\",\n                                    style: {\n                                        backgroundColor: !formData.coverageCategory ? \"#f9fafb\" : \"white\",\n                                        cursor: !formData.coverageCategory ? \"not-allowed\" : \"pointer\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select coverage type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 815,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        formData.coverageCategory && (data === null || data === void 0 ? void 0 : (_data_coverageMap = data.coverageMap) === null || _data_coverageMap === void 0 ? void 0 : (_data_coverageMap_formData_coverageCategory = _data_coverageMap[formData.coverageCategory]) === null || _data_coverageMap_formData_coverageCategory === void 0 ? void 0 : _data_coverageMap_formData_coverageCategory.map((subType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: subType,\n                                                children: subType\n                                            }, subType, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 15\n                                            }, undefined))) || []\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 804,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"carrier\",\n                                    children: [\n                                        \"Carrier\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"carrier\",\n                                    value: formData.carrier,\n                                    onChange: (e)=>handleInputChange(\"carrier\", e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select carrier\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        (data === null || data === void 0 ? void 0 : (_data_carriers = data.carriers) === null || _data_carriers === void 0 ? void 0 : _data_carriers.map((carrier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: carrier._id,\n                                                children: carrier.displayName || carrier.carrierName\n                                            }, carrier._id, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 15\n                                            }, undefined))) || []\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"planName\",\n                                    children: [\n                                        \"Plan Name\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 849,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\",\n                                        width: \"100%\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"planName\",\n                                            placeholder: \"e.g. Blue Shield PPO 500\",\n                                            value: formData.planName,\n                                            onChange: (e)=>handleInputChange(\"planName\", e.target.value),\n                                            style: {\n                                                width: \"100%\",\n                                                borderColor: planNameStatus.isDuplicate ? \"#ef4444\" : planNameStatus.isChecking ? \"#f59e0b\" : formData.planName && !planNameStatus.isDuplicate ? \"#10b981\" : \"#d1d5db\",\n                                                paddingRight: \"40px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                right: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                fontSize: \"14px\",\n                                                fontFamily: \"sans-serif\"\n                                            },\n                                            children: [\n                                                planNameStatus.isChecking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        border: \"2px solid #f59e0b\",\n                                                        borderTop: \"2px solid transparent\",\n                                                        borderRadius: \"50%\",\n                                                        animation: \"spin 1s linear infinite\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                !planNameStatus.isChecking && formData.planName && !planNameStatus.isDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#10b981\",\n                                                        fontWeight: \"600\",\n                                                        fontFamily: \"sans-serif\"\n                                                    },\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                planNameStatus.isDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#ef4444\",\n                                                        fontWeight: \"600\",\n                                                        fontFamily: \"sans-serif\"\n                                                    },\n                                                    children: \"✗\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 11\n                                }, undefined),\n                                planNameStatus.isDuplicate && planNameStatus.existingPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#fef2f2\",\n                                        border: \"1px solid #fecaca\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#dc2626\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Plan name already exists:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        ' \"',\n                                        planNameStatus.existingPlan.planName,\n                                        '\"',\n                                        planNameStatus.existingPlan.planCode && \" (\".concat(planNameStatus.existingPlan.planCode, \")\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: \"#7f1d1d\",\n                                                fontFamily: \"sans-serif\"\n                                            },\n                                            children: \"Please choose a different name.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 13\n                                }, undefined),\n                                planNameStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#fef3cd\",\n                                        border: \"1px solid #fde68a\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#92400e\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Warning:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 935,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        planNameStatus.error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 13\n                                }, undefined),\n                                formData.planName && !planNameStatus.isChecking && !planNameStatus.isDuplicate && !planNameStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#f0fdf4\",\n                                        border: \"1px solid #bbf7d0\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#166534\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: \"✓ Plan name is available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 848,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"planCode\",\n                                    children: [\n                                        \"Plan Code\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 960,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 958,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\",\n                                        width: \"100%\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"planCode\",\n                                            placeholder: \"e.g. BS-PPO-500\",\n                                            value: formData.planCode,\n                                            onChange: (e)=>handleInputChange(\"planCode\", e.target.value),\n                                            style: {\n                                                width: \"100%\",\n                                                borderColor: planCodeStatus.isDuplicate ? \"#ef4444\" : planCodeStatus.isChecking ? \"#f59e0b\" : formData.planCode && !planCodeStatus.isDuplicate ? \"#10b981\" : \"#d1d5db\",\n                                                paddingRight: \"40px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                right: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                fontSize: \"14px\",\n                                                fontFamily: \"sans-serif\"\n                                            },\n                                            children: [\n                                                planCodeStatus.isChecking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        border: \"2px solid #f59e0b\",\n                                                        borderTop: \"2px solid transparent\",\n                                                        borderRadius: \"50%\",\n                                                        animation: \"spin 1s linear infinite\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                !planCodeStatus.isChecking && formData.planCode && !planCodeStatus.isDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#10b981\",\n                                                        fontWeight: \"600\",\n                                                        fontFamily: \"sans-serif\"\n                                                    },\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                planCodeStatus.isDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#ef4444\",\n                                                        fontWeight: \"600\",\n                                                        fontFamily: \"sans-serif\"\n                                                    },\n                                                    children: \"✗\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 981,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 11\n                                }, undefined),\n                                planCodeStatus.isDuplicate && planCodeStatus.existingPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#fef2f2\",\n                                        border: \"1px solid #fecaca\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#dc2626\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Plan code already exists:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        ' \"',\n                                        planCodeStatus.existingPlan.planCode,\n                                        '\"',\n                                        planCodeStatus.existingPlan.planName && \" (\".concat(planCodeStatus.existingPlan.planName, \")\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: \"#7f1d1d\",\n                                                fontFamily: \"sans-serif\"\n                                            },\n                                            children: \"Please choose a different code.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1012,\n                                    columnNumber: 13\n                                }, undefined),\n                                planCodeStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#fef3cd\",\n                                        border: \"1px solid #fde68a\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#92400e\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Warning:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        planCodeStatus.error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 13\n                                }, undefined),\n                                formData.planCode && !planCodeStatus.isChecking && !planCodeStatus.isDuplicate && !planCodeStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"8px\",\n                                        padding: \"8px 12px\",\n                                        backgroundColor: \"#f0fdf4\",\n                                        border: \"1px solid #bbf7d0\",\n                                        borderRadius: \"6px\",\n                                        fontSize: \"13px\",\n                                        color: \"#166534\",\n                                        lineHeight: \"1.4\",\n                                        fontFamily: \"sans-serif\"\n                                    },\n                                    children: \"✓ Plan code is available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1049,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 957,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"planType\",\n                                    children: [\n                                        \"Plan Type\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"planType\",\n                                    value: formData.planType,\n                                    onChange: (e)=>handleInputChange(\"planType\", e.target.value),\n                                    title: \"Select the plan structure (PPO, HMO, etc.)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        (data === null || data === void 0 ? void 0 : (_data_planTypes = data.planTypes) === null || _data_planTypes === void 0 ? void 0 : _data_planTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: type,\n                                                children: type\n                                            }, type, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 15\n                                            }, undefined))) || []\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"metalTier\",\n                                    children: [\n                                        \"Metal Tier (Optional)\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"metalTier\",\n                                    value: formData.metalTier,\n                                    onChange: (e)=>handleInputChange(\"metalTier\", e.target.value),\n                                    title: \"Choose the coverage level (Bronze, Silver, Gold, Platinum) - Optional\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select tier (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1104,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        (data === null || data === void 0 ? void 0 : (_data_metalTiers = data.metalTiers) === null || _data_metalTiers === void 0 ? void 0 : _data_metalTiers.map((tier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: tier,\n                                                children: tier\n                                            }, tier, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1106,\n                                                columnNumber: 15\n                                            }, undefined))) || []\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1098,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1090,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 752,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-navigation\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/ai-enroller\",\n                            prefetch: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"nav-btn secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineArrowLeft, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Back to Main\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1116,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1115,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn primary \".concat(isStep2Valid ? \"enabled\" : \"disabled\"),\n                            onClick: handleContinue,\n                            disabled: !isStep2Valid,\n                            title: \"Continue to description and video\",\n                            children: \"Continue to Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1122,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1114,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 744,\n            columnNumber: 5\n        }, undefined);\n    };\n    const renderStep3 = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiVideoLine, {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Description & Video\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1139,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1137,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1136,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"videoUrl\",\n                                    children: [\n                                        \"Video URL (Optional)\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Add a YouTube or Vimeo URL to help explain plan benefits and features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1147,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1145,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    id: \"videoUrl\",\n                                    placeholder: \"e.g. https://youtube.com/watch?v=...\",\n                                    value: formData.videoUrl,\n                                    onChange: (e)=>handleInputChange(\"videoUrl\", e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"field-hint\",\n                                    children: \"Add a video to help explain plan benefits and features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1144,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"description\",\n                                    children: [\n                                        \"Plan Description\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Provide a detailed description of the plan benefits, coverage, and key features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1165,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    placeholder: \"Describe the plan benefits and features...\",\n                                    value: formData.description,\n                                    onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                    rows: 4\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1170,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"field-hint\",\n                                    children: \"Describe the key benefits, coverage details, and what makes this plan unique\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1177,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1162,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    children: [\n                                        \"Plan Highlights\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"tooltip-icon\",\n                                            \"data-tooltip\": \"Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1181,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"field-hint\",\n                                    children: \"Add the most important features that make this plan attractive\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 11\n                                }, undefined),\n                                formData.highlights.map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"highlight-input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"e.g. Low deductible, Nationwide network\",\n                                                value: highlight,\n                                                onChange: (e)=>updateHighlight(index, e.target.value),\n                                                title: \"Enter a key benefit or feature\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            formData.highlights.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"remove-highlight\",\n                                                onClick: ()=>removeHighlight(index),\n                                                title: \"Remove this highlight\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineX, {\n                                                    size: 14\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1190,\n                                        columnNumber: 13\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"add-highlight\",\n                                    onClick: addHighlight,\n                                    title: \"Add another highlight\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlinePlus, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        \"Add Highlight\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1210,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1180,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1143,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-navigation\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn secondary\",\n                            onClick: handleBack,\n                            title: \"Go back to basic information\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineArrowLeft, {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1224,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Back\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1223,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn primary \".concat(isStep3Valid ? \"enabled\" : \"disabled\"),\n                            onClick: handleContinue,\n                            disabled: !isStep3Valid,\n                            title: \"Continue to preview your plan\",\n                            children: \"Preview Plan\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1228,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1222,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1135,\n            columnNumber: 5\n        }, undefined);\n    const renderStep4 = ()=>{\n        var _data_carriers_find, _data_carriers, _data_carriers_find1, _data_carriers1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-header-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiCheckboxCircleLine, {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"AI-Powered Plan Preview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1243,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ready-badge\",\n                            title: \"All required information has been provided\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineCheckCircle, {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1248,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Ready to Create\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1247,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1242,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"review-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"review-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-section-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiHealthBookLine, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1256,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Plan Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1257,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1255,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-items\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"The name of this plan\",\n                                                    children: \"Plan Name:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1261,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value\",\n                                                    children: formData.planName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1262,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1260,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Unique identifier for this plan\",\n                                                    children: \"Plan Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1265,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value plan-code\",\n                                                    children: formData.planCode\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1266,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Insurance carrier providing this plan\",\n                                                    children: \"Carrier:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value\",\n                                                    children: (data === null || data === void 0 ? void 0 : (_data_carriers = data.carriers) === null || _data_carriers === void 0 ? void 0 : (_data_carriers_find = _data_carriers.find((c)=>c._id === formData.carrier)) === null || _data_carriers_find === void 0 ? void 0 : _data_carriers_find.displayName) || (data === null || data === void 0 ? void 0 : (_data_carriers1 = data.carriers) === null || _data_carriers1 === void 0 ? void 0 : (_data_carriers_find1 = _data_carriers1.find((c)=>c._id === formData.carrier)) === null || _data_carriers_find1 === void 0 ? void 0 : _data_carriers_find1.carrierName) || \"Unknown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1270,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1268,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Type of health plan structure\",\n                                                    children: \"Plan Type:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1276,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value\",\n                                                    children: formData.planType\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1277,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1275,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Type of coverage provided\",\n                                                    children: \"Coverage Type:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value\",\n                                                    children: formData.coverageType\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1279,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Metal tier level indicating coverage level\",\n                                                    children: \"Metal Tier:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1284,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-value metal-tier\",\n                                                    children: formData.metalTier\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1285,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1283,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1254,\n                            columnNumber: 9\n                        }, undefined),\n                        (formData.videoUrl || formData.documents.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"review-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-section-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineDocumentText, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1293,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Media & Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1294,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1292,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-items\",\n                                    children: [\n                                        formData.videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item full-width\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Video URL for plan explanation\",\n                                                    children: \"Video URL:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1299,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: formData.videoUrl,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"review-link\",\n                                                    children: formData.videoUrl\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1298,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        formData.documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item full-width\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Documents uploaded for this plan\",\n                                                    children: \"Documents:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1307,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"review-documents\",\n                                                    children: formData.documents.map((doc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"review-document\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineDocumentText, {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                                    lineNumber: 1311,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: doc.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    children: [\n                                                                        \"(\",\n                                                                        (doc.size / 1024).toFixed(1),\n                                                                        \" KB)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                                    lineNumber: 1313,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                            lineNumber: 1310,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1308,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1306,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1296,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1291,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"review-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-section-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiFileTextLine, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1325,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Description & Highlights\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1326,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1324,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"review-items\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item full-width\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Detailed plan description\",\n                                                    children: \"Description:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1330,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"review-description\",\n                                                    children: formData.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1331,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1329,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"review-item full-width\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"review-label\",\n                                                    title: \"Key plan features and benefits\",\n                                                    children: \"Highlights:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1334,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"review-highlights\",\n                                                    children: formData.highlights.filter((h)=>h.trim()).map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: highlight\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                            lineNumber: 1337,\n                                                            columnNumber: 19\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                    lineNumber: 1335,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1333,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1328,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1323,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"create-confirmation\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"confirmation-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiCheckboxCircleLine, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1346,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1345,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"confirmation-text\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Ready to Create Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1349,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                'Your new plan \"',\n                                                formData.planName,\n                                                '\" will be added to your catalog and available for assignment to employer groups.'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1350,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1348,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1344,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1253,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"form-navigation\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn secondary\",\n                            onClick: handleBack,\n                            title: \"Go back to description and video\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineArrowLeft, {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1357,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Back\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1356,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"nav-btn primary enabled\",\n                            onClick: handleCreatePlan,\n                            title: \"Create this plan and add it to your catalog\",\n                            children: \"Create Plan\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1361,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1355,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1241,\n            columnNumber: 5\n        }, undefined);\n    };\n    const renderStep5 = ()=>{\n        var _data_carriers_find, _data_carriers_find1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"form-section success-section\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"success-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"success-icon\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiShieldCheckLine, {\n                            size: 32\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1376,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1375,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Plan Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1378,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Your plan '\",\n                            formData.planName,\n                            \"' is now available in your catalog.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1379,\n                        columnNumber: 9\n                    }, undefined),\n                    createdPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"plan-details-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"plan-details-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiHealthBookLine, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1385,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Plan Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1386,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1384,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"plan-details-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Plan ID:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1390,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value plan-id\",\n                                                children: createdPlan._id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1391,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1389,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Plan Code:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1394,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value plan-code\",\n                                                children: createdPlan.planCode\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1395,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value status-active\",\n                                                children: createdPlan.status || \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1399,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Created:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value\",\n                                                children: createdPlan.createdAt ? new Date(createdPlan.createdAt).toLocaleString() : \"Just now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1403,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1401,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"plan-detail-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-label\",\n                                                children: \"Carrier:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1406,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"detail-value\",\n                                                children: (data === null || data === void 0 ? void 0 : (_data_carriers_find = data.carriers.find((c)=>c._id === (createdPlan.carrierId || createdPlan.carrier))) === null || _data_carriers_find === void 0 ? void 0 : _data_carriers_find.displayName) || (data === null || data === void 0 ? void 0 : (_data_carriers_find1 = data.carriers.find((c)=>c._id === (createdPlan.carrierId || createdPlan.carrier))) === null || _data_carriers_find1 === void 0 ? void 0 : _data_carriers_find1.carrierName) || \"Unknown\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1407,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1405,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1388,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1383,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"success-actions\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/ai-enroller/manage-groups\",\n                                prefetch: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"nav-btn primary\",\n                                    children: \"Assign to Group Now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1418,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1417,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/ai-enroller\",\n                                prefetch: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"nav-btn secondary\",\n                                    children: \"Back to Main Menu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1424,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1423,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                        lineNumber: 1416,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                lineNumber: 1374,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1373,\n            columnNumber: 5\n        }, undefined);\n    };\n    const renderStepContent = ()=>{\n        switch(currentStep){\n            case 1:\n                return renderStep1();\n            case 2:\n                return renderStep2();\n            case 3:\n                return renderStep3();\n            case 4:\n                return renderStep4();\n            case 5:\n                return renderStep5();\n            default:\n                return renderStep1();\n        }\n    };\n    // Show loading state while data is being fetched\n    if (isLoading || !data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"create-plan-wrapper\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"create-plan-page\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1455,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                lineNumber: 1454,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1453,\n            columnNumber: 7\n        }, undefined);\n    }\n    const getStepMessage = ()=>{\n        switch(currentStep){\n            case 1:\n                return {\n                    title: \"Hi! I'm Brea, your AI Benefits Assistant. Let's create an amazing plan together! \\uD83D\\uDE0A\",\n                    subtitle: \"I'll help you set up the basic plan information including name, carrier, and coverage details. This should only take a few minutes!\"\n                };\n            case 2:\n                return {\n                    title: \"Great progress! Now let's add some media to make your plan shine ✨\",\n                    subtitle: \"You can upload videos, brochures, or any documents that help explain your plan. Don't worry, this step is completely optional!\"\n                };\n            case 3:\n                return {\n                    title: \"Perfect! Now tell me what makes this plan special \\uD83C\\uDF1F\",\n                    subtitle: \"Help me understand the key benefits and highlights. I'll use this to create compelling descriptions that really sell your plan!\"\n                };\n            case 4:\n                return {\n                    title: \"Almost there! Let's review everything before we launch your plan \\uD83D\\uDE80\",\n                    subtitle: \"Take a moment to review all the details. Once you're happy with everything, I'll create your plan and make it available immediately!\"\n                };\n            case 5:\n                return {\n                    title: \"Congratulations! Your plan is now live and ready to go! \\uD83C\\uDF89\",\n                    subtitle: \"I've successfully created your plan and it's now available for assignment to employer groups. Great work!\"\n                };\n            default:\n                return {\n                    title: \"Hi there! Ready to create something amazing? \\uD83D\\uDCAB\",\n                    subtitle: \"I'm here to help you build the perfect benefits plan. Let's get started!\"\n                };\n        }\n    };\n    const getPageIcon = (stepNumber)=>{\n        switch(stepNumber){\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiHealthBookLine, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1498,\n                    columnNumber: 22\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineArrowLeft_HiOutlineCheckCircle_HiOutlineCloudUpload_HiOutlineDocumentText_HiOutlinePlus_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiOutlineDocumentText, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1499,\n                    columnNumber: 22\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiFileTextLine, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1500,\n                    columnNumber: 22\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiCheckboxCircleLine, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1501,\n                    columnNumber: 22\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCheckboxCircleLine_RiFileTextLine_RiFileUploadLine_RiHealthBookLine_RiShieldCheckLine_RiVideoLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_11__.RiHealthBookLine, {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1502,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"create-plan-wrapper\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEnrollerHeader, {\n                    title: \"Create Plan\",\n                    showBackButton: true,\n                    backUrl: \"/ai-enroller/plans\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1509,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CreatePlanOptimizer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1514,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"create-plan-page\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"progress-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"progress-title\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"page-title\",\n                                            children: \"Plan Creation Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1519,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"subtitle-text\",\n                                            children: [\n                                                Math.min(currentStep, 4),\n                                                \" of 4\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1520,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1518,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"progress-bar-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"progress-bar-fill\",\n                                        style: {\n                                            width: \"\".concat(Math.min(currentStep, 4) / 4 * 100, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1523,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1522,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1517,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-navigation\",\n                            children: steps.slice(0, 4).map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"page-nav-item \".concat(step.active ? \"active\" : \"\", \" \").concat(step.completed ? \"completed\" : \"\"),\n                                    onClick: ()=>setCurrentStep(step.number),\n                                    disabled: step.number > currentStep,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"nav-icon\",\n                                            children: getPageIcon(step.number)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1539,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        step.title\n                                    ]\n                                }, step.number, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                    lineNumber: 1533,\n                                    columnNumber: 13\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1531,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ai-assistant-message\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ai-message-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ai-avatar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"avatar-circle\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: \"/brea.png\",\n                                                alt: \"Brea - AI Assistant\",\n                                                className: \"brea-avatar\",\n                                                width: 48,\n                                                height: 48,\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1550,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                            lineNumber: 1549,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1548,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"chat-bubble\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"chat-message\",\n                                                children: getStepMessage().title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1561,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"chat-subtitle\",\n                                                children: getStepMessage().subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                                lineNumber: 1564,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                        lineNumber: 1560,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1547,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1546,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"main-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-container\",\n                                children: renderStepContent()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                                lineNumber: 1573,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                            lineNumber: 1572,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n                    lineNumber: 1515,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n            lineNumber: 1508,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\create-plan\\\\page.tsx\",\n        lineNumber: 1507,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreatePlanPage, \"1EZ8mez6EkQqFLBApQVXXOj4RAk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_7__.usePerformanceMonitor\n    ];\n});\n_c = CreatePlanPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreatePlanPage);\nvar _c;\n$RefreshReg$(_c, \"CreatePlanPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/create-plan/page.tsx\n"));

/***/ })

});