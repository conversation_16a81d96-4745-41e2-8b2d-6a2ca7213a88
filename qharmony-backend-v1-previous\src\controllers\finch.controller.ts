import express from 'express';

import Controller from '../interfaces/controller.interface';
import CommonService from '../services/common.service';
import EnvService from '../services/env.service';
import FinchService from '../services/finch.service';
import { createFailureResponse } from '../interfaces/response';
import logger from '../utils/logger';
import axios from 'axios';

class FinchController implements Controller {
  public router = express.Router();
  public commonService = new CommonService();
  public finchService = new FinchService();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.get(`/finch/handle-auth-redirect`, this.handleAuthRedirect);
  }

  private handleAuthRedirect = async (
    request: express.Request,
    response: express.Response
  ) => {
    // Extract the code and state from the request query. The state is the slack team id.
    const code = request.query.code as string;
    const state = request.query.state as string;

    // Get the access token
    try {
      logger.info(`Finch code: ${code} state: ${state}`);
      const resp = await axios.post('https://api.tryfinch.com/auth/token', {
        client_id: this.finchService.finchClient.clientId,
        client_secret: this.finchService.finchClient.clientSecret,
        code,
        redirect_uri: EnvService.env().FINCH_REDIRECT_URI,
      });
      response.send(
        'Successfully connected to Finch! Please check the home tab of the qHarmony Bot at Slack'
      );
      // Save the access token in the database.
      await this.finchService.saveAccessToken({
        accessToken: resp.data.access_token,
        state,
      });
    } catch (e) {
      logger.error(e);
      response.send(createFailureResponse(400, e));
    }
  };
}

export default FinchController;
