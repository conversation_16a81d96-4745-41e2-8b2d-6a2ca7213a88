# 🎯 Comprehensive Enrollment APIs Test Suite

## Overview

This test suite validates the 4 refactored employee enrollment APIs with complete data creation and automatic cleanup. The tests run against a test database on port 8080 and ensure all created data is properly cleaned up after testing.

## 🔧 Test Configuration

- **Server URL:** `http://localhost:8080`
- **Database:** Test database (automatically cleaned up)
- **APIs Tested:**
  - `GET /api/pre-enrollment/employee-enrollments/enrollment-periods/:planAssignmentId`
  - `POST /api/pre-enrollment/employee-enrollments/estimate-plan-costs`
  - `GET /api/pre-enrollment/employee-enrollments/expired`
  - `POST /api/pre-enrollment/employee-enrollments/check-expired`

## 📁 Test Files

### 1. `comprehensive-test-with-cleanup.js` ⭐ **RECOMMENDED**
- **Purpose:** Complete test with data creation and cleanup
- **Features:**
  - ✅ Creates all necessary test data in the database
  - ✅ Runs comprehensive API tests
  - ✅ Automatically cleans up all created data
  - ✅ Works with test database on port 8080
  - ✅ No manual data setup required

### 2. `comprehensive-enrollment-test-with-dummy-data.js`
- **Purpose:** Tests with predefined dummy data structure
- **Features:**
  - ✅ Comprehensive test scenarios
  - ✅ Complete dummy data structure
  - ❌ Requires manual data setup
  - ❌ No automatic cleanup

### 3. `run-comprehensive-tests.js`
- **Purpose:** Test runner with server health checks
- **Features:**
  - ✅ Server health validation
  - ✅ Setup instructions
  - ✅ Test execution guidance

## 🚀 How to Run Tests

### Prerequisites
1. **Build the project:**
   ```bash
   npm run build
   ```

2. **Start the server on port 8080 (test database):**
   ```bash
   npm run dev
   # OR
   npm start
   ```

3. **Wait for server to start** - Look for:
   - "Server running on port 8080"
   - "Connected to MongoDB (test database)"
   - "Database connection established"

### Running the Tests

#### Option 1: Comprehensive Test with Cleanup (Recommended)
```bash
node test-scripts/comprehensive-test-with-cleanup.js
```

**What it does:**
1. 🏗️ Creates test companies, users, carriers, plans, plan assignments
2. 🧪 Runs comprehensive API tests
3. 📊 Generates detailed test results
4. 🧹 Cleans up ALL created data automatically

#### Option 2: Test Runner with Health Check
```bash
node test-scripts/run-comprehensive-tests.js
```

**What it does:**
1. ✅ Checks if server is running
2. 📋 Provides setup instructions if needed
3. 🚀 Runs comprehensive tests if server is ready

#### Option 3: Original Dummy Data Tests
```bash
node test-scripts/comprehensive-enrollment-test-with-dummy-data.js
```

**Note:** Requires manual data setup and no automatic cleanup.

## 📊 Test Coverage

### Data Creation
- **2 Companies:** Test Brokerage Alpha, Test TechCorp Inc
- **4 Users:** SuperAdmin, Broker, Company Admin, Employee
- **1 Carrier:** Test Blue Shield Health
- **1 Plan:** Test Blue Shield PPO Gold
- **1 Plan Assignment:** Active assignment for TechCorp

### API Test Scenarios

#### 1. Get Enrollment Periods (4 tests)
- ✅ SuperAdmin access to valid plan assignment
- ✅ Invalid plan assignment ID handling
- ✅ Response structure validation
- ✅ Error status code validation

#### 2. Estimate Plan Costs (4 tests)
- ✅ SuperAdmin access with custom scenarios
- ✅ Missing planAssignmentId validation
- ✅ Response structure validation
- ✅ Cost estimation logic validation

#### 3. Get Expired Enrollments (4 tests)
- ✅ SuperAdmin access in user mode
- ✅ Invalid mode parameter handling
- ✅ Response structure validation
- ✅ Mode-specific response validation

#### 4. Check Expired Enrollments (4 tests)
- ✅ SuperAdmin access (should succeed)
- ✅ Non-SuperAdmin access (should fail with 403)
- ✅ Response structure validation
- ✅ Access control validation

### Total: 16+ comprehensive test scenarios

## 🧹 Automatic Cleanup

The test suite automatically cleans up all created data in reverse order:
1. 🗑️ Enrollments
2. 🗑️ Plan Assignments
3. 🗑️ Plans
4. 🗑️ Carriers
5. 🗑️ Users
6. 🗑️ Companies

**Cleanup is guaranteed** even if tests fail, ensuring the test database remains clean.

## 📈 Expected Results

### ✅ Success Scenario
```
🎯 COMPREHENSIVE ENROLLMENT APIS TEST WITH DATA CREATION & CLEANUP
📊 Total Tests: 16
✅ Passed: 16
❌ Failed: 0
📈 Success Rate: 100.0%

🎉 ALL TESTS PASSED! Refactored APIs are working correctly.
✅ All test data cleaned up successfully!
```

### ❌ Failure Scenario
```
📊 Total Tests: 16
✅ Passed: 14
❌ Failed: 2
📈 Success Rate: 87.5%

❌ Failed Tests:
1. [enrollmentPeriods] SuperAdmin should access enrollment periods
2. [estimatePlanCosts] Missing planAssignmentId should fail
```

## 🔍 Troubleshooting

### Server Not Running
```
❌ Server is not running at http://localhost:8080
Error: connect ECONNREFUSED 127.0.0.1:8080
```
**Solution:** Start the server with `npm run dev` or `npm start`

### Database Connection Issues
```
❌ Failed to create company: Test Brokerage Alpha - Database connection error
```
**Solution:** Ensure MongoDB is running and test database is accessible

### Permission Errors
```
❌ Failed to delete company: 507f1f77bcf86cd799439011 - Access denied
```
**Solution:** Ensure test user has proper permissions for CRUD operations

## 🎯 Validation Results

The comprehensive test suite validates that:

✅ **All 4 refactored APIs maintain original functionality**
✅ **Service layer delegation works correctly**
✅ **Access control is properly implemented**
✅ **Error handling returns appropriate status codes**
✅ **Response structures are consistent**
✅ **Business logic is preserved**
✅ **Data cleanup prevents test pollution**

## 🚀 Production Readiness

After successful test completion, the refactored employee enrollment APIs are:
- ✅ **Fully service-oriented** with proper separation of concerns
- ✅ **Backward compatible** with existing API contracts
- ✅ **Thoroughly tested** with comprehensive scenarios
- ✅ **Production-ready** with robust error handling
- ✅ **Maintainable** with clean architecture patterns

---

**Ready for deployment! 🎉**
