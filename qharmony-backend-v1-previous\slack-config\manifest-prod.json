{"display_information": {"name": "qHarmony", "description": "Maximize Your Employee Benefits & Perks!", "background_color": "#000000", "long_description": "*Say Goodbye to the Old Way: qHarmony Redefines Benefit Management! 🔄*\n❌ No more waiting for days to get answers to your benefit queries!\n❌ Keep everything in one place, no more scrambling through multiple locations.\n❌ Don't miss out on the benefits you deserve!\n\n*Maximize Your Benefit Management with qHarmony on Slack! 🚀*\n🌟 Centralized benefit management in Slack.\n🔄 Seamlessly integrated into your daily routine with AI-driven assistance.\n🧠 Receive personalized, real-time responses to benefit queries.\n📚 Year-round engaging benefit microlearning.\n🎯 Gain on-time and precise insights for informed decisions.\n💡 Get daily tips and tricks to maximize your employee benefits.\n\n*By using qHarmony on Slack, you won't miss out on:*\n🚗 Commute Stipends\n🏋️‍♂️ Gym Memberships\n👩‍⚕️ Preventive Care Yearly Checkups\n😁 Dental Coverage\n👂 Vision Checkups\n👥 Employee Assistance Program\n💼 Professional Development Opportunities\n🍏 Wellness Programs\n🎓 Educational Assistance\nAnd more…\n\n*Seize every benefit opportunity with ease! Optimize your well-being today.*"}, "features": {"app_home": {"home_tab_enabled": true, "messages_tab_enabled": true, "messages_tab_read_only_enabled": false}, "bot_user": {"display_name": "qHarmony", "always_online": false}}, "oauth_config": {"redirect_urls": ["https://slack.getqharmony.com/slack/oauth/access-token"], "scopes": {"bot": ["users:read", "users:write", "chat:write.public", "app_mentions:read", "channels:history", "chat:write", "chat:write.customize", "commands", "groups:history", "groups:read", "groups:write", "im:history", "im:read", "im:write", "metadata.message:read", "mpim:history", "mpim:read", "mpim:write"]}}, "settings": {"event_subscriptions": {"request_url": "https://slack.getqharmony.com/slack/event", "bot_events": ["app_home_opened", "app_mention", "message.channels", "message.groups", "message.im", "message.mpim"]}, "interactivity": {"is_enabled": true, "request_url": "https://slack.getqharmony.com/slack/interaction"}, "org_deploy_enabled": false, "socket_mode_enabled": false, "token_rotation_enabled": false}}