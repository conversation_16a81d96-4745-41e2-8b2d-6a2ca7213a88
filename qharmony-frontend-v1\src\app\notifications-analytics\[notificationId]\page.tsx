"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import {
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
} from "@mui/material";
import ProtectedRoute from "@/components/ProtectedRoute";
import withSidebar from "@/components/withSidebar";
import { BASE_URL, getRequest } from "@/APILayer/axios_helper";
import axios from "axios";

interface AnalyticsRowData {
  date: string;
  employer: string;
  message: string;
  messageDelivered: number;
  noActionTaken: number;
  total: number;
  [key: string]: any; // Dynamic keys
}

// Create a function to download the CSV
const downloadCSV = async (messageId: string) => {
  try {
    // Make the GET request using axios
    const response = await axios.get(`${BASE_URL}/download-notifications-analytics`, {
      params: { messageId },  // Pass the messageId as query params
      responseType: 'blob',    // Set response type to 'blob' for binary data
    });

    // Check if the response is successful and contains data
    if (response && response.data) {
      // Create a Blob from the response data
      const blob = new Blob([response.data], { type: 'text/csv' });

      // Create a link element to trigger the download
      const link = document.createElement('a');
      const url = window.URL.createObjectURL(blob);
      link.href = url;
      link.download = 'notification_analytics.csv'; // Specify the file name

      // Trigger the download
      link.click();

      // Clean up the object URL
      window.URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error('Failed to download CSV:', error);
  }
};

const NotificationAnalytics = () => {
  const router = useRouter();
  const pathname = usePathname();

  // Extract notificationId from the dynamic route
  const notificationId = pathname.split("/").pop();

  const [tableData, setTableData] = useState<AnalyticsRowData[]>([]);
  const [dynamicKeys, setDynamicKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!notificationId) return;

    const fetchAnalytics = async () => {
      setLoading(true);
      try {
        const result = await getRequest(`/notifications-analytics`, {
          messageId: notificationId,
        });

        if (result.success) {
          const data = result.data.map((row: AnalyticsRowData) => ({
            ...row,
            date: new Date(row.date).toISOString().split("T")[0], // Convert to readable date
          }));

          // Extract dynamic keys
          const keys = new Set<string>();
          data.forEach((row: AnalyticsRowData) => {
            Object.keys(row).forEach((key) => {
              if (
                ![
                  "date",
                  "employer",
                  "message",
                  "messageDelivered",
                  "noActionTaken",
                  "total",
                ].includes(key)
              ) {
                keys.add(key);
              }
            });
          });

          setDynamicKeys(Array.from(keys));
          setTableData(data);
        }
      } catch (error) {
        console.error("Failed to fetch analytics data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [notificationId]);

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6FA",
          px: 4,
          py: 2,
          width: "100%",
          height: "100vh",
          overflow: "hidden",
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
            mt: 3,
          }}
        >
          <Typography
            sx={{
              fontWeight: 600,
              fontSize: "28px",
              color: "black",
              lineHeight: "34px",
              textAlign: "left",
            }}
          >
            Notification Analytics
          </Typography>
          <Button
            variant="contained"
            onClick={async () => {
              if (notificationId) {
                downloadCSV(notificationId); // Trigger CSV download
              }
            }}
            sx={{
              textTransform: "none",
              borderRadius: "6px",
              bgcolor: "black",
              color: "white",
              boxShadow: "none",
              width: "140px",
              paddingY: "7px",
              paddingX: "16px",
              border: "1px solid #D2D2D2",
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.1)", // Slightly darker on hover
                boxShadow: "none",
              },
            }}
          >
            Download CSV
          </Button>

        </Box>

        {/* Analytics Table */}
        <TableContainer
          component={Paper}
          sx={{
            maxHeight: "calc(100vh - 140px)",
            overflow: "auto",
          }}
        >
          <Table stickyHeader sx={{ minWidth: 650 }} aria-label="analytics table">
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: "bold", width: "12%" }}>Date</TableCell>
                <TableCell sx={{ fontWeight: "bold", width: "15%" }}>Employer</TableCell>
                <TableCell sx={{ fontWeight: "bold", width: "25%" }}>Message</TableCell>
                <TableCell sx={{ fontWeight: "bold", width: "8%" }}>Message Delivered</TableCell>
                {dynamicKeys.map((key) => (
                  <TableCell key={key} sx={{ fontWeight: "bold", width: "8%" }}>
                    {key}
                  </TableCell>
                ))}
                <TableCell sx={{ fontWeight: "bold", width: "8%" }}>No Action Taken</TableCell>
                <TableCell sx={{ fontWeight: "bold", width: "8%" }}>Total</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tableData.map((row, index) => (
                <TableRow
                  key={index}
                  sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                >
                  <TableCell sx={{ width: "12%" }}>{row.date}</TableCell>
                  <TableCell sx={{ width: "15%" }}>{row.employer}</TableCell>
                  <TableCell sx={{ width: "25%" }}>{row.message}</TableCell>
                  <TableCell sx={{ width: "8%" }}>{row.messageDelivered}</TableCell>
                  {dynamicKeys.map((key) => (
                    <TableCell key={key} sx={{ width: "8%" }}>
                      {row[key] || 0}
                    </TableCell>
                  ))}
                  <TableCell sx={{ width: "8%" }}>{row.noActionTaken}</TableCell>
                  <TableCell sx={{ width: "8%" }}>{row.total}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </ProtectedRoute>
  );
};

// Wrap with sidebar HOC
export default withSidebar(NotificationAnalytics);
