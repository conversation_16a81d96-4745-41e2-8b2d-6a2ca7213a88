
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, TrendingUp, Trophy } from 'lucide-react';
import { SummaryCard } from '@/components/SummaryCard';
import { PlanQADialog } from '@/components/PlanQADialog';
import { UserProfile } from '@/components/BenefitsEnrollmentBot';
import { BotQuestion } from '@/components/BotQuestion';

interface SummaryPageProps {
  userProfile: UserProfile;
  onConfirm: () => void;
  onEdit: () => void;
}

export const SummaryPage = ({ userProfile, onConfirm, onEdit }: SummaryPageProps) => {
  const calculateTotalAnnualValue = () => {
    const medical = userProfile.selectedMedical?.cost ? userProfile.selectedMedical.cost * 26 * 4 : 0;
    const dental = userProfile.selectedDental?.cost ? userProfile.selectedDental.cost * 26 + 800 : 0;
    const vision = userProfile.selectedVision?.cost ? userProfile.selectedVision.cost * 26 + 300 : 0;
    const pet = userProfile.selectedPetInsurance?.cost ? userProfile.selectedPetInsurance.cost * 26 * 3 : 0;
    const hospital = userProfile.selectedHospitalIndemnity?.cost ? userProfile.selectedHospitalIndemnity.cost * 26 * 5 : 0;
    
    return Math.round(medical + dental + vision + pet + hospital);
  };

  const calculateEmployeeCost = () => {
    const medical = userProfile.selectedMedical?.cost ? userProfile.selectedMedical.cost * 26 * 0.2 : 0;
    const dental = userProfile.selectedDental?.cost ? userProfile.selectedDental.cost * 26 * 0.5 : 0;
    const vision = userProfile.selectedVision?.cost ? userProfile.selectedVision.cost * 26 * 0.5 : 0;
    const pet = userProfile.selectedPetInsurance?.cost ? userProfile.selectedPetInsurance.cost * 26 : 0;
    const hospital = userProfile.selectedHospitalIndemnity?.cost ? userProfile.selectedHospitalIndemnity.cost * 26 : 0;
    
    return Math.round(medical + dental + vision + pet + hospital);
  };

  const totalValue = calculateTotalAnnualValue();
  const employeeCost = calculateEmployeeCost();
  const netBenefit = totalValue - employeeCost;
  const roi = employeeCost > 0 ? Math.round((netBenefit / employeeCost) * 100) : 0;

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <BotQuestion 
        question="📋 Perfect! Here's your personalized benefits package summary."
        context="Take a moment to review everything. You can always go back and make changes if needed."
      />

      {/* Congratulatory Value Card */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-100 dark:from-green-950 dark:to-emerald-900 border-green-200 dark:border-green-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800 dark:text-green-200">
            <Trophy className="w-6 h-6" />
            🎉 Congratulations! Smart Choices Made
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
              <p className="text-sm text-muted-foreground">Annual Value</p>
              <p className="text-xl font-bold text-green-600">${totalValue.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
              <p className="text-sm text-muted-foreground">You Pay</p>
              <p className="text-xl font-bold text-blue-600">${employeeCost.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
              <p className="text-sm text-muted-foreground">Net Benefit</p>
              <p className="text-xl font-bold text-green-600">${netBenefit.toLocaleString()}</p>
            </div>
          </div>
          
          <div className="text-center p-4 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-lg">
            <div className="flex items-center justify-center gap-2 mb-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <span className="font-medium">Your Total ROI</span>
            </div>
            <p className="text-3xl font-bold text-green-700 dark:text-green-300">{roi}%</p>
            <Badge className="mt-2 bg-green-600 text-white">
              💰 Excellent Financial Decision!
            </Badge>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>💡 Smart Summary:</strong> You're getting ${totalValue.toLocaleString()} in value for just ${employeeCost.toLocaleString()} - 
              that's like getting ${netBenefit.toLocaleString()} in free benefits! Your employer is contributing significantly to your healthcare costs.
            </p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-6 h-6 text-orange-500" />
            📋 Smart Enrollment Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-lg">✅ Here's your personalized benefits package:</p>
          
          <SummaryCard 
            userProfile={userProfile}
            onConfirm={onConfirm}
            onEdit={onEdit}
          />
          
          <div className="flex gap-2 pt-4">
            <PlanQADialog selectedPlans={{}} />
            <Button variant="outline" onClick={() => window.print()}>
              📄 Print Summary
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
