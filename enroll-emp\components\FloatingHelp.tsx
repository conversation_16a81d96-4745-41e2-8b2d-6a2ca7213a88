
import React, { useState } from 'react';
import { HelpCircle, FileText, Calculator, Users, Clock, X } from 'lucide-react';

export const FloatingHelp = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('deadlines');

  const helpTopics = {
    deadlines: [
      { q: "When is the enrollment deadline?", a: "December 15, 2024 at 11:59 PM" },
      { q: "What happens if I miss the deadline?", a: "You'll have no coverage for 2025 unless you have a qualifying life event" },
      { q: "Can I change my mind after enrolling?", a: "Only during next year's open enrollment or with a qualifying life event" }
    ],
    costs: [
      { q: "How much will plans cost me?", a: "Costs vary by plan and family size. Use our calculator above to see your specific costs." },
      { q: "When do deductions start?", a: "Payroll deductions begin with your first paycheck in January 2025" },
      { q: "Are there any hidden fees?", a: "No hidden fees. The costs shown include all plan premiums." }
    ],
    coverage: [
      { q: "When does coverage start?", a: "January 1, 2025 for all plans" },
      { q: "What if I need care before January 1?", a: "Your current 2024 benefits remain active through December 31, 2024" },
      { q: "Do I need to re-enroll every year?", a: "Yes, you must actively enroll each year during open enrollment" }
    ],
    plans: [
      { q: "What's the difference between HMO and PPO?", a: "PPO offers more flexibility but costs more. HMO requires referrals but has lower costs." },
      { q: "Can I have different plan types for different benefits?", a: "Yes! You can mix and match (e.g., HMO medical with PPO dental)" },
      { q: "Which plan is best for me?", a: "Use our personalization tool above to get recommendations based on your needs" }
    ]
  };

  const tabs = [
    { id: 'deadlines', label: 'Deadlines', icon: Clock },
    { id: 'costs', label: 'Costs', icon: Calculator },
    { id: 'coverage', label: 'Coverage', icon: FileText },
    { id: 'plans', label: 'Plans', icon: Users }
  ];

  return (
    <>
      {/* Floating Help Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
      >
        <HelpCircle className="w-6 h-6" />
      </button>

      {/* Help Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <HelpCircle className="w-5 h-5 text-blue-500" />
                  Benefits Enrollment Help Center
                </h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6">
              {/* Tab Navigation */}
              <div className="grid grid-cols-4 gap-2 mb-6">
                {tabs.map(tab => {
                  const IconComponent = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <IconComponent className="w-3 h-3" />
                      {tab.label}
                    </button>
                  );
                })}
              </div>

              {/* Tab Content */}
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {helpTopics[activeTab as keyof typeof helpTopics].map((topic, index) => (
                  <div key={index} className="bg-white border rounded-lg">
                    <div className="p-4 border-b">
                      <h4 className="font-medium text-base">{topic.q}</h4>
                    </div>
                    <div className="p-4">
                      <p className="text-sm text-gray-600">{topic.a}</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Contact Info */}
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium mb-2">Still need help?</h4>
                <div className="flex gap-2 flex-wrap">
                  <span className="px-3 py-1 bg-white border rounded-full text-sm">📞 Call HR: (555) 123-4567</span>
                  <span className="px-3 py-1 bg-white border rounded-full text-sm">📧 Email: <EMAIL></span>
                  <span className="px-3 py-1 bg-white border rounded-full text-sm">💬 Live Chat: Mon-Fri 9AM-5PM</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
