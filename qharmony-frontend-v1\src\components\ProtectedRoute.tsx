import { ReactNode, useEffect, useState } from "react";
import { useAuth } from "./AuthContext";
import { useRouter, usePathname } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { setUserId } from "@/redux/reducers/userSlice";
import {
  getCompanyDetails,
  getUserDetails,
} from "@/middleware/company_middleware";
import { RootState } from "@/redux/store";
import CircularProgress from "@mui/material/CircularProgress";
import Box from "@mui/material/Box";

// Utility function to detect mobile devices
const isMobileDevice = () => {
  return /Mobi|Android/i.test(navigator.userAgent);
};

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useAppDispatch();

  const [seeProtectedRoute, setSeeProtectedRoute] = useState(false);

  const userDetails = useAppSelector(
    (state: RootState) => state.user.userProfile,
  );

  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      const userId = localStorage.getItem("userid1") || localStorage.getItem("userId");

      console.log("USER ID FROM LOCAL STORAGE: ", userId);

      if (userId && !userDetails.name) { // Only fetch if we don't have user details yet
        dispatch(setUserId(userId));

        const fetchUserData = async () => {
          try {
            await getUserDetails(dispatch, userId);
            await getCompanyDetails(dispatch);
          } catch (error) {
            console.error("Error fetching user data in ProtectedRoute:", error);
          }
        };

        fetchUserData();
      }
    }
  }, [dispatch, userDetails.name]); // Add userDetails.name to dependencies

  useEffect(() => {
    console.log("ProtectedRoute useEffect triggered");
    console.log("Current user: ", user);
    console.log("Loading state: ", loading);
    console.log("Current user details: ", userDetails);



    if (!loading && !user) {
      console.log("User not authenticated, redirecting to home");
      setSeeProtectedRoute(false);
      router.push("/");
    }

    if (!loading && userDetails.companyId && userDetails.companyId === "") {
      console.log("Waiting to retrieve company details");
      setSeeProtectedRoute(false);
    }

    if (!loading && userDetails.companyId && userDetails.companyId !== "") {
      console.log("User found, rendering children");
      setSeeProtectedRoute(true);
    }

    // Check if the user is on a mobile device and redirect to mobile route
    if (isMobileDevice() && !pathname.startsWith("/mobile")) {
      console.log(`Redirecting to mobile version of ${pathname}`);
      router.push(`/mobile${pathname}`);
    }
  }, [user, loading, userDetails, router, pathname]);

  if (!seeProtectedRoute) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          bgcolor: "#f6f8fc",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return user ? <>{children}</> : null;
};

export default ProtectedRoute;
