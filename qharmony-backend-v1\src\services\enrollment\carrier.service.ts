import express from 'express';
import UserModelClass from '../../nosql/user.model';
import CarrierModelClass, { CarrierDataInterface, UpdateableCarrierDataInterface } from '../../nosql/preEnrollment/carrier.model';
import logger from '../../utils/logger';
import { CARRIER_STATUSES } from '../../constants';

/**
 * Centralized Carrier Service
 * Combines business logic, access control, and data operations
 */
export class CarrierService {

  // ===== ACCESS CONTROL METHODS =====

  /**
   * Validate user access and get user details
   */
  static async validateUserAccess(userId: string) {
    const user = await UserModelClass.getDataById(userId);
    if (!user) {
      return { hasAccess: false, reason: 'User not found', user: null };
    }
    return { hasAccess: true, user, reason: null };
  }

  /**
   * Get carriers based on user access level
   */
  static async getCarriersForUser(userId: string, user: any): Promise<CarrierDataInterface[]> {
    if (UserModelClass.isSuperAdmin(user)) {
      return await CarrierModelClass.getAllData();
    } else if (user.isBroker) {
      return await CarrierModelClass.getDataByBrokerId(userId);
    } else if (user.isAdmin) {
      return await CarrierModelClass.getSystemCarriers();
    } else {
      return await CarrierModelClass.getSystemCarriers();
    }
  }

  /**
   * Get assignable carriers based on user access level
   */
  static async getAssignableCarriersForUser(userId: string, user: any): Promise<CarrierDataInterface[]> {
    if (UserModelClass.isSuperAdmin(user)) {
      return await CarrierModelClass.getAssignableCarriers();
    } else if (user.isBroker) {
      return await CarrierModelClass.getAssignableCarriers(userId);
    } else {
      const carriers = await CarrierModelClass.getAssignableCarriers();
      return carriers.filter(carrier => carrier.isSystemCarrier);
    }
  }

  /**
   * Get single carrier with access control
   */
  static async getCarrierWithAccess(carrierId: string, userId: string, user: any) {
    let carrier: CarrierDataInterface | null = null;

    if (UserModelClass.isSuperAdmin(user)) {
      carrier = await CarrierModelClass.getDataById(carrierId);
    } else if (user.isBroker) {
      carrier = await CarrierModelClass.getDataById(carrierId, userId);
    } else {
      carrier = await CarrierModelClass.getDataById(carrierId, userId);
      if (carrier && !carrier.isSystemCarrier) {
        return { carrier: null, hasAccess: false, reason: 'Access denied to non-system carrier' };
      }
    }

    if (!carrier) {
      return { carrier: null, hasAccess: false, reason: 'Carrier not found or access denied' };
    }

    return { carrier, hasAccess: true };
  }

  // ===== BUSINESS LOGIC METHODS =====

  /**
   * Create a new carrier with full business validation
   */
  static async createCarrier(carrierData: any, userId: string, user: any) {
    try {
      // Permission checks
      if (!user.isBroker && !UserModelClass.isSuperAdmin(user)) {
        return { success: false, error: 'Only super admins and brokers can create carriers' };
      }

      if (carrierData.isSystemCarrier && !UserModelClass.isSuperAdmin(user)) {
        return { success: false, error: 'Only super admins can create system carriers' };
      }

      // Check uniqueness
      const existingCarrier = await CarrierModelClass.getDataByCode(carrierData.carrierCode);
      if (existingCarrier) {
        return { success: false, error: 'Carrier code already exists' };
      }

      // Determine ownership
      let brokerId: string | undefined;
      let brokerageId: string | undefined;
      let shouldBeSystemCarrier = carrierData.isSystemCarrier;

      if (UserModelClass.isSuperAdmin(user)) {
        shouldBeSystemCarrier = true;
        brokerId = userId;
        brokerageId = user.companyId;
      } else if (user.isBroker) {
        shouldBeSystemCarrier = false;
        brokerId = userId;
        brokerageId = user.companyId;
      }

      // Broker-specific uniqueness validation
      if (brokerId && !shouldBeSystemCarrier) {
        const codeValidation = await CarrierModelClass.validateBrokerUniqueCarrierCode(brokerId, carrierData.carrierCode);
        if (!codeValidation.isUnique) {
          return { success: false, error: codeValidation.message };
        }

        const nameValidation = await CarrierModelClass.validateBrokerUniqueCarrierName(brokerId, carrierData.carrierName);
        if (!nameValidation.isUnique) {
          return { success: false, error: nameValidation.message };
        }
      }

      // Build final carrier data
      const finalCarrierData: CarrierDataInterface = {
        ...carrierData,
        brokerId,
        brokerageId,
        isSystemCarrier: shouldBeSystemCarrier,
        contactInfo: carrierData.contactInfo || {},
        supportedPlanTypes: carrierData.supportedPlanTypes || [],
        supportedCoverageTypes: carrierData.supportedCoverageTypes || [],
        supportedCoverageSubTypes: carrierData.supportedCoverageSubTypes || [],
        integration: carrierData.integration || { ediCapable: false },
        licenseStates: carrierData.licenseStates || [],
        status: CARRIER_STATUSES[0], // 'Active'
        isActive: true,
        isActivated: true
      };

      const createdCarrier = await CarrierModelClass.addData(finalCarrierData);
      if (!createdCarrier) {
        return { success: false, error: 'Failed to create carrier' };
      }

      logger.info(`Carrier created successfully: ${createdCarrier._id} by user: ${userId}`);
      return { success: true, carrier: createdCarrier };

    } catch (error) {
      logger.error('Error in createCarrier service:', error);
      return { success: false, error: 'Internal server error during carrier creation' };
    }
  }

  /**
   * Update carrier with access control and validation
   */
  static async updateCarrier(carrierId: string, updateData: UpdateableCarrierDataInterface, userId: string, user: any) {
    try {
      // Check update permissions
      let existingCarrier: CarrierDataInterface | null = null;

      if (UserModelClass.isSuperAdmin(user)) {
        existingCarrier = await CarrierModelClass.getDataById(carrierId);
      } else if (user.isBroker) {
        existingCarrier = await CarrierModelClass.getDataById(carrierId, userId);
        if (existingCarrier && existingCarrier.isSystemCarrier) {
          return { success: false, error: 'Brokers cannot update system carriers' };
        }
      } else {
        return { success: false, error: 'Insufficient permissions to update carriers' };
      }

      if (!existingCarrier) {
        return { success: false, error: 'Carrier not found or access denied' };
      }

      // Validate uniqueness if name being updated (carrierCode is not updatable)
      if (updateData.carrierName && updateData.carrierName !== existingCarrier.carrierName) {
        if (existingCarrier.brokerId && !existingCarrier.isSystemCarrier) {
          const nameValidation = await CarrierModelClass.validateBrokerUniqueCarrierName(existingCarrier.brokerId, updateData.carrierName, carrierId);
          if (!nameValidation.isUnique) {
            return { success: false, error: nameValidation.message };
          }
        }
      }

      const updateResult = await CarrierModelClass.updateData({ id: carrierId, data: updateData });
      if (!updateResult || updateResult.modifiedCount === 0) {
        return { success: false, error: 'Failed to update carrier' };
      }

      // Get updated carrier
      const updatedCarrier = await CarrierModelClass.getDataById(carrierId);
      if (!updatedCarrier) {
        return { success: false, error: 'Failed to retrieve updated carrier' };
      }

      logger.info(`Carrier updated successfully: ${carrierId} by user: ${userId}`);
      return { success: true, carrier: updatedCarrier };

    } catch (error) {
      logger.error('Error in updateCarrier service:', error);
      return { success: false, error: 'Internal server error during carrier update' };
    }
  }

  /**
   * Delete carrier with access control
   */
  static async deleteCarrier(carrierId: string, userId: string, user: any) {
    try {
      // Check delete permissions (same logic as update)
      let existingCarrier: CarrierDataInterface | null = null;

      if (UserModelClass.isSuperAdmin(user)) {
        existingCarrier = await CarrierModelClass.getDataById(carrierId);
      } else if (user.isBroker) {
        existingCarrier = await CarrierModelClass.getDataById(carrierId, userId);
        if (existingCarrier && existingCarrier.isSystemCarrier) {
          return { success: false, error: 'Brokers cannot delete system carriers' };
        }
      } else {
        return { success: false, error: 'Insufficient permissions to delete carriers' };
      }

      if (!existingCarrier) {
        return { success: false, error: 'Carrier not found or access denied' };
      }

      const deleted = await CarrierModelClass.deleteData(carrierId);
      if (!deleted) {
        return { success: false, error: 'Failed to delete carrier' };
      }

      logger.info(`Carrier deleted successfully: ${carrierId} by user: ${userId}`);
      return { success: true };

    } catch (error) {
      logger.error('Error in deleteCarrier service:', error);
      return { success: false, error: 'Internal server error during carrier deletion' };
    }
  }

  /**
   * Activate/Deactivate/Archive carrier
   */
  static async modifyCarrierStatus(carrierId: string, action: 'activate' | 'deactivate' | 'archive', userId: string, user: any) {
    try {
      // Check permissions first (without actually updating)
      let existingCarrier: CarrierDataInterface | null = null;

      if (UserModelClass.isSuperAdmin(user)) {
        existingCarrier = await CarrierModelClass.getDataById(carrierId);
      } else if (user.isBroker) {
        existingCarrier = await CarrierModelClass.getDataById(carrierId, userId);
        if (existingCarrier && existingCarrier.isSystemCarrier) {
          return { success: false, error: 'Brokers cannot modify system carriers' };
        }
      } else {
        return { success: false, error: 'Insufficient permissions to modify carriers' };
      }

      if (!existingCarrier) {
        return { success: false, error: 'Carrier not found or access denied' };
      }

      let result: any;
      switch (action) {
        case 'activate':
          result = await CarrierModelClass.activateCarrier(carrierId);
          break;
        case 'deactivate':
          result = await CarrierModelClass.deactivateCarrier(carrierId);
          break;
        case 'archive':
          result = await CarrierModelClass.archiveCarrier(carrierId);
          break;
      }

      if (!result) {
        return { success: false, error: `Failed to ${action} carrier` };
      }

      logger.info(`Carrier ${action}d successfully: ${carrierId} by user: ${userId}`);
      return { success: true, carrier: result };

    } catch (error) {
      logger.error(`Error in ${action}Carrier service:`, error);
      return { success: false, error: `Internal server error during carrier ${action}` };
    }
  }

  // ===== UTILITY METHODS =====

  /**
   * Filter carriers based on query parameters
   */
  static filterCarriers(carriers: CarrierDataInterface[], filters: any): CarrierDataInterface[] {
    let filteredCarriers = [...carriers];
    const { status, planType, coverageType, coverageSubTypes, state, ediCapable, isSystemCarrier } = filters;

    if (status) filteredCarriers = filteredCarriers.filter(c => c.status === status);
    if (planType) filteredCarriers = filteredCarriers.filter(c => c.supportedPlanTypes.includes(planType));
    if (coverageType) filteredCarriers = filteredCarriers.filter(c => c.supportedCoverageTypes.includes(coverageType));
    if (state) filteredCarriers = filteredCarriers.filter(c => c.licenseStates.includes(state));
    if (ediCapable === 'true') filteredCarriers = filteredCarriers.filter(c => c.integration.ediCapable);
    if (isSystemCarrier !== undefined) {
      const isSystem = isSystemCarrier === 'true';
      filteredCarriers = filteredCarriers.filter(c => c.isSystemCarrier === isSystem);
    }

    if (coverageSubTypes) {
      const parsedSubTypes = typeof coverageSubTypes === 'string' 
        ? coverageSubTypes.split(',').map(s => s.trim()) : coverageSubTypes;
      if (parsedSubTypes?.length > 0) {
        filteredCarriers = filteredCarriers.filter(c =>
          parsedSubTypes.every((subType: string) => c.supportedCoverageSubTypes.includes(subType))
        );
      }
    }

    return filteredCarriers;
  }

  /**
   * Create pagination object
   */
  static createPagination(page: number, limit: number, totalCarriers: number) {
    return {
      currentPage: page,
      totalPages: Math.ceil(totalCarriers / limit),
      totalCarriers,
      hasNext: page * limit < totalCarriers,
      hasPrev: page > 1
    };
  }

  // ===== MISSING METHODS FROM CONTROLLER =====

  /**
   * Validate carrier compatibility (used by controller)
   */
  static async validateCarrierCompatibility(carrierId: string, validationData: any, userId: string, user: any) {
    try {
      const accessCheck = await this.getCarrierWithAccess(carrierId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason };
      }

      const result = await CarrierModelClass.validatePlanCarrierCompatibility(
        carrierId,
        validationData.planType,
        validationData.coverageType,
        validationData.coverageSubTypes
      );

      return { success: true, result };
    } catch (error) {
      logger.error('Error in validateCarrierCompatibility service:', error);
      return { success: false, error: 'Internal server error during carrier validation' };
    }
  }

  /**
   * Check if carrier can be edited (used by controller)
   */
  static async canEditCarrier(carrierId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getCarrierWithAccess(carrierId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason };
      }

      const result = await CarrierModelClass.canEditCarrier(carrierId);
      return { success: true, result };
    } catch (error) {
      logger.error('Error in canEditCarrier service:', error);
      return { success: false, error: 'Internal server error during edit check' };
    }
  }

  /**
   * Check if carrier can be deleted (used by controller)
   */
  static async canDeleteCarrierCheck(carrierId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getCarrierWithAccess(carrierId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason };
      }

      const result = await CarrierModelClass.canDeleteCarrier(carrierId);
      return { success: true, result };
    } catch (error) {
      logger.error('Error in canDeleteCarrier service:', error);
      return { success: false, error: 'Internal server error during delete check' };
    }
  }

  /**
   * Get dependent plans for carrier (used by controller)
   */
  static async getDependentPlans(carrierId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getCarrierWithAccess(carrierId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason };
      }

      const result = await CarrierModelClass.getPlansReferencingCarrier(carrierId);
      return { success: true, result: { plans: result, count: result.length } };
    } catch (error) {
      logger.error('Error in getDependentPlans service:', error);
      return { success: false, error: 'Internal server error during dependent plans retrieval' };
    }
  }
}
