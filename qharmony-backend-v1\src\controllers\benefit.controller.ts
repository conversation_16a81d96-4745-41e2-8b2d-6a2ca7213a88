import express from 'express';
import Controller from '../interfaces/controller.interface';
import logger from '../utils/logger';
// import authMiddleware from '../middleware/auth.middleware';
import adminAuthMiddleware from '../middleware/isAdmin.middleware';
import BenefitModelClass, {
  BenefitDataInterface,
} from '../nosql/benefit.model';
// import isEmployerMiddleware from '../middleware/isEmployer.middleware';
// import AWSService from '../services/aws';
import { v4 as uuidv4 } from 'uuid';
import multer from 'multer';
import { getContentType } from '../utils/fileUtils';
import CompanyModelClass from '../nosql/company.model';
import axios from 'axios';
import { BENEFIT_TYPE_SUBTYPE_MAP } from '../constants';
import AzureBlobService from '../services/azure';
import AzureNamespaceService from '../services/azureNamespace.service';
import UserModelClass from '../nosql/user.model';
import GroupModelClass from '../nosql/group.model';
import EnvService from '../services/env.service';

class EmployerController implements Controller {
  public router = express.Router();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    const upload = multer({ storage: multer.memoryStorage() });

    // this.router.get('/benefits', authMiddleware(), this.getBenefits);
    // this.router.put(
    //   '/benefits/:benefitId',
    //   authMiddleware(),
    //   isEmployerMiddleware(),
    //   this.updateBenefit
    // );
    // this.router.post(
    //   '/benefits/get-upload-url/:benefitId',
    //   authMiddleware(),
    //   isEmployerMiddleware(),
    //   this.getUploadUrl
    // );
    this.router.post(
      '/benefits/toggle-benefits',
      adminAuthMiddleware,
      this.toggleBenefits
    );
    this.router.get('/benefits/all-benefits', this.getAllBenefits);
    this.router.get('/benefits/one-benefit/', this.getOneBenefit);
    this.router.get('/benefits/benefit-by-type', this.getBenefitByType);
    this.router.get('/benefits/benefit-types', this.benefitTypes);
    this.router.post('/benefits/add/links', adminAuthMiddleware, this.addLink);
    this.router.post(
      '/benefits/add/document',
      upload.array('documents'),
      adminAuthMiddleware,
      this.addDocument
    );
    this.router.post(
      '/benefits/delete/link',
      adminAuthMiddleware,
      this.removeLink
    );
    this.router.post(
      '/benefits/delete/document',
      adminAuthMiddleware,
      this.removeDocument
    );
    this.router.get('/benefits/document', this.getDocument);
  }

  // private getBenefits = async (
  //   _request: express.Request,
  //   response: express.Response
  // ) => {
  //   try {
  //     const { company, user } = _request.body;
  //     // Check if the user is an employer.
  //     if (user.role !== 'employer') {
  //       response.status(401).json({ error: 'You are not authorized' });
  //       return;
  //     }
  //     const benefits = await BenefitModelClass.getDataByCompanyId({
  //       companyId: company._id,
  //     });
  //     // Extract the imageS3Urls from the benefits and call the AWS service's generatePresignedUrlForDownload method to get the presigned URL for each image and reconstruct the benefits object.
  //     const constructedBenefits = await Promise.all(
  //       benefits.map(async (benefit) => {
  //         const imageS3Urls = benefit.imageS3Urls;
  //         const presignedUrls = await Promise.all(
  //           imageS3Urls.map(async (imageS3Url) => {
  //             return await AWSService.generatePresignedUrlForDownload({
  //               Bucket: 'qharmony-benefits',
  //               Key: imageS3Url,
  //             });
  //           })
  //         );
  //         return {
  //           ...((benefit as any)._doc as BenefitDataInterface),
  //           imageS3Urls: presignedUrls,
  //         };
  //       })
  //     );
  //     response.status(200).json({ benefits: constructedBenefits });
  //   } catch (error) {
  //     logger.error('Error processing Slack webhook:', error);
  //     response.status(500).json({ error: 'Internal server error' });
  //   }
  // };

  // private updateBenefit = async (
  //   request: express.Request,
  //   response: express.Response
  // ) => {
  //   try {
  //     const { company } = request.body;
  //     const benefitId = request.params.benefitId;
  //     const benefit = request.body.benefit;
  //     // Check that companyId are not present in the benefit object.
  //     if (benefit.companyId || benefit.imageS3Urls) {
  //       response.status(400).json({ error: 'Invalid request' });
  //       return;
  //     }
  //     const objects =
  //       (await AWSService.getAllObjects({
  //         Bucket: 'qharmony-benefits',
  //         Prefix: `${company._id}/${benefitId}`,
  //       })) ?? [];

  //     const updatedBenefit =
  //       await BenefitModelClass.updateDataByCompanyIdAndBenefitId({
  //         companyId: company._id,
  //         benefitId,
  //         data: {
  //           ...benefit,
  //           imageS3Urls: objects.map((object) => object.Key),
  //         },
  //       });

  //     response.status(200).json({ updatedBenefit, objects });
  //   } catch (error) {
  //     logger.error('Error processing Slack webhook:', error);
  //     response.status(500).json({ error: 'Internal server error' });
  //   }
  // };

  // private getUploadUrl = async (
  //   request: express.Request,
  //   response: express.Response
  // ) => {
  //   try {
  //     const { company, filename } = request.body;
  //     const benefitId = request.params.benefitId;
  //     // const path = `${company._id}/${benefitId}`;
  //     let path = `${company._id.toString()}/${benefitId}/${Date.now()}_${filename}`;
  //     path = decodeURIComponent(path);
  //     const expiryTimeInSecs = 60 * 5;
  //     response.status(200).json({
  //       url: await AWSService.generatePresignedUrl({
  //         Bucket: 'qharmony-benefits',
  //         Key: path,
  //         ACL: 'private',
  //         Expires: expiryTimeInSecs,
  //         Metadata: {
  //           companyId: company._id.toString(),
  //           benefitId: benefitId,
  //         },
  //       }),
  //       expiryTime: expiryTimeInSecs + Math.round(Date.now() / 1000),
  //     });
  //   } catch (error) {
  //     logger.error('Error generating presigned URL:', error);
  //     response.status(500).json({ error: 'Internal server error' });
  //   }
  // };

  // Modified by Devesh Paragiri

  private toggleBenefits = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { companyId } = request.body as {
        companyId: string;
      };

      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }

      const { benefitId, isActivated } = request.body as {
        benefitId: string;
        isActivated: boolean;
      };

      const result = await BenefitModelClass.updateDataByCompanyIdAndBenefitId({
        companyId: companyId,
        benefitId: benefitId,
        data: { isActivated },
      });

      if (result.matchedCount === 0) {
        response.status(400).json({ error: 'Benefit not found' });
        return;
      }

      if (result.modifiedCount) {
        response
          .status(200)
          .json({ message: `Benefit is set to ${isActivated}` });
        return;
      } else {
        response
          .status(200)
          .json({ error: 'Benefit is already set to requested value' });
        return;
      }
    } catch (error) {
      logger.error('Error toggling benefits', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private getAllBenefits = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const companyId = request.query.companyId as string;
      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }
      const benefits = await BenefitModelClass.getDataByCompanyId({
        companyId,
      });

      if (!benefits || benefits.length === 0) {
        response.status(404).json({ error: 'No benefits found' });
        return;
      }

      // Group benefits by type
      const benefitsByType = benefits.reduce((acc, benefit) => {
        if (!acc[benefit.type]) {
          acc[benefit.type] = [];
        }
        acc[benefit.type].push(benefit);
        return acc;
      }, {} as Record<string, BenefitDataInterface[]>);

      // Sort the benefit types based on the order in BENEFIT_TYPE_SUBTYPE_MAP
      const sortedBenefitTypes = BENEFIT_TYPE_SUBTYPE_MAP.map(
        (item) => item.type
      ).filter((type) => benefitsByType.hasOwnProperty(type));

      // Create a sorted benefits array
      const benefitsArray = sortedBenefitTypes.map((type) => {
        const typeObj = BENEFIT_TYPE_SUBTYPE_MAP.find(
          (item) => item.type === type
        );

        if (!typeObj) {
          return { benefitType: type, benefits: benefitsByType[type] };
        }

        // Sort the benefits by the order of subtypes in BENEFIT_TYPE_SUBTYPE_MAP
        const sortedBenefits = typeObj.subTypes
          .map((subType) => {
            return benefitsByType[type].filter(
              (benefit) => benefit.subType === subType
            );
          })
          .flat();

        return { benefitType: type, benefits: sortedBenefits };
      });

      response.status(200).json({ benefitsPerType: benefitsArray });
      return;
    } catch (error) {
      logger.error('Error viewing all benefits', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private getOneBenefit = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const user_id = request.headers['user-id'] as string;
      const benefitId = request.query.benefitId as string;
      const page = request.query.page as string;
      const benefit = await BenefitModelClass.getDataById(benefitId);

      if (!benefit) {
        response.status(400).json({ error: 'Benefit not found' });
        return;
      }

      const userDetails = await UserModelClass.getDataById(user_id);

      if (!userDetails) {
        response.status(400).json({ error: 'User not found' });
        return;
      }

      let filteredDocuments;

      if (page === 'edit_benefit' && Boolean(userDetails?.isAdmin)) {
        filteredDocuments = benefit.imageS3Urls;
      } else {
        // Check if user has groupIds and it's not empty
        if (!userDetails?.groupIds?.length) {
          response
            .status(400)
            .json({ error: 'User is not associated with any group' });
          return;
        }

        let groupIds = userDetails.groupIds;

        const documentIds = await GroupModelClass.getCombinedDocumentIds(
          groupIds
        );

        filteredDocuments = documentIds.filter((documentId) =>
          benefit.imageS3Urls.includes(documentId)
        );
      }

      response
        .status(200)
        .json({ documents: filteredDocuments, links: benefit.links });
    } catch (error) {
      logger.error('Error viewing one benefit', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private benefitTypes = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const companyId = request.query.companyId as string;

      // Check if companyId is provided
      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }

      // Check if the company exists
      const companyExists = await CompanyModelClass.getDataById(companyId);
      if (!companyExists) {
        response.status(404).json({ error: 'Company not found' });
        return;
      }

      const benefits = await BenefitModelClass.getDataByCompanyId({
        companyId,
      });

      // Check if benefits exist for the company
      if (benefits.length === 0) {
        response
          .status(404)
          .json({ error: 'No benefits found for this company' });
        return;
      }

      // Filter benefits to only include those with isActivated set to true
      const activatedBenefits = benefits.filter(
        (benefit) => Boolean(benefit?.isActivated)
      );

      // Extract unique benefit types from activated benefits
      const benefitTypes = activatedBenefits.map((benefit) => benefit.type);
      const uniqueBenefitTypes = [...new Set(benefitTypes)];

      // Sort unique benefit types based on BENEFIT_TYPE_SUBTYPE_MAP
      const sortedBenefitTypes = BENEFIT_TYPE_SUBTYPE_MAP.map(
        (typeObj) => typeObj.type
      ).filter((type) => uniqueBenefitTypes.includes(type));

      response.status(200).json({ benefitTypes: sortedBenefitTypes });
      return;
    } catch (error) {
      logger.error('Error viewing benefit types', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private addDocument = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { companyId } = request.body as { companyId: string };
      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }
      const { benefitId } = request.body as { benefitId: string };

      if (
        !request.files ||
        !Array.isArray(request.files) ||
        request.files.length === 0
      ) {
        response.status(400).json({ error: 'No files were uploaded.' });
        return;
      }

      // =============================================================================
      // AWS LOGIC
      // =============================================================================

      // const bucketName = `employer-${companyId}`;
      // const exists = await AWSService.bucketExists(bucketName);

      // // Create a bucket if it does not exist
      // if (!exists) {
      //   const data = await AWSService.createBucket(bucketName);
      //   logger.info(
      //     `Bucket ${bucketName} created successfully: ${data.Location}`
      //   );
      // }

      // const uploadedFiles = [];
      // const objectKeys = [];

      // for (const file of request.files) {
      //   const fileExtension = file.originalname.split('.').pop() || '';
      //   const fileNameWithoutExtension = file.originalname.replace(
      //     /\.[^/.]+$/,
      //     ''
      //   );
      //   const objectKey = `${benefitId}-${Date.now()}-${uuidv4()}_____${fileNameWithoutExtension}.${fileExtension}`;

      //   const uploadParams: AWS.S3.PutObjectRequest = {
      //     Bucket: bucketName,
      //     Key: objectKey,
      //     Body: file.buffer,
      //     ContentType: file.mimetype,
      //   };

      //   const uploadResult = await AWSService.upload(uploadParams);
      //   logger.info(
      //     `File ${file.originalname} uploaded successfully: ${uploadResult.Location}`
      //   );

      //   uploadedFiles.push({
      //     objectKey,
      //     fileUrl: uploadResult.Location,
      //     fileName: file.originalname,
      //   });
      //   objectKeys.push(objectKey);
      // }

      // =============================================================================

      // =============================================================================
      // AZURE LOGIC
      // =============================================================================

      const containerName = AzureNamespaceService.getEmployerContainer(companyId);
      const containerExists = await AzureBlobService.containerExists(
        containerName
      );

      if (!containerExists) {
        await AzureBlobService.createContainer(containerName);
      }

      const uploadedDocuments = [];
      const blobNames = [];

      for (const file of request.files) {
        const fileExtension = file.originalname.split('.').pop() || '';
        const fileNameWithoutExtension = file.originalname.replace(
          /\.[^/.]+$/,
          ''
        );
        const blobName = `${benefitId}-${Date.now()}-${uuidv4()}_____${fileNameWithoutExtension}.${fileExtension}`;

        const blobUrl = await AzureBlobService.upload(
          containerName,
          blobName,
          file.buffer,
          file.mimetype
        );

        logger.info(
          `File ${file.originalname} uploaded successfully: ${blobUrl}`
        );

        uploadedDocuments.push({
          blobName,
          fileUrl: blobUrl,
          fileName: file.originalname,
        });
        blobNames.push(blobName);
      }

      // =============================================================================

      const benefit = await BenefitModelClass.getDataById(benefitId);

      if (!benefit) {
        response.status(400).json({ error: 'Benefit not found' });
        return;
      }

      // benefit.imageS3Urls = [
      //   ...new Set([...benefit.imageS3Urls, ...objectKeys]),
      // ];

      benefit.imageS3Urls = [
        ...new Set([...benefit.imageS3Urls, ...blobNames]),
      ];

      await BenefitModelClass.updateDataByCompanyIdAndBenefitId({
        companyId,
        benefitId,
        data: { imageS3Urls: benefit.imageS3Urls },
      });

      const pineconeResponse = await axios.post(
        `${EnvService.env().CHATBOT_BASE_URL}/update-pinecone-index`,
        {
          team_id: companyId,
          // object_keys: objectKeys,
          object_keys: blobNames,
        }
      );

      response.status(200).json({
        isSuccess: true,
        // objectKeys: objectKeys,
        objectKeys: blobNames,
        pineconeResponse: pineconeResponse.data,
      });
      return;
    } catch (error) {
      logger.error('Error adding document', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private removeDocument = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { companyId } = request.body as { companyId: string };
      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }
      const { benefitId, objectKey } = request.body as {
        benefitId: string;
        objectKey: string;
      };

      if (!objectKey) {
        response.status(400).json({ error: 'Object key is required' });
        return;
      }

      // ============================================================================
      // AWS LOGIC
      // ============================================================================

      // const bucketName = `employer-${companyId}`;

      // // Check if the bucket exists
      // const exists = await AWSService.bucketExists(bucketName);
      // if (!exists) {
      //   response.status(404).json({ error: 'Bucket not found' });
      //   return;
      // }

      // // Delete the object from S3
      // await AWSService.deleteObject({
      //   Bucket: bucketName,
      //   Key: objectKey,
      // });

      // logger.info(`Object ${objectKey} deleted successfully`);

      // ============================================================================

      // ============================================================================
      // AZURE LOGIC
      // ============================================================================

      const containerName = AzureNamespaceService.getEmployerContainer(companyId);
      const containerExists = await AzureBlobService.containerExists(
        containerName
      );

      if (!containerExists) {
        response.status(404).json({ error: 'Blob Container Not Found' });
        return;
      }

      await AzureBlobService.deleteBlob(containerName, objectKey);

      logger.info(`Blob ${objectKey} deleted successfully`);

      // ===============================================================================

      // Update the benefit document
      const benefit = await BenefitModelClass.getDataById(benefitId);

      if (!benefit) {
        response.status(404).json({ error: 'Benefit not found' });
        return;
      }

      // Remove the objectKey from imageS3Urls
      const updatedImageS3Urls = benefit.imageS3Urls.filter(
        (url) => url !== objectKey
      );

      // Update the benefit with the new imageS3Urls array
      await BenefitModelClass.updateDataByCompanyIdAndBenefitId({
        companyId,
        benefitId,
        data: { imageS3Urls: updatedImageS3Urls },
      });

      // Remove the document from all groups in the company
      await GroupModelClass.removeDocumentFromAllGroups(companyId, objectKey);

      const pineconeResponse = await axios.post(
        `${EnvService.env().CHATBOT_BASE_URL}/delete-from-pinecone-index`,
        {
          team_id: companyId,
          object_keys: [objectKey],
        }
      );
      response.status(200).json({
        isSuccess: true,
        message: 'Document removed successfully',
        pineconeResponse: pineconeResponse.data,
      });
      return;
    } catch (error) {
      logger.error('Error removing document', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private getDocument = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const companyId = request.query.companyId as string;

      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }

      const objectKey = request.query.objectKey as string;

      console.log('Object Key >>');

      if (!companyId || !objectKey) {
        response
          .status(400)
          .json({ error: 'Company ID and Object Key are required' });
        return;
      }

      // ======================================================================
      // AWS LOGIC
      // ======================================================================

      // const bucketName = `employer-${companyId}`;

      // // Check if the bucket exists
      // const exists = await AWSService.bucketExists(bucketName);
      // if (!exists) {
      //   response.status(404).json({ error: 'Bucket not found' });
      //   return;
      // }

      // // Get the object from S3
      // const params: AWS.S3.GetObjectRequest = {
      //   Bucket: bucketName,
      //   Key: objectKey,
      // };

      // const fileData = await AWSService.getObject(params);

      // if (!fileData) {
      //   response.status(404).json({ error: 'File not found' });
      //   return;
      // }

      // ======================================================================

      const containerName = AzureNamespaceService.getEmployerContainer(companyId);
      const containerExists = await AzureBlobService.containerExists(
        containerName
      );

      if (!containerExists) {
        response.status(404).json({ error: 'Blob container not found' });
        return;
      }

      const blobData = await AzureBlobService.getObject(
        containerName,
        objectKey
      );

      if (!blobData) {
        response.status(404).json({ error: 'File not found' });
        return;
      }

      // ==================================================================================

      // Extract and decode the original file name from the object key
      const originalFileName = decodeURIComponent(objectKey.split('_____')[1]);

      // Determine content type
      const contentType = getContentType(objectKey);

      // Set headers
      response.setHeader('Content-Type', contentType);
      response.setHeader(
        'Content-Disposition',
        `inline; filename="${originalFileName}"`
      );

      logger.info(`File ${originalFileName} retrieved successfully`);

      // Send the file
      // response.send(fileData);
      response.send(blobData);
    } catch (error) {
      logger.error('Error retrieving document:', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private getBenefitByType = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const user_id = request.headers['user-id'] as string;
      const companyId = request.query.companyId as string;
      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }

      const type = request.query.type as string;

      const benefits = await BenefitModelClass.getDataByCompanyId({
        companyId,
      });

      const user = await UserModelClass.getDataById(user_id);

      // Check if user has groupIds and it's not empty
      if (!user?.groupIds?.length) {
        response
          .status(400)
          .json({ error: 'User is not associated with any group' });
        return;
      }

      const getAllDocumentsOfUser =
        await GroupModelClass.getCombinedDocumentIds(user.groupIds);

      if (!benefits) {
        response.status(400).json({ error: 'No benefits found' });
        return;
      }

      const filteredBenefits = benefits
        .filter(
          (benefit) => benefit.type === type && benefit.imageS3Urls.length > 0
        )
        .map((benefit) => ({
          // @ts-ignore
          ...(benefit.toObject() as BenefitDataInterface),
          imageS3Urls: benefit.imageS3Urls.filter((url) =>
            getAllDocumentsOfUser.includes(url)
          ),
        }))
        .filter((benefit) => benefit.imageS3Urls.length > 0); // Only keep benefits that still have accessible images

      // Get the subtypes for the specified type from BENEFIT_TYPE_SUBTYPE_MAP
      const typeObj = BENEFIT_TYPE_SUBTYPE_MAP.find(
        (item) => item.type === type
      );

      console.log('TYPE OBJ >>', typeObj);

      if (!typeObj) {
        response.status(400).json({ error: 'Invalid benefit type' });
        return;
      }

      // Sort the filtered benefits by the order of subtypes in BENEFIT_TYPE_SUBTYPE_MAP
      const sortedBenefits = typeObj.subTypes
        .map((subType) => {
          return filteredBenefits.filter((benefit) => {
            return benefit.subType === subType;
          });
        })
        .flat();


      response.status(200).json({ benefits: sortedBenefits });
      return;
    } catch (error) {
      logger.error('Error viewing benefits by type', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private addLink = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { companyId } = request.body as { companyId: string };
      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }
      const { benefitId, urls } = request.body as {
        benefitId: string;
        urls: string | string[];
      };
      if (!urls) {
        response.status(400).json({ error: 'URLs required' });
        return;
      }
      const benefit = await BenefitModelClass.getDataById(benefitId);

      if (!benefit) {
        response.status(400).json({ error: 'Benefit not found' });
        return;
      }

      if (!benefit.links) {
        benefit.links = [];
      }

      const urlsToAdd = Array.isArray(urls) ? urls : [urls];

      // Filter out any duplicate URLs
      const newLinks = urlsToAdd.filter((url) => !benefit.links.includes(url));

      benefit.links.push(...newLinks);

      await BenefitModelClass.updateDataByCompanyIdAndBenefitId({
        companyId,
        benefitId,
        data: { links: benefit.links },
      });

      response.status(200).json({ isSuccess: true });
      return;
    } catch (error) {
      logger.error('Error adding link', error);
      response.status(500).json({ error: 'Internal server error' });
    }
  };

  private removeLink = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { companyId } = request.body as { companyId: string };

      if (!companyId) {
        response.status(400).json({ error: 'Company ID is required' });
        return;
      }

      const { benefitId, urls } = request.body as {
        benefitId: string;
        urls: string | string[];
      };

      if (!urls) {
        response.status(400).json({ error: 'URL(s) required' });
        return;
      }

      const benefit = await BenefitModelClass.getDataById(benefitId);

      if (!benefit) {
        response.status(400).json({ error: 'Benefit not found' });
        return;
      }

      if (!benefit.links || benefit.links.length === 0) {
        response.status(400).json({ error: 'No links to delete' });
        return;
      }

      // Convert urls to an array if it's a string
      const urlsToRemove = Array.isArray(urls) ? urls : [urls];

      // Filter out the URLs to be removed
      const updatedLinks = benefit.links.filter(
        (link) => !urlsToRemove.includes(link)
      );

      if (updatedLinks.length === benefit.links.length) {
        response
          .status(400)
          .json({ error: 'Specified URL(s) not found in benefit links' });
        return;
      }

      await BenefitModelClass.updateDataByCompanyIdAndBenefitId({
        companyId,
        benefitId,
        data: { links: updatedLinks },
      });

      response.status(200).json({ isSuccess: true });
      return;
    } catch (error) {
      logger.error('Error removing link', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };
}

export default EmployerController;
