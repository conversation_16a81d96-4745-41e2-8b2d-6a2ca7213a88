import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { UserProfile } from './BenefitsEnrollmentBot';
import { Heart, Shield, CheckCircle, X, Plus } from 'lucide-react';

interface AdditionalRecommendationsProps {
  userProfile: UserProfile;
  onComplete: (additionalPlans: any) => void;
}

export const AdditionalRecommendations = ({ userProfile, onComplete }: AdditionalRecommendationsProps) => {
  const [selectedPetInsurance, setSelectedPetInsurance] = useState<any>(null);
  const [selectedHospitalIndemnity, setSelectedHospitalIndemnity] = useState<any>(null);

  const petInsurancePlans = [
    {
      name: "MetLife Pet Plan",
      cost: 18.50,
      features: [
        "Up to 90% reimbursement for vet bills",
        "No age limits for enrollment",
        "Coverage for accidents & illness",
        "Optional wellness add-on available"
      ]
    },
    {
      name: "ASPCA Pet Health Plan",
      cost: 22.00,
      features: [
        "Comprehensive accident & illness coverage",
        "Hereditary condition coverage",
        "24/7 pet helpline",
        "No breed restrictions"
      ]
    }
  ];

  const hospitalIndemnityPlans = [
    {
      name: "Hospital Cash Plan",
      cost: 8.75,
      features: [
        "Cash benefit for hospital stays",
        "No network restrictions",
        "Use funds however you need",
        "Covers unexpected medical expenses"
      ]
    },
    {
      name: "Critical Illness Plan",
      cost: 12.50,
      features: [
        "Lump sum for critical illness diagnosis",
        "Cancer, heart attack, stroke coverage",
        "Help with out-of-pocket expenses",
        "Peace of mind protection"
      ]
    }
  ];

  const hospitalIndemnityBenefits = [
    "Cash payments directly to you for hospital stays",
    "Helps cover deductibles and out-of-pocket costs",
    "No network restrictions - use any hospital"
  ];

  const handlePetPlanSelect = (plan: any) => {
    setSelectedPetInsurance(plan);
    // Auto-advance if both selections are made
    if (selectedHospitalIndemnity) {
      setTimeout(() => {
        onComplete({
          selectedPetInsurance: plan,
          selectedHospitalIndemnity: selectedHospitalIndemnity
        });
      }, 500);
    }
  };

  const handleHospitalPlanSelect = (plan: any) => {
    setSelectedHospitalIndemnity(plan);
    // Auto-advance if both selections are made
    if (selectedPetInsurance) {
      setTimeout(() => {
        onComplete({
          selectedPetInsurance: selectedPetInsurance,
          selectedHospitalIndemnity: plan
        });
      }, 500);
    }
  };

  const handleSkipPet = () => {
    const skipPlan = { name: 'No Pet Insurance', cost: 0 };
    setSelectedPetInsurance(skipPlan);
    // Auto-advance if hospital selection is also made
    if (selectedHospitalIndemnity) {
      setTimeout(() => {
        onComplete({
          selectedPetInsurance: skipPlan,
          selectedHospitalIndemnity: selectedHospitalIndemnity
        });
      }, 500);
    }
  };

  const handleSkipHospital = () => {
    const skipPlan = { name: 'No Hospital Indemnity', cost: 0 };
    setSelectedHospitalIndemnity(skipPlan);
    // Auto-advance if pet selection is also made
    if (selectedPetInsurance) {
      setTimeout(() => {
        onComplete({
          selectedPetInsurance: selectedPetInsurance,
          selectedHospitalIndemnity: skipPlan
        });
      }, 500);
    }
  };

  const handleContinue = () => {
    onComplete({
      selectedPetInsurance: selectedPetInsurance || { name: 'No Pet Insurance', cost: 0 },
      selectedHospitalIndemnity: selectedHospitalIndemnity || { name: 'No Hospital Indemnity', cost: 0 }
    });
  };

  const isComplete = selectedPetInsurance && selectedHospitalIndemnity;

  return (
    <div className="space-y-6">
      {/* Pet Insurance Recommendation */}
      <Card className="border-2 border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="w-5 h-5 text-orange-500" />
            🐕 Are you a pet owner?
            <Badge className="bg-orange-500 text-white">Recommended</Badge>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Pet insurance can help you manage unexpected veterinary costs and ensure your furry family members get the care they need.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-3">
            {petInsurancePlans.map((plan, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedPetInsurance?.name === plan.name
                    ? 'border-orange-500 bg-orange-100 dark:bg-orange-900'
                    : 'border-gray-200 bg-white dark:bg-gray-800 hover:border-orange-300'
                }`}
                onClick={() => handlePetPlanSelect(plan)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{plan.name}</h4>
                  <Badge variant="secondary">${plan.cost}/paycheck</Badge>
                </div>
                <ul className="space-y-1">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleSkipPet} className="flex-1">
              <X className="w-4 h-4 mr-2" />
              Skip Pet Insurance
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Hospital Indemnity Recommendation */}
      <Card className="border-2 border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-500" />
            🏥 Want extra financial protection?
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Hospital indemnity insurance provides cash benefits to help with unexpected medical expenses.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg">
            <p className="font-medium text-sm mb-2">Top 3 Benefits:</p>
            <ul className="space-y-1">
              {hospitalIndemnityBenefits.map((benefit, index) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <Plus className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="grid gap-3">
            {hospitalIndemnityPlans.map((plan, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedHospitalIndemnity?.name === plan.name
                    ? 'border-blue-500 bg-blue-100 dark:bg-blue-900'
                    : 'border-gray-200 bg-white dark:bg-gray-800 hover:border-blue-300'
                }`}
                onClick={() => handleHospitalPlanSelect(plan)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{plan.name}</h4>
                  <Badge variant="secondary">${plan.cost}/paycheck</Badge>
                </div>
                <ul className="space-y-1">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleSkipHospital} className="flex-1">
              <X className="w-4 h-4 mr-2" />
              Skip Hospital Indemnity
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Continue Button - Only show if both selections made but auto-advance didn't trigger */}
      {isComplete && (
        <>
          <Separator />
          <div className="flex justify-center">
            <Button onClick={handleContinue} size="lg" className="w-full max-w-md">
              Continue to Summary
            </Button>
          </div>
        </>
      )}
    </div>
  );
};
