"use client";

import { useEffect, useState } from "react";
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from "@mui/material";
import ProtectedRoute from "@/components/ProtectedRoute";
import { getRequest } from "@/APILayer/axios_helper";

const DownloadUsers = () => {
  const [users, setUsers] = useState([]);

  async function downloadJSON() {
    const response = await getRequest("/users?apiKey=24$FrostySnow");
    console.log(response);
    setUsers(response.users);
  }

  useEffect(() => {
    downloadJSON();
  }, []);

  return (
    <ProtectedRoute>
      <Box
        sx={{
          bgcolor: "#F5F6F8",
          height: "95vh",
          padding: "32px",
          overflow: "auto",
        }}
      >
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Is Broker</TableCell>
                <TableCell>Is Admin</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user, index) => (
                <TableRow key={index}>
                  <TableCell>{(user as any).name}</TableCell>
                  <TableCell>{(user as any).email}</TableCell>
                  <TableCell>{(user as any).isBroker ? "Yes" : "No"}</TableCell>
                  <TableCell>{(user as any).isAdmin ? "Yes" : "No"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </ProtectedRoute>
  );
};

export default DownloadUsers;
