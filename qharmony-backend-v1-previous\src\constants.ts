export const PROD = 'PROD';
export const ASSIGNEE = 'ASSIGNEE';
export const ASSIGNER = 'ASSIGNER';
export const CREATOR = 'CREATOR';

export const INTERNAL_SERVER_ERROR = 'Internal server error';
export const BENEFIT_TYPE_SUBTYPE_MAP = [
  {
    type: 'Your Health',
    subTypes: [
      'Medical',
      'Dental',
      'Vision',
      'Wellness',
      'Employee Assistance Program',
      'Gym Membership',
    ],
  },
  {
    type: 'Income Security',
    subTypes: ['Life', 'Short Term Disability', 'Long Term Disability'],
  },
  {
    type: 'Your Money',
    subTypes: [
      'Pay & Bonus',
      'Stock Options',
      'Health Savings Account',
      'Flexible Savings Accounts',
      'Technology Stipend',
      'Commuter Benefits',
    ],
  },
  {
    type: 'Your Time Away',
    subTypes: [
      'Paid Time Off (PTO)',
      'Parental Leave',
      'Family and Medical Leave',
      'Paid Volunteer Time',
    ],
  },
  {
    type: 'Your Family',
    subTypes: [
      'On-site Child Care',
      'Student Loan Assistance',
      'Pet Insurance',
    ],
  },
  {
    type: 'Your Career',
    subTypes: [
      'Employee Training & Development',
      'Tuition Reimbursement',
      'Employee Recognition',
      'Performance Goals & Process',
    ],
  },
  {
    type: 'Work Policies',
    subTypes: [
      'Pet-friendly Workplace',
      'Ergonomic Workplace',
      'Company Handbook',
    ],
  },
  {
    type: 'Life Events',
    subTypes: [
      'Marriage or Divorce',
      'New Baby or Adoption',
      'Loss of Insurance',
    ],
  },
];
