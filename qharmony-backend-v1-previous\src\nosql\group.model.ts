import mongoose, { Document, Model, UpdateWriteOpResult } from 'mongoose';
import logger from '../utils/logger';

const { Schema } = mongoose;

/**
 * Interface representing a group.
 */
export interface GroupDataInterface {
  _id?: mongoose.Types.ObjectId;
  companyId: string;
  name: string;
  document_ids: string[];
  employee_ids: string[];
}

/**
 * Mongoose document interface (excluding _id conflict).
 */
interface GroupDocument extends Document, Omit<GroupDataInterface, '_id'> { }

class GroupModelClass {
  private static groupModel: Model<GroupDocument>;

  /**
   * Initializes the Mongoose model.
   */
  public static initializeModel() {
    const schema = new Schema({
      companyId: { type: String, required: true },
      name: { type: String, required: true },
      document_ids: { type: [String], default: [] },
      employee_ids: { type: [String], default: [] },
    });

    this.groupModel = mongoose.model<GroupDocument>('Group', schema);
  }

  /**
   * Creates a new group for a given company.
   * @param companyId - The ID of the company.
   * @param name - The name of the group.
   * @returns The created group's ID as a string, or null if an error occurs.
   */
  public static async createGroup(companyId: string, name: string): Promise<string | null> {
    try {
      const group = await this.groupModel.create({ companyId, name });
      return group._id.toString();
    } catch (error) {
      logger.error("Error creating group:", error);
      return null;
    }
  }

  /**
   * Retrieves all groups for a given company ID.
   * @param companyId - The ID of the company.
   * @returns An array of groups belonging to the company.
   */
  public static async getGroupsByCompanyId(companyId: string): Promise<GroupDataInterface[]> {
    try {
      const groups = await this.groupModel.find({ companyId }).lean();
      // Use unknown as an intermediate type to fix the TypeScript error
      return groups as unknown as GroupDataInterface[];
    } catch (error) {
      logger.error("Error fetching groups by companyId:", error);
      return [];
    }
  }

  /**
   * Retrieves all groups for the given array of company IDs.
   * @param companyIds - Array of company IDs to fetch groups for
   * @returns An array of groups belonging to the specified companies
   */
  public static async getAllGroupsByCompanyId(companyIds: string[]): Promise<GroupDataInterface[]> {
    try {
      const groups = await this.groupModel.find({
        companyId: { $in: companyIds }
      }).lean();
      // Use unknown as an intermediate type to fix the TypeScript error
      return groups as unknown as GroupDataInterface[];
    } catch (error) {
      logger.error("Error fetching groups by companyIds:", error);
      return [];
    }
  }

  /**
   * Retrieves a group by its unique _id.
   * @param groupId - The unique ID of the group.
   * @returns The group details if found, otherwise null.
   */
  public static async getGroupById(groupId: string): Promise<GroupDataInterface | null> {
    try {
      const group = await this.groupModel.findById(groupId).lean();
      // Use unknown as an intermediate type to fix the TypeScript error
      return group as unknown as GroupDataInterface | null;
    } catch (error) {
      logger.error(`Error fetching group with _id: ${groupId}`, error);
      return null;
    }
  }

  /**
   * Updates the document_ids and employee_ids of a group by groupId.
   * @param groupId - The unique ID of the group to be updated.
   * @param documentIds - The new array of document IDs to set.
   * @param employeeIds - The new array of employee IDs to set.
   * @returns The update result.
   */
  public static async updateGroupDetails(
    groupId: string,
    documentIds: string[],
    employeeIds: string[]
  ): Promise<UpdateWriteOpResult | null> {
    try {
      const updateResult = await this.groupModel.updateOne(
        { _id: groupId },
        { $set: { document_ids: documentIds, employee_ids: employeeIds } }
      );

      return updateResult;
    } catch (error) {
      logger.error(`Error updating group details for groupId: ${groupId}`, error);
      return null;
    }
  }

  /**
   * Retrieves the combined document_ids array for a given list of group IDs.
   * @param groupIds - Array of group IDs.
   * @returns An array containing all unique document IDs from the specified groups.
   */
  public static async getCombinedDocumentIds(groupIds: string[]): Promise<string[]> {
    try {
      const groups = await this.groupModel.find({ _id: { $in: groupIds } }).lean();
      const combinedDocumentIds = Array.from(new Set(groups.flatMap(group => group.document_ids)));
      return combinedDocumentIds;
    } catch (error) {
      logger.error("Error fetching combined document IDs:", error);
      return [];
    }
  }

  /**
   * Removes a document ID from all groups within a company.
   * @param companyId - The ID of the company.
   * @param documentId - The document ID to remove.
   * @returns The update result indicating the number of modified documents.
   */
  public static async removeDocumentFromAllGroups(
    companyId: string,
    documentId: string
  ): Promise<UpdateWriteOpResult | null> {
    try {
      const updateResult = await this.groupModel.updateMany(
        { companyId },
        { $pull: { document_ids: documentId } }
      );
      return updateResult;
    } catch (error) {
      logger.error(`Error removing document ${documentId} from groups in company ${companyId}:`, error);
      return null;
    }
  }

  /**
   * Retrieves groups by their IDs.
   * @param groupIds - Array of group IDs to fetch.
   * @returns An array of groups matching the provided IDs.
   */
  public static async getGroupsByIds(groupIds: string[]): Promise<GroupDataInterface[]> {
    try {
      const groups = await this.groupModel.find({
        _id: { $in: groupIds }
      }).lean();
      // Use unknown as an intermediate type to fix the TypeScript error
      return groups as unknown as GroupDataInterface[];
    } catch (error) {
      logger.error("Error fetching groups by IDs:", error);
      return [];
    }
  }
}

// Initialize the model
GroupModelClass.initializeModel();

export default GroupModelClass;
