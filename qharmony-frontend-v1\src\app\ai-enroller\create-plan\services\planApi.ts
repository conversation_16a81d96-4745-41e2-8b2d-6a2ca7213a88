import { getApiBaseUrl, getUserId } from '../../../../utils/env';

// API service for plan creation and management
const API_BASE_URL = getApiBaseUrl();

export interface PlanData {
  planName: string;
  planCode: string;
  coverageType: string;
  coverageSubTypes: string[];
  planType: string;
  metalTier: string;
  description: string;
  highlights: string[];
  informativeLinks: string[];
  carrierId: string;
  carrierPlanId?: string;
  groupNumber?: string;
  isTemplate?: boolean;
  status?: string;
}

export interface CarrierData {
  _id: string;
  carrierName: string;
  carrierCode: string;
  displayName?: string;
  brokerId?: string;
  brokerageId?: string;
  isSystemCarrier: boolean;
  contactInfo: {
    phone?: string;
    email?: string;
    website?: string;
    supportEmail?: string;
    claimsPhone?: string;
    memberServicesPhone?: string;
  };
  supportedPlanTypes: string[];
  supportedCoverageTypes: string[];
  supportedCoverageSubTypes: string[];
  integration: {
    ediCapable: boolean;
    apiEndpoint?: string;
    apiVersion?: string;
    authMethod?: string;
    dataFormat?: string;
  };
  licenseStates: string[];
  amRating?: string;
  networkName?: string;
  status: string;
  isActive: boolean;
  isActivated: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Types
export interface PlanData {
  planName: string;
  planCode: string;
  carrier: string;
  planType: string;
  coverageType: string;
  coverageSubTypes: string[];
  metalTier: string;
  videoUrl?: string;
  description: string;
  highlights: string[];
  informativeLinks: string[];
  carrierId: string;
  isTemplate?: boolean;
  status?: string;
  documents?: File[];
}

export interface Plan {
  _id: string;
  planName: string;
  planCode: string;
  carrier: string;
  carrierId?: string;
  planType: string;
  coverageType: string;
  coverageSubTypes: string[];
  metalTier: string;
  description?: string;
  highlights?: string[];
  informativeLinks?: string[];
  videoUrl?: string;
  isEditable?: boolean;
  isDeletable?: boolean;
  status?: string;
  isActivated?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  isTemplate?: boolean;
  brokerId?: string;
  carrierData?: {
    _id: string;
    carrierName: string;
    carrierCode: string;
    amBestRating?: string;
    financialStrength?: string;
    supportedCoverageTypes: string[];
    integrationCapabilities: string[];
    contactInfo: {
      phone?: string;
      email?: string;
      website?: string;
    };
    status: string;
    isActive: boolean;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Note: getUserId is now imported from utils/env.ts

// Common headers for API requests
const getHeaders = () => ({
  'Content-Type': 'application/json',
  'user-id': getUserId(),
});

// Get constants data (using fallback data that matches backend constants.ts)
export const getConstantsData = (): ApiResponse<{
  planTypes: string[];
  coverageCategories: string[];
  coverageMap: { [key: string]: string[] };
  metalTiers: string[];
}> => {
  // PRE_ENROLLMENT_COVERAGE_MAP from backend constants
  const preEnrollmentCoverageMap = {
    'Health Insurance': ['Medical'],
    'Ancillary Benefits': ['Dental', 'Vision'],
    'Life & Disability Insurance': [
      'Term Life',
      'Supplemental Life Insurance',
      'Short-Term Disability',
      'Long-Term Disability',
      'Whole Life',
      'Group (Employer) Life',
      'Accidental Death & Dismemberment (AD&D)'
    ],
    'Voluntary Benefits': [
      'Hospital Indemnity',
      'Accident Insurance',
      'Critical Illness Insurance',
      'Cancer Insurance',
      'Gap Insurance',
      'Legal Insurance',
      'Identity Theft Protection',
      'Accident & Illness (Pets)',
      'Nursing Care / Custodial Care'
    ],
    'Wellness & Mental Health': [
      'Wellness Programs',
      'Employee Assistance Program',
      'Gym Membership'
    ],
    'Spending & Savings Accounts': [
      'Health Savings Account',
      'Flexible Savings Accounts',
      'Commuter Benefits',
      'Technology Stipend'
    ],
    'Financial Benefits': [
      'Pay & Bonus',
      'Stock Options',
      'Student Loan Assistance'
    ],
    'Retirement Benefits': [
      '401(k)',
      '403(b)',
      'Pension Plan'
    ],
    'Time Off & Leave': [
      'Paid Time Off (PTO)',
      'Parental Leave',
      'Family and Medical Leave',
      'Paid Volunteer Time'
    ],
    'Family & Caregiver Support': ['On-site Child Care'],
    'Career & Development': [
      'Employee Training & Development',
      'Tuition Reimbursement',
      'Employee Recognition',
      'Performance Goals & Process'
    ],
    'Workplace Environment': [
      'Pet-friendly Workplace',
      'Ergonomic Workplace',
      'Company Handbook'
    ],
    'Life Events': [
      'Marriage or Divorce',
      'New Baby or Adoption',
      'Loss of Insurance'
    ]
  };

  return {
    success: true,
    data: {
      // Hardcoded plan types from backend constants
      planTypes: ['PPO', 'HMO', 'HDHP', 'MEC', 'EPO', 'POS', 'Indemnity', 'Term Life', 'Whole Life', 'STD', 'LTD'],
      // Coverage categories (main types)
      coverageCategories: Object.keys(preEnrollmentCoverageMap),
      // Coverage map (category -> subtypes)
      coverageMap: preEnrollmentCoverageMap,
      // Hardcoded metal tiers from backend constants
      metalTiers: ['Bronze', 'Silver', 'Gold', 'Platinum']
    }
  };
};

// Get assignable carriers
export const getCarriers = async (): Promise<ApiResponse<CarrierData[]>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/carriers/assignable`, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      const errorText = await response.text();

      // If the user-specific call fails, try to get system carriers only
      try {
        const fallbackResponse = await fetch(`${API_BASE_URL}/api/pre-enrollment/carriers?isSystemCarrier=true`, {
          method: 'GET',
          headers: getHeaders(),
        });

        if (fallbackResponse.ok) {
          const fallbackResult = await fallbackResponse.json();
          return {
            success: true,
            data: fallbackResult.carriers || []
          };
        }
      } catch (fallbackError) {
        // Fallback failed
      }

      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const result = await response.json();

    return {
      success: true,
      data: result.carriers || []
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch carriers'
    };
  }
};

// Debug function to test different user IDs (removed for production)

// Check if plan can be edited
export const checkPlanEditable = async (planId: string): Promise<ApiResponse<{ canEdit: boolean; reason?: string }>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/can-edit`, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check plan editability'
    };
  }
};

// Check if plan can be deleted
export const checkPlanDeletable = async (planId: string): Promise<ApiResponse<{ canDelete: boolean; reason?: string }>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/can-delete`, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check plan deletability'
    };
  }
};

// Get plan dependencies
export const getPlanDependencies = async (planId: string): Promise<ApiResponse<{ dependencies: string[] }>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/dependent-plans`, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get plan dependencies'
    };
  }
};

// Delete plan
export const deletePlan = async (planId: string): Promise<ApiResponse<any>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
      method: 'DELETE',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete plan'
    };
  }
};

// Create a new plan
export const createPlan = async (planData: PlanData): Promise<ApiResponse<any>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(planData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create plan'
    };
  }
};

// Get all plans with pagination support
export const getPlans = async (filters?: {
  status?: string;
  coverageType?: string;
  planType?: string;
  isTemplate?: boolean;
  planName?: string;
  planCode?: string;
  strict?: boolean;
  includeCarrierData?: boolean;
  page?: number;
  limit?: number;
}): Promise<ApiResponse<{
  plans: Plan[];
  count?: number;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalPlans: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}>> => {
  try {
    const queryParams = new URLSearchParams();
    if (filters) {
      console.log('🔧 getPlans called with filters:', filters);
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const url = `${API_BASE_URL}/api/pre-enrollment/plans${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log('🌐 Fetching plans from URL:', url);
    console.log('🔑 Headers:', getHeaders());

    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      console.log('❌ getPlans HTTP error:', response.status, response.statusText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('📦 getPlans response:', result);
    console.log('📊 Plans returned:', result.plans?.length || 0);

    return {
      success: true,
      data: {
        plans: result.plans || [],
        count: result.count,
        pagination: result.pagination
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch plans'
    };
  }
};

// Get plan by ID
export const getPlanById = async (planId: string): Promise<ApiResponse<any>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.plan
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch plan'
    };
  }
};

// Update plan
export const updatePlan = async (planId: string, planData: Partial<PlanData>): Promise<ApiResponse<any>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(planData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update plan'
    };
  }
};

// Activate plan (convert from Template to Active)
export const activatePlan = async (planId: string): Promise<ApiResponse<any>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/activate`, {
      method: 'POST',
      headers: getHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to activate plan'
    };
  }
};

// Upload documents for a plan
export const uploadPlanDocuments = async (planId: string, files: File[]): Promise<ApiResponse<any>> => {
  try {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('documents', file);
    });

    const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}/documents`, {
      method: 'POST',
      headers: {
        'user-id': getUserId(),
        // Don't set Content-Type for FormData, let browser set it with boundary
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload documents'
    };
  }
};

// Check for duplicate plan name using backend filter
export const checkPlanNameDuplicate = async (planName: string, excludeId?: string): Promise<ApiResponse<{ isDuplicate: boolean; existingPlan?: Plan }>> => {
  try {
    // Use backend filter to check for exact plan name match
    console.log('🔍 Checking for duplicate plan name:', planName, excludeId ? `(excluding ${excludeId})` : '');
    const plansResult = await getPlans({ planName: planName, strict: true });
    console.log('📊 Plan name duplicate check result:', plansResult);
    console.log('📊 Number of plans returned:', plansResult.data?.plans?.length || 0);

    if (!plansResult.success) {
      console.log('❌ Plan name check failed:', plansResult.error);
      return {
        success: false,
        error: plansResult.error || 'Failed to fetch plans for duplicate check'
      };
    }

    // Filter out the current plan if excludeId is provided
    const plans = plansResult.data?.plans || [];
    const filteredPlans = excludeId
      ? plans.filter((plan: Plan) => plan._id !== excludeId)
      : plans;

    const duplicatePlan = filteredPlans.length > 0 ? filteredPlans[0] : undefined;
    console.log('🎯 Duplicate plan found:', duplicatePlan ? `YES - ${duplicatePlan.planName}` : 'NO', excludeId ? `(excluded ${excludeId})` : '');

    return {
      success: true,
      data: {
        isDuplicate: !!duplicatePlan,
        existingPlan: duplicatePlan
      }
    };
  } catch (error) {
    console.log('💥 Plan name check error:', error);
    return {
      success: false,
      error: 'Failed to check for duplicate plan name'
    };
  }
};

// Check for duplicate plan code using backend filter
export const checkPlanCodeDuplicate = async (planCode: string, excludeId?: string): Promise<ApiResponse<{ isDuplicate: boolean; existingPlan?: Plan }>> => {
  try {
    // Use backend filter to check for exact plan code match
    console.log('🔍 Checking for duplicate plan code:', planCode, excludeId ? `(excluding ${excludeId})` : '');
    const plansResult = await getPlans({ planCode: planCode, strict: true });
    console.log('📊 Plan code duplicate check result:', plansResult);
    console.log('📊 Number of plans returned:', plansResult.data?.plans?.length || 0);

    if (!plansResult.success) {
      console.log('❌ Plan code check failed:', plansResult.error);
      return {
        success: false,
        error: plansResult.error || 'Failed to fetch plans for duplicate check'
      };
    }

    // Filter out the current plan if excludeId is provided
    const plans = plansResult.data?.plans || [];
    const filteredPlans = excludeId
      ? plans.filter((plan: Plan) => plan._id !== excludeId)
      : plans;

    const duplicatePlan = filteredPlans.length > 0 ? filteredPlans[0] : undefined;
    console.log('🎯 Duplicate plan found:', duplicatePlan ? `YES - ${duplicatePlan.planCode}` : 'NO', excludeId ? `(excluded ${excludeId})` : '');

    return {
      success: true,
      data: {
        isDuplicate: !!duplicatePlan,
        existingPlan: duplicatePlan
      }
    };
  } catch (error) {
    console.log('💥 Plan code check error:', error);
    return {
      success: false,
      error: 'Failed to check for duplicate plan code'
    };
  }
};

