// mongoose.service.ts

import mongoose from 'mongoose';
import EnvService from './env.service';
import AbstractService from './service';

class MongooseService extends AbstractService {
  private MONGO_URI: string | null = null;
  private MONGO_DB_NAME: string | null = null;

  constructor() {
    super();
    // Environment variables will be loaded when connect() is called
  }

  static async init(): Promise<void> {
    const mongooseService = new MongooseService();
    await mongooseService.connect();
  }

  private initializeEnvVariables(): void {
    if (!this.MONGO_URI || !this.MONGO_DB_NAME) {
      const env = EnvService.env();
      this.MONGO_URI = env.MONGO_URI;
      this.MONGO_DB_NAME = env.MONGO_DB_NAME;
    }
  }

  private async connect(): Promise<void> {
    try {
      // Initialize environment variables when actually needed
      this.initializeEnvVariables();

      await mongoose.connect(this.MONGO_URI!, {
        dbName: this.MONGO_DB_NAME!,
        serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
        connectTimeoutMS: 10000,
      });

      console.log('✅ Connected to MongoDB, ENV:', this.MONGO_DB_NAME!);

      // Set up connection event handlers after successful connection
      this.setupConnectionEventHandlers();

    } catch (err) {
      console.error('❌ Error connecting to MongoDB:', err);
      console.log('🔄 Server will continue running without MongoDB connection');
      console.log('📝 Note: API endpoints requiring database will not work until connection is restored');

      // Re-throw the error so the caller knows the connection failed
      throw err;
    }
  }

  private setupConnectionEventHandlers(): void {
    // Handle connection events
    mongoose.connection.on('error', (err) => {
      console.error('❌ MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      console.log('🔌 MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      console.log('✅ MongoDB reconnected');
    });
  }
}

export default MongooseService;
