/**
 * Test the REAL implementation to see if it's actually incomplete
 * This will import the actual CostCalculationService and test it
 */

// Import the real service from compiled dist
const CostCalculationService = require('../dist/services/costCalculationService.js').default;

// Test data that matches the real interfaces
const testPlanAssignmentSalaryBased = {
  rateStructure: 'Salary-Based',
  salaryBasedRates: [
    { salaryMin: 30000, salaryMax: 50000, rate: 0.8, type: 'multiplier' },
    { salaryMin: 50001, salaryMax: 80000, rate: 1.0, type: 'multiplier' },
    { salaryMin: 80001, salaryMax: 120000, rate: 1.3, type: 'multiplier' }
  ],
  coverageTiers: [
    {
      tierName: 'Family',
      totalCost: 1000.00,
      employeeCost: 200.00,
      employerCost: 800.00
    }
  ],
  employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
  employeeContribution: { contributionType: 'Remainder', contributionAmount: 0 }
};

const testPlanAssignmentSalaryBasedFixed = {
  rateStructure: 'Salary-Based',
  salaryBasedRates: [
    { salaryMin: 30000, salaryMax: 50000, rate: 100, type: 'fixed' },
    { salaryMin: 50001, salaryMax: 80000, rate: 200, type: 'fixed' },
    { salaryMin: 80001, salaryMax: 120000, rate: 300, type: 'fixed' }
  ],
  coverageTiers: [
    {
      tierName: 'Family',
      totalCost: 1000.00,
      employeeCost: 200.00,
      employerCost: 800.00
    }
  ],
  employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
  employeeContribution: { contributionType: 'Remainder', contributionAmount: 0 }
};

const testPlanAssignmentSalaryPercentage = {
  rateStructure: 'Salary-Based',
  salaryPercentage: 2.5, // 2.5% of annual salary
  coverageTiers: [
    {
      tierName: 'Family',
      totalCost: 1000.00,
      employeeCost: 200.00,
      employerCost: 800.00
    }
  ],
  employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
  employeeContribution: { contributionType: 'Remainder', contributionAmount: 0 }
};

function testRealImplementation() {
  console.log('🧪 TESTING REAL COSTCALCULATIONSERVICE IMPLEMENTATION');
  console.log('====================================================');

  let allPassed = true;

  // Test 1: Salary-Based Multiplier
  console.log('\n--- Test 1: Salary-Based Multiplier ---');
  try {
    const result1 = CostCalculationService.calculateEnrollmentCost({
      planAssignment: testPlanAssignmentSalaryBased,
      employeeAge: 35,
      selectedTier: 'Family',
      employeeSalary: 90000 // Should hit 1.3x multiplier
    });

    console.log('Input:', {
      rateStructure: 'Salary-Based',
      salaryBand: '80001-120000 (1.3x multiplier)',
      baseCost: 1000,
      expectedTotal: 1300
    });

    console.log('Result:', result1);

    if (result1.success && result1.cost.totalAmount === 1300) {
      console.log('✅ PASSED');
    } else {
      console.log('❌ FAILED');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ ERROR:', error.message);
    allPassed = false;
  }

  // Test 2: Salary-Based Fixed
  console.log('\n--- Test 2: Salary-Based Fixed ---');
  try {
    const result2 = CostCalculationService.calculateEnrollmentCost({
      planAssignment: testPlanAssignmentSalaryBasedFixed,
      employeeAge: 35,
      selectedTier: 'Family',
      employeeSalary: 90000 // Should hit +$300 fixed
    });

    console.log('Input:', {
      rateStructure: 'Salary-Based',
      salaryBand: '80001-120000 (+$300 fixed)',
      baseCost: 1000,
      expectedTotal: 1300
    });

    console.log('Result:', result2);

    if (result2.success && result2.cost.totalAmount === 1300) {
      console.log('✅ PASSED');
    } else {
      console.log('❌ FAILED');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ ERROR:', error.message);
    allPassed = false;
  }

  // Test 3: Salary Percentage
  console.log('\n--- Test 3: Salary Percentage ---');
  try {
    const result3 = CostCalculationService.calculateEnrollmentCost({
      planAssignment: testPlanAssignmentSalaryPercentage,
      employeeAge: 35,
      selectedTier: 'Family',
      employeeSalary: 60000 // 2.5% = $1500/year = $125/month
    });

    console.log('Input:', {
      rateStructure: 'Salary-Based',
      salaryPercentage: '2.5%',
      salary: 60000,
      expectedMonthly: 125
    });

    console.log('Result:', result3);

    if (result3.success && result3.cost.totalAmount === 125) {
      console.log('✅ PASSED');
    } else {
      console.log('❌ FAILED');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ ERROR:', error.message);
    allPassed = false;
  }

  // Test 4: Missing Salary (Error Handling)
  console.log('\n--- Test 4: Missing Salary (Error Handling) ---');
  try {
    const result4 = CostCalculationService.calculateEnrollmentCost({
      planAssignment: testPlanAssignmentSalaryBased,
      employeeAge: 35,
      selectedTier: 'Family'
      // No employeeSalary - should fallback to tier cost
    });

    console.log('Input:', {
      rateStructure: 'Salary-Based',
      employeeSalary: 'undefined',
      expectedFallback: 1000
    });

    console.log('Result:', result4);

    if (result4.success && result4.cost.totalAmount === 1000) {
      console.log('✅ PASSED (Fallback worked)');
    } else {
      console.log('❌ FAILED');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ ERROR:', error.message);
    allPassed = false;
  }

  console.log('\n📊 REAL IMPLEMENTATION TEST SUMMARY');
  console.log('===================================');
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! Implementation is complete and working.');
  } else {
    console.log('⚠️  Some tests failed. Implementation may be incomplete.');
  }

  return allPassed;
}

// Run the test
if (require.main === module) {
  testRealImplementation();
}

module.exports = { testRealImplementation };
