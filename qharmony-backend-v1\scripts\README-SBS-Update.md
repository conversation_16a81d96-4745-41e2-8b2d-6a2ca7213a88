# Southern Benefits Systems User Details Update Script

This script updates user details for Southern Benefits Systems employees from a CSV file containing employee and dependent information.

## 📋 Prerequisites

1. **Node.js** installed with required packages:
   ```bash
   npm install mongoose csv-parser
   ```

2. **MongoDB connection** - Set the `MONGO_URI` environment variable or update the script

3. **CSV file** named `Census.csv` in the same directory as the script

## 📄 CSV File Format

The CSV file must contain the following columns:

| Column | Description | Required | Example |
|--------|-------------|----------|---------|
| `First Name` | Employee/dependent first name | ✅ | "<PERSON>" |
| `Last Name` | Employee/dependent last name | ✅ | "Doe" |
| `Sex` | Gender (M/F/Male/Female) | ✅ | "M" |
| `DOB` | Date of birth (MM/DD/YYYY) | ✅ | "01/15/1985" |
| `Address 1` | Street address | ✅ | "123 Main St" |
| `Address 2` | Apartment/Suite (optional) | ❌ | "Apt 2B" |
| `City` | City | ✅ | "Greenville" |
| `State` | State abbreviation | ✅ | "SC" |
| `Zip` | ZIP code | ✅ | "29601" |
| `Relationship` | Employee/Spouse/Child/etc | ✅ | "Employee" |
| `Marital Status` | Marital status (optional) | ❌ | "Married" |

### 🔗 Relationship Values

- **Employee**: `Employee`, `Self`, or empty
- **Dependents**: `Spouse`, `Child`, `Son`, `Daughter`, `Wife`, `Husband`, `Domestic Partner`, `Stepchild`, `Adopted Child`

## 🏢 Company Information

- **Company**: Southern Benefits Systems
- **Company ID**: `67bf65bf50bad0a4b3d805ba`
- **Admin Contact**: <EMAIL>

## 👥 Current Users in Database

The script will update details for these existing users:

1. **Christina Retzer** (<EMAIL>) - Admin/Broker ✅ Activated
2. **Josh Hyman** (<EMAIL>) - Employee ✅ Activated
3. **Zach Bridgeman** (<EMAIL>) - Employee ✅ Activated
4. **Nathan Craig** (<EMAIL>) - Employee ❌ Not Activated
5. **David Dodd** (<EMAIL>) - Employee ✅ Activated
6. **Trey Elder** (<EMAIL>) - Employee ❌ Not Activated
7. **Brett Finley** (<EMAIL>) - Employee ❌ Not Activated
8. **Michael Fox** (<EMAIL>) - Employee ❌ Not Activated
9. **Lucas Gartenmayer** (<EMAIL>) - Employee ❌ Not Activated
10. **Greg Kaye** (<EMAIL>) - Employee ❌ Not Activated
11. **Sophia Kasprzycki** (<EMAIL>) - Employee ✅ Activated
12. **Josh Kintz** (<EMAIL>) - Employee ✅ Activated
13. **Susan McSweeney** (<EMAIL>) - Employee ✅ Activated
14. **Tracy Morrison** (<EMAIL>) - Employee ❌ Disabled
15. **Marie Redderson** (<EMAIL>) - Employee ❌ Not Activated
16. **Michael Tankersley** (<EMAIL>) - Employee ✅ Activated
17. **John Bradley** (<EMAIL>) - Employee ❌ Not Activated
18. **Jackie Thomas** (<EMAIL>) - Employee ❌ Not Activated
19. **Payton Vaughn** (<EMAIL>) - Employee ❌ Not Activated
20. **Charley Worley** (<EMAIL>) - Employee ✅ Activated

## 🚀 Usage

### **Quick Start (Recommended):**
```bash
cd qharmony-backend-v1/scripts
run-sbs-update.bat
```

### **Manual Steps:**

1. **Install dependencies**:
   ```bash
   cd qharmony-backend-v1/scripts
   npm install
   ```

2. **Test database connection**:
   ```bash
   npm run test-connection
   ```

3. **Validate CSV data**:
   ```bash
   npm run validate
   ```

4. **Run the update**:
   ```bash
   npm run update-sbs
   ```

## 🔧 What the Script Does

### ✅ **For Employees:**
1. **Finds matching users** by name in the database
2. **Updates missing details** only (doesn't overwrite existing data):
   - Date of birth
   - Home address
   - Mailing address (copies from home address)
3. **Preserves existing data** like phone numbers, titles, departments

### ✅ **For Dependents:**
1. **Groups dependents** with employees by matching address
2. **Creates dependent records** with:
   - Full name
   - Gender (mapped from M/F to Male/Female)
   - Date of birth
   - Relationship (mapped to schema enums)
   - Address (same as employee)
3. **Prevents duplicates** by checking existing dependents
4. **Validates data** before adding

### ✅ **Safety Features:**
- **No data overwriting** - only fills in missing information
- **Duplicate prevention** - won't add existing dependents
- **Error handling** - continues processing if individual records fail
- **Detailed logging** - shows exactly what was updated
- **Rollback safe** - MongoDB transactions ensure data integrity

## 📊 Expected Output

```
🚀 Starting Southern Benefits Systems user details update...

📡 Connecting to MongoDB...
✅ Connected to MongoDB

📄 Looking for CSV file: /path/to/scripts/Census.csv
✅ Parsed 45 rows from CSV file
📊 Found 20 employees and 25 dependents in CSV

👥 Fetching users for company: 67bf65bf50bad0a4b3d805ba
✅ Found 20 users in database

📋 Current users in database:
  1. Christina Retzer (<EMAIL>) - Activated
  2. Josh Hyman (<EMAIL>) - Activated
  ...

🔄 Processing employee updates...
📝 Processing employee: John Bradley (<EMAIL>)
  ✅ Updated details for John Bradley: DOB, Hire Date, Employee Class, Address, Mailing Address
  👨‍👩‍👧‍👦 Found 1 potential dependents
    ✅ Added dependent: Whitney Bradley (Spouse)

📝 Processing employee: Brett Finley (<EMAIL>)
  ✅ Updated details for Brett Finley: DOB, Hire Date, Employee Class, Address, Mailing Address
  👨‍👩‍👧‍👦 Found 4 potential dependents
    ✅ Added dependent: Allie Finley (Spouse)
    ✅ Added dependent: Emma-Grace Finley (Child)
    ✅ Added dependent: Reece Finley (Child)
    ✅ Added dependent: Hayden Finley (Child)

📊 Update Summary:
✅ Users updated: 19
👨‍👩‍👧‍👦 Dependents added: 15
📋 Fields updated: DOB, Hire Date, Employee Class (Full-Time), Address, Mailing Address

🎉 Script completed successfully!
```

## ⚠️ Important Notes

1. **Backup your database** before running the script
2. **Test with a small CSV** first to verify the mapping
3. **Check the sample CSV** (`Census-sample.csv`) for format reference
4. **Review the output** to ensure all updates were successful
5. **The script is idempotent** - safe to run multiple times

## 🐛 Troubleshooting

### Common Issues:

1. **"Employee not found"** - Check name spelling in CSV vs database
2. **"Invalid date format"** - Ensure dates are in MM/DD/YYYY format
3. **"CSV file not found"** - Ensure `Census.csv` is in the scripts directory
4. **"Connection failed"** - Check MongoDB connection string

### Debug Mode:

Add more logging by modifying the script:
```javascript
console.log('Debug: Processing row:', csvRow);
```

## 📞 Support

For issues or questions, contact:
- **Technical**: Development team
- **Business**: <EMAIL>
