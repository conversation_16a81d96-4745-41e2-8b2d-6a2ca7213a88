import React from 'react';
import { BotQuestion } from './BotQuestion';
import { UserProfile } from '../page';

interface DentalPlanPageProps {
  userProfile: UserProfile;
  onPlanSelect: (planData: any) => void;
}

export const DentalPlanPage = ({ userProfile, onPlanSelect }: DentalPlanPageProps) => {
  const plans = [
    {
      id: 'basic',
      name: 'Basic Dental',
      cost: 8.50,
      coverage: 'Preventive + Basic',
      features: ['100% preventive care', 'Basic procedures covered', 'Low monthly cost']
    },
    {
      id: 'comprehensive',
      name: 'Comprehensive Dental',
      cost: 15.20,
      coverage: 'Full Coverage',
      features: ['100% preventive care', 'Major procedures covered', 'Orthodontics included']
    }
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <BotQuestion 
        question="Now let's pick your dental coverage!"
        context="Dental care is important for your overall health. Choose the level that fits your needs."
      />

      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Dental Plans</h3>
        <div className="grid gap-4">
          {plans.map(plan => (
            <div key={plan.id} className="border rounded-lg p-4 hover:border-blue-300 transition-colors">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{plan.name}</h4>
                  <span className="text-sm bg-gray-100 px-2 py-1 rounded">{plan.coverage}</span>
                </div>
                <div className="text-right">
                  <p className="text-xl font-bold text-green-600">${plan.cost}/paycheck</p>
                </div>
              </div>
              <ul className="space-y-1 mb-4">
                {plan.features.map((feature, index) => (
                  <li key={index} className="text-sm">✓ {feature}</li>
                ))}
              </ul>
              <button
                onClick={() => onPlanSelect(plan)}
                className="w-full px-4 py-2 border border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition-colors"
              >
                Select Plan
              </button>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-4 border-t">
          <button
            onClick={() => onPlanSelect(null)}
            className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Skip Dental Coverage
          </button>
        </div>
      </div>
    </div>
  );
};
