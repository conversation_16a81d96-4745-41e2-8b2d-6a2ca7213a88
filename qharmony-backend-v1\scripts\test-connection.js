/**
 * Test MongoDB connection using same config as main app
 */

const mongoose = require('mongoose');
const path = require('path');

// Load environment variables from parent directory
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function testConnection() {
  try {
    console.log('🔍 Testing MongoDB connection...\n');
    
    const MONGO_URI = process.env.MONGO_URI;
    const MONGO_DB_NAME = process.env.MONGO_DB_NAME;
    
    console.log('📋 Environment Check:');
    console.log(`   MONGO_URI: ${MONGO_URI ? '✅ Found' : '❌ Missing'}`);
    console.log(`   MONGO_DB_NAME: ${MONGO_DB_NAME ? '✅ Found' : '❌ Missing'}`);
    console.log('');
    
    if (!MONGO_URI) {
      throw new Error('MONGO_URI environment variable is not set. Please check your .env file.');
    }
    
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(MONGO_URI, {
      dbName: MONGO_DB_NAME,
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    });
    
    console.log(`✅ Successfully connected to MongoDB!`);
    console.log(`📍 Database: ${mongoose.connection.db.databaseName}`);
    console.log(`📍 Host: ${mongoose.connection.host}`);
    console.log(`📍 Port: ${mongoose.connection.port}`);
    
    // Test a simple query
    console.log('\n🔍 Testing database access...');
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`✅ Found ${collections.length} collections in database`);
    
    // Check for users collection specifically
    const usersCollection = collections.find(c => c.name === 'users');
    if (usersCollection) {
      const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
      const userCount = await User.countDocuments({ companyId: '67bf65bf50bad0a4b3d805ba' });
      console.log(`✅ Found ${userCount} SBS users in database`);
    } else {
      console.log('⚠️ Users collection not found');
    }
    
    console.log('\n🎉 Connection test completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Connection test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n🔧 Troubleshooting tips:');
      console.log('   1. Make sure MongoDB is running');
      console.log('   2. Check if the MongoDB URI in .env is correct');
      console.log('   3. Verify network connectivity to the database');
      console.log('   4. If using MongoDB Atlas, check IP whitelist');
    }
    
    process.exit(1);
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('📡 Disconnected from MongoDB');
    }
  }
}

// Run the test
if (require.main === module) {
  testConnection().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = { testConnection };
