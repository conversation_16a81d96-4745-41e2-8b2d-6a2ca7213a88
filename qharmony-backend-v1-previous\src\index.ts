import dotenv from 'dotenv';
dotenv.config();

import App from './app';
import InitService from './services/init.service';
import MergeController from './controllers/merge.controller';
import Finch<PERSON>ontroller from './controllers/finch.controller';
import <PERSON>lack<PERSON>ontroller from './controllers/slack.controller';
import AuthController from './controllers/auth.controller';
import AdminController from './controllers/admin.controller';
import BenfitController from './controllers/benefit.controller';
import EmployeeController from './controllers/employee.controller';
import GroupController from './controllers/group.controller';
import WaitlistController from './controllers/waitlist.controller';

InitService.init().then(async () => {
  const app = new App([
    new MergeController(),
    new FinchController(),
    new SlackController(),
    new AuthController(),
    new AdminController(),
    new BenfitController(),
    new EmployeeController(),
    new GroupController(),
    new WaitlistController(),
  ]);
  app.listen();
});
