import React from "react";
import { Box, Typography, Avatar } from "@mui/material";
import { useRouter } from "next/navigation";
import { Bot } from "lucide-react";

export default function AIEnrollerCard() {
  const router = useRouter();

  const handleClick = () => {
    router.push('/ai-enroller');
  };

  return (
    <Box
      onClick={handleClick}
      sx={{
        backgroundColor: "white",
        padding: 2,
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        borderRadius: "30px",
        boxShadow: "none",
        maxWidth: "100%",
        mt: 3,
        cursor: "pointer",
        transition: "all 0.2s ease",
        "&:hover": {
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
          transform: "translateY(-2px)",
        },
      }}
    >
      {/* Left side with Avatar */}
      <Box sx={{ display: "flex", alignItems: "center", flexDirection: "row" }}>
        <Avatar
          sx={{
            width: 50,
            height: 50,
            mr: 2,
            backgroundColor: "#8b5cf6",
            background: "linear-gradient(135deg,rgb(101, 224, 255) 10%,rgb(24, 53, 56) 100%)",
          }}
        >
          <Bot size={28} color="white" />
        </Avatar>
        <Box>
          <Box
            sx={{ display: "flex", alignItems: "center", flexDirection: "row" }}
          >
            {/* AI Enroller Text */}
            <Typography
              sx={{
                fontWeight: 700,
                fontSize: "24px",
                display: "flex",
                alignItems: "center",
              }}
            >
              AI Enroller
            </Typography>
          </Box>
          <Typography
            sx={{
              fontWeight: 500,
              fontSize: "14px",
              color: "#6c757d",
            }}
          >
            Manage insurance plans and employee enrollment
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}
