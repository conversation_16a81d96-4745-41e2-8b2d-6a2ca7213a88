'use client';

import React, { useEffect, useState } from 'react';
import { Avatar, Box, Button, Typography } from "@mui/material";
import LogoutIcon from "@mui/icons-material/Logout";
import Image from 'next/image';
import { useAuth } from "@/components/AuthContext";
import { useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
import HarmonyLogo from '../../../../../public/logo.png';
import HappyStar from '../../../../../public/vectors/happy_star.svg';
import { useRouter } from 'next/navigation';

const EnrollmentHeader = () => {
  const router = useRouter();
  const { logout } = useAuth();
  
  const userDetails = useAppSelector(
    (state: RootState) => state.user.userProfile,
  );

  const [isTeamsApp, setIsTeamsApp] = useState(false);

  useEffect(() => {
    const isTeamsApp = localStorage.getItem("isTeamsApp1");
    setIsTeamsApp(isTeamsApp === "true");
  }, []);

  const getInitials = (name: string) => {
    if (!name) return "";
    const [firstName, lastName] = name.split(" ");
    return `${firstName[0].toUpperCase()}${lastName ? lastName[0].toUpperCase() : ""}`;
  };

  const isAdmin = useSelector(
    (state: RootState) => state.user.userProfile.isAdmin,
  );

  const isBroker = useSelector(
    (state: RootState) => state.user.userProfile.isBroker,
  );

  const capitalizeWords = (name: string) => {
    return name.replace(/\b\w/g, (char) => char.toUpperCase());
  };

  const handleLogoClick = () => {
    router.push("/dashboard");
  };

  return (
    <Box sx={{ 
      bgcolor: "white", 
      padding: 2, 
      textAlign: "center", 
      height: "80px", 
      borderBottom: "1px solid #D2D2D2",
      position: 'sticky',
      top: 0,
      zIndex: 50,
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
    }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* Left Section - BenOsphere Logo + User Info */}
        <Box sx={{ display: "flex", alignItems: "center" }}>
          {/* BenOsphere Logo */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              cursor: "pointer",
              mr: 3
            }}
            onClick={handleLogoClick}
          >
            <Image
              src={HarmonyLogo}
              alt="BenOsphere Logo"
              width={40}
              height={40}
            />
            <Typography sx={{ fontWeight: 800, fontSize: "1.5rem", ml: 1, color: "#111827" }}>
              BenOsphere
            </Typography>
          </Box>

          {/* User Info Section */}
          <Avatar
            sx={{
              bgcolor: "black",
              color: "#ffffff",
              width: 35,
              height: 35,
              fontSize: "1.2rem",
              mr: 1.5,
              ml: 2,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              paddingBottom: "1px",
              fontWeight: 800,
            }}
          >
            {getInitials(userDetails.name)}
          </Avatar>
          <Typography
            variant="h4"
            sx={{
              mb: 0,
              fontWeight: "bold",
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-start",
              textAlign: "flex-start",
              fontSize: "16px",
              mr: 1.5,
              color: "#111827",
            }}
          >
            {capitalizeWords(userDetails.name)}
          </Typography>

          {/* Admin Badge */}
          {isAdmin && (
            <Box
              sx={{
                bgcolor: "rgba(0, 0, 0, 0.06)",
                borderRadius: "8px",
                padding: "4px 8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontWeight: "bold",
                fontSize: "12px",
                color: "#333",
                mr: 1.5,
              }}
            >
              ADMIN
            </Box>
          )}

          {/* Broker Badge */}
          {isBroker && (
            <Box
              sx={{
                bgcolor: "#f5f5f5",
                borderRadius: "8px",
                padding: "2px 8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontWeight: "bold",
                fontSize: "0.9rem",
                color: "#333",
                mr: 1.5,
              }}
            >
              BROKER
            </Box>
          )}
        </Box>

        {/* Right Section - Action Buttons Only */}
        <Box sx={{ display: "flex", alignItems: "center" }}>

          {/* Guide Button */}
          {!isTeamsApp && (
            <Button
              sx={{
                backgroundColor: "transparent",
                color: "#333",
                textTransform: "none",
                padding: "8px 8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-start",
                gap: "2px",
                "&:hover": {
                  backgroundColor: "transparent",
                  color: "#555",
                },
                boxShadow: "none",
              }}
            >
              <Box sx={{ mt: 0.5, mr: 0.5 }}>
                <HappyStar />
              </Box>
              <Typography sx={{ fontWeight: 500, fontSize: "14px", color: "#333" }}>
                Guide
              </Typography>
            </Button>
          )}

          {/* Logout Button */}
          {!isTeamsApp && (
            <Button
              onClick={logout}
              sx={{
                backgroundColor: "transparent",
                color: "#333",
                marginLeft: 1,
                textTransform: "none",
                padding: "8px 16px",
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-start",
                gap: "8px",
                "&:hover": {
                  backgroundColor: "transparent",
                  color: "#555",
                },
                boxShadow: "none",
              }}
            >
              <LogoutIcon sx={{ fontSize: "18px" }} />
              <Typography sx={{ fontWeight: 500, fontSize: "14px", color: "#333" }}>
                Logout
              </Typography>
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default EnrollmentHeader;
