/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/ai-enroller/create-plan/create-plan.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/* Create Plan Page Styles */
:root {
  --primary-gradient: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
}

.create-plan-wrapper {
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.create-plan-page {
  max-width: 100%;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  padding: 0 5%;
}

/* New Progress Header - 25% of page height */
.progress-header {
  background: white;
  padding: 2vh 0 1vh;
  border-bottom: 0px solid #e5e7eb;
  min-height: 25vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.progress-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2vh;
  margin-left: 10%;
  width: 80%;
}

/* Progress title uses design system classes */
.progress-counter {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.progress-bar-container {
  margin-left: 10%;
  width: 80%;
  margin-bottom: 2vh;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Page Navigation */
.page-navigation {
  display: flex;
  gap: 0.75rem;
  padding: 0 0 2vh;
  margin-left: 10%;
  width: 80%;
  background: white;
  overflow-x: auto;
}

.page-nav-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: none;
  border-radius: 1.5rem;
  cursor: pointer;
  font-size: 13px;
  font-weight: 450;
  line-height: 1.2;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #6b7280;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.page-nav-item.active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
}

.page-nav-item.completed {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
}

.page-nav-item.completed::after {
  content: '✓';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-left: 0.5rem;
  font-size: 0.625rem;
  font-weight: 600;
  color: white;
  line-height: 1;
}

.page-nav-item:hover:not(.active):not(.completed) {
  background: #e5e7eb;
  color: #374151;
}

/* Navigation Icon Styling */
.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.page-nav-item.active .nav-icon,
.page-nav-item.completed .nav-icon {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Header */
.create-plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 3vw, 1.5rem);
  border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
  color: #374151;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  font-weight: 500;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.auto-save-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: clamp(0.75rem, 2vw, 0.875rem);
  color: #059669;
  font-weight: 500;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.save-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #059669;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.save-checkmark {
  color: #059669;
  font-weight: 600;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* AI Assistant Message - 30% of page height */
.ai-assistant-message {
  background: transparent;
  padding: 2vh 0 2vh 0;
  margin: 0;
  min-height: 30vh;
  display: flex;
  align-items: center;
}

.ai-message-content {
  display: flex;
  align-items: flex-start;
  gap: 0rem;
  margin-left: 10%;
  width: 80%;
}

.chat-bubble {
  background: white;
  border-radius: 1rem;
  padding: 2vh 2vw;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  margin-left: 1rem;
  margin-bottom: 1vh;
  flex: 1;
}

.chat-bubble::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 1rem;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid white;
}

.ai-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 3rem;
  height: 3rem;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.brea-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-text {
  flex: 1;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-greeting {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.ai-text p {
  font-size: 0.9rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.chat-message {
  font-size: 0.875rem;
  color: #1f2937;
  line-height: 1.6;
  margin-bottom: 0.75rem;
}

.chat-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.saved-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: #dcfce7;
  color: #166534;
  padding: 0.25rem 0.75rem;
  border-radius: 0.5rem;
  font-size: clamp(0.75rem, 1.8vw, 0.875rem);
  font-weight: 600;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main Content Area - 45% of page height */
.main-content {
  margin: 0 auto;
  padding: 1vh 0 3vh 0;
  background: white;
  min-height: 45vh;
  display: flex;
  flex-direction: column;
}

.form-container {
  background: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-left: 10%;
  width: 80%;
  position: relative;
  flex: 1;
}

.form-container::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 1.5rem;
  width: 0.5rem;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 50%;
}

/* Form Section */
.form-section {
  background: white;
  display: flex;
  flex-direction: column;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 1.5rem 0;
  border-bottom: 1px solid #e5e7eb00;
  margin-bottom: 0.5rem;
}

.form-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #8b5cf6;
}

.form-header-content svg {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 1px 2px rgba(139, 92, 246, 0.2));
}

.form-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-assist-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 0.625rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ai-assist-btn:hover {
  background: var(--primary-gradient);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
  filter: brightness(1.1);
}

/* Form Content */
.form-content {
  width : 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Tooltip Styles */
.tooltip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 0.7rem;
  height: 0.7rem;
  border: 1px solid #9ca3af;
  border-radius: 50%;
  font-size: 0.625rem;
  font-weight: 500;
  color: #6b7280;
  cursor: help;
  transition: all 0.2s ease;
  position: relative;
  margin-left: 0rem;
}

.tooltip-icon::after {
  content: 'i';
}

.tooltip-icon:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #eff6ff;
}

/* Hide default tooltips - we'll use JavaScript to create them */
.tooltip-icon[data-tooltip]:hover::before,
.tooltip-icon[data-tooltip]:hover::after {
  display: none;
}

/* Custom tooltip styles for JavaScript-created tooltips */
.custom-tooltip {
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}



.field-hint {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
  margin-top: 0.25rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  transform: translateY(-1px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #9ca3af;
}

/* Navigation */
.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0 0 0;
  margin-top: 1rem;
  gap: 1rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-width: 140px;
  justify-content: center;
}

.nav-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-btn.secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.nav-btn.primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
}

.nav-btn.primary.enabled:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.nav-btn.primary.disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
}

/* Step 2 Specific Styles - Additional textarea properties */
.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.highlight-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.highlight-input input {
  flex: 1;
}

.remove-highlight {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
}

.remove-highlight:hover {
  background: #dc2626;
}

.add-highlight {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px dashed #d1d5db;
  border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 3vw, 1.25rem);
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 0.75rem;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  justify-content: center;
}

.add-highlight:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* File Upload Styles */
.file-upload-area {
  position: relative;
  margin-top: 0.5rem;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 2rem;
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  background: #f9fafb;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.file-upload-label:hover {
  border-color: #8b5cf6;
  background: #faf5ff;
  color: #8b5cf6;
}

.file-upload-label span {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.file-upload-label small {
  font-size: 0.75rem;
  color: #6b7280;
}

.uploaded-files {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.uploaded-files h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
}

.uploaded-file:last-child {
  margin-bottom: 0;
}

.file-name {
  flex: 1;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.file-size {
  font-size: 0.75rem;
  color: #6b7280;
}

.remove-file {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.remove-file:hover {
  background: #dc2626;
}

/* Step 3 Specific Styles */
.input-with-prefix {
  position: relative;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  z-index: 1;
}

.input-with-prefix input {
  padding-left: 2rem;
}

/* Step 4 Review Styles */
.ready-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #dcfce7;
  color: #166534;
  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.75rem, 2vw, 1rem);
  border-radius: 1rem;
  font-size: clamp(0.7rem, 1.8vw, 0.8rem);
  font-weight: 600;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.review-content {
  padding: clamp(0.5rem, 0.25rem, 1rem);
  display: flex;
  flex-direction: column;
  gap: clamp(0.75rem, 2vw, 1.5rem);
}

.review-section {
  background: #f8fafc;
  border-radius: clamp(0.75rem, 2vw, 1rem);
  padding: clamp(1rem, 3vw, 1.5rem);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.review-section:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.review-section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.25rem;
  color: #3b82f6;
}

.review-section-header svg {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 1px 2px rgba(139, 92, 246, 0.2));
}

.review-section-header h4 {
  font-size: clamp(0.9rem, 2.5vw, 1.125rem);
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.review-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.review-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.review-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.review-value {
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 600;
}

.review-value.metal-tier {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
}

.review-value.plan-code {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 0.75rem;
  font-weight: 700;
}

.review-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  word-break: break-all;
}

.review-link:hover {
  text-decoration: underline;
}

.review-documents {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
}

.review-document {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
}

.review-document span {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.review-document small {
  font-size: 0.75rem;
  color: #6b7280;
}

.review-description {
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  background: hsl(0, 0%, 93%);
  padding: 0.7rem;
  border-radius: 0.5rem;

}

.review-highlights {
  margin: 0;
  padding: 0;
  list-style: none;
  background: hsl(0, 0%, 93%);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 0.7rem;
}

.review-highlights li {
  font-size: 0.875rem;
  color: #374151;
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.review-highlights li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #059669;
  font-weight: bold;
}

.create-confirmation {
  background-color: rgb(246, 237, 255);              /* bg-purple-50 */
  border: 1px solid rgb(220, 189, 253);    
  color: rgb(147, 7, 247);
  border-radius: 1rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
  gap: 1rem;
}

.confirmation-icon {
  background: var(--primary-gradient);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  flex-shrink: 0;
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.confirmation-text h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.confirmation-text p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
  line-height: 1.5;
}

/* Step 5 Success Styles */
.success-section {
  text-align: center;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-radius: 1rem;
  margin: 0;
}

.success-content {
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.success-icon {
  background: var(--primary-gradient);
  color: white;
  border-radius: 50%;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.success-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.success-content p {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

.success-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  max-width: 300px;
  margin-top: 0.5rem;
}

/* Plan Details Card */
.plan-details-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  max-width: 500px;
  width: 100%;
}

.plan-details-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: var(--primary-gradient);
  border-bottom: 1px solid #f1f5f9;
  padding-bottom: 0.75rem;
}

.plan-details-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.plan-details-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.plan-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8fafc;
}

.plan-detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #64748b;
  font-weight: 500;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.detail-value {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #1e293b;
  font-weight: 600;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.detail-value.plan-id {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
}

.detail-value.plan-code {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 700;
}

.detail-value.status-active {
  background: #dcfce7;
  color: #166534;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 600;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .create-plan-page {
    padding: 0 3%;
  }

  .progress-title,
  .progress-bar-container,
  .page-navigation,
  .ai-message-content,
  .form-container {
    margin-left: 5%;
    width: 90%;
  }
}

@media (max-width: 768px) {
  .create-plan-page {
    padding: 0 2%;
  }

  .create-plan-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 1rem;
  }

  .progress-header {
    min-height: 20vh;
    padding: 1vh 0;
  }

  .progress-title,
  .progress-bar-container,
  .page-navigation,
  .form-container {
    margin-left: 2%;
    width: 96%;
  }

  .ai-assistant-message {
    min-height: 25vh;
    padding: 1vh 0;
  }

  .ai-message-content {
    margin-left: 2%;
    width: 96%;
  }

  .chat-bubble {
    padding: 1.5vh 3vw;
    margin-left: 0.5rem;
  }

  .main-content {
    min-height: 55vh;
    padding: 1vh 0 2vh 0;
  }

  .form-container::before {
    display: none;
  }

  .form-navigation {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-btn {
    width: 100%;
    min-width: auto;
  }

  .review-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .create-confirmation {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .success-actions {
    width: 100%;
  }

  .plan-details-card {
    max-width: 100%;
    margin: 0.75rem 0;
    padding: 1rem;
  }

  .plan-detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    text-align: left;
    padding: 0.375rem 0;
  }

  .detail-value {
    max-width: 100%;
    text-align: left;
    word-break: break-word;
  }
}

@media (max-width: 480px) {
  .highlight-input {
    flex-direction: column;
    gap: 0.75rem;
  }

  .remove-highlight {
    align-self: flex-end;
  }

  .success-content {
    padding: 1.5rem 1rem;
    gap: 0.75rem;
  }

  .plan-details-card {
    padding: 1rem;
    margin: 0.5rem 0;
  }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
  .create-plan-page {
    padding: 0 8%;
  }

  .progress-title,
  .progress-bar-container,
  .page-navigation,
  .ai-message-content,
  .form-container {
    margin-left: 15%;
    width: 70%;
  }

  .review-content {
    max-width: 1000px;
    margin: 0 auto;
  }
}

/* Ultra-wide screen optimizations */
@media (min-width: 1600px) {
  .create-plan-page {
    padding: 0 12%;
  }

  .progress-title,
  .progress-bar-container,
  .page-navigation,
  .ai-message-content,
  .form-container {
    margin-left: 20%;
    width: 60%;
  }
}

