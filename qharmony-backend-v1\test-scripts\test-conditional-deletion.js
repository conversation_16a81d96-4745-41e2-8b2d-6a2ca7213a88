/**
 * 🧪 TEST CONDITIONAL DELETION IMPLEMENTATION
 * 
 * This script tests the new conditional deletion logic:
 * - Hard delete when no assignment references
 * - Block deletion when assignment references exist
 */

const axios = require('axios');

const TEST_CONFIG = {
  BASE_URL: 'http://localhost:8080',
  TIMEOUT: 10000
};

const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    test: '🧪'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
};

const makeRequest = async (method, endpoint, data = null, userId = null) => {
  try {
    const config = {
      method,
      url: `${TEST_CONFIG.BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: TEST_CONFIG.TIMEOUT
    };
    
    if (userId) {
      config.headers['user-id'] = userId;
    }
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { 
      success: true, 
      data: response.data, 
      status: response.status
    };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status || 500
    };
  }
};

async function testConditionalDeletion() {
  log('🎯 TESTING CONDITIONAL DELETION IMPLEMENTATION', 'test');
  
  // Mock IDs for testing (replace with real IDs from your test database)
  const mockSuperAdminId = '507f1f77bcf86cd799439012';
  const mockPlanWithNoReferences = '507f1f77bcf86cd799439013'; // Plan with 0 assignments
  const mockPlanWithReferences = '507f1f77bcf86cd799439014';   // Plan with 1+ assignments
  
  console.log('\n' + '='.repeat(80));
  log('TEST 1: Check deletion capability for plan with NO references', 'test');
  console.log('='.repeat(80));
  
  // Test 1: Check if plan can be deleted (should return canDelete: true)
  const canDeleteResult1 = await makeRequest(
    'GET',
    `/api/pre-enrollment/plans/${mockPlanWithNoReferences}/can-delete`,
    null,
    mockSuperAdminId
  );
  
  if (canDeleteResult1.success) {
    log(`✅ Can delete check: ${JSON.stringify(canDeleteResult1.data)}`, 'success');
    
    if (canDeleteResult1.data.result?.canDelete) {
      log('✅ Plan has no references - should allow hard deletion', 'success');
      
      // Test actual deletion (this would perform hard delete)
      log('⚠️ Skipping actual deletion test to preserve test data', 'info');
      log('💡 To test: DELETE /api/pre-enrollment/plans/' + mockPlanWithNoReferences, 'info');
      log('💡 Expected: 200 OK with deletionType: "hard"', 'info');
    } else {
      log('❌ Plan has references - unexpected for this test', 'error');
    }
  } else {
    log(`❌ Can delete check failed: ${canDeleteResult1.error}`, 'error');
  }
  
  console.log('\n' + '='.repeat(80));
  log('TEST 2: Check deletion capability for plan WITH references', 'test');
  console.log('='.repeat(80));
  
  // Test 2: Check if plan can be deleted (should return canDelete: false)
  const canDeleteResult2 = await makeRequest(
    'GET',
    `/api/pre-enrollment/plans/${mockPlanWithReferences}/can-delete`,
    null,
    mockSuperAdminId
  );
  
  if (canDeleteResult2.success) {
    log(`✅ Can delete check: ${JSON.stringify(canDeleteResult2.data)}`, 'success');
    
    if (!canDeleteResult2.data.result?.canDelete) {
      log('✅ Plan has references - should block deletion', 'success');
      log(`📊 Reference count: ${canDeleteResult2.data.result?.referenceCount}`, 'info');
      
      // Test actual deletion (this should be blocked)
      log('🧪 Testing deletion of plan with references...', 'test');
      
      const deleteResult = await makeRequest(
        'DELETE',
        `/api/pre-enrollment/plans/${mockPlanWithReferences}`,
        null,
        mockSuperAdminId
      );
      
      if (!deleteResult.success && deleteResult.status === 409) {
        log('✅ Deletion correctly blocked with 409 Conflict', 'success');
        log(`✅ Error message: ${deleteResult.error.error}`, 'success');
        
        if (deleteResult.error.suggestion) {
          log(`✅ Suggestion provided: ${deleteResult.error.suggestion}`, 'success');
        }
        
        if (deleteResult.error.action === 'archive_instead') {
          log('✅ Action guidance provided: archive_instead', 'success');
        }
      } else {
        log('❌ Deletion should have been blocked with 409 status', 'error');
        log(`❌ Got status: ${deleteResult.status}`, 'error');
      }
    } else {
      log('❌ Plan should have references for this test', 'error');
    }
  } else {
    log(`❌ Can delete check failed: ${canDeleteResult2.error}`, 'error');
  }
  
  console.log('\n' + '='.repeat(80));
  log('TEST 3: Test archive API as alternative', 'test');
  console.log('='.repeat(80));
  
  // Test 3: Test archive API (should work regardless of references)
  log('⚠️ Skipping actual archive test to preserve test data', 'info');
  log('💡 To test: POST /api/pre-enrollment/plans/' + mockPlanWithReferences + '/archive', 'info');
  log('💡 Expected: 200 OK with status changed to "Archived"', 'info');
  
  console.log('\n' + '='.repeat(80));
  log('🎉 CONDITIONAL DELETION TESTS COMPLETED', 'test');
  console.log('='.repeat(80));
  
  log('📋 Summary of Expected Behavior:', 'info');
  log('  ✅ Plans with 0 assignment references → Hard delete (permanent removal)', 'info');
  log('  ❌ Plans with 1+ assignment references → Deletion blocked (409 Conflict)', 'info');
  log('  💡 Alternative for referenced plans → Use archive API instead', 'info');
  log('  🔧 Azure containers → Cleaned up during hard deletion', 'info');
  log('  📝 Audit logs → Record deletion type and reasoning', 'info');
}

// Run tests
if (require.main === module) {
  testConditionalDeletion()
    .then(() => {
      log('🎯 Test script completed', 'success');
    })
    .catch(error => {
      log(`💥 Test script failed: ${error.message}`, 'error');
    });
}

module.exports = { testConditionalDeletion };
