"use client";

import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  Typography,
  Button,
  Avatar,
  Divider,
  CircularProgress,
} from "@mui/material";
import withSidebar from "@/components/withSidebar";
import theme from "@/theme";
// import AddTeamMemberDialog from "./AddTeamMemberDialogue";
import {
    getAllCompaniesUnderBroker,
  getCompanyTeamMembers,
  offboardEmployee,
  sendLoginLinkToEmployee,
} from "@/middleware/company_middleware";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useAppSelector } from "@/redux/hooks";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import AddCompanyDialog from "./AddCompany";

// Colors for avatars
const colors = [
  "#FFB6C1",
  "#FF6347",
  "#FFD700",
  "#90EE90",
  "#00CED1",
  "#1E90FF",
  "#BA55D3",
];

const getInitials = (name: string) => {
  const [firstName, lastName] = name.split(" ");
  return `${firstName[0]}${lastName ? lastName[0] : ""}`;
};

const getAvatarColor = (index: number) => colors[index % colors.length];

const ManageCompanies = () => {
  const dispatch = useDispatch();

  const currentUserId = useAppSelector((state) => state.user._id);

  const companies = useSelector(
    (state: RootState) => state.user.managedCompanies,
  );

  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(true); // New state for loading

  const handleAddCompany = () => {
    setOpenDialog(true);
  };

  useEffect(() => {
    const fetchCompanies = async () => {
      setLoading(true);
      await getAllCompaniesUnderBroker(dispatch, currentUserId);
      setLoading(false);
    };
    fetchCompanies();
  }, [dispatch, currentUserId]);

  return (
    <ProtectedRoute>
      <Box sx={{ bgcolor: "#F5F6FA", p: 4, width: "100%", height: "100vh", overflow: "auto" }}>
        <Grid
          container
          alignItems="center"
          justifyContent="space-between"
          sx={{ mb: 2 }}
        >
          <Grid item>
            <Typography variant="h5">Team Members</Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddCompany}
              sx={{
                backgroundColor: "#000000",
                textTransform: "none",
                borderRadius: "6px",
              }}
            >
              Add new company
            </Button>
          </Grid>
        </Grid>

        <Box
          sx={{
            bgcolor: "#ffffff",
            borderRadius: "12px",
            width: "100%",
            p: 2,
            boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
          }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : companies.length === 0 ? (
            <Typography variant="body1" sx={{ textAlign: "center", my: 4 }}>
              Add your first company to get started
            </Typography>
          ) : (
            companies.map((company: any, index: any) => (
              <Box key={index}>
                <Grid container alignItems="center" spacing={2}>
                  {/* Avatar and Name */}
                  <Grid item xs={3} container alignItems="center">
                    <Avatar
                      sx={{
                        bgcolor: getAvatarColor(index),
                        color: "#ffffff",
                        width: 48,
                        height: 48,
                        fontSize: "1.2rem",
                        mr: 2, // Add space between Avatar and Name
                      }}
                    >
                      {getInitials(company.name)}
                    </Avatar>
                    <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                      {company.name}
                    </Typography>
                  </Grid>

                  {/* Email */}
                  <Grid item xs={3}>
                    <Typography variant="body2">{company.adminEmail}</Typography>
                  </Grid>
                </Grid>
                {index < companies.length - 1 && <Divider sx={{ my: 2 }} />}
              </Box>
            ))
          )}
        </Box>
        <AddCompanyDialog
          open={openDialog}
          onClose={() => setOpenDialog(false)}
        />
      </Box>
    </ProtectedRoute>
  );
};

export default withSidebar(ManageCompanies);
