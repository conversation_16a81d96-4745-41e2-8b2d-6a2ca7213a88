import EnvService from './env.service';
import logger from '../utils/logger';

/**
 * Azure Blob Storage Namespace Service
 * Provides environment-aware container naming for proper data isolation
 */
class AzureNamespaceService {
  
  /**
   * Get environment-aware employer container name
   * Production: employer-{companyId}
   * Test: employer-test-{companyId}
   */
  static getEmployerContainer(companyId: string): string {
    const env = EnvService.env();
    const prefix = env.AZURE_EMPLOYER_PREFIX;
    const containerName = `${prefix}${companyId}`;
    
    logger.info(`Generated employer container name: ${containerName}`);
    return containerName;
  }

  /**
   * Get environment-aware plan container name
   * Production: plan-{planId}
   * Test: plan-test-{planId}
   */
  static getPlanContainer(planId: string): string {
    const env = EnvService.env();
    const prefix = env.AZURE_PLAN_PREFIX;
    const containerName = `${prefix}${planId}`;
    
    logger.info(`Generated plan container name: ${containerName}`);
    return containerName;
  }

  /**
   * Get current environment info for debugging
   */
  static getEnvironmentInfo(): { 
    employerPrefix: string; 
    planPrefix: string; 
    environment: string;
  } {
    const env = EnvService.env();
    return {
      employerPrefix: env.AZURE_EMPLOYER_PREFIX,
      planPrefix: env.AZURE_PLAN_PREFIX,
      environment: env.MONGO_DB_NAME
    };
  }

  /**
   * Validate container name format
   */
  static validateContainerName(containerName: string): boolean {
    // Azure container naming rules:
    // - 3-63 characters
    // - lowercase letters, numbers, and hyphens only
    // - must start and end with letter or number
    const azureContainerRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/;
    const isValid = azureContainerRegex.test(containerName) && 
                   containerName.length >= 3 && 
                   containerName.length <= 63;
    
    if (!isValid) {
      logger.warn(`Invalid Azure container name: ${containerName}`);
    }
    
    return isValid;
  }

  /**
   * Get all environment-specific container prefixes for cleanup/migration
   */
  static getAllPrefixes(): { employer: string; plan: string } {
    const env = EnvService.env();
    return {
      employer: env.AZURE_EMPLOYER_PREFIX,
      plan: env.AZURE_PLAN_PREFIX
    };
  }
}

export default AzureNamespaceService;
