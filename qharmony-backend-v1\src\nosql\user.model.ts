import mongoose, { Document, Model } from 'mongoose';
import { EMPLOYEE_CLASS_TYPES, PAYROLL_FREQUENCIES } from '../constants';

const { Schema } = mongoose;

// 🏠 Address Interface
export interface AddressInterface {
  street1: string;                    // Primary street address
  street2?: string;                   // Apartment, suite, unit, etc.
  city: string;                       // City name
  state: string;                      // State abbreviation (e.g., "CA", "NY")
  zipCode: string;                    // ZIP/postal code
  country?: string;                   // Country (defaults to "US")
}

// 👨‍👩‍👧‍👦 Dependent Interface
export interface DependentInterface {
  _id?: mongoose.Types.ObjectId;      // Auto-generated MongoDB ID
  name: string;                       // Full name of dependent
  gender: string;                     // "Male", "Female", "Other", "Prefer not to say"
  dateOfBirth: Date;                  // Date of birth for age calculation
  relationship: string;               // "Spouse", "Child", "Domestic Partner", "Other"
  ssn?: string;                       // Social Security Number (encrypted, optional)
  isStudent?: boolean;                // Whether dependent is a student (affects coverage eligibility)
  isDisabled?: boolean;               // Whether dependent has a disability (affects coverage)
  coverageEndAge?: number;            // Age when coverage ends (e.g., 26 for children)

  // 📋 Additional dependent details
  address?: AddressInterface;         // Dependent's address (if different from employee)
  phoneNumber?: string;               // Dependent's phone number
  email?: string;                     // Dependent's email address

  // 🏥 Medical information (optional)
  primaryCarePhysician?: string;      // Name of primary care doctor
  medicalConditions?: string[];       // List of known medical conditions
  medications?: string[];             // List of current medications

  // 📅 Enrollment tracking
  enrolledPlans?: string[];           // Array of plan IDs dependent is enrolled in
  enrollmentDate?: Date;              // When dependent was added to coverage
  effectiveDate?: Date;               // When coverage becomes effective
  terminationDate?: Date;             // When coverage ends (if applicable)

  // 📝 Documentation
  birthCertificateUrl?: string;       // URL to birth certificate document
  marriageCertificateUrl?: string;    // URL to marriage certificate (for spouse)
  adoptionPapersUrl?: string;         // URL to adoption papers (for adopted children)

  // 🔄 Status tracking
  isActive?: boolean;                 // Whether dependent is currently active
  createdAt?: Date;                   // When dependent record was created
  updatedAt?: Date;                   // When dependent record was last updated
}

export interface UserDataInterface {
  _id?: mongoose.Types.ObjectId;
  name: string;
  email: string;
  role: string;
  companyId: string;
  isAdmin: boolean;
  isBroker: boolean;
  isSuperAdmin?: boolean; // New flag for super admin privileges (optional for backward compatibility)
  isActivated: boolean;
  isDisabled: boolean;
  groupIds?: string[];
  lastLoginAt?: Date;

  details?: {
    // 📞 Basic Contact Information
    phoneNumber?: string;
    department?: string;
    title?: string;
    role?: string;

    // 🎯 Employee demographic data for cost calculation
    dateOfBirth?: Date;           // To calculate age for age-banded pricing
    hireDate?: Date;              // For waiting period eligibility calculation
    annualSalary?: number;        // For salary-based pricing calculations
    employeeClassType?: string;   // "Full-Time", "Part-Time", "Contractor", etc.
    customPayrollFrequency?: string; // Optional override of company payroll frequency

    // 🆔 Personal Identification (NEW - Backward Compatible)
    ssn?: string;                 // Social Security Number (encrypted storage)

    // 🏠 Address Information (NEW - Backward Compatible)
    address?: AddressInterface;   // Employee's primary address
    mailingAddress?: AddressInterface; // Mailing address (if different from primary)

    // 👨‍👩‍👧‍👦 Family Information (NEW - Backward Compatible)
    dependents?: DependentInterface[]; // Array of dependents for benefits enrollment

    // 🚨 Emergency Contact (NEW - Backward Compatible)
    emergencyContact?: {
      name?: string;               // Emergency contact name
      relationship?: string;      // Relationship to employee
      phoneNumber?: string;       // Emergency contact phone
      email?: string;            // Emergency contact email
      address?: AddressInterface; // Emergency contact address
    };

    // 💼 Employment Details (NEW - Backward Compatible)
    employeeId?: string;          // Company-specific employee ID
    managerId?: string;           // Manager's user ID
    workLocation?: string;        // Primary work location
    workSchedule?: string;        // Work schedule type ("Full-Time", "Part-Time", "Remote", etc.)

    // �🏥 Health & Benefits (NEW - Backward Compatible)
    tobaccoUser?: boolean;        // Tobacco use status (affects insurance rates)
    disabilityStatus?: string;    // Disability status for ADA compliance
    veteranStatus?: string;       // Veteran status for reporting

    // 📋 Compliance & Reporting (NEW - Backward Compatible)
    i9Verified?: boolean;         // I-9 employment verification status
    w4OnFile?: boolean;          // W-4 tax form on file
    directDepositSetup?: boolean; // Direct deposit setup status

    // 📅 Important Dates (NEW - Backward Compatible)
    terminationDate?: Date;       // Employment termination date (if applicable)
    rehireDate?: Date;           // Rehire date (if applicable)
    lastReviewDate?: Date;       // Last performance review date
    nextReviewDate?: Date;       // Next scheduled performance review

    // 📝 Notes & Comments (NEW - Backward Compatible)
    notes?: string;              // General notes about the employee
    hrNotes?: string;           // HR-specific notes (restricted access)

    // ✍️ Digital Signature (NEW - Backward Compatible)
    signature?: string;          // Base64-encoded signature data (simple approach)

    // ✍️ Enhanced Enrollment Signature (NEW - Backward Compatible)
    enrollmentSignature?: {
      signatureData: string;     // Base64 image data
      signedAt: Date;           // When signed (clearer than 'timestamp')
      isVerified: boolean;      // Verification status
      verifiedAt?: Date;        // When verified
      verifiedBy?: string;      // User ID who verified
    };
  };
}

interface UserDocument extends Document {
  data: UserDataInterface[];
}

class UserModelClass {
  private static userModel: Model<UserDocument>;

  public static initializeModel() {
    const schema = new Schema({
      name: String,
      email: String,
      role: String,
      companyId: String,
      isAdmin: Boolean,
      isBroker: Boolean,
      isSuperAdmin: { type: Boolean, default: false }, // New field with default false for backward compatibility
      isActivated: Boolean,
      isDisabled: Boolean,
      groupIds: { type: [String], default: [] },
      lastLoginAt: { type: Date, default: null },

      details: {
        // 📞 Basic Contact Information
        phoneNumber: String,
        department: String,
        title: String,
        role: String,

        // 🎯 Employee demographic data for cost calculation
        dateOfBirth: { type: Date },
        hireDate: { type: Date },
        annualSalary: { type: Number, min: 0 },
        employeeClassType: {
          type: String,
          enum: EMPLOYEE_CLASS_TYPES
        },
        customPayrollFrequency: {
          type: String,
          enum: PAYROLL_FREQUENCIES
        },

        // 🆔 Personal Identification (NEW - Backward Compatible)
        ssn: {
          type: String,
          validate: {
            validator: function(value: string) {
              // Allow empty/null values for backward compatibility
              if (!value) return true;
              // Validate SSN format: XXX-XX-XXXX or XXXXXXXXX
              return /^(\d{3}-?\d{2}-?\d{4})$/.test(value);
            },
            message: 'SSN must be in format XXX-XX-XXXX or XXXXXXXXX'
          }
        },

        // 🏠 Address Information (NEW - Backward Compatible)
        address: {
          street1: { type: String },
          street2: { type: String },
          city: { type: String },
          state: {
            type: String,
            validate: {
              validator: function(value: string) {
                if (!value) return true;
                // Validate US state abbreviations (2 characters)
                return /^[A-Z]{2}$/.test(value);
              },
              message: 'State must be a valid 2-character abbreviation (e.g., CA, NY)'
            }
          },
          zipCode: {
            type: String,
            validate: {
              validator: function(value: string) {
                if (!value) return true;
                // Validate US ZIP codes: XXXXX or XXXXX-XXXX
                return /^\d{5}(-\d{4})?$/.test(value);
              },
              message: 'ZIP code must be in format XXXXX or XXXXX-XXXX'
            }
          },
          country: { type: String, default: 'US' }
        },

        mailingAddress: {
          street1: { type: String },
          street2: { type: String },
          city: { type: String },
          state: {
            type: String,
            validate: {
              validator: function(value: string) {
                if (!value) return true;
                return /^[A-Z]{2}$/.test(value);
              },
              message: 'State must be a valid 2-character abbreviation'
            }
          },
          zipCode: {
            type: String,
            validate: {
              validator: function(value: string) {
                if (!value) return true;
                return /^\d{5}(-\d{4})?$/.test(value);
              },
              message: 'ZIP code must be in format XXXXX or XXXXX-XXXX'
            }
          },
          country: { type: String, default: 'US' }
        },

        // 👨‍👩‍👧‍👦 Family Information (NEW - Backward Compatible)
        dependents: [{
          name: { type: String, required: true },
          gender: {
            type: String,
            enum: ['Male', 'Female', 'Other', 'Prefer not to say'],
            required: true
          },
          dateOfBirth: { type: Date, required: true },
          relationship: {
            type: String,
            enum: ['Spouse', 'Child', 'Domestic Partner', 'Stepchild', 'Adopted Child', 'Other'],
            required: true
          },
          ssn: {
            type: String,
            validate: {
              validator: function(value: string) {
                if (!value) return true;
                return /^(\d{3}-?\d{2}-?\d{4})$/.test(value);
              },
              message: 'Dependent SSN must be in format XXX-XX-XXXX or XXXXXXXXX'
            }
          },
          isStudent: { type: Boolean, default: false },
          isDisabled: { type: Boolean, default: false },
          coverageEndAge: { type: Number, min: 0, max: 100 },

          // Additional dependent details
          address: {
            street1: { type: String },
            street2: { type: String },
            city: { type: String },
            state: { type: String },
            zipCode: { type: String },
            country: { type: String, default: 'US' }
          },
          phoneNumber: { type: String },
          email: {
            type: String,
            validate: {
              validator: function(value: string) {
                if (!value) return true;
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
              },
              message: 'Invalid email format'
            }
          },

          // Medical information
          primaryCarePhysician: { type: String },
          medicalConditions: [{ type: String }],
          medications: [{ type: String }],

          // Enrollment tracking
          enrolledPlans: [{ type: String }],
          enrollmentDate: { type: Date },
          effectiveDate: { type: Date },
          terminationDate: { type: Date },

          // Documentation
          birthCertificateUrl: { type: String },
          marriageCertificateUrl: { type: String },
          adoptionPapersUrl: { type: String },

          // Status tracking
          isActive: { type: Boolean, default: true },
          createdAt: { type: Date, default: Date.now },
          updatedAt: { type: Date, default: Date.now }
        }],

        // 🚨 Emergency Contact (NEW - Backward Compatible)
        emergencyContact: {
          name: { type: String },
          relationship: { type: String },
          phoneNumber: { type: String },
          email: {
            type: String,
            validate: {
              validator: function(value: string) {
                if (!value) return true;
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
              },
              message: 'Invalid emergency contact email format'
            }
          },
          address: {
            street1: { type: String },
            street2: { type: String },
            city: { type: String },
            state: { type: String },
            zipCode: { type: String },
            country: { type: String, default: 'US' }
          }
        },

        // 💼 Employment Details (NEW - Backward Compatible)
        employeeId: { type: String },
        managerId: { type: String },
        workLocation: { type: String },
        workSchedule: {
          type: String,
          enum: ['Full-Time', 'Part-Time', 'Remote', 'Hybrid', 'Contract', 'Temporary']
        },

        // �🏥 Health & Benefits (NEW - Backward Compatible)
        tobaccoUser: { type: Boolean, default: false },
        disabilityStatus: {
          type: String,
          enum: ['None', 'Temporary', 'Permanent', 'Prefer not to say']
        },
        veteranStatus: {
          type: String,
          enum: ['Not a veteran', 'Veteran', 'Active duty', 'Prefer not to say']
        },

        // 📋 Compliance & Reporting (NEW - Backward Compatible)
        i9Verified: { type: Boolean, default: false },
        w4OnFile: { type: Boolean, default: false },
        directDepositSetup: { type: Boolean, default: false },

        // 📅 Important Dates (NEW - Backward Compatible)
        terminationDate: { type: Date },
        rehireDate: { type: Date },
        lastReviewDate: { type: Date },
        nextReviewDate: { type: Date },

        // 📝 Notes & Comments (NEW - Backward Compatible)
        notes: { type: String },
        hrNotes: { type: String }, // Restricted access field

        // ✍️ Digital Signature (NEW - Backward Compatible)
        signature: {
          type: String,
          validate: {
            validator: function(value: string) {
              // Allow empty/null values for backward compatibility
              if (!value) return true;
              // Validate Base64 format (basic check)
              return /^[A-Za-z0-9+/]*={0,2}$/.test(value);
            },
            message: 'Signature must be a valid Base64-encoded string'
          }
        },

        // ✍️ Enhanced Enrollment Signature (NEW - Backward Compatible)
        enrollmentSignature: {
          signatureData: {
            type: String,
            validate: {
              validator: function(value: string) {
                if (!value) return true;
                return /^[A-Za-z0-9+/]*={0,2}$/.test(value);
              },
              message: 'Signature data must be a valid Base64-encoded string'
            }
          },
          signedAt: { type: Date },
          isVerified: { type: Boolean, default: false },
          verifiedAt: { type: Date },
          verifiedBy: { type: String }
        }
      },
    });

    this.userModel = mongoose.model<UserDocument>('User', schema);
  }

  public static async addData(data: UserDataInterface): Promise<
    | (mongoose.Document<unknown, {}, UserDocument> &
      UserDocument & {
        _id: mongoose.Types.ObjectId;
      })
    | null>
    {
    try {
      // Use type assertion to fix the TypeScript error
      const createdUser = await this.userModel.create(data);
      return createdUser as mongoose.Document<unknown, {}, UserDocument> &
        UserDocument & {
          _id: mongoose.Types.ObjectId;
        };
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  public static async getData(): Promise<UserDataInterface[]> {
    try {
      const data = (await this.userModel.find()) as UserDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async getDataById(id: string): Promise<UserDataInterface> {
    try {
      const data = (await this.userModel.findById(id)) as UserDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return {} as UserDataInterface;
    }
  }

  public static async getDataByCompanyId(
    id: string
  ): Promise<UserDataInterface[]> {
    try {
      const data = (await this.userModel.find({
        companyId: id,
      })) as UserDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async getDataByEmail({
    email,
  }: {
    email: string;
  }): Promise<UserDataInterface> {
    try {
      const data = (await this.userModel.findOne({
        email,
      })) as UserDataInterface;
      return data;
    } catch (error) {
      console.error(error);
      return {} as UserDataInterface;
    }
  }

  public static async updateData(
    id: string,
    data: Partial<UserDataInterface>
  ): Promise<void> {
    try {
      // make sure you update only the fields that are present in the data object
      await this.userModel.findByIdAndUpdate(id, data);
    } catch (error) {
      console.error(error);
    }
  }

  public static async getAllData(): Promise<UserDataInterface[]> {
    try {
      const data = (await this.userModel.find()) as UserDataInterface[];
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  public static async addGroupToUsers(userIds: string[], groupId: string): Promise<void> {
    try {
      await this.userModel.updateMany(
        { _id: { $in: userIds } },
        { $addToSet: { groupIds: groupId } } // Ensures groupIds is unique
      );
    } catch (error) {
      console.error("Error adding group to users:", error);
    }
  }

  public static async removeGroupFromUsers(userIds: string[], groupId: string): Promise<void> {
    try {
      await this.userModel.updateMany(
        { _id: { $in: userIds } },
        { $pull: { groupIds: groupId } } // Removes the groupId from users
      );
    } catch (error) {
      console.error("Error removing group from users:", error);
    }
  }

  public static async getUsersByIds(userIds: string[]): Promise<UserDataInterface[]> {
    try {
      const users = await this.userModel.find({ _id: { $in: userIds } }).lean();
      return users as unknown as UserDataInterface[];
    } catch (error) {
      console.error("Error fetching users by IDs:", error);
      return [];
    }
  }

  public static async getUsersByCompanyIdsWithTenantId(companyIds: string[]): Promise<{ email: string; companyName: string; tenantId: string }[]> {
    try {
      const users = await this.userModel.aggregate([
        {
          $match: {
            companyId: { $in: companyIds },
            isDisabled: false
          }
        },
        {
          $addFields: {
            companyIdObject: { $toObjectId: '$companyId' } // Convert companyId to ObjectId
          }
        },
        {
          $lookup: {
            from: 'companies', // The name of the companies collection
            localField: 'companyIdObject', // Use the converted ObjectId field
            foreignField: '_id', // Field in the companies collection
            as: 'companyData' // The name of the array field to store the joined data
          }
        },
        {
          $unwind: {
            path: '$companyData',
            preserveNullAndEmptyArrays: true // In case no matching company is found
          }
        },
        {
          $addFields: {
            tenantId: '$companyData.tenantId', // Add tenantId from the companyData to the user object
            companyName: '$companyData.name' // Add companyName from the companyData to the user object
          }
        },
        {
          $project: {
            _id: 0,
            email: 1, // Include only the email field
            tenantId: 1, // Include only the tenantId field
            companyName: 1 // Include companyName
          }
        }
      ]) as unknown as { email: string; companyName: string; tenantId: string }[]; // Explicitly cast to the desired return type

      return users;
    } catch (error) {
      console.error("Error fetching users by company IDs with tenantId:", error);
      return [];
    }
  }

  public static async getUsersByIdsWithTenantId(userIds: string[]): Promise<{ email: string; companyName: string; tenantId: string }[]> {
    try {
      const users = await this.userModel.aggregate([
        {
          $match: {
            _id: { $in: userIds.map(id => new mongoose.Types.ObjectId(id)) }, // Convert userIds to ObjectId
            $or: [
              { isDisabled: false },
              { isDisabled: { $exists: false } }
            ]
          }
        },
        {
          $addFields: {
            companyIdObject: { $toObjectId: '$companyId' } // Convert companyId to ObjectId
          }
        },
        {
          $lookup: {
            from: 'companies', // The name of the companies collection
            localField: 'companyIdObject', // Use the converted ObjectId field
            foreignField: '_id', // Field in the companies collection
            as: 'companyData' // The name of the array field to store the joined data
          }
        },
        {
          $unwind: {
            path: '$companyData',
            preserveNullAndEmptyArrays: true // In case no matching company is found
          }
        },
        {
          $addFields: {
            tenantId: '$companyData.tenantId', // Add tenantId from the companyData to the user object
            companyName: '$companyData.name' // Add companyName from the companyData to the user object
          }
        },
        {
          $project: {
            _id: 0,
            email: 1, // Include only the email field
            tenantId: 1, // Include only the tenantId field
            companyName: 1 // Include companyName
          }
        }
      ]) as unknown as { email: string; companyName: string; tenantId: string }[]; // Explicitly cast to the desired return type

      return users;
    } catch (error) {
      console.error("Error fetching users by user IDs with tenantId:", error);
      return [];
    }
  }

  public static async updateLoginTimestamp(userId: string): Promise<void> {
    try {
      await this.userModel.findByIdAndUpdate(userId, {
        lastLoginAt: new Date()
      });
      console.log(`Updated login timestamp for user ${userId}`);
    } catch (error) {
      console.error("Error updating login timestamp:", error);
    }
  }

  // Helper method to check if user is super admin
  public static isSuperAdmin(user: UserDataInterface): boolean {
    // Safely handle undefined/null values - default to false if field is missing
    return Boolean(user?.isSuperAdmin);
  }

  // Helper method to check if user has elevated privileges (super admin or admin)
  public static hasElevatedPrivileges(user: UserDataInterface): boolean {
    // Safely handle undefined/null values for both fields
    return Boolean(user?.isSuperAdmin) || Boolean(user?.isAdmin);
  }

  // Helper method to check if user can manage system resources (only super admin)
  public static canManageSystemResources(user: UserDataInterface): boolean {
    // Safely handle undefined/null values - default to false if field is missing
    return Boolean(user?.isSuperAdmin);
  }

  // Method to promote a user to super admin (should be used carefully)
  public static async promoteToSuperAdmin(userId: string): Promise<boolean> {
    try {
      const result = await this.userModel.findByIdAndUpdate(userId, {
        isSuperAdmin: true,
        isAdmin: true, // Super admins should also have admin privileges
        isBroker: true // Super admins should also have broker privileges for functionality
      });
      return !!result;
    } catch (error) {
      console.error("Error promoting user to super admin:", error);
      return false;
    }
  }

  // Method to revoke super admin privileges
  public static async revokeSuperAdmin(userId: string): Promise<boolean> {
    try {
      const result = await this.userModel.findByIdAndUpdate(userId, {
        isSuperAdmin: false
      });
      return !!result;
    } catch (error) {
      console.error("Error revoking super admin privileges:", error);
      return false;
    }
  }

  // Get all super admins
  public static async getSuperAdmins(): Promise<UserDataInterface[]> {
    try {
      const data = (await this.userModel.find({ isSuperAdmin: true })) as UserDataInterface[];
      return data;
    } catch (error) {
      console.error("Error fetching super admins:", error);
      return [];
    }
  }

  // ===== NEW HELPER METHODS FOR ENHANCED USER DATA =====

  // 👨‍👩‍👧‍👦 Dependent Management Methods
  public static async addDependent(userId: string, dependent: DependentInterface): Promise<boolean> {
    try {
      // Add timestamps
      dependent.createdAt = new Date();
      dependent.updatedAt = new Date();
      dependent.isActive = dependent.isActive !== false; // Default to true

      const result = await this.userModel.findByIdAndUpdate(
        userId,
        { $push: { 'details.dependents': dependent } },
        { new: true }
      );
      return !!result;
    } catch (error) {
      console.error("Error adding dependent:", error);
      return false;
    }
  }

  public static async updateDependent(userId: string, dependentId: string, updates: Partial<DependentInterface>): Promise<boolean> {
    try {
      updates.updatedAt = new Date();

      const result = await this.userModel.findOneAndUpdate(
        {
          _id: userId,
          'details.dependents._id': dependentId
        },
        {
          $set: Object.keys(updates).reduce((acc, key) => {
            acc[`details.dependents.$.${key}`] = updates[key as keyof DependentInterface];
            return acc;
          }, {} as any)
        },
        { new: true }
      );
      return !!result;
    } catch (error) {
      console.error("Error updating dependent:", error);
      return false;
    }
  }

  public static async removeDependent(userId: string, dependentId: string): Promise<boolean> {
    try {
      const result = await this.userModel.findByIdAndUpdate(
        userId,
        { $pull: { 'details.dependents': { _id: dependentId } } },
        { new: true }
      );
      return !!result;
    } catch (error) {
      console.error("Error removing dependent:", error);
      return false;
    }
  }

  public static async getDependents(userId: string): Promise<DependentInterface[]> {
    try {
      const user = await this.userModel.findById(userId).select('details.dependents') as unknown as UserDataInterface;
      return user?.details?.dependents || [];
    } catch (error) {
      console.error("Error fetching dependents:", error);
      return [];
    }
  }

  // 🏠 Address Management Methods
  public static async updateAddress(userId: string, address: AddressInterface, isMailingAddress: boolean = false): Promise<boolean> {
    try {
      const fieldPath = isMailingAddress ? 'details.mailingAddress' : 'details.address';
      const result = await this.userModel.findByIdAndUpdate(
        userId,
        { $set: { [fieldPath]: address } },
        { new: true }
      );
      return !!result;
    } catch (error) {
      console.error("Error updating address:", error);
      return false;
    }
  }

  // 🚨 Emergency Contact Management
  public static async updateEmergencyContact(userId: string, emergencyContact: any): Promise<boolean> {
    try {
      const result = await this.userModel.findByIdAndUpdate(
        userId,
        { $set: { 'details.emergencyContact': emergencyContact } },
        { new: true }
      );
      return !!result;
    } catch (error) {
      console.error("Error updating emergency contact:", error);
      return false;
    }
  }

  // 🔍 Search and Filter Methods
  public static async getUsersByDependentCount(companyId: string, minDependents: number = 1): Promise<UserDataInterface[]> {
    try {
      const users = await this.userModel.find({
        companyId,
        'details.dependents': { $exists: true, $not: { $size: 0 } }
      }).where('details.dependents').size(minDependents);
      return users as unknown as UserDataInterface[];
    } catch (error) {
      console.error("Error fetching users by dependent count:", error);
      return [];
    }
  }

  public static async getUsersByTobaccoStatus(companyId: string, tobaccoUser: boolean): Promise<UserDataInterface[]> {
    try {
      const users = await this.userModel.find({
        companyId,
        'details.tobaccoUser': tobaccoUser
      });
      return users as unknown as UserDataInterface[];
    } catch (error) {
      console.error("Error fetching users by tobacco status:", error);
      return [];
    }
  }

  // 📊 Analytics and Reporting Methods
  public static async getCompanyDemographics(companyId: string): Promise<{
    totalEmployees: number;
    averageAge: number;
    dependentCount: number;
    tobaccoUsers: number;
    employeeClassBreakdown: Record<string, number>;
  }> {
    try {
      const users = await this.userModel.find({ companyId }) as unknown as UserDataInterface[];
      const currentDate = new Date();

      let totalAge = 0;
      let ageCount = 0;
      let dependentCount = 0;
      let tobaccoUsers = 0;
      const employeeClassBreakdown: Record<string, number> = {};

      users.forEach(user => {
        // Calculate age
        if (user.details?.dateOfBirth) {
          const age = currentDate.getFullYear() - user.details.dateOfBirth.getFullYear();
          totalAge += age;
          ageCount++;
        }

        // Count dependents
        if (user.details?.dependents) {
          dependentCount += user.details.dependents.length;
        }

        // Count tobacco users
        if (user.details?.tobaccoUser) {
          tobaccoUsers++;
        }

        // Employee class breakdown
        const employeeClass = user.details?.employeeClassType || 'Unknown';
        employeeClassBreakdown[employeeClass] = (employeeClassBreakdown[employeeClass] || 0) + 1;
      });

      return {
        totalEmployees: users.length,
        averageAge: ageCount > 0 ? Math.round(totalAge / ageCount) : 0,
        dependentCount,
        tobaccoUsers,
        employeeClassBreakdown
      };
    } catch (error) {
      console.error("Error calculating company demographics:", error);
      return {
        totalEmployees: 0,
        averageAge: 0,
        dependentCount: 0,
        tobaccoUsers: 0,
        employeeClassBreakdown: {}
      };
    }
  }

  // 🔐 Data Validation Methods
  public static validateSSN(ssn: string): boolean {
    if (!ssn) return true; // Allow empty for backward compatibility
    return /^(\d{3}-?\d{2}-?\d{4})$/.test(ssn);
  }

  public static validateAddress(address: AddressInterface): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!address.street1) errors.push('Street address is required');
    if (!address.city) errors.push('City is required');
    if (!address.state) errors.push('State is required');
    if (!address.zipCode) errors.push('ZIP code is required');

    if (address.state && !/^[A-Z]{2}$/.test(address.state)) {
      errors.push('State must be a valid 2-character abbreviation');
    }

    if (address.zipCode && !/^\d{5}(-\d{4})?$/.test(address.zipCode)) {
      errors.push('ZIP code must be in format XXXXX or XXXXX-XXXX');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  public static validateDependent(dependent: DependentInterface): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!dependent.name) errors.push('Dependent name is required');
    if (!dependent.gender) errors.push('Dependent gender is required');
    if (!dependent.dateOfBirth) errors.push('Dependent date of birth is required');
    if (!dependent.relationship) errors.push('Dependent relationship is required');

    if (dependent.ssn && !this.validateSSN(dependent.ssn)) {
      errors.push('Invalid SSN format for dependent');
    }

    if (dependent.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(dependent.email)) {
      errors.push('Invalid email format for dependent');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
UserModelClass.initializeModel();

export default UserModelClass;
