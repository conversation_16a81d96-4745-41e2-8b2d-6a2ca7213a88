# 🎯 Comprehensive Pre-Enrollment System Test Suite

A comprehensive testing suite for the QHarmony pre-enrollment system that tests carriers, plans, company settings, and plan assignments with extensive edge case coverage.

## 📋 What This Test Suite Covers

### ✅ **Carrier Operations**
- Create broker-specific carriers
- Validate carrier uniqueness (name & code)
- Test invalid coverage type/subtype combinations
- Carrier activation/deactivation
- Access control validation

### ✅ **Plan Operations**
- Create plans with system carriers
- Create plans with broker carriers
- Plan uniqueness validation
- Plan activation/deactivation
- Invalid carrier assignment validation

### ✅ **Company & User Management**
- Create test employer company under broker
- Create employee users for broker company
- Role-based access control testing

### ✅ **Company Benefits Settings**
- Update broker company settings
- Update employer company settings
- Fetch and validate settings

### ✅ **Plan Assignments**
- Assign plans to broker's own company
- Assign plans to employer companies
- Duplicate assignment validation
- Assignment retrieval by company

## 🚀 Quick Start

### Prerequisites
- Node.js 14+ installed
- QHarmony backend server running on `http://localhost:8080`
- Existing broker user with the following details:
  - User ID: `6838677aef6db0212bcfdacd`
  - Company ID: `6838677aef6db0212bcfdacb`
  - Email: `<EMAIL>`

### Installation & Execution

1. **Navigate to backend directory:**
   ```bash
   cd qharmony-backend-v1
   ```

2. **Install dependencies (if not already installed):**
   ```bash
   npm install
   ```

3. **Start the backend server (in separate terminal):**
   ```bash
   npm run dev
   ```

4. **Run comprehensive tests:**
   ```bash
   npm test
   ```

5. **Alternative execution methods:**
   ```bash
   # Run specific test modules
   npm run test:carriers
   npm run test:plans
   npm run test:assignments
   
   # Direct execution
   node tests/comprehensive_pre_enrollment_test.js
   ```

## 📊 Test Data Structure

The test suite creates and manages the following test data:

### 👥 **Users Created**
- 5 broker employees (`@benosphere.com` domain)

### 🏢 **Companies Created**
- 1 test employer company under the broker

### 🚛 **Carriers Created**
- 1 broker-specific carrier with full configuration

### 📋 **Plans Created**
- 1 plan with system carrier (Kaiser)
- 1 plan with broker carrier

### 📌 **Plan Assignments Created**
- 1 assignment to broker's own company
- 1 assignment to employer company

## 🔍 Test Execution Flow

```
Phase 1: User Creation
├── Create 5 broker employees
└── Validate user creation

Phase 2: Carrier Operations
├── Create broker carrier
├── Test uniqueness validation
├── Test invalid combinations
├── Test activation/deactivation
└── Fetch all carriers

Phase 3: Plan Operations
├── Create plan with system carrier
├── Create plan with broker carrier
├── Test plan activation
└── Validate plan operations

Phase 4: Company Creation
├── Create test employer company
└── Validate company creation

Phase 5: Company Benefits Settings
├── Update broker company settings
└── Validate settings operations

Phase 6: Plan Assignments
├── Assign plan to broker company
├── Assign plan to employer company
└── Validate assignment operations
```

## 📈 Expected Results

### ✅ **Successful Operations**
- All valid operations should complete successfully
- Proper data creation and relationships
- Correct access control enforcement

### ❌ **Expected Failures (Validation Working)**
- Duplicate name/code attempts
- Invalid coverage combinations
- Unauthorized access attempts
- Missing required fields

## 🛠️ Configuration

### Environment Variables
The test suite uses the following configuration:

```javascript
const BASE_URL = 'http://localhost:8080';
const BROKER_USER_ID = '6838677aef6db0212bcfdacd';
const BROKER_COMPANY_ID = '6838677aef6db0212bcfdacb';
const BROKER_EMAIL = '<EMAIL>';
const DOMAIN = 'benosphere.com';
```

### System Carriers
The test suite references these existing system carriers:
- Kaiser Permanente: `683b406283324ea4fc21b8a2`
- Aetna: `683b407683324ea4fc21b8a3`
- Cigna: `683b408383324ea4fc21b8a4`
- UnitedHealth: `683b40ac83324ea4fc21b8a5`

## 📋 Test Output

The test suite provides detailed logging including:
- ✅ Success indicators for passed tests
- ❌ Error indicators for failed tests
- 🔍 Detailed operation logs
- 📊 Comprehensive summary with statistics

## 🎯 Usage Notes

1. **Server Must Be Running**: Ensure the QHarmony backend is running on port 8080
2. **Clean State**: Tests create new data but don't clean up (for inspection)
3. **Rate Limiting**: Built-in delays prevent API rate limiting
4. **Error Handling**: Comprehensive error catching and reporting
5. **Modular Design**: Individual test phases can be run separately

## 🚀 Ready to Test!

Run `npm test` to execute the comprehensive test suite and validate your pre-enrollment system implementation!
