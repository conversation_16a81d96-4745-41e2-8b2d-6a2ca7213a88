import axios from 'axios';
import EnvService from './env.service';
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface ChatResponse {
  message: string;
}

export class OpenAiService {
  private apiUrl = 'https://api.openai.com/v1/chat/completions';
  private threadUrl = 'https://api.openai.com/v1/threads';

  async createRun(threadId: string, assistantId: string) {
    const API_KEY = EnvService.env().OPENAI_API_KEY;

    try {
      const run = await axios.post(
        `${this.threadUrl}/${threadId}/runs`,
        {
          assistant_id: assistantId,
        },
        {
          headers: {
            Authorization: `Bearer ${API_KEY}`,
            'Content-Type': 'application/json',
            'OpenAI-Beta': 'assistants=v1',
          },
        }
      );
      console.log('🚀 ~ OpenAiService ~ createRun ~ run:', run.data);
      let runStatus = await axios.get(
        `${this.threadUrl}/${threadId}/runs/${run.data.id}`,
        {
          headers: {
            Authorization: `Bearer ${API_KEY}`,
            'OpenAI-Beta': 'assistants=v1',
          },
        }
      );
      console.log(
        '🚀 ~ OpenAiService ~ createRun ~ runStatus:',
        runStatus.data
      );
      // While the run is not complete keep on running it
      while (runStatus.data.status !== 'completed') {
        runStatus = await axios.get(
          `${this.threadUrl}/${threadId}/runs/${run.data.id}`,
          {
            headers: {
              Authorization: `Bearer ${API_KEY}`,
              'OpenAI-Beta': 'assistants=v1',
            },
          }
        );
      }
      console.log(
        '🚀 ~ OpenAiService ~ createRun ~ runStatus: after going through the while loop',
        runStatus.data
      );
      // Retrieve the message added to the assistant
      if (runStatus.data.status === 'completed') {
        const response = await axios.get(
          `${this.threadUrl}/${threadId}/messages`,
          {
            headers: {
              Authorization: `Bearer ${API_KEY}`,
              'Content-Type': 'application/json',
              'OpenAI-Beta': 'assistants=v1',
            },
          }
        );

        return response.data.data[0].content[0].text.value;
      }
      return undefined;
    } catch (error) {
      throw new Error(`Error creating run: ${error.message}`);
    }
  }

  async sendMessage(messages: ChatMessage[]): Promise<string> {
    const API_KEY = EnvService.env().OPENAI_API_KEY;
    try {
      const response = await axios.post(
        this.apiUrl,
        {
          model: 'gpt-3.5-turbo',
          messages: messages,
        },
        {
          headers: {
            Authorization: `Bearer ${API_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data.choices[0].message.content;
    } catch (error) {
      console.error('Error sending message to Chat GPT:', error); // TODO : Make a more robust method to deal with error
      return 'Sorry, I am unable to process your request at the moment.';
    }
  }

  async printMessages(threadId: any) {
    const API_KEY = EnvService.env().OPENAI_API_KEY;
    console.log('🚀 ~ printMessages ~ API KEY:', API_KEY);
    const threadContent = await axios.get(
      `https://api.openai.com/v1/threads/${threadId}/messages`,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${API_KEY}`,
          'OpenAI-Beta': 'assistants=v1',
        },
      }
    );

    console.log('🚀 ~ printMessages ~ threadContent:');
    threadContent.data.data.forEach((item: any) => {
      console.log('🚀 ~ printMessages ~ item.role', item.role);
      console.log('🚀 ~ printMessages ~ item.content', item.content);
    });
  }
}
