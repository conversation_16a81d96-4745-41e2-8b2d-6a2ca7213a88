{"name": "qharmony-scripts", "version": "1.0.0", "description": "QHarmony database update scripts", "main": "update-sbs-user-details.js", "scripts": {"test-connection": "node test-connection.js", "validate": "node validate-csv.js", "update-sbs": "node update-sbs-user-details.js", "update-sbs-custom": "node update-sbs-user-details.js"}, "dependencies": {"mongoose": "^7.0.0", "csv-parser": "^3.0.0", "dotenv": "^16.0.0"}, "devDependencies": {}, "keywords": ["qharmony", "database", "user-update", "csv-import"], "author": "QHarmony Development Team", "license": "ISC"}