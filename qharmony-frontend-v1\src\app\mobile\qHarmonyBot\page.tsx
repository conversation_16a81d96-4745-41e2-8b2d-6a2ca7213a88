"use client";

import { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Typography,
  Grid,
  Paper,
  TextField,
  Button,
  List,
  ListItem,
  ListItemText,
  Avatar,
} from "@mui/material";
import PersonIcon from "@mui/icons-material/Person";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import { RootState } from "../../../redux/store";
import {
  addMessage,
  ChatMessage,
  setIsLoading,
} from "../../../redux/reducers/qHarmonyBotSlice";
import { sendChatMessage } from "../../../middleware/chatbot_middleware";
import { keyframes } from "@emotion/react";
import {
  getUsersCompanyId,
  clearSelectedFAQQuestion,
} from "@/redux/reducers/userSlice";
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";
import assistant_image from "../../../../public/ai_assistant.png"; // Import the new image
import Image from "next/image";
import withMobileEdgeFill from "@/components/mobile_edge_fill";

const QHarmonyBotPage = () => {
  const dispatch = useDispatch();

  const companyId = useSelector((state: RootState) => getUsersCompanyId(state));
  const userId = useSelector((state: RootState) => state.user._id);
  const userDetails = useSelector((state: RootState) => state.user.userProfile);
  const selectedFAQQuestion = useSelector(
    (state: RootState) => state.user.selectedFAQQuestion,
  );
  const chatHistory = useSelector(
    (state: RootState) => state.qHarmonyBot.chatHistory,
  );
  const isLoading = useSelector(
    (state: RootState) => state.qHarmonyBot.isLoading,
  );
  const [inputMessage, setInputMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const handleSendMessage = (message: string) => {
    if (message.trim() === "") return;

    const newMessage: ChatMessage = {
      sender: "user",
      message: message.replace(/\n/g, "<br/>"),
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(newMessage));
    dispatch(setIsLoading(true));
    sendChatMessage(dispatch, message, userId, companyId);
    setInputMessage("");
  };

  const getInitials = (name: string) => {
    if (!name) return ""; // Handle empty or undefined name
    const [firstName, lastName] = name.split(" ");
    return `${firstName[0].toUpperCase()}${
      lastName ? lastName[0].toUpperCase() : ""
    }`;
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage(inputMessage);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (selectedFAQQuestion) {
      handleSendMessage(selectedFAQQuestion);
      dispatch(clearSelectedFAQQuestion());
    }
  }, [selectedFAQQuestion, dispatch]);

  useEffect(() => {
    if (chatHistory.length === 0 && userDetails.name && !selectedFAQQuestion) {
      dispatch(setIsLoading(true));
      setTimeout(() => {
        const defaultMessage: ChatMessage = {
          sender: "bot",
          message: `Hey ${userDetails.name}, how can I help you today?`,
          timestamp: new Date().toISOString(),
        };
        dispatch(addMessage(defaultMessage));
        dispatch(setIsLoading(false));
      }, 2000);
    }
  }, [chatHistory.length, userDetails.name, dispatch, selectedFAQQuestion]);

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory]);

  const typingAnimation = keyframes`
    0% { content: ''; }
    25% { content: '.'; }
    50% { content: '..'; }
    75% { content: '...'; }
    100% { content: ''; }
  `;

  const quickReplies = [
    "View My Benefits",
    "Enroll Now",
    "Check Time Off",
    "Update My Elections",
  ];

  const LoadingIndicator = () => (
    <ListItem sx={{ display: "flex", alignItems: "flex-start" }}>
      <Image
        src={assistant_image}
        alt="AI Chat"
        style={{
          borderRadius: "50%",
          width: "40px",
          height: "40px",
          marginRight: "10px",
        }}
      />
      <ListItemText
        primary={
          <Typography
            component="span"
            sx={{
              display: "inline-block",
              fontSize: "16px",
              "&::after": {
                content: "''",
                animation: `${typingAnimation} 1.5s infinite`,
                display: "inline-block",
                width: "16px",
              },
            }}
          >
            Brea is typing
          </Typography>
        }
        sx={{
          bgcolor: "#fff",
          borderRadius: "20px",
          p: 1.5,
          mb: 1,
          maxWidth: "80%",
          mt: -0.5,
        }}
      />
    </ListItem>
  );

  return (
    <ProtectedRoute>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: "flex",
          flexDirection: "column",
          height: "95vh",
          bgcolor: "#f6f8fc",
        }}
      >
        <Typography variant="h5" sx={{ fontWeight: "bold", p: 2 }}>
          Brea - Your Round-the-Clock Benefits Expert
        </Typography>

        <Box sx={{ flexGrow: 1, overflow: "auto" }}>
          <List>
            {chatHistory.map((chat, index) => (
              <ListItem
                key={index}
                sx={{
                  display: "flex",
                  flexDirection: chat.sender === "user" ? "row-reverse" : "row",
                  alignItems: "flex-start",
                }}
              >
                <Avatar
                  sx={{
                    bgcolor: chat.sender === "user" ? "#000" : "transparent",
                    mr: chat.sender === "user" ? 0 : 2,
                    ml: chat.sender === "user" ? 2 : 0,
                    mt: 0.5,
                  }}
                >
                  {chat.sender === "user" ? (
                    <Avatar
                      sx={{
                        bgcolor: "black",
                        color: "#ffffff",
                        width: 35,
                        height: 35,
                        fontSize: "1.2rem",
                        mr: 1.5,
                        ml: 1.5,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        paddingBottom: "1px",
                        fontWeight: 800,
                      }}
                    >
                      {getInitials(userDetails.name)}
                    </Avatar>
                  ) : (
                    <Image
                      src={assistant_image}
                      alt="AI Assistant"
                      style={{
                        borderRadius: "50%",
                        width: "40px",
                        height: "40px",
                      }}
                    />
                  )}
                </Avatar>
                <Box sx={{ maxWidth: "80%" }}>
                  <ListItemText
                    primary={
                      <span
                        dangerouslySetInnerHTML={{
                          __html:
                            chat.sender === "bot"
                              ? `${chat.message}<br/><small style="color: gray;">AI-generated content—verify before use.</small>`
                              : chat.message,
                        }}
                        style={{
                          whiteSpace: "pre-wrap",
                          wordBreak: "break-word",
                        }}
                      />
                    }
                    sx={{
                      bgcolor: chat.sender === "user" ? "#000" : "#fff",
                      color: chat.sender === "user" ? "#fff" : "#000",
                      borderRadius:
                        chat.sender === "user"
                          ? "16px 16px 4px 16px"
                          : "16px 16px 16px 4px",
                      padding: "10px",
                      mb: 1,
                    }}
                  />
                  {chat.sender === "bot" &&
                    chat.message.includes("how can I help you today?") &&
                    index === chatHistory.length - 1 && (
                      <Box sx={{ display: "flex", mt: 1 }}>
                        {quickReplies.map((reply) => (
                          <Button
                            key={reply}
                            variant="contained"
                            sx={{
                              mr: 1,
                              textTransform: "none",
                              fontSize: "12px",
                              borderRadius: "6px",
                              px: 2,
                              py: 1,
                              bgcolor: "black",
                              color: "white",
                            }}
                            onClick={() => handleSendMessage(reply)}
                          >
                            {reply}
                          </Button>
                        ))}
                      </Box>
                    )}
                </Box>
              </ListItem>
            ))}
            {isLoading && <LoadingIndicator />}
            <div ref={messagesEndRef} />
          </List>
        </Box>

        <Box sx={{ p: 2, borderTop: "1px solid #e0e0e0" }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "flex-start",
              bgcolor: "#fff",
              borderRadius: "20px",
              height: "150px",
              boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
            }}
          >
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Type your message..."
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              multiline
              rows={5}
              sx={{
                mr: 2,
                borderRadius: "20px",
                fontSize: "16px",
                height: "150px",
                "& .MuiOutlinedInput-root": {
                  padding: "12px",
                  border: "none",
                  display: "flex",
                  alignItems: "flex-start",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
              }}
            />
            <Button
              variant="contained"
              sx={{
                bgcolor: inputMessage ? "#1073ff" : "#e0e0e0",
                color: "#fff",
                borderRadius: "25px",
                m: 2,
                p: 0,
                fontSize: "16px",
                height: "45px",
                width: "60px",
                boxShadow: "none",
                textTransform: "none",
                "&:hover": {
                  bgcolor: inputMessage ? "#005bb5" : "#d0d0d0",
                },
              }}
              onClick={() => handleSendMessage(inputMessage)}
            >
              Send
            </Button>
          </Box>
        </Box>
      </Box>
    </ProtectedRoute>
  );
};

export default withMobileEdgeFill(QHarmonyBotPage);
