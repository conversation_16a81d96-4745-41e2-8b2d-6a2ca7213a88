/**
 * Environment utilities for QHarmony Frontend
 * Centralized access to environment variables
 */

// =============================================================================
// API Configuration
// =============================================================================
export const getApiBaseUrl = (): string => {
  return process.env.NEXT_PUBLIC_API_URL!;
};

// =============================================================================
// User Configuration
// =============================================================================
export const getUserId = (): string => {
  const primaryKey = process.env.NEXT_PUBLIC_USER_ID_KEY || "userid1";
  const altKey = process.env.NEXT_PUBLIC_USER_ID_ALT_KEY || "userId";

  const userId = localStorage.getItem(primaryKey) || localStorage.getItem(altKey);

  // 🔍 DEBUG: Log what we're getting
  console.log('🔍 getUserId Debug:', {
    primaryKey,
    altKey,
    primaryValue: localStorage.getItem(primaryKey),
    altValue: localStorage.getItem(altKey),
    finalUserId: userId
  });

  if (!userId) {
    throw new Error('User ID not found in localStorage. Please authenticate first.');
  }

  return userId;
};

export const getAdminEmails = (): string[] => {
  const adminEmailsString = process.env.NEXT_PUBLIC_ADMIN_EMAILS!;
  return adminEmailsString.split(',').map(email => email.trim());
};

// =============================================================================
// AI/Chatbot Configuration
// =============================================================================
export const getChatbotUrl = (): string => {
  return process.env.NEXT_PUBLIC_CHATBOT_URL!;
};

// =============================================================================
// External Services Configuration
// =============================================================================
export const getTeamsBotDevUrl = (): string => {
  return process.env.NEXT_PUBLIC_TEAMS_BOT_DEV_URL!;
};

export const getTeamsBotProdUrl = (): string => {
  return process.env.NEXT_PUBLIC_TEAMS_BOT_PROD_URL!;
};

export const getFinchApiUrl = (): string => {
  return process.env.NEXT_PUBLIC_FINCH_API_URL!;
};

export const getFrontendUrl = (): string => {
  return process.env.NEXT_PUBLIC_FRONTEND_URL!;
};

// =============================================================================
// Image Domains Configuration
// =============================================================================
export const getS3Domain = (): string => {
  return process.env.NEXT_PUBLIC_S3_DOMAIN!;
};

export const getAzureBlobDomain = (): string => {
  return process.env.NEXT_PUBLIC_AZURE_BLOB_DOMAIN!;
};

export const getImageDomains = (): string[] => {
  return [getS3Domain(), getAzureBlobDomain()];
};

// =============================================================================
// Firebase Configuration
// =============================================================================
export const getFirebaseConfig = () => {
  // Check if we're in browser environment and use hostname-based detection
  // This matches the existing firebase.js logic
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;

    if (hostname.includes('test.benosphere.com')) {
      // Test Environment - qharmony-test project
      return {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_TEST_API_KEY!,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_TEST_AUTH_DOMAIN!,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_TEST_PROJECT_ID!,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_TEST_STORAGE_BUCKET!,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_TEST_MESSAGING_SENDER_ID!,
        appId: process.env.NEXT_PUBLIC_FIREBASE_TEST_APP_ID!,
      };
    }
  }

  // Production Environment - qharmony-dev project (default)
  return {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_PROD_API_KEY!,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_PROD_AUTH_DOMAIN!,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROD_PROJECT_ID!,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_PROD_STORAGE_BUCKET!,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_PROD_MESSAGING_SENDER_ID!,
    appId: process.env.NEXT_PUBLIC_FIREBASE_PROD_APP_ID!,
  };
};

// =============================================================================
// Environment Validation
// =============================================================================
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check required environment variables
  if (!process.env.NEXT_PUBLIC_API_URL) {
    errors.push('NEXT_PUBLIC_API_URL is required');
  }



  if (!process.env.NEXT_PUBLIC_ADMIN_EMAILS) {
    errors.push('NEXT_PUBLIC_ADMIN_EMAILS is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
