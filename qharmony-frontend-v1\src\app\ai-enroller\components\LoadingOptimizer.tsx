'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const LoadingOptimizer = () => {
  const router = useRouter();

  useEffect(() => {
    // Preload critical routes immediately
    const preloadRoutes = [
      '/ai-enroller/create-plan',
      '/ai-enroller/plans',
      '/ai-enroller/manage-groups',
      '/ai-enroller/employee-enrol'
    ];

    // Preload routes with a small delay to not block initial render
    const preloadTimer = setTimeout(() => {
      preloadRoutes.forEach(route => {
        router.prefetch(route);
      });
    }, 100);

    // Preload images that might be used
    const preloadImages = [
      '/brea.png'
    ];

    preloadImages.forEach(src => {
      const img = new Image();
      img.src = src;
    });

    // Preload fonts if not already loaded
    if (document.fonts) {
      document.fonts.load('400 16px SF Pro').catch(() => {
        // Font loading failed, but continue
      });
      document.fonts.load('500 16px SF Pro').catch(() => {
        // Font loading failed, but continue
      });
      document.fonts.load('600 16px SF Pro').catch(() => {
        // Font loading failed, but continue
      });
    }

    return () => {
      clearTimeout(preloadTimer);
    };
  }, [router]);

  return null; // This component doesn't render anything
};

export default LoadingOptimizer;
