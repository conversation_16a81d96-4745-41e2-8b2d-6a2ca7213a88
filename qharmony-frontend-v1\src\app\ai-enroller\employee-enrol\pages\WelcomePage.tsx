'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { BotQuestion } from '../components/BotQuestion';

interface WelcomePageProps {
  onNext: () => void;
}

export const WelcomePage: React.FC<WelcomePageProps> = ({ onNext }) => {
  return (
    <div className="space-y-6">
      {/* Bot Question */}
      <div className="flex gap-3">
        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
          <Bot className="w-5 h-5 text-blue-600" />
        </div>
        <div className="flex-1">
          <div className="bg-gray-100 rounded-2xl px-4 py-3">
            <p className="text-gray-900 font-medium">
              👋 Hi there! Ready to find the perfect benefits for 2025?
            </p>
            <p className="text-gray-600 text-sm mt-1">
              I&apos;m here to make this super easy. I&apos;ll ask smart questions and recommend the best plans for your situation.
            </p>
          </div>
        </div>
      </div>

      {/* Main Welcome Card */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Bell className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl font-semibold">Welcome to Your AI Benefits Assistant!</h2>
            <Sparkles className="w-5 h-5 text-yellow-500" />
          </div>

          <div className="space-y-4">
            <p className="text-lg">👋 Hi there! It&apos;s time to enroll in your 2025 Medical, Dental, and Vision benefits.</p>

            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <p className="font-semibold text-red-800">⏰ Deadline: December 15, 2024</p>
            </div>

            <div className="space-y-3">
              <p className="font-medium">🧠 I&apos;m your personalized benefits assistant. I&apos;ll:</p>
              <ul className="list-disc list-inside space-y-2 ml-4 text-gray-700">
                <li>Ask smart questions to understand your needs</li>
                <li>Recommend the best plans for your situation</li>
                <li>Show you helpful videos explaining each plan</li>
                <li>Answer any questions you have</li>
                <li>Help you compare plans side-by-side</li>
              </ul>
            </div>

            <div className="flex gap-2 flex-wrap pt-4">
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                Ask Questions
              </button>
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                📺 Watch Video
              </button>
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                📊 Compare Plans
              </button>
            </div>

            <div className="pt-4">
              <button
                onClick={onNext}
                className="w-full bg-gray-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-900 transition-colors flex items-center justify-center gap-2"
              >
                🚀 Start My Smart Enrollment
              </button>
              <p className="text-sm text-gray-500 text-center mt-2">This will take about 5-10 minutes</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
