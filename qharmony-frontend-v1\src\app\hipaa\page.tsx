// pages/benosphere-controls.tsx
"use client";
import { useState, useEffect } from 'react';
import styles from './benosphere.module.css';
import Image from 'next/image';
import Logo from '../../../public/logo1.png';

export default function BenosphereControls() {
  const [activeFilter, setActiveFilter] = useState('All');
  const categories = ['All', 'Product', 'Infrastructure', 'Organizational'];

  useEffect(() => {
    // Filter table rows based on selected category
    const rows = document.querySelectorAll('tr[data-category]');
    rows.forEach(row => {
      if (activeFilter === 'All' || row.getAttribute('data-category') === activeFilter) {
        (row as HTMLElement).style.display = '';
      } else {
        (row as HTMLElement).style.display = 'none';
      }
    });
  }, [activeFilter]);

  return (
    <>
      <div className={styles.container}>
        <header className={styles.header}>
          <h1 className={styles.title}>🔐 HIPAA Compliance at BenOsphere</h1>
          
          <div className={styles.infoBox}>
            <p className={styles.infoText}>
              At BenOsphere, protecting employee health information is a top priority. As a platform trusted to handle sensitive benefits and wellness data,
              we are fully committed to maintaining HIPAA-compliant systems, policies, and processes. Our HIPAA compliance program has been reviewed and attested by the team at <a href="https://www.compliancy.com/" className={styles.link} target="_blank" rel="noopener noreferrer">Compliancy</a>, an industry-recognized third-party compliance partner.
            </p>
            <p className={styles.infoText}>
              We understand the responsibility that comes with managing protected health information (PHI), and we take that responsibility seriously.
              From secure infrastructure to strict access controls, every layer of BenOsphere is designed with privacy and compliance in mind.
            </p>
            <p className={styles.infoText}>
              Have questions about our HIPAA practices? 📩 <a href="mailto:<EMAIL>" className={styles.link}><EMAIL></a>
            </p>
          </div>
        </header>

        <div className={styles.filterContainer}>
          {categories.map(category => (
            <button 
              key={category}
              className={`${styles.filterButton} ${activeFilter === category ? styles.filterButtonActive : ''}`}
              onClick={() => setActiveFilter(category)}
            >
              {category}
            </button>
          ))}
        </div>

        <main>
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead>
                <tr className={styles.tr}>
                  <th className={styles.th}></th>
                  <th className={styles.th}>Control</th>
                  <th className={styles.th}>Category</th>
                  <th className={styles.th}>Description</th>
                </tr>
              </thead>
              <tbody>
                {/* Keep your existing table rows, just update the class names */}
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Customer Data Deletion Upon Termination</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>Customer data is securely deleted when no longer needed or upon contract termination in accordance with data retention policies.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Data Classification</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>Data is classified by sensitivity and handled accordingly to ensure appropriate levels of protection.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Designated Security Officials</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Designated Security Officials.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Contractor Requirements</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Contractor Requirements.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Credential Keys Managed</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Credential Keys Managed.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Cryptography Policies</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Cryptography Policies.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>System Access Granted</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>System access is managed using a role-based model and is revoked immediately upon employee or contractor termination.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Terminated Employee Access Revoked Within One Business Day</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>System access is managed using a role-based model and is revoked immediately upon employee or contractor termination.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Unique Accounts Used</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Unique Accounts Used.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Unique SSH</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Public SSH access is disabled. Only authenticated and authorized users with unique credentials can access systems via secure protocols.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Users Can Access All Their Information</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>Each user is provisioned with a unique account. Access is monitored and governed by least privilege principles.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Users Can Update their Information</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>Users can view and update their information through a secure, authenticated interface in compliance with data accuracy standards.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>VPN Required for Production Access</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Production systems are only accessible through a secure VPN to protect internal assets from unauthorized external access.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Vulnerability Management</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Vulnerability Management.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Inactivity and Browser Exit Logout</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Least-Privileged Policy for Customer Data Access</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>A formal Least-Privileged Policy for Customer Data Access is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Log Management System</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Logging/Monitoring</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Logs Centrally Stored</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Malware Detection Software Installed</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Malware Detection Software Installed.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Multiple Availability Zones</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Multiple Availability Zones.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Network segmentation in place</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Network segmentation in place.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Operational Audit</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Oversight of Security Controls</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Oversight of Security Controls.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Password Manager</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Password Manager.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Password Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Password Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Password Storage</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Password Storage.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Removable Media Device Encryption</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Require Authentication for Access</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>System access is managed using a role-based model and is revoked immediately upon employee or contractor termination.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Require Encryption of Web-Based Admin Access</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Role-Based Security Implementation</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Role-Based Security Implementation.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Servers Monitored and Alarmed</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Session Lock</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Session Lock.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>SSL/TLS Enforced</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: SSL/TLS Enforced.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Activity Review</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Annual Access Control Review</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Annual Incident Response Test</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A comprehensive incident response plan exists and is tested annually to ensure swift action during security events.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Annual Penetration Tests</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Annual Penetration Tests.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Architectural Diagram</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Architectural Diagram.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Authentication Protocol</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Authentication Protocol.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Backup Integrity and Completeness</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Backup Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Customer Data is Encrypted at Rest</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Customer Data is Encrypted at Rest.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Customer Data Policies</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Customer Data Policies.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Daily Backup Statuses Monitored</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Data Destruction Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Data Destruction Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Data Retention Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Data Retention Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Database Monitored and Alarmed</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Denial of Public SSH</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Public SSH access is disabled. Only authenticated and authorized users with unique credentials can access systems via secure protocols.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Disaster Recovery Plan</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Business continuity and disaster recovery plans are in place to minimize disruption during unforeseen incidents.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Disposal of Sensitive Data on Hardware</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Disposal of Sensitive Data on Hardware.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Disposal of Sensitive Data on Paper</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Disposal of Sensitive Data on Paper.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Encryption Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Event Logging</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Failed Backup Alert and Action</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>FIM (File Integrity Monitoring) Software in Place</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Hard-Disk Encryption</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Hardening Standards in Place</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Hardening Standards in Place.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Document Retention Period</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Data retention is governed by clearly defined policies ensuring legal and operational requirements are met.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Employee Disclosure Process</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Employee Disclosure Process.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Follow-Ups Tracked</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Follow-Ups Tracked.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>HIPAA Awareness Training</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>All employees and contractors complete regular HIPAA and cybersecurity training to ensure awareness of responsibilities and threats.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Incident Response Team</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A comprehensive incident response plan exists and is tested annually to ensure swift action during security events.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Incident Response Plan</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A comprehensive incident response plan exists and is tested annually to ensure swift action during security events.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Termination/Offboarding Checklist</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Termination/Offboarding Checklist.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>3rd Parties and Vendors Given Instructions on Breach Reporting</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Acceptable Use Policy Employees Acknowledge</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Acceptable Use Policy Employees Acknowledge is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Allowable Use and Disclosure</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Allowable Use and Disclosure.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Annual Review of Purposes</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Asset Management Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Asset Management Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Background Checks</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Background Checks.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Business Associate Agreements</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Business Associate Agreements.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Business Continuity Plan</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Business continuity and disaster recovery plans are in place to minimize disruption during unforeseen incidents.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Business Impact Analysis</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Business Impact Analysis.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Breach Notification</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Breach Notification.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Board Oversight Briefings Conducted</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Board Oversight Briefings Conducted.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Code of Conduct</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Code of Conduct.</td>
                </tr>
            
                <tr data-category="Product">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Commitments Explained to Customers</td>
                    <td className={`${styles.td} ${styles.category}`}>Product</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Commitments Explained to Customers.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Communication to 3rd Parties</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Communication to 3rd Parties.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Conduct Control Self-Assessments</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Conduct Control Self-Assessments.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Continuous Control Monitoring</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Defined Management Roles &amp; Responsibilities</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Defined Management Roles &amp; Responsibilities.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Data Protection Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Data Protection Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>DLP (Data Loss Prevention) Software is Used</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: DLP (Data Loss Prevention) Software is Used.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Disclosure with 3rd Parties</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Disclosure with 3rd Parties.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Disclosure Process for Customers</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Disclosure Process for Customers.</td>
                </tr>
                
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Information Security Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Information Security Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Information Security Skills Matrix</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Information Security Skills Matrix.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Maintains a Privacy Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Maintains a Privacy Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Maintains Asset Inventory</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Maintains Asset Inventory.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Messaging Queues Monitored and Alarmed</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Notice of Breach to Affected Users</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Each user is provisioned with a unique account. Access is monitored and governed by least privilege principles.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>PII with 3rd Parties and Vendors</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Privacy Policy Includes 3rd Party Vendors</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Privacy Policy Includes 3rd Party Vendors is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Privacy Policy Publicly Available</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Privacy Policy Publicly Available is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Privacy, Use, and Disclosure</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Privacy, Use, and Disclosure.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Provide Notice of Privacy Practices</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Provide Notice of Privacy Practices.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Quarterly Review of Privacy Compliance</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Remediation Plan</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Remediation Plan.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Review Privacy Notice Annually</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Risk Assessment Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Risk Assessment Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Security Team Communicates in a Timely Manner</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A dedicated security team oversees compliance efforts and ensures accountability at the organizational level.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Security Team/Steering Committee</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A dedicated security team oversees compliance efforts and ensures accountability at the organizational level.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Security Training</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>All employees and contractors complete regular HIPAA and cybersecurity training to ensure awareness of responsibilities and threats.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Security Updates</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Security Updates.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Software Development Life Cycle Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Software Development Life Cycle Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Storage of Sensitive Data on Paper</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Storage of Sensitive Data on Paper.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>System Access Control Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal System Access Control Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Unauthorized Disclosures by 3rd Parties</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Unauthorized Disclosures by 3rd Parties.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Vendor Agreements Maintained</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Vendor Compliance Reports</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Vendor Management Policy</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>A formal Vendor Management Policy is in place to govern consistent and secure operations across the organization.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Vendors and PHI</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards.</td>
                </tr>
            
                <tr data-category="Organizational">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Annual Risk Assessment</td>
                    <td className={`${styles.td} ${styles.category}`}>Organizational</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Annual Risk Assessment.</td>
                </tr>
            
                <tr data-category="Infrastructure">
                    <td className={`${styles.td} ${styles.checkmark}`}>&#10003;</td>
                    <td className={styles.td}>Intrusion Detection System in Place</td>
                    <td className={`${styles.td} ${styles.category}`}>Infrastructure</td>
                    <td className={styles.td}>BenOsphere has implemented and verified control for: Intrusion Detection System in Place.</td>
                </tr>
              </tbody>
            </table>
          </div>
        </main>
      </div>
    </>
  );
}
