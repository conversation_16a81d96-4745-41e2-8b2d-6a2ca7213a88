import mongoose, { Document, Model, UpdateWriteOpResult } from 'mongoose';
import {
  PAYROLL_FREQUENCIES,
  ENROLLMENT_PERIOD_TYPES
} from '../../constants';

const { Schema } = mongoose;

// Global Eligibility Interface
export interface GlobalEligibility {
  payrollFrequency: string;     // "Weekly", "Biweekly", "Semi-Monthly", "Monthly"
  firstPayrollDate?: Date;      // First payroll date
  defaultWaitingPeriod?: string; // "First of month after 30 days", etc.
  rehirePolicy?: string;        // Policy for rehired employees
  allowCustomPayrollFrequency?: boolean; // Allow employees to override company payroll frequency
}

// Note: Contribution policies moved to PlanAssignment model for plan-specific control

// Enrollment Period Interface
export interface EnrollmentPeriod {
  _id?: mongoose.Types.ObjectId;
  type: string;                 // "Open Enrollment", "New Hire", "Qualifying Life Event"
  startDate: Date;
  endDate: Date;
  coverageStartDate: Date;
  coverageEndDate: Date;
  description?: string;
  isActive: boolean;
}

// Company Preferences Interface
export interface CompanyPreferences {
  allowEmployeeBenefitChanges?: boolean;
  requireBeneficiaryDesignation?: boolean;
  enableDependentVerification?: boolean;
  autoEnrollNewHires?: boolean;
  defaultCoverageLevel?: string;
}

// Main CompanyBenefitsSettings Data Interface
export interface CompanyBenefitsSettingsDataInterface {
  _id?: mongoose.Types.ObjectId;

  // Company Reference
  companyId: string;            // Reference to Company model

  // Global eligibility rules
  globalEligibility: GlobalEligibility;

  // Note: Contribution policies moved to PlanAssignment model for plan-specific control

  // Enrollment periods
  enrollmentPeriods: EnrollmentPeriod[];

  // Company preferences and settings
  companyPreferences?: CompanyPreferences;

  // System fields
  isActive: boolean;

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

// Updatable fields interface
export interface UpdateableCompanyBenefitsSettingsDataInterface {
  globalEligibility?: Partial<GlobalEligibility>;
  enrollmentPeriods?: EnrollmentPeriod[];
  companyPreferences?: CompanyPreferences;
  isActive?: boolean;
}

interface CompanyBenefitsSettingsDocument extends Document, Omit<CompanyBenefitsSettingsDataInterface, '_id'> {}

class CompanyBenefitsSettingsModelClass {
  private static companyBenefitsSettingsModel: Model<CompanyBenefitsSettingsDocument>;

  public static initializeModel() {
    const schema = new Schema({
      // Company Reference
      companyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true,
        unique: true  // One settings document per company
      },

      // Global eligibility rules
      globalEligibility: {
        payrollFrequency: {
          type: String,
          enum: PAYROLL_FREQUENCIES,
          default: 'Monthly', // ✅ Default fallback if not set
          required: true
        },
        firstPayrollDate: { type: Date },
        defaultWaitingPeriod: { type: String },
        rehirePolicy: { type: String },
        allowCustomPayrollFrequency: { type: Boolean, default: false }
      },

      // Note: Contribution policies moved to PlanAssignment model for plan-specific control

      // Enrollment periods
      enrollmentPeriods: [{
        type: {
          type: String,
          enum: ENROLLMENT_PERIOD_TYPES,
          required: true
        },
        startDate: { type: Date, required: true },
        endDate: { type: Date, required: true },
        coverageStartDate: { type: Date, required: true },
        coverageEndDate: { type: Date, required: true },
        description: { type: String },
        isActive: { type: Boolean, default: true }
      }],

      // Company preferences and settings
      companyPreferences: {
        allowEmployeeBenefitChanges: { type: Boolean, default: true },
        requireBeneficiaryDesignation: { type: Boolean, default: false },
        enableDependentVerification: { type: Boolean, default: false },
        autoEnrollNewHires: { type: Boolean, default: false },
        defaultCoverageLevel: { type: String }
      },

      // System fields
      isActive: { type: Boolean, default: true }
    }, {
      timestamps: true // Automatically adds createdAt and updatedAt
    });

    // Add indexes for performance
    // Note: companyId already has unique index from schema definition
    schema.index({ isActive: 1 });
    schema.index({ 'enrollmentPeriods.startDate': 1, 'enrollmentPeriods.endDate': 1 });

    this.companyBenefitsSettingsModel = mongoose.model<CompanyBenefitsSettingsDocument>('CompanyBenefitsSettings', schema);
  }

  // ===== CORE CRUD OPERATIONS =====

  // Create company benefits settings
  public static async addData(data: CompanyBenefitsSettingsDataInterface): Promise<CompanyBenefitsSettingsDataInterface | null> {
    try {
      const settings = await this.companyBenefitsSettingsModel.create(data);
      return settings as unknown as CompanyBenefitsSettingsDataInterface;
    } catch (error) {
      console.error('Error creating company benefits settings:', error);
      return null;
    }
  }

  // Get all company benefits settings (ADMIN ONLY)
  public static async getAllData(): Promise<CompanyBenefitsSettingsDataInterface[]> {
    try {
      const data = await this.companyBenefitsSettingsModel.find({ isActive: true }) as CompanyBenefitsSettingsDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching all company benefits settings:', error);
      return [];
    }
  }

  // Get company benefits settings by company ID
  public static async getDataByCompanyId(companyId: string): Promise<CompanyBenefitsSettingsDataInterface | null> {
    try {
      const data = await this.companyBenefitsSettingsModel.findOne({
        companyId,
        isActive: true
      }) as CompanyBenefitsSettingsDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching company benefits settings by company ID:', error);
      return null;
    }
  }

  // Get company benefits settings by ID
  public static async getDataById(id: string): Promise<CompanyBenefitsSettingsDataInterface | null> {
    try {
      const data = await this.companyBenefitsSettingsModel.findOne({
        _id: id,
        isActive: true
      }) as CompanyBenefitsSettingsDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching company benefits settings by ID:', error);
      return null;
    }
  }

  // Update company benefits settings
  public static async updateData({
    id,
    data,
  }: {
    id: string;
    data: Partial<UpdateableCompanyBenefitsSettingsDataInterface>;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.companyBenefitsSettingsModel.updateOne({ _id: id }, data);
    } catch (error) {
      console.error('Error updating company benefits settings:', error);
      throw error;
    }
  }

  // Update company benefits settings by company ID
  public static async updateDataByCompanyId({
    companyId,
    data,
  }: {
    companyId: string;
    data: Partial<UpdateableCompanyBenefitsSettingsDataInterface>;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.companyBenefitsSettingsModel.updateOne({ companyId }, data);
    } catch (error) {
      console.error('Error updating company benefits settings by company ID:', error);
      throw error;
    }
  }

  // Soft delete company benefits settings
  public static async deleteData(id: string): Promise<UpdateWriteOpResult> {
    try {
      return await this.companyBenefitsSettingsModel.updateOne({ _id: id }, { isActive: false });
    } catch (error) {
      console.error('Error deleting company benefits settings:', error);
      throw error;
    }
  }

  // Hard delete company benefits settings (for cleanup)
  public static async hardDeleteData(id: string): Promise<void> {
    try {
      await this.companyBenefitsSettingsModel.findByIdAndDelete(id);
    } catch (error) {
      console.error('Error hard deleting company benefits settings:', error);
      throw error;
    }
  }

  // Check if company has benefits settings configured
  public static async hasSettings(companyId: string): Promise<boolean> {
    try {
      const settings = await this.companyBenefitsSettingsModel.findOne({
        companyId,
        isActive: true
      });
      return !!settings;
    } catch (error) {
      console.error('Error checking if company has settings:', error);
      return false;
    }
  }

  // ===== ENROLLMENT PERIOD MANAGEMENT =====

  // Add enrollment period to company settings
  public static async addEnrollmentPeriod({
    companyId,
    enrollmentPeriod,
  }: {
    companyId: string;
    enrollmentPeriod: Omit<EnrollmentPeriod, '_id'>;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.companyBenefitsSettingsModel.updateOne(
        { companyId },
        { $push: { enrollmentPeriods: enrollmentPeriod } }
      );
    } catch (error) {
      console.error('Error adding enrollment period:', error);
      throw error;
    }
  }

  // Update enrollment period
  public static async updateEnrollmentPeriod({
    companyId,
    periodId,
    periodData,
  }: {
    companyId: string;
    periodId: string;
    periodData: Partial<EnrollmentPeriod>;
  }): Promise<UpdateWriteOpResult> {
    try {
      const updateFields: any = {};
      Object.keys(periodData).forEach(key => {
        if (key !== '_id') {
          updateFields[`enrollmentPeriods.$.${key}`] = (periodData as any)[key];
        }
      });

      return await this.companyBenefitsSettingsModel.updateOne(
        { companyId, 'enrollmentPeriods._id': periodId },
        { $set: updateFields }
      );
    } catch (error) {
      console.error('Error updating enrollment period:', error);
      throw error;
    }
  }

  // Remove enrollment period
  public static async removeEnrollmentPeriod({
    companyId,
    periodId,
  }: {
    companyId: string;
    periodId: string;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.companyBenefitsSettingsModel.updateOne(
        { companyId },
        { $pull: { enrollmentPeriods: { _id: periodId } } }
      );
    } catch (error) {
      console.error('Error removing enrollment period:', error);
      throw error;
    }
  }

  // Get active enrollment periods for a company
  public static async getActiveEnrollmentPeriods(companyId: string): Promise<EnrollmentPeriod[]> {
    try {
      const settings = await this.companyBenefitsSettingsModel.findOne({ companyId, isActive: true });
      if (!settings) return [];

      const now = new Date();
      return settings.enrollmentPeriods.filter(period =>
        period.isActive &&
        period.startDate <= now &&
        period.endDate >= now
      );
    } catch (error) {
      console.error('Error getting active enrollment periods:', error);
      return [];
    }
  }

  // Check if company is in enrollment period
  public static async isInEnrollmentPeriod(companyId: string, enrollmentType?: string): Promise<boolean> {
    try {
      const activeEnrollments = await this.getActiveEnrollmentPeriods(companyId);
      if (enrollmentType) {
        return activeEnrollments.some(period => period.type === enrollmentType);
      }
      return activeEnrollments.length > 0;
    } catch (error) {
      console.error('Error checking enrollment period:', error);
      return false;
    }
  }

  // ===== COMPANY PREFERENCES MANAGEMENT =====

  // Update company preferences
  public static async updateCompanyPreferences({
    companyId,
    preferences,
  }: {
    companyId: string;
    preferences: CompanyPreferences;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.companyBenefitsSettingsModel.updateOne(
        { companyId },
        { $set: { companyPreferences: preferences } }
      );
    } catch (error) {
      console.error('Error updating company preferences:', error);
      throw error;
    }
  }

  // Get company preferences
  public static async getCompanyPreferences(companyId: string): Promise<CompanyPreferences | null> {
    try {
      const settings = await this.companyBenefitsSettingsModel.findOne({ companyId, isActive: true });
      return settings?.companyPreferences || null;
    } catch (error) {
      console.error('Error getting company preferences:', error);
      return null;
    }
  }

  // Note: Contribution calculation methods moved to PlanAssignment model for plan-specific control

  // Get companies by broker (for broker access control)
  public static async getCompaniesByBroker(_brokerId: string): Promise<CompanyBenefitsSettingsDataInterface[]> {
    try {
      // This would require joining with Company model to filter by brokerId
      // For now, return empty array - this should be implemented when Company model relationship is established
      return [];
    } catch (error) {
      console.error('Error getting companies by broker:', error);
      return [];
    }
  }

  // Validate company settings completeness
  public static async validateSettingsCompleteness(companyId: string): Promise<{ isComplete: boolean; missingFields: string[] }> {
    try {
      const settings = await this.getDataByCompanyId(companyId);
      if (!settings) {
        return { isComplete: false, missingFields: ['Company benefits settings not found'] };
      }

      const missingFields: string[] = [];

      // Check required global eligibility fields
      if (!settings.globalEligibility.payrollFrequency) {
        missingFields.push('Payroll frequency');
      }

      // Note: Contribution policy validation moved to PlanAssignment model

      // Check if at least one enrollment period exists
      if (!settings.enrollmentPeriods || settings.enrollmentPeriods.length === 0) {
        missingFields.push('Enrollment periods');
      }

      return {
        isComplete: missingFields.length === 0,
        missingFields
      };
    } catch (error) {
      console.error('Error validating settings completeness:', error);
      return { isComplete: false, missingFields: ['Error validating settings'] };
    }
  }
}

// Initialize the model
CompanyBenefitsSettingsModelClass.initializeModel();

export default CompanyBenefitsSettingsModelClass;