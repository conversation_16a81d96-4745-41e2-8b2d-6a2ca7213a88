import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the Company interface
interface Company {
  _id: string;
  name: string;
  companySize: number;
  industry: string;
  location: string;
  website: string;
  adminEmail: string;
  adminRole: string;
  brokerId: string;
  brokerageId: string;
  isBrokerage: boolean;
  isActivated: boolean;
  howHeard: string;
  details?: {
    logo: string;
  };
}

// First, update the CompanyState interface to include teamMembers
interface CompanyState {
  companies: Company[];
  selectedCompany: Company | null;
  loading: boolean;
  error: string | null;
  teamMembers: Employee[]; // Add this line
}

// Make sure the Employee interface is defined
interface Employee {
  _id: string;
  name: string;
  email: string;
  role: string;
  isActivated: boolean;
}

// Then make sure the initial state includes teamMembers
const initialState: CompanyState = {
  companies: [],
  selectedCompany: null,
  loading: false,
  error: null,
  teamMembers: [], // Add this line
};

// Add a reducer to handle setting team members
export const companySlice = createSlice({
  name: 'company',
  initialState,
  reducers: {
    // Add existing reducers if any
    setCompanies: (state, action: PayloadAction<Company[]>) => {
      state.companies = action.payload;
    },
    setSelectedCompany: (state, action: PayloadAction<Company>) => {
      state.selectedCompany = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    // Add this reducer
    setTeamMembers: (state, action: PayloadAction<Employee[]>) => {
      state.teamMembers = action.payload;
    },
  },
});

// Export the new action
export const { 
  setCompanies,
  setSelectedCompany,
  setLoading,
  setError,
  setTeamMembers 
} = companySlice.actions;

export default companySlice.reducer;
