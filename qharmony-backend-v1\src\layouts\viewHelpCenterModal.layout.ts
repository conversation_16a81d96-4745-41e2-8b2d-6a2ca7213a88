import logger, { prettyJSON } from '../utils/logger';

export const viewHelpCenterModalLayout = () => {
  try {
    const modalBody = {
      type: 'modal',
      title: {
        type: 'plain_text',
        text: `Help Center`,
        emoji: true,
      },
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'Need Help? Contact Us!',
            emoji: true,
          },
        },
        {
          "type": "section",
          "text": {
            "type": "mrkdwn",
            "text": "For assistance, call our toll-free number: 1-800-123-4567\nOr email us at: <EMAIL>"
          },
        },
      ],
    };

    return modalBody;
  } catch (e) {
    logger.error(`Error creating viewHelpCenterModalLayout`);
    logger.error(prettyJSON(e));
  }
  return {};
};
