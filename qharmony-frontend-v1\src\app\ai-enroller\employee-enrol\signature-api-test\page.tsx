'use client';

import React, { useState } from 'react';
import { saveSignatureWithRetry, checkSignatureExists, validateSignatureData } from '../services/signatureApi';

const SignatureApiTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testSaveSignature = async () => {
    setIsLoading(true);
    addResult('🧪 Testing signature save to database...');

    try {
      // Create a dummy signature for testing
      const dummySignature = btoa(JSON.stringify({
        signature: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        timestamp: new Date().toISOString(),
        employeeName: 'Test Employee',
        userAgent: navigator.userAgent,
        signatureHash: 'test-hash-123'
      }));

      const result = await saveSignatureWithRetry(dummySignature);
      
      if (result.success) {
        addResult('✅ Signature saved successfully to database');
        addResult(`📋 Response: ${result.message}`);
        if (result.signatureId) {
          addResult(`🆔 Signature ID: ${result.signatureId}`);
        }
      } else {
        addResult(`❌ Failed to save signature: ${result.error}`);
      }
    } catch (error) {
      addResult(`❌ Error during save test: ${error}`);
    }

    setIsLoading(false);
  };

  const testLocalStorageSignature = () => {
    addResult('🧪 Testing local signature storage...');

    try {
      const localSignature = localStorage.getItem('enrollmentSignature');

      if (localSignature) {
        addResult('✅ Signature found in localStorage');
        addResult(`📋 Signature data length: ${localSignature.length} characters`);

        // Try to decode and show metadata
        try {
          const decoded = JSON.parse(atob(localSignature));
          addResult(`👤 Employee: ${decoded.employeeName || 'Unknown'}`);
          addResult(`📅 Timestamp: ${decoded.timestamp || 'Unknown'}`);
        } catch (decodeError) {
          addResult('⚠️ Could not decode signature metadata');
        }
      } else {
        addResult('ℹ️ No signature found in localStorage');
      }
    } catch (error) {
      addResult(`❌ Error during localStorage test: ${error}`);
    }
  };

  const testSignatureExists = async () => {
    setIsLoading(true);
    addResult('🧪 Testing signature existence check...');

    try {
      const exists = await checkSignatureExists();
      
      if (exists) {
        addResult('✅ Signature exists in database');
      } else {
        addResult('ℹ️ No signature found in database');
      }
    } catch (error) {
      addResult(`❌ Error during existence check: ${error}`);
    }

    setIsLoading(false);
  };

  const testValidation = () => {
    addResult('🧪 Testing signature validation...');

    // Test valid signature
    const validSignature = btoa('{"test": "data"}');
    const validResult = validateSignatureData(validSignature);
    addResult(`✅ Valid signature test: ${validResult.isValid ? 'PASSED' : 'FAILED'}`);

    // Test empty signature
    const emptyResult = validateSignatureData('');
    addResult(`✅ Empty signature test: ${!emptyResult.isValid ? 'PASSED' : 'FAILED'} - ${emptyResult.error}`);

    // Test invalid base64
    const invalidResult = validateSignatureData('invalid-base64!@#');
    addResult(`✅ Invalid base64 test: ${!invalidResult.isValid ? 'PASSED' : 'FAILED'} - ${invalidResult.error}`);
  };

  const runAllTests = async () => {
    clearResults();
    addResult('🚀 Starting comprehensive API tests...');
    
    testValidation();
    await testSignatureExists();
    await testSaveSignature();
    testLocalStorageSignature();
    
    addResult('🏁 All tests completed!');
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f9fafb',
      padding: '40px 20px'
    }}>
      <div style={{
        maxWidth: '1000px',
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '16px',
        padding: '40px',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <h1 style={{
            fontSize: '32px',
            fontWeight: '600',
            color: '#111827',
            margin: '0 0 16px 0'
          }}>
            🧪 Signature API Test Suite
          </h1>
          <p style={{
            color: '#6b7280',
            fontSize: '16px',
            margin: 0,
            lineHeight: '24px'
          }}>
            Test the signature API integration with your backend endpoint:<br/>
            <code style={{ backgroundColor: '#f3f4f6', padding: '2px 6px', borderRadius: '4px' }}>
              POST /admin/update/signature
            </code>
          </p>
        </div>

        {/* Test Buttons */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '32px'
        }}>
          <button
            onClick={testSaveSignature}
            disabled={isLoading}
            style={{
              padding: '16px 24px',
              backgroundColor: '#2563eb',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              opacity: isLoading ? 0.6 : 1
            }}
          >
            💾 Test Save Signature
          </button>

          <button
            onClick={testLocalStorageSignature}
            style={{
              padding: '16px 24px',
              backgroundColor: '#059669',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer',
              fontWeight: '500',
              fontSize: '16px'
            }}
          >
            💾 Test localStorage
          </button>

          <button
            onClick={testSignatureExists}
            disabled={isLoading}
            style={{
              padding: '16px 24px',
              backgroundColor: '#7c3aed',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              opacity: isLoading ? 0.6 : 1
            }}
          >
            🔍 Test Exists Check
          </button>

          <button
            onClick={testValidation}
            disabled={isLoading}
            style={{
              padding: '16px 24px',
              backgroundColor: '#dc2626',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              opacity: isLoading ? 0.6 : 1
            }}
          >
            ✅ Test Validation
          </button>

          <button
            onClick={runAllTests}
            disabled={isLoading}
            style={{
              padding: '16px 24px',
              backgroundColor: '#059669',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              opacity: isLoading ? 0.6 : 1,
              gridColumn: 'span 2'
            }}
          >
            🚀 Run All Tests
          </button>

          <button
            onClick={clearResults}
            style={{
              padding: '16px 24px',
              backgroundColor: '#6b7280',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              gridColumn: 'span 2'
            }}
          >
            🗑️ Clear Results
          </button>
        </div>

        {/* Results Panel */}
        <div style={{
          backgroundColor: '#f8fafc',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '24px',
          minHeight: '300px'
        }}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#111827',
            margin: '0 0 16px 0'
          }}>
            Test Results:
          </h3>
          
          {testResults.length === 0 ? (
            <p style={{ color: '#6b7280', fontStyle: 'italic' }}>
              No tests run yet. Click a test button to start.
            </p>
          ) : (
            <div style={{
              backgroundColor: '#1f2937',
              color: '#f9fafb',
              padding: '16px',
              borderRadius: '6px',
              fontFamily: 'monospace',
              fontSize: '14px',
              lineHeight: '20px',
              maxHeight: '400px',
              overflowY: 'auto'
            }}>
              {testResults.map((result, index) => (
                <div key={index} style={{ marginBottom: '4px' }}>
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* API Info */}
        <div style={{
          marginTop: '32px',
          padding: '16px',
          backgroundColor: '#fef3c7',
          border: '1px solid #f59e0b',
          borderRadius: '8px',
          fontSize: '14px',
          color: '#92400e'
        }}>
          <strong>📋 API Endpoint Details:</strong><br/>
          <code>POST /admin/update/signature</code><br/>
          <strong>Body:</strong> <code>{`{ "signatureData": "base64string..." }`}</code><br/>
          <strong>Headers:</strong> <code>Content-Type: application/json, user-id: [userId]</code>
        </div>

        {/* Back Link */}
        <div style={{ textAlign: 'center', marginTop: '32px' }}>
          <a
            href="/ai-enroller/employee-enrol"
            style={{
              display: 'inline-block',
              padding: '12px 24px',
              backgroundColor: 'white',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              color: '#374151',
              textDecoration: 'none',
              fontWeight: '500',
              fontSize: '14px'
            }}
          >
            ← Back to Enrollment
          </a>
        </div>
      </div>
    </div>
  );
};

export default SignatureApiTestPage;
