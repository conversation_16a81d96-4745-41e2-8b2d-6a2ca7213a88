from masai.GenerativeModel.baseGenerativeModel.basegenerativeModel import BaseGenerativeModel
import asyncio
from datetime import datetime
from typing import Dict, Optional, AsyncGenerator
from app.Tools.vectorStore import VectorStore
import redis.asyncio as redis_async
import json
import pickle
from app.utils.vstore_utils.vectorstore_utils import get_retriever, fetch_document_ids, fetch_user_data
from app.utils.Prompts.prompt_template import BREA_SYSTEM_PROMPT
from app.utils.vstore_utils.filters import metadata_similarity_filter
from config.config import config
from app.utils.vstore_utils.vectorstore_utils import load_vector_store_cache

class ChatSession:
    """Manages a chat session for a specific user."""
    
    def __init__(self, user_id: str, team_id: str, vector_store : VectorStore, mongo_client, model_config: dict = {}, info:dict={}):
        self.user_id = user_id
        self.team_id = team_id
        self.index_name = config.index_name
        self.namespace = f"{config.namespace_prefix}-{self.team_id}"
        self.vector_store: VectorStore = vector_store
        self.mongo_client = mongo_client
        self.last_activity = datetime.now()
        self.document_ids = None
        self.model_config=model_config
        self.retriever = None
        self.info: dict=info
        self.chat_history = []
        self.chat_model = None

        self._initialize_chat_model()
        
    async def initialize(self):
        """Initialize the chat session with necessary components."""
        # Clear caches for critical functions to ensure fresh data
        # from app.utils.Cache.lru_cache import clear_cache_for_function
        # await clear_cache_for_function("fetch_document_ids")
        # await clear_cache_for_function("fetch_user_data")
        
        # Initialize document_ids if not already done
        if self.document_ids is None:
            self.document_ids = await self._fetch_document_ids()        
        # Always reinitialize retriever after Redis restoration
        # This ensures we have a fresh retriever with proper connections
        
        self.vector_store.vector_store = await load_vector_store_cache(vector_store=self.vector_store,index_name=self.index_name, namespace=self.namespace)
        self.retriever = await self._get_retriever()
        
        if len(self.chat_history) > 0:
            if self.chat_model is not None:
                self.chat_model.chat_history = self.chat_history.copy()
        
        if not self.info:
            self.info = await self._fetch_user_info()
            if self.chat_model is not None:
                self.chat_model.info = self.info

        # Ensure chat_model is initialized/reinitialized
        self._initialize_chat_model()
        
        return self
    
    async def _fetch_document_ids(self):
        """Fetch document IDs for the user, with caching."""
        try:
            print(f"Fetching document_ids for user {self.user_id}, team {self.team_id}")
            document_ids = await fetch_document_ids(
                user_id=self.user_id,
                team_id=self.team_id,
                vector_store=self.vector_store,
                mongo_client=self.mongo_client,
                mongo_db_name=config.mongo_db
            )
            # print(f"Fetched {len(document_ids) if document_ids else 0} document_ids")
            return document_ids
        except Exception as e:
            print(f"Error fetching document_ids: {e}")
            # Return empty list instead of None to avoid further errors
            return []
    
    async def _fetch_user_info(self):
        """Fetch user information, with caching."""
        # This could be cached in Redis or in memory
        return await fetch_user_data(
            user_identity=self.user_id,
            mongo_client=self.mongo_client,
            db_name=config.mongo_db,
            by="id"
        )
    
    def _initialize_chat_model(self):
        """Initialize the chat model with necessary components."""
        if self.chat_model is None:
            self.chat_model = BaseGenerativeModel(
                model_name=self.model_config.get("model_name"),
                category=self.model_config.get("model_category"),
                temperature=config.model_temperature,
                memory=config.memory,
                memory_order=config.memory_order,
                info=self.info,
                system_prompt=BREA_SYSTEM_PROMPT,
                input_variables=config.inference_variables,
                logging=config.ai_logging
            )

        if len(self.chat_history)>0:
                # print("____________UPDATING CHAT HISTORY--------------------------")
                self.chat_model.chat_history = self.chat_history.copy()

    async def _get_retriever(self):
        """Get or create a retriever for the session."""
        # Ensure document_ids are fetched
        if self.document_ids is None:
            self.document_ids = await self._fetch_document_ids()
            # print(f"Fetched document_ids: {self.document_ids}")
        
        # Check if document_ids is empty or None
        if not self.document_ids:
            print(f"WARNING: No document_ids found for user {self.user_id}, team {self.team_id}")
        
        # Create retriever with detailed logging
        try:
            # print(f"Creating retriever with document_ids: {self.document_ids}")
            self.retriever = await get_retriever(
                vector_store=self.vector_store,
                search_kwargs=metadata_similarity_filter(
                    metadata_key="file_key", 
                    ids=self.document_ids
                )
            )
            # print(f"Retriever created successfully for user {self.user_id}-{self.retriever}")
            return self.retriever
        except Exception as e:
            print(f"Error creating retriever: {e}")
            # Fallback to a basic retriever if possible
            try:
                # print("Attempting to create fallback retriever without filters")
                return await get_retriever(vector_store=self.vector_store, search_kwargs={"k": 10, "filter": {"file_key": {"$in": self.document_ids}}})
            except Exception as e2:
                print(f"Fallback retriever creation also failed: {e2}")
                raise
    
    async def process_message(self, user_message: str) -> AsyncGenerator[str, None]:
        """Process a user message and stream the response."""
        self.last_activity = datetime.now()        
        # Get relevant documents using the retriever
        try:
            retrieved_docs = self.retriever.invoke(input=user_message)
            print(f"----------------------{len(retrieved_docs)} documents retrieved for user {self.user_id}-------------------------")
            
            # If no documents were retrieved, try reinitializing the retriever
            if len(retrieved_docs) == 0:
                print("No documents retrieved, attempting to reinitialize retriever")
                self.vector_store.load_vector_store(index_name=self.index_name, namespace=self.namespace)
                await self._get_retriever()
                
                # Try retrieval again
                retrieved_docs = self.retriever.invoke(input=user_message)
                print(f"After reinitialization: {len(retrieved_docs)} documents retrieved")
        except Exception as e:
            print(f"Error retrieving documents: {e}. Reinitializing retriever...")
            # Reinitialize retriever on error
            self.retriever = await self._get_retriever()
            try:
                retrieved_docs = self.retriever.invoke(input=user_message)
                print(f"----------------------{len(retrieved_docs)} documents retrieved after reinitialization-------------------------")
            except Exception as e2:
                print(f"Still failed to retrieve documents: {e2}. Proceeding with empty documents.")
                retrieved_docs = []
        
        # Stream the response from the model
        async for chunk in self.chat_model.astream_response(
            prompt=user_message,
            custom_inputs={"documents": str(retrieved_docs)}
        ):
            yield chunk
            
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """Check if the session has expired."""
        delta = datetime.now() - self.last_activity
        return delta.total_seconds() > float(timeout_minutes * 60)

    def __getstate__(self):
        """Control what gets pickled."""
        state = self.__dict__.copy()
        # Remove unpicklable objects
        state['retriever'] = None
        state['model'] = None
        state['vector_store'] = None
        state['mongo_client'] = None
        state['chat_model'] = None  # Explicitly set chat_model to None
        
        # Keep model_config intact for reinitializing chat_model later
        # Make sure model_config is serializable
        if 'model_config' in state and state['model_config'] is not None:
            try:
                pickle.dumps(state['model_config'])
            except (TypeError, pickle.PickleError):
                # If not serializable, create a copy with basic types
                if isinstance(state['model_config'], dict):
                    state['model_config'] = {k: str(v) for k, v in state['model_config'].items()}

        if self.chat_model and hasattr(self.chat_model, 'chat_history'):
            state['chat_history'] = self.chat_model.chat_history.copy()


        for key in list(state.keys()):
            if key not in ['model_config']:  # Preserve these if possible
                try:

                    # Test if the object is serializable
                    pickle.dumps(state[key])
                except (TypeError, pickle.PickleError):
                    # If not serializable, set to None
                    state[key] = None
        
        return state

    def __setstate__(self, state):
        """Restore state from unpickled values."""

        self.__dict__.update(state)
        # chat_model will be recreated during initialize()
