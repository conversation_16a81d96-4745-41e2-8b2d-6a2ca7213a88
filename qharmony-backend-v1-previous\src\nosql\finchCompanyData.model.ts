import mongoose, { Document, Model } from 'mongoose';

const { Schema } = mongoose;

export interface Department {
  name: string;
  parent: string | null;
}

export interface Location {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
}

export interface FinchCompanyDataInterface {
  _ts: number;
  data: {
    id: string;
    legal_name: string;
    ein: string;
    entity: {
      type: string;
      subtype: string;
    };
    primary_email: string;
    primary_phone_number: string;
    departments: Department[];
    locations: Location[];
    accounts: any[]; // You can replace 'any[]' with the appropriate type for accounts
  }[];
}

interface FinchCompanyDocument extends Document {
  _ts: number;
  data: FinchCompanyDataInterface['data'];
}

class FinchCompanyModelClass {
  private static finchCompanyModel: Model<FinchCompanyDocument>;

  public static initializeModel() {
    const schema = new Schema({
      _ts: Number,
      data: [
        {
          id: String,
          legal_name: String,
          ein: String,
          entity: {
            type: String,
            subtype: String,
          },
          primary_email: String,
          primary_phone_number: String,
          departments: [
            {
              name: String,
              parent: String,
            },
          ],
          locations: [
            {
              line1: String,
              line2: String,
              city: String,
              state: String,
              country: String,
              postal_code: String,
            },
          ],
          accounts: [],
        },
      ],
    });

    this.finchCompanyModel = mongoose.model<FinchCompanyDocument>(
      'FinchCompany',
      schema
    );
  }

  public static async addData(
    data: FinchCompanyDataInterface['data']
  ): Promise<void> {
    try {
      const ifExists = await this.finchCompanyModel.findOne();
      if (ifExists) {
        await this.finchCompanyModel.updateOne(
          {},
          {
            $push: {
              data: {
                _ts: Date.now(),
                data,
              },
            },
          }
        );
      } else {
        await this.finchCompanyModel.create({
          data: [
            {
              _ts: Date.now(),
              data,
            },
          ],
        });
      }
    } catch (error) {
      console.error(error);
    }
  }

  public static async getLatestData(): Promise<FinchCompanyDataInterface | null> {
    try {
      const result = await this.finchCompanyModel
        .findOne()
        .sort({ _ts: -1 })
        .limit(1);
      return result ? { _ts: result._ts, data: result.data } : null;
    } catch (error) {
      console.error(error);
      return null;
    }
  }
}

FinchCompanyModelClass.initializeModel();

export default FinchCompanyModelClass;
