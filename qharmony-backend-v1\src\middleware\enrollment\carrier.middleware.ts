import express from 'express';
import { isValidObjectId } from '../../utils/validation';
import { PLAN_TYPES, PRE_ENROLLMENT_COVERAGE_TYPES, PRE_ENROLLMENT_COVERAGE_SUBTYPES, CARRIER_STATUSES } from '../../constants';
import { CarrierDataInterface } from '../../nosql/preEnrollment/carrier.model';

/**
 * Combined Carrier Middleware
 * Handles validation, response formatting, and common middleware functions
 */
export class CarrierMiddleware {

  // ===== VALIDATION MIDDLEWARE =====

  /**
   * Validate user ID in headers (used in ALL carrier endpoints)
   */
  static validateUserId() {
    return (req: express.Request, res: express.Response, next: express.NextFunction) => {
      const userId = req.headers['user-id'] as string;
      if (!userId) {
        return res.status(401).json({ error: 'User ID is required' });
      }
      (req as any).userId = userId;
      next();
    };
  }

  /**
   * Validate carrier ID parameter format
   */
  static validateCarrierId() {
    return (req: express.Request, res: express.Response, next: express.NextFunction) => {
      const { carrierId } = req.params;
      if (!carrierId) {
        return res.status(400).json({ error: 'Carrier ID is required' });
      }
      if (!isValidObjectId(carrierId)) {
        return res.status(400).json({ error: 'Invalid carrier ID format' });
      }
      next();
    };
  }

  /**
   * Validate carrier creation fields
   */
  static validateCarrierCreation() {
    return (req: express.Request, res: express.Response, next: express.NextFunction) => {
      const { carrierName, carrierCode, supportedPlanTypes, supportedCoverageTypes, supportedCoverageSubTypes } = req.body;
      
      // Required fields
      if (!carrierName || !carrierCode) {
        return res.status(400).json({ error: 'Carrier name and carrier code are required' });
      }

      // Validate plan types
      if (supportedPlanTypes?.length > 0) {
        for (const planType of supportedPlanTypes) {
          if (!(PLAN_TYPES as readonly string[]).includes(planType)) {
            return res.status(400).json({ error: `Invalid plan type: ${planType}` });
          }
        }
      }

      // Validate coverage types
      if (supportedCoverageTypes?.length > 0) {
        for (const coverageType of supportedCoverageTypes) {
          if (!(PRE_ENROLLMENT_COVERAGE_TYPES as readonly string[]).includes(coverageType)) {
            return res.status(400).json({ error: `Invalid coverage type: ${coverageType}` });
          }
        }
      }

      // Validate coverage subtypes
      if (supportedCoverageSubTypes?.length > 0) {
        for (const subType of supportedCoverageSubTypes) {
          if (!(PRE_ENROLLMENT_COVERAGE_SUBTYPES as readonly string[]).includes(subType)) {
            return res.status(400).json({ error: `Invalid coverage subtype: ${subType}` });
          }
        }
      }

      next();
    };
  }

  /**
   * Validate query parameters for filtering
   */
  static validateQueryParams() {
    return (req: express.Request, res: express.Response, next: express.NextFunction) => {
      const { status, planType, coverageType, ediCapable, isSystemCarrier, page, limit } = req.query;
      
      if (status && !(CARRIER_STATUSES as readonly string[]).includes(status as string)) {
        return res.status(400).json({ error: `Invalid status filter: ${status}` });
      }

      if (planType && !(PLAN_TYPES as readonly string[]).includes(planType as string)) {
        return res.status(400).json({ error: `Invalid plan type filter: ${planType}` });
      }

      if (coverageType && !(PRE_ENROLLMENT_COVERAGE_TYPES as readonly string[]).includes(coverageType as string)) {
        return res.status(400).json({ error: `Invalid coverage type filter: ${coverageType}` });
      }
      
      if (ediCapable && !['true', 'false'].includes(ediCapable as string)) {
        return res.status(400).json({ error: 'ediCapable must be true or false' });
      }
      
      if (isSystemCarrier && !['true', 'false'].includes(isSystemCarrier as string)) {
        return res.status(400).json({ error: 'isSystemCarrier must be true or false' });
      }
      
      if (page && (isNaN(Number(page)) || Number(page) < 1)) {
        return res.status(400).json({ error: 'Page must be a positive number' });
      }
      
      if (limit && (isNaN(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
        return res.status(400).json({ error: 'Limit must be between 1 and 100' });
      }
      
      next();
    };
  }

  // ===== RESPONSE HANDLERS =====

  /**
   * Success response for carrier creation
   */
  static carrierCreated(res: express.Response, carrier: CarrierDataInterface) {
    return res.status(201).json({
      message: 'Carrier created successfully',
      carrier
    });
  }

  /**
   * Success response for carrier retrieval
   */
  static carrierRetrieved(res: express.Response, carrier: CarrierDataInterface) {
    return res.status(200).json({ carrier });
  }

  /**
   * Success response for carrier list
   */
  static carriersListed(res: express.Response, carriers: CarrierDataInterface[], pagination?: any) {
    const response: any = { carriers, count: carriers.length };
    if (pagination) response.pagination = pagination;
    return res.status(200).json(response);
  }

  /**
   * Success response for carrier update
   */
  static carrierUpdated(res: express.Response, carrier: CarrierDataInterface) {
    return res.status(200).json({
      message: 'Carrier updated successfully',
      carrier
    });
  }

  /**
   * Success response for carrier deletion
   */
  static carrierDeleted(res: express.Response) {
    return res.status(200).json({
      message: 'Carrier deleted successfully'
    });
  }

  /**
   * Success response for status operations
   */
  static carrierStatusChanged(res: express.Response, action: string, carrier?: CarrierDataInterface) {
    const response: any = { message: `Carrier ${action}d successfully` };
    if (carrier) response.carrier = carrier;
    return res.status(200).json(response);
  }

  /**
   * Success response for validation/dependency operations
   */
  static operationResult(res: express.Response, result: any) {
    return res.status(200).json(result);
  }

  // ===== ERROR HANDLERS =====

  /**
   * Handle service errors with appropriate HTTP status codes
   */
  static handleServiceError(res: express.Response, error: string) {
    // Map common error messages to appropriate status codes
    if (error.includes('not found') || error.includes('access denied')) {
      return res.status(404).json({ error });
    }
    
    if (error.includes('permissions') || error.includes('cannot') || error.includes('Access denied')) {
      return res.status(403).json({ error });
    }
    
    if (error.includes('already exists') || error.includes('required') || error.includes('Invalid')) {
      return res.status(400).json({ error });
    }
    
    if (error.includes('Internal server error') || error.includes('Failed to')) {
      return res.status(500).json({ error });
    }
    
    // Default to 400 for other errors
    return res.status(400).json({ error });
  }

  /**
   * Internal server error
   */
  static internalError(res: express.Response, message: string = 'Internal server error') {
    return res.status(500).json({ error: message });
  }

  // ===== COMBINED MIDDLEWARE CHAINS =====

  /**
   * Middleware chain for carrier creation
   */
  static forCarrierCreation() {
    return [
      this.validateUserId(),
      this.validateCarrierCreation()
    ];
  }

  /**
   * Middleware chain for carrier updates
   */
  static forCarrierUpdate() {
    return [
      this.validateUserId(),
      this.validateCarrierId()
    ];
  }

  /**
   * Middleware chain for carrier retrieval
   */
  static forCarrierRetrieval() {
    return [
      this.validateUserId(),
      this.validateCarrierId()
    ];
  }

  /**
   * Middleware chain for carrier listing
   */
  static forCarrierListing() {
    return [
      this.validateUserId(),
      this.validateQueryParams()
    ];
  }

  /**
   * Middleware chain for basic operations (just user validation)
   */
  static forBasicOperation() {
    return [
      this.validateUserId()
    ];
  }

  /**
   * Apply pagination to carrier array
   */
  static paginateCarriers(carriers: CarrierDataInterface[], page: number = 1, limit: number = 20) {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCarriers = carriers.slice(startIndex, endIndex);

    const pagination = {
      currentPage: page,
      totalPages: Math.ceil(carriers.length / limit),
      totalCarriers: carriers.length,
      hasNext: page * limit < carriers.length,
      hasPrev: page > 1
    };

    return { paginatedCarriers, pagination };
  }
}
