import express from 'express';
import logger from '../../utils/logger';
import { isValidObjectId } from '../../utils/validation';

/**
 * Plan Assignment Middleware
 * Modular middleware for plan assignment operations
 * Provides consistent validation, authentication, and response handling
 */
export class PlanAssignmentMiddleware {

  /**
   * Basic middleware stack for plan assignment operations
   */
  static forBasicOperation() {
    return [
      this.validateUserId,
      this.validateObjectIds
    ];
  }

  /**
   * Middleware stack for plan assignment creation
   */
  static forPlanAssignmentCreation() {
    return [
      this.validateUserId,
      this.validatePlanAssignmentCreationData
    ];
  }

  /**
   * Middleware stack for plan assignment listing
   */
  static forPlanAssignmentListing() {
    return [
      this.validateUserId,
      this.validateQueryParameters
    ];
  }

  /**
   * Middleware stack for plan assignment retrieval
   */
  static forPlanAssignmentRetrieval() {
    return [
      this.validateUserId,
      this.validateAssignmentId
    ];
  }

  /**
   * Middleware stack for plan assignment update
   */
  static forPlanAssignmentUpdate() {
    return [
      this.validateUserId,
      this.validateAssignmentId
    ];
  }

  /**
   * Middleware stack for status management
   */
  static forStatusManagement() {
    return [
      this.validateUserId,
      this.validateAssignmentId
    ];
  }

  /**
   * Middleware stack for time constraints update
   */
  static forTimeConstraints() {
    return [
      this.validateUserId,
      this.validateAssignmentId,
      this.validateTimeConstraintsData
    ];
  }

  /**
   * Middleware stack for plan reassignment
   */
  static forPlanReassignment() {
    return [
      this.validateUserId,
      this.validateAssignmentId,
      this.validatePlanReassignmentData
    ];
  }

  // ===== VALIDATION MIDDLEWARE =====

  /**
   * Validate user ID in headers
   */
  private static validateUserId = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const userId = request.headers['user-id'] as string;

    if (!userId) {
      return response.status(401).json({ error: 'User ID is required' });
    }

    // Attach userId to request for easy access
    (request as any).userId = userId;
    next();
  };

  /**
   * Validate assignment ID parameter
   */
  private static validateAssignmentId = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { id } = request.params;

    if (!id) {
      return response.status(400).json({ error: 'Assignment ID is required' });
    }

    if (!isValidObjectId(id)) {
      return response.status(400).json({ error: 'Invalid assignment ID format' });
    }

    next();
  };

  /**
   * Validate ObjectIds in request
   */
  private static validateObjectIds = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { id } = request.params;
    const { companyId } = request.query;
    const { planId, companyId: bodyCompanyId } = request.body;

    if (id && !isValidObjectId(id)) {
      return response.status(400).json({ error: 'Invalid assignment ID format' });
    }

    if (companyId && !isValidObjectId(companyId as string)) {
      return response.status(400).json({ error: 'Invalid company ID format' });
    }

    if (planId && !isValidObjectId(planId)) {
      return response.status(400).json({ error: 'Invalid plan ID format' });
    }

    if (bodyCompanyId && !isValidObjectId(bodyCompanyId)) {
      return response.status(400).json({ error: 'Invalid company ID format' });
    }

    next();
  };

  /**
   * Validate plan assignment creation data
   */
  private static validatePlanAssignmentCreationData = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { planId, companyId, rateStructure, coverageTiers, planEffectiveDate, planEndDate, enrollmentStartDate, enrollmentEndDate } = request.body;

    // Required fields validation
    const requiredFields = ['planId', 'companyId', 'rateStructure', 'coverageTiers', 'planEffectiveDate', 'planEndDate', 'enrollmentStartDate', 'enrollmentEndDate'];
    const missingFields = requiredFields.filter(field => !request.body[field]);

    if (missingFields.length > 0) {
      return response.status(400).json({
        error: 'Missing required fields',
        required: requiredFields,
        missing: missingFields
      });
    }

    next();
  };

  /**
   * Validate query parameters
   */
  private static validateQueryParameters = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { assignmentYear, referenceDate, date } = request.query;

    if (assignmentYear && isNaN(Number(assignmentYear))) {
      return response.status(400).json({ error: 'Invalid assignment year format. Must be a valid number' });
    }

    if (referenceDate && isNaN(Date.parse(referenceDate as string))) {
      return response.status(400).json({ error: 'Invalid reference date format. Use ISO date string' });
    }

    if (date && isNaN(Date.parse(date as string))) {
      return response.status(400).json({ error: 'Invalid date format. Use ISO date string' });
    }

    // Validate mutually exclusive filters for company endpoint
    const { enrollmentPeriodOnly, effectiveOnly, futureOnly } = request.query;
    const timeFilters = [enrollmentPeriodOnly, effectiveOnly, futureOnly].filter(f => f === 'true');
    if (timeFilters.length > 1) {
      return response.status(400).json({
        error: 'Only one time-based filter can be applied at a time',
        conflictingFilters: { enrollmentPeriodOnly, effectiveOnly, futureOnly }
      });
    }

    next();
  };

  /**
   * Validate time constraints data
   */
  private static validateTimeConstraintsData = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { planEffectiveDate, planEndDate, enrollmentStartDate, enrollmentEndDate } = request.body;

    // Validate at least one time field is provided
    if (!planEffectiveDate && !planEndDate && !enrollmentStartDate && !enrollmentEndDate) {
      return response.status(400).json({
        error: 'At least one time constraint field must be provided',
        allowedFields: ['planEffectiveDate', 'planEndDate', 'enrollmentStartDate', 'enrollmentEndDate']
      });
    }

    next();
  };

  /**
   * Validate plan reassignment data
   */
  private static validatePlanReassignmentData = (request: express.Request, response: express.Response, next: express.NextFunction) => {
    const { newPlanId } = request.body;

    if (!newPlanId) {
      return response.status(400).json({ error: 'New plan ID is required' });
    }

    if (!isValidObjectId(newPlanId)) {
      return response.status(400).json({ error: 'Invalid new plan ID format' });
    }

    next();
  };

  // ===== RESPONSE HANDLERS =====

  /**
   * Handle successful plan assignment creation
   */
  static assignmentCreated(response: express.Response, assignment: any) {
    return response.status(201).json({
      message: 'Plan assignment created successfully',
      assignment
    });
  }

  /**
   * Handle successful plan assignment retrieval
   */
  static assignmentRetrieved(response: express.Response, assignment: any) {
    return response.status(200).json({
      assignment
    });
  }

  /**
   * Handle successful plan assignment listing
   */
  static assignmentsListed(response: express.Response, data: any) {
    return response.status(200).json(data);
  }

  /**
   * Handle successful plan assignment update
   */
  static assignmentUpdated(response: express.Response, assignment: any) {
    return response.status(200).json({
      message: 'Plan assignment updated successfully',
      assignment
    });
  }

  /**
   * Handle successful plan assignment status change
   */
  static assignmentStatusChanged(response: express.Response, message: string) {
    return response.status(200).json({ message });
  }

  /**
   * Handle successful plan assignment cloning
   */
  static assignmentCloned(response: express.Response, data: any) {
    return response.status(201).json({
      message: 'Plan assignment cloned successfully. Assignment is deactivated and requires activation.',
      assignment: data.assignment,
      originalId: data.originalId,
      requiresActivation: data.requiresActivation,
      activationEndpoint: data.activationEndpoint
    });
  }

  /**
   * Handle successful plan assignment deletion
   */
  static assignmentDeleted(response: express.Response) {
    return response.status(200).json({
      message: 'Plan assignment deleted successfully'
    });
  }

  /**
   * Handle operation results (for validation endpoints)
   */
  static operationResult(response: express.Response, result: any) {
    return response.status(200).json(result);
  }

  /**
   * Handle successful plan reassignment
   */
  static planReassigned(response: express.Response, assignment: any) {
    return response.status(200).json({
      message: 'Plan reassigned successfully',
      assignment
    });
  }

  /**
   * Handle successful time constraints update
   */
  static timeConstraintsUpdated(response: express.Response, assignment: any) {
    return response.status(200).json({
      message: 'Time constraints updated successfully',
      assignment
    });
  }

  /**
   * Handle service errors
   */
  static handleServiceError(response: express.Response, error: string) {
    return response.status(400).json({ error });
  }

  /**
   * Handle internal server errors
   */
  static internalError(response: express.Response) {
    return response.status(500).json({ error: 'Internal server error' });
  }
}
