import logger, { prettyJSON } from '../utils/logger';
import { convertYyyyMmDdToMmDdYyyy } from '../utils/utils';

export const viewPayHistoryModalLayout = () => {
  try {
    const modalBody = {
      type: 'modal',
      title: {
        type: 'plain_text',
        text: `Pay History`,
        emoji: true,
      },
      blocks: [
        {
          type: 'section',
          fields: [
            {
              type: 'plain_text',
              text: 'Start Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-10-21')}*`,
            },
            {
              type: 'plain_text',
              text: 'End Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-11-10')}*`,
            },
            {
              type: 'plain_text',
              text: 'Debit Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-11-07')}*`,
            },
            {
              type: 'plain_text',
              text: 'Gross Pay',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: '*$2,750,923 USD*',
            },
            {
              type: 'plain_text',
              text: 'Net Pay',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: '*$1,963,923 USD*',
            },
          ],
        },
        {
          type: 'divider',
        },
        {
          type: 'section',
          fields: [
            {
              type: 'plain_text',
              text: 'Start Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-09-21')}*`,
            },
            {
              type: 'plain_text',
              text: 'End Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-10-10')}*`,
            },
            {
              type: 'plain_text',
              text: 'Debit Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-10-07')}*`,
            },
            {
              type: 'plain_text',
              text: 'Gross Pay',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: '*$2,750,923 USD*',
            },
            {
              type: 'plain_text',
              text: 'Net Pay',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: '*$1,963,923 USD*',
            },
          ],
        },
        {
          type: 'divider',
        },
        {
          type: 'section',
          fields: [
            {
              type: 'plain_text',
              text: 'Start Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-08-21')}*`,
            },
            {
              type: 'plain_text',
              text: 'End Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-09-10')}*`,
            },
            {
              type: 'plain_text',
              text: 'Debit Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-09-07')}*`,
            },
            {
              type: 'plain_text',
              text: 'Gross Pay',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: '*$2,750,923 USD*',
            },
            {
              type: 'plain_text',
              text: 'Net Pay',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: '*$1,963,923 USD*',
            },
          ],
        },
        {
          type: 'divider',
        },
        {
          type: 'section',
          fields: [
            {
              type: 'plain_text',
              text: 'Start Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-07-21')}*`,
            },
            {
              type: 'plain_text',
              text: 'End Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-08-10')}*`,
            },
            {
              type: 'plain_text',
              text: 'Debit Date',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: `*${convertYyyyMmDdToMmDdYyyy('2023-08-07')}*`,
            },
            {
              type: 'plain_text',
              text: 'Gross Pay',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: '*$2,750,923 USD*',
            },
            {
              type: 'plain_text',
              text: 'Net Pay',
              emoji: true,
            },
            {
              type: 'mrkdwn',
              text: '*$1,963,923 USD*',
            },
          ],
        },
        {
          type: 'divider',
        },
      ],
    };

    return modalBody;
  } catch (e) {
    logger.error(`Error creating viewPayHistoryModalLayout`);
    logger.error(prettyJSON(e));
  }
  return {};
};
