import { configureStore } from "@reduxjs/toolkit";
import userReducer from "./reducers/userSlice";
import companyReducer from "./reducers/companySlice";
import benefitsReducer from "./reducers/benefitsSlice";
import qHarmonyBotReducer from "./reducers/qHarmonyBotSlice";
import onboardingReducer from "./reducers/onboardingSlice";
import mobileSidebarReducer from "./reducers/mobileSidebarSlice";

export const store = configureStore({
  reducer: {
    user: userReducer,
    company: companyReducer,
    benefits: benefitsReducer,
    qHarmonyBot: qHarmonyBotReducer,
    onboarding: onboardingReducer,
    mobileSidebarToggle: mobileSidebarReducer
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;