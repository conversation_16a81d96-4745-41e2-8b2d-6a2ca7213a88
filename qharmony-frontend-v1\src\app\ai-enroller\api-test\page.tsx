'use client';

import React, { useState } from 'react';
import { getApiBaseUrl, getUserId } from '../../../utils/env';

interface TestResult {
  endpoint: string;
  status: number;
  ok: boolean;
  data?: any;
  error?: string;
  issues: string[];
}

interface ValidationResults {
  plans: { structure: boolean; pagination: boolean; filtering: boolean };
  assignments: { structure: boolean; pagination: boolean; filtering: boolean };
  companyAssignments: { structure: boolean; pagination: boolean; filtering: boolean };
}

export default function ApiTestPage() {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [validationResults, setValidationResults] = useState<ValidationResults | null>(null);
  const [testCompanyId, setTestCompanyId] = useState('6756b8b4e5f4a2b8c9d0e1f2');

  const makeApiRequest = async (endpoint: string, params: Record<string, any> = {}) => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();
    const url = new URL(`${API_BASE_URL}${endpoint}`);
    
    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      const data = await response.json();
      
      return {
        status: response.status,
        ok: response.ok,
        data: data,
        url: url.toString()
      };
    } catch (error: any) {
      return {
        status: 0,
        ok: false,
        error: error.message,
        url: url.toString()
      };
    }
  };

  const validatePlansStructure = (data: any): string[] => {
    const issues: string[] = [];
    
    if (!data.plans || !Array.isArray(data.plans)) {
      issues.push('Missing or invalid plans array');
      return issues;
    }

    if (data.plans.length > 0) {
      const plan = data.plans[0];
      const requiredFields = ['_id', 'planName', 'coverageType', 'status'];
      
      requiredFields.forEach(field => {
        if (!(field in plan)) {
          issues.push(`Missing required field: ${field}`);
        }
      });

      const validStatuses = ['Active', 'Inactive', 'Draft'];
      if (plan.status && !validStatuses.includes(plan.status)) {
        issues.push(`Invalid status value: ${plan.status}. Expected: ${validStatuses.join(', ')}`);
      }
    }

    return issues;
  };

  const validatePlanAssignmentsStructure = (data: any): string[] => {
    const issues: string[] = [];
    
    if (!data.assignments || !Array.isArray(data.assignments)) {
      issues.push('Missing or invalid assignments array');
      return issues;
    }

    if (data.totalCount === undefined) {
      issues.push('Missing totalCount field');
    }

    if (data.assignments.length > 0) {
      const assignment = data.assignments[0];
      const requiredFields = ['_id', 'planId', 'companyId', 'status'];
      
      requiredFields.forEach(field => {
        if (!(field in assignment)) {
          issues.push(`Missing required field: ${field}`);
        }
      });

      const validStatuses = ['Active', 'Expired', 'Deactivated'];
      if (assignment.status && !validStatuses.includes(assignment.status)) {
        issues.push(`Invalid status value: ${assignment.status}. Expected: ${validStatuses.join(', ')}`);
      }
    }

    return issues;
  };

  const testPagination = async (endpoint: string): Promise<boolean> => {
    const response = await makeApiRequest(endpoint, { page: 1, limit: 5 });
    
    if (!response.ok) {
      return false;
    }

    const issues: string[] = [];
    
    if (response.data.totalCount === undefined) {
      issues.push('Missing totalCount in paginated response');
    }
    
    if (response.data.totalPages === undefined) {
      issues.push('Missing totalPages in paginated response');
    }

    const dataArray = response.data.plans || response.data.assignments || [];
    if (dataArray.length > 5) {
      issues.push(`Expected max 5 items, got ${dataArray.length}`);
    }

    return issues.length === 0;
  };

  const testFiltering = async (endpoint: string): Promise<boolean> => {
    const response = await makeApiRequest(endpoint, { status: 'Active' });
    
    if (!response.ok) {
      return false;
    }

    const dataArray = response.data.plans || response.data.assignments || [];
    const nonActiveItems = dataArray.filter((item: any) => item.status !== 'Active');
    
    return nonActiveItems.length === 0;
  };

  const runValidation = async () => {
    setTesting(true);
    setResults([]);
    setValidationResults(null);

    const testResults: TestResult[] = [];
    const validation: ValidationResults = {
      plans: { structure: false, pagination: false, filtering: false },
      assignments: { structure: false, pagination: false, filtering: false },
      companyAssignments: { structure: false, pagination: false, filtering: false }
    };

    // Test 1: Plans endpoint
    const plansResponse = await makeApiRequest('/api/pre-enrollment/plans');
    const plansIssues = plansResponse.ok ? validatePlansStructure(plansResponse.data) : ['Request failed'];
    testResults.push({
      endpoint: '/api/pre-enrollment/plans',
      ...plansResponse,
      issues: plansIssues
    });

    if (plansResponse.ok) {
      validation.plans.structure = plansIssues.length === 0;
      validation.plans.pagination = await testPagination('/api/pre-enrollment/plans');
      validation.plans.filtering = await testFiltering('/api/pre-enrollment/plans');
    }

    // Test 2: Plan assignments endpoint
    const assignmentsResponse = await makeApiRequest('/api/pre-enrollment/plan-assignments');
    const assignmentsIssues = assignmentsResponse.ok ? validatePlanAssignmentsStructure(assignmentsResponse.data) : ['Request failed'];
    testResults.push({
      endpoint: '/api/pre-enrollment/plan-assignments',
      ...assignmentsResponse,
      issues: assignmentsIssues
    });

    if (assignmentsResponse.ok) {
      validation.assignments.structure = assignmentsIssues.length === 0;
      validation.assignments.pagination = await testPagination('/api/pre-enrollment/plan-assignments');
      validation.assignments.filtering = await testFiltering('/api/pre-enrollment/plan-assignments');
    }

    // Test 3: Company plan assignments endpoint
    const companyEndpoint = `/api/pre-enrollment/plan-assignments/company/${testCompanyId}`;
    const companyResponse = await makeApiRequest(companyEndpoint);
    const companyIssues = companyResponse.ok ? validatePlanAssignmentsStructure(companyResponse.data) : ['Request failed'];
    testResults.push({
      endpoint: companyEndpoint,
      ...companyResponse,
      issues: companyIssues
    });

    if (companyResponse.ok) {
      validation.companyAssignments.structure = companyIssues.length === 0;
      validation.companyAssignments.pagination = await testPagination(companyEndpoint);
      validation.companyAssignments.filtering = await testFiltering(companyEndpoint);
    }

    setResults(testResults);
    setValidationResults(validation);
    setTesting(false);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '24px' }}>
        API Validation Dashboard
      </h1>
      
      <div style={{ marginBottom: '24px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
          Test Company ID:
        </label>
        <input
          type="text"
          value={testCompanyId}
          onChange={(e) => setTestCompanyId(e.target.value)}
          style={{
            padding: '8px 12px',
            border: '1px solid #D1D5DB',
            borderRadius: '6px',
            width: '300px',
            marginRight: '12px'
          }}
        />
        <button
          onClick={runValidation}
          disabled={testing}
          style={{
            padding: '8px 16px',
            background: testing ? '#9CA3AF' : '#3B82F6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: testing ? 'not-allowed' : 'pointer'
          }}
        >
          {testing ? 'Testing...' : 'Run Validation'}
        </button>
      </div>

      {validationResults && (
        <div style={{ marginBottom: '32px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px' }}>
            Validation Summary
          </h2>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
            {Object.entries(validationResults).map(([endpoint, result]) => (
              <div key={endpoint} style={{
                background: 'white',
                border: '1px solid #E5E7EB',
                borderRadius: '8px',
                padding: '16px'
              }}>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                  {endpoint.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </h3>
                {Object.entries(result).map(([test, passed]) => (
                  <div key={test} style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                    <span style={{ marginRight: '8px' }}>{passed ? '✅' : '❌'}</span>
                    <span>{test.charAt(0).toUpperCase() + test.slice(1)}</span>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      )}

      {results.length > 0 && (
        <div>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px' }}>
            Detailed Results
          </h2>
          {results.map((result, index) => (
            <div key={index} style={{
              background: 'white',
              border: '1px solid #E5E7EB',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '16px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <span style={{ marginRight: '12px' }}>
                  {result.ok ? '✅' : '❌'}
                </span>
                <h3 style={{ fontSize: '16px', fontWeight: '600' }}>
                  {result.endpoint}
                </h3>
                <span style={{
                  marginLeft: 'auto',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  background: result.ok ? '#D1FAE5' : '#FEE2E2',
                  color: result.ok ? '#065F46' : '#991B1B'
                }}>
                  {result.status}
                </span>
              </div>
              
              {result.issues.length > 0 && (
                <div>
                  <h4 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>Issues:</h4>
                  <ul style={{ margin: 0, paddingLeft: '20px' }}>
                    {result.issues.map((issue, i) => (
                      <li key={i} style={{ color: '#DC2626', fontSize: '14px' }}>{issue}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {result.ok && result.data && (
                <details style={{ marginTop: '12px' }}>
                  <summary style={{ cursor: 'pointer', fontSize: '14px', fontWeight: '500' }}>
                    View Response Data
                  </summary>
                  <pre style={{
                    background: '#F9FAFB',
                    padding: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    overflow: 'auto',
                    marginTop: '8px'
                  }}>
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
