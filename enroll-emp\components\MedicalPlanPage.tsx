import React from 'react';
import { BotQuestion } from './BotQuestion';
import { UserProfile } from '../page';

interface MedicalPlanPageProps {
  userProfile: UserProfile;
  onPlanSelect: (planData: any) => void;
  recommendation: any;
}

export const MedicalPlanPage = ({ userProfile, onPlanSelect, recommendation }: MedicalPlanPageProps) => {
  const plans = [
    {
      id: 'hsa',
      name: 'Blue Cross HSA Plan',
      cost: 45.20,
      deductible: 3000,
      type: 'HDHP',
      features: ['Lowest monthly cost', 'HSA eligible', 'Perfect for healthy individuals']
    },
    {
      id: 'ppo',
      name: 'Anthem PPO 035',
      cost: 82.90,
      deductible: 2000,
      type: 'PPO',
      features: ['Balanced cost and coverage', 'No referrals needed', 'Good for moderate usage']
    },
    {
      id: 'hmo',
      name: 'Kaiser HMO',
      cost: 65.40,
      deductible: 1500,
      type: 'HMO',
      features: ['Integrated healthcare', 'Lower cost', 'Coordinated care']
    }
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <BotQuestion 
        question="Based on your answers, here's my medical plan recommendation!"
        context={recommendation?.reason || "I've analyzed your needs and found the best options for you."}
      />

      {recommendation && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">🎯 Recommended for You</h3>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-semibold">{recommendation.plan.name}</h4>
            <p className="text-2xl font-bold text-green-600">${recommendation.plan.cost}/paycheck</p>
            <p className="text-sm text-gray-600">Deductible: ${recommendation.plan.deductible}</p>
            <ul className="mt-2 space-y-1">
              {recommendation.plan.features.map((feature: string, index: number) => (
                <li key={index} className="text-sm">✓ {feature}</li>
              ))}
            </ul>
            <button
              onClick={() => onPlanSelect(recommendation.plan)}
              className="mt-4 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Select This Plan
            </button>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">All Medical Plans</h3>
        <div className="grid gap-4">
          {plans.map(plan => (
            <div key={plan.id} className="border rounded-lg p-4 hover:border-blue-300 transition-colors">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{plan.name}</h4>
                  <span className="text-sm bg-gray-100 px-2 py-1 rounded">{plan.type}</span>
                </div>
                <div className="text-right">
                  <p className="text-xl font-bold text-green-600">${plan.cost}/paycheck</p>
                  <p className="text-sm text-gray-600">Deductible: ${plan.deductible}</p>
                </div>
              </div>
              <ul className="space-y-1 mb-4">
                {plan.features.map((feature, index) => (
                  <li key={index} className="text-sm">✓ {feature}</li>
                ))}
              </ul>
              <button
                onClick={() => onPlanSelect(plan)}
                className="w-full px-4 py-2 border border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition-colors"
              >
                Select Plan
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
