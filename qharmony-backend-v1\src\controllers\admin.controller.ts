import express from 'express';
import Controller from '../interfaces/controller.interface';
import logger from '../utils/logger';
import multer from 'multer';
import UserModelClass, { UserDataInterface } from '../nosql/user.model';
import CompanyModelClass, {
  CompanyDataInterface,
} from '../nosql/company.model';
import { v4 as uuidv4 } from 'uuid';
import BenefitModelClass from '../nosql/benefit.model';
import EmployeeData, { EmployeeUpdateData } from '../interfaces/employee.interface';
import { BENEFIT_TYPE_SUBTYPE_MAP } from '../constants';
import { AuthService } from '../services/auth.service';
// import AWSService from '../services/aws';
import AzureBlobService from '../services/azure';
import AzureNamespaceService from '../services/azureNamespace.service';

class AdminController implements Controller {
  public router = express.Router();
  public authService = new AuthService();
  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    const upload = multer({ storage: multer.memoryStorage() });
    this.router.post('/admin/onboard', this.onboardAdmin);
    this.router.post('/admin/self-onboard', this.selfOnboardAdmin);
    this.router.post('/admin/add/employer', this.addEmployer);
    this.router.post('/admin/add/employees', this.addEmployees);
    this.router.post('/admin/send-user-login-link', this.sendUserLoginLink);
    this.router.get('/admin/all-employees', this.viewEmployees);
    this.router.put('/admin/update/employee', this.updateEmployee); // 🎯 Updated: Allows both employees and employers to update details
    this.router.post('/admin/update/signature', this.updateSignature); // 🎯 NEW: Dedicated signature update endpoint
    this.router.get('/admin/all-companies', this.getAdminCompanies);
    this.router.post(
      '/admin/update-company-logo',
      upload.single('logoImage'),
      this.updateCompanyLogo
    );
  }

  private onboardAdmin = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { company, user } = request.body as {
        company: CompanyDataInterface;
        user: UserDataInterface;
      };

      user.email = user.email.toLowerCase();

      if (company.adminEmail !== user.email) {
        response.status(400).json({
          error: 'Admin email in company and current user email do not match',
        });
        return;
      }

      const adminEmail = user.email;
      // Check if the company already exists.
      const existingCompany = await CompanyModelClass.getDataByAdminEmail({
        adminEmail,
      });
      if (existingCompany) {
        response.status(400).json({ error: 'Company already exists' });
        return;
      }

      if (company.industry === 'Insurance Broker') {
        company.isBrokerage = true;
        user.isBroker = true;
      }

      // Save the company in the database.
      const companyEntity = await CompanyModelClass.addData(company);

      if (!companyEntity) {
        response.status(400).json({ error: 'Error onboarding company' });
        return;
      }
      // Save the first user in the database.
      const userEntity = await UserModelClass.addData({
        ...user,
        companyId: companyEntity._id.toString(),
        isActivated: true,
        isDisabled: false,
        details: {
          phoneNumber: user.details?.phoneNumber || '',
          department: user.details?.department || '',
          title: company.adminRole || 'Admin',
          role: company.adminRole || 'Admin',
        },
      });
      if (!userEntity) {
        response.status(400).json({ error: 'Error onboarding user' });
        return;
      }

      // Create the benfits skeleton for the company. Iterate through the BENEFIT_TYPE_SUBTYPE_MAP and create the benefits.
      const benefits = [];

      for (const benefitUnit of BENEFIT_TYPE_SUBTYPE_MAP) {
        const subTypes = benefitUnit.subTypes;
        for (const subType of subTypes) {
          const benefit = {
            companyId: companyEntity._id,
            type: benefitUnit.type,
            subType,
            heading: '',
            description: '',
            imageS3Urls: [],
            links: [],
            isActivated: false,
          };
          benefits.push(benefit);
        }
      }

      // Save the benefits in the database.
      await BenefitModelClass.addDataList(benefits);

      // Update login timestamp for the new admin
      await UserModelClass.updateLoginTimestamp(userEntity._id.toString());

      response
        .status(200)
        .json({ companyId: companyEntity._id, userId: userEntity._id });
    } catch (error) {
      logger.error('Error onboarding company and user:', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private selfOnboardAdmin = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      let { userEmail } = request.body as { userEmail: string };
      userEmail = userEmail.toLowerCase();
      const userDomain = userEmail.split('@')[1];
      const companies = await CompanyModelClass.getAllData();

      for (const company of companies) {
        const companyDomain = company.adminEmail.split('@')[1].toLowerCase();
        if (companyDomain === userDomain) {
          response.status(400).json({
            error: 'Domain already exists',
            existingAdminEmail: company.adminEmail,
          });
          return;
        }
      }
      // SEND MAGIC LINK TO companyAdminEmail
      const companyDetails = {
        name: '', // companyName variable
        adminEmail: userEmail, // companyAdminEmail variable
        adminRole: '',
        companySize: 0,
        industry: '',
        location: '',
        website: '',
        howHeard: '',
        brokerId: '', // brokerId variable
        brokerageId: '', // brokerageEntity._id converted to string
        isBrokerage: false,
        isActivated: false,
      };
      const userDetails = {
        name: '', // companyAdminName variable
        email: userEmail, // companyAdminEmail variable
        isAdmin: true,
        isBroker: false,
        isActivated: true,
        role: 'admin',
        details: {
          phoneNumber: '', // Add phone number here
          department: '', // Add department here
          title: '', // Add title here
          role: 'admin', // Add role here
        },
      };

      const additionalParams = {
        email: userEmail,
        isAdmin: true,
      };

      await this.authService.sendOnboardingMagicLink(
        userEmail.toLowerCase(),
        companyDetails,
        userDetails,
        additionalParams
      );
      response.status(200).json({ companies });
      return;
    } catch (error) {
      logger.error('Error onboarding company and user:', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private addEmployer = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      let { brokerId, companyName, companyAdminEmail, companyAdminName } =
        request.body as {
          brokerId: string;
          companyName: string;
          companyAdminEmail: string;
          companyAdminName: string;
        };

      companyAdminEmail = companyAdminEmail.toLowerCase();

      const broker = await UserModelClass.getDataById(brokerId);

      console.log(broker);

      if (!broker || !Boolean(broker?.isBroker)) {
        response.status(400).json({ error: 'Invalid broker' });
        return;
      }

      const brokerageEntity = await CompanyModelClass.getDataById(
        broker.companyId
      );
      if (!brokerageEntity || !Boolean(brokerageEntity?.isBrokerage)) {
        response.status(400).json({ error: 'Invalid brokerage' });
        return;
      }

      // console.log(broker, brokerageEntity);

      if (broker.email !== brokerageEntity.adminEmail) {
        response.status(400).json({
          error: 'Admin email in brokerage and current user email do not match',
        });
        return;
      }

      // SEND MAGIC LINK TO companyAdminEmail
      const companyDetails = {
        name: companyName, // companyName variable
        adminEmail: companyAdminEmail, // companyAdminEmail variable
        adminRole: '',
        companySize: 0,
        industry: '',
        location: '',
        website: '',
        howHeard: '',
        brokerId: brokerId, // brokerId variable
        brokerageId: broker.companyId, // brokerageEntity._id converted to string
        isBrokerage: false,
        isActivated: true,
      };
      const userDetails = {
        name: companyAdminName,
        email: companyAdminEmail,
        isAdmin: true,
        isBroker: false,
        isActivated: true,
        role: 'admin',
        details: {
          phoneNumber: '', // Add phone number here
          department: '', // Add department here
          title: '', // Add title here
          role: 'admin', // Add role here
        },
      };

      const additionalParams = {
        email: companyAdminEmail,
        isAdmin: true,
      };

      await this.authService.sendOnboardingMagicLink(
        companyAdminEmail,
        companyDetails,
        userDetails,
        additionalParams
      );

      response.status(200).json({
        companyDetails,
        userDetails,
      });
    } catch (error) {
      logger.error('Error adding employer.', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private addEmployees = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const adminId = request.headers['user-id'] as string;
      const { employeeList } = request.body as {
        employeeList: EmployeeData[];
      };

      // Convert all employee emails to lowercase
      const normalizedEmployeeList = employeeList.map(employee => ({
        ...employee,
        email: employee.email.toLowerCase(),
      }));

      // Check if admin is valid.
      const admin = await UserModelClass.getDataById(adminId);
      if (!admin || !Boolean(admin?.isAdmin)) {
        response.status(400).json({ error: 'Invalid admin' });
        return;
      }

      // Check if the company exists.
      const company = await CompanyModelClass.getDataById(admin.companyId);
      if (!company) {
        response.status(400).json({ error: 'Company does not exist' });
        return;
      }

      // Check if the admin is the admin of the company.
      if (company.adminEmail !== admin.email) {
        response
          .status(400)
          .json({ error: 'Admin does not have permission to add employees' });
        return;
      }

      // Process each employee

      const results = await Promise.all(
        normalizedEmployeeList.map(async (employee) => {
          // Check if employee already exists for this company
          const existingEmployee = await UserModelClass.getDataByEmail({
            email: employee.email,
          });

          if (existingEmployee) {
            // Employee already exists, do nothing
            return {
              email: employee.email,
              status: 'skipped',
              message: 'Employee already exists',
            };
          } else {
            if (!company._id) {
              return {
                email: employee.email,
                status: 'error',
                message: 'Company does not exist',
              };
            }
            // Employee does not exist, add to the database
            const newEmployee = await UserModelClass.addData({
              name: employee.name,
              email: employee.email,
              companyId: company._id.toString(),
              isAdmin: false,
              isBroker: false,
              isActivated: false,
              isDisabled: false,
              role: 'employee',
              details: {
                phoneNumber: employee.phoneNumber,
                department: employee.department,
                title: employee.title,
                role: 'employee',
              },
            });

            const additionalParams = {
              email: employee.email,
              userId: newEmployee?._id,
              companyId: company._id,
            };
            await this.authService.sendLoginMagicLink(
              employee.email,
              additionalParams
            );
            console.log(`Magic link sent to email ${employee.email}`);
            if (newEmployee) {
              return {
                email: employee.email,
                status: 'added',
                message: 'Employee added successfully',
              };
            } else {
              return {
                email: employee.email,
                status: 'error',
                message: 'Failed to add employee',
              };
            }
          }
        })
      );

      const addedCount = results.filter((r) => r.status === 'added').length;
      const isSuccess = addedCount > 0;

      response
        .status(200)
        .json({ is_success: isSuccess, addedCount: addedCount });
      return;
    } catch (error) {
      logger.error('Error adding employees.', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private viewEmployees = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;

      if (!userId) {
        response.status(400).json({ error: 'Invalid request' });
        return;
      }

      const user = await UserModelClass.getDataById(userId);

      if (!user || !Boolean(user?.isAdmin)) {
        response.status(400).json({ error: 'Invalid user' });
        return;
      }

      const company = await CompanyModelClass.getDataByAdminEmail({
        adminEmail: user.email,
      });

      if (!company) {
        response.status(400).json({ error: 'Company not found' });
        return;
      }

      if (company.adminEmail !== user.email) {
        response.status(400).json({
          error: 'Admin email in company and current user email do not match',
        });
        return;
      }
      const employees = await UserModelClass.getDataByCompanyId(user.companyId);

      // const activeEmployees = employees.filter(
      //   (employee) => employee.isDisabled === false
      // );

      response.status(200).json({ employees: employees });
      return;
    } catch (error) {
      logger.error('Error viewing employees.', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private updateEmployee = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { updatedDetails } = request.body as {
        updatedDetails: EmployeeUpdateData;
      };
      if (!userId) {
        response.status(400).json({ error: 'User ID required in headers' });
        return;
      }
      // Get the requesting user - this is the user being updated
      const userToUpdate = await UserModelClass.getDataById(userId);
      if (!userToUpdate) {
        response.status(400).json({ error: 'User to update does not exist' });
        return;
      }
      // 🎯 UPDATED: Allow limited updates for non-activated users
      // Non-activated users can only update signature-related fields for enrollment
      if (!userToUpdate.isActivated) {
        const isSignatureOnlyUpdate = updatedDetails.details &&
          Object.keys(updatedDetails.details).every(key =>
            ['signature', 'enrollmentSignature'].includes(key)
          ) &&
          Object.keys(updatedDetails).every(key =>
            ['details'].includes(key)
          );

        if (!isSignatureOnlyUpdate) {
          response.status(403).json({
            error: 'User account is not activated. Only signature updates are allowed for enrollment purposes.'
          });
          return;
        }
      }
      // Get company to verify user belongs to a valid company
      const company = await CompanyModelClass.getDataById(userToUpdate.companyId);
      if (!company) {
        response.status(400).json({ error: 'Company does not exist' });
        return;
      }
      // Filter out email from updates for security
      const { email, ...filteredDetails } = updatedDetails;
      // Update user details
      await UserModelClass.updateData(userId, filteredDetails);
      response.status(200).json({
        message: 'User updated successfully',
        updatedUser: {
          id: userToUpdate._id,
          name: userToUpdate.name,
          email: userToUpdate.email
        }
      });
    } catch (error) {
      logger.error('Error updating user:', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private updateSignature = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const userId = request.headers['user-id'] as string;
      const { signatureData } = request.body as {
        signatureData: string;
      };

      if (!userId) {
        response.status(400).json({ error: 'User ID required in headers' });
        return;
      }

      if (!signatureData) {
        response.status(400).json({
          error: 'signatureData is required'
        });
        return;
      }

      // Get the user
      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        response.status(400).json({ error: 'User not found' });
        return;
      }

      // 🎯 UPDATED: Allow signature updates for non-activated users (needed for enrollment)
      // Signature updates are allowed for enrollment purposes even before activation

      // Validate signature data size (limit to 1MB Base64)
      if (signatureData.length > 1048576) {
        response.status(400).json({
          error: 'Signature data too large. Maximum size is 1MB.'
        });
        return;
      }

      // Validate Base64 format
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(signatureData)) {
        response.status(400).json({
          error: 'Invalid signature data format. Must be Base64 encoded.'
        });
        return;
      }

      // Prepare signature update
      const signatureUpdate = {
        details: {
          ...user.details,
          enrollmentSignature: {
            signatureData,
            signedAt: new Date(),
            isVerified: false
          }
        }
      };

      // Update user with signature
      await UserModelClass.updateData(userId, signatureUpdate);

      response.status(200).json({
        message: 'Enrollment signature updated successfully',
        signedAt: new Date().toISOString(),
        employeeName: user.name // Use existing name from user model
      });

    } catch (error) {
      logger.error('Error updating signature:', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private sendUserLoginLink = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const { userId, companyId } = request.body as {
        userId: string;
        companyId: string;
      };

      const user = await UserModelClass.getDataById(userId);
      if (!user || user.companyId !== companyId) {
        response.status(400).json({ error: 'Invalid user' });
        return;
      }

      const additionalParams = {
        email: user.email,
        userId: user._id,
        companyId: user.companyId,
      };
      await this.authService.sendLoginMagicLink(user.email, additionalParams);

      response.status(200).json({ message: 'Magic link sent successfully' });
      return;
    } catch (error) {
      logger.error('Error sending user login link.', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  private getAdminCompanies = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const adminId = request.headers['user-id'] as string;

      // Check if admin is valid.
      const admin = await UserModelClass.getDataById(adminId);
      if (!admin || !Boolean(admin?.isAdmin)) {
        response.status(400).json({ error: 'Invalid admin' });
        return;
      }

      // Get companies added by the admin
      const companies = await CompanyModelClass.getDataByBrokerId(adminId);

      response.status(200).json({ companies });
    } catch (error) {
      logger.error('Error fetching companies added by admin.', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };

  updateCompanyLogo = async (
    request: express.Request,
    response: express.Response
  ) => {
    try {
      const adminId = request.headers['user-id'] as string;
      // Check if admin is valid.
      const admin = await UserModelClass.getDataById(adminId);
      if (!admin || !Boolean(admin?.isAdmin)) {
        response.status(400).json({ error: 'Invalid admin' });
        return;
      }

      // Check if the company exists.
      const company = await CompanyModelClass.getDataById(admin.companyId);
      if (!company) {
        response.status(400).json({ error: 'Company does not exist' });
        return;
      }

      if (!request.file) {
        response.status(400).json({ error: 'No file uploaded' });
        return;
      }

      // ===================================================================================================
      // AWS LOGIC START
      // ===================================================================================================

      // const bucketName = `employer-${admin.companyId.toString()}`;
      // const exists = await AWSService.bucketExists(bucketName);
      // if (!exists) {
      //   const data = await AWSService.createBucket(bucketName);
      //   logger.info(
      //     `Bucket ${bucketName} created successfully: ${data.Location}`
      //   );
      // }

      // const fileExtension = request.file.originalname.split('.').pop() || '';
      // const fileNameWithoutExtension = request.file.originalname.replace(
      //   /\.[^/.]+$/,
      //   ''
      // );
      // const objectKey = `company-logo-${Date.now()}-${uuidv4()}${fileNameWithoutExtension}.${fileExtension}`;

      // const uploadParams: AWS.S3.PutObjectRequest = {
      //   Bucket: bucketName,
      //   Key: objectKey,
      //   Body: request.file.buffer,
      //   ContentType: request.file.mimetype,
      // };

      // const uploadResult = await AWSService.upload(uploadParams);
      // logger.info(
      //   `File ${request.file.originalname} uploaded successfully: ${uploadResult.Location}`
      // );

      // ===================================================================================================
      // AWS LOGIC END
      // ===================================================================================================

      // ===================================================================================================
      // AZURE LOGIC START
      // ===================================================================================================

      const containerName = AzureNamespaceService.getEmployerContainer(admin.companyId.toString());
      const blobName = `company-logo-${Date.now()}-${uuidv4()}-${request.file.originalname}`;
      const contentType = request.file.mimetype

      const containerExists = await AzureBlobService.containerExists(containerName);

      if (!containerExists) {
        await AzureBlobService.createContainer(containerName);
      }

      await AzureBlobService.upload(containerName, blobName, request.file.buffer, contentType);

      // ===================================================================================================
      // AZURE LOGIC END
      // ===================================================================================================

      // Update the company details with the logo key
      // company.details = { logo: objectKey };
      company.details = { logo: blobName };      // Replace with blob name because we can only add one obj name
      await CompanyModelClass.updateData({
        id: admin.companyId,
        data: { details: company.details },
      });

      response
        .status(200)
        .json({ message: 'Company logo updated successfully' });
    } catch (error) {
      logger.error('Error updating company logo.', error);
      response.status(500).json({ error: 'Internal server error' });
      return;
    }
  };
}

export default AdminController;
