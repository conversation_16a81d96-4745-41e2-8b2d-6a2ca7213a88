'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { User, CheckCircle, Printer, Mail, Download, Stethoscope, Eye, Heart, Shield, Home } from 'lucide-react';
import { FloatingHelp } from './FloatingHelp';
import ChatModal from './ChatModal';
import { useAppSelector } from '../../../../redux/hooks';
import { RootState } from '../../../../redux/store';

interface ConfirmationPageProps {
  enrollmentData?: any;
  planAssignments?: Record<string, any[]>; // Plan assignments for dynamic dates
}

interface EnrollmentSummary {
  dentalPlan?: any;
  visionPlan?: any;
  lifePlan?: any;
  addPlan?: any;
  dependents?: any[];
  selectedCoverageTier?: string;
  enrollmentDate?: string;
  dentalWaived?: boolean;
  visionWaived?: boolean;
  lifeWaived?: boolean;
  addWaived?: boolean;
  dentalWaiveReason?: string;
  visionWaiveReason?: string;
  lifeWaiveReason?: string;
  addWaiveReason?: string;
}

const ConfirmationPage: React.FC<ConfirmationPageProps> = ({ enrollmentData, planAssignments = {} }) => {
  const router = useRouter();
  const [showHelp, setShowHelp] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);
  const [enrollmentSummary, setEnrollmentSummary] = useState<EnrollmentSummary | null>(null);
  const userDetails = useAppSelector((state: RootState) => state.user.userProfile);

  // Function to get the earliest start date from plan assignments
  const getBenefitsStartDate = (): string => {
    try {
      // Get all plan assignments from all categories
      const allPlanAssignments = Object.values(planAssignments).flat();

      if (allPlanAssignments.length === 0) {
        console.log('🔍 No plan assignments found, using default date');
        return 'January 1, 2025'; // Fallback to default
      }

      // Extract start dates from plan assignments
      const startDates: Date[] = [];

      for (const planAssignment of allPlanAssignments) {
        // Check for start date in the assignment object
        const assignment = planAssignment.assignment || planAssignment;
        const startDateStr = assignment.startDate || assignment.effectiveDate || assignment.coverageStartDate;

        if (startDateStr) {
          const startDate = new Date(startDateStr);
          if (!isNaN(startDate.getTime())) {
            startDates.push(startDate);
          }
        }
      }

      if (startDates.length === 0) {
        console.log('🔍 No valid start dates found in plan assignments, using default');
        return 'January 1, 2025'; // Fallback to default
      }

      // Get the earliest start date
      const earliestDate = new Date(Math.min(...startDates.map(date => date.getTime())));

      // Format the date nicely
      const formattedDate = earliestDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      console.log('🔍 Dynamic benefits start date calculated:', formattedDate);
      console.log('🔍 Plan assignments used for date calculation:', allPlanAssignments.length, 'assignments');
      return formattedDate;
    } catch (error) {
      console.error('❌ Error getting benefits start date:', error);
      return 'January 1, 2025'; // Fallback to default
    }
  };

  // Load enrollment summary data on component mount
  useEffect(() => {
    console.log('🔍 ConfirmationPage - Plan assignments received:', planAssignments);
    console.log('🔍 ConfirmationPage - Plan assignment keys:', Object.keys(planAssignments));

    const storedSummary = localStorage.getItem('enrollmentSummarySnapshot');
    console.log('🔍 Checking for stored enrollment summary...');
    if (storedSummary) {
      try {
        const summary = JSON.parse(storedSummary);
        setEnrollmentSummary(summary);
        console.log('📸 Loaded enrollment summary snapshot:', summary);
        console.log('🛡️ Life insurance data in summary:', {
          lifePlan: summary.lifePlan,
          lifeWaived: summary.lifeWaived,
          lifeWaiveReason: summary.lifeWaiveReason,
          hasLifePlan: !!summary.lifePlan,
          lifePlanKeys: summary.lifePlan ? Object.keys(summary.lifePlan) : 'null'
        });
        console.log('👓 Vision data in summary:', {
          visionPlan: summary.visionPlan,
          visionWaived: summary.visionWaived,
          visionWaiveReason: summary.visionWaiveReason,
          hasVisionPlan: !!summary.visionPlan,
          visionPlanKeys: summary.visionPlan ? Object.keys(summary.visionPlan) : 'null'
        });
        console.log('🦷 Dental data in summary:', {
          dentalPlan: summary.dentalPlan,
          dentalWaived: summary.dentalWaived,
          dentalWaiveReason: summary.dentalWaiveReason,
          hasDentalPlan: !!summary.dentalPlan
        });
        console.log('🛡️ AD&D data in summary:', {
          addPlan: summary.addPlan,
          addWaived: summary.addWaived,
          addWaiveReason: summary.addWaiveReason,
          hasADDPlan: !!summary.addPlan,
          addPlanKeys: summary.addPlan ? Object.keys(summary.addPlan) : 'null'
        });

        // Check what should be displayed
        console.log('🎯 Plan display logic check:', {
          shouldShowDental: !!(summary.dentalPlan && !summary.dentalWaived),
          shouldShowVision: !!(summary.visionPlan && !summary.visionWaived),
          shouldShowLife: !!(summary.lifePlan && !summary.lifeWaived),
          shouldShowADD: !!(summary.addPlan && !summary.addWaived),
          shouldShowDentalWaived: !!summary.dentalWaived,
          shouldShowVisionWaived: !!summary.visionWaived,
          shouldShowLifeWaived: !!summary.lifeWaived,
          shouldShowADDWaived: !!summary.addWaived
        });

        // Additional debugging for plan display issues
        console.log('🔍 Detailed plan analysis:');
        console.log('  Dental Plan:', summary.dentalPlan ? 'EXISTS' : 'NULL', '| Waived:', summary.dentalWaived ? 'YES' : 'NO');
        console.log('  Vision Plan:', summary.visionPlan ? 'EXISTS' : 'NULL', '| Waived:', summary.visionWaived ? 'YES' : 'NO');
        console.log('  Life Plan:', summary.lifePlan ? 'EXISTS' : 'NULL', '| Waived:', summary.lifeWaived ? 'YES' : 'NO');
        console.log('  AD&D Plan:', summary.addPlan ? 'EXISTS' : 'NULL', '| Waived:', summary.addWaived ? 'YES' : 'NO');

        // If AD&D data is missing from snapshot, try to reconstruct it from localStorage
        if (!summary.addPlan && !summary.addWaived) {
          console.log('🔧 AD&D data missing from snapshot, checking localStorage directly...');
          const addPlanData = localStorage.getItem('selectedADDPlan');
          const addWaivedData = localStorage.getItem('addWaived');

          if (addPlanData) {
            try {
              const parsedAddPlan = JSON.parse(addPlanData);
              console.log('🔧 Found AD&D plan in localStorage:', parsedAddPlan);
              summary.addPlan = parsedAddPlan;
            } catch (e) {
              console.error('❌ Error parsing AD&D plan from localStorage:', e);
            }
          }

          if (addWaivedData === 'true') {
            console.log('🔧 Found AD&D waived in localStorage');
            summary.addWaived = true;
            summary.addWaiveReason = localStorage.getItem('addWaiveReason');
          }

          // Update the enrollment summary with the reconstructed data
          setEnrollmentSummary({...summary});
          console.log('🔧 Updated enrollment summary with AD&D data:', summary);
        }
      } catch (error) {
        console.error('❌ Error parsing enrollment summary:', error);
      }
    } else {
      console.warn('⚠️ No enrollment summary snapshot found in localStorage');
      // Fallback: try to construct from enrollmentData prop
      if (enrollmentData) {
        console.log('🔄 Attempting to use enrollmentData as fallback:', enrollmentData);
        const fallbackSummary = {
          dentalPlan: enrollmentData.selectedDental,
          visionPlan: enrollmentData.selectedVision,
          lifePlan: enrollmentData.selectedLife,
          addPlan: enrollmentData.selectedADD,
          dependents: enrollmentData.dependents,
          selectedCoverageTier: enrollmentData.familyMembers || 'Employee Only',
          enrollmentDate: new Date().toISOString(),
          dentalWaived: localStorage.getItem('dentalWaived') === 'true',
          visionWaived: localStorage.getItem('visionWaived') === 'true',
          lifeWaived: localStorage.getItem('lifeWaived') === 'true',
          addWaived: localStorage.getItem('addWaived') === 'true',
          dentalWaiveReason: localStorage.getItem('dentalWaiveReason') || undefined,
          visionWaiveReason: localStorage.getItem('visionWaiveReason') || undefined,
          lifeWaiveReason: localStorage.getItem('lifeWaiveReason') || undefined,
          addWaiveReason: localStorage.getItem('addWaiveReason') || undefined
        };
        setEnrollmentSummary(fallbackSummary);
        console.log('🔄 Using fallback enrollment summary:', fallbackSummary);
      }
    }
  }, [enrollmentData, planAssignments]);

  const handlePrintSummary = () => {
    if (!enrollmentSummary) {
      console.warn('No enrollment summary data available for printing');
      return;
    }

    // Calculate costs and prepare plan data
    const planCosts: any[] = [];
    const waivedCoverages: string[] = [];
    let totalEmployeeCost = 0;
    let totalEmployerCost = 0;

    // Helper function to calculate cost for a plan
    const calculatePlanCost = (plan: any, coverageTier: string) => {
      if (!plan || !plan.coverageTiers) return { employeeCost: 0, employerCost: 0, totalCost: 0 };

      const tier = plan.coverageTiers.find((t: any) => t.tierName === coverageTier);
      if (tier) {
        return {
          employeeCost: tier.employeeCost || 0,
          employerCost: tier.employerCost || 0,
          totalCost: tier.totalCost || (tier.employeeCost + tier.employerCost) || 0
        };
      }
      return { employeeCost: plan.cost || 0, employerCost: 0, totalCost: plan.cost || 0 };
    };

    // Process dental plan
    if (enrollmentSummary.dentalWaived) {
      waivedCoverages.push('Dental');
    } else if (enrollmentSummary.dentalPlan) {
      const costs = calculatePlanCost(enrollmentSummary.dentalPlan, enrollmentSummary.selectedCoverageTier || 'Employee Only');
      planCosts.push({
        planName: enrollmentSummary.dentalPlan.name || 'Dental Plan',
        planType: 'Dental',
        ...costs
      });
      totalEmployeeCost += costs.employeeCost;
      totalEmployerCost += costs.employerCost;
    }

    // Process vision plan
    if (enrollmentSummary.visionWaived) {
      waivedCoverages.push('Vision');
    } else if (enrollmentSummary.visionPlan) {
      const costs = calculatePlanCost(enrollmentSummary.visionPlan, enrollmentSummary.selectedCoverageTier || 'Employee Only');
      planCosts.push({
        planName: enrollmentSummary.visionPlan.name || 'Vision Plan',
        planType: 'Vision',
        ...costs
      });
      totalEmployeeCost += costs.employeeCost;
      totalEmployerCost += costs.employerCost;
    }

    // Process life insurance plan
    if (enrollmentSummary.lifeWaived) {
      waivedCoverages.push('Life Insurance');
    } else if (enrollmentSummary.lifePlan) {
      const costs = calculatePlanCost(enrollmentSummary.lifePlan, enrollmentSummary.selectedCoverageTier || 'Employee Only');
      planCosts.push({
        planName: enrollmentSummary.lifePlan.name || 'Life Insurance Plan',
        planType: 'Life Insurance',
        ...costs
      });
      totalEmployeeCost += costs.employeeCost;
      totalEmployerCost += costs.employerCost;
    }

    // Process AD&D plan
    if (enrollmentSummary.addWaived) {
      waivedCoverages.push('AD&D');
    } else if (enrollmentSummary.addPlan) {
      const costs = calculatePlanCost(enrollmentSummary.addPlan, enrollmentSummary.selectedCoverageTier || 'Employee Only');
      planCosts.push({
        planName: enrollmentSummary.addPlan.name || 'AD&D Plan',
        planType: 'AD&D',
        ...costs
      });
      totalEmployeeCost += costs.employeeCost;
      totalEmployerCost += costs.employerCost;
    }

    const grandTotal = totalEmployeeCost + totalEmployerCost;

    // Enhanced print with plan details
    const printContent = `
      <html>
        <head>
          <title>Benefits Enrollment Summary</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 40px;
              color: #1f2937;
              line-height: 1.6;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
            }

            .container {
              max-width: 800px;
              margin: 0 auto;
              background: white;
              border-radius: 16px;
              box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
              overflow: hidden;
            }

            .header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              text-align: center;
              padding: 40px 30px;
              position: relative;
            }

            .header h1 {
              font-size: 32px;
              font-weight: 700;
              margin-bottom: 8px;
            }

            .header p {
              font-size: 16px;
              opacity: 0.9;
            }

            .content {
              padding: 40px 30px;
            }

            .coverage-info {
              background: #f8fafc;
              padding: 20px;
              border-radius: 12px;
              margin-bottom: 30px;
              border-left: 4px solid #3b82f6;
            }

            .coverage-info h3 {
              color: #1e40af;
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 8px;
            }

            .plan-section {
              margin: 20px 0;
              padding: 24px;
              border: 1px solid #e5e7eb;
              border-radius: 12px;
              background: white;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .plan-title {
              font-weight: 600;
              font-size: 18px;
              margin-bottom: 16px;
              color: #1f2937;
              display: flex;
              align-items: center;
              gap: 8px;
            }

            .cost-breakdown {
              background: #f9fafb;
              padding: 16px;
              border-radius: 8px;
              border: 1px solid #f3f4f6;
            }

            .cost-breakdown p {
              margin: 8px 0;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            .cost-breakdown p:last-child {
              text-align: right;
            }

            .total-section {
              margin-top: 30px;
              padding: 24px;
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
              color: white;
              border-radius: 12px;
              box-shadow: 0 4px 6px rgba(16, 185, 129, 0.25);
            }

            .total-section h3 {
              font-size: 20px;
              font-weight: 700;
              margin-bottom: 16px;
              text-align: center;
            }

            .total-section p {
              margin: 12px 0;
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 16px;
            }

            .waived {
              background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
              border-color: #fca5a5;
              color: #991b1b;
            }

            .dependents-section {
              margin-top: 30px;
              padding: 24px;
              background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
              border-radius: 12px;
              border: 1px solid #bfdbfe;
            }

            .dependents-section h3 {
              color: #1e40af;
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 16px;
            }

            .next-steps {
              margin-top: 30px;
              padding: 24px;
              background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
              border-radius: 12px;
              border: 1px solid #f59e0b;
            }

            .next-steps .plan-title {
              color: #92400e;
            }

            .next-steps ul {
              list-style: none;
              padding: 0;
            }

            .next-steps li {
              margin: 12px 0;
              padding: 8px 0;
              border-bottom: 1px solid rgba(146, 64, 14, 0.1);
              color: #92400e;
              font-weight: 500;
            }

            .next-steps li:last-child {
              border-bottom: none;
            }

            @media print {
              body {
                background: white !important;
                padding: 20px !important;
              }

              .container {
                box-shadow: none !important;
                border: 1px solid #e5e7eb;
              }

              .header {
                background: #4f46e5 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
              }

              .total-section {
                background: #10b981 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
              }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 Benefits Enrollment Summary</h1>
              <p><strong>Enrollment Date:</strong> ${new Date(enrollmentSummary.enrollmentDate || Date.now()).toLocaleDateString()}</p>
              <p><strong>Coverage Effective:</strong> ${getBenefitsStartDate()}</p>
            </div>

            <div class="content">
              <div class="coverage-info">
                <h3>📋 Coverage Information</h3>
                <p><strong>Coverage Tier:</strong> ${enrollmentSummary.selectedCoverageTier || 'Employee Only'}</p>
              </div>

          ${planCosts.map(plan => `
            <div class="plan-section">
              <div class="plan-title">${plan.planType}: ${plan.planName}</div>
              <div class="cost-breakdown">
                <p><strong>Total Cost:</strong> $${plan.totalCost.toFixed(2)}/paycheck</p>
                <p><strong>Employer Contribution:</strong> $${plan.employerCost.toFixed(2)}</p>
                <p><strong>Your Cost:</strong> $${plan.employeeCost.toFixed(2)}</p>
              </div>
            </div>
          `).join('')}

          ${waivedCoverages.map(coverage => `
            <div class="plan-section waived">
              <div class="plan-title">${coverage}: Waived</div>
              <p>No coverage selected - $0.00/paycheck</p>
            </div>
          `).join('')}

          <div class="total-section">
            <h3>Total Summary</h3>
            <p><strong>Total Plan Cost: $${grandTotal.toFixed(2)}/paycheck</strong></p>
            <p><strong>Employer Contribution: -$${totalEmployerCost.toFixed(2)}</strong></p>
            <p><strong>Your Cost per Paycheck: $${totalEmployeeCost.toFixed(2)}</strong></p>
          </div>

          <div class="dependents-section">
            <h3>${enrollmentSummary.dependents && enrollmentSummary.dependents.length > 0 ? 'Family Members Covered' : 'Coverage For'}</h3>
            <p><strong>${userDetails.name || 'Myself'}</strong> (Employee)</p>
            ${enrollmentSummary.dependents ? enrollmentSummary.dependents.map((dep: any) => `
              <p><strong>${dep.name}</strong> (${dep.relationship})</p>
            `).join('') : ''}
          </div>

              <div class="next-steps">
                <div class="plan-title">💡 Next Steps & Pro Tips</div>
                <ul>
                  <li>🦷 Find dentists in your member portal</li>
                  <li>👓 Use vision benefit for free exam & $150 frames</li>
                  <li>💰 Set up HSA/FSA to save on taxes</li>
                  <li>📱 Download insurance apps for easy access</li>
                  <li>🗓️ Schedule preventive care visits early</li>
                </ul>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const handleEmailSummary = () => {
    const subject = encodeURIComponent('Benefits Enrollment Summary');
    const body = encodeURIComponent('Please find my benefits enrollment summary attached.');
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
  };

  const handleAskQuestions = () => {
    setShowChatModal(true);
  };

  const handleReturnHome = () => {
    // Clean up remaining enrollment data when leaving confirmation page
    console.log('🧹 Final cleanup - removing remaining enrollment data...');
    const finalKeysToRemove = [
      'selectedDentalPlan',
      'selectedVisionPlan',
      'selectedLifePlan',
      'selectedADDPlan',
      'dentalWaived',
      'visionWaived',
      'lifeWaived',
      'addWaived',
      'dentalWaiveReason',
      'visionWaiveReason',
      'lifeWaiveReason',
      'addWaiveReason',
      'enrollmentSummarySnapshot'
    ];

    finalKeysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    console.log('✅ Final cleanup completed');

    // Check if user is broker or admin
    const userRole = userDetails?.role?.toLowerCase();
    const isBroker = userRole === 'broker' || userRole === 'admin';

    if (isBroker) {
      // Brokers and admins go to ai-enroller
      router.push('/ai-enroller');
    } else {
      // Regular employees go to dashboard
      router.push('/dashboard');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: 0 ,
            fontWeight: 'bold' 
          }}>
            🎉 Congratulations—you&apos;re enrolled!
          </p>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: '8px 0 0 0' 
          }}>
            Take a look at the tips and follow-up actions to make the most of your benefits.
          </p>
        </div>
      </div>

      {/* Confirmation Card */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        border: '1px solid #e5e7eb', 
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        

        

        {/* Enrollment Summary */}
        {enrollmentSummary && (
          <div style={{
            backgroundColor: '#f0f9ff',
            borderRadius: '8px',
            padding: '20px',
            marginBottom: '24px',
            border: '1px solid #bfdbfe'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
              <span style={{ fontSize: '16px' }}>📋</span>
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
                Your Enrollment Summary:
              </h3>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              

              {/* Selected Plans */}
              {enrollmentSummary.dentalPlan && !enrollmentSummary.dentalWaived && (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>🦷 Dental Plan:</span>
                  <span style={{ color: '#111827', fontSize: '14px', fontWeight: '600' }}>
                    {enrollmentSummary.dentalPlan.name}
                  </span>
                </div>
              )}

              {enrollmentSummary.visionPlan && !enrollmentSummary.visionWaived && (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>👓 Vision Plan:</span>
                  <span style={{ color: '#111827', fontSize: '14px', fontWeight: '600' }}>
                    {enrollmentSummary.visionPlan.name}
                  </span>
                </div>
              )}

              {enrollmentSummary.lifePlan && !enrollmentSummary.lifeWaived && (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>🛡️ Life Insurance:</span>
                  <span style={{ color: '#111827', fontSize: '14px', fontWeight: '600' }}>
                    {enrollmentSummary.lifePlan.name || enrollmentSummary.lifePlan.planName || 'Life Insurance Plan'}
                  </span>
                </div>
              )}

              {enrollmentSummary.addPlan && !enrollmentSummary.addWaived && (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>🛡️ Accidental Death & Dismemberment (AD&D):</span>
                  <span style={{ color: '#111827', fontSize: '14px', fontWeight: '600' }}>
                    {enrollmentSummary.addPlan.name || enrollmentSummary.addPlan.planName || 'AD&D Plan'}
                  </span>
                </div>
              )}



              {/* Waived Coverages */}
              {enrollmentSummary.dentalWaived && (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '500' }}>🦷 Dental:</span>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '600' }}>Waived</span>
                </div>
              )}

              {enrollmentSummary.visionWaived && (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '500' }}>👓 Vision:</span>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '600' }}>Waived</span>
                </div>
              )}

              {enrollmentSummary.lifeWaived && (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '500' }}>🛡️ Life Insurance:</span>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '600' }}>Waived</span>
                </div>
              )}

              {enrollmentSummary.addWaived && (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '500' }}>🛡️ Accidental Death & Dismemberment (AD&D):</span>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '600' }}>Waived</span>
                </div>
              )}

              {/* Family Members - Always show employee, plus dependents if any */}
              <div style={{ marginTop: '8px', paddingTop: '12px', borderTop: '1px solid #e5e7eb' }}>
                <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500', marginBottom: '8px', display: 'block' }}>
                  👨‍👩‍👧‍👦 {enrollmentSummary.dependents && enrollmentSummary.dependents.length > 0 ? 'Family Members Covered:' : 'Coverage For:'}
                </span>
                {/* Always show the employee */}
                <div style={{ marginLeft: '16px', marginBottom: '4px' }}>
                  <span style={{ color: '#111827', fontSize: '14px', fontWeight: '600' }}>
                    {userDetails.name || 'Myself'} (Employee)
                  </span>
                </div>
                {/* Show dependents if any */}
                {enrollmentSummary.dependents && enrollmentSummary.dependents.map((dependent: any, index: number) => (
                  <div key={index} style={{ marginLeft: '16px', marginBottom: '4px' }}>
                    <span style={{ color: '#111827', fontSize: '14px' }}>
                      {dependent.name} ({dependent.relationship})
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        

        {/* Fallback Display - Show if main summary is empty */}
        {enrollmentSummary &&
         !enrollmentSummary.dentalPlan && !enrollmentSummary.dentalWaived &&
         !enrollmentSummary.visionPlan && !enrollmentSummary.visionWaived &&
         !enrollmentSummary.lifePlan && !enrollmentSummary.lifeWaived &&
         !enrollmentSummary.addPlan && !enrollmentSummary.addWaived && (
          <div style={{
            backgroundColor: '#fef2f2',
            borderRadius: '8px',
            padding: '20px',
            marginBottom: '24px',
            border: '1px solid #fca5a5'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
              <span style={{ fontSize: '16px' }}>⚠️</span>
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#dc2626', margin: 0 }}>
                No Enrollment Data Found
              </h3>
            </div>
            <p style={{ color: '#dc2626', fontSize: '14px', margin: 0 }}>
              It appears no plan selections or waive decisions were recorded. Please contact HR or try enrolling again.
            </p>
          </div>
        )}

        {/* Next Steps */}
        <div style={{
          backgroundColor: '#f8fafc',
          borderRadius: '8px',
          padding: '20px',
          marginBottom: '24px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <span style={{ fontSize: '16px' }}>📋</span>
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>
              Next Steps & Pro Tips:
            </h3>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <Stethoscope style={{ width: '16px', height: '16px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Need a dentist? Find one near you in your member portal
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <Eye style={{ width: '16px', height: '16px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Use your vision benefit for a free exam & $150 frames
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <span style={{ fontSize: '14px', marginTop: '2px' }}>💰</span>
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Set up your HSA/FSA to save on taxes
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <Download style={{ width: '16px', height: '16px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Download your insurance apps for easy access
              </span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
              <Stethoscope style={{ width: '16px', height: '16px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
              <span style={{ color: '#374151', fontSize: '14px' }}>
                Schedule your preventive care visits early in the year
              </span>
            </div>
          </div>
        </div>



        {/* Action Buttons - All in One Line */}
        <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button
            onClick={handleReturnHome}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 20px',
              backgroundColor: '#000000',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer',
              fontWeight: '600',
              fontSize: '14px',
              transition: 'all 0.2s ease',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.backgroundColor = '#374151';
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.backgroundColor = '#000000';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Home size={16} />
            Return to Home
          </button>

          <button
            onClick={handleAskQuestions}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 20px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
          >
            <span style={{ color: '#2563eb' }}>❓</span>
            Ask Questions
          </button>

          <button
            onClick={handleEmailSummary}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 20px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
          >
            <Mail size={16} style={{ color: '#6b7280' }} />
            Email Summary
          </button>

          <button
            onClick={handlePrintSummary}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 20px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
          >
            <Printer size={16} style={{ color: '#6b7280' }} />
            Print Summary
          </button>
        </div>
      </div>

      {/* Modal Components */}
      {showHelp && (
        <FloatingHelp onClose={() => setShowHelp(false)} />
      )}

      {/* Chat Modal */}
      {showChatModal && (
        <ChatModal
          isOpen={showChatModal}
          onClose={() => setShowChatModal(false)}
        />
      )}
    </div>
  );
};

export default ConfirmationPage;
