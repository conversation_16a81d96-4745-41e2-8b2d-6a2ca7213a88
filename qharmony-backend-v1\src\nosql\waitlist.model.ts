import mongoose, { Document, Model } from 'mongoose';
import logger from '../utils/logger';

const { Schema } = mongoose;

export interface WaitlistDataInterface {
  email: string;
  phone: string;
  joinedAt: Date;
  source: string;

}

interface WaitlistDocument extends Document, WaitlistDataInterface {}

class WaitlistModelClass {
  private static waitlistModel: Model<WaitlistDocument>;

  public static initializeModel() {
    const schema = new Schema({
      email: { 
        type: String, 
        required: true,
        lowercase: true 
        // Removed `unique: true` to avoid index duplication
      },
      phone: { 
        type: String, 
        required: true 
      },
      joinedAt: { 
        type: Date, 
        default: Date.now 
      },
      source: { 
        type: String, 
        default: 'benefits_waitlist' 
      }
    }, {
      timestamps: true // Adds createdAt and updatedAt
    });

    // Add unique index only here
    schema.index({ email: 1 }, { unique: true });
    schema.index({ joinedAt: -1 });

    this.waitlistModel = mongoose.model<WaitlistDocument>('Waitlist', schema);
  }

  public static async addToWaitlist(data: WaitlistDataInterface): Promise<{ success: boolean; message: string }> {
    try {
      // Add validation
      if (!data.email) {
        return { 
          success: false, 
          message: 'Email is required' 
        };
      }
      
    //   if (!data.phone) {
    //     return {
    //       success: false,
    //       message: 'Phone is required'
    //     };
    //   }
      
      const existingEntry = await this.waitlistModel.findOne({ 
        email: data.email.toLowerCase() 
      });
      
      if (existingEntry) {
        return { 
          success: false, 
          message: 'Email already exists in waitlist' 
        };
      }
  
      // Ensure email is lowercase
      const waitlistEntry = {
        ...data,
        email: data.email.toLowerCase(),
        joinedAt: data.joinedAt || new Date(),
        source: data.source || 'benefits_waitlist'
      };
  
      await this.waitlistModel.create(waitlistEntry);
      
      return { 
        success: true, 
        message: 'Successfully added to waitlist' 
      };
    } catch (error) {
      logger.error('Error adding to waitlist:', error);
      throw error;
    }
  }

  public static async getWaitlistEntries(): Promise<WaitlistDocument[]> {
    try {
      return await this.waitlistModel.find().sort({ joinedAt: -1 });
    } catch (error) {
      logger.error('Error fetching waitlist entries:', error);
      throw error;
    }
  }
}

WaitlistModelClass.initializeModel();

export default WaitlistModelClass;