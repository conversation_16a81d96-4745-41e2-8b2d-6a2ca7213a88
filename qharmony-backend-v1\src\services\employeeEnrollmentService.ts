import EmployeeEnrollmentModelClass, { EmployeeEnrollmentDataInterface } from '../nosql/preEnrollment/employeeEnrollment.model';
import UserModelClass from '../nosql/user.model';
import PlanAssignmentModelClass from '../nosql/preEnrollment/planAssignment.model';
import CompanyModelClass from '../nosql/company.model';
import CostCalculationService from './costCalculationService';
import logger from '../utils/logger';
import mongoose from 'mongoose';
import { QUALIFYING_LIFE_EVENT_TYPES, ENROLLMENT_STATUSES, ENROLLMENT_PERIOD_TYPES, PLAN_ASSIGNMENT_STATUSES } from '../constants';

/**
 * EmployeeEnrollmentService
 *
 * Service layer for complex enrollment business logic and workflows.
 * Keeps the model focused on core CRUD operations while handling
 * complex business processes here.
 *
 * 🎯 NEW: Enhanced service layer with separation of concerns
 * - Core CRUD operations
 * - Query and filtering operations
 * - Status management operations
 * - Business logic operations
 * - Bulk operations
 */
class EmployeeEnrollmentService {

  // ===== CORE CRUD OPERATIONS =====

  /**
   * Create enrollment with validation and access control
   */
  static async createEnrollment(enrollmentData: any, userId: string, user: any) {
    try {
      // Validate user access
      if (!user.isBroker && !UserModelClass.isSuperAdmin(user)) {
        // For employees, validate they can only enroll themselves
        if (enrollmentData.employeeId !== userId) {
          return { success: false, error: 'Employees can only create enrollments for themselves', enrollment: null };
        }
      }

      // Use existing comprehensive service method
      const result = await this.createEnrollmentWithCostCalculation({
        employeeId: enrollmentData.employeeId,
        planAssignmentId: enrollmentData.planAssignmentId,
        companyId: enrollmentData.companyId,
        employeeClassType: enrollmentData.employeeClassType,
        coverageTier: enrollmentData.coverageTier,
        dependentIds: enrollmentData.dependentIds || [],
        effectiveDate: enrollmentData.effectiveDate,
        enrollmentType: enrollmentData.enrollmentType || 'Open Enrollment',
        qualifyingLifeEvent: enrollmentData.qualifyingLifeEvent,
        newHireDate: enrollmentData.newHireDate,
        isSuperAdmin: UserModelClass.isSuperAdmin(user)
      });

      return result;
    } catch (error) {
      logger.error('Error in createEnrollment service:', error);
      return { success: false, error: 'Internal server error during enrollment creation', enrollment: null };
    }
  }

  /**
   * Get enrollment by ID with access control
   */
  static async getEnrollmentById(enrollmentId: string, userId: string, user: any) {
    try {
      const enrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!enrollment) {
        return { success: false, error: 'Enrollment not found', enrollment: null };
      }

      // Access control validation
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, enrollment.employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied', enrollment: null };
      }

      return { success: true, error: null, enrollment };
    } catch (error) {
      logger.error('Error in getEnrollmentById service:', error);
      return { success: false, error: 'Internal server error', enrollment: null };
    }
  }

  /**
   * Update enrollment with validation and access control
   */
  static async updateEnrollment(enrollmentId: string, updateData: any, userId: string, user: any) {
    try {
      // Get existing enrollment
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return { success: false, error: 'Enrollment not found', enrollment: null };
      }

      // Access control validation
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, existingEnrollment.employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied', enrollment: null };
      }

      // Validate dependent count if updating coverage tier or dependents
      if (updateData.coverageTier || updateData.dependentIds) {
        const coverageTier = updateData.coverageTier || existingEnrollment.coverageTier;
        const dependentIds = updateData.dependentIds || existingEnrollment.enrolledDependents?.map(d => d.dependentId) || [];

        const dependentValidation = EmployeeEnrollmentModelClass.validateDependentCount(coverageTier, dependentIds.length);
        if (!dependentValidation.isValid) {
          return { success: false, error: dependentValidation.error, enrollment: null };
        }
      }

      // Update enrollment
      const result = await EmployeeEnrollmentModelClass.updateData({
        id: enrollmentId,
        data: updateData
      });

      if (result.modifiedCount === 0) {
        return { success: false, error: 'Failed to update enrollment', enrollment: null };
      }

      // Get updated enrollment
      const updatedEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      return { success: true, error: null, enrollment: updatedEnrollment };
    } catch (error) {
      logger.error('Error in updateEnrollment service:', error);
      return { success: false, error: 'Internal server error during enrollment update', enrollment: null };
    }
  }

  /**
   * Delete enrollment with access control
   */
  static async deleteEnrollment(enrollmentId: string, userId: string, user: any) {
    try {
      // Get existing enrollment
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return { success: false, error: 'Enrollment not found' };
      }

      // Access control validation
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, existingEnrollment.employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied' };
      }

      // Delete enrollment
      await EmployeeEnrollmentModelClass.deleteData(enrollmentId);
      return { success: true, error: null, message: 'Enrollment deleted successfully' };
    } catch (error) {
      logger.error('Error in deleteEnrollment service:', error);
      return { success: false, error: 'Internal server error during enrollment deletion' };
    }
  }

  // ===== QUERY & FILTERING OPERATIONS =====

  /**
   * Get enrollments with optimized pagination and filtering
   */
  static async getEnrollmentsOptimized(filters: any = {}, pagination: any = {}, userId: string, user: any) {
    try {
      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      let query: any = {};

      // Apply role-based filtering
      if (UserModelClass.isSuperAdmin(user)) {
        // SuperAdmins can view all enrollments
        if (filters.companyId) query.companyId = filters.companyId;
        if (filters.employeeId) query.employeeId = filters.employeeId;
      } else if (user.isBroker) {
        // Brokers can view enrollments for their plan assignments only
        // This requires aggregation with plan assignments
        return await this.getEnrollmentsWithBrokerFilter(userId, filters, pagination);
      } else {
        // Employees can only view their own enrollments
        query.employeeId = userId;
      }

      // Apply status filters
      if (filters.status) {
        query.status = filters.status;
      } else {
        // Default: exclude expired enrollments
        query.status = { $ne: ENROLLMENT_STATUSES[4] }; // 'Expired'
      }

      // Apply date filters
      if (filters.effectiveDate) {
        query.effectiveDate = { $lte: new Date(filters.effectiveDate) };
      }

      // Get enrollments with pagination - use model methods instead of direct access
      const allEnrollments = await EmployeeEnrollmentModelClass.getAllData();

      // Apply query filters
      let filteredEnrollments = allEnrollments.filter(enrollment => {
        // Apply role-based filtering
        if (query.employeeId && enrollment.employeeId !== query.employeeId) return false;
        if (query.companyId && enrollment.companyId !== query.companyId) return false;
        if (query.status && query.status.$ne && enrollment.status === query.status.$ne) return false;
        if (query.status && typeof query.status === 'string' && enrollment.status !== query.status) return false;
        return true;
      });

      // Apply pagination
      const total = filteredEnrollments.length;
      const enrollments = filteredEnrollments
        .sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime())
        .slice(skip, skip + limit);

      return {
        success: true,
        error: null,
        enrollments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error in getEnrollmentsOptimized service:', error);
      return { success: false, error: 'Internal server error', enrollments: [] };
    }
  }

  /**
   * Get enrollments by employee with status filtering
   */
  static async getEnrollmentsByEmployee(employeeId: string, filters: any = {}, userId: string, user: any) {
    try {
      // Access control validation
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied', enrollments: [] };
      }

      // Build status filter
      let allowedStatuses: string[] = [ENROLLMENT_STATUSES[2], ENROLLMENT_STATUSES[0]]; // ['Pending', 'Enrolled']

      if (filters.includeWaived !== false) allowedStatuses.push(ENROLLMENT_STATUSES[1]); // 'Waived'
      if (filters.includeTerminated !== false) allowedStatuses.push(ENROLLMENT_STATUSES[3]); // 'Terminated'
      if (filters.includeExpired === true) allowedStatuses.push(ENROLLMENT_STATUSES[4]); // 'Expired'

      // Get enrollments with status filter
      const enrollments = await EmployeeEnrollmentModelClass.getDataByEmployeeIdWithStatusFilter(employeeId, allowedStatuses);

      // Apply additional filters
      let filteredEnrollments = enrollments;
      if (filters.planAssignmentId) {
        filteredEnrollments = enrollments.filter(e => e.planAssignmentId === filters.planAssignmentId);
      }

      return { success: true, error: null, enrollments: filteredEnrollments };
    } catch (error) {
      logger.error('Error in getEnrollmentsByEmployee service:', error);
      return { success: false, error: 'Internal server error', enrollments: [] };
    }
  }

  /**
   * Get enrollments by company with access control
   */
  static async getEnrollmentsByCompany(companyId: string, filters: any = {}, userId: string, user: any) {
    try {
      // Access control: Only SuperAdmins and Brokers can view company enrollments
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        return { success: false, error: 'Access denied. Only admins and brokers can view company enrollments.', enrollments: [] };
      }

      // For brokers, validate they have access to this company
      if (user.isBroker) {
        // Check if broker has plan assignments for this company
        const brokerAssignments = await PlanAssignmentModelClass.getBrokerAssignmentsForCompany(userId, companyId);
        if (brokerAssignments.length === 0) {
          return { success: false, error: 'Access denied. No plan assignments found for this company.', enrollments: [] };
        }
      }

      // Get enrollments by company
      const enrollments = await EmployeeEnrollmentModelClass.getDataByCompanyId(companyId);

      // Apply status filters
      let filteredEnrollments = enrollments;
      if (filters.status) {
        filteredEnrollments = enrollments.filter(e => e.status === filters.status);
      }

      return { success: true, error: null, enrollments: filteredEnrollments };
    } catch (error) {
      logger.error('Error in getEnrollmentsByCompany service:', error);
      return { success: false, error: 'Internal server error', enrollments: [] };
    }
  }

  /**
   * Get enrollments by plan assignment with access control
   */
  static async getEnrollmentsByPlanAssignment(planAssignmentId: string, filters: any = {}, userId: string, user: any) {
    try {
      // Get plan assignment for access control
      const planAssignment = await PlanAssignmentModelClass.getDataById(planAssignmentId);
      if (!planAssignment) {
        return { success: false, error: 'Plan assignment not found', enrollments: [] };
      }

      // Access control validation
      if (!UserModelClass.isSuperAdmin(user) && !user.isBroker) {
        return { success: false, error: 'Access denied. Only admins and brokers can view plan assignment enrollments.', enrollments: [] };
      }

      // For brokers, validate they own this plan assignment
      if (user.isBroker) {
        const brokerAssignments = await PlanAssignmentModelClass.getBrokerAssignmentsForCompany(userId, planAssignment.companyId);
        const hasAccess = brokerAssignments.some(assignment => assignment._id.toString() === planAssignmentId);
        if (!hasAccess) {
          return { success: false, error: 'Access denied. Plan assignment not owned by broker.', enrollments: [] };
        }
      }

      // Get enrollments by plan assignment
      const enrollments = await EmployeeEnrollmentModelClass.getDataByPlanAssignmentId(planAssignmentId);

      return { success: true, error: null, enrollments };
    } catch (error) {
      logger.error('Error in getEnrollmentsByPlanAssignment service:', error);
      return { success: false, error: 'Internal server error', enrollments: [] };
    }
  }

  /**
   * Get enrollments with broker filter (aggregation-based)
   */
  static async getEnrollmentsWithBrokerFilter(brokerId: string, filters: any = {}, pagination: any = {}) {
    try {
      // This would require aggregation with plan assignments to filter by broker
      // For now, return basic implementation
      const enrollments = await EmployeeEnrollmentModelClass.getAllData();

      return {
        success: true,
        error: null,
        enrollments,
        pagination: pagination
      };
    } catch (error) {
      logger.error('Error in getEnrollmentsWithBrokerFilter service:', error);
      return { success: false, error: 'Internal server error', enrollments: [] };
    }
  }

  // ===== STATUS MANAGEMENT OPERATIONS =====

  /**
   * Terminate enrollment with validation and access control
   */
  static async terminateEnrollment(enrollmentId: string, terminationData: any, userId: string, user: any) {
    try {
      // Get existing enrollment
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return { success: false, error: 'Enrollment not found', enrollment: null };
      }

      // Access control validation
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, existingEnrollment.employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied', enrollment: null };
      }

      // Use model method for termination
      const result = await EmployeeEnrollmentModelClass.terminateEnrollment({
        enrollmentId,
        terminationDate: terminationData.terminationDate || new Date(),
        terminationReason: terminationData.terminationReason || 'Manual termination',
        userId: userId
      });

      if (!result.success) {
        return { success: false, error: result.message || 'Termination failed', enrollment: null };
      }

      return { success: true, error: null, enrollment: result.enrollment, message: 'Enrollment terminated successfully' };
    } catch (error) {
      logger.error('Error in terminateEnrollment service:', error);
      return { success: false, error: 'Internal server error during enrollment termination', enrollment: null };
    }
  }

  /**
   * Waive enrollment with validation and access control
   */
  static async waiveEnrollment(enrollmentId: string, waiveData: any, userId: string, user: any) {
    try {
      // Get existing enrollment
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return { success: false, error: 'Enrollment not found', enrollment: null };
      }

      // Access control validation
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, existingEnrollment.employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied', enrollment: null };
      }

      // Use model method for waiving
      const result = await EmployeeEnrollmentModelClass.waiveEnrollment({
        enrollmentId,
        waiveDate: waiveData.waiveDate || new Date(),
        waiveReason: waiveData.waiveReason || 'Employee declined coverage',
        userId: userId
      });

      if (!result.success) {
        return { success: false, error: result.message || 'Waiving failed', enrollment: null };
      }

      return { success: true, error: null, enrollment: result.enrollment, message: 'Enrollment waived successfully' };
    } catch (error) {
      logger.error('Error in waiveEnrollment service:', error);
      return { success: false, error: 'Internal server error during enrollment waiving', enrollment: null };
    }
  }

  /**
   * Reinstate enrollment with validation and access control
   */
  static async reinstateEnrollment(enrollmentId: string, reinstateData: any, userId: string, user: any) {
    try {
      // Get existing enrollment
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return { success: false, error: 'Enrollment not found', enrollment: null };
      }

      // Access control validation
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, existingEnrollment.employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied', enrollment: null };
      }

      // Use model method for reinstatement
      const result = await EmployeeEnrollmentModelClass.reinstateEnrollment({
        enrollmentId,
        newStatus: reinstateData.newStatus || 'Pending',
        reinstateReason: reinstateData.reinstateReason || 'Manual reinstatement',
        userId: userId,
        newEffectiveDate: reinstateData.newEffectiveDate
      });

      if (!result.success) {
        return { success: false, error: result.message || 'Reinstatement failed', enrollment: null };
      }

      return { success: true, error: null, enrollment: result.enrollment, message: 'Enrollment reinstated successfully' };
    } catch (error) {
      logger.error('Error in reinstateEnrollment service:', error);
      return { success: false, error: 'Internal server error during enrollment reinstatement', enrollment: null };
    }
  }

  /**
   * Activate enrollment with validation and access control
   */
  static async activateEnrollment(enrollmentId: string, activateData: any, userId: string, user: any) {
    try {
      // Get existing enrollment
      const existingEnrollment = await EmployeeEnrollmentModelClass.getDataById(enrollmentId);
      if (!existingEnrollment) {
        return { success: false, error: 'Enrollment not found', enrollment: null };
      }

      // Access control validation
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, existingEnrollment.employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied', enrollment: null };
      }

      // Use model method for activation
      const result = await EmployeeEnrollmentModelClass.activateEnrollment({
        enrollmentId,
        userId: userId,
        activationDate: activateData.activationDate || new Date()
      });

      if (!result.success) {
        return { success: false, error: result.message || 'Activation failed', enrollment: null };
      }

      return { success: true, error: null, enrollment: result.enrollment, message: 'Enrollment activated successfully' };
    } catch (error) {
      logger.error('Error in activateEnrollment service:', error);
      return { success: false, error: 'Internal server error during enrollment activation', enrollment: null };
    }
  }

  // ===== BUSINESS LOGIC OPERATIONS =====

  /**
   * Check enrollment eligibility with comprehensive validation
   */
  static async checkEligibility(eligibilityData: any, userId: string, user: any) {
    try {
      // Use existing comprehensive eligibility validation
      const result = await this.validateComprehensiveEligibility({
        employeeId: eligibilityData.employeeId,
        planAssignmentId: eligibilityData.planAssignmentId,
        operation: 'enrollment',
        userId,
        enrollmentType: eligibilityData.enrollmentType || 'Open Enrollment',
        qualifyingLifeEvent: eligibilityData.qualifyingLifeEvent,
        newHireDate: eligibilityData.newHireDate
      });

      return result;
    } catch (error) {
      logger.error('Error in checkEligibility service:', error);
      return { success: false, error: 'Internal server error during eligibility check' };
    }
  }

  /**
   * Calculate enrollment cost with validation
   */
  static async calculateCost(costData: any, userId: string, user: any) {
    try {
      // Use existing cost calculation method
      const result = await EmployeeEnrollmentModelClass.calculateEnrollmentCost({
        userId: costData.employeeId,
        employeeId: costData.employeeId,
        planAssignmentId: costData.planAssignmentId,
        coverageTier: costData.coverageTier,
        dependentIds: costData.dependentIds || []
      });

      return result;
    } catch (error) {
      logger.error('Error in calculateCost service:', error);
      return { success: false, error: 'Internal server error during cost calculation' };
    }
  }

  /**
   * Validate enrollment period with comprehensive checks
   */
  static async validateEnrollmentPeriodService(periodData: any, userId: string, user: any) {
    try {
      // Get plan assignment for validation
      const planAssignment = await PlanAssignmentModelClass.getDataById(periodData.planAssignmentId);
      if (!planAssignment) {
        return { success: false, error: 'Plan assignment not found' };
      }

      // Use existing enrollment period validation
      const result = await this.validateEnrollmentPeriod({
        enrollmentType: periodData.enrollmentType || 'Open Enrollment',
        planAssignment: planAssignment,
        qualifyingLifeEvent: periodData.qualifyingLifeEvent,
        newHireDate: periodData.newHireDate,
        isSuperAdmin: UserModelClass.isSuperAdmin(user)
      });

      return result;
    } catch (error) {
      logger.error('Error in validateEnrollmentPeriodService:', error);
      return { success: false, error: 'Internal server error during enrollment period validation' };
    }
  }

  // ===== BULK OPERATIONS =====

  /**
   * Bulk enrollment with validation and rollback
   */
  static async bulkEnrollment(bulkData: any, userId: string, user: any) {
    try {
      // Use existing bulk enrollment method
      const result = await this.enrollEmployeeInMultiplePlans({
        employeeId: bulkData.employeeId,
        companyId: bulkData.companyId,
        employeeClassType: bulkData.employeeClassType || 'Full-Time',
        planSelections: bulkData.planSelections || [],
        effectiveDate: bulkData.effectiveDate,
        isSuperAdmin: UserModelClass.isSuperAdmin(user)
      });

      return result;
    } catch (error) {
      logger.error('Error in bulkEnrollment service:', error);
      return { success: false, error: 'Internal server error during bulk enrollment' };
    }
  }

  /**
   * Bulk waive enrollments with validation
   */
  static async bulkWaiveEnrollments(bulkWaiveData: any, userId: string, user: any) {
    try {
      const { employeeId, planAssignmentIds, waiveReason, waiveDate } = bulkWaiveData;

      // Validate access to employee
      const accessValidation = await EmployeeEnrollmentModelClass.validateEmployeeAccess(userId, employeeId, user);
      if (!accessValidation.hasAccess) {
        return { success: false, error: accessValidation.reason || 'Access denied', results: [] };
      }

      const results = [];
      const errors = [];

      // Process each plan assignment
      for (const planAssignmentId of planAssignmentIds) {
        try {
          // Find existing enrollment
          const enrollment = await EmployeeEnrollmentModelClass.getDataByEmployeeAndPlan(employeeId, planAssignmentId);

          if (enrollment) {
            // Waive existing enrollment
            const waiveResult = await EmployeeEnrollmentModelClass.waiveEnrollment({
              enrollmentId: enrollment._id.toString(),
              waiveDate: waiveDate || new Date(),
              waiveReason: waiveReason || 'Bulk waive operation',
              userId: userId
            });

            if (waiveResult.success) {
              results.push({ planAssignmentId, status: 'waived', enrollmentId: enrollment._id });
            } else {
              errors.push({ planAssignmentId, error: waiveResult.message || 'Waive failed' });
            }
          } else {
            // Create waived enrollment entry with all required fields
            const waiveEnrollmentData = {
              employeeId,
              planAssignmentId,
              companyId: bulkWaiveData.companyId,
              status: ENROLLMENT_STATUSES[1], // 'Waived'
              waiveDate: waiveDate || new Date(),
              waiveReason: waiveReason || 'Bulk waive operation',
              enrollmentType: ENROLLMENT_PERIOD_TYPES[0], // 'Open Enrollment'
              coverageTier: 'Employee Only', // Default for waived
              effectiveDate: new Date(),
              enrolledDependents: [],
              // Required fields for interface compliance
              coverageType: 'Health', // Default
              coverageSubTypes: [],
              employeeClassType: 'Full-Time', // Default
              contribution: {
                employeeAmount: 0,
                employerAmount: 0,
                totalAmount: 0
              },
              enrollmentPeriod: {
                type: 'Open Enrollment',
                startDate: new Date(),
                endDate: new Date()
              },
              enrolledUnder: 'Employee',
              enrollmentDate: new Date(),
              planYear: new Date().getFullYear(),
              planEndDate: new Date(new Date().getFullYear(), 11, 31) // End of current year
            };

            const newEnrollment = await EmployeeEnrollmentModelClass.addData(waiveEnrollmentData);
            if (newEnrollment) {
              results.push({ planAssignmentId, status: 'waived', enrollmentId: newEnrollment._id });
            } else {
              errors.push({ planAssignmentId, error: 'Failed to create waived enrollment' });
            }
          }
        } catch (error) {
          errors.push({ planAssignmentId, error: error.message });
        }
      }

      return {
        success: errors.length === 0,
        error: errors.length > 0 ? `${errors.length} operations failed` : null,
        results,
        errors,
        summary: {
          total: planAssignmentIds.length,
          successful: results.length,
          failed: errors.length
        }
      };
    } catch (error) {
      logger.error('Error in bulkWaiveEnrollments service:', error);
      return { success: false, error: 'Internal server error during bulk waive operation', results: [] };
    }
  }

  /**
   * Create enrollment with cost calculation and validation
   */
  public static async createEnrollmentWithCostCalculation({
    employeeId,
    planAssignmentId,
    companyId,
    employeeClassType,
    coverageTier,
    dependentIds = [],
    effectiveDate,
    enrollmentType = 'Open Enrollment',
    qualifyingLifeEvent,
    newHireDate,
    isSuperAdmin = false
  }: {
    employeeId: string;
    planAssignmentId: string;
    companyId: string;
    employeeClassType: string;
    coverageTier: string;
    dependentIds: string[];
    effectiveDate: Date;
    enrollmentType?: string;
    qualifyingLifeEvent?: {
      eventType: string;
      eventDate: Date;
      documentationUrl?: string;
      allowedEnrollmentWindow?: {
        start: Date;
        end: Date;
      };
      processedBy?: string;
      processedAt?: Date;
    };
    newHireDate?: string;
    isSuperAdmin?: boolean;
  }): Promise<{
    success: boolean;
    message: string;
    enrollmentId?: string;
    calculatedCost?: any;
    missingFields?: string[];
    errors?: string[];
    reason?: string;
    eligibilityDate?: Date;
    daysUntilEligible?: number;
    employeeClass?: string;
    eligibleClasses?: string[];
  }> {
    try {
      // Get plan assignment
      const planAssignment = await CostCalculationService.getPlanAssignmentById(planAssignmentId);
      if (!planAssignment) {
        return { success: false, message: 'Plan assignment not found' };
      }

      // Validate plan assignment status
      if (planAssignment.status !== PLAN_ASSIGNMENT_STATUSES[0]) { // 'Active'
        return {
          success: false,
          message: `Plan assignment is ${planAssignment.status}. Only Active assignments allow enrollment.`
        };
      }

      // Get the plan to extract coverage details
      const planModel = mongoose.model('Plan');
      const plan = await planModel.findById(planAssignment.planId);
      if (!plan) {
        return { success: false, message: 'Plan not found' };
      }

      // Get employee data
      const employee = await UserModelClass.getDataById(employeeId);
      if (!employee) {
        return { success: false, message: 'Employee not found' };
      }

      // Validate employee profile completeness
      const profileValidation = await EmployeeEnrollmentModelClass.validateEmployeeProfileForEnrollment(employee, planAssignment);
      if (!profileValidation.isValid) {
        return {
          success: false,
          message: 'Employee profile incomplete for enrollment',
          missingFields: profileValidation.missingFields,
          errors: profileValidation.errors
        };
      }

      // Validate hire date eligibility
      const hireDateEligibility = EmployeeEnrollmentModelClass.isEmployeeEligibleByHireDate(employee, planAssignment);
      if (!hireDateEligibility.isEligible) {
        return {
          success: false,
          message: 'Employee not yet eligible for enrollment',
          reason: hireDateEligibility.reason,
          eligibilityDate: hireDateEligibility.eligibilityDate,
          daysUntilEligible: hireDateEligibility.daysUntilEligible
        };
      }

      // Validate employee class type eligibility
      const classEligibility = EmployeeEnrollmentModelClass.isEmployeeClassEligible(employee, planAssignment);
      if (!classEligibility.isEligible) {
        return {
          success: false,
          message: 'Employee class type not eligible for this plan',
          reason: classEligibility.reason,
          employeeClass: classEligibility.employeeClass,
          eligibleClasses: classEligibility.eligibleClasses
        };
      }

      // 🎯 NEW: Validate enrollment period based on enrollment type
      const periodValidation = await this.validateEnrollmentPeriod({
        enrollmentType,
        planAssignment,
        qualifyingLifeEvent,
        newHireDate,
        isSuperAdmin
      });

      if (!periodValidation.isAllowed) {
        return {
          success: false,
          message: 'Enrollment period validation failed',
          reason: periodValidation.reason
        };
      }

      // 🎯 NEW: Validate dependent count against coverage tier
      const dependentValidation = EmployeeEnrollmentModelClass.validateDependentCount(
        coverageTier,
        dependentIds.length
      );
      if (!dependentValidation.isValid) {
        const req = dependentValidation.requirement!;
        return {
          success: false,
          message: `${coverageTier} requires ${req.min === req.max ? req.min : `${req.min}-${req.max}`} dependents, but ${dependentIds.length} provided`
        };
      }

      // 🎯 NEW: Validate dependent IDs belong to employee (if any provided)
      if (dependentIds.length > 0) {
        const dependentIdValidation = await EmployeeEnrollmentModelClass.validateDependentIds(
          employeeId,
          dependentIds
        );
        if (!dependentIdValidation.isValid) {
          return {
            success: false,
            message: `Invalid dependent IDs: ${dependentIdValidation.errors.join(', ')}`
          };
        }
      }

      // Calculate employee age
      let employeeAge: number | undefined;
      if (employee.details?.dateOfBirth) {
        const birthDate = new Date(employee.details.dateOfBirth);
        const today = new Date();
        employeeAge = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          employeeAge--;
        }
      }

      // Extract employee salary for cost calculation
      const employeeSalary = employee.details?.annualSalary;

      // Validate salary requirement for salary-based rate structures
      if (planAssignment.rateStructure === 'Salary-Based' && (!employeeSalary || employeeSalary <= 0)) {
        return {
          success: false,
          message: 'Employee annual salary is required for salary-based plan cost calculations'
        };
      }

      // Calculate enrollment cost with all required parameters
      const costResult = CostCalculationService.calculateEnrollmentCost({
        planAssignment,
        employeeAge,
        selectedTier: coverageTier,
        employeeSalary,
        payrollFrequency: 'Monthly' // Default payroll frequency
      });

      if (!costResult.success) {
        console.error('Cost calculation failed:', {
          planAssignmentId,
          coverageTier,
          employeeAge,
          employeeSalary,
          rateStructure: planAssignment.rateStructure,
          costError: costResult.error,
          planAssignment: {
            id: planAssignment._id,
            coverageTiers: planAssignment.coverageTiers?.length || 0,
            rateStructure: planAssignment.rateStructure
          }
        });
        return {
          success: false,
          message: `Failed to calculate enrollment cost: ${costResult.error || 'Unknown cost calculation error'}`
        };
      }

      // Create enrolled dependents
      const enrolledDependents = await EmployeeEnrollmentModelClass.createEnrolledDependentsFromIds(
        employeeId,
        dependentIds,
        effectiveDate
      );

      // Create enrollment data
      const enrollmentData: EmployeeEnrollmentDataInterface = {
        planAssignmentId,
        employeeId,
        companyId,
        coverageType: plan.coverageType,
        coverageSubTypes: plan.coverageSubTypes,
        employeeClassType,
        coverageTier,
        contribution: costResult.cost!,
        enrolledDependents,
        status: ENROLLMENT_STATUSES[0], // 'Enrolled' - 🎯 UPDATED: Auto-enroll since all validations passed in controller
        enrolledUnder: enrollmentType,
        enrollmentDate: new Date(),
        effectiveDate,
        // Add plan reference fields for expiry management
        planYear: planAssignment.planYear,
        planEndDate: planAssignment.planEndDate,
        ...(qualifyingLifeEvent && {
          qualifyingLifeEvent: {
            ...qualifyingLifeEvent,
            allowedEnrollmentWindow: qualifyingLifeEvent.allowedEnrollmentWindow || {
              start: new Date(qualifyingLifeEvent.eventDate),
              end: new Date(new Date(qualifyingLifeEvent.eventDate).getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days
            }
          }
        }),
      };

      // Create the enrollment
      const enrollment = await EmployeeEnrollmentModelClass.addData(enrollmentData);
      if (!enrollment) {
        return { success: false, message: 'Failed to create enrollment' };
      }

      return {
        success: true,
        message: 'Enrollment created and activated successfully with calculated costs', // 🎯 UPDATED: Reflects auto-enrollment
        enrollmentId: enrollment._id!.toString(),
        calculatedCost: costResult.cost
      };

    } catch (error) {
      console.error('Error creating enrollment with cost calculation:', error);
      return { success: false, message: 'Internal error creating enrollment' };
    }
  }

  /**
   * Validate enrollment period based on enrollment type
   */
  public static async validateEnrollmentPeriod({
    enrollmentType,
    planAssignment,
    qualifyingLifeEvent,
    newHireDate,
    isSuperAdmin = false
  }: {
    enrollmentType: string;
    planAssignment: any;
    qualifyingLifeEvent?: any;
    newHireDate?: string;
    isSuperAdmin?: boolean;
  }): Promise<{
    isAllowed: boolean;
    reason?: string;
    validationDetails?: any;
  }> {
    try {
      if (enrollmentType === 'Open Enrollment') {
        const periodCheck = await EmployeeEnrollmentModelClass.isWithinEnrollmentPeriod(planAssignment._id);
        if (!periodCheck.isWithin && !isSuperAdmin) {
          return { isAllowed: false, reason: periodCheck.reason };
        }
        return { isAllowed: true };

      } else if (enrollmentType === 'New Hire') {
        if (!newHireDate) {
          return { isAllowed: false, reason: 'New hire date required for New Hire enrollment' };
        }

        const hireDateObj = new Date(newHireDate);
        const now = new Date();
        const daysSinceHire = Math.floor((now.getTime() - hireDateObj.getTime()) / (1000 * 60 * 60 * 24));
        const newHireWindowDays = planAssignment.waitingPeriod?.days || 30;

        if (daysSinceHire <= newHireWindowDays || isSuperAdmin) {
          return { isAllowed: true };
        } else {
          return {
            isAllowed: false,
            reason: `New hire enrollment window expired. Hired ${daysSinceHire} days ago (max ${newHireWindowDays} days)`
          };
        }

      } else if (enrollmentType === 'Qualifying Life Event') {
        if (!qualifyingLifeEvent?.eventType || !qualifyingLifeEvent?.eventDate) {
          return { isAllowed: false, reason: 'QLE event type and date required for Qualifying Life Event enrollment' };
        }

        // Validate QLE event type is allowed for this plan
        const qleValidation = EmployeeEnrollmentModelClass.validateQLEEventType(qualifyingLifeEvent.eventType, planAssignment);
        if (!qleValidation.isValid) {
          return {
            isAllowed: false,
            reason: qleValidation.error,
            validationDetails: { allowedEvents: qleValidation.allowedEvents }
          };
        }

        // Validate QLE window timing
        const qleWindowDays = planAssignment.qualifyingLifeEventWindow?.windowDays || 30;
        const qleDate = new Date(qualifyingLifeEvent.eventDate);
        const now = new Date();
        const daysSinceQLE = Math.floor((now.getTime() - qleDate.getTime()) / (1000 * 60 * 60 * 24));

        if (daysSinceQLE <= qleWindowDays || isSuperAdmin) {
          return { isAllowed: true };
        } else {
          return {
            isAllowed: false,
            reason: `Qualifying Life Event window expired. Event occurred ${daysSinceQLE} days ago (max ${qleWindowDays} days)`
          };
        }
      }

      return { isAllowed: true };

    } catch (error) {
      console.error('Error validating enrollment period:', error);
      return { isAllowed: false, reason: 'Error validating enrollment period' };
    }
  }

  /**
   * Simple enrollment period check for existing enrollments (update/delete/terminate/waive operations)
   */
  public static async validateEnrollmentPeriodForExistingEnrollment({
    planAssignmentId,
    isSuperAdmin = false,
    operation = 'modify'
  }: {
    planAssignmentId: string;
    isSuperAdmin?: boolean;
    operation?: string;
  }): Promise<{
    isAllowed: boolean;
    reason?: string;
  }> {
    try {
      if (isSuperAdmin) {
        return { isAllowed: true };
      }

      const periodCheck = await EmployeeEnrollmentModelClass.isWithinEnrollmentPeriod(planAssignmentId);
      if (!periodCheck.isWithin) {
        return {
          isAllowed: false,
          reason: `Cannot ${operation} enrollment outside of enrollment period: ${periodCheck.reason}`
        };
      }

      return { isAllowed: true };

    } catch (error) {
      console.error('Error validating enrollment period for existing enrollment:', error);
      return { isAllowed: false, reason: 'Error validating enrollment period' };
    }
  }

  /**
   * Validate comprehensive eligibility for enrollment operations
   */
  public static async validateComprehensiveEligibility({
    employeeId,
    planAssignmentId,
    operation = 'enrollment',
    userId,
    enrollmentType = 'Open Enrollment',
    qualifyingLifeEvent,
    newHireDate
  }: {
    employeeId: string;
    planAssignmentId: string;
    operation?: 'enrollment' | 'reinstatement' | 'activation';
    userId: string;
    enrollmentType?: string;
    qualifyingLifeEvent?: any;
    newHireDate?: string;
  }): Promise<{
    isEligible: boolean;
    checks: Record<string, boolean>;
    reasons: string[];
    warnings: string[];
  }> {
    const checks: Record<string, boolean> = {};
    const reasons: string[] = [];
    const warnings: string[] = [];

    try {
      // Get employee and plan assignment
      const employee = await UserModelClass.getDataById(employeeId);
      const planAssignment = await PlanAssignmentModelClass.getDataById(planAssignmentId);

      if (!employee || !planAssignment) {
        return {
          isEligible: false,
          checks: { employeeExists: !!employee, planExists: !!planAssignment },
          reasons: [!employee ? 'Employee not found' : 'Plan assignment not found'],
          warnings: []
        };
      }

      // Profile completeness check
      const profileValidation = await EmployeeEnrollmentModelClass.validateEmployeeProfileForEnrollment(employee, planAssignment);
      checks.profileComplete = profileValidation.isValid;
      if (!profileValidation.isValid) {
        reasons.push('Employee profile incomplete');
        warnings.push(...profileValidation.warnings);
      }

      // Hire date eligibility check
      const hireDateEligibility = EmployeeEnrollmentModelClass.isEmployeeEligibleByHireDate(employee, planAssignment);
      checks.hireDateEligible = hireDateEligibility.isEligible;
      if (!hireDateEligibility.isEligible) {
        reasons.push(hireDateEligibility.reason);
      }

      // Employee class eligibility check
      const classEligibility = EmployeeEnrollmentModelClass.isEmployeeClassEligible(employee, planAssignment);
      checks.classEligible = classEligibility.isEligible;
      if (!classEligibility.isEligible) {
        reasons.push(classEligibility.reason);
      }

      // Plan assignment status check
      checks.planActive = planAssignment.status === PLAN_ASSIGNMENT_STATUSES[0]; // 'Active'
      if (planAssignment.status !== PLAN_ASSIGNMENT_STATUSES[0]) { // 'Active'
        reasons.push(`Plan assignment is ${planAssignment.status}`);
      }

      // Company match check
      checks.companyMatch = employee.companyId === planAssignment.companyId;
      if (employee.companyId !== planAssignment.companyId) {
        reasons.push('Employee does not belong to the company assigned to this plan');
      }

      const isEligible = Object.values(checks).every(check => check);

      return {
        isEligible,
        checks,
        reasons,
        warnings
      };

    } catch (error) {
      console.error('Error validating comprehensive eligibility:', error);
      return {
        isEligible: false,
        checks: { error: false },
        reasons: ['Error validating eligibility'],
        warnings: []
      };
    }
  }

  /**
   * Enroll employee in multiple plan assignments
   */
  public static async enrollEmployeeInMultiplePlans({
    employeeId,
    companyId,
    employeeClassType,
    planSelections,
    effectiveDate,
    isSuperAdmin = false
  }: {
    employeeId: string;
    companyId: string;
    employeeClassType: string;
    planSelections: Array<{
      planAssignmentId: string;
      coverageTier: string;
      dependentIds: string[];
      enrollmentType?: string;
      qualifyingLifeEvent?: any;
      newHireDate?: string;
    }>;
    effectiveDate: Date;
    isSuperAdmin?: boolean;
  }): Promise<{ success: boolean; message: string; enrollmentIds?: string[]; calculatedCosts?: any[]; validationWarnings?: any[]; failedPlan?: string; error?: string }> {
    const enrollmentIds: string[] = [];
    const calculatedCosts: any[] = [];
    const validationWarnings: any[] = [];

    try {
      // Create one enrollment per plan assignment
      for (let i = 0; i < planSelections.length; i++) {
        const selection = planSelections[i];

        const result = await this.createEnrollmentWithCostCalculation({
          employeeId,
          planAssignmentId: selection.planAssignmentId,
          companyId,
          employeeClassType,
          coverageTier: selection.coverageTier,
          dependentIds: selection.dependentIds,
          effectiveDate,
          enrollmentType: selection.enrollmentType,
          qualifyingLifeEvent: selection.qualifyingLifeEvent,
          newHireDate: selection.newHireDate,
          isSuperAdmin
        });

        if (result.success && result.enrollmentId) {
          enrollmentIds.push(result.enrollmentId);
          calculatedCosts.push(result.calculatedCost);

          // Collect validation warnings for this plan
          const tierValidation = await EmployeeEnrollmentModelClass.validateCoverageTierSelection(
            selection.coverageTier,
            employeeId,
            selection.dependentIds
          );
          if (!tierValidation.isValid || tierValidation.suggestions.length > 0) {
            validationWarnings.push({
              planAssignmentId: selection.planAssignmentId,
              warnings: tierValidation.warnings,
              suggestions: tierValidation.suggestions
            });
          }
        } else {
          // Enrollment failed - rollback all previously created enrollments
          console.error(`Enrollment failed for plan ${selection.planAssignmentId}:`, result.message);

          if (enrollmentIds.length > 0) {
            console.log(`Rolling back ${enrollmentIds.length} previously created enrollments...`);
            await this.rollbackEnrollments(enrollmentIds);
          }

          return {
            success: false,
            message: `Enrollment failed for plan ${i + 1}: ${result.message}. All enrollments have been rolled back.`,
            failedPlan: selection.planAssignmentId,
            error: result.message
          };
        }
      }

      return {
        success: true,
        message: `Successfully enrolled employee in ${enrollmentIds.length} plans`,
        enrollmentIds,
        calculatedCosts,
        validationWarnings
      };
    } catch (error) {
      console.error('Error enrolling employee in multiple plans:', error);

      // Rollback any enrollments that were created before the error
      if (enrollmentIds.length > 0) {
        console.log(`Rolling back ${enrollmentIds.length} enrollments due to error...`);
        try {
          await this.rollbackEnrollments(enrollmentIds);
        } catch (rollbackError) {
          console.error('Error during rollback:', rollbackError);
        }
      }

      return {
        success: false,
        message: 'Internal error during enrollment. All enrollments have been rolled back.',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Rollback enrollments by deleting them from the database
   */
  private static async rollbackEnrollments(enrollmentIds: string[]): Promise<void> {
    try {
      for (const enrollmentId of enrollmentIds) {
        await EmployeeEnrollmentModelClass.deleteData(enrollmentId);
        console.log(`Rolled back enrollment: ${enrollmentId}`);
      }
    } catch (error) {
      console.error('Error during enrollment rollback:', error);
      throw error;
    }
  }
  // ===== ENROLLMENT PERIODS OPERATIONS =====

  /**
   * Get enrollment periods for a plan assignment
   */
  static async getEnrollmentPeriods(planAssignmentId: string, userId: string, user: any) {
    try {
      // Validate access to plan assignment
      const planAssignment = await PlanAssignmentModelClass.getDataById(planAssignmentId);
      if (!planAssignment) {
        return { success: false, error: 'Plan assignment not found' };
      }

      // Access control validation
      if (!UserModelClass.isSuperAdmin(user)) {
        if (user.isBroker) {
          // Brokers can access plan assignments for their companies
          const brokerCompanies = await CompanyModelClass.getDataByBrokerId(userId);
          const brokerCompanyIds = brokerCompanies.map(company => company._id.toString());
          if (!brokerCompanyIds.includes(planAssignment.companyId.toString())) {
            return { success: false, error: 'Access denied. Plan assignment not accessible to this broker.' };
          }
        } else if (user.isAdmin) {
          // Company admins can only access their own company's plan assignments
          if (planAssignment.companyId.toString() !== user.companyId.toString()) {
            return { success: false, error: 'Access denied. Plan assignment not accessible to this company admin.' };
          }
        } else {
          // Employees can only access plan assignments for their company
          if (planAssignment.companyId.toString() !== user.companyId.toString()) {
            return { success: false, error: 'Access denied. Plan assignment not accessible to this employee.' };
          }
        }
      }

      // Build enrollment periods using existing data
      const now = new Date();
      const enrollmentPeriods = {
        openEnrollment: {
          enabled: true,
          startDate: planAssignment.enrollmentStartDate,
          endDate: planAssignment.enrollmentEndDate,
          isCurrentlyActive: now >= new Date(planAssignment.enrollmentStartDate) && now <= new Date(planAssignment.enrollmentEndDate),
          description: 'Annual open enrollment period'
        },
        newHire: {
          enabled: planAssignment.waitingPeriod?.enabled ?? true,
          windowDays: planAssignment.waitingPeriod?.days ?? 30,
          description: 'New hire enrollment window (uses waiting period configuration)'
        },
        qualifyingLifeEvent: {
          enabled: planAssignment.qualifyingLifeEventWindow?.enabled ?? true,
          windowDays: planAssignment.qualifyingLifeEventWindow?.windowDays ?? 30,
          allowedEvents: planAssignment.qualifyingLifeEventWindow?.allowedEvents ?? [...QUALIFYING_LIFE_EVENT_TYPES],
          description: planAssignment.qualifyingLifeEventWindow?.description ?? 'Qualifying life event enrollment window'
        }
      };

      return {
        success: true,
        planAssignmentId,
        enrollmentPeriods,
        currentDate: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Error in getEnrollmentPeriods service:', error);
      return { success: false, error: 'Internal server error getting enrollment periods' };
    }
  }

  // ===== COST ESTIMATION OPERATIONS =====

  /**
   * Estimate plan costs for all tiers and scenarios
   */
  static async estimatePlanCosts(estimationData: any, userId: string, user: any) {
    try {
      const { planAssignmentId, scenarios = [] } = estimationData;

      // Validate plan assignment access
      const planAssignment = await PlanAssignmentModelClass.getDataById(planAssignmentId);
      if (!planAssignment) {
        return { success: false, error: 'Plan assignment not found' };
      }

      // Access control validation
      if (!UserModelClass.isSuperAdmin(user)) {
        if (user.isBroker) {
          // Brokers can access plan assignments for their companies
          const brokerCompanies = await CompanyModelClass.getDataByBrokerId(userId);
          const brokerCompanyIds = brokerCompanies.map(company => company._id.toString());
          if (!brokerCompanyIds.includes(planAssignment.companyId.toString())) {
            return { success: false, error: 'Access denied. Plan assignment not accessible to this broker.' };
          }
        } else if (user.isAdmin) {
          // Company admins can only access their own company's plan assignments
          if (planAssignment.companyId.toString() !== user.companyId.toString()) {
            return { success: false, error: 'Access denied. Plan assignment not accessible to this company admin.' };
          }
        } else {
          // Employees can only access plan assignments for their company
          if (planAssignment.companyId.toString() !== user.companyId.toString()) {
            return { success: false, error: 'Access denied. Plan assignment not accessible to this employee.' };
          }
        }
      }

      // Default scenarios if none provided
      const scenariosToCalculate = scenarios.length > 0 ? scenarios : [
        { employeeAge: 25, employeeSalary: 50000, description: 'Young employee, average salary' },
        { employeeAge: 35, employeeSalary: 75000, description: 'Mid-career employee, higher salary' },
        { employeeAge: 45, employeeSalary: 100000, description: 'Senior employee, high salary' },
        { employeeAge: 55, employeeSalary: 120000, description: 'Executive level employee' }
      ];

      const payrollFrequency = 'Monthly'; // Default payroll frequency
      const costEstimations = [];

      // Calculate costs for each coverage tier
      for (const tier of planAssignment.coverageTiers || []) {
        const tierEstimation = {
          tierName: tier.tierName,
          baseCost: tier.totalCost,
          scenarios: []
        };

        for (const scenario of scenariosToCalculate) {
          const costResult = CostCalculationService.calculateEnrollmentCost({
            planAssignment,
            employeeAge: scenario.employeeAge,
            selectedTier: tier.tierName,
            employeeSalary: scenario.employeeSalary,
            payrollFrequency
          });

          if (costResult.success) {
            tierEstimation.scenarios.push({
              ...scenario,
              cost: costResult.cost,
              calculationSuccess: true
            });
          } else {
            tierEstimation.scenarios.push({
              ...scenario,
              cost: null,
              calculationSuccess: false,
              error: costResult.error
            });
          }
        }

        costEstimations.push(tierEstimation);
      }

      return {
        success: true,
        planAssignmentId,
        costEstimations,
        calculationDate: new Date().toISOString(),
        scenarios: scenariosToCalculate
      };

    } catch (error) {
      logger.error('Error in estimatePlanCosts service:', error);
      return { success: false, error: 'Internal server error estimating plan costs' };
    }
  }

  // ===== EXPIRED ENROLLMENTS OPERATIONS =====

  /**
   * Get expired enrollments with access control
   */
  static async getExpiredEnrollments(queryData: any, userId: string, user: any) {
    try {
      const { mode = 'user', targetUserId, planAssignmentIds } = queryData;

      let expiredEnrollments: any[] = [];

      if (mode === 'user') {
        // Mode 1: Get expired enrollments for a specific user
        const employeeId = targetUserId || userId;

        // Access control for user mode
        if (!UserModelClass.isSuperAdmin(user)) {
          if (user.isBroker) {
            // Brokers can access expired enrollments for employees in their companies
            const employee = await UserModelClass.getDataById(employeeId);
            if (!employee) {
              return { success: false, error: 'Employee not found' };
            }

            const brokerCompanies = await CompanyModelClass.getDataByBrokerId(userId);
            const brokerCompanyIds = brokerCompanies.map(company => company._id.toString());
            if (!brokerCompanyIds.includes(employee.companyId.toString())) {
              return { success: false, error: 'Access denied. Employee not accessible to this broker.' };
            }
          } else if (user.isAdmin) {
            // Company admins can access expired enrollments for employees in their company
            const employee = await UserModelClass.getDataById(employeeId);
            if (!employee) {
              return { success: false, error: 'Employee not found' };
            }

            if (employee.companyId.toString() !== user.companyId.toString()) {
              return { success: false, error: 'Access denied. Employee not in your company.' };
            }
          } else {
            // Employees can only access their own expired enrollments
            if (employeeId !== userId) {
              return { success: false, error: 'Access denied. Employees can only view their own expired enrollments.' };
            }
          }
        }

        // Get expired enrollments for the specific user (includes automatic expiry check)
        expiredEnrollments = await EmployeeEnrollmentModelClass.getExpiredEnrollmentsByEmployeeId(employeeId);

      } else if (mode === 'planAssignments') {
        // Mode 2: Get expired enrollments by plan assignment IDs
        if (!planAssignmentIds) {
          return { success: false, error: 'planAssignmentIds parameter is required for planAssignments mode' };
        }

        const planAssignmentIdArray = planAssignmentIds.split(',').map((id: string) => id.trim());

        // Access control for plan assignments mode
        if (!user.isAdmin && !user.isBroker && !UserModelClass.isSuperAdmin(user)) {
          return { success: false, error: 'Access denied. Only admins, brokers, and super admins can filter expired enrollments by plan assignments.' };
        }

        // Get expired enrollments for the specified plan assignments (includes automatic expiry check)
        expiredEnrollments = await EmployeeEnrollmentModelClass.getExpiredEnrollmentsByPlanAssignmentIds(planAssignmentIdArray);

        // Filter by broker access if not SuperAdmin
        if (user.isBroker && !UserModelClass.isSuperAdmin(user)) {
          const brokerCompanies = await CompanyModelClass.getDataByBrokerId(userId);
          const brokerCompanyIds = brokerCompanies.map(company => company._id.toString());
          expiredEnrollments = expiredEnrollments.filter(enrollment =>
            brokerCompanyIds.includes(enrollment.companyId.toString())
          );
        }

        // Filter by company access if company admin
        if (user.isAdmin && !user.isBroker && !UserModelClass.isSuperAdmin(user)) {
          expiredEnrollments = expiredEnrollments.filter(enrollment =>
            enrollment.companyId.toString() === user.companyId.toString()
          );
        }
      }

      return {
        success: true,
        mode,
        expiredEnrollments,
        count: expiredEnrollments.length,
        message: `Found ${expiredEnrollments.length} expired enrollments`,
        expiryCheckPerformed: true,
        timestamp: new Date().toISOString(),
        ...(mode === 'user' && { targetUserId: targetUserId || userId }),
        ...(mode === 'planAssignments' && { planAssignmentIds: planAssignmentIds.split(',').map((id: string) => id.trim()) })
      };

    } catch (error) {
      logger.error('Error in getExpiredEnrollments service:', error);
      return { success: false, error: 'Internal server error getting expired enrollments' };
    }
  }

  /**
   * Check and update expired enrollments (SuperAdmin only)
   */
  static async checkExpiredEnrollments(userId: string, user: any) {
    try {
      // SuperAdmin access control
      if (!UserModelClass.isSuperAdmin(user)) {
        return { success: false, error: 'Access denied. Only SuperAdmins can manually trigger expiry checks.' };
      }

      // Perform expiry check and update
      const result = await EmployeeEnrollmentModelClass.checkExpiredEnrollments();

      logger.info(`Manual expiry check completed by user: ${userId}, expired: ${result.expiredCount}`);

      return {
        success: true,
        message: `Expiry check completed. ${result.expiredCount} enrollments marked as expired.`,
        expiredCount: result.expiredCount,
        updatedEnrollmentIds: result.updatedEnrollments,
        timestamp: new Date().toISOString(),
        performedBy: userId
      };

    } catch (error) {
      logger.error('Error in checkExpiredEnrollments service:', error);
      return { success: false, error: 'Internal server error checking expired enrollments' };
    }
  }

}

export default EmployeeEnrollmentService;
