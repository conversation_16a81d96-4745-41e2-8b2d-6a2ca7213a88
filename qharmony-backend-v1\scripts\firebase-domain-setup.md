# Firebase Domain Setup Guide

## 🎯 Quick Fix for "Domain not whitelisted" Error

### **Option 1: Firebase Console (Recommended)**

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select Project**: `qharmony-dev`
3. **Navigate to**: Authentication → Settings → Authorized domains
4. **Add Domain**: `*.benosphere.com` (wildcard for all subdomains)

### **Option 2: Firebase CLI**

```bash
# Install Firebase CLI (if not installed)
npm install -g firebase-tools

# Login to Firebase
firebase login

# Set project
firebase use qharmony-dev

# Note: Firebase CLI doesn't directly support adding authorized domains
# You'll need to use the console for this specific task
```

### **Option 3: Google Cloud Console**

1. **Go to**: https://console.cloud.google.com/
2. **Select Project**: `qharmony-dev`
3. **Navigate to**: APIs & Services → Credentials
4. **Find OAuth 2.0 Client**: Look for web client
5. **Edit**: Add authorized domains

## 🔧 Current Configuration

**Project ID**: `qharmony-dev`
**Service Account**: `<EMAIL>`

## 📋 Recommended Authorized Domains

Add these domains to your Firebase project:

```
localhost
*.benosphere.com
```

This will authorize:
- ✅ `localhost` (development)
- ✅ `app.benosphere.com` (production)
- ✅ `test.benosphere.com` (testing)
- ✅ `staging.benosphere.com` (staging)
- ✅ Any future subdomains

## 🚨 Current Issue

Your backend is trying to generate magic links for:
```
https://test.benosphere.com/onboard
https://test.benosphere.com/login
```

But `test.benosphere.com` is not authorized in Firebase, causing:
```
Error: Domain not whitelisted by project
```

## ✅ After Adding Domain

Once you add `*.benosphere.com` to Firebase authorized domains:

1. **Magic links will work** for all environments
2. **No code changes needed** - the backend will automatically work
3. **Future subdomains** will work without additional configuration

## 🔍 Verification

After adding the domain, test by:

1. **Restart your backend** to see the domain check log
2. **Try self-onboarding** with any email
3. **Check logs** for successful magic link generation

The backend will now log domain status on startup and provide helpful error messages if domain issues occur.
