'use client';

import React, { useState } from 'react';
import { User, Heart, Users, DollarSign, Activity, Play } from 'lucide-react';

interface PersonalizationPageProps {
  onComplete: (profile: any) => void;
}

export const PersonalizationPage: React.FC<PersonalizationPageProps> = ({ onComplete }) => {
  const [familyMembers, setFamilyMembers] = useState('');
  const [expectedUsage, setExpectedUsage] = useState('');
  const [budgetPreference, setBudgetPreference] = useState('');
  const [budgetAmount, setBudgetAmount] = useState(100);
  const [healthConsiderations, setHealthConsiderations] = useState<string[]>([]);

  const handleHealthConsiderationChange = (consideration: string, checked: boolean) => {
    if (checked) {
      setHealthConsiderations(prev => [...prev, consideration]);
    } else {
      setHealthConsiderations(prev => prev.filter(item => item !== consideration));
    }
  };

  const handleSubmit = () => {
    const profile = {
      familyMembers,
      expectedMedicalUsage: expectedUsage,
      budgetPreference,
      budgetAmount,
      healthConsiderations,
      chronicConditions: healthConsiderations.includes('ongoing-conditions'),
      prescriptionNeeds: healthConsiderations.includes('prescription-medications'),
      hasPreferredDoctors: healthConsiderations.includes('preferred-doctors'),
    };
    onComplete(profile);
  };

  const isFormValid = familyMembers && expectedUsage && budgetPreference;

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          backgroundColor: '#dbeafe',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <User style={{ width: '20px', height: '20px', color: '#2563eb' }} />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '512px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0
          }}>
            🤔 Let me learn about you to give the best recommendations!
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0'
          }}>
            I'll ask a few quick questions about your healthcare needs, budget preferences, and family situation.
          </p>
        </div>
      </div>

      {/* Personalization Form */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '24px' }}>
          <div style={{
            width: '24px',
            height: '24px',
            backgroundColor: '#f3e8ff',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Activity style={{ width: '16px', height: '16px', color: '#9333ea' }} />
          </div>
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            AI-Powered Personalization ✨
          </h2>
        </div>

        <p style={{
          color: '#6b7280',
          marginBottom: '24px',
          margin: 0
        }}>
          Let me learn about you to provide the most accurate recommendations:
        </p>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {/* Enhanced Health Profile */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Heart style={{ width: '20px', height: '20px', color: '#ef4444' }} />
              <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#111827', margin: 0 }}>Enhanced Health Profile</h3>
            </div>

            {/* Family Coverage */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Users style={{ width: '16px', height: '16px', color: '#6b7280' }} />
                <label style={{ fontWeight: '500', color: '#374151', margin: 0 }}>Family Coverage Needed</label>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {[
                  { value: 'employee-only', label: 'Just me (Employee only)' },
                  { value: 'employee-spouse', label: 'Me + Spouse/Partner' },
                  { value: 'employee-family', label: 'Me + Family (includes children)' }
                ].map((option) => (
                  <label key={option.value} style={{ display: 'flex', alignItems: 'center', gap: '12px', cursor: 'pointer' }}>
                    <input
                      type="radio"
                      name="familyMembers"
                      value={option.value}
                      checked={familyMembers === option.value}
                      onChange={(e) => setFamilyMembers(e.target.value)}
                      style={{ width: '16px', height: '16px', accentColor: '#2563eb' }}
                    />
                    <span style={{ color: '#374151' }}>{option.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Expected Healthcare Usage */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Activity style={{ width: '16px', height: '16px', color: '#6b7280' }} />
                <label style={{ fontWeight: '500', color: '#374151', margin: 0 }}>Expected Healthcare Usage</label>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {[
                  { value: 'low', label: 'Low - Just preventive care & checkups' },
                  { value: 'moderate', label: 'Moderate - Occasional visits & some prescriptions' },
                  { value: 'high', label: 'High - Regular specialists, procedures, or chronic conditions' }
                ].map((option) => (
                  <label key={option.value} style={{ display: 'flex', alignItems: 'center', gap: '12px', cursor: 'pointer' }}>
                    <input
                      type="radio"
                      name="expectedUsage"
                      value={option.value}
                      checked={expectedUsage === option.value}
                      onChange={(e) => setExpectedUsage(e.target.value)}
                      style={{ width: '16px', height: '16px', accentColor: '#2563eb' }}
                    />
                    <span style={{ color: '#374151' }}>{option.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Budget Preference */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <DollarSign style={{ width: '16px', height: '16px', color: '#6b7280' }} />
                <label style={{ fontWeight: '500', color: '#374151', margin: 0 }}>Budget Preference</label>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {[
                  { value: 'low-premium', label: 'Lower monthly cost, higher deductible' },
                  { value: 'balanced', label: 'Balanced monthly cost and deductible' },
                  { value: 'low-deductible', label: 'Higher monthly cost, lower deductible' }
                ].map((option) => (
                  <label key={option.value} style={{ display: 'flex', alignItems: 'center', gap: '12px', cursor: 'pointer' }}>
                    <input
                      type="radio"
                      name="budgetPreference"
                      value={option.value}
                      checked={budgetPreference === option.value}
                      onChange={(e) => setBudgetPreference(e.target.value)}
                      style={{ width: '16px', height: '16px', accentColor: '#2563eb' }}
                    />
                    <span style={{ color: '#374151' }}>{option.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Budget Slider */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <label style={{ fontWeight: '500', color: '#374151', margin: 0 }}>
                Maximum monthly budget: ${budgetAmount}/paycheck
              </label>
              <input
                type="range"
                min="50"
                max="200"
                value={budgetAmount}
                onChange={(e) => setBudgetAmount(Number(e.target.value))}
                style={{
                  width: '100%',
                  height: '8px',
                  backgroundColor: '#e5e7eb',
                  borderRadius: '8px',
                  appearance: 'none',
                  cursor: 'pointer'
                }}
              />
            </div>

            {/* Health Considerations */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <label style={{ fontWeight: '500', color: '#374151', margin: 0 }}>Health Considerations</label>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {[
                  { value: 'glasses-contacts', label: 'I wear glasses or contacts' },
                  { value: 'dental-care', label: 'I need regular dental care or have dental work planned' },
                  { value: 'ongoing-conditions', label: 'I have ongoing health conditions requiring regular care' },
                  { value: 'prescription-medications', label: 'I take regular prescription medications' },
                  { value: 'preferred-doctors', label: 'I have preferred doctors I want to keep seeing' }
                ].map((option) => (
                  <label key={option.value} style={{ display: 'flex', alignItems: 'center', gap: '12px', cursor: 'pointer' }}>
                    <input
                      type="checkbox"
                      value={option.value}
                      checked={healthConsiderations.includes(option.value)}
                      onChange={(e) => handleHealthConsiderationChange(option.value, e.target.checked)}
                      style={{ width: '16px', height: '16px', accentColor: '#2563eb' }}
                    />
                    <span style={{ color: '#374151' }}>{option.label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            onClick={handleSubmit}
            disabled={!isFormValid}
            style={{
              width: '100%',
              padding: '12px 24px',
              borderRadius: '8px',
              fontWeight: '500',
              border: 'none',
              cursor: isFormValid ? 'pointer' : 'not-allowed',
              transition: 'background-color 0.2s ease',
              backgroundColor: isFormValid ? '#4b5563' : '#d1d5db',
              color: isFormValid ? 'white' : '#9ca3af'
            }}
            onMouseOver={(e) => {
              if (isFormValid) e.currentTarget.style.backgroundColor = '#374151';
            }}
            onMouseOut={(e) => {
              if (isFormValid) e.currentTarget.style.backgroundColor = '#4b5563';
            }}
          >
            Get My Personalized Recommendations
          </button>
        </div>

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          gap: '12px',
          paddingTop: '24px',
          borderTop: '1px solid #e5e7eb',
          marginTop: '24px'
        }}>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}
          onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
          >
            <Play size={16} style={{ color: '#6b7280' }} />
            Watch Video
          </button>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}
          onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
          >
            <span style={{ color: '#2563eb' }}>❓</span>
            Ask Questions
          </button>
        </div>
      </div>
    </div>
  );
};
