'use client';

import React, { useState } from 'react';
import SignatureModal from '../components/SignatureModal';
import SignatureViewer from '../components/SignatureViewer';
import { getSignatureReference, debugSignature } from '../utils/signatureUtils';

const SignatureTestPage: React.FC = () => {
  const [showSignatureModal, setShowSignatureModal] = useState(false);
  const [showSignatureViewer, setShowSignatureViewer] = useState(false);
  const [signatureStatus, setSignatureStatus] = useState<string>('');

  const handleSignatureComplete = (signatureData: string) => {
    console.log('✅ Signature completed!', signatureData);
    setSignatureStatus('Signature completed and stored successfully!');
    setShowSignatureModal(false);
  };

  const checkSignatureStatus = () => {
    const signature = getSignatureReference();
    if (signature) {
      setSignatureStatus(`Signature found: ${signature.employeeName} signed on ${new Date(signature.timestamp).toLocaleString()}`);
    } else {
      setSignatureStatus('No signature found in storage');
    }
  };

  const handleDebugSignature = () => {
    debugSignature();
    setSignatureStatus('Debug info logged to console');
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f9fafb',
      padding: '40px 20px'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '16px',
        padding: '40px',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <h1 style={{
            fontSize: '32px',
            fontWeight: '600',
            color: '#111827',
            margin: '0 0 16px 0'
          }}>
            📝 Signature Test Page
          </h1>
          <p style={{
            color: '#6b7280',
            fontSize: '16px',
            margin: 0,
            lineHeight: '24px'
          }}>
            Test the digital signature functionality for enrollment process
          </p>
        </div>

        {/* Status Display */}
        {signatureStatus && (
          <div style={{
            backgroundColor: '#f0fdf4',
            border: '1px solid #bbf7d0',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '32px',
            color: '#047857'
          }}>
            <strong>Status:</strong> {signatureStatus}
          </div>
        )}

        {/* Action Buttons */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '32px'
        }}>
          <button
            onClick={() => setShowSignatureModal(true)}
            style={{
              padding: '16px 24px',
              backgroundColor: '#2563eb',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              textAlign: 'center'
            }}
          >
            🖊️ Create Signature
          </button>

          <button
            onClick={() => setShowSignatureViewer(true)}
            style={{
              padding: '16px 24px',
              backgroundColor: '#059669',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              textAlign: 'center'
            }}
          >
            👁️ View Signature
          </button>

          <button
            onClick={checkSignatureStatus}
            style={{
              padding: '16px 24px',
              backgroundColor: '#7c3aed',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              textAlign: 'center'
            }}
          >
            🔍 Check Status
          </button>

          <button
            onClick={handleDebugSignature}
            style={{
              padding: '16px 24px',
              backgroundColor: '#6b7280',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer',
              fontWeight: '500',
              fontSize: '16px',
              textAlign: 'center'
            }}
          >
            🐛 Debug Info
          </button>
        </div>

        {/* Information Panel */}
        <div style={{
          backgroundColor: '#f8fafc',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '24px'
        }}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#111827',
            margin: '0 0 16px 0'
          }}>
            How it works:
          </h3>
          <ul style={{
            color: '#4b5563',
            fontSize: '14px',
            lineHeight: '20px',
            margin: 0,
            paddingLeft: '20px'
          }}>
            <li><strong>Create Signature:</strong> Opens the signature modal where you can draw your signature</li>
            <li><strong>View Signature:</strong> Shows the stored signature with metadata and verification</li>
            <li><strong>Check Status:</strong> Displays current signature status and basic info</li>
            <li><strong>Debug Info:</strong> Logs detailed signature information to browser console</li>
          </ul>
          
          <div style={{
            marginTop: '16px',
            padding: '12px',
            backgroundColor: '#fef3c7',
            border: '1px solid #f59e0b',
            borderRadius: '6px',
            fontSize: '12px',
            color: '#92400e'
          }}>
            <strong>🔒 Security Note:</strong> Signatures are encrypted and stored locally in browser storage. 
            In production, these would be sent to a secure backend server.
          </div>
        </div>

        {/* Back to Enrollment */}
        <div style={{ textAlign: 'center', marginTop: '32px' }}>
          <a
            href="/ai-enroller/employee-enrol"
            style={{
              display: 'inline-block',
              padding: '12px 24px',
              backgroundColor: 'white',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              color: '#374151',
              textDecoration: 'none',
              fontWeight: '500',
              fontSize: '14px',
              transition: 'all 0.2s'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.borderColor = '#2563eb';
              e.currentTarget.style.color = '#2563eb';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.borderColor = '#e5e7eb';
              e.currentTarget.style.color = '#374151';
            }}
          >
            ← Back to Enrollment
          </a>
        </div>
      </div>

      {/* Signature Modal */}
      <SignatureModal
        isOpen={showSignatureModal}
        onClose={() => setShowSignatureModal(false)}
        onSignatureComplete={handleSignatureComplete}
        employeeName="Test Employee"
      />

      {/* Signature Viewer */}
      <SignatureViewer
        isOpen={showSignatureViewer}
        onClose={() => setShowSignatureViewer(false)}
      />
    </div>
  );
};

export default SignatureTestPage;
