'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { HiOutlineEye, HiOutlineDownload, HiOutlineCheckCircle, HiOutlinePencil, HiOutlineUsers, HiOutlineHome, HiOutlineOfficeBuilding, HiOutlineClipboardList, HiOutlineCalendar } from 'react-icons/hi';
import { getPlanAssignmentsByCompany } from '../../../services/planAssignmentApi';
import { getApiBaseUrl, getUserId } from '../../../../../../utils/env';

interface Plan {
  _id: string;
  planName: string;
  carrier: string;
  type: string;
  planCode: string;
  totalMonthlyPremium?: number;
  employerContribution?: number;
  employeeContribution?: number;
  isNew?: boolean;
  isUpdated?: boolean;
  changes?: string[];
}

interface Company {
  _id: string;
  companyName: string;
  employeeCount?: number;
}

export default function ReviewPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const companyId = params.companyId as string;
  const selectedAssignmentIds = searchParams.get('assignments')?.split(',') || [];

  const [company, setCompany] = useState<Company | null>(null);
  const [selectedPlans, setSelectedPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);

  // Function to fetch plan details by ID (same as other pages)
  const fetchPlanDetails = async (planId: string) => {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
    const getUserId = () => {
      const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found. Please authenticate first.');
      }
      return userId;
    };

    try {
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': getUserId(),
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          planName: data.plan?.planName || 'Unknown Plan',
          planCode: data.plan?.planCode || 'N/A',
          planType: data.plan?.planType || 'N/A',
          coverageType: data.plan?.coverageType || 'Unknown',
          coverageSubTypes: data.plan?.coverageSubTypes || [],
          metalTier: data.plan?.metalTier || '',
          carrierName: data.carrier?.carrierName || 'Unknown Carrier'
        };
      }
    } catch (error) {
      console.error('Error fetching plan details for planId:', planId, error);
    }

    return {
      planName: 'Unknown Plan',
      planCode: 'N/A',
      planType: 'N/A',
      coverageType: 'Unknown',
      coverageSubTypes: [],
      metalTier: '',
      carrierName: 'Unknown Carrier'
    };
  };

  // Function to fetch plan assignment details including coverage tiers
  const fetchPlanAssignmentDetails = async (assignmentId: string) => {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
    const getUserId = () => localStorage.getItem('userid1') || localStorage.getItem('userId') || '6838677aef6db0212bcfdacd';

    try {
      console.log('Fetching plan assignment details for ID:', assignmentId);
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': getUserId(),
        },
      });

      if (response.ok) {
        const data = await response.json();
        const assignment = data.assignment._doc || data.assignment;
        return assignment;
      } else {
        console.error('Failed to fetch plan assignment details. Status:', response.status);
      }
    } catch (error) {
      console.error('Error fetching plan assignment details:', error);
    }
    return null;
  };

  // Function to fetch company details with employee count (handles broker's own company)
  const fetchCompanyDetails = async (companyId: string) => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      // First, check if this is the broker's own company using /employee/company-details
      const ownCompanyResponse = await fetch(`${API_BASE_URL}/employee/company-details`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (ownCompanyResponse.ok) {
        const ownCompanyData = await ownCompanyResponse.json();
        if (ownCompanyData.company &&
            ownCompanyData.company.isBrokerage &&
            ownCompanyData.company._id === companyId) {
          console.log('Found broker\'s own company with employee count:', ownCompanyData.company.companySize);
          return {
            _id: ownCompanyData.company._id,
            companyName: ownCompanyData.company.name || 'Unknown Company',
            employeeCount: ownCompanyData.company.companySize || 250
          };
        }
      }

      // If not broker's own company, try to get from /admin/all-companies (client companies)
      const companiesResponse = await fetch(`${API_BASE_URL}/admin/all-companies`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        const targetCompany = companiesData.companies?.find((company: any) => company._id === companyId);

        if (targetCompany) {
          console.log('Found client company with employee count:', targetCompany.companySize);
          return {
            _id: targetCompany._id,
            companyName: targetCompany.name || 'Unknown Company',
            employeeCount: targetCompany.companySize || 250
          };
        }
      }
    } catch (error) {
      console.error('Error fetching company details:', error);
    }

    // Final fallback
    return {
      _id: companyId,
      companyName: 'Unknown Company',
      employeeCount: 250
    };
  };

  const fetchCompanyAndPlans = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch company details with employee count
      const companyDetails = await fetchCompanyDetails(companyId);

      // Set company data
      setCompany(companyDetails);

      // Fetch plan assignments for the company
      const planAssignmentsResult = await getPlanAssignmentsByCompany(companyId, true);

      if (planAssignmentsResult.success && planAssignmentsResult.data) {
        const assignments = planAssignmentsResult.data.assignments;
        console.log('All assignments:', assignments.length);
        console.log('Selected assignment IDs from URL:', selectedAssignmentIds);

        // Filter assignments based on selected assignment IDs if provided
        let filteredAssignments = assignments;
        if (selectedAssignmentIds.length > 0) {
          filteredAssignments = assignments.filter(assignment => {
            return selectedAssignmentIds.includes(assignment._id);
          });
        }

        console.log('Filtered assignments for review:', filteredAssignments.length);

        // Enhance assignments with plan details and calculate costs
        const enhancedPlans = await Promise.all(
          filteredAssignments.map(async (assignment) => {
            // Get planId as string
            const planIdString = typeof assignment.planId === 'string' ? assignment.planId : assignment.planId?._id || '';
            console.log('Processing assignment:', assignment._id, 'with planId:', planIdString);

            // Fetch plan details using the planId
            let planDetails = null;
            if (planIdString) {
              planDetails = await fetchPlanDetails(planIdString);
              console.log('Fetched plan details:', planDetails);
            }

            // Fetch assignment details to get coverage tiers
            const assignmentDetails = await fetchPlanAssignmentDetails(assignment._id);
            console.log('Fetched assignment details:', assignmentDetails);

            // Get "Employee Only" tier for cost calculations
            const coverageTiers = assignmentDetails?.coverageTiers || assignment.coverageTiers || [];
            const employeeOnlyTier = coverageTiers.find((tier: any) =>
              tier.tierName?.toLowerCase().includes('employee only') ||
              tier.tierName?.toLowerCase() === 'employee'
            ) || coverageTiers[0] || {};

            const totalMonthlyPremium = employeeOnlyTier.totalCost || 0;
            const employerContribution = employeeOnlyTier.employerCost || 0;
            const employeeContribution = employeeOnlyTier.employeeCost || (totalMonthlyPremium - employerContribution);

            // Determine coverage type for display
            let displayType = 'Medical';
            if (planDetails?.coverageSubTypes && planDetails.coverageSubTypes.length > 0) {
              const primarySubtype = planDetails.coverageSubTypes[0].toLowerCase();
              if (primarySubtype.includes('dental')) {
                displayType = 'Dental';
              } else if (primarySubtype.includes('vision')) {
                displayType = 'Vision';
              }
            } else if (planDetails?.coverageType) {
              const coverageType = planDetails.coverageType.toLowerCase();
              if (coverageType.includes('dental')) {
                displayType = 'Dental';
              } else if (coverageType.includes('vision')) {
                displayType = 'Vision';
              }
            }

            return {
              _id: assignment._id,
              planName: planDetails?.planName || 'Unknown Plan',
              carrier: planDetails?.carrierName || 'Unknown Carrier',
              type: displayType,
              planCode: planDetails?.planCode || 'N/A',
              totalMonthlyPremium,
              employerContribution,
              employeeContribution,
              isNew: assignment.status === 'Draft', // Draft status indicates new
              isUpdated: assignment.status === 'Active', // Active status indicates updated
              changes: assignment.status === 'Draft' ? ['New plan added'] : ['Plan configuration updated']
            };
          })
        );

        console.log('Enhanced plans for review:', enhancedPlans);
        setSelectedPlans(enhancedPlans);

      } else {
        console.error('Failed to fetch plan assignments:', planAssignmentsResult.error);
        setSelectedPlans([]);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      // Set fallback data even on error
      setCompany({
        _id: companyId,
        companyName: 'TechCorp Inc.',
        employeeCount: 250
      });
      setSelectedPlans([]);
    } finally {
      setLoading(false);
    }
  }, [companyId]);

  useEffect(() => {
    fetchCompanyAndPlans();
  }, [fetchCompanyAndPlans]);

  const handleViewPlansPage = () => {
    router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`);
  };

  const handleDownloadSummary = () => {
    // Mock download functionality
    alert('Summary downloaded successfully!');
  };

  const handleConfirmAndSave = () => {
    // Navigate to confirmation page with assignment IDs
    const assignmentIds = selectedPlans.map(plan => plan._id);
    router.push(`/ai-enroller/manage-groups/company/${companyId}/confirmation?assignments=${assignmentIds.join(',')}`);
  };

  // Calculate costs correctly based on your requirements
  const employeeCount = company?.employeeCount || 250;

  // 1. Sum all plans' employer costs (this is cost per employee for all plans combined)
  const sumOfAllPlansEmployerCost = selectedPlans.reduce((sum, plan) => sum + (plan.employerContribution || 0), 0);
  const sumOfAllPlansEmployeeCost = selectedPlans.reduce((sum, plan) => sum + (plan.employeeContribution || 0), 0);
  const sumOfAllPlansTotalCost = selectedPlans.reduce((sum, plan) => sum + (plan.totalMonthlyPremium || 0), 0);

  // 2. Total Monthly Employer Cost = sum of all plans × number of employees
  const totalCompanyMonthlyEmployerCost = sumOfAllPlansEmployerCost * employeeCount;
  const totalCompanyAnnualEmployerCost = totalCompanyMonthlyEmployerCost * 12;

  // 3. Average Cost per Employee = sum of all plans (not multiplied by employee count)
  const averageCostPerEmployee = sumOfAllPlansEmployerCost;

  console.log('Cost Calculation Debug:');
  console.log('Employee Count:', employeeCount);
  console.log('Sum of all plans employer cost (per employee):', sumOfAllPlansEmployerCost);
  console.log('Total company monthly employer cost:', totalCompanyMonthlyEmployerCost);
  console.log('Average cost per employee:', averageCostPerEmployee);

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading review...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Step Navigation */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex gap-3 overflow-x-auto">
            <button
              onClick={() => router.push('/ai-enroller')}
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineHome className="w-4 h-4" />
              Home
              <span className="flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2">
                <HiOutlineCheckCircle className="w-5 h-5" />
              </span>
            </button>

            <button
              onClick={() => router.push('/ai-enroller/manage-groups/select-company')}
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineOfficeBuilding className="w-4 h-4" />
              Select Company
              <span className="flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2">
                <HiOutlineCheckCircle className="w-5 h-5" />
              </span>
            </button>

            <button
              onClick={() => router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`)}
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineClipboardList className="w-4 h-4" />
              View Plans
              <span className="flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2">
                <HiOutlineCheckCircle className="w-5 h-5" />
              </span>
            </button>

            <button
              onClick={() => router.push(`/ai-enroller/manage-groups/company/${companyId}/set-dates`)}
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineCalendar className="w-4 h-4" />
              Set Dates
              <span className="flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2">
                <HiOutlineCheckCircle className="w-5 h-5" />
              </span>
            </button>

            <div
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-purple-50 text-purple-600 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineCheckCircle className="w-4 h-4" />
              Review
            </div>

            <div
              className="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap"
              style={{
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                fontSize: '13px',
                fontWeight: '450',
                lineHeight: '1.2'
              }}
            >
              <HiOutlineCheckCircle className="w-4 h-4" />
              Confirmation
            </div>
          </div>
          <div className="mt-4">
            <div className="text-right text-sm text-gray-500">
              Step 5 of 6<br />
              <span className="text-xs text-purple-600 font-medium">Final review</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-8 bg-white">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Final Review & Confirmation</h1>
          <p className="text-gray-600">Review all changes before saving for {company?.companyName}</p>
        </div>

        {/* Ready to Save Banner */}
        <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-8">
          <div className="flex items-center gap-2">
            <HiOutlineCheckCircle className="text-green-600 w-5 h-5" />
            <span className="text-green-800 font-medium">Ready to save!</span>
            <span className="text-green-700">Review the changes below and confirm when ready.</span>
          </div>
        </div>

        {/* Plan Summary */}
        <div className="bg-white border border-gray-200 rounded-xl p-6 mb-8">
          <div className="flex items-center gap-3 mb-6">
            <HiOutlineCheckCircle className="text-gray-600 w-6 h-6" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Plan Summary</h2>
              <p className="text-sm text-gray-600">Side-by-side comparison of new vs. existing plans</p>
            </div>
          </div>

          <div className="space-y-6">
            {selectedPlans.map((plan) => (
              <div key={plan._id} className="border-l-4 border-blue-500 pl-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{plan.planName}</h3>
                    <p className="text-sm text-gray-600">{plan.carrier} • {plan.type}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    {plan.isUpdated && (
                      <span className="bg-blue-100 text-blue-800 text-xs px-2.5 py-1 rounded-xl border border-blue-200 font-medium">
                        Updated
                      </span>
                    )}
                    {plan.isNew && (
                      <span className="bg-green-100 text-green-800 text-xs px-2.5 py-1 rounded-md border border-green-200 font-medium">
                        New
                      </span>
                    )}
                    <button className="text-blue-600 hover:text-blue-700 p-1">
                      <HiOutlinePencil className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Cost Breakdown */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 rounded-xl">
                    <p className="text-xs text-gray-500 mb-1">Total Monthly Premium</p>
                    <p className="text-lg font-semibold text-gray-900">${plan.totalMonthlyPremium?.toFixed(2)}</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-xl">
                    <p className="text-xs text-gray-500 mb-1">Employer Contribution</p>
                    <p className="text-lg font-semibold text-green-600">${plan.employerContribution?.toFixed(2)}</p>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-xl">
                    <p className="text-xs text-gray-500 mb-1">Employee Contribution</p>
                    <p className="text-lg font-semibold text-blue-600">${plan.employeeContribution?.toFixed(2)}</p>
                  </div>
                </div>

                {/* Changes Made */}
                {plan.changes && plan.changes.length > 0 && (
                  <div className="bg-blue-50 rounded-xl p-3">
                    <p className="text-sm font-medium text-blue-900 mb-2">Changes Made:</p>
                    <ul className="space-y-1">
                      {plan.changes.map((change, index) => (
                        <li key={index} className="text-sm text-blue-800 flex items-center gap-2">
                          <span className="w-1 h-1 bg-blue-600 rounded-full"></span>
                          {change}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Company Impact Summary */}
        <div className="bg-white border border-gray-200 rounded-xl p-6 mb-8">
          <div className="flex items-center gap-3 mb-6">
            <HiOutlineUsers className="text-gray-600 w-6 h-6" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Company Impact Summary</h2>
              <p className="text-sm text-gray-600">Total cost impact for {company?.companyName}</p>
            </div>
          </div>

          {/* Debug Information */}
          <div className="bg-gray-50 rounded-xl p-4 mb-6 text-xs">
            <h4 className="font-medium text-gray-700 mb-2">Calculation Breakdown:</h4>
            <div className="space-y-1 text-gray-600">
              <p>• Company has {employeeCount} employees</p>
              <p>• Plans selected: {selectedPlans.length}</p>
              {selectedPlans.map((plan, index) => (
                <p key={index}>• {plan.planName}: ${plan.employerContribution?.toFixed(2) || '0.00'} employer cost per employee</p>
              ))}
              <p className="font-medium pt-2">• Total per employee: ${sumOfAllPlansEmployerCost.toFixed(2)}</p>
              <p className="font-medium">• Total for company: ${sumOfAllPlansEmployerCost.toFixed(2)} × {employeeCount} = ${totalCompanyMonthlyEmployerCost.toFixed(2)}</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-6">
            <div className="border-l-4 border-green-500 pl-4">
              <p className="text-sm text-gray-600 mb-1">Total Monthly Employer Cost</p>
              <p className="text-2xl font-bold text-green-600">${totalCompanyMonthlyEmployerCost.toFixed(2)}</p>
              <p className="text-xs text-green-600">${sumOfAllPlansEmployerCost.toFixed(2)} per employee × {employeeCount} employees</p>
            </div>
            <div className="border-l-4 border-blue-500 pl-4">
              <p className="text-sm text-gray-600 mb-1">Total Annual Employer Cost</p>
              <p className="text-2xl font-bold text-blue-600">${totalCompanyAnnualEmployerCost.toFixed(2)}</p>
              <p className="text-xs text-blue-600">Projected for full year</p>
            </div>
            <div className="border-l-4 border-orange-500 pl-4">
              <p className="text-sm text-gray-600 mb-1">Average Cost per Employee</p>
              <p className="text-2xl font-bold text-orange-600">${averageCostPerEmployee.toFixed(2)}</p>
              <p className="text-xs text-orange-600">Sum of all plans for Employee Only tier</p>
            </div>
          </div>
        </div>

        {/* Validation Check */}
        <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-8">
          <div className="flex items-center gap-2">
            <HiOutlineCheckCircle className="text-green-600 w-5 h-5" />
            <span className="text-green-800 font-medium">Validation Check:</span>
            <span className="text-green-700">All contribution amounts are within acceptable ranges. No issues detected.</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 mb-8">
          <button
            onClick={handleViewPlansPage}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2"
          >
            <HiOutlineEye className="w-4 h-4" />
            View Plans Page
          </button>
          <button
            onClick={handleDownloadSummary}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2"
          >
            <HiOutlineDownload className="w-4 h-4" />
            Download Summary
          </button>
          <button
            onClick={handleConfirmAndSave}
            className="flex-1 px-6 py-2 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors flex items-center justify-center gap-2"
          >
            <HiOutlineCheckCircle className="w-4 h-4" />
            Confirm and Save
          </button>
        </div>

        {/* Important Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-start gap-2">
            <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
              <span className="text-white text-xs">!</span>
            </div>
            <div>
              <span className="font-medium text-blue-800">Important:</span>
              <span className="text-blue-700"> Once confirmed, these changes will be applied to {company?.companyName}&apos;s benefit plans. Plan documents and employee communications should be updated accordingly.</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

