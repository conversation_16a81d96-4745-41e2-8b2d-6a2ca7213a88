// Simple service worker for caching ai-enroller resources
const CACHE_NAME = 'ai-enroller-cache-v1';
const urlsToCache = [
  '/brea.png',
  '/font.css'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache);
      })
  );
});

self.addEventListener('fetch', (event) => {
  // Only cache GET requests for ai-enroller resources
  if (event.request.method === 'GET' && 
      (event.request.url.includes('/ai-enroller') || 
       event.request.url.includes('/brea.png') ||
       event.request.url.includes('/font.css'))) {
    
    event.respondWith(
      caches.match(event.request)
        .then((response) => {
          // Return cached version or fetch from network
          return response || fetch(event.request);
        })
    );
  }
});
