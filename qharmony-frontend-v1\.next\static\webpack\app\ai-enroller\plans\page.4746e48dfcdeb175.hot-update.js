"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-enroller/plans/page",{

/***/ "(app-pages-browser)/./src/app/ai-enroller/plans/page.tsx":
/*!********************************************!*\
  !*** ./src/app/ai-enroller/plans/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineDuplicate,HiOutlinePause,HiOutlinePencil,HiOutlinePlay,HiOutlinePlus,HiOutlineQuestionMarkCircle,HiOutlineSearch,HiOutlineTrash,HiOutlineViewGrid,HiOutlineX!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=RiCalendarLine,RiHealthBookLine,RiMoneyDollarCircleLine,RiShieldCheckLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../create-plan/services/planApi */ \"(app-pages-browser)/./src/app/ai-enroller/create-plan/services/planApi.ts\");\n/* harmony import */ var _manage_groups_company_companyId_plans_components_CreatePlanForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../manage-groups/company/[companyId]/plans/components/CreatePlanForm */ \"(app-pages-browser)/./src/app/ai-enroller/manage-groups/company/[companyId]/plans/components/CreatePlanForm.tsx\");\n/* harmony import */ var _employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../employee-enrol/components/EnrollmentHeader */ \"(app-pages-browser)/./src/app/ai-enroller/employee-enrol/components/EnrollmentHeader.tsx\");\n/* harmony import */ var _plans_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plans.css */ \"(app-pages-browser)/./src/app/ai-enroller/plans/plans.css\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../utils/env */ \"(app-pages-browser)/./src/utils/env.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// API configuration\nconst API_BASE_URL = (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getApiBaseUrl)();\nconst PlansPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [carrierFilter, setCarrierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [carriers, setCarriers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPlanModal, setShowPlanModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPlan, setEditingPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [planAssignmentCounts, setPlanAssignmentCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Custom modal states\n    const [showConfirmModal, setShowConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmModalData, setConfirmModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAlertModal, setShowAlertModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [alertModalData, setAlertModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInputModal, setShowInputModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inputModalData, setInputModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadPlans();\n    }, []);\n    // Function to fetch assignment counts for all plans\n    const loadPlanAssignmentCounts = async (planIds)=>{\n        try {\n            const counts = {};\n            // Fetch assignment counts for each plan\n            await Promise.all(planIds.map(async (planId)=>{\n                try {\n                    const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plan-assignments?planId=\").concat(planId), {\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                        }\n                    });\n                    if (response.ok) {\n                        const result = await response.json();\n                        counts[planId] = result.count || 0;\n                    } else {\n                        counts[planId] = 0;\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching assignment count for plan \".concat(planId, \":\"), error);\n                    counts[planId] = 0;\n                }\n            }));\n            setPlanAssignmentCounts(counts);\n        } catch (error) {\n            console.error(\"Error loading plan assignment counts:\", error);\n        }\n    };\n    const loadPlans = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load both plans and carriers\n            const [plansResult, carriersResult] = await Promise.all([\n                (0,_create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__.getPlans)(),\n                (0,_create_plan_services_planApi__WEBPACK_IMPORTED_MODULE_4__.getCarriers)()\n            ]);\n            if (plansResult.success && plansResult.data) {\n                const plans = plansResult.data.plans;\n                setPlans(plans);\n                // Calculate statistics\n                const totalPlans = plans.length;\n                const activePlans = plans.filter((p)=>p.status === \"Active\").length;\n                const recentPlans = plans.filter((p)=>{\n                    if (!p.createdAt) return false;\n                    const createdDate = new Date(p.createdAt);\n                    const weekAgo = new Date();\n                    weekAgo.setDate(weekAgo.getDate() - 7);\n                    return createdDate > weekAgo;\n                });\n                const plansByStatus = plans.reduce((acc, plan)=>{\n                    const status = plan.status || \"Unknown\";\n                    acc[status] = (acc[status] || 0) + 1;\n                    return acc;\n                }, {});\n                setStats({\n                    totalPlans,\n                    plansByStatus,\n                    recentPlans\n                });\n                // Load assignment counts for all plans\n                const planIds = plans.map((plan)=>plan._id);\n                loadPlanAssignmentCounts(planIds);\n            } else {\n                setError(plansResult.error || \"Failed to load plans\");\n            }\n            // Load carriers for display purposes\n            if (carriersResult.success && carriersResult.data) {\n                setCarriers(carriersResult.data);\n            }\n        } catch (err) {\n            setError(\"Failed to load plans\");\n            console.error(\"Error loading plans:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredPlans = plans.filter((plan)=>{\n        var _plan_planType;\n        const matchesSearch = (plan.planName || \"\").toLowerCase().includes(searchQuery.toLowerCase()) || (plan.description || \"\").toLowerCase().includes(searchQuery.toLowerCase()) || (plan.planCode || \"\").toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesFilter = filterType === \"all\" || ((_plan_planType = plan.planType) === null || _plan_planType === void 0 ? void 0 : _plan_planType.toLowerCase()) === filterType.toLowerCase() || (plan.status || \"\").toLowerCase() === filterType.toLowerCase();\n        const matchesCarrier = carrierFilter === \"all\" || plan.carrierId === carrierFilter;\n        return matchesSearch && matchesFilter && matchesCarrier;\n    });\n    // Pagination logic\n    const totalPages = Math.ceil(filteredPlans.length / itemsPerPage);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    const paginatedPlans = filteredPlans.slice(startIndex, endIndex);\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    const handleClearFilters = ()=>{\n        setSearchQuery(\"\");\n        setFilterType(\"all\");\n        setCarrierFilter(\"all\");\n        setCurrentPage(1);\n    };\n    // Custom modal helpers\n    const showCustomAlert = (title, message, onClose)=>{\n        setAlertModalData({\n            title,\n            message,\n            onClose\n        });\n        setShowAlertModal(true);\n    };\n    const showCustomConfirm = (title, message, onConfirm, onCancel)=>{\n        setConfirmModalData({\n            title,\n            message,\n            onConfirm,\n            onCancel\n        });\n        setShowConfirmModal(true);\n    };\n    const closeAlertModal = ()=>{\n        setShowAlertModal(false);\n        if (alertModalData === null || alertModalData === void 0 ? void 0 : alertModalData.onClose) {\n            alertModalData.onClose();\n        }\n        setAlertModalData(null);\n    };\n    const closeConfirmModal = ()=>{\n        setShowConfirmModal(false);\n        if (confirmModalData === null || confirmModalData === void 0 ? void 0 : confirmModalData.onCancel) {\n            confirmModalData.onCancel();\n        }\n        setConfirmModalData(null);\n    };\n    const confirmAction = ()=>{\n        if (confirmModalData === null || confirmModalData === void 0 ? void 0 : confirmModalData.onConfirm) {\n            confirmModalData.onConfirm();\n        }\n        closeConfirmModal();\n    };\n    const showCustomInput = (title, fields, onSubmit, onCancel)=>{\n        setInputModalData({\n            title,\n            fields,\n            onSubmit,\n            onCancel\n        });\n        setShowInputModal(true);\n    };\n    const closeInputModal = ()=>{\n        setShowInputModal(false);\n        if (inputModalData === null || inputModalData === void 0 ? void 0 : inputModalData.onCancel) {\n            inputModalData.onCancel();\n        }\n        setInputModalData(null);\n    };\n    const handleEditPlan = async (planId)=>{\n        try {\n            // Check if plan can be edited\n            const canEditResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/can-edit\"), {\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (canEditResponse.ok) {\n                const canEditResult = await canEditResponse.json();\n                if (canEditResult.canEdit) {\n                    // Find the plan and open edit modal\n                    const plan = plans.find((p)=>p._id === planId);\n                    if (plan) {\n                        setEditingPlan(plan);\n                        setShowPlanModal(true);\n                    } else {\n                        showCustomAlert(\"Error\", \"Plan not found\");\n                    }\n                } else {\n                    showCustomAlert(\"Cannot Edit Plan\", canEditResult.message);\n                }\n            } else {\n                showCustomAlert(\"Error\", \"Error checking plan editability\");\n            }\n        } catch (error) {\n            console.error(\"Error checking plan editability:\", error);\n            showCustomAlert(\"Error\", \"Error checking plan editability\");\n        }\n    };\n    const handleCopyPlan = async (planId)=>{\n        try {\n            const plan = plans.find((p)=>p._id === planId);\n            if (!plan) {\n                showCustomAlert(\"Error\", \"Plan not found\");\n                return;\n            }\n            // Show custom input modal for plan details\n            showCustomInput(\"Copy Plan\", [\n                {\n                    name: \"planName\",\n                    label: \"Plan Name\",\n                    placeholder: \"Enter name for the copied plan\",\n                    defaultValue: \"\".concat(plan.planName, \" (Copy)\"),\n                    required: true\n                },\n                {\n                    name: \"planCode\",\n                    label: \"Plan Code (Optional)\",\n                    placeholder: \"Enter plan code for the copied plan\",\n                    defaultValue: \"\".concat(plan.planCode || \"\", \"-COPY\"),\n                    required: false\n                }\n            ], async (values)=>{\n                const newPlanName = values.planName;\n                const newPlanCode = values.planCode;\n                try {\n                    // Call duplicate API\n                    const duplicateResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/duplicate\"), {\n                        method: \"POST\",\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)(),\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            planName: newPlanName,\n                            planCode: newPlanCode || undefined\n                        })\n                    });\n                    if (duplicateResponse.ok) {\n                        const result = await duplicateResponse.json();\n                        showCustomAlert(\"Success\", \"Plan copied successfully!\");\n                        loadPlans(); // Reload the plans list\n                    } else {\n                        const errorData = await duplicateResponse.json();\n                        showCustomAlert(\"Error\", \"Error copying plan: \".concat(errorData.error));\n                    }\n                } catch (error) {\n                    console.error(\"Error copying plan:\", error);\n                    showCustomAlert(\"Error\", \"Error copying plan\");\n                }\n            });\n        } catch (error) {\n            console.error(\"Error copying plan:\", error);\n            showCustomAlert(\"Error\", \"Error copying plan\");\n        }\n    };\n    const handleDeletePlan = async (planId)=>{\n        try {\n            // Check if plan can be deleted\n            const canDeleteResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/can-delete\"), {\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (canDeleteResponse.ok) {\n                const canDeleteResult = await canDeleteResponse.json();\n                if (canDeleteResult.canDelete) {\n                    showCustomConfirm(\"Delete Plan\", \"Are you sure you want to delete this plan? This action cannot be undone.\", async ()=>{\n                        try {\n                            const deleteResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId), {\n                                method: \"DELETE\",\n                                headers: {\n                                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                                }\n                            });\n                            if (deleteResponse.ok) {\n                                showCustomAlert(\"Success\", \"Plan deleted successfully!\");\n                                loadPlans(); // Reload the plans list\n                            } else {\n                                const errorData = await deleteResponse.json();\n                                showCustomAlert(\"Error\", \"Error deleting plan: \".concat(errorData.error || \"Unknown error\"));\n                            }\n                        } catch (deleteError) {\n                            console.error(\"Error deleting plan:\", deleteError);\n                            showCustomAlert(\"Error\", \"Error deleting plan. Please try again.\");\n                        }\n                    });\n                } else {\n                    // Show dependencies using correct endpoint\n                    const dependenciesResponse = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/dependent-assignments\"), {\n                        headers: {\n                            \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                        }\n                    });\n                    if (dependenciesResponse.ok) {\n                        var _dependencies_dependentAssignments;\n                        const dependencies = await dependenciesResponse.json();\n                        const assignmentsList = ((_dependencies_dependentAssignments = dependencies.dependentAssignments) === null || _dependencies_dependentAssignments === void 0 ? void 0 : _dependencies_dependentAssignments.map((assignment)=>\"Assignment \".concat(assignment._id)).join(\", \")) || \"Unknown assignments\";\n                        showCustomAlert(\"Cannot Delete Plan\", \"\".concat(canDeleteResult.message, \"\\n\\nThis plan is referenced by \").concat(dependencies.count, \" assignment(s):\\n\").concat(assignmentsList));\n                    } else {\n                        showCustomAlert(\"Cannot Delete Plan\", canDeleteResult.message);\n                    }\n                }\n            } else {\n                showCustomAlert(\"Error\", \"Error checking plan dependencies\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting plan:\", error);\n            showCustomAlert(\"Error\", \"Error deleting plan\");\n        }\n    };\n    const handleActivatePlan = async (planId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/activate\"), {\n                method: \"POST\",\n                headers: {\n                    \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                }\n            });\n            if (response.ok) {\n                showCustomAlert(\"Success\", \"Plan activated successfully!\");\n                loadPlans(); // Reload the plans list\n            } else {\n                const errorData = await response.json();\n                showCustomAlert(\"Error\", \"Error activating plan: \".concat(errorData.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error activating plan:\", error);\n            showCustomAlert(\"Error\", \"Error activating plan. Please try again.\");\n        }\n    };\n    const handleDeactivatePlan = async (planId)=>{\n        try {\n            showCustomConfirm(\"Convert to Draft\", \"Are you sure you want to convert this plan to draft? It will no longer be available for new assignments.\", async ()=>{\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/api/pre-enrollment/plans/\").concat(planId, \"/convert-to-draft\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"user-id\": (0,_utils_env__WEBPACK_IMPORTED_MODULE_8__.getUserId)()\n                    }\n                });\n                if (response.ok) {\n                    showCustomAlert(\"Success\", \"Plan converted to draft successfully!\");\n                    loadPlans(); // Reload the plans list\n                } else {\n                    const errorData = await response.json();\n                    showCustomAlert(\"Error\", \"Error converting plan to draft: \".concat(errorData.error || \"Unknown error\"));\n                }\n            });\n        } catch (error) {\n            console.error(\"Error converting plan to draft:\", error);\n            showCustomAlert(\"Error\", \"Error converting plan to draft. Please try again.\");\n        }\n    };\n    // Helper function to get carrier name by ID\n    const getCarrierName = (carrierId)=>{\n        const carrier = carriers.find((c)=>c._id === carrierId);\n        return carrier ? carrier.carrierName : \"Unknown Carrier\";\n    };\n    // Handle plan modal submission\n    const handlePlanSubmit = (plan)=>{\n        setShowPlanModal(false);\n        setEditingPlan(null);\n        loadPlans(); // Reload plans list (this will also reload assignment counts)\n    };\n    // Handle plan modal cancel\n    const handlePlanCancel = ()=>{\n        setShowPlanModal(false);\n        setEditingPlan(null);\n    };\n    const headerActions = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"create-btn\",\n        onClick: ()=>{\n            setEditingPlan(null);\n            setShowPlanModal(true);\n        },\n        style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"8px\",\n            padding: \"10px 16px\",\n            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"8px\",\n            fontSize: \"14px\",\n            fontWeight: \"500\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                size: 16\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, undefined),\n            \"Create Plan\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n        lineNumber: 491,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"plans-wrapper\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_employee_enrol_components_EnrollmentHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: \"white\",\n                        padding: \"24px 0\",\n                        borderBottom: \"1px solid #E5E7EB\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            maxWidth: \"95%\",\n                            margin: \"0 auto\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\",\n                            padding: \"0 2%\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n                                            borderRadius: \"8px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HiOutlineClipboardList, {\n                                            style: {\n                                                width: \"18px\",\n                                                height: \"18px\",\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"600\",\n                                                    color: \"#111827\",\n                                                    margin: 0\n                                                },\n                                                children: \"Plan Management\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: \"#6B7280\",\n                                                    margin: 0\n                                                },\n                                                children: \"Manage and view all insurance plans\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"white\",\n                                            border: \"1px solid #D1D5DB\",\n                                            borderRadius: \"8px\",\n                                            color: \"#374151\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineQuestionMarkCircle, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Ask Questions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"white\",\n                                            border: \"1px solid #D1D5DB\",\n                                            borderRadius: \"8px\",\n                                            color: \"#374151\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineViewGrid, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPlanModal(true),\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"10px 16px\",\n                                            background: \"linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)\",\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"8px\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create New Plan\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"plans-page\",\n                    children: [\n                        stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"grid\",\n                                gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n                                gap: \"16px\",\n                                maxWidth: \"95%\",\n                                margin: \"24px auto\",\n                                padding: \"0 2%\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#EFF6FF\",\n                                        border: \"1px solid #DBEAFE\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#2563EB\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Total Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#1E40AF\"\n                                                    },\n                                                    children: stats.totalPlans\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#2563EB\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiHealthBookLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#F0FDF4\",\n                                        border: \"1px solid #BBF7D0\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#16A34A\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Active Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#15803D\"\n                                                    },\n                                                    children: stats.plansByStatus.Active || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#16A34A\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiCalendarLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#FEF3C7\",\n                                        border: \"1px solid #FDE68A\",\n                                        borderRadius: \"12px\",\n                                        padding: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#D97706\",\n                                                        fontWeight: \"500\",\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: \"Recent Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"32px\",\n                                                        fontWeight: \"700\",\n                                                        color: \"#B45309\"\n                                                    },\n                                                    children: stats.recentPlans.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"48px\",\n                                                height: \"48px\",\n                                                background: \"#D97706\",\n                                                borderRadius: \"8px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiMoneyDollarCircleLine, {\n                                                style: {\n                                                    width: \"24px\",\n                                                    height: \"24px\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"white\",\n                                border: \"1px solid #E5E7EB\",\n                                borderRadius: \"12px\",\n                                padding: \"24px\",\n                                margin: \"0 auto 24px\",\n                                maxWidth: \"95%\",\n                                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"8px\",\n                                        marginBottom: \"16px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineSearch, {\n                                            style: {\n                                                width: \"16px\",\n                                                height: \"16px\",\n                                                color: \"#6B7280\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\",\n                                                fontWeight: \"500\",\n                                                color: \"#374151\"\n                                            },\n                                            children: \"Search & Filter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 11\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 9\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                                        gap: \"12px\",\n                                        marginBottom: \"16px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search by plan name, code, or carrier type...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            style: {\n                                                padding: \"10px 12px\",\n                                                border: \"1px solid #D1D5DB\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                outline: \"none\",\n                                                transition: \"border-color 0.2s\",\n                                                gridColumn: \"span 2\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterType,\n                                            onChange: (e)=>setFilterType(e.target.value),\n                                            style: {\n                                                padding: \"10px 12px\",\n                                                border: \"1px solid #D1D5DB\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                outline: \"none\",\n                                                background: \"white\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"active\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"inactive\",\n                                                    children: \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"draft\",\n                                                    children: \"Draft\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"template\",\n                                                    children: \"Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"archived\",\n                                                    children: \"Archived\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 13\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: carrierFilter,\n                                            onChange: (e)=>setCarrierFilter(e.target.value),\n                                            style: {\n                                                padding: \"10px 12px\",\n                                                border: \"1px solid #D1D5DB\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                outline: \"none\",\n                                                background: \"white\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Carriers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 13\n                                                }, undefined),\n                                                carriers.map((carrier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: carrier._id,\n                                                        children: carrier.carrierName\n                                                    }, carrier._id, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 15\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 11\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleClearFilters,\n                                            style: {\n                                                padding: \"10px 16px\",\n                                                background: \"white\",\n                                                border: \"1px solid #D1D5DB\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                color: \"#374151\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: \"Clear Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 11\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 9\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        color: \"#6B7280\"\n                                    },\n                                    children: [\n                                        \"Showing \",\n                                        filteredPlans.length,\n                                        \" of \",\n                                        plans.length,\n                                        \" plans\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 824,\n                                    columnNumber: 9\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 7\n                        }, undefined),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-state\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-spinner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading plans...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 836,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 834,\n                            columnNumber: 9\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"error-state\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Error: \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadPlans,\n                                    className: \"retry-btn\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 9\n                        }, undefined),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"plans-table-container\",\n                            children: filteredPlans.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"empty-state\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiCalendarLine_RiHealthBookLine_RiMoneyDollarCircleLine_RiShieldCheckLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_10__.RiShieldCheckLine, {\n                                        size: 48\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 855,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"No Plans Found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: plans.length === 0 ? \"You haven't created any plans yet. Create your first plan to get started.\" : \"No plans match your search criteria. Try adjusting your filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"create-first-plan-btn\",\n                                        onClick: ()=>router.push(\"/ai-enroller/create-plan\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlus, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Create Your First Plan\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 854,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"table-header\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"Plans List\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 874,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"table-wrapper\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"plans-table\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Plan Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Plan Code\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Coverage Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 883,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Groups\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: paginatedPlans.map((plan)=>{\n                                                        var _this, _plan_coverageSubTypes, _plan_coverageSubTypes1;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"plan-name-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"plan-name\",\n                                                                        children: plan.planName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 893,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"plan-code-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"plan-code-badge\",\n                                                                        children: plan.planCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 896,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"carrier-type-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"carrier-type-badge \".concat((_this = ((_plan_coverageSubTypes = plan.coverageSubTypes) === null || _plan_coverageSubTypes === void 0 ? void 0 : _plan_coverageSubTypes[0]) || plan.coverageType) === null || _this === void 0 ? void 0 : _this.toLowerCase().replace(\" \", \"-\")),\n                                                                        children: ((_plan_coverageSubTypes1 = plan.coverageSubTypes) === null || _plan_coverageSubTypes1 === void 0 ? void 0 : _plan_coverageSubTypes1[0]) || plan.coverageType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 899,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"status-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"status-badge \".concat((plan.status || \"unknown\").toLowerCase()),\n                                                                        children: plan.status || \"Unknown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 904,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"groups-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"groups-count\",\n                                                                        children: planAssignmentCounts[plan._id] !== undefined ? planAssignmentCounts[plan._id] : \"...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 909,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 908,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"actions-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"action-buttons\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn edit\",\n                                                                                onClick: ()=>handleEditPlan(plan._id),\n                                                                                title: \"Edit Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePencil, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 920,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 915,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn copy\",\n                                                                                onClick: ()=>handleCopyPlan(plan._id),\n                                                                                title: \"Copy Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineDuplicate, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 927,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 922,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            plan.status === \"Active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn deactivate\",\n                                                                                onClick: ()=>handleDeactivatePlan(plan._id),\n                                                                                title: \"Convert to Draft\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePause, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 935,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 930,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn activate\",\n                                                                                onClick: ()=>handleActivatePlan(plan._id),\n                                                                                title: \"Activate Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlinePlay, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 943,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 938,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"action-btn delete\",\n                                                                                onClick: ()=>handleDeletePlan(plan._id),\n                                                                                title: \"Delete Plan\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineTrash, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                    lineNumber: 951,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                                lineNumber: 946,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 914,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                    lineNumber: 913,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, plan._id, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 878,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pagination-container\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pagination-info\",\n                                                children: [\n                                                    \"Showing \",\n                                                    startIndex + 1,\n                                                    \"-\",\n                                                    Math.min(endIndex, filteredPlans.length),\n                                                    \" of \",\n                                                    filteredPlans.length,\n                                                    \" plans\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pagination-controls\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"pagination-btn\",\n                                                        onClick: ()=>handlePageChange(currentPage - 1),\n                                                        disabled: currentPage === 1,\n                                                        children: \"Previous\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    Array.from({\n                                                        length: totalPages\n                                                    }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"pagination-btn \".concat(page === currentPage ? \"active\" : \"\"),\n                                                            onClick: ()=>handlePageChange(page),\n                                                            children: page\n                                                        }, page, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                            lineNumber: 976,\n                                                            columnNumber: 23\n                                                        }, undefined)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"pagination-btn\",\n                                                        onClick: ()=>handlePageChange(currentPage + 1),\n                                                        disabled: currentPage === totalPages,\n                                                        children: \"Next\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 967,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 9\n                        }, undefined),\n                        showPlanModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: handlePlanCancel,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content plan-modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: editingPlan ? \"Edit Plan\" : \"Create New Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: handlePlanCancel,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1006,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1005,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_manage_groups_company_companyId_plans_components_CreatePlanForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            initialData: editingPlan,\n                                            onSubmit: handlePlanSubmit,\n                                            onCancel: handlePlanCancel,\n                                            isModal: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1009,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1001,\n                            columnNumber: 9\n                        }, undefined),\n                        showAlertModal && alertModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeAlertModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: alertModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeAlertModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1025,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                whiteSpace: \"pre-line\"\n                                            },\n                                            children: alertModalData.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"modal-btn primary\",\n                                            onClick: closeAlertModal,\n                                            children: \"OK\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1035,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1023,\n                            columnNumber: 9\n                        }, undefined),\n                        showConfirmModal && confirmModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeConfirmModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: confirmModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1048,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeConfirmModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1050,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1049,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1047,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-body\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                whiteSpace: \"pre-line\"\n                                            },\n                                            children: confirmModalData.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                            lineNumber: 1054,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-btn secondary\",\n                                                onClick: closeConfirmModal,\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1057,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-btn primary\",\n                                                onClick: confirmAction,\n                                                children: \"Confirm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1056,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1046,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1045,\n                            columnNumber: 9\n                        }, undefined),\n                        showInputModal && inputModalData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-overlay\",\n                            onClick: closeInputModal,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: inputModalData.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1073,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"modal-close\",\n                                                onClick: closeInputModal,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineDuplicate_HiOutlinePause_HiOutlinePencil_HiOutlinePlay_HiOutlinePlus_HiOutlineQuestionMarkCircle_HiOutlineSearch_HiOutlineTrash_HiOutlineViewGrid_HiOutlineX_react_icons_hi__WEBPACK_IMPORTED_MODULE_9__.HiOutlineX, {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                    lineNumber: 1075,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1074,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1072,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: (e)=>{\n                                            e.preventDefault();\n                                            const formData = new FormData(e.target);\n                                            const values = {};\n                                            inputModalData.fields.forEach((field)=>{\n                                                values[field.name] = formData.get(field.name) || \"\";\n                                            });\n                                            inputModalData.onSubmit(values);\n                                            closeInputModal();\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-body\",\n                                                children: inputModalData.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-group\",\n                                                        style: {\n                                                            marginBottom: \"1rem\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: field.name,\n                                                                style: {\n                                                                    display: \"block\",\n                                                                    marginBottom: \"0.5rem\",\n                                                                    fontSize: \"14px\",\n                                                                    lineHeight: \"21px\",\n                                                                    fontWeight: \"500\",\n                                                                    color: \"#374151\"\n                                                                },\n                                                                children: [\n                                                                    field.label,\n                                                                    field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: \"#dc2626\"\n                                                                        },\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                        lineNumber: 1100,\n                                                                        columnNumber: 42\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: field.name,\n                                                                name: field.name,\n                                                                placeholder: field.placeholder,\n                                                                defaultValue: field.defaultValue,\n                                                                required: field.required,\n                                                                style: {\n                                                                    width: \"100%\",\n                                                                    padding: \"0.75rem\",\n                                                                    border: \"1px solid #d1d5db\",\n                                                                    borderRadius: \"0.5rem\",\n                                                                    fontSize: \"14px\",\n                                                                    lineHeight: \"21px\",\n                                                                    fontFamily: \"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                                lineNumber: 1102,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, field.name, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1090,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1088,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-footer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"modal-btn secondary\",\n                                                        onClick: closeInputModal,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        className: \"modal-btn primary\",\n                                                        children: \"Submit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                        lineNumber: 1126,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                                lineNumber: 1122,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                            lineNumber: 1070,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n            lineNumber: 515,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\ai-enroller\\\\plans\\\\page.tsx\",\n        lineNumber: 514,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"rj+iLjGTAPCU1ONVJi170qf1XYk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = PlansPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-enroller/plans/page.tsx\n"));

/***/ })

});