'use client';

import { useEffect, useState } from 'react';
import { getUserId } from '@/utils/env';

export default function TestAuthPage() {
  const [authStatus, setAuthStatus] = useState<{
    hasUserId: boolean;
    userId?: string;
    error?: string;
  }>({ hasUserId: false });

  useEffect(() => {
    try {
      const userId = getUserId();
      setAuthStatus({
        hasUserId: true,
        userId
      });
    } catch (error) {
      setAuthStatus({
        hasUserId: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }, []);

  const setTestUserId = () => {
    localStorage.setItem('userid1', '6838677aef6db0212bcfdacd');
    window.location.reload();
  };

  const clearUserId = () => {
    localStorage.removeItem('userid1');
    localStorage.removeItem('userId');
    window.location.reload();
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>🔐 Authentication Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>Current Status</h2>
        <div style={{ 
          background: authStatus.hasUserId ? '#d4edda' : '#f8d7da', 
          padding: '10px', 
          borderRadius: '5px' 
        }}>
          {authStatus.hasUserId ? (
            <>
              <div><strong>✅ Authenticated</strong></div>
              <div><strong>User ID:</strong> {authStatus.userId}</div>
            </>
          ) : (
            <>
              <div><strong>❌ Not Authenticated</strong></div>
              <div><strong>Error:</strong> {authStatus.error}</div>
            </>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Test Actions</h2>
        <div style={{ display: 'flex', gap: '10px' }}>
          <button 
            onClick={setTestUserId}
            style={{ 
              padding: '8px 16px', 
              background: '#007bff', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px' 
            }}
          >
            Set Test User ID
          </button>
          <button 
            onClick={clearUserId}
            style={{ 
              padding: '8px 16px', 
              background: '#dc3545', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px' 
            }}
          >
            Clear User ID
          </button>
        </div>
      </div>

      <div style={{ marginTop: '30px', padding: '15px', background: '#e9ecef', borderRadius: '5px' }}>
        <h3>💡 Expected Behavior</h3>
        <ul>
          <li><strong>Without User ID:</strong> Should show "Not Authenticated" error</li>
          <li><strong>With User ID:</strong> Should show "Authenticated" with user ID</li>
          <li><strong>AI Enroller pages:</strong> Should redirect to home if no user ID</li>
          <li><strong>No more fallback:</strong> No automatic fallback user IDs</li>
        </ul>
      </div>
    </div>
  );
}
