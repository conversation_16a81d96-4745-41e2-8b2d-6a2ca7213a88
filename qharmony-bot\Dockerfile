# Use a base image with the necessary environment
FROM python:3.9-slim

# Set the working directory
WORKDIR /app

# Install gcc and other build tools
RUN apt-get update && apt-get install -y \
    gcc \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy the .env file with environment variables
COPY .env .

# Copy the requirements file and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade -r requirements.txt

# Verify uvicorn installation
RUN uvicorn --version

# Copy the rest of the application code
COPY . .

EXPOSE 8000

# Command to run the server using Python module invocation
CMD ["python", "-m", "uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8080"]