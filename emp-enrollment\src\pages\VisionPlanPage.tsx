import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Eye } from 'lucide-react';
import { PlanCard } from '@/components/PlanCard';
import { VideoPlayer } from '@/components/VideoPlayer';
import { PlanQADialog } from '@/components/PlanQADialog';
import { UserProfile } from '@/components/BenefitsEnrollmentBot';
import { BotQuestion } from '@/components/BotQuestion';
import { ROICalculator } from '@/components/ROICalculator';
import { PopularChoiceBadge } from '@/components/PopularChoiceBadge';

interface VisionPlanPageProps {
  userProfile: UserProfile;
  onPlanSelect: (planData: any) => void;
}

export const VisionPlanPage = ({ userProfile, onPlanSelect }: VisionPlanPageProps) => {
  const questionText = userProfile.wearGlasses 
    ? "👓 Perfect! Here's your vision coverage recommendation"
    : "👀 Final step - vision coverage for eye health";
    
  const contextText = userProfile.wearGlasses
    ? "The $150 frame allowance almost pays for the entire year!"
    : "Even without glasses, regular eye exams detect health issues early.";

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <BotQuestion 
        question={questionText}
        context={contextText}
      />

      {/* ROI Calculator */}
      {userProfile.wearGlasses && (
        <ROICalculator 
          planCost={5.25}
          planType="vision"
          familySize={userProfile.familyMembers}
        />
      )}
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl">Vision Coverage Options</h2>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-3">
            <div className="flex items-center gap-2 mb-2">
              <PopularChoiceBadge percentage={65} planType="vision" />
              <span className="text-sm text-muted-foreground">Most employees with glasses choose coverage</span>
            </div>
            
            <PlanCard
              type="vision"
              recommended={userProfile.wearGlasses}
              title="VSP Choice Plan"
              cost="$5.25"
              period="paycheck"
              features={[
                "Exam every 12 months, $10 copay",
                "$150 frame allowance",
                "Contact lens coverage",
                "💰 Employer pays 50% (saves you $68/year)",
                "👓 Frame allowance saves $150/year",
                "🔍 Early disease detection (diabetes, glaucoma)"
              ]}
              onSelect={() => onPlanSelect({
                name: 'VSP Choice Plan',
                cost: 5.25,
                type: 'Choice'
              })}
            />
            <PlanCard
              type="vision"
              title="No Vision Plan"
              cost="$0.00"
              period="paycheck"
              features={[
                "Skip if not needed",
                "Save money if you don't wear glasses",
                "Pay full price for exams (~$150)",
                "Pay full price for glasses (~$300+)"
              ]}
              onSelect={() => onPlanSelect({
                name: 'No Vision Plan',
                cost: 0.00,
                type: 'None'
              })}
            />
          </div>

          {/* Value Comparison */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 className="font-medium mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">💡 Annual Value Comparison:</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-blue-800 dark:text-blue-200">With VSP Plan</p>
                <p>Annual cost: $136</p>
                <p>Eye exam: $10</p>
                <p>Glasses: $50 (with $150 allowance)</p>
                <p className="font-bold bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">Total: $196</p>
              </div>
              <div>
                <p className="font-medium text-red-800 dark:text-red-200">Without Plan</p>
                <p>Annual cost: $0</p>
                <p>Eye exam: $150</p>
                <p>Glasses: $300</p>
                <p className="font-bold text-red-600">Total: $450</p>
              </div>
            </div>
            <p className="text-center mt-2 font-bold bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
              💰 You save $254 per year!
            </p>
          </div>
          
          <div className="flex gap-2 pt-4">
            <VideoPlayer 
              title="Vision Benefits Overview" 
              description="Understanding your vision coverage options"
              planType="vision"
            />
            <PlanQADialog selectedPlans={{}} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
