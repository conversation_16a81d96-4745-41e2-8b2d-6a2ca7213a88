'use client';

import React from 'react';
import { useRouter, useParams } from 'next/navigation';
import { HiOutlineCheckCircle, HiOutlineDownload, HiOutlineHome } from 'react-icons/hi';

export default function ConfirmationPage() {
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;

  const handleDownloadSummary = () => {
    // Handle download functionality
    console.log('Download summary');
  };

  const handleReturnToDashboard = () => {
    router.push('/ai-enroller');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center space-x-2 text-sm text-gray-500">
            <button
              onClick={() => router.push('/ai-enroller')}
              className="hover:text-gray-700"
            >
              Home
            </button>
            <span>›</span>
            <button
              onClick={() => router.push('/ai-enroller/manage-groups/select-company')}
              className="hover:text-gray-700"
            >
              Select Company
            </button>
            <span>›</span>
            <button
              onClick={() => router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`)}
              className="hover:text-gray-700"
            >
              View Plans
            </button>
            <span>›</span>
            <span className="text-gray-400">Contributions</span>
            <span>›</span>
            <span className="text-gray-400">Set Dates</span>
            <span>›</span>
            <span className="text-gray-400">Review</span>
            <span>›</span>
            <span className="text-green-600 font-medium">Confirmation</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Success Icon and Message */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <HiOutlineCheckCircle className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Configuration Complete!</h1>
          <p className="text-gray-600">All benefit plans have been successfully configured for TechCorp Inc.</p>
        </div>

        {/* Success Banner */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <div className="flex items-center gap-2">
            <HiOutlineCheckCircle className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-900">Success! Your benefit plan configurations have been saved and are now active.</span>
          </div>
        </div>

        {/* Configuration Summary */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <div className="flex items-center gap-2 mb-4">
            <span className="text-lg font-semibold text-gray-900">📋 Configuration Summary</span>
          </div>
          <p className="text-gray-600 mb-6">Plans configured for TechCorp Inc.</p>

          <div className="space-y-4">
            {/* Blue Cross Blue Shield PPO */}
            <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div>
                <h3 className="font-semibold text-blue-900">Blue Cross Blue Shield PPO</h3>
                <div className="text-sm text-blue-700">
                  <span className="font-medium">Medical</span>
                  <span className="mx-2">•</span>
                  <span>Code: BCBS-PPO-2024 | Policy: POL-123456789</span>
                </div>
              </div>
              <HiOutlineCheckCircle className="w-6 h-6 text-green-600" />
            </div>

            {/* Delta Dental PPO */}
            <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
              <div>
                <h3 className="font-semibold text-green-900">Delta Dental PPO</h3>
                <div className="text-sm text-green-700">
                  <span className="font-medium">Dental</span>
                  <span className="mx-2">•</span>
                  <span>Code: DD-PPO-2024 | Policy: POL-987654321</span>
                </div>
              </div>
              <HiOutlineCheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        {/* What's Next */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="font-semibold text-blue-900 mb-3">What&apos;s Next?</h2>
          <ul className="text-sm text-blue-800 space-y-2">
            <li>• Employee communications will be generated based on these configurations</li>
            <li>• Plan documents will be updated with the new contribution rates</li>
            <li>• Enrollment systems will be updated for the next plan year</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4">
          <button
            onClick={handleDownloadSummary}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
          >
            <HiOutlineDownload className="w-4 h-4" />
            Download Summary
          </button>
          <button
            onClick={handleReturnToDashboard}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <HiOutlineHome className="w-4 h-4" />
            Return to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}


